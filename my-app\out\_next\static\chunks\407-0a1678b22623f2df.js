"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[407],{344:(e,r,t)=>{t.d(r,{Image:()=>o._V,a8:()=>o.a8,q6:()=>o.q6,rd:()=>o.rd});var o=t(7429);t(7505)},1285:(e,r,t)=>{t.d(r,{B:()=>i});var o,n=t(2115),a=t(2712),l=(o||(o=t.t(n,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function i(e){let[r,t]=n.useState(l());return(0,a.N)(()=>{e||t(e=>e??String(s++))},[e]),e||(r?`radix-${r}`:"")}},1339:(e,r,t)=>{t.d(r,{NavProvider:()=>s,hI:()=>i});var o=t(5155),n=t(2115),a=t(344);(0,a.q6)("StylesContext",{tocNav:"xl:hidden",toc:"max-xl:hidden"});let l=(0,a.q6)("NavContext",{isTransparent:!1});function s(e){let{transparentMode:r="none",children:t}=e,[a,s]=(0,n.useState)("none"!==r);return(0,n.useEffect)(()=>{if("top"!==r)return;let e=()=>{s(window.scrollY<10)};return e(),window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[r]),(0,o.jsx)(l.Provider,{value:(0,n.useMemo)(()=>({isTransparent:a}),[a]),children:t})}function i(){return l.use()}},2712:(e,r,t)=>{t.d(r,{N:()=>n});var o=t(2115),n=globalThis?.document?o.useLayoutEffect:()=>{}},3243:(e,r,t)=>{t.d(r,{F:()=>a});let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=function(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=r);return o},a=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:l,defaultVariants:s}=r,i=Object.keys(l).map(e=>{let r=null==t?void 0:t[e],n=null==s?void 0:s[e];if(null===r)return null;let a=o(r)||o(n);return l[e][a]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return n(e,i,null==r||null==(a=r.compoundVariants)?void 0:a.reduce((e,r)=>{let{class:t,className:o,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...s,...d}[r]):({...s,...d})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},3655:(e,r,t)=>{t.d(r,{hO:()=>i,sG:()=>s});var o=t(2115),n=t(7650),a=t(9708),l=t(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,a.TL)(`Primitive.${r}`),n=o.forwardRef((e,o)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(n?t:r,{...a,ref:o})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{});function i(e,r){e&&n.flushSync(()=>e.dispatchEvent(r))}},4315:(e,r,t)=>{t.d(r,{FX:()=>l,jH:()=>s});var o=t(2115),n=t(5155),a=o.createContext(void 0),l=e=>{let{dir:r,children:t}=e;return(0,n.jsx)(a.Provider,{value:r,children:t})};function s(e){let r=o.useContext(a);return e||r||"ltr"}},5185:(e,r,t)=>{function o(e,r,{checkForDefaultPrevented:t=!0}={}){return function(o){if(e?.(o),!1===t||!o.defaultPrevented)return r?.(o)}}t.d(r,{mK:()=>o}),"undefined"!=typeof window&&window.document&&window.document.createElement},5845:(e,r,t)=>{t.d(r,{i:()=>s});var o,n=t(2115),a=t(2712),l=(o||(o=t.t(n,2)))[" useInsertionEffect ".trim().toString()]||a.N;function s({prop:e,defaultProp:r,onChange:t=()=>{},caller:o}){let[a,s,i]=function({defaultProp:e,onChange:r}){let[t,o]=n.useState(e),a=n.useRef(t),s=n.useRef(r);return l(()=>{s.current=r},[r]),n.useEffect(()=>{a.current!==t&&(s.current?.(t),a.current=t)},[t,a]),[t,o,s]}({defaultProp:r,onChange:t}),d=void 0!==e,c=d?e:a;{let r=n.useRef(void 0!==e);n.useEffect(()=>{let e=r.current;if(e!==d){let r=d?"controlled":"uncontrolled";console.warn(`${o} is changing from ${e?"controlled":"uncontrolled"} to ${r}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}r.current=d},[d,o])}return[c,n.useCallback(r=>{if(d){let t="function"==typeof r?r(e):r;t!==e&&i.current?.(t)}else s(r)},[d,e,s,i])]}Symbol("RADIX:SYNC_STATE")},6081:(e,r,t)=>{t.d(r,{A:()=>l,q:()=>a});var o=t(2115),n=t(5155);function a(e,r){let t=o.createContext(r),a=e=>{let{children:r,...a}=e,l=o.useMemo(()=>a,Object.values(a));return(0,n.jsx)(t.Provider,{value:l,children:r})};return a.displayName=e+"Provider",[a,function(n){let a=o.useContext(t);if(a)return a;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function l(e,r=[]){let t=[],a=()=>{let r=t.map(e=>o.createContext(e));return function(t){let n=t?.[e]||r;return o.useMemo(()=>({[`__scope${e}`]:{...t,[e]:n}}),[t,n])}};return a.scopeName=e,[function(r,a){let l=o.createContext(a),s=t.length;t=[...t,a];let i=r=>{let{scope:t,children:a,...i}=r,d=t?.[e]?.[s]||l,c=o.useMemo(()=>i,Object.values(i));return(0,n.jsx)(d.Provider,{value:c,children:a})};return i.displayName=r+"Provider",[i,function(t,n){let i=n?.[e]?.[s]||l,d=o.useContext(i);if(d)return d;if(void 0!==a)return a;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=t.reduce((r,{useScope:t,scopeName:o})=>{let n=t(e)[`__scope${o}`];return{...r,...n}},{});return o.useMemo(()=>({[`__scope${r.scopeName}`]:n}),[n])}};return t.scopeName=r.scopeName,t}(a,...r)]}},6101:(e,r,t)=>{t.d(r,{s:()=>l,t:()=>a});var o=t(2115);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,o=e.map(e=>{let o=n(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():n(e[r],null)}}}}function l(...e){return o.useCallback(a(...e),e)}},7429:(e,r,t)=>{t.d(r,{N_:()=>u,Uy:()=>s,_V:()=>c,a8:()=>i,q6:()=>m,rd:()=>d});var o=t(2115),n=t(5155),a=()=>{throw Error("You need to wrap your application inside `FrameworkProvider`.")},l=m("FrameworkContext",{useParams:a,useRouter:a,usePathname:a});function s({Link:e,useRouter:r,useParams:t,usePathname:a,Image:s,children:i}){let d=o.useMemo(()=>({usePathname:a,useRouter:r,Link:e,Image:s,useParams:t}),[e,a,r,t,s]);return(0,n.jsx)(l.Provider,{value:d,children:i})}function i(){return l.use().usePathname()}function d(){return l.use().useRouter()}function c(e){let{Image:r}=l.use();if(!r){let{src:r,alt:t,priority:o,...a}=e;return(0,n.jsx)("img",{alt:t,src:r,fetchPriority:o?"high":"auto",...a})}return(0,n.jsx)(r,{...e})}function u(e){let{Link:r}=l.use();if(!r){let{href:r,prefetch:t,...o}=e;return(0,n.jsx)("a",{href:r,...o})}return(0,n.jsx)(r,{...e})}function m(e,r){let t=o.createContext(r);return{Provider:e=>(0,n.jsx)(t.Provider,{value:e.value,children:e.children}),use:r=>{let n=o.useContext(t);if(!n)throw Error(r??`Provider of ${e} is required but missing.`);return n}}}},7505:(e,r,t)=>{t.d(r,{P:()=>d,f:()=>c});var o=Object.create,n=Object.defineProperty,a=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyNames,s=Object.getPrototypeOf,i=Object.prototype.hasOwnProperty,d=(e,r)=>function(){return r||(0,e[l(e)[0]])((r={exports:{}}).exports,r),r.exports},c=(e,r,t)=>(t=null!=e?o(s(e)):{},((e,r,t,o)=>{if(r&&"object"==typeof r||"function"==typeof r)for(let s of l(r))i.call(e,s)||s===t||n(e,s,{get:()=>r[s],enumerable:!(o=a(r,s))||o.enumerable});return e})(!r&&e&&e.__esModule?t:n(t,"default",{value:e,enumerable:!0}),e))},7936:(e,r,t)=>{t.d(r,{r:()=>o});let o=(0,t(3243).F)("inline-flex items-center justify-center rounded-md p-2 text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none",{variants:{color:{primary:"bg-fd-primary text-fd-primary-foreground hover:bg-fd-primary/80",outline:"border hover:bg-fd-accent hover:text-fd-accent-foreground",ghost:"hover:bg-fd-accent hover:text-fd-accent-foreground",secondary:"border bg-fd-secondary text-fd-secondary-foreground hover:bg-fd-accent hover:text-fd-accent-foreground"},size:{sm:"gap-1 px-2 py-1.5 text-xs",icon:"p-1.5 [&_svg]:size-5","icon-sm":"p-1.5 [&_svg]:size-4.5","icon-xs":"p-1 [&_svg]:size-4"}}})},8686:(e,r,t)=>{t.d(r,{$3:()=>h,AX:()=>p,Bx:()=>d,Gr:()=>m,JG:()=>x,Jl:()=>b,Ml:()=>c,QR:()=>g,Vw:()=>k,bd:()=>f,c_:()=>v,iU:()=>y,vj:()=>u,yQ:()=>i});var o=t(5155),n=t(2115),a=t(9688);let l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},s=(e,r)=>{let t=(0,n.forwardRef)(({className:e,size:t=24,color:s="currentColor",children:i,...d},c)=>(0,o.jsxs)("svg",{ref:c,...l,width:t,height:t,stroke:s,className:(0,a.QP)("lucide",e),...d,children:[r.map(([e,r])=>(0,n.createElement)(e,r)),i]}));return t.displayName=e,t},i=s("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);s("languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);let d=s("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]),c=s("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]),u=s("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),m=s("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),p=s("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),f=s("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),h=s("airplay",[["path",{d:"M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1",key:"ns4c3b"}],["path",{d:"m12 15 5 6H7Z",key:"14qnn2"}]]);s("menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),s("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),s("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),s("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),s("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);let b=s("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);s("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),s("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);let g=s("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),y=s("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),k=s("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);s("text",[["path",{d:"M15 18H3",key:"olowqp"}],["path",{d:"M17 6H3",key:"16j9eg"}],["path",{d:"M21 12H3",key:"2avoz0"}]]),s("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]),s("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),s("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),s("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),s("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),s("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);let v=s("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),x=s("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);s("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),s("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),s("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8905:(e,r,t)=>{t.d(r,{C:()=>l});var o=t(2115),n=t(6101),a=t(2712),l=e=>{let{present:r,children:t}=e,l=function(e){var r,t;let[n,l]=o.useState(),i=o.useRef(null),d=o.useRef(e),c=o.useRef("none"),[u,m]=(r=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,r)=>{let o=t[e][r];return null!=o?o:e},r));return o.useEffect(()=>{let e=s(i.current);c.current="mounted"===u?e:"none"},[u]),(0,a.N)(()=>{let r=i.current,t=d.current;if(t!==e){let o=c.current,n=s(r);e?m("MOUNT"):"none"===n||(null==r?void 0:r.display)==="none"?m("UNMOUNT"):t&&o!==n?m("ANIMATION_OUT"):m("UNMOUNT"),d.current=e}},[e,m]),(0,a.N)(()=>{if(n){var e;let r,t=null!=(e=n.ownerDocument.defaultView)?e:window,o=e=>{let o=s(i.current).includes(CSS.escape(e.animationName));if(e.target===n&&o&&(m("ANIMATION_END"),!d.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",r=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},a=e=>{e.target===n&&(c.current=s(i.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",o),n.addEventListener("animationend",o),()=>{t.clearTimeout(r),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",o),n.removeEventListener("animationend",o)}}m("ANIMATION_END")},[n,m]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(r),i="function"==typeof t?t({present:l.isPresent}):o.Children.only(t),d=(0,n.s)(l.ref,function(e){var r,t;let o=null==(r=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:r.get,n=o&&"isReactWarning"in o&&o.isReactWarning;return n?e.ref:(n=(o=null==(t=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof t||l.isPresent?o.cloneElement(i,{ref:d}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},9033:(e,r,t)=>{t.d(r,{c:()=>n});var o=t(2115);function n(e){let r=o.useRef(e);return o.useEffect(()=>{r.current=e}),o.useMemo(()=>(...e)=>r.current?.(...e),[])}},9688:(e,r,t)=>{t.d(r,{QP:()=>ee});let o=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],n=r.nextPart.get(t),a=n?o(e.slice(1),n):void 0;if(a)return a;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},n=/^\[(.+)\]$/,a=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:l(r,e)).classGroupId=t;return}if("function"==typeof e)return s(e)?void a(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,n])=>{a(n,l(r,e),t,o)})})},l=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},s=e=>e.isThemeGetter,i=/\s+/;function d(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=c(e))&&(o&&(o+=" "),o+=r);return o}let c=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=c(e[o]))&&(t&&(t+=" "),t+=r);return t},u=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},m=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,f=/^\d+\/\d+$/,h=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,b=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,g=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,y=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,k=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,v=e=>f.test(e),x=e=>!!e&&!Number.isNaN(Number(e)),w=e=>!!e&&Number.isInteger(Number(e)),M=e=>e.endsWith("%")&&x(e.slice(0,-1)),z=e=>h.test(e),N=()=>!0,j=e=>b.test(e)&&!g.test(e),P=()=>!1,C=e=>y.test(e),E=e=>k.test(e),O=e=>!_(e)&&!A(e),I=e=>D(e,Q,P),_=e=>m.test(e),S=e=>D(e,X,j),q=e=>D(e,Y,x),T=e=>D(e,Z,P),R=e=>D(e,B,E),$=e=>D(e,K,C),A=e=>p.test(e),L=e=>F(e,X),H=e=>F(e,J),V=e=>F(e,Z),G=e=>F(e,Q),U=e=>F(e,B),W=e=>F(e,K,!0),D=(e,r,t)=>{let o=m.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},F=(e,r,t=!1)=>{let o=p.exec(e);return!!o&&(o[1]?r(o[1]):t)},Z=e=>"position"===e||"percentage"===e,B=e=>"image"===e||"url"===e,Q=e=>"length"===e||"size"===e||"bg-size"===e,X=e=>"length"===e,Y=e=>"number"===e,J=e=>"family-name"===e,K=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...r){let t,l,s,c=function(i){let d;return l=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,a)=>{t.set(n,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}})((d=r.reduce((e,r)=>r(e),e())).cacheSize),parseClassName:(e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t,o=[],n=0,a=0,l=0;for(let t=0;t<e.length;t++){let s=e[t];if(0===n&&0===a){if(":"===s){o.push(e.slice(l,t)),l=t+1;continue}if("/"===s){r=t;continue}}"["===s?n++:"]"===s?n--:"("===s?a++:")"===s&&a--}let s=0===o.length?e:e.substring(l),i=(t=s).endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t;return{modifiers:o,hasImportantModifier:i!==s,baseClassName:i,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o})(d),sortModifiers:(e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}})(d),...(e=>{let r=(e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)a(t[e],o,e,r);return o})(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:l}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),o(t,r)||(e=>{if(n.test(e)){let r=n.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}})(e)},getConflictingClassGroupIds:(e,r)=>{let o=t[e]||[];return r&&l[e]?[...o,...l[e]]:o}}})(d)}).cache.get,s=t.cache.set,c=u,u(i)};function u(e){let r=l(e);if(r)return r;let o=((e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=r,l=[],s=e.trim().split(i),d="";for(let e=s.length-1;e>=0;e-=1){let r=s[e],{isExternal:i,modifiers:c,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:p}=t(r);if(i){d=r+(d.length>0?" "+d:d);continue}let f=!!p,h=o(f?m.substring(0,p):m);if(!h){if(!f||!(h=o(m))){d=r+(d.length>0?" "+d:d);continue}f=!1}let b=a(c).join(":"),g=u?b+"!":b,y=g+h;if(l.includes(y))continue;l.push(y);let k=n(h,f);for(let e=0;e<k.length;++e){let r=k[e];l.push(g+r)}d=r+(d.length>0?" "+d:d)}return d})(e,t);return s(e,o),o}return function(){return c(d.apply(null,arguments))}}(()=>{let e=u("color"),r=u("font"),t=u("text"),o=u("font-weight"),n=u("tracking"),a=u("leading"),l=u("breakpoint"),s=u("container"),i=u("spacing"),d=u("radius"),c=u("shadow"),m=u("inset-shadow"),p=u("text-shadow"),f=u("drop-shadow"),h=u("blur"),b=u("perspective"),g=u("aspect"),y=u("ease"),k=u("animate"),j=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...P(),A,_],E=()=>["auto","hidden","clip","visible","scroll"],D=()=>["auto","contain","none"],F=()=>[A,_,i],Z=()=>[v,"full","auto",...F()],B=()=>[w,"none","subgrid",A,_],Q=()=>["auto",{span:["full",w,A,_]},w,A,_],X=()=>[w,"auto",A,_],Y=()=>["auto","min","max","fr",A,_],J=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],K=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...F()],er=()=>[v,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...F()],et=()=>[e,A,_],eo=()=>[...P(),V,T,{position:[A,_]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",G,I,{size:[A,_]}],el=()=>[M,L,S],es=()=>["","none","full",d,A,_],ei=()=>["",x,L,S],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[x,M,V,T],em=()=>["","none",h,A,_],ep=()=>["none",x,A,_],ef=()=>["none",x,A,_],eh=()=>[x,A,_],eb=()=>[v,"full",...F()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[z],breakpoint:[z],color:[N],container:[z],"drop-shadow":[z],ease:["in","out","in-out"],font:[O],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[z],shadow:[z],spacing:["px",x],text:[z],"text-shadow":[z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",v,_,A,g]}],container:["container"],columns:[{columns:[x,_,A,s]}],"break-after":[{"break-after":j()}],"break-before":[{"break-before":j()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:D()}],"overscroll-x":[{"overscroll-x":D()}],"overscroll-y":[{"overscroll-y":D()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Z()}],"inset-x":[{"inset-x":Z()}],"inset-y":[{"inset-y":Z()}],start:[{start:Z()}],end:[{end:Z()}],top:[{top:Z()}],right:[{right:Z()}],bottom:[{bottom:Z()}],left:[{left:Z()}],visibility:["visible","invisible","collapse"],z:[{z:[w,"auto",A,_]}],basis:[{basis:[v,"full","auto",s,...F()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[x,v,"auto","initial","none",_]}],grow:[{grow:["",x,A,_]}],shrink:[{shrink:["",x,A,_]}],order:[{order:[w,"first","last","none",A,_]}],"grid-cols":[{"grid-cols":B()}],"col-start-end":[{col:Q()}],"col-start":[{"col-start":X()}],"col-end":[{"col-end":X()}],"grid-rows":[{"grid-rows":B()}],"row-start-end":[{row:Q()}],"row-start":[{"row-start":X()}],"row-end":[{"row-end":X()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Y()}],"auto-rows":[{"auto-rows":Y()}],gap:[{gap:F()}],"gap-x":[{"gap-x":F()}],"gap-y":[{"gap-y":F()}],"justify-content":[{justify:[...J(),"normal"]}],"justify-items":[{"justify-items":[...K(),"normal"]}],"justify-self":[{"justify-self":["auto",...K()]}],"align-content":[{content:["normal",...J()]}],"align-items":[{items:[...K(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...K(),{baseline:["","last"]}]}],"place-content":[{"place-content":J()}],"place-items":[{"place-items":[...K(),"baseline"]}],"place-self":[{"place-self":["auto",...K()]}],p:[{p:F()}],px:[{px:F()}],py:[{py:F()}],ps:[{ps:F()}],pe:[{pe:F()}],pt:[{pt:F()}],pr:[{pr:F()}],pb:[{pb:F()}],pl:[{pl:F()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":F()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":F()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[s,"screen",...er()]}],"min-w":[{"min-w":[s,"screen","none",...er()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[l]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,L,S]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,A,q]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",M,_]}],"font-family":[{font:[H,_,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,A,_]}],"line-clamp":[{"line-clamp":[x,"none",A,q]}],leading:[{leading:[a,...F()]}],"list-image":[{"list-image":["none",A,_]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",A,_]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[x,"from-font","auto",A,S]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[x,"auto",A,_]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:F()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",A,_]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",A,_]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},w,A,_],radial:["",A,_],conic:[w,A,_]},U,R]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[x,A,_]}],"outline-w":[{outline:["",x,L,S]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",c,W,$]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",m,W,$]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[x,S]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",p,W,$]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[x,A,_]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[x]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[A,_]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":P()}],"mask-image-conic-pos":[{"mask-conic":[x]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",A,_]}],filter:[{filter:["","none",A,_]}],blur:[{blur:em()}],brightness:[{brightness:[x,A,_]}],contrast:[{contrast:[x,A,_]}],"drop-shadow":[{"drop-shadow":["","none",f,W,$]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",x,A,_]}],"hue-rotate":[{"hue-rotate":[x,A,_]}],invert:[{invert:["",x,A,_]}],saturate:[{saturate:[x,A,_]}],sepia:[{sepia:["",x,A,_]}],"backdrop-filter":[{"backdrop-filter":["","none",A,_]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[x,A,_]}],"backdrop-contrast":[{"backdrop-contrast":[x,A,_]}],"backdrop-grayscale":[{"backdrop-grayscale":["",x,A,_]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[x,A,_]}],"backdrop-invert":[{"backdrop-invert":["",x,A,_]}],"backdrop-opacity":[{"backdrop-opacity":[x,A,_]}],"backdrop-saturate":[{"backdrop-saturate":[x,A,_]}],"backdrop-sepia":[{"backdrop-sepia":["",x,A,_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":F()}],"border-spacing-x":[{"border-spacing-x":F()}],"border-spacing-y":[{"border-spacing-y":F()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",A,_]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[x,"initial",A,_]}],ease:[{ease:["linear","initial",y,A,_]}],delay:[{delay:[x,A,_]}],animate:[{animate:["none",k,A,_]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,A,_]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eh()}],"skew-x":[{"skew-x":eh()}],"skew-y":[{"skew-y":eh()}],transform:[{transform:[A,_,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eb()}],"translate-x":[{"translate-x":eb()}],"translate-y":[{"translate-y":eb()}],"translate-z":[{"translate-z":eb()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",A,_]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":F()}],"scroll-mx":[{"scroll-mx":F()}],"scroll-my":[{"scroll-my":F()}],"scroll-ms":[{"scroll-ms":F()}],"scroll-me":[{"scroll-me":F()}],"scroll-mt":[{"scroll-mt":F()}],"scroll-mr":[{"scroll-mr":F()}],"scroll-mb":[{"scroll-mb":F()}],"scroll-ml":[{"scroll-ml":F()}],"scroll-p":[{"scroll-p":F()}],"scroll-px":[{"scroll-px":F()}],"scroll-py":[{"scroll-py":F()}],"scroll-ps":[{"scroll-ps":F()}],"scroll-pe":[{"scroll-pe":F()}],"scroll-pt":[{"scroll-pt":F()}],"scroll-pr":[{"scroll-pr":F()}],"scroll-pb":[{"scroll-pb":F()}],"scroll-pl":[{"scroll-pl":F()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",A,_]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[x,L,S,q]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9697:(e,r,t)=>{t.r(r),t.d(r,{I18nContext:()=>a,I18nLabel:()=>l,defaultTranslations:()=>n,useI18n:()=>s});var o=t(2115);let n={search:"Search",searchNoResult:"No results found",toc:"On this page",tocNoHeadings:"No Headings",lastUpdate:"Last updated on",chooseLanguage:"Choose a language",nextPage:"Next Page",previousPage:"Previous Page",chooseTheme:"Theme",editOnGithub:"Edit on GitHub"},a=(0,o.createContext)({text:n});function l(e){let{text:r}=s();return r[e.label]}function s(){return(0,o.useContext)(a)}},9708:(e,r,t)=>{t.d(r,{TL:()=>l});var o=t(2115),n=t(6101),a=t(5155);function l(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:t,...a}=e;if(o.isValidElement(t)){var l;let e,s,i=(l=t,(s=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(s=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,r){let t={...r};for(let o in r){let n=e[o],a=r[o];/^on[A-Z]/.test(o)?n&&a?t[o]=(...e)=>{let r=a(...e);return n(...e),r}:n&&(t[o]=n):"style"===o?t[o]={...n,...a}:"className"===o&&(t[o]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==o.Fragment&&(d.ref=r?(0,n.t)(r,i):i),o.cloneElement(t,d)}return o.Children.count(t)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:n,...l}=e,s=o.Children.toArray(n),d=s.find(i);if(d){let e=d.props.children,n=s.map(r=>r!==d?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...l,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,a.jsx)(r,{...l,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var s=Symbol("radix.slottable");function i(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}}}]);