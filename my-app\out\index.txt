1:"$Sreact.fragment"
2:I[9513,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","874","static/chunks/874-437a265a67d6cfee.js","379","static/chunks/379-50e981a99501adfb.js","177","static/chunks/app/layout-417276e6d105b1a1.js"],"Provider"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[1339,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","244","static/chunks/244-904cb1778dd50666.js","328","static/chunks/328-a0d4f8e3eb6521f3.js","790","static/chunks/app/(home)/layout-17b3782e48b73f92.js"],"NavProvider"]
6:I[7110,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","244","static/chunks/244-904cb1778dd50666.js","328","static/chunks/328-a0d4f8e3eb6521f3.js","790","static/chunks/app/(home)/layout-17b3782e48b73f92.js"],"Navbar"]
7:I[2808,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","244","static/chunks/244-904cb1778dd50666.js","328","static/chunks/328-a0d4f8e3eb6521f3.js","790","static/chunks/app/(home)/layout-17b3782e48b73f92.js"],"default"]
8:I[5403,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","244","static/chunks/244-904cb1778dd50666.js","328","static/chunks/328-a0d4f8e3eb6521f3.js","790","static/chunks/app/(home)/layout-17b3782e48b73f92.js"],"LargeSearchToggle"]
9:I[1624,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","244","static/chunks/244-904cb1778dd50666.js","328","static/chunks/328-a0d4f8e3eb6521f3.js","790","static/chunks/app/(home)/layout-17b3782e48b73f92.js"],"ThemeToggle"]
a:I[5403,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","244","static/chunks/244-904cb1778dd50666.js","328","static/chunks/328-a0d4f8e3eb6521f3.js","790","static/chunks/app/(home)/layout-17b3782e48b73f92.js"],"SearchToggle"]
b:I[5619,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","244","static/chunks/244-904cb1778dd50666.js","328","static/chunks/328-a0d4f8e3eb6521f3.js","790","static/chunks/app/(home)/layout-17b3782e48b73f92.js"],"Menu"]
c:I[5619,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","244","static/chunks/244-904cb1778dd50666.js","328","static/chunks/328-a0d4f8e3eb6521f3.js","790","static/chunks/app/(home)/layout-17b3782e48b73f92.js"],"MenuTrigger"]
d:I[5619,["407","static/chunks/407-0a1678b22623f2df.js","607","static/chunks/607-decfe929aa00677c.js","244","static/chunks/244-904cb1778dd50666.js","328","static/chunks/328-a0d4f8e3eb6521f3.js","790","static/chunks/app/(home)/layout-17b3782e48b73f92.js"],"MenuContent"]
12:I[8393,[],""]
:HL["/_next/static/css/97e78c7008a781cb.css","style"]
0:{"P":null,"b":"-UfTfY9npJwVLAeBnUpXo","p":"","c":["",""],"i":false,"f":[[["",{"children":["(home)",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/97e78c7008a781cb.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"__className_e8ce0c","suppressHydrationWarning":true,"children":["$","body",null,{"className":"flex flex-col min-h-screen","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["(home)",["$","$1","c",{"children":[null,["$","$L5",null,{"transparentMode":"$undefined","children":["$","main",null,{"id":"nd-home-layout","children":[["$","$L6",null,{"children":[["$","$L7",null,{"href":"/","className":"inline-flex items-center gap-2.5 font-semibold","children":[["$","svg",null,{"width":"24","height":"24","xmlns":"http://www.w3.org/2000/svg","aria-label":"Logo","children":["$","circle",null,{"cx":12,"cy":12,"r":12,"fill":"currentColor"}]}],"My App"]}],"$undefined",["$","ul",null,{"className":"flex flex-row items-center gap-2 px-6 max-sm:hidden","children":[]}],["$","div",null,{"className":"flex flex-row items-center justify-end gap-1.5 flex-1 max-lg:hidden","children":[["$","$L8",null,{"className":"w-full rounded-full ps-2.5 max-w-[240px]","hideIfDisabled":true}],["$","$L9",null,{"mode":"$undefined"}],null,["$","div",null,{"className":"flex flex-row items-center empty:hidden","children":[]}]]}],["$","ul",null,{"className":"flex flex-row items-center ms-auto -me-1.5 lg:hidden","children":[["$","$La",null,{"className":"p-2","hideIfDisabled":true}],["$","$Lb",null,{"children":[["$","$Lc",null,{"aria-label":"Toggle Menu","className":"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground p-1.5 [&_svg]:size-5 group","enableHover":"$undefined","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide !size-5.5 transition-transform duration-300 group-data-[state=open]:rotate-180","children":[[["$","path","qrunsl",{"d":"m6 9 6 6 6-6"}]],"$undefined"]}]}],["$","$Ld",null,{"className":"sm:flex-row sm:items-center sm:justify-end","children":[[],["$","div",null,{"className":"-ms-1.5 flex flex-row items-center gap-1.5 max-sm:mt-2","children":[[],["$","div",null,{"role":"separator","className":"flex-1"}],null,["$","$L9",null,{"mode":"$undefined"}]]}]]}]]}]]}]]}],["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:notFound:0:1:props:style","children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],"$Le","$Lf"]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]],"className":"flex flex-1 flex-col pt-14"}]}]]}],{"children":["__PAGE__","$L10",{},null,false]},null,false]},null,false],"$L11",false]],"m":"$undefined","G":["$12",[]],"s":false,"S":true}
13:I[6874,["874","static/chunks/874-437a265a67d6cfee.js","813","static/chunks/app/(home)/page-bd2284f16defa557.js"],""]
14:I[9665,[],"OutletBoundary"]
16:I[4911,[],"AsyncMetadataOutlet"]
18:I[9665,[],"ViewportBoundary"]
1a:I[9665,[],"MetadataBoundary"]
1b:"$Sreact.suspense"
e:["$","h1",null,{"className":"next-error-h1","style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style","children":404}]
f:["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style","children":["$","h2",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style","children":"This page could not be found."}]}]
10:["$","$1","c",{"children":[["$","main",null,{"className":"flex flex-1 flex-col justify-center text-center","children":[["$","h1",null,{"className":"mb-4 text-2xl font-bold","children":"Hello World"}],["$","p",null,{"className":"text-fd-muted-foreground","children":["You can open"," ",["$","$L13",null,{"href":"/docs","className":"text-fd-foreground font-semibold underline","children":"/docs"}]," ","and see the documentation."]}]]}],null,["$","$L14",null,{"children":["$L15",["$","$L16",null,{"promise":"$@17"}]]}]]}]
11:["$","$1","h",{"children":[null,[["$","$L18",null,{"children":"$L19"}],null],["$","$L1a",null,{"children":["$","div",null,{"hidden":true,"children":["$","$1b",null,{"fallback":null,"children":"$L1c"}]}]}]]}]
19:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
15:null
17:{"metadata":[],"error":null,"digest":"$undefined"}
1c:"$17:metadata"
