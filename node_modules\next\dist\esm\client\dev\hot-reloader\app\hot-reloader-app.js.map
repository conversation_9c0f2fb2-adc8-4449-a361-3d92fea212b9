{"version": 3, "sources": ["../../../../../src/client/dev/hot-reloader/app/hot-reloader-app.tsx"], "sourcesContent": ["/// <reference types=\"webpack/module.d.ts\" />\n\nimport type { ReactNode } from 'react'\nimport { useEffect, startTransition, useRef } from 'react'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport formatWebpackMessages from '../../../../shared/lib/format-webpack-messages'\nimport { useRouter } from '../../../components/navigation'\nimport {\n  REACT_REFRESH_FULL_RELOAD,\n  REACT_REFRESH_FULL_RELOAD_FROM_ERROR,\n  reportInvalidHmrMessage,\n} from '../shared'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { ReplaySsrOnlyErrors } from '../../../../next-devtools/userspace/app/errors/replay-ssr-only-errors'\nimport { AppDevOverlayErrorBoundary } from '../../../../next-devtools/userspace/app/app-dev-overlay-error-boundary'\nimport { useErrorHandler } from '../../../../next-devtools/userspace/app/errors/use-error-handler'\nimport { RuntimeErrorHandler } from '../../runtime-error-handler'\nimport {\n  useSendMessage,\n  useTurbopack,\n  useWebsocket,\n  useWebsocketPing,\n} from './use-websocket'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport { useUntrackedPathname } from '../../../components/navigation-untracked'\nimport reportHmrLatency from '../../report-hmr-latency'\nimport { TurbopackHmr } from '../turbopack-hot-reloader-common'\nimport { NEXT_HMR_REFRESH_HASH_COOKIE } from '../../../components/app-router-headers'\nimport type { GlobalErrorState } from '../../../components/app-router-instance'\nimport { useForwardConsoleLog } from '../../../../next-devtools/userspace/app/errors/use-forward-console-log'\n\nlet mostRecentCompilationHash: any = null\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now())\nlet reloading = false\nlet webpackStartMsSinceEpoch: number | null = null\nconst turbopackHmr: TurbopackHmr | null = process.env.TURBOPACK\n  ? new TurbopackHmr()\n  : null\n\nlet pendingHotUpdateWebpack = Promise.resolve()\nlet resolvePendingHotUpdateWebpack: () => void = () => {}\nfunction setPendingHotUpdateWebpack() {\n  pendingHotUpdateWebpack = new Promise((resolve) => {\n    resolvePendingHotUpdateWebpack = () => {\n      resolve()\n    }\n  })\n}\n\nexport function waitForWebpackRuntimeHotUpdate() {\n  return pendingHotUpdateWebpack\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */\nfunction isUpdateAvailable() {\n  if (process.env.TURBOPACK) {\n    return true\n  }\n\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: any) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: any) {\n      if (status === 'idle') {\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    module.hot.addStatusHandler(handler)\n  }\n}\n\nfunction performFullReload(err: any, sendMessage: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  if (reloading) return\n  reloading = true\n  window.location.reload()\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdatesWebpack(sendMessage: (message: string) => void) {\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    resolvePendingHotUpdateWebpack()\n    dispatcher.onBuildOk()\n    reportHmrLatency(sendMessage, [], webpackStartMsSinceEpoch!, Date.now())\n    return\n  }\n\n  function handleApplyUpdates(\n    err: any,\n    updatedModules: (string | number)[] | null\n  ) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || updatedModules == null) {\n      if (err) {\n        console.warn(REACT_REFRESH_FULL_RELOAD)\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err, sendMessage)\n      return\n    }\n\n    dispatcher.onBuildOk()\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdatesWebpack(sendMessage)\n      return\n    }\n\n    dispatcher.onRefresh()\n    resolvePendingHotUpdateWebpack()\n    reportHmrLatency(\n      sendMessage,\n      updatedModules,\n      webpackStartMsSinceEpoch!,\n      Date.now()\n    )\n\n    if (process.env.__NEXT_TEST_MODE) {\n      afterApplyUpdates(() => {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      })\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: (string | number)[] | null) => {\n      if (updatedModules == null) {\n        return null\n      }\n\n      // We should always handle an update, even if updatedModules is empty (but\n      // non-null) for any reason. That's what webpack would normally do:\n      // https://github.com/webpack/webpack/blob/3aa6b6bc3a64/lib/hmr/HotModuleReplacement.runtime.js#L296-L298\n      dispatcher.onBeforeRefresh()\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: (string | number)[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\n/** Handles messages from the server for the App Router. */\nfunction processMessage(\n  obj: HMR_ACTION_TYPES,\n  sendMessage: (message: string) => void,\n  processTurbopackMessage: (msg: TurbopackMsgToBrowser) => void,\n  router: ReturnType<typeof useRouter>,\n  appIsrManifestRef: ReturnType<typeof useRef>,\n  pathnameRef: ReturnType<typeof useRef>\n) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  function handleErrors(errors: ReadonlyArray<unknown>) {\n    // \"Massage\" webpack messages.\n    const formatted = formatWebpackMessages({\n      errors: errors,\n      warnings: [],\n    })\n\n    // Only show the first error.\n    dispatcher.onBuildError(formatted.errors[0])\n\n    // Also log them to the console.\n    for (let i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n\n    // Do not attempt to reload now.\n    // We will reload on next success instead.\n    if (process.env.__NEXT_TEST_MODE) {\n      if (self.__NEXT_HMR_CB) {\n        self.__NEXT_HMR_CB(formatted.errors[0])\n        self.__NEXT_HMR_CB = null\n      }\n    }\n  }\n\n  function handleHotUpdate() {\n    if (process.env.TURBOPACK) {\n      const hmrUpdate = turbopackHmr!.onBuilt()\n      if (hmrUpdate != null) {\n        reportHmrLatency(\n          sendMessage,\n          [...hmrUpdate.updatedModules],\n          hmrUpdate.startMsSinceEpoch,\n          hmrUpdate.endMsSinceEpoch,\n          // suppress the `client-hmr-latency` event if the update was a no-op:\n          hmrUpdate.hasUpdates\n        )\n      }\n      dispatcher.onBuildOk()\n    } else {\n      tryApplyUpdatesWebpack(sendMessage)\n    }\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      if (process.env.__NEXT_DEV_INDICATOR) {\n        if (appIsrManifestRef) {\n          appIsrManifestRef.current = obj.data\n\n          // handle initial status on receiving manifest\n          // navigation is handled in useEffect for pathname changes\n          // as we'll receive the updated manifest before usePathname\n          // triggers for new value\n          if ((pathnameRef.current as string) in obj.data) {\n            dispatcher.onStaticIndicator(true)\n          } else {\n            dispatcher.onStaticIndicator(false)\n          }\n        }\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      dispatcher.buildingIndicatorShow()\n\n      if (process.env.TURBOPACK) {\n        turbopackHmr!.onBuilding()\n      } else {\n        webpackStartMsSinceEpoch = Date.now()\n        setPendingHotUpdateWebpack()\n        console.log('[Fast Refresh] rebuilding')\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      dispatcher.buildingIndicatorHide()\n\n      if (obj.hash) {\n        handleAvailableHash(obj.hash)\n      }\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('debug' in obj && obj.debug) dispatcher.onDebugInfo(obj.debug)\n      if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator)\n      if ('devToolsConfig' in obj)\n        dispatcher.onDevToolsConfig(obj.devToolsConfig)\n\n      const hasErrors = Boolean(errors && errors.length)\n      // Compilation with errors (e.g. syntax error or missing modules).\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        handleErrors(errors)\n        return\n      }\n\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        // Print warnings to the console.\n        const formattedMessages = formatWebpackMessages({\n          warnings: warnings,\n          errors: [],\n        })\n\n        for (let i = 0; i < formattedMessages.warnings.length; i++) {\n          if (i === 5) {\n            console.warn(\n              'There were more warnings in other files.\\n' +\n                'You can find a complete log in the terminal.'\n            )\n            break\n          }\n          console.warn(stripAnsi(formattedMessages.warnings[i]))\n        }\n\n        // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: __nextDevClientId,\n        })\n      )\n\n      if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n        handleHotUpdate()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n        data: {\n          sessionId: obj.data.sessionId,\n        },\n      })\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      turbopackHmr!.onTurbopackMessage(obj)\n      dispatcher.onBeforeRefresh()\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n        data: obj.data,\n      })\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null, sendMessage)\n      }\n      dispatcher.onRefresh()\n      break\n    }\n    // TODO-APP: make server component change more granular\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      turbopackHmr?.onServerComponentChanges()\n      sendMessage(\n        JSON.stringify({\n          event: 'server-component-reload-page',\n          clientId: __nextDevClientId,\n          hash: obj.hash,\n        })\n      )\n\n      // Store the latest hash in a session cookie so that it's sent back to the\n      // server with any subsequent requests.\n      document.cookie = `${NEXT_HMR_REFRESH_HASH_COOKIE}=${obj.hash};path=/`\n\n      if (\n        RuntimeErrorHandler.hadRuntimeError ||\n        document.documentElement.id === '__next_error__'\n      ) {\n        if (reloading) return\n        reloading = true\n        return window.location.reload()\n      }\n\n      startTransition(() => {\n        router.hmrRefresh()\n        dispatcher.onRefresh()\n      })\n\n      if (process.env.__NEXT_TEST_MODE) {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      }\n\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n      turbopackHmr?.onReloadPage()\n      sendMessage(\n        JSON.stringify({\n          event: 'client-reload-page',\n          clientId: __nextDevClientId,\n        })\n      )\n      if (reloading) return\n      reloading = true\n      return window.location.reload()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE: {\n      turbopackHmr?.onPageAddRemove()\n      // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n      return router.hmrRefresh()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEVTOOLS_CONFIG: {\n      dispatcher.onDevToolsConfig(obj.data)\n      return\n    }\n    default: {\n      obj satisfies never\n    }\n  }\n}\n\nexport default function HotReload({\n  assetPrefix,\n  children,\n  globalError,\n}: {\n  assetPrefix: string\n  children: ReactNode\n  globalError: GlobalErrorState\n}) {\n  useErrorHandler(dispatcher.onUnhandledError, dispatcher.onUnhandledRejection)\n\n  const webSocketRef = useWebsocket(assetPrefix)\n\n  useWebsocketPing(webSocketRef)\n  const sendMessage = useSendMessage(webSocketRef)\n  useForwardConsoleLog(webSocketRef)\n  const processTurbopackMessage = useTurbopack(sendMessage, (err) =>\n    performFullReload(err, sendMessage)\n  )\n\n  const router = useRouter()\n\n  // We don't want access of the pathname for the dev tools to trigger a dynamic\n  // access (as the dev overlay will never be present in production).\n  const pathname = useUntrackedPathname()\n  const appIsrManifestRef = useRef<Record<string, false | number>>({})\n  const pathnameRef = useRef(pathname)\n\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    // this conditional is only for dead-code elimination which\n    // isn't a runtime conditional only build-time so ignore hooks rule\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      pathnameRef.current = pathname\n\n      const appIsrManifest = appIsrManifestRef.current\n\n      if (appIsrManifest) {\n        if (pathname && pathname in appIsrManifest) {\n          try {\n            dispatcher.onStaticIndicator(true)\n          } catch (reason) {\n            let message = ''\n\n            if (reason instanceof DOMException) {\n              // Most likely a SecurityError, because of an unavailable localStorage\n              message = reason.stack ?? reason.message\n            } else if (reason instanceof Error) {\n              message = 'Error: ' + reason.message + '\\n' + (reason.stack ?? '')\n            } else {\n              message = 'Unexpected Exception: ' + reason\n            }\n\n            console.warn('[HMR] ' + message)\n          }\n        } else {\n          dispatcher.onStaticIndicator(false)\n        }\n      }\n    }, [pathname])\n  }\n\n  useEffect(() => {\n    const websocket = webSocketRef.current\n    if (!websocket) return\n\n    const handler = (event: MessageEvent<any>) => {\n      try {\n        const obj = JSON.parse(event.data)\n        processMessage(\n          obj,\n          sendMessage,\n          processTurbopackMessage,\n          router,\n          appIsrManifestRef,\n          pathnameRef\n        )\n      } catch (err: unknown) {\n        reportInvalidHmrMessage(event, err)\n      }\n    }\n\n    websocket.addEventListener('message', handler)\n    return () => websocket.removeEventListener('message', handler)\n  }, [\n    sendMessage,\n    router,\n    webSocketRef,\n    processTurbopackMessage,\n    appIsrManifestRef,\n  ])\n  return (\n    <AppDevOverlayErrorBoundary globalError={globalError}>\n      <ReplaySsrOnlyErrors onBlockingError={dispatcher.openErrorOverlay} />\n      {children}\n    </AppDevOverlayErrorBoundary>\n  )\n}\n"], "names": ["useEffect", "startTransition", "useRef", "stripAnsi", "formatWebpackMessages", "useRouter", "REACT_REFRESH_FULL_RELOAD", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "reportInvalidHmrMessage", "dispatcher", "ReplaySsrOnlyErrors", "AppDevOverlayErrorBoundary", "useErrorHandler", "RuntimeError<PERSON>andler", "useSendMessage", "useTurbopack", "useWebsocket", "useWebsocketPing", "HMR_ACTIONS_SENT_TO_BROWSER", "useUntrackedPathname", "reportHmrLatency", "TurbopackHmr", "NEXT_HMR_REFRESH_HASH_COOKIE", "useForwardConsoleLog", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "webpackStartMsSinceEpoch", "turbopackHmr", "process", "env", "TURBOPACK", "pendingHotUpdateWebpack", "Promise", "resolve", "resolvePendingHotUpdateWebpack", "setPendingHotUpdateWebpack", "waitForWebpackRuntimeHotUpdate", "handleAvailableHash", "hash", "isUpdateAvailable", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "sendMessage", "stackTrace", "stack", "split", "slice", "join", "message", "JSON", "stringify", "event", "hadRuntimeError", "dependency<PERSON><PERSON>n", "undefined", "window", "location", "reload", "tryApplyUpdatesWebpack", "onBuildOk", "handleApplyUpdates", "updatedModules", "console", "warn", "onRefresh", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "onBeforeRefresh", "apply", "processMessage", "obj", "processTurbopackMessage", "router", "appIsrManifestRef", "pathnameRef", "handleErrors", "errors", "formatted", "warnings", "onBuildError", "i", "length", "error", "handleHotUpdate", "hmrUpdate", "onBuilt", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdates", "action", "ISR_MANIFEST", "__NEXT_DEV_INDICATOR", "current", "data", "onStaticIndicator", "BUILDING", "buildingIndicatorShow", "onBuilding", "log", "BUILT", "SYNC", "buildingIndicatorHide", "onVersionInfo", "versionInfo", "debug", "onDebugInfo", "onDevIndicator", "devIndicator", "onDevToolsConfig", "devToolsConfig", "hasErrors", "Boolean", "errorCount", "clientId", "hasWarnings", "warningCount", "formattedMessages", "TURBOPACK_CONNECTED", "type", "sessionId", "TURBOPACK_MESSAGE", "onTurbopackMessage", "SERVER_COMPONENT_CHANGES", "onServerComponentChanges", "document", "cookie", "documentElement", "id", "hmrRefresh", "RELOAD_PAGE", "onReloadPage", "ADDED_PAGE", "REMOVED_PAGE", "onPageAddRemove", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "DEVTOOLS_CONFIG", "HotReload", "assetPrefix", "children", "globalError", "onUnhandledError", "onUnhandledRejection", "webSocketRef", "pathname", "appIsrManifest", "reason", "DOMException", "websocket", "addEventListener", "removeEventListener", "onBlockingError", "openErrorOverlay"], "mappings": "AAAA,6CAA6C;;AAG7C,SAASA,SAAS,EAAEC,eAAe,EAAEC,MAAM,QAAQ,QAAO;AAC1D,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,2BAA2B,iDAAgD;AAClF,SAASC,SAAS,QAAQ,iCAAgC;AAC1D,SACEC,yBAAyB,EACzBC,oCAAoC,EACpCC,uBAAuB,QAClB,YAAW;AAClB,SAASC,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,mBAAmB,QAAQ,wEAAuE;AAC3G,SAASC,0BAA0B,QAAQ,yEAAwE;AACnH,SAASC,eAAe,QAAQ,mEAAkE;AAClG,SAASC,mBAAmB,QAAQ,8BAA6B;AACjE,SACEC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,gBAAgB,QACX,kBAAiB;AACxB,SAASC,2BAA2B,QAAQ,4CAA2C;AAKvF,SAASC,oBAAoB,QAAQ,2CAA0C;AAC/E,OAAOC,sBAAsB,2BAA0B;AACvD,SAASC,YAAY,QAAQ,mCAAkC;AAC/D,SAASC,4BAA4B,QAAQ,yCAAwC;AAErF,SAASC,oBAAoB,QAAQ,yEAAwE;AAE7G,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAChB,IAAIC,2BAA0C;AAC9C,MAAMC,eAAoCC,QAAQC,GAAG,CAACC,SAAS,GAC3D,IAAIf,iBACJ;AAEJ,IAAIgB,0BAA0BC,QAAQC,OAAO;AAC7C,IAAIC,iCAA6C,KAAO;AACxD,SAASC;IACPJ,0BAA0B,IAAIC,QAAQ,CAACC;QACrCC,iCAAiC;YAC/BD;QACF;IACF;AACF;AAEA,OAAO,SAASG;IACd,OAAOL;AACT;AAEA,kDAAkD;AAClD,SAASM,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCpB,4BAA4BoB;AAC9B;AAEA;;;;CAIC,GACD,SAASC;IACP,IAAIX,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,OAAO;IACT;IAEA,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOZ,8BAA8BsB;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrBF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACAJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEC,WAAgB;IACnD,MAAMC,aACJF,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDN,IAAIO,OAAO,IACXP,MAAM,EAAC;IAEXC,YACEO,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPR;QACAS,iBAAiB,CAAC,CAACvD,oBAAoBuD,eAAe;QACtDC,iBAAiBZ,MAAMA,IAAIY,eAAe,GAAGC;IAC/C;IAGF,IAAIvC,WAAW;IACfA,YAAY;IACZwC,OAAOC,QAAQ,CAACC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,uBAAuBhB,WAAsC;IACpE,IAAI,CAACb,uBAAuB,CAACE,mBAAmB;QAC9CP;QACA/B,WAAWkE,SAAS;QACpBvD,iBAAiBsC,aAAa,EAAE,EAAE1B,0BAA2BH,KAAKC,GAAG;QACrE;IACF;IAEA,SAAS8C,mBACPnB,GAAQ,EACRoB,cAA0C;QAE1C,IAAIpB,OAAO5C,oBAAoBuD,eAAe,IAAIS,kBAAkB,MAAM;YACxE,IAAIpB,KAAK;gBACPqB,QAAQC,IAAI,CAACzE;YACf,OAAO,IAAIO,oBAAoBuD,eAAe,EAAE;gBAC9CU,QAAQC,IAAI,CAACxE;YACf;YACAiD,kBAAkBC,KAAKC;YACvB;QACF;QAEAjD,WAAWkE,SAAS;QAEpB,IAAI9B,qBAAqB;YACvB,+DAA+D;YAC/D6B,uBAAuBhB;YACvB;QACF;QAEAjD,WAAWuE,SAAS;QACpBxC;QACApB,iBACEsC,aACAmB,gBACA7C,0BACAH,KAAKC,GAAG;QAGV,IAAII,QAAQC,GAAG,CAAC8C,gBAAgB,EAAE;YAChC9B,kBAAkB;gBAChB,IAAI+B,KAAKC,aAAa,EAAE;oBACtBD,KAAKC,aAAa;oBAClBD,KAAKC,aAAa,GAAG;gBACvB;YACF;QACF;IACF;IAEA,2DAA2D;IAC3DnC,OAAOC,GAAG,CACPmC,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACR;QACL,IAAIA,kBAAkB,MAAM;YAC1B,OAAO;QACT;QAEA,0EAA0E;QAC1E,mEAAmE;QACnE,yGAAyG;QACzGpE,WAAW6E,eAAe;QAC1B,2DAA2D;QAC3D,OAAOtC,OAAOC,GAAG,CAACsC,KAAK;IACzB,GACCF,IAAI,CACH,CAACR;QACCD,mBAAmB,MAAMC;IAC3B,GACA,CAACpB;QACCmB,mBAAmBnB,KAAK;IAC1B;AAEN;AAEA,yDAAyD,GACzD,SAAS+B,eACPC,GAAqB,EACrB/B,WAAsC,EACtCgC,uBAA6D,EAC7DC,MAAoC,EACpCC,iBAA4C,EAC5CC,WAAsC;IAEtC,IAAI,CAAE,CAAA,YAAYJ,GAAE,GAAI;QACtB;IACF;IAEA,SAASK,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAY5F,sBAAsB;YACtC2F,QAAQA;YACRE,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7BxF,WAAWyF,YAAY,CAACF,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAII,IAAI,GAAGA,IAAIH,UAAUD,MAAM,CAACK,MAAM,EAAED,IAAK;YAChDrB,QAAQuB,KAAK,CAAClG,UAAU6F,UAAUD,MAAM,CAACI,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIjE,QAAQC,GAAG,CAAC8C,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACa,UAAUD,MAAM,CAAC,EAAE;gBACtCb,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,SAASmB;QACP,IAAIpE,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzB,MAAMmE,YAAYtE,aAAcuE,OAAO;YACvC,IAAID,aAAa,MAAM;gBACrBnF,iBACEsC,aACA;uBAAI6C,UAAU1B,cAAc;iBAAC,EAC7B0B,UAAUE,iBAAiB,EAC3BF,UAAUG,eAAe,EACzB,qEAAqE;gBACrEH,UAAUI,UAAU;YAExB;YACAlG,WAAWkE,SAAS;QACtB,OAAO;YACLD,uBAAuBhB;QACzB;IACF;IAEA,OAAQ+B,IAAImB,MAAM;QAChB,KAAK1F,4BAA4B2F,YAAY;YAAE;gBAC7C,IAAI3E,QAAQC,GAAG,CAAC2E,oBAAoB,EAAE;oBACpC,IAAIlB,mBAAmB;wBACrBA,kBAAkBmB,OAAO,GAAGtB,IAAIuB,IAAI;wBAEpC,8CAA8C;wBAC9C,0DAA0D;wBAC1D,2DAA2D;wBAC3D,yBAAyB;wBACzB,IAAI,AAACnB,YAAYkB,OAAO,IAAetB,IAAIuB,IAAI,EAAE;4BAC/CvG,WAAWwG,iBAAiB,CAAC;wBAC/B,OAAO;4BACLxG,WAAWwG,iBAAiB,CAAC;wBAC/B;oBACF;gBACF;gBACA;YACF;QACA,KAAK/F,4BAA4BgG,QAAQ;YAAE;gBACzCzG,WAAW0G,qBAAqB;gBAEhC,IAAIjF,QAAQC,GAAG,CAACC,SAAS,EAAE;oBACzBH,aAAcmF,UAAU;gBAC1B,OAAO;oBACLpF,2BAA2BH,KAAKC,GAAG;oBACnCW;oBACAqC,QAAQuC,GAAG,CAAC;gBACd;gBACA;YACF;QACA,KAAKnG,4BAA4BoG,KAAK;QACtC,KAAKpG,4BAA4BqG,IAAI;YAAE;gBACrC9G,WAAW+G,qBAAqB;gBAEhC,IAAI/B,IAAI7C,IAAI,EAAE;oBACZD,oBAAoB8C,IAAI7C,IAAI;gBAC9B;gBAEA,MAAM,EAAEmD,MAAM,EAAEE,QAAQ,EAAE,GAAGR;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKhF,WAAWgH,aAAa,CAAChC,IAAIiC,WAAW;gBAClE,IAAI,WAAWjC,OAAOA,IAAIkC,KAAK,EAAElH,WAAWmH,WAAW,CAACnC,IAAIkC,KAAK;gBACjE,IAAI,kBAAkBlC,KAAKhF,WAAWoH,cAAc,CAACpC,IAAIqC,YAAY;gBACrE,IAAI,oBAAoBrC,KACtBhF,WAAWsH,gBAAgB,CAACtC,IAAIuC,cAAc;gBAEhD,MAAMC,YAAYC,QAAQnC,UAAUA,OAAOK,MAAM;gBACjD,kEAAkE;gBAClE,IAAI6B,WAAW;oBACbvE,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPgE,YAAYpC,OAAOK,MAAM;wBACzBgC,UAAU3G;oBACZ;oBAGFqE,aAAaC;oBACb;gBACF;gBAEA,MAAMsC,cAAcH,QAAQjC,YAAYA,SAASG,MAAM;gBACvD,IAAIiC,aAAa;oBACf3E,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPmE,cAAcrC,SAASG,MAAM;wBAC7BgC,UAAU3G;oBACZ;oBAGF,iCAAiC;oBACjC,MAAM8G,oBAAoBnI,sBAAsB;wBAC9C6F,UAAUA;wBACVF,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAII,IAAI,GAAGA,IAAIoC,kBAAkBtC,QAAQ,CAACG,MAAM,EAAED,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXrB,QAAQC,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACAD,QAAQC,IAAI,CAAC5E,UAAUoI,kBAAkBtC,QAAQ,CAACE,EAAE;oBACtD;gBAEA,uHAAuH;gBACzH;gBAEAzC,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPiE,UAAU3G;gBACZ;gBAGF,IAAIgE,IAAImB,MAAM,KAAK1F,4BAA4BoG,KAAK,EAAE;oBACpDhB;gBACF;gBACA;YACF;QACA,KAAKpF,4BAA4BsH,mBAAmB;YAAE;gBACpD9C,wBAAwB;oBACtB+C,MAAMvH,4BAA4BsH,mBAAmB;oBACrDxB,MAAM;wBACJ0B,WAAWjD,IAAIuB,IAAI,CAAC0B,SAAS;oBAC/B;gBACF;gBACA;YACF;QACA,KAAKxH,4BAA4ByH,iBAAiB;YAAE;gBAClD1G,aAAc2G,kBAAkB,CAACnD;gBACjChF,WAAW6E,eAAe;gBAC1BI,wBAAwB;oBACtB+C,MAAMvH,4BAA4ByH,iBAAiB;oBACnD3B,MAAMvB,IAAIuB,IAAI;gBAChB;gBACA,IAAInG,oBAAoBuD,eAAe,EAAE;oBACvCU,QAAQC,IAAI,CAACxE;oBACbiD,kBAAkB,MAAME;gBAC1B;gBACAjD,WAAWuE,SAAS;gBACpB;YACF;QACA,uDAAuD;QACvD,KAAK9D,4BAA4B2H,wBAAwB;YAAE;gBACzD5G,gCAAAA,aAAc6G,wBAAwB;gBACtCpF,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPiE,UAAU3G;oBACVmB,MAAM6C,IAAI7C,IAAI;gBAChB;gBAGF,0EAA0E;gBAC1E,uCAAuC;gBACvCmG,SAASC,MAAM,GAAG,AAAG1H,+BAA6B,MAAGmE,IAAI7C,IAAI,GAAC;gBAE9D,IACE/B,oBAAoBuD,eAAe,IACnC2E,SAASE,eAAe,CAACC,EAAE,KAAK,kBAChC;oBACA,IAAInH,WAAW;oBACfA,YAAY;oBACZ,OAAOwC,OAAOC,QAAQ,CAACC,MAAM;gBAC/B;gBAEAxE,gBAAgB;oBACd0F,OAAOwD,UAAU;oBACjB1I,WAAWuE,SAAS;gBACtB;gBAEA,IAAI9C,QAAQC,GAAG,CAAC8C,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAKjE,4BAA4BkI,WAAW;YAAE;gBAC5CnH,gCAAAA,aAAcoH,YAAY;gBAC1B3F,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPiE,UAAU3G;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAOwC,OAAOC,QAAQ,CAACC,MAAM;YAC/B;QACA,KAAKvD,4BAA4BoI,UAAU;QAC3C,KAAKpI,4BAA4BqI,YAAY;YAAE;gBAC7CtH,gCAAAA,aAAcuH,eAAe;gBAC7B,qFAAqF;gBACrF,OAAO7D,OAAOwD,UAAU;YAC1B;QACA,KAAKjI,4BAA4BuI,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGjE;gBACtB,IAAIiE,WAAW;oBACb,MAAM,EAAE1F,OAAO,EAAEJ,KAAK,EAAE,GAAGK,KAAK0F,KAAK,CAACD;oBACtC,MAAMrD,QAAQ,qBAAkB,CAAlB,IAAIuD,MAAM5F,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/BqC,MAAMzC,KAAK,GAAGA;oBACdkC,aAAa;wBAACO;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKnF,4BAA4B2I,yBAAyB;YAAE;gBAC1D;YACF;QACA,KAAK3I,4BAA4B4I,eAAe;YAAE;gBAChDrJ,WAAWsH,gBAAgB,CAACtC,IAAIuB,IAAI;gBACpC;YACF;QACA;YAAS;gBACPvB;YACF;IACF;AACF;AAEA,eAAe,SAASsE,UAAU,KAQjC;IARiC,IAAA,EAChCC,WAAW,EACXC,QAAQ,EACRC,WAAW,EAKZ,GARiC;IAShCtJ,gBAAgBH,WAAW0J,gBAAgB,EAAE1J,WAAW2J,oBAAoB;IAE5E,MAAMC,eAAerJ,aAAagJ;IAElC/I,iBAAiBoJ;IACjB,MAAM3G,cAAc5C,eAAeuJ;IACnC9I,qBAAqB8I;IACrB,MAAM3E,0BAA0B3E,aAAa2C,aAAa,CAACD,MACzDD,kBAAkBC,KAAKC;IAGzB,MAAMiC,SAAStF;IAEf,8EAA8E;IAC9E,mEAAmE;IACnE,MAAMiK,WAAWnJ;IACjB,MAAMyE,oBAAoB1F,OAAuC,CAAC;IAClE,MAAM2F,cAAc3F,OAAOoK;IAE3B,IAAIpI,QAAQC,GAAG,CAAC2E,oBAAoB,EAAE;QACpC,2DAA2D;QAC3D,mEAAmE;QACnE,sDAAsD;QACtD9G,UAAU;YACR6F,YAAYkB,OAAO,GAAGuD;YAEtB,MAAMC,iBAAiB3E,kBAAkBmB,OAAO;YAEhD,IAAIwD,gBAAgB;gBAClB,IAAID,YAAYA,YAAYC,gBAAgB;oBAC1C,IAAI;wBACF9J,WAAWwG,iBAAiB,CAAC;oBAC/B,EAAE,OAAOuD,QAAQ;wBACf,IAAIxG,UAAU;wBAEd,IAAIwG,kBAAkBC,cAAc;gCAExBD;4BADV,sEAAsE;4BACtExG,UAAUwG,CAAAA,gBAAAA,OAAO5G,KAAK,YAAZ4G,gBAAgBA,OAAOxG,OAAO;wBAC1C,OAAO,IAAIwG,kBAAkBZ,OAAO;gCACaY;4BAA/CxG,UAAU,YAAYwG,OAAOxG,OAAO,GAAG,OAAQwG,CAAAA,CAAAA,iBAAAA,OAAO5G,KAAK,YAAZ4G,iBAAgB,EAAC;wBAClE,OAAO;4BACLxG,UAAU,2BAA2BwG;wBACvC;wBAEA1F,QAAQC,IAAI,CAAC,WAAWf;oBAC1B;gBACF,OAAO;oBACLvD,WAAWwG,iBAAiB,CAAC;gBAC/B;YACF;QACF,GAAG;YAACqD;SAAS;IACf;IAEAtK,UAAU;QACR,MAAM0K,YAAYL,aAAatD,OAAO;QACtC,IAAI,CAAC2D,WAAW;QAEhB,MAAMrH,UAAU,CAACc;YACf,IAAI;gBACF,MAAMsB,MAAMxB,KAAK0F,KAAK,CAACxF,MAAM6C,IAAI;gBACjCxB,eACEC,KACA/B,aACAgC,yBACAC,QACAC,mBACAC;YAEJ,EAAE,OAAOpC,KAAc;gBACrBjD,wBAAwB2D,OAAOV;YACjC;QACF;QAEAiH,UAAUC,gBAAgB,CAAC,WAAWtH;QACtC,OAAO,IAAMqH,UAAUE,mBAAmB,CAAC,WAAWvH;IACxD,GAAG;QACDK;QACAiC;QACA0E;QACA3E;QACAE;KACD;IACD,qBACE,MAACjF;QAA2BuJ,aAAaA;;0BACvC,KAACxJ;gBAAoBmK,iBAAiBpK,WAAWqK,gBAAgB;;YAChEb;;;AAGP", "ignoreList": [0]}