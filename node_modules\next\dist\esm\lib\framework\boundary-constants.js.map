{"version": 3, "sources": ["../../../src/lib/framework/boundary-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\nexport const ROOT_LAYOUT_BOUNDARY_NAME = '__next_root_layout_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME"], "mappings": "AAAA,OAAO,MAAMA,yBAAyB,6BAA4B;AAClE,OAAO,MAAMC,yBAAyB,6BAA4B;AAClE,OAAO,MAAMC,uBAAuB,2BAA0B;AAC9D,OAAO,MAAMC,4BAA4B,gCAA+B", "ignoreList": [0]}