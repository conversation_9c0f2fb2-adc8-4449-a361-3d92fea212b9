{"version": 3, "sources": ["../../../src/shared/lib/promise-with-resolvers.ts"], "sourcesContent": ["export function createPromiseWithResolvers<T>(): PromiseWithResolvers<T> {\n  // Shim of Stage 4 Promise.withResolvers proposal\n  let resolve: (value: T | PromiseLike<T>) => void\n  let reject: (reason: any) => void\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res\n    reject = rej\n  })\n  return { resolve: resolve!, reject: reject!, promise }\n}\n"], "names": ["createPromiseWithResolvers", "resolve", "reject", "promise", "Promise", "res", "rej"], "mappings": "AAAA,OAAO,SAASA;IACd,iDAAiD;IACjD,IAAIC;IACJ,IAAIC;IACJ,MAAMC,UAAU,IAAIC,QAAW,CAACC,KAAKC;QACnCL,UAAUI;QACVH,SAASI;IACX;IACA,OAAO;QAAEL,SAASA;QAAUC,QAAQA;QAASC;IAAQ;AACvD", "ignoreList": [0]}