{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type {\n  <PERSON>R<PERSON>ult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  RenderOpts,\n  Segment,\n  CacheNodeSeedData,\n  PreloadCallbacks,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from './types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type {\n  PrerenderStoreModernRuntime,\n  RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\n\nimport React, { type ErrorInfo, type JSX } from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HASH_COOKIE,\n  NEXT_DID_POSTPONE_HEADER,\n} from '../../client/components/app-router-headers'\nimport { createMetadataContext } from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags, type ImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport { dynamicParamTypes } from './get-short-dynamic-param-type'\nimport { getSegmentParam } from './get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree, getRootParams } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport {\n  getServerModuleMap,\n  setReferenceManifestsSingleton,\n} from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  DynamicHTMLPreludeState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createRenderInBrowserAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  PreludeState,\n  consumeDynamicAccess,\n  type DynamicAccess,\n  logDisallowedDynamicError,\n  warnOnSyncDynamicError,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getIsPossibleServerAction } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../client/components/app-router-instance'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport {\n  prerenderAndAbortInSequentialTasksWithStages,\n  processPrelude,\n} from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport { scheduleInSequentialTasks } from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport { HTML_CONTENT_TYPE_HEADER, INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n  type PrerenderResumeDataCache,\n  type RenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\nimport isError from '../../lib/is-error'\nimport { createServerInsertedMetadata } from './metadata-insertion/create-server-inserted-metadata'\nimport { getPreviouslyRevalidatedTags } from '../server-utils'\nimport { executeRevalidates } from '../revalidation-utils'\nimport {\n  trackPendingChunkLoad,\n  trackPendingImport,\n  trackPendingModules,\n} from './module-loading/track-module-loading.external'\nimport { isReactLargeShellError } from './react-large-shell-error'\nimport type { GlobalErrorComponent } from '../../client/components/builtin/global-error'\nimport { normalizeConventionFilePath } from './segment-explorer-path'\nimport { getRequestMeta } from '../request-meta'\nimport { getDynamicParam } from '../../shared/lib/router/utils/get-dynamic-param'\nimport type { ExperimentalConfig } from '../config-shared'\nimport type { Params } from '../request/params'\nimport { createPromiseWithResolvers } from '../../shared/lib/promise-with-resolvers'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => DynamicParam | null\n\nexport type DynamicParam = {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n}\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppSharedContext = {\n  buildId: string\n}\n\nexport type AppRenderContext = {\n  sharedContext: AppSharedContext\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isPossibleServerAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n  /**\n   * For now, the implicit tags are common for the whole route. If we ever start\n   * rendering/revalidating segments independently, they need to move to the\n   * work unit store.\n   */\n  implicitTags: ImplicitTags\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isDevWarmup: undefined | boolean\n  readonly isRoutePPREnabled: boolean\n  readonly previewModeId: string | undefined\n}\n\nconst flightDataPathHeadKey = 'h'\nconst getFlightViewportKey = (requestId: string) => requestId + 'v'\nconst getFlightMetadataKey = (requestId: string) => requestId + 'm'\n\nconst filterStackFrame =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .filterStackFrameDEV\n    : undefined\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isRuntimePrefetchRequest: boolean\n  readonly isRouteTreePrefetchRequest: boolean\n  readonly isDevWarmupRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n  readonly previouslyRevalidatedTags: string[]\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  const isDevWarmupRequest = options.isDevWarmup === true\n\n  // dev warmup requests are treated as prefetch RSC requests\n  // runtime prefetch requests are *not* treated as prefetch requests\n  // (TODO: this is confusing, we should refactor this to express this better)\n  const isPrefetchRequest =\n    isDevWarmupRequest || headers[NEXT_ROUTER_PREFETCH_HEADER] === '1'\n\n  const isRuntimePrefetchRequest = headers[NEXT_ROUTER_PREFETCH_HEADER] === '2'\n\n  const isHmrRefresh = headers[NEXT_HMR_REFRESH_HEADER] !== undefined\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isRSCRequest = isDevWarmupRequest || headers[RSC_HEADER] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(headers[NEXT_ROUTER_STATE_TREE_HEADER])\n    : undefined\n\n  // Checks if this is a prefetch of the Route Tree by the Segment Cache\n  const isRouteTreePrefetchRequest =\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] === '/_tree'\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n    headers,\n    options.previewModeId\n  )\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isRuntimePrefetchRequest,\n    isRouteTreePrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    isDevWarmupRequest,\n    nonce,\n    previouslyRevalidatedTags,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  const components = loaderTree[2]\n  const hasGlobalNotFound = !!components['global-not-found']\n  return [\n    '',\n    {\n      children: [\n        PAGE_SEGMENT_KEY,\n        {},\n        {\n          page: components['global-not-found'] ?? components['not-found'],\n        },\n      ],\n    },\n    // When global-not-found is present, skip layout from components\n    hasGlobalNotFound ? components : {},\n  ]\n}\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  params: { [key: string]: any },\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n    const segmentKey = segmentParam.param\n    const dynamicParamType = dynamicParamTypes[segmentParam.type]\n    return getDynamicParam(\n      params,\n      segmentKey,\n      dynamicParamType,\n      pagePath,\n      fallbackRouteParams\n    )\n  }\n}\n\nfunction NonIndex({\n  pagePath,\n  statusCode,\n  isPossibleServerAction,\n}: {\n  pagePath: string\n  statusCode: number | undefined\n  isPossibleServerAction: boolean\n}) {\n  const is404Page = pagePath === '/404'\n  const isInvalidStatusCode = typeof statusCode === 'number' && statusCode > 400\n\n  // Only render noindex for page request, skip for server actions\n  // TODO: is this correct if `isPossibleServerAction` is a false positive?\n  if (!isPossibleServerAction && (is404Page || isInvalidStatusCode)) {\n    return <meta name=\"robots\" content=\"noindex\" />\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `next-router-state-tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      tree: loaderTree,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const {\n      ViewportTree,\n      MetadataTree,\n      getViewportReady,\n      getMetadataReady,\n      StreamingMetadataOutlet,\n    } = createMetadataComponents({\n      tree: loaderTree,\n      parsedQuery: query,\n      pathname: url.pathname,\n      metadataContext: createMetadataContext(ctx.renderOpts),\n      getDynamicParamFromSegment,\n      appUsingSizeAdjustment,\n      workStore,\n      MetadataBoundary,\n      ViewportBoundary,\n      serveStreamingMetadata,\n    })\n\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        // For flight, render metadata inside leaf page\n        rscHead: (\n          <React.Fragment key={flightDataPathHeadKey}>\n            {/* noindex needs to be blocking */}\n            <NonIndex\n              pagePath={ctx.pagePath}\n              statusCode={ctx.res.statusCode}\n              isPossibleServerAction={ctx.isPossibleServerAction}\n            />\n            {/* Adding requestId as react key to make metadata remount for each render */}\n            <ViewportTree key={getFlightViewportKey(requestId)} />\n            <MetadataTree key={getFlightMetadataKey(requestId)} />\n          </React.Fragment>\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        getViewportReady,\n        getMetadataReady,\n        preloadCallbacks,\n        StreamingMetadataOutlet,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.sharedContext.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.sharedContext.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    // TODO: is this correct if `isPossibleServerAction` is a false positive?\n    routeType: ctx.isPossibleServerAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    !!renderOpts.dev,\n    onFlightDataRenderError\n  )\n\n  const RSCPayload: RSCPayload & {\n    /** Only available during cacheComponents development builds. Used for logging errors. */\n    _validation?: Promise<React.ReactNode>\n  } = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    ctx.componentMod.renderToReadableStream,\n    RSCPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n      filterStackFrame,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n  })\n}\n\nasync function generateRuntimePrefetchResult(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  requestStore: RequestStore\n): Promise<RenderResult> {\n  const { workStore } = ctx\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      // TODO(runtime-ppr): should we use a different value?\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    false,\n    onFlightDataRenderError\n  )\n\n  const metadata: AppPageRenderResultMetadata = {}\n\n  const generatePayload = () => generateDynamicRSCPayload(ctx, undefined)\n\n  const {\n    componentMod: { tree },\n    getDynamicParamFromSegment,\n  } = ctx\n  const rootParams = getRootParams(tree, getDynamicParamFromSegment)\n\n  // We need to share caches between the prospective prerender and the final prerender,\n  // but we're not going to persist this anywhere.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  // We're not resuming an existing render.\n  const renderResumeDataCache = null\n\n  await prospectiveRuntimeServerPrerender(\n    ctx,\n    generatePayload,\n    prerenderResumeDataCache,\n    renderResumeDataCache,\n    rootParams,\n    requestStore.cookies,\n    requestStore.draftMode\n  )\n\n  const response = await finalRuntimeServerPrerender(\n    ctx,\n    generatePayload,\n    prerenderResumeDataCache,\n    renderResumeDataCache,\n    rootParams,\n    requestStore.cookies,\n    requestStore.draftMode,\n    onError\n  )\n\n  applyMetadataFromPrerenderResult(response, metadata, workStore)\n  metadata.fetchMetrics = ctx.workStore.fetchMetrics\n\n  if (response.isPartial) {\n    res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n  }\n\n  return new FlightRenderResult(response.result.prelude, metadata)\n}\n\nasync function prospectiveRuntimeServerPrerender(\n  ctx: AppRenderContext,\n  getPayload: () => any,\n  prerenderResumeDataCache: PrerenderResumeDataCache | null,\n  renderResumeDataCache: RenderResumeDataCache | null,\n  rootParams: Params,\n  cookies: PrerenderStoreModernRuntime['cookies'],\n  draftMode: PrerenderStoreModernRuntime['draftMode']\n) {\n  const { implicitTags, renderOpts, workStore } = ctx\n\n  const { clientReferenceManifest, ComponentMod } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  // The cacheSignal helps us track whether caches are still filling or we are ready\n  // to cut the render off.\n  const cacheSignal = new CacheSignal()\n\n  const initialServerPrerenderStore: PrerenderStoreModernRuntime = {\n    type: 'prerender-runtime',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    // We only need to track dynamic accesses during the final prerender.\n    dynamicTracking: null,\n    // Runtime prefetches are never cached server-side, only client-side,\n    // so we set `expire` and `revalidate` to their minimum values just in case.\n    revalidate: 1,\n    expire: 0,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    renderResumeDataCache,\n    prerenderResumeDataCache,\n    hmrRefreshHash: undefined,\n    captureOwnerStack: undefined,\n    // We only need task sequencing in the final prerender.\n    runtimeStagePromise: null,\n    // These are not present in regular prerenders, but allowed in a runtime prerender.\n    cookies,\n    draftMode,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const initialServerPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getPayload\n  )\n\n  const pendingInitialServerResult = workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    ComponentMod.prerender,\n    initialServerPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError: (err) => {\n        const digest = getDigestForWellKnownError(err)\n\n        if (digest) {\n          return digest\n        }\n\n        if (initialServerPrerenderController.signal.aborted) {\n          // The render aborted before this error was handled which indicates\n          // the error is caused by unfinished components within the render\n          return\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      },\n      // we don't care to track postpones during the prospective render because we need\n      // to always do a final render anyway\n      onPostpone: undefined,\n      // We don't want to stop rendering until the cacheSignal is complete so we pass\n      // a different signal to this render call than is used by dynamic APIs to signify\n      // transitioning out of the prerender environment\n      signal: initialServerRenderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We don't need to continue the prerender process if we already\n  // detected invalid dynamic usage in the initial prerender phase.\n  if (workStore.invalidDynamicUsageError) {\n    throw workStore.invalidDynamicUsageError\n  }\n\n  try {\n    return await createReactServerPrerenderResult(pendingInitialServerResult)\n  } catch (err) {\n    if (\n      initialServerRenderController.signal.aborted ||\n      initialServerPrerenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, workStore.route)\n    }\n    return null\n  }\n}\n\nasync function finalRuntimeServerPrerender(\n  ctx: AppRenderContext,\n  getPayload: () => any,\n  prerenderResumeDataCache: PrerenderResumeDataCache | null,\n  renderResumeDataCache: RenderResumeDataCache | null,\n  rootParams: Params,\n  cookies: PrerenderStoreModernRuntime['cookies'],\n  draftMode: PrerenderStoreModernRuntime['draftMode'],\n  onError: (err: unknown) => string | undefined\n) {\n  const { implicitTags, renderOpts } = ctx\n\n  const {\n    clientReferenceManifest,\n    ComponentMod,\n    experimental,\n    isDebugDynamicAccesses,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const selectStaleTime = createSelectStaleTime(experimental)\n\n  let serverIsDynamic = false\n  const finalServerController = new AbortController()\n\n  const serverDynamicTracking = createDynamicTrackingState(\n    isDebugDynamicAccesses\n  )\n\n  const { promise: runtimeStagePromise, resolve: resolveBlockedRuntimeAPIs } =\n    createPromiseWithResolvers<void>()\n\n  const finalServerPrerenderStore: PrerenderStoreModernRuntime = {\n    type: 'prerender-runtime',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    // Runtime prefetches are never cached server-side, only client-side,\n    // so we set `expire` and `revalidate` to their minimum values just in case.\n    revalidate: 1,\n    expire: 0,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache,\n    hmrRefreshHash: undefined,\n    captureOwnerStack: undefined,\n    // Used to separate the \"Static\" stage from the \"Runtime\" stage.\n    runtimeStagePromise,\n    // These are not present in regular prerenders, but allowed in a runtime prerender.\n    cookies,\n    draftMode,\n  }\n\n  const finalRSCPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getPayload\n  )\n\n  let prerenderIsPending = true\n  const result = await prerenderAndAbortInSequentialTasksWithStages(\n    async () => {\n      // Static stage\n      const prerenderResult = await workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        ComponentMod.prerender,\n        finalRSCPayload,\n        clientReferenceManifest.clientModules,\n        {\n          filterStackFrame,\n          onError,\n          signal: finalServerController.signal,\n        }\n      )\n      prerenderIsPending = false\n      return prerenderResult\n    },\n    () => {\n      // Advance to the runtime stage.\n      //\n      // We make runtime APIs hang during the first task (above), and unblock them in the following task (here).\n      // This makes sure that, at this point, we'll have finished all the static parts (what we'd prerender statically).\n      // We know that they don't contain any incorrect sync IO, because that'd have caused a build error.\n      // After we unblock Runtime APIs, if we encounter sync IO (e.g. `await cookies(); Date.now()`),\n      // we'll abort, but we'll produce at least as much output as a static prerender would.\n      resolveBlockedRuntimeAPIs()\n    },\n    () => {\n      // Abort.\n      if (finalServerController.signal.aborted) {\n        // If the server controller is already aborted we must have called something\n        // that required aborting the prerender synchronously such as with new Date()\n        serverIsDynamic = true\n        return\n      }\n\n      if (prerenderIsPending) {\n        // If prerenderIsPending then we have blocked for longer than a Task and we assume\n        // there is something unfinished.\n        serverIsDynamic = true\n      }\n      finalServerController.abort()\n    }\n  )\n\n  warnOnSyncDynamicError(serverDynamicTracking)\n\n  return {\n    result,\n    // TODO(runtime-ppr): do we need to produce a digest map here?\n    // digestErrorsMap: ...,\n    dynamicAccess: serverDynamicTracking,\n    isPartial: serverIsDynamic,\n    collectedRevalidate: finalServerPrerenderStore.revalidate,\n    collectedExpire: finalServerPrerenderStore.expire,\n    collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n    collectedTags: finalServerPrerenderStore.tags,\n  }\n}\n\n/**\n * Performs a \"warmup\" render of the RSC payload for a given route. This function is called by the server\n * prior to an actual render request in Dev mode only. It's purpose is to fill caches so the actual render\n * can accurately log activity in the right render context (Prerender vs Render).\n *\n * At the moment this implementation is mostly a fork of generateDynamicFlightRenderResult\n */\nasync function warmupDevRender(\n  req: BaseNextRequest,\n  ctx: AppRenderContext\n): Promise<RenderResult> {\n  const {\n    clientReferenceManifest,\n    componentMod: ComponentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const {\n    allowEmptyStaticShell = false,\n    dev,\n    onInstrumentationRequestError,\n  } = renderOpts\n\n  if (!dev) {\n    throw new InvariantError(\n      'generateDynamicFlightRenderResult should never be called in `next start` mode.'\n    )\n  }\n\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    true,\n    onFlightDataRenderError\n  )\n\n  // We're doing a dev warmup, so we should create a new resume data cache so\n  // we can fill it.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const renderController = new AbortController()\n  const prerenderController = new AbortController()\n  const reactController = new AbortController()\n  const cacheSignal = new CacheSignal()\n\n  const prerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: renderController.signal,\n    controller: prerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash: req.cookies[NEXT_HMR_REFRESH_HASH_COOKIE],\n    captureOwnerStack: ComponentMod.captureOwnerStack,\n    // warmup is a dev only feature and no fallback params are used in the\n    // primary render which is static. We only use a prerender store here to\n    // allow the warmup to halt on Request data APIs and fetches.\n    fallbackRouteParams: null,\n  }\n\n  const rscPayload = await workUnitAsyncStorage.run(\n    prerenderStore,\n    generateDynamicRSCPayload,\n    ctx\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  workUnitAsyncStorage.run(\n    prerenderStore,\n    ComponentMod.renderToReadableStream,\n    rscPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError,\n      signal: renderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  // We unset the cache so any late over-run renders aren't able to write into this cache\n  prerenderStore.prerenderResumeDataCache = null\n  // Abort the render\n  reactController.abort()\n  renderController.abort()\n\n  // We don't really want to return a result here but the stack of functions\n  // that calls into renderToHTML... expects a result. We should refactor this to\n  // lift the warmup pathway outside of renderToHTML... but for now this suffices\n  return new FlightRenderResult('', {\n    fetchMetrics: workStore.fetchMetrics,\n    renderResumeDataCache: createRenderResumeDataCache(\n      prerenderResumeDataCache\n    ),\n  })\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: React.ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const hasGlobalNotFound = !!tree[2]['global-not-found']\n\n  const {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  } = createMetadataComponents({\n    tree,\n    // When it's using global-not-found, metadata errorType is undefined, which will retrieve the\n    // metadata from the page.\n    // When it's using not-found, metadata errorType is 'not-found', which will retrieve the\n    // metadata from the not-found.js boundary.\n    // TODO: remove this condition and keep it undefined when global-not-found is stabilized.\n    errorType: is404 && !hasGlobalNotFound ? 'not-found' : undefined,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const seedData = await createComponentTree({\n    ctx,\n    loaderTree: tree,\n    parentParams: {},\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    getViewportReady,\n    getMetadataReady,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n    StreamingMetadataOutlet,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree />\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: <Preloads preloadCallbacks={preloadCallbacks} />,\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  ssrError: unknown,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const { MetadataTree, ViewportTree } = createMetadataComponents({\n    tree,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata: serveStreamingMetadata,\n  })\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree />\n      {process.env.NODE_ENV === 'development' && (\n        <meta name=\"next-error\" content=\"not-found\" />\n      )}\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  let err: Error | undefined = undefined\n  if (ssrError) {\n    err = isError(ssrError) ? ssrError : new Error(ssrError + '')\n  }\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const seedData: CacheNodeSeedData = [\n    initialTree[0],\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        {process.env.NODE_ENV !== 'production' && err ? (\n          <template\n            data-next-error-message={err.message}\n            data-next-error-digest={'digest' in err ? err.digest : ''}\n            data-next-error-stack={err.stack}\n          />\n        ) : null}\n      </body>\n    </html>,\n    {},\n    null,\n    false,\n  ]\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\nfunction assertClientReferenceManifest(\n  clientReferenceManifest: RenderOpts['clientReferenceManifest']\n): asserts clientReferenceManifest is NonNullable<\n  RenderOpts['clientReferenceManifest']\n> {\n  if (!clientReferenceManifest) {\n    throw new InvariantError('Expected clientReferenceManifest to be defined.')\n  }\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ServerInsertedHTMLProvider>\n        <AppRouter\n          actionQueue={actionQueue}\n          globalErrorState={response.G}\n          assetPrefix={response.p}\n        />\n      </ServerInsertedHTMLProvider>\n    </HeadManagerContext.Provider>\n  )\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction ErrorApp<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  return (\n    <ServerInsertedHTMLProvider>\n      <AppRouter\n        actionQueue={actionQueue}\n        globalErrorState={response.G}\n        assetPrefix={response.p}\n      />\n    </ServerInsertedHTMLProvider>\n  )\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  postponedState: PostponedState | null,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext,\n  fallbackRouteParams: FallbackRouteParams | null\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    clientReferenceManifest,\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we track calls to `loadChunk` and `require`. This allows us\n    // to treat chunk/module loading with similar semantics as cache reads to avoid\n    // module loading from causing a prerender to abort too early.\n\n    const shouldTrackModuleLoading = () => {\n      if (!renderOpts.experimental.cacheComponents) {\n        return false\n      }\n      if (renderOpts.dev) {\n        return true\n      }\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      if (!workUnitStore) {\n        return false\n      }\n\n      switch (workUnitStore.type) {\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-runtime':\n        case 'cache':\n        case 'private-cache':\n          return true\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n        case 'unstable-cache':\n          return false\n        default:\n          workUnitStore satisfies never\n      }\n    }\n\n    const __next_require__: typeof instrumented.require = (...args) => {\n      const exportsOrPromise = instrumented.require(...args)\n      if (shouldTrackModuleLoading()) {\n        // requiring an async module returns a promise.\n        trackPendingImport(exportsOrPromise)\n      }\n      return exportsOrPromise\n    }\n    // @ts-expect-error\n    globalThis.__next_require__ = __next_require__\n\n    const __next_chunk_load__: typeof instrumented.loadChunk = (...args) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      if (shouldTrackModuleLoading()) {\n        trackPendingChunkLoad(loadingChunk)\n      }\n      return loadingChunk\n    }\n    // @ts-expect-error\n    globalThis.__next_chunk_load__ = __next_chunk_load__\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // reset isr status at start of request\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setIsrStatus?.(pathname, null)\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    res.onClose(() => {\n      // We stop tracking fetch metrics when the response closes, since we\n      // report them at that time.\n      workStore.shouldTrackFetchMetrics = false\n    })\n\n    req.originalRequest.on('end', () => {\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {\n    statusCode: isNotFoundPath ? 404 : undefined,\n  }\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const { tree: loaderTree, taintObjectReference } = ComponentMod\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to Client Components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRuntimePrefetchRequest,\n    isRSCRequest,\n    isDevWarmupRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  const { isStaticGeneration } = workStore\n\n  /**\n   * The metadata items array created in next-app-loader with all relevant information\n   * that we need to resolve the final metadata.\n   */\n  let requestId: string\n\n  if (isStaticGeneration) {\n    requestId = Buffer.from(\n      await crypto.subtle.digest('SHA-1', Buffer.from(req.url))\n    ).toString('hex')\n  } else if (process.env.NEXT_RUNTIME === 'edge') {\n    requestId = crypto.randomUUID()\n  } else {\n    requestId = (\n      require('next/dist/compiled/nanoid') as typeof import('next/dist/compiled/nanoid')\n    ).nanoid()\n  }\n\n  /**\n   * Dynamic parameters. E.g. when you visit `/dashboard/vercel` which is rendered by `/dashboard/[slug]` the value will be {\"slug\": \"vercel\"}.\n   */\n  const params = renderOpts.params ?? {}\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    params,\n    pagePath,\n    fallbackRouteParams\n  )\n\n  const isPossibleActionRequest = getIsPossibleServerAction(req)\n\n  const implicitTags = await getImplicitTags(\n    workStore.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isPossibleServerAction: isPossibleActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n    sharedContext,\n    implicitTags,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      loaderTree,\n      fallbackRouteParams\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (workStore.invalidDynamicUsageError) {\n      logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError)\n      throw new StaticGenBailoutError()\n    }\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n      contentType: HTML_CONTENT_TYPE_HEADER,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    applyMetadataFromPrerenderResult(response, metadata, workStore)\n\n    if (response.renderResumeDataCache) {\n      metadata.renderResumeDataCache = response.renderResumeDataCache\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.renderResumeDataCache ?? postponedState?.renderResumeDataCache\n\n    const rootParams = getRootParams(loaderTree, ctx.getDynamicParamFromSegment)\n    const devValidatingFallbackParams =\n      getRequestMeta(req, 'devValidatingFallbackParams') || null\n    const requestStore = createRequestStoreForRender(\n      req,\n      res,\n      url,\n      rootParams,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache,\n      devValidatingFallbackParams\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setIsrStatus &&\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req) &&\n      !isDevWarmupRequest\n    ) {\n      const setIsrStatus = renderOpts.setIsrStatus\n      req.originalRequest.on('end', () => {\n        if (!requestStore.usedDynamic && !workStore.forceDynamic) {\n          // only node can be ISR so we only need to update the status here\n          const { pathname } = new URL(req.url || '/', 'http://n')\n          setIsrStatus(pathname, true)\n        }\n      })\n    }\n\n    if (isDevWarmupRequest) {\n      return warmupDevRender(req, ctx)\n    } else if (isRSCRequest) {\n      if (isRuntimePrefetchRequest) {\n        return generateRuntimePrefetchResult(req, res, ctx, requestStore)\n      } else {\n        return generateDynamicFlightRenderResult(req, ctx, requestStore)\n      }\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let formState: null | any = null\n    if (isPossibleActionRequest) {\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n        metadata,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          metadata.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            notFoundLoaderTree,\n            formState,\n            postponedState,\n            metadata,\n            devValidatingFallbackParams\n          )\n\n          return new RenderResult(stream, {\n            metadata,\n            contentType: HTML_CONTENT_TYPE_HEADER,\n          })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n      contentType: HTML_CONTENT_TYPE_HEADER,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      requestStore,\n      req,\n      res,\n      ctx,\n      loaderTree,\n      formState,\n      postponedState,\n      metadata,\n      devValidatingFallbackParams\n    )\n\n    // Invalid dynamic usages should only error the request in development.\n    // In production, it's better to produce a result.\n    // (the dynamic error will still be thrown inside the component tree, but it's catchable by error boundaries)\n    if (workStore.invalidDynamicUsageError && workStore.dev) {\n      throw workStore.invalidDynamicUsageError\n    }\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  isDevWarmup: boolean,\n  sharedContext: AppSharedContext\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  isDevWarmup,\n  sharedContext\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isDevWarmup,\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n    previewModeId: renderOpts.previewProps?.previewModeId,\n  })\n\n  const { isPrefetchRequest, previouslyRevalidatedTags } = parsedRequestHeaders\n\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      renderOpts.params\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.renderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    renderOpts,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n    buildId: sharedContext.buildId,\n    previouslyRevalidatedTags,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    postponedState,\n    serverComponentsHmrCache,\n    sharedContext,\n    fallbackRouteParams\n  )\n}\n\nfunction applyMetadataFromPrerenderResult(\n  response: Pick<\n    PrerenderToStreamResult,\n    | 'collectedExpire'\n    | 'collectedRevalidate'\n    | 'collectedStale'\n    | 'collectedTags'\n  >,\n  metadata: AppPageRenderResultMetadata,\n  workStore: WorkStore\n) {\n  if (response.collectedTags) {\n    metadata.fetchTags = response.collectedTags.join(',')\n  }\n\n  // Let the client router know how long to keep the cached entry around.\n  const staleHeader = String(response.collectedStale)\n  metadata.headers ??= {}\n  metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n  // If force static is specifically set to false, we should not revalidate\n  // the page.\n  if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n    metadata.cacheControl = { revalidate: 0, expire: undefined }\n  } else {\n    // Copy the cache control value onto the render result metadata.\n    metadata.cacheControl = {\n      revalidate:\n        response.collectedRevalidate >= INFINITE_CACHE\n          ? false\n          : response.collectedRevalidate,\n      expire:\n        response.collectedExpire >= INFINITE_CACHE\n          ? undefined\n          : response.collectedExpire,\n    }\n  }\n\n  // provide bailout info for debugging\n  if (metadata.cacheControl?.revalidate === 0) {\n    metadata.staticBailoutInfo = {\n      description: workStore.dynamicUsageDescription,\n      stack: workStore.dynamicUsageStack,\n    }\n  }\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null,\n  metadata: AppPageRenderResultMetadata,\n  devValidatingFallbackParams: FallbackRouteParams | null\n): Promise<ReadableStream<Uint8Array>> {\n  const { assetPrefix, nonce, pagePath, renderOpts } = ctx\n\n  const {\n    basePath,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod,\n    crossOrigin,\n    dev = false,\n    experimental,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    shouldWaitOnAllReady,\n    subresourceIntegrityManifest,\n    supportsDynamicResponse,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n\n  const setHeader = res.setHeader.bind(res)\n  const appendHeader = res.appendHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when running `next dev`\n      dev &&\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into cacheComponents\n      experimental.cacheComponents\n    ) {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: InitialRSCPayload & {\n        /** Only available during cacheComponents development builds. Used for logging errors. */\n        _validation?: Promise<React.ReactNode>\n      } = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      RSCPayload._validation = validationOutlet\n\n      const reactServerStream = await workUnitAsyncStorage.run(\n        requestStore,\n        scheduleInSequentialTasks,\n        () => {\n          requestStore.prerenderPhase = true\n          return ComponentMod.renderToReadableStream(\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n              environmentName: () =>\n                requestStore.prerenderPhase === true ? 'Prerender' : 'Server',\n              filterStackFrame,\n            }\n          )\n        },\n        () => {\n          requestStore.prerenderPhase = false\n        }\n      )\n\n      spawnDynamicValidationInDev(\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        requestStore,\n        devValidatingFallbackParams\n      )\n\n      reactServerResult = new ReactServerResult(reactServerStream)\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          ComponentMod.renderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: serverComponentsErrorHandler,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const { postponed, preludeState } =\n          getPostponedFromState(postponedState)\n        const resume = (\n          require('react-dom/server') as typeof import('react-dom/server')\n        ).resume\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n          />,\n          postponed,\n          { onError: htmlRendererErrorHandler, nonce }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          // If the prelude is empty (i.e. is no static shell), we should wait for initial HTML to be rendered\n          // to avoid injecting RSC data too early.\n          // If we have a non-empty-prelude (i.e. a static HTML shell), then it's already been sent separately,\n          // so we shouldn't wait for any HTML to be emitted from the resume before sending RSC data.\n          delayDataUntilFirstHtmlChunk:\n            preludeState === DynamicHTMLPreludeState.Empty,\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = (\n      require('react-dom/server') as typeof import('react-dom/server')\n    ).renderToReadableStream\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        nonce={nonce}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            appendHeader(key, value)\n          })\n        },\n        maxHeadersLength: reactMaxHeadersLength,\n        bootstrapScripts: [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n     *\n     *   3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n     *       resolve all suspenses and generate a full HTML. e.g. when it's a\n     *       html limited bot requests, we produce the full HTML content.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML =\n      supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n      buildId: ctx.workStore.buildId,\n      getServerInsertedHTML,\n      getServerInsertedMetadata,\n      validateRootLayout: dev,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? null : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={nonce}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n       *    3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n       *        resolve all suspenses and generate a full HTML. e.g. when it's a\n       *        html limited bot requests, we produce the full HTML content.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML =\n        supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n        buildId: ctx.workStore.buildId,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        getServerInsertedMetadata,\n        validateRootLayout: dev,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: React.ReactNode) => void\n  let outlet = new Promise<React.ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\n/**\n * This function is a fork of prerenderToStream cacheComponents branch.\n * While it doesn't return a stream we want it to have identical\n * prerender semantics to prerenderToStream and should update it\n * in conjunction with any changes to that function.\n */\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: React.ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  requestStore: RequestStore,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<void> {\n  const {\n    componentMod: ComponentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const { allowEmptyStaticShell = false } = renderOpts\n\n  // These values are placeholder values for this validating render\n  // that are provided during the actual prerenderToStream.\n  const preinitScripts = () => {}\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  const hmrRefreshHash = requestStore.cookies.get(\n    NEXT_HMR_REFRESH_HASH_COOKIE\n  )?.value\n\n  // The prerender controller represents the lifetime of the prerender. It will\n  // be aborted when a task is complete or a synchronously aborting API is\n  // called. Notably, during prospective prerenders, this does not actually\n  // terminate the prerender itself, which will continue until all caches are\n  // filled.\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller is used to abort the React prerender.\n  const initialServerReactController = new AbortController()\n\n  // This controller represents the lifetime of the React prerender. Its signal\n  // can be used for any I/O operation to abort the I/O and/or to reject, when\n  // prerendering aborts. This includes our own hanging promises for accessing\n  // request data, and for fetch calls. It might be replaced in the future by\n  // React.cacheSignal(). It's aborted after the React controller, so that no\n  // pending I/O can register abort listeners that are called before React's\n  // abort listener is called. This ensures that pending I/O is not rejected too\n  // early when aborting the prerender. Notably, during the prospective\n  // prerender, it is different from the prerender controller because we don't\n  // want to end the React prerender until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  // The cacheSignal helps us track whether caches are still filling or we are\n  // ready to cut the render off.\n  const cacheSignal = new CacheSignal()\n\n  const captureOwnerStackClient = React.captureOwnerStack\n  const captureOwnerStackServer = ComponentMod.captureOwnerStack\n\n  // The resume data cache here should use a fresh instance as it's\n  // performing a fresh prerender. If we get to implementing the\n  // prerendering of an already prerendered page, we should use the passed\n  // resume data cache instead.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPayloadPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n    // various request data APIs bind to this controller to reject after completion.\n    renderSignal: initialServerRenderController.signal,\n    // When we generate the RSC payload we might abort this controller due to sync IO\n    // but we don't actually care about sync IO in this phase so we use a throw away controller\n    // that isn't connected to anything\n    controller: new AbortController(),\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const initialServerPayload = await workUnitAsyncStorage.run(\n    initialServerPayloadPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const pendingInitialServerResult = workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    ComponentMod.prerender,\n    initialServerPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError: (err) => {\n        const digest = getDigestForWellKnownError(err)\n\n        if (digest) {\n          return digest\n        }\n\n        if (isReactLargeShellError(err)) {\n          // TODO: Aggregate\n          console.error(err)\n          return undefined\n        }\n\n        if (initialServerPrerenderController.signal.aborted) {\n          // The render aborted before this error was handled which indicates\n          // the error is caused by unfinished components within the render\n          return\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      },\n      // we don't care to track postpones during the prospective render because we need\n      // to always do a final render anyway\n      onPostpone: undefined,\n      // We don't want to stop rendering until the cacheSignal is complete so we pass\n      // a different signal to this render call than is used by dynamic APIs to signify\n      // transitioning out of the prerender environment\n      signal: initialServerReactController.signal,\n    }\n  )\n\n  // The listener to abort our own render controller must be added after React\n  // has added its listener, to ensure that pending I/O is not aborted/rejected\n  // too early.\n  initialServerReactController.signal.addEventListener(\n    'abort',\n    () => {\n      initialServerRenderController.abort()\n    },\n    { once: true }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  initialServerReactController.abort()\n\n  // We don't need to continue the prerender process if we already\n  // detected invalid dynamic usage in the initial prerender phase.\n  const { invalidDynamicUsageError } = workStore\n  if (invalidDynamicUsageError) {\n    resolveValidation(\n      <LogSafely\n        fn={() => {\n          console.error(invalidDynamicUsageError)\n        }}\n      />\n    )\n    return\n  }\n\n  let initialServerResult\n  try {\n    initialServerResult = await createReactServerPrerenderResult(\n      pendingInitialServerResult\n    )\n  } catch (err) {\n    if (\n      initialServerReactController.signal.aborted ||\n      initialServerPrerenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, workStore.route)\n    }\n  }\n\n  if (initialServerResult) {\n    const initialClientPrerenderController = new AbortController()\n    const initialClientReactController = new AbortController()\n    const initialClientRenderController = new AbortController()\n\n    const initialClientPrerenderStore: PrerenderStore = {\n      type: 'prerender-client',\n      phase: 'render',\n      rootParams,\n      fallbackRouteParams,\n      implicitTags,\n      renderSignal: initialClientRenderController.signal,\n      controller: initialClientPrerenderController,\n      // For HTML Generation the only cache tracked activity\n      // is module loading, which has it's own cache signal\n      cacheSignal: null,\n      dynamicTracking: null,\n      allowEmptyStaticShell,\n      revalidate: INFINITE_CACHE,\n      expire: INFINITE_CACHE,\n      stale: INFINITE_CACHE,\n      tags: [...implicitTags.tags],\n      prerenderResumeDataCache,\n      renderResumeDataCache: null,\n      hmrRefreshHash: undefined,\n      captureOwnerStack: captureOwnerStackClient,\n    }\n\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      <App\n        reactServerStream={initialServerResult.asUnclosingStream()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        nonce={nonce}\n      />,\n      {\n        signal: initialClientReactController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (isReactLargeShellError(err)) {\n            // TODO: Aggregate\n            console.error(err)\n            return undefined\n          }\n\n          if (initialClientReactController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        },\n        // We don't need bootstrap scripts in this prerender\n        // bootstrapScripts: [bootstrapScript],\n      }\n    )\n\n    // The listener to abort our own render controller must be added after React\n    // has added its listener, to ensure that pending I/O is not\n    // aborted/rejected too early.\n    initialClientReactController.signal.addEventListener(\n      'abort',\n      () => {\n        initialClientRenderController.abort()\n      },\n      { once: true }\n    )\n\n    pendingInitialClientResult.catch((err) => {\n      if (\n        initialClientReactController.signal.aborted ||\n        isPrerenderInterruptedError(err)\n      ) {\n        // These are expected errors that might error the prerender. we ignore them.\n      } else if (\n        process.env.NEXT_DEBUG_BUILD ||\n        process.env.__NEXT_VERBOSE_LOGGING\n      ) {\n        // We don't normally log these errors because we are going to retry anyway but\n        // it can be useful for debugging Next.js itself to get visibility here when needed\n        printDebugThrownValueForProspectiveRender(err, workStore.route)\n      }\n    })\n\n    // This is mostly needed for dynamic `import()`s in client components.\n    // Promises passed to client were already awaited above (assuming that they came from cached functions)\n    trackPendingModules(cacheSignal)\n    await cacheSignal.cacheReady()\n    initialClientReactController.abort()\n  }\n\n  const finalServerReactController = new AbortController()\n  const finalServerRenderController = new AbortController()\n\n  const finalServerPayloadPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n    // various request data APIs bind to this controller to reject after completion.\n    renderSignal: finalServerRenderController.signal,\n    // When we generate the RSC payload we might abort this controller due to sync IO\n    // but we don't actually care about sync IO in this phase so we use a throw away controller\n    // that isn't connected to anything\n    controller: new AbortController(),\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n    finalServerPayloadPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const serverDynamicTracking = createDynamicTrackingState(\n    false // isDebugDynamicAccesses\n  )\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    renderSignal: finalServerRenderController.signal,\n    controller: finalServerReactController,\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const reactServerResult = await createReactServerPrerenderResult(\n    prerenderAndAbortInSequentialTasks(\n      async () => {\n        const pendingPrerenderResult = workUnitAsyncStorage.run(\n          // The store to scope\n          finalServerPrerenderStore,\n          // The function to run\n          ComponentMod.prerender,\n          // ... the arguments for the function to run\n          finalAttemptRSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: (err: unknown) => {\n              if (\n                finalServerReactController.signal.aborted &&\n                isPrerenderInterruptedError(err)\n              ) {\n                return err.digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n            signal: finalServerReactController.signal,\n          }\n        )\n\n        // The listener to abort our own render controller must be added after\n        // React has added its listener, to ensure that pending I/O is not\n        // aborted/rejected too early.\n        finalServerReactController.signal.addEventListener(\n          'abort',\n          () => {\n            finalServerRenderController.abort()\n          },\n          { once: true }\n        )\n\n        return pendingPrerenderResult\n      },\n      () => {\n        finalServerReactController.abort()\n      }\n    )\n  )\n\n  const clientDynamicTracking = createDynamicTrackingState(\n    false //isDebugDynamicAccesses\n  )\n  const finalClientReactController = new AbortController()\n  const finalClientRenderController = new AbortController()\n\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender-client',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    renderSignal: finalClientRenderController.signal,\n    controller: finalClientReactController,\n    // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackClient,\n  }\n\n  let dynamicValidation = createDynamicValidationState()\n\n  try {\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    let { prelude: unprocessedPrelude } =\n      await prerenderAndAbortInSequentialTasks(\n        () => {\n          const pendingFinalClientResult = workUnitAsyncStorage.run(\n            finalClientPrerenderStore,\n            prerender,\n            <App\n              reactServerStream={reactServerResult.asUnclosingStream()}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={nonce}\n            />,\n            {\n              signal: finalClientReactController.signal,\n              onError: (err: unknown, errorInfo: ErrorInfo) => {\n                if (\n                  isPrerenderInterruptedError(err) ||\n                  finalClientReactController.signal.aborted\n                ) {\n                  const componentStack = errorInfo.componentStack\n                  if (typeof componentStack === 'string') {\n                    trackAllowedDynamicAccess(\n                      workStore,\n                      componentStack,\n                      dynamicValidation,\n                      clientDynamicTracking\n                    )\n                  }\n                  return\n                }\n\n                if (isReactLargeShellError(err)) {\n                  // TODO: Aggregate\n                  console.error(err)\n                  return undefined\n                }\n\n                return getDigestForWellKnownError(err)\n              },\n              // We don't need bootstrap scripts in this prerender\n              // bootstrapScripts: [bootstrapScript],\n            }\n          )\n\n          // The listener to abort our own render controller must be added after\n          // React has added its listener, to ensure that pending I/O is not\n          // aborted/rejected too early.\n          finalClientReactController.signal.addEventListener(\n            'abort',\n            () => {\n              finalClientRenderController.abort()\n            },\n            { once: true }\n          )\n\n          return pendingFinalClientResult\n        },\n        () => {\n          finalClientReactController.abort()\n        }\n      )\n\n    const { preludeIsEmpty } = await processPrelude(unprocessedPrelude)\n    resolveValidation(\n      <LogSafely\n        fn={throwIfDisallowedDynamic.bind(\n          null,\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        )}\n      />\n    )\n  } catch (thrownValue) {\n    // Even if the root errors we still want to report any cache components errors\n    // that were discovered before the root errored.\n\n    let loggingFunction = throwIfDisallowedDynamic.bind(\n      null,\n      workStore,\n      PreludeState.Errored,\n      dynamicValidation,\n      serverDynamicTracking\n    )\n\n    if (process.env.NEXT_DEBUG_BUILD || process.env.__NEXT_VERBOSE_LOGGING) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      const originalLoggingFunction = loggingFunction\n      loggingFunction = () => {\n        console.error(\n          'During dynamic validation the root of the page errored. The next logged error is the thrown value. It may be a duplicate of errors reported during the normal development mode render.'\n        )\n        console.error(thrownValue)\n        originalLoggingFunction()\n      }\n    }\n\n    resolveValidation(<LogSafely fn={loggingFunction} />)\n  }\n}\n\nasync function LogSafely({ fn }: { fn: () => unknown }) {\n  try {\n    await fn()\n  } catch {}\n  return null\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n  renderResumeDataCache?: RenderResumeDataCache\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  tree: LoaderTree,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n\n  const {\n    assetPrefix,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    pagePath,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const {\n    allowEmptyStaticShell = false,\n    basePath,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod,\n    crossOrigin,\n    dev = false,\n    experimental,\n    isDebugDynamicAccesses,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    subresourceIntegrityManifest,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const rootParams = getRootParams(tree, getDynamicParamFromSegment)\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult: null | ReactServerPrerenderResult = null\n  const setMetadataHeader = (name: string) => {\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n  }\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n    setMetadataHeader(name)\n    return res\n  }\n  const appendHeader = (name: string, value: string | string[]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => {\n        res.appendHeader(name, item)\n      })\n    } else {\n      res.appendHeader(name, value)\n    }\n    setMetadataHeader(name)\n  }\n\n  const selectStaleTime = createSelectStaleTime(experimental)\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (experimental.cacheComponents) {\n      /**\n       * cacheComponents with PPR\n       *\n       * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n       * Once we have settled all cache reads we restart the render and abort after a single Task.\n       *\n       * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n       * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n       * and a synchronous abort might prevent us from filling all caches.\n       *\n       * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n       * and the reactServerIsDynamic value to determine how to treat the resulting render\n       */\n\n      // The prerender controller represents the lifetime of the prerender. It\n      // will be aborted when a task is complete or a synchronously aborting API\n      // is called. Notably, during prospective prerenders, this does not\n      // actually terminate the prerender itself, which will continue until all\n      // caches are filled.\n      const initialServerPrerenderController = new AbortController()\n\n      // This controller is used to abort the React prerender.\n      const initialServerReactController = new AbortController()\n\n      // This controller represents the lifetime of the React prerender. Its\n      // signal can be used for any I/O operation to abort the I/O and/or to\n      // reject, when prerendering aborts. This includes our own hanging\n      // promises for accessing request data, and for fetch calls. It might be\n      // replaced in the future by React.cacheSignal(). It's aborted after the\n      // React controller, so that no pending I/O can register abort listeners\n      // that are called before React's abort listener is called. This ensures\n      // that pending I/O is not rejected too early when aborting the prerender.\n      // Notably, during the prospective prerender, it is different from the\n      // prerender controller because we don't want to end the React prerender\n      // until all caches are filled.\n      const initialServerRenderController = new AbortController()\n\n      // The cacheSignal helps us track whether caches are still filling or we are ready\n      // to cut the render off.\n      const cacheSignal = new CacheSignal()\n\n      let resumeDataCache: RenderResumeDataCache | PrerenderResumeDataCache\n      let renderResumeDataCache: RenderResumeDataCache | null = null\n      let prerenderResumeDataCache: PrerenderResumeDataCache | null = null\n\n      if (renderOpts.renderResumeDataCache) {\n        // If a prefilled immutable render resume data cache is provided, e.g.\n        // when prerendering an optional fallback shell after having prerendered\n        // pages with defined params, we use this instead of a prerender resume\n        // data cache.\n        resumeDataCache = renderResumeDataCache =\n          renderOpts.renderResumeDataCache\n      } else {\n        // Otherwise we create a new mutable prerender resume data cache.\n        resumeDataCache = prerenderResumeDataCache =\n          createPrerenderResumeDataCache()\n      }\n\n      const initialServerPayloadPrerenderStore: PrerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n        // various request data APIs bind to this controller to reject after completion.\n        renderSignal: initialServerRenderController.signal,\n        // When we generate the RSC payload we might abort this controller due to sync IO\n        // but we don't actually care about sync IO in this phase so we use a throw away controller\n        // that isn't connected to anything\n        controller: new AbortController(),\n        // During the initial prerender we need to track all cache reads to ensure\n        // we render long enough to fill every cache it is possible to visit during\n        // the final prerender.\n        cacheSignal,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      // We're not going to use the result of this render because the only time it could be used\n      // is if it completes in a microtask and that's likely very rare for any non-trivial app\n      const initialServerPayload = await workUnitAsyncStorage.run(\n        initialServerPayloadPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        renderSignal: initialServerRenderController.signal,\n        controller: initialServerPrerenderController,\n        // During the initial prerender we need to track all cache reads to ensure\n        // we render long enough to fill every cache it is possible to visit during\n        // the final prerender.\n        cacheSignal,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      const pendingInitialServerResult = workUnitAsyncStorage.run(\n        initialServerPrerenderStore,\n        ComponentMod.prerender,\n        initialServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          filterStackFrame,\n          onError: (err) => {\n            const digest = getDigestForWellKnownError(err)\n\n            if (digest) {\n              return digest\n            }\n\n            if (isReactLargeShellError(err)) {\n              // TODO: Aggregate\n              console.error(err)\n              return undefined\n            }\n\n            if (initialServerPrerenderController.signal.aborted) {\n              // The render aborted before this error was handled which indicates\n              // the error is caused by unfinished components within the render\n              return\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          },\n          // we don't care to track postpones during the prospective render because we need\n          // to always do a final render anyway\n          onPostpone: undefined,\n          // We don't want to stop rendering until the cacheSignal is complete so we pass\n          // a different signal to this render call than is used by dynamic APIs to signify\n          // transitioning out of the prerender environment\n          signal: initialServerReactController.signal,\n        }\n      )\n\n      // The listener to abort our own render controller must be added after\n      // React has added its listener, to ensure that pending I/O is not\n      // aborted/rejected too early.\n      initialServerReactController.signal.addEventListener(\n        'abort',\n        () => {\n          initialServerRenderController.abort()\n        },\n        { once: true }\n      )\n\n      // Wait for all caches to be finished filling and for async imports to resolve\n      trackPendingModules(cacheSignal)\n      await cacheSignal.cacheReady()\n\n      initialServerReactController.abort()\n\n      // We don't need to continue the prerender process if we already\n      // detected invalid dynamic usage in the initial prerender phase.\n      if (workStore.invalidDynamicUsageError) {\n        logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError)\n        throw new StaticGenBailoutError()\n      }\n\n      let initialServerResult\n      try {\n        initialServerResult = await createReactServerPrerenderResult(\n          pendingInitialServerResult\n        )\n      } catch (err) {\n        if (\n          initialServerReactController.signal.aborted ||\n          initialServerPrerenderController.signal.aborted\n        ) {\n          // These are expected errors that might error the prerender. we ignore them.\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          // We don't normally log these errors because we are going to retry anyway but\n          // it can be useful for debugging Next.js itself to get visibility here when needed\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      }\n\n      if (initialServerResult) {\n        const initialClientPrerenderController = new AbortController()\n        const initialClientReactController = new AbortController()\n        const initialClientRenderController = new AbortController()\n\n        const initialClientPrerenderStore: PrerenderStore = {\n          type: 'prerender-client',\n          phase: 'render',\n          rootParams,\n          fallbackRouteParams,\n          implicitTags,\n          renderSignal: initialClientRenderController.signal,\n          controller: initialClientPrerenderController,\n          // For HTML Generation the only cache tracked activity\n          // is module loading, which has it's own cache signal\n          cacheSignal: null,\n          dynamicTracking: null,\n          allowEmptyStaticShell,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          renderResumeDataCache,\n          hmrRefreshHash: undefined,\n          captureOwnerStack: undefined, // Not available in production.\n        }\n\n        const prerender = (\n          require('react-dom/static') as typeof import('react-dom/static')\n        ).prerender\n        const pendingInitialClientResult = workUnitAsyncStorage.run(\n          initialClientPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={initialServerResult.asUnclosingStream()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n          />,\n          {\n            signal: initialClientReactController.signal,\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              if (initialClientReactController.signal.aborted) {\n                // These are expected errors that might error the prerender. we ignore them.\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                // We don't normally log these errors because we are going to retry anyway but\n                // it can be useful for debugging Next.js itself to get visibility here when needed\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            bootstrapScripts: [bootstrapScript],\n          }\n        )\n\n        // The listener to abort our own render controller must be added after\n        // React has added its listener, to ensure that pending I/O is not\n        // aborted/rejected too early.\n        initialClientReactController.signal.addEventListener(\n          'abort',\n          () => {\n            initialClientRenderController.abort()\n          },\n          { once: true }\n        )\n\n        pendingInitialClientResult.catch((err) => {\n          if (\n            initialClientReactController.signal.aborted ||\n            isPrerenderInterruptedError(err)\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        })\n\n        // This is mostly needed for dynamic `import()`s in client components.\n        // Promises passed to client were already awaited above (assuming that they came from cached functions)\n        trackPendingModules(cacheSignal)\n        await cacheSignal.cacheReady()\n        initialClientReactController.abort()\n      }\n\n      const finalServerReactController = new AbortController()\n      const finalServerRenderController = new AbortController()\n\n      const finalServerPayloadPrerenderStore: PrerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n        // various request data APIs bind to this controller to reject after completion.\n        renderSignal: finalServerRenderController.signal,\n        // When we generate the RSC payload we might abort this controller due to sync IO\n        // but we don't actually care about sync IO in this phase so we use a throw away controller\n        // that isn't connected to anything\n        controller: new AbortController(),\n        // All caches we could read must already be filled so no tracking is necessary\n        cacheSignal: null,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n        finalServerPayloadPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const serverDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n      let serverIsDynamic = false\n\n      const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        renderSignal: finalServerRenderController.signal,\n        controller: finalServerReactController,\n        // All caches we could read must already be filled so no tracking is necessary\n        cacheSignal: null,\n        dynamicTracking: serverDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      let prerenderIsPending = true\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResult(\n          prerenderAndAbortInSequentialTasks(\n            async () => {\n              const pendingPrerenderResult = workUnitAsyncStorage.run(\n                // The store to scope\n                finalServerPrerenderStore,\n                // The function to run\n                ComponentMod.prerender,\n                // ... the arguments for the function to run\n                finalAttemptRSCPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  filterStackFrame,\n                  onError: (err: unknown) => {\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerReactController.signal,\n                }\n              )\n\n              // The listener to abort our own render controller must be added\n              // after React has added its listener, to ensure that pending I/O\n              // is not aborted/rejected too early.\n              finalServerReactController.signal.addEventListener(\n                'abort',\n                () => {\n                  finalServerRenderController.abort()\n                },\n                { once: true }\n              )\n\n              const prerenderResult = await pendingPrerenderResult\n              prerenderIsPending = false\n\n              return prerenderResult\n            },\n            () => {\n              if (finalServerReactController.signal.aborted) {\n                // If the server controller is already aborted we must have called something\n                // that required aborting the prerender synchronously such as with new Date()\n                serverIsDynamic = true\n                return\n              }\n\n              if (prerenderIsPending) {\n                // If prerenderIsPending then we have blocked for longer than a Task and we assume\n                // there is something unfinished.\n                serverIsDynamic = true\n              }\n\n              finalServerReactController.abort()\n            }\n          )\n        ))\n\n      const clientDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n\n      const finalClientReactController = new AbortController()\n      const finalClientRenderController = new AbortController()\n\n      const finalClientPrerenderStore: PrerenderStore = {\n        type: 'prerender-client',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        renderSignal: finalClientRenderController.signal,\n        controller: finalClientReactController,\n        // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n        cacheSignal: null,\n        dynamicTracking: clientDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      let dynamicValidation = createDynamicValidationState()\n\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      let { prelude: unprocessedPrelude, postponed } =\n        await prerenderAndAbortInSequentialTasks(\n          () => {\n            const pendingFinalClientResult = workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                nonce={nonce}\n              />,\n              {\n                signal: finalClientReactController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientReactController.signal.aborted\n                  ) {\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore,\n                        componentStack,\n                        dynamicValidation,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    appendHeader(key, value)\n                  })\n                },\n                maxHeadersLength: reactMaxHeadersLength,\n                bootstrapScripts: [bootstrapScript],\n              }\n            )\n\n            // The listener to abort our own render controller must be added\n            // after React has added its listener, to ensure that pending I/O is\n            // not aborted/rejected too early.\n            finalClientReactController.signal.addEventListener(\n              'abort',\n              () => {\n                finalClientRenderController.abort()\n              },\n              { once: true }\n            )\n\n            return pendingFinalClientResult\n          },\n          () => {\n            finalClientReactController.abort()\n          }\n        )\n\n      const { prelude, preludeIsEmpty } =\n        await processPrelude(unprocessedPrelude)\n\n      // If we've disabled throwing on empty static shell, then we don't need to\n      // track any dynamic access that occurs above the suspense boundary because\n      // we'll do so in the route shell.\n      if (!allowEmptyStaticShell) {\n        throwIfDisallowedDynamic(\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n      metadata.flightData = flightData\n      metadata.segmentData = await collectSegmentData(\n        flightData,\n        finalServerPrerenderStore,\n        ComponentMod,\n        renderOpts\n      )\n\n      // If there are fallback route params, the RSC data is inherently dynamic\n      // today because it's encoded into the flight router state. Until we can\n      // move the fallback route params out of the flight router state, we need\n      // to always perform a dynamic resume after the static prerender.\n      const hasFallbackRouteParams =\n        fallbackRouteParams && fallbackRouteParams.size > 0\n\n      if (serverIsDynamic || hasFallbackRouteParams) {\n        // Dynamic case\n        // We will always need to perform a \"resume\" render of some kind when this route is accessed\n        // because the RSC data itself is dynamic. We determine if there are any HTML holes or not\n        // but generally this is a \"partial\" prerender in that there will be a per-request compute\n        // concatenated to the static shell.\n        if (postponed != null) {\n          // Dynamic HTML case\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            preludeIsEmpty\n              ? DynamicHTMLPreludeState.Empty\n              : DynamicHTMLPreludeState.Full,\n            fallbackRouteParams,\n            resumeDataCache\n          )\n        } else {\n          // Dynamic Data case\n          metadata.postponed =\n            await getDynamicDataPostponedState(resumeDataCache)\n        }\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      } else {\n        // Static case\n        // We will not perform resumption per request. The result can be served statically to the requestor\n        // and if there was anything dynamic it will only be rendered in the browser.\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createRenderInBrowserAbortSignal(),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      }\n    } else if (experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(isDebugDynamicAccesses)\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      }\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      const { prelude: unprocessedPrelude, postponed } =\n        await workUnitAsyncStorage.run(\n          ssrPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={reactServerResult.asUnclosingStream()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n          />,\n          {\n            onError: htmlRendererErrorHandler,\n            onHeaders: (headers: Headers) => {\n              headers.forEach((value, key) => {\n                appendHeader(key, value)\n              })\n            },\n            maxHeadersLength: reactMaxHeadersLength,\n            bootstrapScripts: [bootstrapScript],\n          }\n        )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      const { prelude, preludeIsEmpty } =\n        await processPrelude(unprocessedPrelude)\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            preludeIsEmpty\n              ? DynamicHTMLPreludeState.Empty\n              : DynamicHTMLPreludeState.Full,\n            fallbackRouteParams,\n            prerenderResumeDataCache\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createRenderInBrowserAbortSignal(),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = (\n        require('react-dom/server') as typeof import('react-dom/server')\n      ).renderToReadableStream\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: selectStaleTime(prerenderLegacyStore.stale),\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      rootParams,\n      implicitTags: implicitTags,\n      revalidate:\n        typeof prerenderStore?.revalidate !== 'undefined'\n          ? prerenderStore.revalidate\n          : INFINITE_CACHE,\n      expire:\n        typeof prerenderStore?.expire !== 'undefined'\n          ? prerenderStore.expire\n          : INFINITE_CACHE,\n      stale:\n        typeof prerenderStore?.stale !== 'undefined'\n          ? prerenderStore.stale\n          : INFINITE_CACHE,\n      tags: [...(prerenderStore?.tags || implicitTags.tags)],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? undefined : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      // TODO we should use the same prerender semantics that we initially rendered\n      // with in this case too. The only reason why this is ok atm is because it's essentially\n      // an empty page and no user code runs.\n      const fizzStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={nonce}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream = reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          getServerInsertedMetadata,\n          validateRootLayout: dev,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale: selectStaleTime(\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE\n        ),\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<{\n  GlobalError: GlobalErrorComponent\n  styles: React.ReactNode | undefined\n}> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  const GlobalErrorComponent: GlobalErrorComponent =\n    ctx.componentMod.GlobalError\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n  if (ctx.renderOpts.dev) {\n    const dir =\n      (process.env.NEXT_RUNTIME === 'edge'\n        ? process.env.__NEXT_EDGE_PROJECT_DIR\n        : ctx.renderOpts.dir) || ''\n\n    const globalErrorModulePath = normalizeConventionFilePath(\n      dir,\n      globalErrorModule?.[1]\n    )\n    if (ctx.renderOpts.devtoolSegmentExplorer && globalErrorModulePath) {\n      const SegmentViewNode = ctx.componentMod.SegmentViewNode\n      globalErrorStyles = (\n        // This will be rendered next to GlobalError component under ErrorBoundary,\n        // it requires a key to avoid React warning about duplicate keys.\n        <SegmentViewNode\n          key=\"ge-svn\"\n          type=\"global-error\"\n          pagePath={globalErrorModulePath}\n        >\n          {globalErrorStyles}\n        </SegmentViewNode>\n      )\n    }\n  }\n\n  return {\n    GlobalError: GlobalErrorComponent,\n    styles: globalErrorStyles,\n  }\n}\n\nfunction createSelectStaleTime(experimental: ExperimentalConfig) {\n  return (stale: number) =>\n    stale === INFINITE_CACHE &&\n    typeof experimental.staleTimes?.static === 'number'\n      ? experimental.staleTimes.static\n      : stale\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (\n    !clientReferenceManifest ||\n    // Do not generate per-segment data unless the experimental Segment Cache\n    // flag is enabled.\n    //\n    // We also skip generating segment data if flag is set to \"client-only\",\n    // rather than true. (The \"client-only\" option only affects the behavior of\n    // the client-side implementation; per-segment prefetches are intentionally\n    // disabled in that configuration).\n    renderOpts.experimental.clientSegmentCache !== true\n  ) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: getServerModuleMap(),\n  }\n\n  const staleTime = prerenderStore.stale\n  return await ComponentMod.collectSegmentData(\n    renderOpts.experimental.clientParamParsing,\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest\n  )\n}\n"], "names": ["workAsyncStorage", "React", "RenderResult", "chainStreams", "renderToInitialFizzStream", "createDocumentClosingStream", "continueFizzStream", "continueDynamicPrerender", "continueStaticP<PERSON><PERSON>", "continueDynamicHTMLResume", "streamToBuffer", "streamToString", "stripInternalQueries", "NEXT_HMR_REFRESH_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_URL", "RSC_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_DID_POSTPONE_HEADER", "createMetadataContext", "createRequestStoreForRender", "createWorkStore", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "getURLFromRedirectError", "getRedirectStatusCodeFromError", "isRedirectError", "getImplicitTags", "AppRenderSpan", "NextNodeServerSpan", "getTracer", "FlightRenderResult", "createFlightReactServerErrorHandler", "createHTMLReactServerErrorHandler", "createHTMLErrorHandler", "isUserLandError", "getDigestForWellKnownError", "dynamicParamTypes", "getSegmentParam", "getScriptNonceFromHeader", "parseAndValidateFlightRouterState", "createFlightRouterStateFromLoaderTree", "handleAction", "isBailoutToCSRError", "warn", "error", "appendMutableCookies", "createServerInsertedHTML", "getRequiredScripts", "addPathPrefix", "makeGetServerInsertedHTML", "walkTreeWithFlightRouterState", "createComponentTree", "getRootParams", "getAssetQueryString", "getServerModuleMap", "setReferenceManifestsSingleton", "DynamicState", "DynamicHTMLPreludeState", "parsePostponedState", "getDynamicDataPostponedState", "getDynamicHTMLPostponedState", "getPostponedFromState", "isDynamicServerError", "useFlightStream", "createInlinedDataReadableStream", "StaticGenBailoutError", "isStaticGenBailoutError", "getStackWithoutErrorMessage", "accessedDynamicData", "createRenderInBrowserAbortSignal", "formatDynamicAPIAccesses", "isPrerenderInterruptedError", "createDynamicTrackingState", "createDynamicValidationState", "trackAllowedDynamicAccess", "throwIfDisallowedDynamic", "PreludeState", "consumeDynamicAccess", "logDisallowedDynamicError", "warnOnSyncDynamicError", "getClientComponentLoaderMetrics", "wrapClientComponentLoader", "createServerModuleMap", "isNodeNextRequest", "parseRelativeUrl", "AppRouter", "getIsPossibleServerAction", "createInitialRouterState", "createMutableActionQueue", "getRevalidateReason", "PAGE_SEGMENT_KEY", "prerenderAndAbortInSequentialTasksWithStages", "processPrelude", "ReactServerResult", "createReactServerPrerenderResult", "createReactServerPrerenderResultFromRender", "prerenderAndAbortInSequentialTasks", "printDebugThrownValueForProspectiveRender", "scheduleInSequentialTasks", "waitAtLeastOneReactRenderTask", "workUnitAsyncStorage", "CacheSignal", "getTracedMetadata", "InvariantError", "HTML_CONTENT_TYPE_HEADER", "INFINITE_CACHE", "createComponentStylesAndScripts", "parseLoaderTree", "createPrerenderResumeDataCache", "createRenderResumeDataCache", "isError", "createServerInsertedMetadata", "getPreviouslyRevalidatedTags", "executeRevalidates", "trackPendingChunkLoad", "trackPendingImport", "trackPendingModules", "isReactLargeShellError", "normalizeConventionFilePath", "getRequestMeta", "getDynamicParam", "createPromiseWithResolvers", "flightDataPathHeadKey", "getFlightViewportKey", "requestId", "getFlightMetadataKey", "filterStackFrame", "process", "env", "NODE_ENV", "require", "filterStackFrameDEV", "undefined", "parseRequestHeaders", "headers", "options", "isDevWarmupRequest", "isDevWarmup", "isPrefetchRequest", "isRuntimePrefetchRequest", "isHmrRefresh", "isRSCRequest", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "isRouteTreePrefetchRequest", "csp", "nonce", "previouslyRevalidatedTags", "previewModeId", "createNotFoundLoaderTree", "loaderTree", "components", "hasGlobalNotFound", "children", "page", "makeGetDynamicParamFromSegment", "params", "pagePath", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "segmentKey", "param", "dynamicParamType", "type", "NonIndex", "statusCode", "isPossibleServerAction", "is404Page", "isInvalidStatusCode", "meta", "name", "content", "generateDynamicRSCPayload", "ctx", "flightData", "componentMod", "tree", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "appUsingSizeAdjustment", "query", "workStore", "url", "serveStreamingMetadata", "renderOpts", "skipFlight", "preloadCallbacks", "ViewportTree", "MetadataTree", "getViewportReady", "getMetadataReady", "StreamingMetadataOutlet", "parsed<PERSON><PERSON><PERSON>", "pathname", "metadataContext", "loaderTreeToFilter", "parentParams", "rscHead", "Fragment", "res", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "map", "path", "slice", "actionResult", "a", "f", "b", "sharedContext", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "revalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "onFlightDataRenderError", "err", "onInstrumentationRequestError", "onError", "dev", "RSCPayload", "run", "flightReadableStream", "renderToReadableStream", "clientReferenceManifest", "clientModules", "temporaryReferences", "fetchMetrics", "generateRuntimePrefetchResult", "metadata", "generatePayload", "rootParams", "prerenderResumeDataCache", "renderResumeDataCache", "prospectiveRuntimeServerPrerender", "cookies", "draftMode", "response", "finalRuntimeServerPrerender", "applyMetadataFromPrerenderResult", "isPartial", "<PERSON><PERSON><PERSON><PERSON>", "result", "prelude", "getPayload", "implicitTags", "ComponentMod", "assertClientReferenceManifest", "initialServerPrerenderController", "AbortController", "initialServerRenderController", "cacheSignal", "initialServerPrerenderStore", "phase", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "expire", "stale", "tags", "hmrRefreshHash", "captureOwnerStack", "runtimeStagePromise", "initialServerPayload", "pendingInitialServerResult", "prerender", "digest", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "route", "onPostpone", "cacheReady", "abort", "invalidDynamicUsageError", "experimental", "isDebugDynamicAccesses", "selectStaleTime", "createSelectStaleTime", "serverIsDynamic", "finalServerController", "serverDynamicTracking", "promise", "resolve", "resolveBlockedRuntimeAPIs", "finalServerPrerenderStore", "finalRSCPayload", "prerenderIsPending", "prerenderResult", "dynamicAccess", "collectedRevalidate", "collectedExpire", "collectedStale", "collectedTags", "warmupDevRender", "allowEmptyStaticShell", "renderController", "prerenderController", "reactController", "prerenderStore", "rscPayload", "prepareInitialCanonicalUrl", "search", "split", "getRSCPayload", "is404", "missingSlots", "initialTree", "errorType", "seedData", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "initialHead", "GlobalError", "styles", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "p", "assetPrefix", "c", "i", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "ssrError", "Error", "html", "id", "head", "body", "template", "data-next-error-message", "message", "data-next-error-digest", "data-next-error-stack", "stack", "App", "reactServerStream", "preinitScripts", "ServerInsertedHTMLProvider", "use", "initialState", "navigatedAt", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "Map", "location", "prerendered", "actionQueue", "HeadManagerContext", "Provider", "value", "appDir", "globalErrorState", "ErrorApp", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "nextFontManifest", "serverActions", "enableTainting", "__next_app__", "instrumented", "shouldTrackModuleLoading", "cacheComponents", "workUnitStore", "getStore", "__next_require__", "args", "exportsOrPromise", "globalThis", "__next_chunk_load__", "loadingChunk", "loadChunk", "URL", "setIsrStatus", "NEXT_RUNTIME", "onClose", "shouldTrackFetchMetrics", "originalRequest", "on", "metrics", "reset", "startSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "appUsingSizeAdjust", "serverModuleMap", "patchFetch", "taintObjectReference", "<PERSON><PERSON><PERSON>", "from", "crypto", "subtle", "toString", "randomUUID", "nanoid", "isPossibleActionRequest", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "getBodyResult", "spanName", "prerenderToStream", "access", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "length", "find", "contentType", "pendingRevalidates", "pendingRevalidateWrites", "pendingRevalidatedTags", "pendingPromise", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "waitUntil", "stream", "devValidatingFallbackParams", "onUpdateCookies", "previewProps", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "formState", "actionRequestResult", "generateFlight", "notFoundLoaderTree", "assignMetadata", "renderToHTMLOrFlight", "routeModule", "definition", "fetchTags", "join", "staleHeader", "String", "forceStatic", "cacheControl", "staticBailoutInfo", "description", "dynamicUsageDescription", "dynamicUsageStack", "basePath", "buildManifest", "crossOrigin", "nextExport", "reactMaxHeadersLength", "shouldWaitOnAllReady", "subresourceIntegrityManifest", "supportsDynamicResponse", "renderServerInsertedHTML", "getServerInsertedMetadata", "tracingMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "integrity", "noModule", "bootstrapScript", "reactServerErrorsByDigest", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "reactServerResult", "bind", "append<PERSON><PERSON>er", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "prerenderPhase", "environmentName", "spawnDynamicValidationInDev", "DATA", "inlinedReactServerDataStream", "tee", "preludeState", "resume", "htmlStream", "getServerInsertedHTML", "serverCapturedErrors", "delayDataUntilFirstHtmlChunk", "Empty", "inlinedDataStream", "consume", "onHeaders", "key", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bootstrapScripts", "generateStaticHTML", "isBuildTimePrerendering", "validateRootLayout", "shouldBailoutToCSR", "reason", "redirectUrl", "Headers", "mutableCookies", "Array", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "has", "errorServerStream", "fizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "outlet", "Promise", "isNotFound", "get", "initialServerReactController", "captureOwnerStackClient", "captureOwnerStackServer", "initialServerPayloadPrerenderStore", "addEventListener", "once", "LogSafely", "fn", "initialServerResult", "initialClientPrerenderController", "initialClientReactController", "initialClientRenderController", "initialClientPrerenderStore", "pendingInitialClientResult", "asUnclosingStream", "catch", "finalServerReactController", "finalServerRenderController", "finalServerPayloadPrerenderStore", "finalAttemptRSCPayload", "pendingPrerenderResult", "clientDynamicTracking", "finalClientReactController", "finalClientRenderController", "finalClientPrerenderStore", "dynamicValidation", "unprocessedPrelude", "pendingFinalClientResult", "errorInfo", "componentStack", "preludeIsEmpty", "Full", "thrownValue", "loggingFunction", "Errored", "originalLoggingFunction", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "setMetadataHeader", "isArray", "item", "resumeDataCache", "asStream", "segmentData", "collectSegmentData", "hasFallbackRouteParams", "foreverStream", "ReadableStream", "resumeStream", "JSON", "parse", "stringify", "consumeAsStream", "reactServerPrerenderStore", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "flightStream", "modules", "globalErrorModule", "GlobalErrorComponent", "filePath", "getComponent", "dir", "__NEXT_EDGE_PROJECT_DIR", "globalErrorModulePath", "devtoolSegmentExplorer", "SegmentViewNode", "staleTimes", "static", "fullPageDataBuffer", "clientSegmentCache", "isEdgeRuntime", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "staleTime", "clientParamParsing"], "mappings": ";AAaA,SACEA,gBAAgB,QAEX,4CAA2C;AAgBlD,OAAOC,WAAyC,QAAO;AAEvD,OAAOC,kBAGA,mBAAkB;AACzB,SACEC,YAAY,EACZC,yBAAyB,EACzBC,2BAA2B,EAC3BC,kBAAkB,EAClBC,wBAAwB,EACxBC,uBAAuB,EACvBC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,QACT,0CAAyC;AAChD,SAASC,oBAAoB,QAAQ,oBAAmB;AACxD,SACEC,uBAAuB,EACvBC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,QAAQ,EACRC,UAAU,EACVC,mCAAmC,EACnCC,4BAA4B,EAC5BC,wBAAwB,QACnB,6CAA4C;AACnD,SAASC,qBAAqB,QAAQ,sCAAqC;AAC3E,SAASC,2BAA2B,QAAQ,iCAAgC;AAC5E,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SACEC,kCAAkC,EAClCC,2BAA2B,EAC3BC,yBAAyB,QACpB,oEAAmE;AAC1E,SACEC,uBAAuB,EACvBC,8BAA8B,QACzB,mCAAkC;AACzC,SAASC,eAAe,QAAQ,yCAAwC;AACxE,SAASC,eAAe,QAA2B,uBAAsB;AACzE,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,yBAAwB;AAC1E,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,mCAAmC,EACnCC,iCAAiC,EACjCC,sBAAsB,EAEtBC,eAAe,EACfC,0BAA0B,QACrB,yBAAwB;AAC/B,SAASC,iBAAiB,QAAQ,iCAAgC;AAClE,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,iCAAiC,QAAQ,2CAA0C;AAC5F,SAASC,qCAAqC,QAAQ,gDAA+C;AACrG,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,IAAI,EAAEC,KAAK,QAAQ,yBAAwB;AACpD,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SAASC,wBAAwB,QAAQ,yBAAwB;AACjE,SAASC,kBAAkB,QAAQ,qBAAoB;AACvD,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,6BAA6B,QAAQ,uCAAsC;AACpF,SAASC,mBAAmB,EAAEC,aAAa,QAAQ,0BAAyB;AAC5E,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SACEC,kBAAkB,EAClBC,8BAA8B,QACzB,qBAAoB;AAC3B,SACEC,YAAY,EAEZC,uBAAuB,EACvBC,mBAAmB,QACd,oBAAmB;AAC1B,SACEC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,qBAAqB,QAChB,oBAAmB;AAC1B,SAASC,oBAAoB,QAAQ,+CAA8C;AACnF,SACEC,eAAe,EACfC,+BAA+B,QAC1B,wBAAuB;AAC9B,SACEC,qBAAqB,EACrBC,uBAAuB,QAClB,oDAAmD;AAC1D,SAASC,2BAA2B,QAAQ,gCAA+B;AAC3E,SACEC,mBAAmB,EACnBC,gCAAgC,EAChCC,wBAAwB,EACxBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,yBAAyB,EACzBC,wBAAwB,EACxBC,YAAY,EACZC,oBAAoB,EAEpBC,yBAAyB,EACzBC,sBAAsB,QACjB,sBAAqB;AAC5B,SACEC,+BAA+B,EAC/BC,yBAAyB,QACpB,sCAAqC;AAC5C,SAASC,qBAAqB,QAAQ,iBAAgB;AACtD,SAASC,iBAAiB,QAAQ,uBAAsB;AACxD,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,OAAOC,eAAe,qCAAoC;AAG1D,SAASC,yBAAyB,QAAQ,oCAAmC;AAC7E,SAASC,wBAAwB,QAAQ,qEAAoE;AAC7G,SAASC,wBAAwB,QAAQ,8CAA6C;AACtF,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,gBAAgB,QAAQ,2BAA0B;AAE3D,SACEC,4CAA4C,EAC5CC,cAAc,QACT,+BAA8B;AACrC,SAEEC,iBAAiB,EACjBC,gCAAgC,EAChCC,0CAA0C,EAC1CC,kCAAkC,QAC7B,+BAA8B;AACrC,SAASC,yCAAyC,QAAQ,6BAA4B;AACtF,SAASC,yBAAyB,QAAQ,4BAA2B;AACrE,SAASC,6BAA6B,QAAQ,sBAAqB;AACnE,SACEC,oBAAoB,QAEf,qCAAoC;AAC3C,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,cAAc,QAAQ,mCAAkC;AAEjE,SAASC,wBAAwB,EAAEC,cAAc,QAAQ,sBAAqB;AAC9E,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SACEC,8BAA8B,EAC9BC,2BAA2B,QAGtB,yCAAwC;AAE/C,OAAOC,aAAa,qBAAoB;AACxC,SAASC,4BAA4B,QAAQ,uDAAsD;AACnG,SAASC,4BAA4B,QAAQ,kBAAiB;AAC9D,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SACEC,qBAAqB,EACrBC,kBAAkB,EAClBC,mBAAmB,QACd,iDAAgD;AACvD,SAASC,sBAAsB,QAAQ,4BAA2B;AAElE,SAASC,2BAA2B,QAAQ,0BAAyB;AACrE,SAASC,cAAc,QAAQ,kBAAiB;AAChD,SAASC,eAAe,QAAQ,kDAAiD;AAGjF,SAASC,0BAA0B,QAAQ,0CAAyC;AAuDpF,MAAMC,wBAAwB;AAC9B,MAAMC,uBAAuB,CAACC,YAAsBA,YAAY;AAChE,MAAMC,uBAAuB,CAACD,YAAsBA,YAAY;AAEhE,MAAME,mBACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBC;AAoBN,SAASC,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,MAAMC,qBAAqBD,QAAQE,WAAW,KAAK;IAEnD,2DAA2D;IAC3D,mEAAmE;IACnE,4EAA4E;IAC5E,MAAMC,oBACJF,sBAAsBF,OAAO,CAAC7H,4BAA4B,KAAK;IAEjE,MAAMkI,2BAA2BL,OAAO,CAAC7H,4BAA4B,KAAK;IAE1E,MAAMmI,eAAeN,OAAO,CAAC9H,wBAAwB,KAAK4H;IAE1D,2DAA2D;IAC3D,MAAMS,eAAeL,sBAAsBF,OAAO,CAACzH,WAAW,KAAKuH;IAEnE,MAAMU,iCACJD,gBAAiB,CAAA,CAACH,qBAAqB,CAACH,QAAQQ,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBvG,kCAAkC+F,OAAO,CAAC5H,8BAA8B,IACxE0H;IAEJ,sEAAsE;IACtE,MAAMa,6BACJX,OAAO,CAACxH,oCAAoC,KAAK;IAEnD,MAAMoI,MACJZ,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMa,QACJ,OAAOD,QAAQ,WAAW5G,yBAAyB4G,OAAOd;IAE5D,MAAMgB,4BAA4BpC,6BAChCsB,SACAC,QAAQc,aAAa;IAGvB,OAAO;QACLL;QACAN;QACAC;QACAM;QACAL;QACAC;QACAL;QACAW;QACAC;IACF;AACF;AAEA,SAASE,yBAAyBC,UAAsB;IACtD,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,MAAME,oBAAoB,CAAC,CAACD,UAAU,CAAC,mBAAmB;IAC1D,OAAO;QACL;QACA;YACEE,UAAU;gBACRhE;gBACA,CAAC;gBACD;oBACEiE,MAAMH,UAAU,CAAC,mBAAmB,IAAIA,UAAU,CAAC,YAAY;gBACjE;aACD;QACH;QACA,gEAAgE;QAChEC,oBAAoBD,aAAa,CAAC;KACnC;AACH;AAEA;;CAEC,GACD,SAASI,+BACPC,MAA8B,EAC9BC,QAAgB,EAChBC,mBAA+C;IAE/C,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAe7H,gBAAgB4H;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QACA,MAAMC,aAAaD,aAAaE,KAAK;QACrC,MAAMC,mBAAmBjI,iBAAiB,CAAC8H,aAAaI,IAAI,CAAC;QAC7D,OAAO9C,gBACLqC,QACAM,YACAE,kBACAP,UACAC;IAEJ;AACF;AAEA,SAASQ,SAAS,EAChBT,QAAQ,EACRU,UAAU,EACVC,sBAAsB,EAKvB;IACC,MAAMC,YAAYZ,aAAa;IAC/B,MAAMa,sBAAsB,OAAOH,eAAe,YAAYA,aAAa;IAE3E,gEAAgE;IAChE,yEAAyE;IACzE,IAAI,CAACC,0BAA2BC,CAAAA,aAAaC,mBAAkB,GAAI;QACjE,qBAAO,KAACC;YAAKC,MAAK;YAASC,SAAQ;;IACrC;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbC,GAAqB,EACrBzC,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAI0C,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,MAAM5B,UAAU,EAChB6B,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDtB,0BAA0B,EAC1BuB,sBAAsB,EACtBC,KAAK,EACL5D,SAAS,EACToB,iBAAiB,EACjByC,SAAS,EACTC,GAAG,EACJ,GAAGV;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IAEtE,IAAI,EAACpD,2BAAAA,QAASsD,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAM,EACJC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;YAC3BD,MAAM5B;YACN6C,aAAaZ;YACba,UAAUX,IAAIW,QAAQ;YACtBC,iBAAiBrL,sBAAsB+J,IAAIY,UAAU;YACrD5B;YACAuB;YACAE;YACAJ;YACAC;YACAK;QACF;QAEAV,aAAa,AACX,CAAA,MAAM/H,8BAA8B;YAClC8H;YACAuB,oBAAoBhD;YACpBiD,cAAc,CAAC;YACfxD;YACA,+CAA+C;YAC/CyD,uBACE,MAAC7M,MAAM8M,QAAQ;;kCAEb,KAACnC;wBACCT,UAAUkB,IAAIlB,QAAQ;wBACtBU,YAAYQ,IAAI2B,GAAG,CAACnC,UAAU;wBAC9BC,wBAAwBO,IAAIP,sBAAsB;;kCAGpD,KAACsB,kBAAkBpE,qBAAqBC;kCACxC,KAACoE,kBAAkBnE,qBAAqBD;;eATrBF;YAYvBkF,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBf;YACAC;YACAJ;YACAK;QACF,EAAC,EACDc,GAAG,CAAC,CAACC,OAASA,KAAKC,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAI5E,2BAAAA,QAAS6E,YAAY,EAAE;QACzB,OAAO;YACLC,GAAG9E,QAAQ6E,YAAY;YACvBE,GAAGrC;YACHsC,GAAGvC,IAAIwC,aAAa,CAACC,OAAO;QAC9B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLF,GAAGvC,IAAIwC,aAAa,CAACC,OAAO;QAC5BH,GAAGrC;QACHyC,GAAGjC,UAAUkC,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACP5C,GAAqB,EACrB6C,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAW/C,IAAIlB,QAAQ;QACvB,yEAAyE;QACzEkE,WAAWhD,IAAIP,sBAAsB,GAAG,WAAW;QACnDoD;QACAI,kBAAkBxI,oBAAoBuF,IAAIS,SAAS;IACrD;AACF;AAEA;;;CAGC,GACD,eAAeyC,kCACbC,GAAoB,EACpBnD,GAAqB,EACrBoD,YAA0B,EAC1B7F,OAMC;IAED,MAAMqD,aAAaZ,IAAIY,UAAU;IAEjC,SAASyC,wBAAwBC,GAAkB;QACjD,OAAO1C,WAAW2C,6BAA6B,oBAAxC3C,WAAW2C,6BAA6B,MAAxC3C,YACL0C,KACAH,KACAP,mBAAmB5C,KAAK;IAE5B;IACA,MAAMwD,UAAUzM,oCACd,CAAC,CAAC6J,WAAW6C,GAAG,EAChBJ;IAGF,MAAMK,aAGF,MAAMtI,qBAAqBuI,GAAG,CAChCP,cACArD,2BACAC,KACAzC;IAGF,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMqG,uBAAuBxI,qBAAqBuI,GAAG,CACnDP,cACApD,IAAIE,YAAY,CAAC2D,sBAAsB,EACvCH,YACA1D,IAAI8D,uBAAuB,CAACC,aAAa,EACzC;QACEP;QACAQ,mBAAmB,EAAEzG,2BAAAA,QAASyG,mBAAmB;QACjDlH;IACF;IAGF,OAAO,IAAIhG,mBAAmB8M,sBAAsB;QAClDK,cAAcjE,IAAIS,SAAS,CAACwD,YAAY;IAC1C;AACF;AAEA,eAAeC,8BACbf,GAAoB,EACpBxB,GAAqB,EACrB3B,GAAqB,EACrBoD,YAA0B;IAE1B,MAAM,EAAE3C,SAAS,EAAE,GAAGT;IACtB,MAAMY,aAAaZ,IAAIY,UAAU;IAEjC,SAASyC,wBAAwBC,GAAkB;QACjD,OAAO1C,WAAW2C,6BAA6B,oBAAxC3C,WAAW2C,6BAA6B,MAAxC3C,YACL0C,KACAH,KACA,sDAAsD;QACtDP,mBAAmB5C,KAAK;IAE5B;IACA,MAAMwD,UAAUzM,oCACd,OACAsM;IAGF,MAAMc,WAAwC,CAAC;IAE/C,MAAMC,kBAAkB,IAAMrE,0BAA0BC,KAAK5C;IAE7D,MAAM,EACJ8C,cAAc,EAAEC,IAAI,EAAE,EACtBnB,0BAA0B,EAC3B,GAAGgB;IACJ,MAAMqE,aAAajM,cAAc+H,MAAMnB;IAEvC,qFAAqF;IACrF,gDAAgD;IAChD,MAAMsF,2BAA2B1I;IACjC,yCAAyC;IACzC,MAAM2I,wBAAwB;IAE9B,MAAMC,kCACJxE,KACAoE,iBACAE,0BACAC,uBACAF,YACAjB,aAAaqB,OAAO,EACpBrB,aAAasB,SAAS;IAGxB,MAAMC,WAAW,MAAMC,4BACrB5E,KACAoE,iBACAE,0BACAC,uBACAF,YACAjB,aAAaqB,OAAO,EACpBrB,aAAasB,SAAS,EACtBlB;IAGFqB,iCAAiCF,UAAUR,UAAU1D;IACrD0D,SAASF,YAAY,GAAGjE,IAAIS,SAAS,CAACwD,YAAY;IAElD,IAAIU,SAASG,SAAS,EAAE;QACtBnD,IAAIoD,SAAS,CAAC/O,0BAA0B;IAC1C;IAEA,OAAO,IAAIc,mBAAmB6N,SAASK,MAAM,CAACC,OAAO,EAAEd;AACzD;AAEA,eAAeK,kCACbxE,GAAqB,EACrBkF,UAAqB,EACrBZ,wBAAyD,EACzDC,qBAAmD,EACnDF,UAAkB,EAClBI,OAA+C,EAC/CC,SAAmD;IAEnD,MAAM,EAAES,YAAY,EAAEvE,UAAU,EAAEH,SAAS,EAAE,GAAGT;IAEhD,MAAM,EAAE8D,uBAAuB,EAAEsB,YAAY,EAAE,GAAGxE;IAElDyE,8BAA8BvB;IAE9B,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAMwB,mCAAmC,IAAIC;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAMC,gCAAgC,IAAID;IAE1C,kFAAkF;IAClF,yBAAyB;IACzB,MAAME,cAAc,IAAIpK;IAExB,MAAMqK,8BAA2D;QAC/DpG,MAAM;QACNqG,OAAO;QACPtB;QACAc;QACAS,cAAcJ,8BAA8BK,MAAM;QAClDC,YAAYR;QACZ,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBG;QACA,qEAAqE;QACrEM,iBAAiB;QACjB,qEAAqE;QACrE,4EAA4E;QAC5EC,YAAY;QACZC,QAAQ;QACRC,OAAOzK;QACP0K,MAAM;eAAIhB,aAAagB,IAAI;SAAC;QAC5B5B;QACAD;QACA8B,gBAAgBhJ;QAChBiJ,mBAAmBjJ;QACnB,uDAAuD;QACvDkJ,qBAAqB;QACrB,mFAAmF;QACnF7B;QACAC;IACF;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAM6B,uBAAuB,MAAMnL,qBAAqBuI,GAAG,CACzD+B,6BACAR;IAGF,MAAMsB,6BAA6BpL,qBAAqBuI,GAAG,CACzD+B,6BACAN,aAAaqB,SAAS,EACtBF,sBACAzC,wBAAwBC,aAAa,EACrC;QACEjH;QACA0G,SAAS,CAACF;YACR,MAAMoD,SAASvP,2BAA2BmM;YAE1C,IAAIoD,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAIpB,iCAAiCO,MAAM,CAACc,OAAO,EAAE;gBACnD,mEAAmE;gBACnE,iEAAiE;gBACjE;YACF,OAAO,IACL5J,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;gBACA5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;YAChE;QACF;QACA,iFAAiF;QACjF,qCAAqC;QACrCC,YAAY3J;QACZ,+EAA+E;QAC/E,iFAAiF;QACjF,iDAAiD;QACjDyI,QAAQL,8BAA8BK,MAAM;IAC9C;IAGF,8EAA8E;IAC9EzJ,oBAAoBqJ;IACpB,MAAMA,YAAYuB,UAAU;IAE5BxB,8BAA8ByB,KAAK;IACnC3B,iCAAiC2B,KAAK;IAEtC,gEAAgE;IAChE,iEAAiE;IACjE,IAAIxG,UAAUyG,wBAAwB,EAAE;QACtC,MAAMzG,UAAUyG,wBAAwB;IAC1C;IAEA,IAAI;QACF,OAAO,MAAMpM,iCAAiC0L;IAChD,EAAE,OAAOlD,KAAK;QACZ,IACEkC,8BAA8BK,MAAM,CAACc,OAAO,IAC5CrB,iCAAiCO,MAAM,CAACc,OAAO,EAC/C;QACA,4EAA4E;QAC9E,OAAO,IACL5J,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnF5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;QAChE;QACA,OAAO;IACT;AACF;AAEA,eAAelC,4BACb5E,GAAqB,EACrBkF,UAAqB,EACrBZ,wBAAyD,EACzDC,qBAAmD,EACnDF,UAAkB,EAClBI,OAA+C,EAC/CC,SAAmD,EACnDlB,OAA6C;IAE7C,MAAM,EAAE2B,YAAY,EAAEvE,UAAU,EAAE,GAAGZ;IAErC,MAAM,EACJ8D,uBAAuB,EACvBsB,YAAY,EACZ+B,YAAY,EACZC,sBAAsB,EACvB,GAAGxG;IAEJyE,8BAA8BvB;IAE9B,MAAMuD,kBAAkBC,sBAAsBH;IAE9C,IAAII,kBAAkB;IACtB,MAAMC,wBAAwB,IAAIjC;IAElC,MAAMkC,wBAAwBjO,2BAC5B4N;IAGF,MAAM,EAAEM,SAASpB,mBAAmB,EAAEqB,SAASC,yBAAyB,EAAE,GACxEnL;IAEF,MAAMoL,4BAAyD;QAC7DvI,MAAM;QACNqG,OAAO;QACPtB;QACAc;QACAS,cAAc4B,sBAAsB3B,MAAM;QAC1CC,YAAY0B;QACZ,8EAA8E;QAC9E/B,aAAa;QACbM,iBAAiB0B;QACjB,qEAAqE;QACrE,4EAA4E;QAC5EzB,YAAY;QACZC,QAAQ;QACRC,OAAOzK;QACP0K,MAAM;eAAIhB,aAAagB,IAAI;SAAC;QAC5B7B;QACAC;QACA6B,gBAAgBhJ;QAChBiJ,mBAAmBjJ;QACnB,gEAAgE;QAChEkJ;QACA,mFAAmF;QACnF7B;QACAC;IACF;IAEA,MAAMoD,kBAAkB,MAAM1M,qBAAqBuI,GAAG,CACpDkE,2BACA3C;IAGF,IAAI6C,qBAAqB;IACzB,MAAM/C,SAAS,MAAMrK,6CACnB;QACE,eAAe;QACf,MAAMqN,kBAAkB,MAAM5M,qBAAqBuI,GAAG,CACpDkE,2BACAzC,aAAaqB,SAAS,EACtBqB,iBACAhE,wBAAwBC,aAAa,EACrC;YACEjH;YACA0G;YACAqC,QAAQ2B,sBAAsB3B,MAAM;QACtC;QAEFkC,qBAAqB;QACrB,OAAOC;IACT,GACA;QACE,gCAAgC;QAChC,EAAE;QACF,0GAA0G;QAC1G,kHAAkH;QAClH,mGAAmG;QACnG,+FAA+F;QAC/F,sFAAsF;QACtFJ;IACF,GACA;QACE,SAAS;QACT,IAAIJ,sBAAsB3B,MAAM,CAACc,OAAO,EAAE;YACxC,4EAA4E;YAC5E,6EAA6E;YAC7EY,kBAAkB;YAClB;QACF;QAEA,IAAIQ,oBAAoB;YACtB,kFAAkF;YAClF,iCAAiC;YACjCR,kBAAkB;QACpB;QACAC,sBAAsBP,KAAK;IAC7B;IAGFlN,uBAAuB0N;IAEvB,OAAO;QACLzC;QACA,8DAA8D;QAC9D,wBAAwB;QACxBiD,eAAeR;QACf3C,WAAWyC;QACXW,qBAAqBL,0BAA0B7B,UAAU;QACzDmC,iBAAiBN,0BAA0B5B,MAAM;QACjDmC,gBAAgBf,gBAAgBQ,0BAA0B3B,KAAK;QAC/DmC,eAAeR,0BAA0B1B,IAAI;IAC/C;AACF;AAEA;;;;;;CAMC,GACD,eAAemC,gBACbnF,GAAoB,EACpBnD,GAAqB;IAErB,MAAM,EACJ8D,uBAAuB,EACvB5D,cAAckF,YAAY,EAC1BpG,0BAA0B,EAC1BmG,YAAY,EACZvE,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EACJuI,wBAAwB,KAAK,EAC7B9E,GAAG,EACHF,6BAA6B,EAC9B,GAAG3C;IAEJ,IAAI,CAAC6C,KAAK;QACR,MAAM,qBAEL,CAFK,IAAIlI,eACR,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM8I,aAAajM,cACjBgN,aAAajF,IAAI,EACjBnB;IAGF,SAASqE,wBAAwBC,GAAkB;QACjD,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB5C,KAAK;IAE5B;IACA,MAAMwD,UAAUzM,oCACd,MACAsM;IAGF,2EAA2E;IAC3E,kBAAkB;IAClB,MAAMiB,2BAA2B1I;IAEjC,MAAM4M,mBAAmB,IAAIjD;IAC7B,MAAMkD,sBAAsB,IAAIlD;IAChC,MAAMmD,kBAAkB,IAAInD;IAC5B,MAAME,cAAc,IAAIpK;IAExB,MAAMsN,iBAAiC;QACrCrJ,MAAM;QACNqG,OAAO;QACPtB;QACAc;QACAS,cAAc4C,iBAAiB3C,MAAM;QACrCC,YAAY2C;QACZhD;QACAM,iBAAiB;QACjBwC;QACAvC,YAAYvK;QACZwK,QAAQxK;QACRyK,OAAOzK;QACP0K,MAAM,EAAE;QACR7B;QACAC,uBAAuB;QACvB6B,gBAAgBjD,IAAIsB,OAAO,CAAC1O,6BAA6B;QACzDsQ,mBAAmBjB,aAAaiB,iBAAiB;QACjD,sEAAsE;QACtE,wEAAwE;QACxE,6DAA6D;QAC7DtH,qBAAqB;IACvB;IAEA,MAAM6J,aAAa,MAAMxN,qBAAqBuI,GAAG,CAC/CgF,gBACA5I,2BACAC;IAGF,0FAA0F;IAC1F,mCAAmC;IACnC5E,qBAAqBuI,GAAG,CACtBgF,gBACAvD,aAAavB,sBAAsB,EACnC+E,YACA9E,wBAAwBC,aAAa,EACrC;QACEjH;QACA0G;QACAqC,QAAQ2C,iBAAiB3C,MAAM;IACjC;IAGF,8EAA8E;IAC9EzJ,oBAAoBqJ;IACpB,MAAMA,YAAYuB,UAAU;IAE5B,uFAAuF;IACvF2B,eAAerE,wBAAwB,GAAG;IAC1C,mBAAmB;IACnBoE,gBAAgBzB,KAAK;IACrBuB,iBAAiBvB,KAAK;IAEtB,0EAA0E;IAC1E,+EAA+E;IAC/E,+EAA+E;IAC/E,OAAO,IAAInQ,mBAAmB,IAAI;QAChCmN,cAAcxD,UAAUwD,YAAY;QACpCM,uBAAuB1I,4BACrByI;IAEJ;AACF;AAEA;;;;;CAKC,GACD,SAASuE,2BAA2BnI,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIW,QAAQ,GAAGX,IAAIoI,MAAM,AAAD,EAAGC,KAAK,CAAC;AAC3C;AAEA,wFAAwF;AACxF,eAAeC,cACb7I,IAAgB,EAChBH,GAAqB,EACrBiJ,KAAc;IAEd,MAAMrH,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAIqH;IAEJ,sDAAsD;IACtD,IAAInM,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CiM,eAAe,IAAIrH;IACrB;IAEA,MAAM,EACJ7C,0BAA0B,EAC1BwB,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZE,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAMmJ,cAAc3R,sCAClB2I,MACAnB,4BACAwB;IAEF,MAAMG,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAMlC,oBAAoB,CAAC,CAAC0B,IAAI,CAAC,EAAE,CAAC,mBAAmB;IAEvD,MAAM,EACJY,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;QAC3BD;QACA,6FAA6F;QAC7F,0BAA0B;QAC1B,wFAAwF;QACxF,2CAA2C;QAC3C,yFAAyF;QACzFiJ,WAAWH,SAAS,CAACxK,oBAAoB,cAAcrB;QACvDgE,aAAaZ;QACba,UAAUX,IAAIW,QAAQ;QACtBC,iBAAiBrL,sBAAsB+J,IAAIY,UAAU;QACrD5B;QACAuB;QACAE;QACAJ;QACAC;QACAK;IACF;IAEA,MAAMG,mBAAqC,EAAE;IAE7C,MAAMuI,WAAW,MAAMlR,oBAAoB;QACzC6H;QACAzB,YAAY4B;QACZqB,cAAc,CAAC;QACfI;QACAE;QACAC;QACAC,oBAAoB;QACpBf;QACAC;QACAgI;QACApI;QACAwI,gBAAgBtJ,IAAIY,UAAU,CAACuG,YAAY,CAACmC,cAAc;QAC1DnI;IACF;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAMoI,aAAavJ,IAAI2B,GAAG,CAAC6H,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAAC9T;IAExD,MAAM+T,4BACJ,MAAC/U,MAAM8M,QAAQ;;0BACb,KAACnC;gBACCT,UAAUkB,IAAIlB,QAAQ;gBACtBU,YAAYQ,IAAI2B,GAAG,CAACnC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,KAACsB;0BACD,KAACC;;OAPkBtE;IAWvB,MAAM,EAAEkN,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvD5J,MACAH;IAGF,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAMgK,wBACJvJ,UAAUkC,kBAAkB,IAC5B3C,IAAIY,UAAU,CAACuG,YAAY,CAACpJ,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FkM,iBAAG,KAACC;YAASpJ,kBAAkBA;;QAC/ByB,GAAGvC,IAAIwC,aAAa,CAACC,OAAO;QAC5B0H,GAAGnK,IAAIoK,WAAW;QAClBC,GAAGxB,2BAA2BnI;QAC9B4J,GAAG,CAAC,CAACb;QACLnH,GAAG;YACD;gBACE6G;gBACAE;gBACAM;gBACAK;aACD;SACF;QACDO,GAAGrB;QACHsB,GAAG;YAACZ;YAAaE;SAAkB;QACnCW,GAAG,OAAOzK,IAAIY,UAAU,CAAC8J,SAAS,KAAK;QACvChI,GAAGjC,UAAUkC,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAASuH,SAAS,EAAEpJ,gBAAgB,EAAoC;IACtEA,iBAAiB6J,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACb1K,IAAgB,EAChBH,GAAqB,EACrB8K,QAAiB,EACjB1B,SAAqD;IAErD,MAAM,EACJpK,0BAA0B,EAC1BwB,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZE,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAM,EAAEK,YAAY,EAAED,YAAY,EAAE,GAAGX,yBAAyB;QAC9DD;QACAiB,aAAaZ;QACba,UAAUX,IAAIW,QAAQ;QACtBC,iBAAiBrL,sBAAsB+J,IAAIY,UAAU;QACrDwI;QACApK;QACAuB;QACAE;QACAJ;QACAC;QACAK,wBAAwBA;IAC1B;IAEA,MAAMgJ,4BACJ,MAAC/U,MAAM8M,QAAQ;;0BACb,KAACnC;gBACCT,UAAUkB,IAAIlB,QAAQ;gBACtBU,YAAYQ,IAAI2B,GAAG,CAACnC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,KAACsB;YACAhE,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,KAAC2C;gBAAKC,MAAK;gBAAaC,SAAQ;;0BAElC,KAACkB;;OAVkBtE;IAcvB,MAAMyM,cAAc3R,sCAClB2I,MACAnB,4BACAwB;IAGF,IAAI8C,MAAyBlG;IAC7B,IAAI0N,UAAU;QACZxH,MAAMxH,QAAQgP,YAAYA,WAAW,qBAAwB,CAAxB,IAAIC,MAAMD,WAAW,KAArB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC9D;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAMzB,WAA8B;QAClCF,WAAW,CAAC,EAAE;sBACd,MAAC6B;YAAKC,IAAG;;8BACP,KAACC;8BACD,KAACC;8BACEpO,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBqG,oBACxC,KAAC8H;wBACCC,2BAAyB/H,IAAIgI,OAAO;wBACpCC,0BAAwB,YAAYjI,MAAMA,IAAIoD,MAAM,GAAG;wBACvD8E,yBAAuBlI,IAAImI,KAAK;yBAEhC;;;;QAGR,CAAC;QACD;QACA;KACD;IAED,MAAM,EAAE7B,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvD5J,MACAH;IAGF,MAAMgK,wBACJvJ,UAAUkC,kBAAkB,IAC5B3C,IAAIY,UAAU,CAACuG,YAAY,CAACpJ,iBAAiB,KAAK;IAEpD,OAAO;QACLwE,GAAGvC,IAAIwC,aAAa,CAACC,OAAO;QAC5B0H,GAAGnK,IAAIoK,WAAW;QAClBC,GAAGxB,2BAA2BnI;QAC9B6J,GAAGnN;QACHkN,GAAG;QACHhI,GAAG;YACD;gBACE6G;gBACAE;gBACAM;gBACAK;aACD;SACF;QACDQ,GAAG;YAACZ;YAAaE;SAAkB;QACnCW,GAAG,OAAOzK,IAAIY,UAAU,CAAC8J,SAAS,KAAK;QACvChI,GAAGjC,UAAUkC,kBAAkB;IACjC;AACF;AAEA,SAAS0C,8BACPvB,uBAA8D;IAI9D,IAAI,CAACA,yBAAyB;QAC5B,MAAM,qBAAqE,CAArE,IAAIvI,eAAe,oDAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAoE;IAC5E;AACF;AAEA,mFAAmF;AACnF,SAASmQ,IAAO,EACdC,iBAAiB,EACjBC,cAAc,EACd9H,uBAAuB,EACvB+H,0BAA0B,EAC1B1N,KAAK,EAON;IACCyN;IACA,MAAMjH,WAAW/P,MAAMkX,GAAG,CACxB/S,gBACE4S,mBACA7H,yBACA3F;IAIJ,MAAM4N,eAAexR,yBAAyB;QAC5C,gEAAgE;QAChE,kBAAkB;QAClByR,aAAa,CAAC;QACdC,mBAAmBtH,SAASrC,CAAC;QAC7B4J,0BAA0BvH,SAAS0F,CAAC;QACpC8B,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACV5C,oBAAoB9E,SAAS2F,CAAC;QAC9BI,WAAW/F,SAAS8F,CAAC;QACrB6B,aAAa3H,SAASjC,CAAC;IACzB;IAEA,MAAM6J,cAAc/R,yBAAyBuR,cAAc;IAE3D,MAAM,EAAES,kBAAkB,EAAE,GAC1BtP,QAAQ;IAEV,qBACE,KAACsP,mBAAmBC,QAAQ;QAC1BC,OAAO;YACLC,QAAQ;YACRxO;QACF;kBAEA,cAAA,KAAC0N;sBACC,cAAA,KAACxR;gBACCkS,aAAaA;gBACbK,kBAAkBjI,SAAS6F,CAAC;gBAC5BJ,aAAazF,SAASwF,CAAC;;;;AAKjC;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAAS0C,SAAY,EACnBlB,iBAAiB,EACjBC,cAAc,EACd9H,uBAAuB,EACvB+H,0BAA0B,EAC1B1N,KAAK,EAON;IACCyN;IACA,MAAMjH,WAAW/P,MAAMkX,GAAG,CACxB/S,gBACE4S,mBACA7H,yBACA3F;IAIJ,MAAM4N,eAAexR,yBAAyB;QAC5C,gEAAgE;QAChE,kBAAkB;QAClByR,aAAa,CAAC;QACdC,mBAAmBtH,SAASrC,CAAC;QAC7B4J,0BAA0BvH,SAAS0F,CAAC;QACpC8B,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACV5C,oBAAoB9E,SAAS2F,CAAC;QAC9BI,WAAW/F,SAAS8F,CAAC;QACrB6B,aAAa3H,SAASjC,CAAC;IACzB;IAEA,MAAM6J,cAAc/R,yBAAyBuR,cAAc;IAE3D,qBACE,KAACF;kBACC,cAAA,KAACxR;YACCkS,aAAaA;YACbK,kBAAkBjI,SAAS6F,CAAC;YAC5BJ,aAAazF,SAASwF,CAAC;;;AAI/B;AASA,eAAe2C,yBACb3J,GAAoB,EACpBxB,GAAqB,EACrBjB,GAAwC,EACxC5B,QAAgB,EAChB0B,KAAyB,EACzBI,UAAsB,EACtBH,SAAoB,EACpBsM,oBAA0C,EAC1CC,cAAqC,EACrCC,wBAA8D,EAC9DzK,aAA+B,EAC/BzD,mBAA+C;IAE/C,MAAMmO,iBAAiBpO,aAAa;IACpC,IAAIoO,gBAAgB;QAClBvL,IAAInC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAM2N,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJvJ,uBAAuB,EACvBwJ,qBAAqB,EACrBlI,YAAY,EACZmI,gBAAgB,EAChBC,aAAa,EACbpD,cAAc,EAAE,EAChBqD,cAAc,EACf,GAAG7M;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAIwE,aAAasI,YAAY,EAAE;QAC7B,MAAMC,eAAe1T,0BAA0BmL;QAE/C,kEAAkE;QAClE,0EAA0E;QAC1E,+EAA+E;QAC/E,8DAA8D;QAE9D,MAAMwI,2BAA2B;YAC/B,IAAI,CAAChN,WAAWuG,YAAY,CAAC0G,eAAe,EAAE;gBAC5C,OAAO;YACT;YACA,IAAIjN,WAAW6C,GAAG,EAAE;gBAClB,OAAO;YACT;YACA,MAAMqK,gBAAgB1S,qBAAqB2S,QAAQ;YAEnD,IAAI,CAACD,eAAe;gBAClB,OAAO;YACT;YAEA,OAAQA,cAAcxO,IAAI;gBACxB,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,OAAO;gBACT,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,OAAO;gBACT;oBACEwO;YACJ;QACF;QAEA,MAAME,mBAAgD,CAAC,GAAGC;YACxD,MAAMC,mBAAmBP,aAAazQ,OAAO,IAAI+Q;YACjD,IAAIL,4BAA4B;gBAC9B,+CAA+C;gBAC/CzR,mBAAmB+R;YACrB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBC,WAAWH,gBAAgB,GAAGA;QAE9B,MAAMI,sBAAqD,CAAC,GAAGH;YAC7D,MAAMI,eAAeV,aAAaW,SAAS,IAAIL;YAC/C,IAAIL,4BAA4B;gBAC9B1R,sBAAsBmS;YACxB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBF,WAAWC,mBAAmB,GAAGA;IACnC;IAEA,IAAIrR,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,uCAAuC;QACvC,MAAM,EAAEoE,QAAQ,EAAE,GAAG,IAAIkN,IAAIpL,IAAIzC,GAAG,IAAI,KAAK;QAC7CE,WAAW4N,YAAY,oBAAvB5N,WAAW4N,YAAY,MAAvB5N,YAA0BS,UAAU;IACtC;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7DtE,QAAQC,GAAG,CAACyR,YAAY,KAAK,UAC7BtU,kBAAkBgJ,MAClB;QACAxB,IAAI+M,OAAO,CAAC;YACV,oEAAoE;YACpE,4BAA4B;YAC5BjO,UAAUkO,uBAAuB,GAAG;QACtC;QAEAxL,IAAIyL,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5B,IAAI,iBAAiBV,YAAY;gBAC/B,MAAMW,UAAU9U,gCAAgC;oBAAE+U,OAAO;gBAAK;gBAC9D,IAAID,SAAS;oBACXjY,YACGmY,SAAS,CAACpY,mBAAmBqY,sBAAsB,EAAE;wBACpDC,WAAWJ,QAAQK,wBAAwB;wBAC3CC,YAAY;4BACV,iCACEN,QAAQO,wBAAwB;4BAClC,kBAAkBzY,mBAAmBqY,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFR,QAAQK,wBAAwB,GAC9BL,QAAQS,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAMpL,WAAwC;QAC5C3E,YAAY0N,iBAAiB,MAAM9P;IACrC;IAEA,MAAMmD,yBAAyB,CAAC,EAACgN,oCAAAA,iBAAkBiC,kBAAkB;IAErEnK,8BAA8BvB;IAE9B,MAAM2L,kBAAkBvV,sBAAsB;QAAEoT;IAAsB;IAEtE/U,+BAA+B;QAC7BoG,MAAM8B,UAAU9B,IAAI;QACpBmF;QACAwJ;QACAmC;IACF;IAEArK,aAAasK,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EAAEvP,MAAM5B,UAAU,EAAEoR,oBAAoB,EAAE,GAAGvK;IACnD,IAAIqI,gBAAgB;QAClBkC,qBACE,kFACA5S,QAAQC,GAAG;IAEf;IAEAyD,UAAUwD,YAAY,GAAG,EAAE;IAC3BE,SAASF,YAAY,GAAGxD,UAAUwD,YAAY;IAE9C,qCAAqC;IACrCzD,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnBjL,qBAAqBiL;IAErB,MAAM,EACJxC,iBAAiB,EACjBN,iBAAiB,EACjBC,wBAAwB,EACxBE,YAAY,EACZL,kBAAkB,EAClBI,YAAY,EACZO,KAAK,EACN,GAAG4O;IAEJ,MAAM,EAAEpK,kBAAkB,EAAE,GAAGlC;IAE/B;;;GAGC,GACD,IAAI7D;IAEJ,IAAI+F,oBAAoB;QACtB/F,YAAYgT,OAAOC,IAAI,CACrB,MAAMC,OAAOC,MAAM,CAACrJ,MAAM,CAAC,SAASkJ,OAAOC,IAAI,CAAC1M,IAAIzC,GAAG,IACvDsP,QAAQ,CAAC;IACb,OAAO,IAAIjT,QAAQC,GAAG,CAACyR,YAAY,KAAK,QAAQ;QAC9C7R,YAAYkT,OAAOG,UAAU;IAC/B,OAAO;QACLrT,YAAY,AACVM,QAAQ,6BACRgT,MAAM;IACV;IAEA;;GAEC,GACD,MAAMrR,SAAS+B,WAAW/B,MAAM,IAAI,CAAC;IAErC,MAAMG,6BAA6BJ,+BACjCC,QACAC,UACAC;IAGF,MAAMoR,0BAA0B7V,0BAA0B6I;IAE1D,MAAMgC,eAAe,MAAMzO,gBACzB+J,UAAU9B,IAAI,EACd+B,KACA3B;IAGF,MAAMiB,MAAwB;QAC5BE,cAAckF;QACd1E;QACAE;QACAH;QACAsM;QACA/N;QACAwB;QACA4P,YAAY1S;QACZ+B,wBAAwB0Q;QACxBhD;QACA5M;QACAvC;QACApB;QACAkC;QACAgF;QACAsG;QACA8C;QACA/O;QACAwD;QACAa;QACA2C;IACF;IAEAtO,YAAYwZ,oBAAoB,CAAC,cAAcvR;IAE/C,IAAI6D,oBAAoB;QACtB,mEAAmE;QACnE,4CAA4C;QAC5C,MAAM2N,+BAA+BzZ,YAAY0Z,IAAI,CACnD5Z,cAAc6Z,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAE3R,UAAU;YAC7CsQ,YAAY;gBACV,cAActQ;YAChB;QACF,GACA4R;QAGF,MAAM/L,WAAW,MAAM2L,6BACrBnN,KACAxB,KACA3B,KACAmE,UACA5F,YACAQ;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACE4F,SAASsD,aAAa,IACtB7O,oBAAoBuL,SAASsD,aAAa,KAC1CrH,WAAWwG,sBAAsB,EACjC;YACAzP,KAAK;YACL,KAAK,MAAMgZ,UAAUrX,yBAAyBqL,SAASsD,aAAa,EAAG;gBACrEtQ,KAAKgZ;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAIlQ,UAAUyG,wBAAwB,EAAE;YACtCpN,0BAA0B2G,WAAWA,UAAUyG,wBAAwB;YACvE,MAAM,IAAIjO;QACZ;QACA,IAAI0L,SAASiM,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoBnM,SAASiM,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAGtE,KAAK;YACxE,IAAIoE,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAInM,SAASsM,SAAS,CAACC,MAAM,EAAE;YAC7B,MAAMJ,oBAAoBnM,SAASsM,SAAS,CAACE,IAAI,CAAC,CAAC7N,MACjDpM,gBAAgBoM;YAElB,IAAIwN,mBAAmB,MAAMA;QAC/B;QAEA,MAAMvT,UAA+B;YACnC4G;YACAiN,aAAa5V;QACf;QACA,oEAAoE;QACpE,IACEiF,UAAU4Q,kBAAkB,IAC5B5Q,UAAU6Q,uBAAuB,IACjC7Q,UAAU8Q,sBAAsB,EAChC;YACA,MAAMC,iBAAiBvV,mBAAmBwE,WAAWgR,OAAO,CAAC;gBAC3D,IAAI1U,QAAQC,GAAG,CAAC0U,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6ClR;gBAC3D;YACF;YAEA,IAAIE,WAAWiR,SAAS,EAAE;gBACxBjR,WAAWiR,SAAS,CAACL;YACvB,OAAO;gBACLjU,QAAQsU,SAAS,GAAGL;YACtB;QACF;QAEA3M,iCAAiCF,UAAUR,UAAU1D;QAErD,IAAIkE,SAASJ,qBAAqB,EAAE;YAClCJ,SAASI,qBAAqB,GAAGI,SAASJ,qBAAqB;QACjE;QAEA,OAAO,IAAI1P,aAAa,MAAMS,eAAeqP,SAASmN,MAAM,GAAGvU;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAMgH,wBACJ3D,WAAW2D,qBAAqB,KAAIyI,kCAAAA,eAAgBzI,qBAAqB;QAE3E,MAAMF,aAAajM,cAAcmG,YAAYyB,IAAIhB,0BAA0B;QAC3E,MAAM+S,8BACJxV,eAAe4G,KAAK,kCAAkC;QACxD,MAAMC,eAAelN,4BACnBiN,KACAxB,KACAjB,KACA2D,YACAc,cACAvE,WAAWoR,eAAe,EAC1BpR,WAAWqR,YAAY,EACvBrU,cACAqP,0BACA1I,uBACAwN;QAGF,IACEhV,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB2D,WAAW4N,YAAY,IACvB,qEAAqE;QACrE,6DAA6D;QAC7DzR,QAAQC,GAAG,CAACyR,YAAY,KAAK,UAC7BtU,kBAAkBgJ,QAClB,CAAC3F,oBACD;YACA,MAAMgR,eAAe5N,WAAW4N,YAAY;YAC5CrL,IAAIyL,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,IAAI,CAACzL,aAAa8O,WAAW,IAAI,CAACzR,UAAU0R,YAAY,EAAE;oBACxD,iEAAiE;oBACjE,MAAM,EAAE9Q,QAAQ,EAAE,GAAG,IAAIkN,IAAIpL,IAAIzC,GAAG,IAAI,KAAK;oBAC7C8N,aAAanN,UAAU;gBACzB;YACF;QACF;QAEA,IAAI7D,oBAAoB;YACtB,OAAO8K,gBAAgBnF,KAAKnD;QAC9B,OAAO,IAAInC,cAAc;YACvB,IAAIF,0BAA0B;gBAC5B,OAAOuG,8BAA8Bf,KAAKxB,KAAK3B,KAAKoD;YACtD,OAAO;gBACL,OAAOF,kCAAkCC,KAAKnD,KAAKoD;YACrD;QACF;QAEA,MAAMgP,4BAA4Bvb,YAAY0Z,IAAI,CAChD5Z,cAAc6Z,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAE3R,UAAU;YAC1CsQ,YAAY;gBACV,cAActQ;YAChB;QACF,GACAuT;QAGF,IAAIC,YAAwB;QAC5B,IAAInC,yBAAyB;YAC3B,gFAAgF;YAChF,MAAMoC,sBAAsB,MAAM9a,aAAa;gBAC7C0L;gBACAxB;gBACAyD;gBACAqK;gBACA+C,gBAAgBtP;gBAChBzC;gBACA2C;gBACAoK;gBACAxN;gBACAmE;YACF;YAEA,IAAIoO,qBAAqB;gBACvB,IAAIA,oBAAoBjT,IAAI,KAAK,aAAa;oBAC5C,MAAMmT,qBAAqBnU,yBAAyBC;oBACpDoD,IAAInC,UAAU,GAAG;oBACjB2E,SAAS3E,UAAU,GAAG;oBACtB,MAAMsS,SAAS,MAAMM,0BACnBhP,cACAD,KACAxB,KACA3B,KACAyS,oBACAH,WACAtF,gBACA7I,UACA4N;oBAGF,OAAO,IAAIld,aAAaid,QAAQ;wBAC9B3N;wBACAiN,aAAa5V;oBACf;gBACF,OAAO,IAAI+W,oBAAoBjT,IAAI,KAAK,QAAQ;oBAC9C,IAAIiT,oBAAoBvN,MAAM,EAAE;wBAC9BuN,oBAAoBvN,MAAM,CAAC0N,cAAc,CAACvO;wBAC1C,OAAOoO,oBAAoBvN,MAAM;oBACnC,OAAO,IAAIuN,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;QACF;QAEA,MAAM/U,UAA+B;YACnC4G;YACAiN,aAAa5V;QACf;QAEA,MAAMsW,SAAS,MAAMM,0BACnBhP,cACAD,KACAxB,KACA3B,KACAzB,YACA+T,WACAtF,gBACA7I,UACA4N;QAGF,uEAAuE;QACvE,kDAAkD;QAClD,6GAA6G;QAC7G,IAAItR,UAAUyG,wBAAwB,IAAIzG,UAAUgD,GAAG,EAAE;YACvD,MAAMhD,UAAUyG,wBAAwB;QAC1C;QAEA,oEAAoE;QACpE,IACEzG,UAAU4Q,kBAAkB,IAC5B5Q,UAAU6Q,uBAAuB,IACjC7Q,UAAU8Q,sBAAsB,EAChC;YACA,MAAMC,iBAAiBvV,mBAAmBwE,WAAWgR,OAAO,CAAC;gBAC3D,IAAI1U,QAAQC,GAAG,CAAC0U,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6ClR;gBAC3D;YACF;YAEA,IAAIE,WAAWiR,SAAS,EAAE;gBACxBjR,WAAWiR,SAAS,CAACL;YACvB,OAAO;gBACLjU,QAAQsU,SAAS,GAAGL;YACtB;QACF;QAEA,iDAAiD;QACjD,OAAO,IAAI3c,aAAaid,QAAQvU;IAClC;AACF;AAcA,OAAO,MAAMoV,uBAAsC,CACjDxP,KACAxB,KACA7C,UACA0B,OACAzB,qBACA6B,YACAqM,0BACAxP,aACA+E;QAaiB5B;IAXjB,IAAI,CAACuC,IAAIzC,GAAG,EAAE;QACZ,MAAM,qBAAwB,CAAxB,IAAIqK,MAAM,gBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B;IAEA,MAAMrK,MAAMtG,iBAAiB+I,IAAIzC,GAAG,EAAEtD,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAM2P,uBAAuB1P,oBAAoB8F,IAAI7F,OAAO,EAAE;QAC5DG;QACAM,mBAAmB6C,WAAWuG,YAAY,CAACpJ,iBAAiB,KAAK;QACjEM,aAAa,GAAEuC,2BAAAA,WAAWqR,YAAY,qBAAvBrR,yBAAyBvC,aAAa;IACvD;IAEA,MAAM,EAAEX,iBAAiB,EAAEU,yBAAyB,EAAE,GAAG2O;IAEzD,IAAIC,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAOpM,WAAW8J,SAAS,KAAK,UAAU;QAC5C,IAAI3L,qBAAqB;YACvB,MAAM,qBAEL,CAFK,IAAIxD,eACR,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAyR,iBAAiBtU,oBACfkI,WAAW8J,SAAS,EACpB9J,WAAW/B,MAAM;IAErB;IAEA,IACEmO,CAAAA,kCAAAA,eAAgBzI,qBAAqB,KACrC3D,WAAW2D,qBAAqB,EAChC;QACA,MAAM,qBAEL,CAFK,IAAIhJ,eACR,+FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMkF,YAAYtK,gBAAgB;QAChCwI,MAAMiC,WAAWgS,WAAW,CAACC,UAAU,CAAClU,IAAI;QAC5CiC;QACA,8CAA8C;QAC9ClD;QACA+E,SAASD,cAAcC,OAAO;QAC9BrE;IACF;IAEA,OAAOzJ,iBAAiBgP,GAAG,CACzBlD,WACA,sBAAsB;IACtBqM,0BACA,mBAAmB;IACnB3J,KACAxB,KACAjB,KACA5B,UACA0B,OACAI,YACAH,WACAsM,sBACAC,gBACAC,0BACAzK,eACAzD;AAEJ,EAAC;AAED,SAAS8F,iCACPF,QAMC,EACDR,QAAqC,EACrC1D,SAAoB;QA8BhB0D;IA5BJ,IAAIQ,SAAS0D,aAAa,EAAE;QAC1BlE,SAAS2O,SAAS,GAAGnO,SAAS0D,aAAa,CAAC0K,IAAI,CAAC;IACnD;IAEA,uEAAuE;IACvE,MAAMC,cAAcC,OAAOtO,SAASyD,cAAc;IAClDjE,SAAS7G,OAAO,KAAK,CAAC;IACtB6G,SAAS7G,OAAO,CAAC3H,8BAA8B,GAAGqd;IAElD,yEAAyE;IACzE,YAAY;IACZ,IAAIvS,UAAUyS,WAAW,KAAK,SAASvO,SAASuD,mBAAmB,KAAK,GAAG;QACzE/D,SAASgP,YAAY,GAAG;YAAEnN,YAAY;YAAGC,QAAQ7I;QAAU;IAC7D,OAAO;QACL,gEAAgE;QAChE+G,SAASgP,YAAY,GAAG;YACtBnN,YACErB,SAASuD,mBAAmB,IAAIzM,iBAC5B,QACAkJ,SAASuD,mBAAmB;YAClCjC,QACEtB,SAASwD,eAAe,IAAI1M,iBACxB2B,YACAuH,SAASwD,eAAe;QAChC;IACF;IAEA,qCAAqC;IACrC,IAAIhE,EAAAA,yBAAAA,SAASgP,YAAY,qBAArBhP,uBAAuB6B,UAAU,MAAK,GAAG;QAC3C7B,SAASiP,iBAAiB,GAAG;YAC3BC,aAAa5S,UAAU6S,uBAAuB;YAC9C7H,OAAOhL,UAAU8S,iBAAiB;QACpC;IACF;AACF;AAEA,eAAelB,eACbjP,YAA0B,EAC1BD,GAAoB,EACpBxB,GAAqB,EACrB3B,GAAqB,EACrBG,IAAgB,EAChBmS,SAAc,EACdtF,cAAqC,EACrC7I,QAAqC,EACrC4N,2BAAuD;IAEvD,MAAM,EAAE3H,WAAW,EAAEjM,KAAK,EAAEW,QAAQ,EAAE8B,UAAU,EAAE,GAAGZ;IAErD,MAAM,EACJwT,QAAQ,EACRC,aAAa,EACb3P,uBAAuB,EACvBsB,YAAY,EACZsO,WAAW,EACXjQ,MAAM,KAAK,EACX0D,YAAY,EACZwM,aAAa,KAAK,EAClBpQ,6BAA6B,EAC7B5E,IAAI,EACJiV,qBAAqB,EACrBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,uBAAuB,EACxB,GAAGnT;IAEJyE,8BAA8BvB;IAE9B,MAAM,EAAE+H,0BAA0B,EAAEmI,wBAAwB,EAAE,GAC5Dlc;IACF,MAAMmc,4BAA4BlY,6BAA6BoC;IAE/D,MAAM+V,kBAAkB5Y,kBACtBzE,YAAYsd,uBAAuB,IACnChN,aAAaiN,mBAAmB;IAGlC,MAAMC,YACJZ,cAAca,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDxS,GAAG,CAAC,CAACuS,WAAc,CAAA;YAClBE,KAAK,GAAGtK,YAAY,OAAO,EAAEoK,WAAWnc,oBACtC2H,KACA,QACC;YACH2U,SAAS,EAAEb,gDAAAA,4BAA8B,CAACU,SAAS;YACnDd;YACAkB,UAAU;YACVzW;QACF,CAAA;IAEJ,MAAM,CAACyN,gBAAgBiJ,gBAAgB,GAAG9c,mBACxC0b,eACA,6CAA6C;IAC7C,8EAA8E;IAC9ErJ,aACAsJ,aACAI,8BACAzb,oBAAoB2H,KAAK,OACzB7B,OACAQ;IAGF,MAAMmW,4BAAwD,IAAI1I;IAClE,MAAM2I,gBAAgB;IACtB,SAASC,qBAAqB1R,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB5C,KAAK;IAE5B;IACA,MAAMiV,+BAA+Bje,kCACnCyM,KACAkQ,YACAmB,2BACAC,eACAC;IAGF,SAASE,qBAAqB5R,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB5C,KAAK;IAE5B;IAEA,MAAMmV,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2Bne,uBAC/BwM,KACAkQ,YACAmB,2BACAK,mBACAJ,eACAG;IAGF,IAAIG,oBAA8C;IAElD,MAAMtQ,YAAYpD,IAAIoD,SAAS,CAACuQ,IAAI,CAAC3T;IACrC,MAAM4T,eAAe5T,IAAI4T,YAAY,CAACD,IAAI,CAAC3T;IAE3C,IAAI;QACF,IACE,qDAAqD;QACrD8B,OACA,uEAAuE;QACvE1G,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,oGAAoG;QACpGF,QAAQC,GAAG,CAACyR,YAAY,KAAK,UAC7B,+EAA+E;QAC/EtH,aAAa0G,eAAe,EAC5B;YACA,wFAAwF;YACxF,MAAMnK,aAGF,MAAMtI,qBAAqBuI,GAAG,CAChCP,cACA4F,eACA7I,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAErB,MAAM,CAACgW,mBAAmBC,iBAAiB,GAAGC;YAC9ChS,WAAWiS,WAAW,GAAGF;YAEzB,MAAM9J,oBAAoB,MAAMvQ,qBAAqBuI,GAAG,CACtDP,cACAlI,2BACA;gBACEkI,aAAawS,cAAc,GAAG;gBAC9B,OAAOxQ,aAAavB,sBAAsB,CACxCH,YACAI,wBAAwBC,aAAa,EACrC;oBACEP,SAASyR;oBACTY,iBAAiB,IACfzS,aAAawS,cAAc,KAAK,OAAO,cAAc;oBACvD9Y;gBACF;YAEJ,GACA;gBACEsG,aAAawS,cAAc,GAAG;YAChC;YAGFE,4BACEN,mBACArV,MACAH,KACA2B,IAAInC,UAAU,KAAK,KACnBsE,yBACAV,cACA2O;YAGFsD,oBAAoB,IAAIxa,kBAAkB8Q;QAC5C,OAAO;YACL,wFAAwF;YACxF,MAAMjI,aAAa,MAAMtI,qBAAqBuI,GAAG,CAC/CP,cACA4F,eACA7I,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAGrB6V,oBAAoB,IAAIxa,kBACtBO,qBAAqBuI,GAAG,CACtBP,cACAgC,aAAavB,sBAAsB,EACnCH,YACAI,wBAAwBC,aAAa,EACrC;gBACEjH;gBACA0G,SAASyR;YACX;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAM9Z;QAEN,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAOyF,WAAW8J,SAAS,KAAK,UAAU;YAC5C,IAAIsC,CAAAA,kCAAAA,eAAgB1N,IAAI,MAAK9G,aAAaud,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+Bhd,gCACnCqc,kBAAkBY,GAAG,IACrB9X,OACAmU;gBAGF,OAAOxd,aACLkhB,8BACAhhB;YAEJ,OAAO,IAAIgY,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAM,EAAEtC,SAAS,EAAEwL,YAAY,EAAE,GAC/Brd,sBAAsBmU;gBACxB,MAAMmJ,SAAS,AACbjZ,QAAQ,oBACRiZ,MAAM;gBAER,MAAMC,aAAa,MAAMhb,qBAAqBuI,GAAG,CAC/CP,cACA+S,sBACA,KAACzK;oBACCC,mBAAmB0J,kBAAkBY,GAAG;oBACxCrK,gBAAgBA;oBAChB9H,yBAAyBA;oBACzB+H,4BAA4BA;oBAC5B1N,OAAOA;oBAETuM,WACA;oBAAElH,SAAS4R;oBAA0BjX;gBAAM;gBAG7C,MAAMkY,wBAAwBpe,0BAA0B;oBACtDoc;oBACAL;oBACAsC,sBAAsBnB;oBACtB3B;oBACAU,iBAAiBA;gBACnB;gBACA,OAAO,MAAM9e,0BAA0BghB,YAAY;oBACjD,oGAAoG;oBACpG,yCAAyC;oBACzC,qGAAqG;oBACrG,2FAA2F;oBAC3FG,8BACEL,iBAAiBzd,wBAAwB+d,KAAK;oBAChDC,mBAAmBzd,gCACjBqc,kBAAkBqB,OAAO,IACzBvY,OACAmU;oBAEF+D;oBACApC;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAMpQ,yBAAyB,AAC7B3G,QAAQ,oBACR2G,sBAAsB;QAExB,MAAMuS,aAAa,MAAMhb,qBAAqBuI,GAAG,CAC/CP,cACAS,sCACA,KAAC6H;YACCC,mBAAmB0J,kBAAkBY,GAAG;YACxCrK,gBAAgBA;YAChB9H,yBAAyBA;YACzB+H,4BAA4BA;YAC5B1N,OAAOA;YAET;YACEqF,SAAS4R;YACTjX;YACAwY,WAAW,CAACrZ;gBACVA,QAAQqN,OAAO,CAAC,CAAC+B,OAAOkK;oBACtBrB,aAAaqB,KAAKlK;gBACpB;YACF;YACAmK,kBAAkBjD;YAClBkD,kBAAkB;gBAACjC;aAAgB;YACnCvC;QACF;QAGF,MAAM+D,wBAAwBpe,0BAA0B;YACtDoc;YACAL;YACAsC,sBAAsBnB;YACtB3B;YACAU,iBAAiBA;QACnB;QACA;;;;;;;;;;;;;;;;KAgBC,GACD,MAAM6C,qBACJhD,4BAA4B,QAAQ,CAAC,CAACF;QAExC,OAAO,MAAM5e,mBAAmBmhB,YAAY;YAC1CK,mBAAmBzd,gCACjBqc,kBAAkBqB,OAAO,IACzBvY,OACAmU;YAEF3P,oBAAoBoU;YACpBC,yBAAyBhX,IAAIS,SAAS,CAACuW,uBAAuB,KAAK;YACnEvU,SAASzC,IAAIS,SAAS,CAACgC,OAAO;YAC9B4T;YACApC;YACAgD,oBAAoBxT;QACtB;IACF,EAAE,OAAOH,KAAK;QACZ,IACEpK,wBAAwBoK,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIgI,OAAO,KAAK,YACvBhI,IAAIgI,OAAO,CAAC5B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMpG;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM4T,qBAAqBxf,oBAAoB4L;QAC/C,IAAI4T,oBAAoB;YACtB,MAAMzL,QAAQtS,4BAA4BmK;YAC1C1L,MACE,GAAG0L,IAAI6T,MAAM,CAAC,mDAAmD,EAAErY,SAAS,kFAAkF,EAAE2M,OAAO;YAGzK,MAAMnI;QACR;QAEA,IAAI8F;QAEJ,IAAI9S,0BAA0BgN,MAAM;YAClC3B,IAAInC,UAAU,GAAGnJ,4BAA4BiN;YAC7Ca,SAAS3E,UAAU,GAAGmC,IAAInC,UAAU;YACpC4J,YAAYhT,mCAAmCuL,IAAInC,UAAU;QAC/D,OAAO,IAAI/I,gBAAgB6M,MAAM;YAC/B8F,YAAY;YACZzH,IAAInC,UAAU,GAAGhJ,+BAA+B8M;YAChDa,SAAS3E,UAAU,GAAGmC,IAAInC,UAAU;YAEpC,MAAM4X,cAAcpf,cAAczB,wBAAwB+M,MAAMkQ;YAEhE,gEAAgE;YAChE,YAAY;YACZ,MAAMlW,UAAU,IAAI+Z;YACpB,IAAIxf,qBAAqByF,SAAS8F,aAAakU,cAAc,GAAG;gBAC9DvS,UAAU,cAAcwS,MAAM1H,IAAI,CAACvS,QAAQyT,MAAM;YACnD;YAEAhM,UAAU,YAAYqS;QACxB,OAAO,IAAI,CAACF,oBAAoB;YAC9BvV,IAAInC,UAAU,GAAG;YACjB2E,SAAS3E,UAAU,GAAGmC,IAAInC,UAAU;QACtC;QAEA,MAAM,CAACgY,qBAAqBC,qBAAqB,GAAG1f,mBAClD0b,eACArJ,aACAsJ,aACAI,8BACAzb,oBAAoB2H,KAAK,QACzB7B,OACA;QAGF,MAAMuZ,kBAAkB,MAAMtc,qBAAqBuI,GAAG,CACpDP,cACAyH,oBACA1K,MACAH,KACA8U,0BAA0B6C,GAAG,CAAC,AAACrU,IAAYoD,MAAM,IAAI,OAAOpD,KAC5D8F;QAGF,MAAMwO,oBAAoBxc,qBAAqBuI,GAAG,CAChDP,cACAgC,aAAavB,sBAAsB,EACnC6T,iBACA5T,wBAAwBC,aAAa,EACrC;YACEjH;YACA0G,SAASyR;QACX;QAGF,IAAII,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAM/R;QACR;QAEA,IAAI;YACF,MAAMuU,aAAa,MAAMzc,qBAAqBuI,GAAG,CAC/CP,cACArO,2BACA;gBACE+iB,gBACE5a,QAAQ;gBACV6a,uBACE,KAAClL;oBACClB,mBAAmBiM;oBACnB/L,4BAA4BA;oBAC5BD,gBAAgB4L;oBAChB1T,yBAAyBA;oBACzB3F,OAAOA;;gBAGX6Z,eAAe;oBACb7Z;oBACA,wCAAwC;oBACxC2Y,kBAAkB;wBAACW;qBAAqB;oBACxCnF;gBACF;YACF;YAGF;;;;;;;;;;;;;;;OAeC,GACD,MAAMyE,qBACJhD,4BAA4B,QAAQ,CAAC,CAACF;YACxC,OAAO,MAAM5e,mBAAmB4iB,YAAY;gBAC1CpB,mBAAmBzd,gCACjB,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACTqc,kBAAkBqB,OAAO,IACzBvY,OACAmU;gBAEF3P,oBAAoBoU;gBACpBC,yBAAyBhX,IAAIS,SAAS,CAACuW,uBAAuB,KAAK;gBACnEvU,SAASzC,IAAIS,SAAS,CAACgC,OAAO;gBAC9B4T,uBAAuBpe,0BAA0B;oBAC/Coc;oBACAL;oBACAsC,sBAAsB,EAAE;oBACxB9C;oBACAU,iBAAiBA;gBACnB;gBACAD;gBACAgD,oBAAoBxT;YACtB;QACF,EAAE,OAAOwU,UAAe;YACtB,IACElb,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB3G,0BAA0B2hB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1Bhb,QAAQ;gBACVgb;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,SAASvC;IACP,IAAIF;IACJ,IAAI2C,SAAS,IAAIC,QAAyB,CAACzQ;QACzC6N,oBAAoB7N;IACtB;IACA,OAAO;QAAC6N;QAAoB2C;KAAO;AACrC;AAEA;;;;;CAKC,GACD,eAAerC,4BACbN,iBAA+D,EAC/DrV,IAAgB,EAChBH,GAAqB,EACrBqY,UAAmB,EACnBvU,uBAA2E,EAC3EV,YAA0B,EAC1BrE,mBAA+C;QAuBxBqE;IArBvB,MAAM,EACJlD,cAAckF,YAAY,EAC1BpG,0BAA0B,EAC1BmG,YAAY,EACZhH,KAAK,EACLyC,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EAAEuI,wBAAwB,KAAK,EAAE,GAAG3H;IAE1C,iEAAiE;IACjE,yDAAyD;IACzD,MAAMgL,iBAAiB,KAAO;IAC9B,MAAM,EAAEC,0BAA0B,EAAE,GAAG/T;IAEvC,MAAMuM,aAAajM,cACjBgN,aAAajF,IAAI,EACjBnB;IAGF,MAAMoH,kBAAiBhD,4BAAAA,aAAaqB,OAAO,CAAC6T,GAAG,CAC7CviB,kDADqBqN,0BAEpBsJ,KAAK;IAER,6EAA6E;IAC7E,wEAAwE;IACxE,yEAAyE;IACzE,2EAA2E;IAC3E,UAAU;IACV,MAAMpH,mCAAmC,IAAIC;IAE7C,wDAAwD;IACxD,MAAMgT,+BAA+B,IAAIhT;IAEzC,6EAA6E;IAC7E,4EAA4E;IAC5E,4EAA4E;IAC5E,2EAA2E;IAC3E,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,qEAAqE;IACrE,4EAA4E;IAC5E,+DAA+D;IAC/D,MAAMC,gCAAgC,IAAID;IAE1C,4EAA4E;IAC5E,+BAA+B;IAC/B,MAAME,cAAc,IAAIpK;IAExB,MAAMmd,0BAA0B5jB,MAAMyR,iBAAiB;IACvD,MAAMoS,0BAA0BrT,aAAaiB,iBAAiB;IAE9D,iEAAiE;IACjE,8DAA8D;IAC9D,wEAAwE;IACxE,6BAA6B;IAC7B,MAAM/B,2BAA2B1I;IACjC,MAAM8c,qCAAqD;QACzDpZ,MAAM;QACNqG,OAAO;QACPtB;QACAtF;QACAoG;QACA,wGAAwG;QACxG,gFAAgF;QAChFS,cAAcJ,8BAA8BK,MAAM;QAClD,iFAAiF;QACjF,2FAA2F;QAC3F,mCAAmC;QACnCC,YAAY,IAAIP;QAChB,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBE;QACAM,iBAAiB;QACjBwC;QACAvC,YAAYvK;QACZwK,QAAQxK;QACRyK,OAAOzK;QACP0K,MAAM;eAAIhB,aAAagB,IAAI;SAAC;QAC5B7B;QACAC,uBAAuB;QACvB6B;QACAC,mBAAmBoS;IACrB;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAMlS,uBAAuB,MAAMnL,qBAAqBuI,GAAG,CACzD+U,oCACA1P,eACA7I,MACAH,KACAqY;IAGF,MAAM3S,8BAA8C;QAClDpG,MAAM;QACNqG,OAAO;QACPtB;QACAtF;QACAoG;QACAS,cAAcJ,8BAA8BK,MAAM;QAClDC,YAAYR;QACZ,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBG;QACAM,iBAAiB;QACjBwC;QACAvC,YAAYvK;QACZwK,QAAQxK;QACRyK,OAAOzK;QACP0K,MAAM;eAAIhB,aAAagB,IAAI;SAAC;QAC5B7B;QACAC,uBAAuB;QACvB6B;QACAC,mBAAmBoS;IACrB;IAEA,MAAMjS,6BAA6BpL,qBAAqBuI,GAAG,CACzD+B,6BACAN,aAAaqB,SAAS,EACtBF,sBACAzC,wBAAwBC,aAAa,EACrC;QACEjH;QACA0G,SAAS,CAACF;YACR,MAAMoD,SAASvP,2BAA2BmM;YAE1C,IAAIoD,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAIrK,uBAAuBiH,MAAM;gBAC/B,kBAAkB;gBAClBqO,QAAQ/Z,KAAK,CAAC0L;gBACd,OAAOlG;YACT;YAEA,IAAIkI,iCAAiCO,MAAM,CAACc,OAAO,EAAE;gBACnD,mEAAmE;gBACnE,iEAAiE;gBACjE;YACF,OAAO,IACL5J,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;gBACA5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;YAChE;QACF;QACA,iFAAiF;QACjF,qCAAqC;QACrCC,YAAY3J;QACZ,+EAA+E;QAC/E,iFAAiF;QACjF,iDAAiD;QACjDyI,QAAQ0S,6BAA6B1S,MAAM;IAC7C;IAGF,4EAA4E;IAC5E,6EAA6E;IAC7E,aAAa;IACb0S,6BAA6B1S,MAAM,CAAC8S,gBAAgB,CAClD,SACA;QACEnT,8BAA8ByB,KAAK;IACrC,GACA;QAAE2R,MAAM;IAAK;IAGf,8EAA8E;IAC9Exc,oBAAoBqJ;IACpB,MAAMA,YAAYuB,UAAU;IAE5BuR,6BAA6BtR,KAAK;IAElC,gEAAgE;IAChE,iEAAiE;IACjE,MAAM,EAAEC,wBAAwB,EAAE,GAAGzG;IACrC,IAAIyG,0BAA0B;QAC5BsO,gCACE,KAACqD;YACCC,IAAI;gBACFnH,QAAQ/Z,KAAK,CAACsP;YAChB;;QAGJ;IACF;IAEA,IAAI6R;IACJ,IAAI;QACFA,sBAAsB,MAAMje,iCAC1B0L;IAEJ,EAAE,OAAOlD,KAAK;QACZ,IACEiV,6BAA6B1S,MAAM,CAACc,OAAO,IAC3CrB,iCAAiCO,MAAM,CAACc,OAAO,EAC/C;QACA,4EAA4E;QAC9E,OAAO,IACL5J,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnF5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;QAChE;IACF;IAEA,IAAIiS,qBAAqB;QACvB,MAAMC,mCAAmC,IAAIzT;QAC7C,MAAM0T,+BAA+B,IAAI1T;QACzC,MAAM2T,gCAAgC,IAAI3T;QAE1C,MAAM4T,8BAA8C;YAClD7Z,MAAM;YACNqG,OAAO;YACPtB;YACAtF;YACAoG;YACAS,cAAcsT,8BAA8BrT,MAAM;YAClDC,YAAYkT;YACZ,sDAAsD;YACtD,qDAAqD;YACrDvT,aAAa;YACbM,iBAAiB;YACjBwC;YACAvC,YAAYvK;YACZwK,QAAQxK;YACRyK,OAAOzK;YACP0K,MAAM;mBAAIhB,aAAagB,IAAI;aAAC;YAC5B7B;YACAC,uBAAuB;YACvB6B,gBAAgBhJ;YAChBiJ,mBAAmBmS;QACrB;QAEA,MAAM/R,YAAY,AAChBvJ,QAAQ,oBACRuJ,SAAS;QACX,MAAM2S,6BAA6Bhe,qBAAqBuI,GAAG,CACzDwV,6BACA1S,yBACA,KAACiF;YACCC,mBAAmBoN,oBAAoBM,iBAAiB;YACxDzN,gBAAgBA;YAChB9H,yBAAyBA;YACzB+H,4BAA4BA;YAC5B1N,OAAOA;YAET;YACE0H,QAAQoT,6BAA6BpT,MAAM;YAC3CrC,SAAS,CAACF;gBACR,MAAMoD,SAASvP,2BAA2BmM;gBAE1C,IAAIoD,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAIrK,uBAAuBiH,MAAM;oBAC/B,kBAAkB;oBAClBqO,QAAQ/Z,KAAK,CAAC0L;oBACd,OAAOlG;gBACT;gBAEA,IAAI6b,6BAA6BpT,MAAM,CAACc,OAAO,EAAE;gBAC/C,4EAA4E;gBAC9E,OAAO,IACL5J,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnF5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;gBAChE;YACF;QAGF;QAGF,4EAA4E;QAC5E,4DAA4D;QAC5D,8BAA8B;QAC9BmS,6BAA6BpT,MAAM,CAAC8S,gBAAgB,CAClD,SACA;YACEO,8BAA8BjS,KAAK;QACrC,GACA;YAAE2R,MAAM;QAAK;QAGfQ,2BAA2BE,KAAK,CAAC,CAAChW;YAChC,IACE2V,6BAA6BpT,MAAM,CAACc,OAAO,IAC3CpN,4BAA4B+J,MAC5B;YACA,4EAA4E;YAC9E,OAAO,IACLvG,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;gBACA,8EAA8E;gBAC9E,mFAAmF;gBACnF5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;YAChE;QACF;QAEA,sEAAsE;QACtE,uGAAuG;QACvG1K,oBAAoBqJ;QACpB,MAAMA,YAAYuB,UAAU;QAC5BiS,6BAA6BhS,KAAK;IACpC;IAEA,MAAMsS,6BAA6B,IAAIhU;IACvC,MAAMiU,8BAA8B,IAAIjU;IAExC,MAAMkU,mCAAmD;QACvDna,MAAM;QACNqG,OAAO;QACPtB;QACAtF;QACAoG;QACA,wGAAwG;QACxG,gFAAgF;QAChFS,cAAc4T,4BAA4B3T,MAAM;QAChD,iFAAiF;QACjF,2FAA2F;QAC3F,mCAAmC;QACnCC,YAAY,IAAIP;QAChB,8EAA8E;QAC9EE,aAAa;QACbM,iBAAiB;QACjBwC;QACAvC,YAAYvK;QACZwK,QAAQxK;QACRyK,OAAOzK;QACP0K,MAAM;eAAIhB,aAAagB,IAAI;SAAC;QAC5B7B;QACAC,uBAAuB;QACvB6B;QACAC,mBAAmBoS;IACrB;IAEA,MAAMiB,yBAAyB,MAAMte,qBAAqBuI,GAAG,CAC3D8V,kCACAzQ,eACA7I,MACAH,KACAqY;IAGF,MAAM5Q,wBAAwBjO,2BAC5B,MAAM,yBAAyB;;IAGjC,MAAMqO,4BAA4C;QAChDvI,MAAM;QACNqG,OAAO;QACPtB;QACAtF;QACAoG;QACAS,cAAc4T,4BAA4B3T,MAAM;QAChDC,YAAYyT;QACZ,8EAA8E;QAC9E9T,aAAa;QACbM,iBAAiB0B;QACjBc;QACAvC,YAAYvK;QACZwK,QAAQxK;QACRyK,OAAOzK;QACP0K,MAAM;eAAIhB,aAAagB,IAAI;SAAC;QAC5B7B;QACAC,uBAAuB;QACvB6B;QACAC,mBAAmBoS;IACrB;IAEA,MAAMpD,oBAAoB,MAAMva,iCAC9BE,mCACE;QACE,MAAM2e,yBAAyBve,qBAAqBuI,GAAG,CACrD,qBAAqB;QACrBkE,2BACA,sBAAsB;QACtBzC,aAAaqB,SAAS,EACtB,4CAA4C;QAC5CiT,wBACA5V,wBAAwBC,aAAa,EACrC;YACEjH;YACA0G,SAAS,CAACF;gBACR,IACEiW,2BAA2B1T,MAAM,CAACc,OAAO,IACzCpN,4BAA4B+J,MAC5B;oBACA,OAAOA,IAAIoD,MAAM;gBACnB;gBAEA,IAAIrK,uBAAuBiH,MAAM;oBAC/B,kBAAkB;oBAClBqO,QAAQ/Z,KAAK,CAAC0L;oBACd,OAAOlG;gBACT;gBAEA,OAAOjG,2BAA2BmM;YACpC;YACAuC,QAAQ0T,2BAA2B1T,MAAM;QAC3C;QAGF,sEAAsE;QACtE,kEAAkE;QAClE,8BAA8B;QAC9B0T,2BAA2B1T,MAAM,CAAC8S,gBAAgB,CAChD,SACA;YACEa,4BAA4BvS,KAAK;QACnC,GACA;YAAE2R,MAAM;QAAK;QAGf,OAAOe;IACT,GACA;QACEJ,2BAA2BtS,KAAK;IAClC;IAIJ,MAAM2S,wBAAwBpgB,2BAC5B,MAAM,wBAAwB;;IAEhC,MAAMqgB,6BAA6B,IAAItU;IACvC,MAAMuU,8BAA8B,IAAIvU;IAExC,MAAMwU,4BAA4C;QAChDza,MAAM;QACNqG,OAAO;QACPtB;QACAtF;QACAoG;QACAS,cAAckU,4BAA4BjU,MAAM;QAChDC,YAAY+T;QACZ,oFAAoF;QACpFpU,aAAa;QACbM,iBAAiB6T;QACjBrR;QACAvC,YAAYvK;QACZwK,QAAQxK;QACRyK,OAAOzK;QACP0K,MAAM;eAAIhB,aAAagB,IAAI;SAAC;QAC5B7B;QACAC,uBAAuB;QACvB6B;QACAC,mBAAmBmS;IACrB;IAEA,IAAIwB,oBAAoBvgB;IAExB,IAAI;QACF,MAAMgN,YAAY,AAChBvJ,QAAQ,oBACRuJ,SAAS;QACX,IAAI,EAAExB,SAASgV,kBAAkB,EAAE,GACjC,MAAMjf,mCACJ;YACE,MAAMkf,2BAA2B9e,qBAAqBuI,GAAG,CACvDoW,2BACAtT,yBACA,KAACiF;gBACCC,mBAAmB0J,kBAAkBgE,iBAAiB;gBACtDzN,gBAAgBA;gBAChB9H,yBAAyBA;gBACzB+H,4BAA4BA;gBAC5B1N,OAAOA;gBAET;gBACE0H,QAAQgU,2BAA2BhU,MAAM;gBACzCrC,SAAS,CAACF,KAAc6W;oBACtB,IACE5gB,4BAA4B+J,QAC5BuW,2BAA2BhU,MAAM,CAACc,OAAO,EACzC;wBACA,MAAMyT,iBAAiBD,UAAUC,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtC1gB,0BACE+G,WACA2Z,gBACAJ,mBACAJ;wBAEJ;wBACA;oBACF;oBAEA,IAAIvd,uBAAuBiH,MAAM;wBAC/B,kBAAkB;wBAClBqO,QAAQ/Z,KAAK,CAAC0L;wBACd,OAAOlG;oBACT;oBAEA,OAAOjG,2BAA2BmM;gBACpC;YAGF;YAGF,sEAAsE;YACtE,kEAAkE;YAClE,8BAA8B;YAC9BuW,2BAA2BhU,MAAM,CAAC8S,gBAAgB,CAChD,SACA;gBACEmB,4BAA4B7S,KAAK;YACnC,GACA;gBAAE2R,MAAM;YAAK;YAGf,OAAOsB;QACT,GACA;YACEL,2BAA2B5S,KAAK;QAClC;QAGJ,MAAM,EAAEoT,cAAc,EAAE,GAAG,MAAMzf,eAAeqf;QAChDzE,gCACE,KAACqD;YACCC,IAAInf,yBAAyB2b,IAAI,CAC/B,MACA7U,WACA4Z,iBAAiBzgB,aAAa4c,KAAK,GAAG5c,aAAa0gB,IAAI,EACvDN,mBACAvS;;IAIR,EAAE,OAAO8S,aAAa;QACpB,8EAA8E;QAC9E,gDAAgD;QAEhD,IAAIC,kBAAkB7gB,yBAAyB2b,IAAI,CACjD,MACA7U,WACA7G,aAAa6gB,OAAO,EACpBT,mBACAvS;QAGF,IAAI1K,QAAQC,GAAG,CAAC4J,gBAAgB,IAAI7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAAE;YACtE,8EAA8E;YAC9E,mFAAmF;YACnF,MAAM6T,0BAA0BF;YAChCA,kBAAkB;gBAChB7I,QAAQ/Z,KAAK,CACX;gBAEF+Z,QAAQ/Z,KAAK,CAAC2iB;gBACdG;YACF;QACF;QAEAlF,gCAAkB,KAACqD;YAAUC,IAAI0B;;IACnC;AACF;AAEA,eAAe3B,UAAU,EAAEC,EAAE,EAAyB;IACpD,IAAI;QACF,MAAMA;IACR,EAAE,OAAM,CAAC;IACT,OAAO;AACT;AAcA;;CAEC,GACD,SAAS6B,+BAA+Bla,SAAoB;IAC1D,MAAM,EAAEkC,kBAAkB,EAAE,GAAGlC;IAC/B,IAAI,CAACkC,oBAAoB,OAAO;IAEhC,OAAO;AACT;AAEA,eAAe+N,kBACbvN,GAAoB,EACpBxB,GAAqB,EACrB3B,GAAqB,EACrBmE,QAAqC,EACrChE,IAAgB,EAChBpB,mBAA+C;IAE/C,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAMuT,YAAY;IAElB,MAAM,EACJlI,WAAW,EACXpL,0BAA0B,EAC1BmG,YAAY,EACZhH,KAAK,EACLW,QAAQ,EACR8B,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EACJuI,wBAAwB,KAAK,EAC7BiL,QAAQ,EACRC,aAAa,EACb3P,uBAAuB,EACvBsB,YAAY,EACZsO,WAAW,EACXjQ,MAAM,KAAK,EACX0D,YAAY,EACZC,sBAAsB,EACtBuM,aAAa,KAAK,EAClBpQ,6BAA6B,EAC7B5E,IAAI,EACJiV,qBAAqB,EACrBE,4BAA4B,EAC7B,GAAGlT;IAEJyE,8BAA8BvB;IAE9B,MAAMO,aAAajM,cAAc+H,MAAMnB;IAEvC,MAAM,EAAE6M,0BAA0B,EAAEmI,wBAAwB,EAAE,GAC5Dlc;IACF,MAAMmc,4BAA4BlY,6BAA6BoC;IAE/D,MAAM+V,kBAAkB5Y,kBACtBzE,YAAYsd,uBAAuB,IACnChN,aAAaiN,mBAAmB;IAGlC,MAAMC,YACJZ,cAAca,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDxS,GAAG,CAAC,CAACuS,WAAc,CAAA;YAClBE,KAAK,GAAGtK,YAAY,OAAO,EAAEoK,WAAWnc,oBACtC2H,KACA,QACC;YACH2U,SAAS,EAAEb,gDAAAA,4BAA8B,CAACU,SAAS;YACnDd;YACAkB,UAAU;YACVzW;QACF,CAAA;IAEJ,MAAM,CAACyN,gBAAgBiJ,gBAAgB,GAAG9c,mBACxC0b,eACA,6CAA6C;IAC7C,8EAA8E;IAC9ErJ,aACAsJ,aACAI,8BACAzb,oBAAoB2H,KAAK,OACzB7B,OACAQ;IAGF,MAAMmW,4BAAwD,IAAI1I;IAClE,+EAA+E;IAC/E,MAAM2I,gBAAgB,CAAC,CAAC5N,aAAapJ,iBAAiB;IACtD,SAASiX,qBAAqB1R,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB5C,KAAK;IAE5B;IACA,MAAMiV,+BAA+Bje,kCACnCyM,KACAkQ,YACAmB,2BACAC,eACAC;IAGF,SAASE,qBAAqB5R,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB5C,KAAK;IAE5B;IACA,MAAMmV,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2Bne,uBAC/BwM,KACAkQ,YACAmB,2BACAK,mBACAJ,eACAG;IAGF,IAAI0F,6BAAgE;IACpE,MAAMC,oBAAoB,CAAChb;QACzBsE,SAAS7G,OAAO,KAAK,CAAC;QACtB6G,SAAS7G,OAAO,CAACuC,KAAK,GAAG8B,IAAI6H,SAAS,CAAC3J;IACzC;IACA,MAAMkF,YAAY,CAAClF,MAAc6M;QAC/B/K,IAAIoD,SAAS,CAAClF,MAAM6M;QACpBmO,kBAAkBhb;QAClB,OAAO8B;IACT;IACA,MAAM4T,eAAe,CAAC1V,MAAc6M;QAClC,IAAI6K,MAAMuD,OAAO,CAACpO,QAAQ;YACxBA,MAAM/B,OAAO,CAAC,CAACoQ;gBACbpZ,IAAI4T,YAAY,CAAC1V,MAAMkb;YACzB;QACF,OAAO;YACLpZ,IAAI4T,YAAY,CAAC1V,MAAM6M;QACzB;QACAmO,kBAAkBhb;IACpB;IAEA,MAAMwH,kBAAkBC,sBAAsBH;IAE9C,IAAIwB,iBAAwC;IAE5C,IAAI;QACF,IAAIxB,aAAa0G,eAAe,EAAE;YAChC;;;;;;;;;;;;OAYC,GAED,wEAAwE;YACxE,0EAA0E;YAC1E,mEAAmE;YACnE,yEAAyE;YACzE,qBAAqB;YACrB,MAAMvI,mCAAmC,IAAIC;YAE7C,wDAAwD;YACxD,MAAMgT,+BAA+B,IAAIhT;YAEzC,sEAAsE;YACtE,sEAAsE;YACtE,kEAAkE;YAClE,wEAAwE;YACxE,wEAAwE;YACxE,wEAAwE;YACxE,wEAAwE;YACxE,0EAA0E;YAC1E,sEAAsE;YACtE,wEAAwE;YACxE,+BAA+B;YAC/B,MAAMC,gCAAgC,IAAID;YAE1C,kFAAkF;YAClF,yBAAyB;YACzB,MAAME,cAAc,IAAIpK;YAExB,IAAI2f;YACJ,IAAIzW,wBAAsD;YAC1D,IAAID,2BAA4D;YAEhE,IAAI1D,WAAW2D,qBAAqB,EAAE;gBACpC,sEAAsE;gBACtE,wEAAwE;gBACxE,uEAAuE;gBACvE,cAAc;gBACdyW,kBAAkBzW,wBAChB3D,WAAW2D,qBAAqB;YACpC,OAAO;gBACL,iEAAiE;gBACjEyW,kBAAkB1W,2BAChB1I;YACJ;YAEA,MAAM8c,qCAAqD;gBACzDpZ,MAAM;gBACNqG,OAAO;gBACPtB;gBACAtF;gBACAoG;gBACA,wGAAwG;gBACxG,gFAAgF;gBAChFS,cAAcJ,8BAA8BK,MAAM;gBAClD,iFAAiF;gBACjF,2FAA2F;gBAC3F,mCAAmC;gBACnCC,YAAY,IAAIP;gBAChB,0EAA0E;gBAC1E,2EAA2E;gBAC3E,uBAAuB;gBACvBE;gBACAM,iBAAiB;gBACjBwC;gBACAvC,YAAYvK;gBACZwK,QAAQxK;gBACRyK,OAAOzK;gBACP0K,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;gBAC5B7B;gBACAC;gBACA6B,gBAAgBhJ;gBAChBiJ,mBAAmBjJ;YACrB;YAEA,0FAA0F;YAC1F,wFAAwF;YACxF,MAAMmJ,uBAAuB,MAAMnL,qBAAqBuI,GAAG,CACzD+U,oCACA1P,eACA7I,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAGrB,MAAMkG,8BAA+CiD,iBAAiB;gBACpErJ,MAAM;gBACNqG,OAAO;gBACPtB;gBACAtF;gBACAoG;gBACAS,cAAcJ,8BAA8BK,MAAM;gBAClDC,YAAYR;gBACZ,0EAA0E;gBAC1E,2EAA2E;gBAC3E,uBAAuB;gBACvBG;gBACAM,iBAAiB;gBACjBwC;gBACAvC,YAAYvK;gBACZwK,QAAQxK;gBACRyK,OAAOzK;gBACP0K,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;gBAC5B7B;gBACAC;gBACA6B,gBAAgBhJ;gBAChBiJ,mBAAmBjJ;YACrB;YAEA,MAAMoJ,6BAA6BpL,qBAAqBuI,GAAG,CACzD+B,6BACAN,aAAaqB,SAAS,EACtBF,sBACAzC,wBAAwBC,aAAa,EACrC;gBACEjH;gBACA0G,SAAS,CAACF;oBACR,MAAMoD,SAASvP,2BAA2BmM;oBAE1C,IAAIoD,QAAQ;wBACV,OAAOA;oBACT;oBAEA,IAAIrK,uBAAuBiH,MAAM;wBAC/B,kBAAkB;wBAClBqO,QAAQ/Z,KAAK,CAAC0L;wBACd,OAAOlG;oBACT;oBAEA,IAAIkI,iCAAiCO,MAAM,CAACc,OAAO,EAAE;wBACnD,mEAAmE;wBACnE,iEAAiE;wBACjE;oBACF,OAAO,IACL5J,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;wBACA5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;oBAChE;gBACF;gBACA,iFAAiF;gBACjF,qCAAqC;gBACrCC,YAAY3J;gBACZ,+EAA+E;gBAC/E,iFAAiF;gBACjF,iDAAiD;gBACjDyI,QAAQ0S,6BAA6B1S,MAAM;YAC7C;YAGF,sEAAsE;YACtE,kEAAkE;YAClE,8BAA8B;YAC9B0S,6BAA6B1S,MAAM,CAAC8S,gBAAgB,CAClD,SACA;gBACEnT,8BAA8ByB,KAAK;YACrC,GACA;gBAAE2R,MAAM;YAAK;YAGf,8EAA8E;YAC9Exc,oBAAoBqJ;YACpB,MAAMA,YAAYuB,UAAU;YAE5BuR,6BAA6BtR,KAAK;YAElC,gEAAgE;YAChE,iEAAiE;YACjE,IAAIxG,UAAUyG,wBAAwB,EAAE;gBACtCpN,0BAA0B2G,WAAWA,UAAUyG,wBAAwB;gBACvE,MAAM,IAAIjO;YACZ;YAEA,IAAI8f;YACJ,IAAI;gBACFA,sBAAsB,MAAMje,iCAC1B0L;YAEJ,EAAE,OAAOlD,KAAK;gBACZ,IACEiV,6BAA6B1S,MAAM,CAACc,OAAO,IAC3CrB,iCAAiCO,MAAM,CAACc,OAAO,EAC/C;gBACA,4EAA4E;gBAC9E,OAAO,IACL5J,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnF5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;gBAChE;YACF;YAEA,IAAIiS,qBAAqB;gBACvB,MAAMC,mCAAmC,IAAIzT;gBAC7C,MAAM0T,+BAA+B,IAAI1T;gBACzC,MAAM2T,gCAAgC,IAAI3T;gBAE1C,MAAM4T,8BAA8C;oBAClD7Z,MAAM;oBACNqG,OAAO;oBACPtB;oBACAtF;oBACAoG;oBACAS,cAAcsT,8BAA8BrT,MAAM;oBAClDC,YAAYkT;oBACZ,sDAAsD;oBACtD,qDAAqD;oBACrDvT,aAAa;oBACbM,iBAAiB;oBACjBwC;oBACAvC,YAAYvK;oBACZwK,QAAQxK;oBACRyK,OAAOzK;oBACP0K,MAAM;2BAAIhB,aAAagB,IAAI;qBAAC;oBAC5B7B;oBACAC;oBACA6B,gBAAgBhJ;oBAChBiJ,mBAAmBjJ;gBACrB;gBAEA,MAAMqJ,YAAY,AAChBvJ,QAAQ,oBACRuJ,SAAS;gBACX,MAAM2S,6BAA6Bhe,qBAAqBuI,GAAG,CACzDwV,6BACA1S,yBACA,KAACiF;oBACCC,mBAAmBoN,oBAAoBM,iBAAiB;oBACxDzN,gBAAgBA;oBAChB9H,yBAAyBA;oBACzB+H,4BAA4BA;oBAC5B1N,OAAOA;oBAET;oBACE0H,QAAQoT,6BAA6BpT,MAAM;oBAC3CrC,SAAS,CAACF;wBACR,MAAMoD,SAASvP,2BAA2BmM;wBAE1C,IAAIoD,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAIrK,uBAAuBiH,MAAM;4BAC/B,kBAAkB;4BAClBqO,QAAQ/Z,KAAK,CAAC0L;4BACd,OAAOlG;wBACT;wBAEA,IAAI6b,6BAA6BpT,MAAM,CAACc,OAAO,EAAE;wBAC/C,4EAA4E;wBAC9E,OAAO,IACL5J,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnF5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;wBAChE;oBACF;oBACAgQ,kBAAkB;wBAACjC;qBAAgB;gBACrC;gBAGF,sEAAsE;gBACtE,kEAAkE;gBAClE,8BAA8B;gBAC9BoE,6BAA6BpT,MAAM,CAAC8S,gBAAgB,CAClD,SACA;oBACEO,8BAA8BjS,KAAK;gBACrC,GACA;oBAAE2R,MAAM;gBAAK;gBAGfQ,2BAA2BE,KAAK,CAAC,CAAChW;oBAChC,IACE2V,6BAA6BpT,MAAM,CAACc,OAAO,IAC3CpN,4BAA4B+J,MAC5B;oBACA,4EAA4E;oBAC9E,OAAO,IACLvG,QAAQC,GAAG,CAAC4J,gBAAgB,IAC5B7J,QAAQC,GAAG,CAAC6J,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnF5L,0CAA0CqI,KAAK7C,UAAUqG,KAAK;oBAChE;gBACF;gBAEA,sEAAsE;gBACtE,uGAAuG;gBACvG1K,oBAAoBqJ;gBACpB,MAAMA,YAAYuB,UAAU;gBAC5BiS,6BAA6BhS,KAAK;YACpC;YAEA,MAAMsS,6BAA6B,IAAIhU;YACvC,MAAMiU,8BAA8B,IAAIjU;YAExC,MAAMkU,mCAAmD;gBACvDna,MAAM;gBACNqG,OAAO;gBACPtB;gBACAtF;gBACAoG;gBACA,wGAAwG;gBACxG,gFAAgF;gBAChFS,cAAc4T,4BAA4B3T,MAAM;gBAChD,iFAAiF;gBACjF,2FAA2F;gBAC3F,mCAAmC;gBACnCC,YAAY,IAAIP;gBAChB,8EAA8E;gBAC9EE,aAAa;gBACbM,iBAAiB;gBACjBwC;gBACAvC,YAAYvK;gBACZwK,QAAQxK;gBACRyK,OAAOzK;gBACP0K,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;gBAC5B7B;gBACAC;gBACA6B,gBAAgBhJ;gBAChBiJ,mBAAmBjJ;YACrB;YAEA,MAAMsc,yBAAyB,MAAMte,qBAAqBuI,GAAG,CAC3D8V,kCACAzQ,eACA7I,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAGrB,MAAMiI,wBAAwBjO,2BAC5B4N;YAEF,IAAIG,kBAAkB;YAEtB,MAAMM,4BAA6Cc,iBAAiB;gBAClErJ,MAAM;gBACNqG,OAAO;gBACPtB;gBACAtF;gBACAoG;gBACAS,cAAc4T,4BAA4B3T,MAAM;gBAChDC,YAAYyT;gBACZ,8EAA8E;gBAC9E9T,aAAa;gBACbM,iBAAiB0B;gBACjBc;gBACAvC,YAAYvK;gBACZwK,QAAQxK;gBACRyK,OAAOzK;gBACP0K,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;gBAC5B7B;gBACAC;gBACA6B,gBAAgBhJ;gBAChBiJ,mBAAmBjJ;YACrB;YAEA,IAAI2K,qBAAqB;YACzB,MAAMsN,oBAAqBuF,6BACzB,MAAM9f,iCACJE,mCACE;gBACE,MAAM2e,yBAAyBve,qBAAqBuI,GAAG,CACrD,qBAAqB;gBACrBkE,2BACA,sBAAsB;gBACtBzC,aAAaqB,SAAS,EACtB,4CAA4C;gBAC5CiT,wBACA5V,wBAAwBC,aAAa,EACrC;oBACEjH;oBACA0G,SAAS,CAACF;wBACR,OAAO2R,6BAA6B3R;oBACtC;oBACAuC,QAAQ0T,2BAA2B1T,MAAM;gBAC3C;gBAGF,gEAAgE;gBAChE,iEAAiE;gBACjE,qCAAqC;gBACrC0T,2BAA2B1T,MAAM,CAAC8S,gBAAgB,CAChD,SACA;oBACEa,4BAA4BvS,KAAK;gBACnC,GACA;oBAAE2R,MAAM;gBAAK;gBAGf,MAAM5Q,kBAAkB,MAAM2R;gBAC9B5R,qBAAqB;gBAErB,OAAOC;YACT,GACA;gBACE,IAAIuR,2BAA2B1T,MAAM,CAACc,OAAO,EAAE;oBAC7C,4EAA4E;oBAC5E,6EAA6E;oBAC7EY,kBAAkB;oBAClB;gBACF;gBAEA,IAAIQ,oBAAoB;oBACtB,kFAAkF;oBAClF,iCAAiC;oBACjCR,kBAAkB;gBACpB;gBAEAgS,2BAA2BtS,KAAK;YAClC;YAIN,MAAM2S,wBAAwBpgB,2BAC5B4N;YAGF,MAAMyS,6BAA6B,IAAItU;YACvC,MAAMuU,8BAA8B,IAAIvU;YAExC,MAAMwU,4BAA4C;gBAChDza,MAAM;gBACNqG,OAAO;gBACPtB;gBACAtF;gBACAoG;gBACAS,cAAckU,4BAA4BjU,MAAM;gBAChDC,YAAY+T;gBACZ,oFAAoF;gBACpFpU,aAAa;gBACbM,iBAAiB6T;gBACjBrR;gBACAvC,YAAYvK;gBACZwK,QAAQxK;gBACRyK,OAAOzK;gBACP0K,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;gBAC5B7B;gBACAC;gBACA6B,gBAAgBhJ;gBAChBiJ,mBAAmBjJ;YACrB;YAEA,IAAI4c,oBAAoBvgB;YAExB,MAAMgN,YAAY,AAChBvJ,QAAQ,oBACRuJ,SAAS;YACX,IAAI,EAAExB,SAASgV,kBAAkB,EAAEvP,SAAS,EAAE,GAC5C,MAAM1P,mCACJ;gBACE,MAAMkf,2BAA2B9e,qBAAqBuI,GAAG,CACvDoW,2BACAtT,yBACA,KAACiF;oBACCC,mBAAmB0J,kBAAkBgE,iBAAiB;oBACtDzN,gBAAgBA;oBAChB9H,yBAAyBA;oBACzB+H,4BAA4BA;oBAC5B1N,OAAOA;oBAET;oBACE0H,QAAQgU,2BAA2BhU,MAAM;oBACzCrC,SAAS,CAACF,KAAc6W;wBACtB,IACE5gB,4BAA4B+J,QAC5BuW,2BAA2BhU,MAAM,CAACc,OAAO,EACzC;4BACA,MAAMyT,iBAAqC,AACzCD,UACAC,cAAc;4BAChB,IAAI,OAAOA,mBAAmB,UAAU;gCACtC1gB,0BACE+G,WACA2Z,gBACAJ,mBACAJ;4BAEJ;4BACA;wBACF;wBAEA,OAAOxE,yBAAyB9R,KAAK6W;oBACvC;oBACAxD,WAAW,CAACrZ;wBACVA,QAAQqN,OAAO,CAAC,CAAC+B,OAAOkK;4BACtBrB,aAAaqB,KAAKlK;wBACpB;oBACF;oBACAmK,kBAAkBjD;oBAClBkD,kBAAkB;wBAACjC;qBAAgB;gBACrC;gBAGF,gEAAgE;gBAChE,oEAAoE;gBACpE,kCAAkC;gBAClCgF,2BAA2BhU,MAAM,CAAC8S,gBAAgB,CAChD,SACA;oBACEmB,4BAA4B7S,KAAK;gBACnC,GACA;oBAAE2R,MAAM;gBAAK;gBAGf,OAAOsB;YACT,GACA;gBACEL,2BAA2B5S,KAAK;YAClC;YAGJ,MAAM,EAAEhC,OAAO,EAAEoV,cAAc,EAAE,GAC/B,MAAMzf,eAAeqf;YAEvB,0EAA0E;YAC1E,2EAA2E;YAC3E,kCAAkC;YAClC,IAAI,CAAC1R,uBAAuB;gBAC1B5O,yBACE8G,WACA4Z,iBAAiBzgB,aAAa4c,KAAK,GAAG5c,aAAa0gB,IAAI,EACvDN,mBACAvS;YAEJ;YAEA,MAAM4O,wBAAwBpe,0BAA0B;gBACtDoc;gBACAL;gBACAsC,sBAAsBnB;gBACtB3B;gBACAU,iBAAiBA;YACnB;YAEA,MAAMjU,aAAa,MAAM5K,eAAeggB,kBAAkB4F,QAAQ;YAClE9W,SAASlE,UAAU,GAAGA;YACtBkE,SAAS+W,WAAW,GAAG,MAAMC,mBAC3Blb,YACA4H,2BACAzC,cACAxE;YAGF,yEAAyE;YACzE,wEAAwE;YACxE,yEAAyE;YACzE,iEAAiE;YACjE,MAAMwa,yBACJrc,uBAAuBA,oBAAoB8R,IAAI,GAAG;YAEpD,IAAItJ,mBAAmB6T,wBAAwB;gBAC7C,eAAe;gBACf,4FAA4F;gBAC5F,0FAA0F;gBAC1F,0FAA0F;gBAC1F,oCAAoC;gBACpC,IAAI1Q,aAAa,MAAM;oBACrB,oBAAoB;oBACpBvG,SAASuG,SAAS,GAAG,MAAM9R,6BACzB8R,WACA2P,iBACI5hB,wBAAwB+d,KAAK,GAC7B/d,wBAAwB6hB,IAAI,EAChCvb,qBACAic;gBAEJ,OAAO;oBACL,oBAAoB;oBACpB7W,SAASuG,SAAS,GAChB,MAAM/R,6BAA6BqiB;gBACvC;gBACA3F,kBAAkBqB,OAAO;gBACzB,OAAO;oBACL9F,iBAAiBkE;oBACjB7D,WAAWkE;oBACXrD,QAAQ,MAAM5c,yBAAyB+P,SAAS;wBAC9CoR;wBACApC;oBACF;oBACAhM,eAAepO,qBACb4N,uBACAmS;oBAEF,0CAA0C;oBAC1C1R,qBAAqBL,0BAA0B7B,UAAU;oBACzDmC,iBAAiBN,0BAA0B5B,MAAM;oBACjDmC,gBAAgBf,gBAAgBQ,0BAA0B3B,KAAK;oBAC/DmC,eAAeR,0BAA0B1B,IAAI;oBAC7C5B,uBAAuB1I,4BAA4Bmf;gBACrD;YACF,OAAO;gBACL,cAAc;gBACd,mGAAmG;gBACnG,6EAA6E;gBAC7E,IAAIva,UAAU0R,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAIlZ,sBACR,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAImd,aAAanR;gBACjB,IAAIyF,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAMyL,SAAS,AACbjZ,QAAQ,oBACRiZ,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMkF,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAMpF,qBACzB,KAACzK;wBACCC,mBAAmB0P;wBACnBzP,gBAAgB,KAAO;wBACvB9H,yBAAyBA;wBACzB+H,4BAA4BA;wBAC5B1N,OAAOA;wBAETqd,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAAChR,aAC1B;wBACE7E,QAAQxM;wBACRmK,SAAS4R;wBACTjX;oBACF;oBAGF,wGAAwG;oBACxGiY,aAAathB,aAAamQ,SAASsW;gBACrC;gBAEA,OAAO;oBACL3K,iBAAiBkE;oBACjB7D,WAAWkE;oBACXrD,QAAQ,MAAM3c,wBAAwBihB,YAAY;wBAChDK,mBAAmBzd,gCACjBqc,kBAAkBsG,eAAe,IACjCxd,OACAmU;wBAEF+D;wBACApC;wBACA+C,yBACEhX,IAAIS,SAAS,CAACuW,uBAAuB,KAAK;wBAC5CvU,SAASzC,IAAIS,SAAS,CAACgC,OAAO;oBAChC;oBACAwF,eAAepO,qBACb4N,uBACAmS;oBAEF,0CAA0C;oBAC1C1R,qBAAqBL,0BAA0B7B,UAAU;oBACzDmC,iBAAiBN,0BAA0B5B,MAAM;oBACjDmC,gBAAgBf,gBAAgBQ,0BAA0B3B,KAAK;oBAC/DmC,eAAeR,0BAA0B1B,IAAI;oBAC7C5B,uBAAuB1I,4BAA4Bmf;gBACrD;YACF;QACF,OAAO,IAAI7T,aAAapJ,iBAAiB,EAAE;YACzC,uEAAuE;YACvE,IAAIgI,kBAAkBvM,2BAA2B4N;YAEjD,MAAM9C,2BAA2B1I;YACjC,MAAMggB,4BAA6CjT,iBAAiB;gBAClErJ,MAAM;gBACNqG,OAAO;gBACPtB;gBACAtF;gBACAoG;gBACAY;gBACAC,YAAYvK;gBACZwK,QAAQxK;gBACRyK,OAAOzK;gBACP0K,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;gBAC5B7B;YACF;YACA,MAAMZ,aAAa,MAAMtI,qBAAqBuI,GAAG,CAC/CiY,2BACA5S,eACA7I,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAErB,MAAM6V,oBAAqBuF,6BACzB,MAAM7f,2CACJK,qBAAqBuI,GAAG,CACtBiY,2BACAxW,aAAavB,sBAAsB,EACnC,4CAA4C;YAC5CH,YACAI,wBAAwBC,aAAa,EACrC;gBACEjH;gBACA0G,SAASyR;YACX;YAIN,MAAM4G,oBAAoC;gBACxCvc,MAAM;gBACNqG,OAAO;gBACPtB;gBACAtF;gBACAoG;gBACAY;gBACAC,YAAYvK;gBACZwK,QAAQxK;gBACRyK,OAAOzK;gBACP0K,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;gBAC5B7B;YACF;YACA,MAAMmC,YAAY,AAChBvJ,QAAQ,oBACRuJ,SAAS;YACX,MAAM,EAAExB,SAASgV,kBAAkB,EAAEvP,SAAS,EAAE,GAC9C,MAAMtP,qBAAqBuI,GAAG,CAC5BkY,mBACApV,yBACA,KAACiF;gBACCC,mBAAmB0J,kBAAkBgE,iBAAiB;gBACtDzN,gBAAgBA;gBAChB9H,yBAAyBA;gBACzB+H,4BAA4BA;gBAC5B1N,OAAOA;gBAET;gBACEqF,SAAS4R;gBACTuB,WAAW,CAACrZ;oBACVA,QAAQqN,OAAO,CAAC,CAAC+B,OAAOkK;wBACtBrB,aAAaqB,KAAKlK;oBACpB;gBACF;gBACAmK,kBAAkBjD;gBAClBkD,kBAAkB;oBAACjC;iBAAgB;YACrC;YAEJ,MAAMwB,wBAAwBpe,0BAA0B;gBACtDoc;gBACAL;gBACAsC,sBAAsBnB;gBACtB3B;gBACAU,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAMjU,aAAa,MAAM5K,eAAeggB,kBAAkB4F,QAAQ;YAElE,IAAIN,+BAA+Bla,YAAY;gBAC7C0D,SAASlE,UAAU,GAAGA;gBACtBkE,SAAS+W,WAAW,GAAG,MAAMC,mBAC3Blb,YACA4b,mBACAzW,cACAxE;YAEJ;YAEA,MAAM,EAAEqE,OAAO,EAAEoV,cAAc,EAAE,GAC/B,MAAMzf,eAAeqf;YAEvB;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAI7gB,oBAAoB2M,gBAAgB+V,eAAe,GAAG;gBACxD,IAAIpR,aAAa,MAAM;oBACrB,qBAAqB;oBACrBvG,SAASuG,SAAS,GAAG,MAAM9R,6BACzB8R,WACA2P,iBACI5hB,wBAAwB+d,KAAK,GAC7B/d,wBAAwB6hB,IAAI,EAChCvb,qBACAuF;gBAEJ,OAAO;oBACL,qBAAqB;oBACrBH,SAASuG,SAAS,GAAG,MAAM/R,6BACzB2L;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtD+Q,kBAAkBqB,OAAO;gBACzB,OAAO;oBACL9F,iBAAiBkE;oBACjB7D,WAAWkE;oBACXrD,QAAQ,MAAM5c,yBAAyB+P,SAAS;wBAC9CoR;wBACApC;oBACF;oBACAhM,eAAelC,gBAAgB+V,eAAe;oBAC9C,0CAA0C;oBAC1C5T,qBAAqB0T,0BAA0B5V,UAAU;oBACzDmC,iBAAiByT,0BAA0B3V,MAAM;oBACjDmC,gBAAgBf,gBAAgBuU,0BAA0B1V,KAAK;oBAC/DmC,eAAeuT,0BAA0BzV,IAAI;gBAC/C;YACF,OAAO,IAAIpH,uBAAuBA,oBAAoB8R,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/B1M,SAASuG,SAAS,GAAG,MAAM/R,6BACzB2L;gBAGF,OAAO;oBACLsM,iBAAiBkE;oBACjB7D,WAAWkE;oBACXrD,QAAQ,MAAM5c,yBAAyB+P,SAAS;wBAC9CoR;wBACApC;oBACF;oBACAhM,eAAelC,gBAAgB+V,eAAe;oBAC9C,0CAA0C;oBAC1C5T,qBAAqB0T,0BAA0B5V,UAAU;oBACzDmC,iBAAiByT,0BAA0B3V,MAAM;oBACjDmC,gBAAgBf,gBAAgBuU,0BAA0B1V,KAAK;oBAC/DmC,eAAeuT,0BAA0BzV,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAI1F,UAAU0R,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAIlZ,sBACR,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAImd,aAAanR;gBACjB,IAAIyF,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAMyL,SAAS,AACbjZ,QAAQ,oBACRiZ,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMkF,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAMpF,qBACzB,KAACzK;wBACCC,mBAAmB0P;wBACnBzP,gBAAgB,KAAO;wBACvB9H,yBAAyBA;wBACzB+H,4BAA4BA;wBAC5B1N,OAAOA;wBAETqd,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAAChR,aAC1B;wBACE7E,QAAQxM;wBACRmK,SAAS4R;wBACTjX;oBACF;oBAGF,wGAAwG;oBACxGiY,aAAathB,aAAamQ,SAASsW;gBACrC;gBAEA,OAAO;oBACL3K,iBAAiBkE;oBACjB7D,WAAWkE;oBACXrD,QAAQ,MAAM3c,wBAAwBihB,YAAY;wBAChDK,mBAAmBzd,gCACjBqc,kBAAkBsG,eAAe,IACjCxd,OACAmU;wBAEF+D;wBACApC;wBACA+C,yBACEhX,IAAIS,SAAS,CAACuW,uBAAuB,KAAK;wBAC5CvU,SAASzC,IAAIS,SAAS,CAACgC,OAAO;oBAChC;oBACAwF,eAAelC,gBAAgB+V,eAAe;oBAC9C,0CAA0C;oBAC1C5T,qBAAqB0T,0BAA0B5V,UAAU;oBACzDmC,iBAAiByT,0BAA0B3V,MAAM;oBACjDmC,gBAAgBf,gBAAgBuU,0BAA0B1V,KAAK;oBAC/DmC,eAAeuT,0BAA0BzV,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAM4V,uBAAwCpT,iBAAiB;gBAC7DrJ,MAAM;gBACNqG,OAAO;gBACPtB;gBACAc;gBACAa,YAAYvK;gBACZwK,QAAQxK;gBACRyK,OAAOzK;gBACP0K,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;YAC9B;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAMzC,aAAa,MAAMtI,qBAAqBuI,GAAG,CAC/CoY,sBACA/S,eACA7I,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAGrB,MAAM6V,oBAAqBuF,6BACzB,MAAM7f,2CACJK,qBAAqBuI,GAAG,CACtBoY,sBACA3W,aAAavB,sBAAsB,EACnCH,YACAI,wBAAwBC,aAAa,EACrC;gBACEjH;gBACA0G,SAASyR;YACX;YAIN,MAAMpR,yBAAyB,AAC7B3G,QAAQ,oBACR2G,sBAAsB;YACxB,MAAMuS,aAAa,MAAMhb,qBAAqBuI,GAAG,CAC/CoY,sBACAlY,sCACA,KAAC6H;gBACCC,mBAAmB0J,kBAAkBgE,iBAAiB;gBACtDzN,gBAAgBA;gBAChB9H,yBAAyBA;gBACzB+H,4BAA4BA;gBAC5B1N,OAAOA;gBAET;gBACEqF,SAAS4R;gBACTjX;gBACA2Y,kBAAkB;oBAACjC;iBAAgB;YACrC;YAGF,IAAI8F,+BAA+Bla,YAAY;gBAC7C,MAAMR,aAAa,MAAM5K,eAAeggB,kBAAkB4F,QAAQ;gBAClE9W,SAASlE,UAAU,GAAGA;gBACtBkE,SAAS+W,WAAW,GAAG,MAAMC,mBAC3Blb,YACA8b,sBACA3W,cACAxE;YAEJ;YAEA,MAAMyV,wBAAwBpe,0BAA0B;gBACtDoc;gBACAL;gBACAsC,sBAAsBnB;gBACtB3B;gBACAU,iBAAiBA;YACnB;YACA,OAAO;gBACLtD,iBAAiBkE;gBACjB7D,WAAWkE;gBACXrD,QAAQ,MAAM7c,mBAAmBmhB,YAAY;oBAC3CK,mBAAmBzd,gCACjBqc,kBAAkBsG,eAAe,IACjCxd,OACAmU;oBAEF3P,oBAAoB;oBACpBqU,yBACEhX,IAAIS,SAAS,CAACuW,uBAAuB,KAAK;oBAC5CvU,SAASzC,IAAIS,SAAS,CAACgC,OAAO;oBAC9B4T;oBACApC;gBACF;gBACA,0CAA0C;gBAC1C/L,qBAAqB6T,qBAAqB/V,UAAU;gBACpDmC,iBAAiB4T,qBAAqB9V,MAAM;gBAC5CmC,gBAAgBf,gBAAgB0U,qBAAqB7V,KAAK;gBAC1DmC,eAAe0T,qBAAqB5V,IAAI;YAC1C;QACF;IACF,EAAE,OAAO7C,KAAK;QACZ,IACEpK,wBAAwBoK,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIgI,OAAO,KAAK,YACvBhI,IAAIgI,OAAO,CAAC5B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMpG;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAIxK,qBAAqBwK,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM4T,qBAAqBxf,oBAAoB4L;QAC/C,IAAI4T,oBAAoB;YACtB,MAAMzL,QAAQtS,4BAA4BmK;YAC1C1L,MACE,GAAG0L,IAAI6T,MAAM,CAAC,mDAAmD,EAAErY,SAAS,kFAAkF,EAAE2M,OAAO;YAGzK,MAAMnI;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAIsX,+BAA+B,MAAM;YACvC,MAAMtX;QACR;QAEA,IAAI8F;QAEJ,IAAI9S,0BAA0BgN,MAAM;YAClC3B,IAAInC,UAAU,GAAGnJ,4BAA4BiN;YAC7Ca,SAAS3E,UAAU,GAAGmC,IAAInC,UAAU;YACpC4J,YAAYhT,mCAAmCuL,IAAInC,UAAU;QAC/D,OAAO,IAAI/I,gBAAgB6M,MAAM;YAC/B8F,YAAY;YACZzH,IAAInC,UAAU,GAAGhJ,+BAA+B8M;YAChDa,SAAS3E,UAAU,GAAGmC,IAAInC,UAAU;YAEpC,MAAM4X,cAAcpf,cAAczB,wBAAwB+M,MAAMkQ;YAEhEzO,UAAU,YAAYqS;QACxB,OAAO,IAAI,CAACF,oBAAoB;YAC9BvV,IAAInC,UAAU,GAAG;YACjB2E,SAAS3E,UAAU,GAAGmC,IAAInC,UAAU;QACtC;QAEA,MAAM,CAACgY,qBAAqBC,qBAAqB,GAAG1f,mBAClD0b,eACArJ,aACAsJ,aACAI,8BACAzb,oBAAoB2H,KAAK,QACzB7B,OACA;QAGF,MAAM4d,uBAAwCpT,iBAAiB;YAC7DrJ,MAAM;YACNqG,OAAO;YACPtB;YACAc,cAAcA;YACda,YACE,QAAO2C,kCAAAA,eAAgB3C,UAAU,MAAK,cAClC2C,eAAe3C,UAAU,GACzBvK;YACNwK,QACE,QAAO0C,kCAAAA,eAAgB1C,MAAM,MAAK,cAC9B0C,eAAe1C,MAAM,GACrBxK;YACNyK,OACE,QAAOyC,kCAAAA,eAAgBzC,KAAK,MAAK,cAC7ByC,eAAezC,KAAK,GACpBzK;YACN0K,MAAM;mBAAKwC,CAAAA,kCAAAA,eAAgBxC,IAAI,KAAIhB,aAAagB,IAAI;aAAE;QACxD;QACA,MAAMuR,kBAAkB,MAAMtc,qBAAqBuI,GAAG,CACpDoY,sBACAlR,oBACA1K,MACAH,KACA8U,0BAA0B6C,GAAG,CAAC,AAACrU,IAAYoD,MAAM,IAAItJ,YAAYkG,KACjE8F;QAGF,MAAMwO,oBAAoBxc,qBAAqBuI,GAAG,CAChDoY,sBACA3W,aAAavB,sBAAsB,EACnC6T,iBACA5T,wBAAwBC,aAAa,EACrC;YACEjH;YACA0G,SAASyR;QACX;QAGF,IAAI;YACF,6EAA6E;YAC7E,wFAAwF;YACxF,uCAAuC;YACvC,MAAM4C,aAAa,MAAMzc,qBAAqBuI,GAAG,CAC/CoY,sBACAhnB,2BACA;gBACE+iB,gBACE5a,QAAQ;gBACV6a,uBACE,KAAClL;oBACClB,mBAAmBiM;oBACnB/L,4BAA4BA;oBAC5BD,gBAAgB4L;oBAChB1T,yBAAyBA;oBACzB3F,OAAOA;;gBAGX6Z,eAAe;oBACb7Z;oBACA,wCAAwC;oBACxC2Y,kBAAkB;wBAACW;qBAAqB;oBACxCnF;gBACF;YACF;YAGF,IAAIqI,+BAA+Bla,YAAY;gBAC7C,MAAMR,aAAa,MAAM5K,eACvBulB,2BAA2BK,QAAQ;gBAErC9W,SAASlE,UAAU,GAAGA;gBACtBkE,SAAS+W,WAAW,GAAG,MAAMC,mBAC3Blb,YACA8b,sBACA3W,cACAxE;YAEJ;YAEA,oEAAoE;YACpE,gEAAgE;YAChE,MAAMob,eAAepB,2BAA2Be,eAAe;YAE/D,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9B/K,iBAAiBkE;gBACjB7D,WAAWkE;gBACXrD,QAAQ,MAAM7c,mBAAmB4iB,YAAY;oBAC3CpB,mBAAmBzd,gCACjBgjB,cACA7d,OACAmU;oBAEF3P,oBAAoB;oBACpBqU,yBACEhX,IAAIS,SAAS,CAACuW,uBAAuB,KAAK;oBAC5CvU,SAASzC,IAAIS,SAAS,CAACgC,OAAO;oBAC9B4T,uBAAuBpe,0BAA0B;wBAC/Coc;wBACAL;wBACAsC,sBAAsB,EAAE;wBACxB9C;wBACAU,iBAAiBA;oBACnB;oBACAD;oBACAgD,oBAAoBxT;gBACtB;gBACAwE,eAAe;gBACfC,qBACES,mBAAmB,OAAOA,eAAe3C,UAAU,GAAGvK;gBACxD0M,iBACEQ,mBAAmB,OAAOA,eAAe1C,MAAM,GAAGxK;gBACpD2M,gBAAgBf,gBACdsB,mBAAmB,OAAOA,eAAezC,KAAK,GAAGzK;gBAEnD4M,eAAeM,mBAAmB,OAAOA,eAAexC,IAAI,GAAG;YACjE;QACF,EAAE,OAAO8R,UAAe;YACtB,IACElb,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB3G,0BAA0B2hB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1Bhb,QAAQ;gBACVgb;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAMlO,uBAAuB,OAC3B5J,MACAH;IAKA,MAAM,EACJic,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAGvgB,gBAAgBwE;IAEpB,MAAMgc,uBACJnc,IAAIE,YAAY,CAAC0J,WAAW;IAC9B,IAAIE;IACJ,IAAIoS,mBAAmB;QACrB,MAAM,GAAGrS,OAAO,GAAG,MAAMnO,gCAAgC;YACvDsE;YACAoc,UAAUF,iBAAiB,CAAC,EAAE;YAC9BG,cAAcH,iBAAiB,CAAC,EAAE;YAClCta,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAiI,oBAAoBD;IACtB;IACA,IAAI7J,IAAIY,UAAU,CAAC6C,GAAG,EAAE;QACtB,MAAM6Y,MACJ,AAACvf,CAAAA,QAAQC,GAAG,CAACyR,YAAY,KAAK,SAC1B1R,QAAQC,GAAG,CAACuf,uBAAuB,GACnCvc,IAAIY,UAAU,CAAC0b,GAAG,AAAD,KAAM;QAE7B,MAAME,wBAAwBlgB,4BAC5BggB,KACAJ,qCAAAA,iBAAmB,CAAC,EAAE;QAExB,IAAIlc,IAAIY,UAAU,CAAC6b,sBAAsB,IAAID,uBAAuB;YAClE,MAAME,kBAAkB1c,IAAIE,YAAY,CAACwc,eAAe;YACxD5S,oBACE,2EAA2E;YAC3E,iEAAiE;0BACjE,KAAC4S;gBAECpd,MAAK;gBACLR,UAAU0d;0BAET1S;eAJG;QAOV;IACF;IAEA,OAAO;QACLF,aAAauS;QACbtS,QAAQC;IACV;AACF;AAEA,SAASxC,sBAAsBH,YAAgC;IAC7D,OAAO,CAACjB;YAECiB;eADPjB,UAAUzK,kBACV,SAAO0L,2BAAAA,aAAawV,UAAU,qBAAvBxV,yBAAyByV,MAAM,MAAK,WACvCzV,aAAawV,UAAU,CAACC,MAAM,GAC9B1W;;AACR;AAEA,eAAeiV,mBACb0B,kBAA0B,EAC1BlU,cAA8B,EAC9BvD,YAA2B,EAC3BxE,UAAsB;IAEtB,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAMkD,0BAA0BlD,WAAWkD,uBAAuB;IAClE,IACE,CAACA,2BACD,yEAAyE;IACzE,mBAAmB;IACnB,EAAE;IACF,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,mCAAmC;IACnClD,WAAWuG,YAAY,CAAC2V,kBAAkB,KAAK,MAC/C;QACA;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,gBAAgBhgB,QAAQC,GAAG,CAACyR,YAAY,KAAK;IACnD,MAAMuO,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWH,gBACPjZ,wBAAwBqZ,oBAAoB,GAC5CrZ,wBAAwBsZ,gBAAgB;QAC5C3N,iBAAiBnX;IACnB;IAEA,MAAM+kB,YAAY1U,eAAezC,KAAK;IACtC,OAAO,MAAMd,aAAa+V,kBAAkB,CAC1Cva,WAAWuG,YAAY,CAACmW,kBAAkB,EAC1CT,oBACAQ,WACAvZ,wBAAwBC,aAAa,EACrCiZ;AAEJ", "ignoreList": [0]}