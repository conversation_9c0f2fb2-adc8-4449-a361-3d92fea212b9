{"version": 3, "sources": ["../../../../../src/server/route-modules/pages/builtin/_error.tsx"], "sourcesContent": ["import App from '../../../../pages/_app'\nimport Document from '../../../../pages/_document'\nimport { RouteKind } from '../../../route-kind'\n\nimport * as moduleError from '../../../../pages/_error'\n\nimport PagesRouteModule from '../module'\nimport { getHandler } from '../pages-handler'\n\nexport const routeModule = new PagesRouteModule({\n  // TODO: add descriptor for internal error page\n  definition: {\n    kind: RouteKind.PAGES,\n    page: '/_error',\n    pathname: '/_error',\n    filename: '',\n    bundlePath: '',\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n  components: {\n    App,\n    Document,\n  },\n  userland: moduleError,\n})\n\nexport const handler = getHandler({\n  srcPage: '/_error',\n  routeModule,\n  userland: moduleError,\n  config: {},\n  isFallbackError: true,\n})\n"], "names": ["handler", "routeModule", "PagesRouteModule", "definition", "kind", "RouteKind", "PAGES", "page", "pathname", "filename", "bundlePath", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "relativeProjectDir", "__NEXT_RELATIVE_PROJECT_DIR", "components", "App", "Document", "userland", "moduleError", "<PERSON><PERSON><PERSON><PERSON>", "srcPage", "config", "isFallbackError"], "mappings": ";;;;;;;;;;;;;;;IA2BaA,OAAO;eAAPA;;IAlBAC,WAAW;eAAXA;;;4DATG;iEACK;2BACK;+DAEG;+DAEA;8BACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB,MAAMA,cAAc,IAAIC,eAAgB,CAAC;IAC9C,+CAA+C;IAC/CC,YAAY;QACVC,MAAMC,oBAAS,CAACC,KAAK;QACrBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,oBAAoBH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;IAC/DC,YAAY;QACVC,KAAAA,YAAG;QACHC,UAAAA,iBAAQ;IACV;IACAC,UAAUC;AACZ;AAEO,MAAMrB,UAAUsB,IAAAA,wBAAU,EAAC;IAChCC,SAAS;IACTtB;IACAmB,UAAUC;IACVG,QAAQ,CAAC;IACTC,iBAAiB;AACnB", "ignoreList": [0]}