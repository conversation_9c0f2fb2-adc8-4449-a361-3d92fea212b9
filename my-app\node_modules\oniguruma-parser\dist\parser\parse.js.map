{"version": 3, "sources": ["../../src/parser/parse.ts"], "sourcesContent": ["import type {AssertionToken, BackreferenceToken, CharacterClassHyphenToken, CharacterClassOpenToken, CharacterSetToken, FlagGroupModifiers, FlagProperties, GroupOpenToken, QuantifierToken, SubroutineToken, Token, TokenCharacterSetKind, TokenDirectiveKind, TokenNamedCalloutKind, TokenQuantifierKind} from '../tokenizer/tokenize.js';\nimport {tokenize} from '../tokenizer/tokenize.js';\nimport {cpOf, getOrInsert, PosixClassNames, r, throwIfNullish} from '../utils.js';\nimport {hasOnlyChild, isAlternativeContainer, isQuantifiable} from './node-utils.js';\n\n// Watch out for the DOM `Node` interface!\ntype Node =\n  AbsenceFunctionNode |\n  AlternativeNode |\n  AssertionNode |\n  BackreferenceNode |\n  CapturingGroupNode |\n  CharacterNode |\n  CharacterClassNode |\n  CharacterClassRangeNode |\n  CharacterSetNode |\n  DirectiveNode |\n  FlagsNode |\n  GroupNode |\n  LookaroundAssertionNode |\n  NamedCalloutNode |\n  QuantifierNode |\n  RegexNode |\n  SubroutineNode;\n\ntype OnigurumaAst = RegexNode;\n\ntype ParentNode =\n  AlternativeContainerNode |\n  AlternativeNode |\n  CharacterClassNode |\n  CharacterClassRangeNode |\n  QuantifierNode;\n\n// See also `isAlternativeContainer`\ntype AlternativeContainerNode =\n  AbsenceFunctionNode | // Some sub-kinds aren't alternative containers\n  CapturingGroupNode |\n  GroupNode |\n  LookaroundAssertionNode |\n  RegexNode;\n\ntype AlternativeElementNode =\n  AbsenceFunctionNode |\n  AssertionNode |\n  BackreferenceNode |\n  CapturingGroupNode |\n  CharacterNode |\n  CharacterClassNode |\n  CharacterSetNode |\n  DirectiveNode |\n  GroupNode |\n  LookaroundAssertionNode |\n  NamedCalloutNode |\n  QuantifierNode |\n  SubroutineNode;\n\ntype CharacterClassElementNode =\n  CharacterNode |\n  CharacterClassNode |\n  CharacterClassRangeNode |\n  CharacterSetNode;\n\n// See also `isQuantifiable`\ntype QuantifiableNode =\n  AbsenceFunctionNode |\n  BackreferenceNode |\n  CapturingGroupNode |\n  CharacterNode |\n  CharacterClassNode |\n  CharacterSetNode |\n  GroupNode |\n  QuantifierNode |\n  SubroutineNode;\n\n// TODO: Support remaining kinds; see <github.com/slevithan/oniguruma-to-es/issues/13>\ntype NodeAbsenceFunctionKind =\n  'repeater';\n\ntype NodeAssertionKind =\n  'line_end' |\n  'line_start' |\n  'search_start' |\n  'string_end' |\n  'string_end_newline' |\n  'string_start' |\n  'text_segment_boundary' |\n  'word_boundary';\n\ntype NodeCharacterClassKind =\n  'union' |\n  'intersection';\n\ntype NodeCharacterSetKind = TokenCharacterSetKind;\n\ntype NodeDirectiveKind = TokenDirectiveKind;\n\ntype NodeLookaroundAssertionKind =\n  'lookahead' |\n  'lookbehind';\n\ntype NodeNamedCalloutKind = TokenNamedCalloutKind;\n\ntype NodeQuantifierKind = TokenQuantifierKind;\n\ntype UnicodePropertyMap = Map<string, string>;\n\ntype Context = {\n  capturingGroups: Array<CapturingGroupNode>;\n  hasNumberedRef: boolean;\n  namedGroupsByName: Map<string, Array<CapturingGroupNode>>;\n  nextIndex: number;\n  normalizeUnknownPropertyNames: boolean;\n  parent: ParentNode;\n  skipBackrefValidation: boolean;\n  skipLookbehindValidation: boolean;\n  skipPropertyNameValidation: boolean;\n  subroutines: Array<SubroutineNode>;\n  tokens: Array<Token>;\n  unicodePropertyMap: UnicodePropertyMap | null;\n  walk: (parent: ParentNode, state: State) => Node;\n};\n\n// Top-level `walk` calls are given empty state; nested calls can add data specific to their `walk`\ntype State = {\n  isCheckingRangeEnd?: boolean;\n  isInAbsenceFunction?: boolean;\n  isInLookbehind?: boolean;\n  isInNegLookbehind?: boolean;\n};\n\ntype ParseOptions = {\n  flags?: string;\n  normalizeUnknownPropertyNames?: boolean;\n  rules?: {\n    captureGroup?: boolean;\n    singleline?: boolean;\n  };\n  skipBackrefValidation?: boolean;\n  skipLookbehindValidation?: boolean;\n  skipPropertyNameValidation?: boolean;\n  unicodePropertyMap?: UnicodePropertyMap | null;\n};\n\nfunction parse(pattern: string, options: ParseOptions = {}): OnigurumaAst {\n  const opts: Required<ParseOptions> = {\n    flags: '',\n    normalizeUnknownPropertyNames: false,\n    skipBackrefValidation: false,\n    skipLookbehindValidation: false,\n    skipPropertyNameValidation: false,\n    // `toOnigurumaAst` provides `OnigUnicodePropertyMap`, but it can be custom or `null`\n    unicodePropertyMap: null,\n    ...options,\n    rules: {\n      captureGroup: false, // `ONIG_OPTION_CAPTURE_GROUP`\n      singleline: false, // `ONIG_OPTION_SINGLELINE`\n      ...options.rules,\n    },\n  };\n  const tokenized = tokenize(pattern, {\n    // Limit to the tokenizer's options\n    flags: opts.flags,\n    rules: {\n      captureGroup: opts.rules.captureGroup,\n      singleline: opts.rules.singleline,\n    },\n  });\n  const walk: Context['walk'] = (parent, state) => {\n    const token = tokenized.tokens[context.nextIndex];\n    context.parent = parent;\n    // Advance for the next iteration\n    context.nextIndex++;\n    switch (token.type) {\n      case 'Alternator':\n        // Top-level only; groups handle their own alternators\n        return createAlternative();\n      case 'Assertion':\n        return parseAssertion(token);\n      case 'Backreference':\n        return parseBackreference(token, context);\n      case 'Character':\n        return createCharacter(token.value, {useLastValid: !!state.isCheckingRangeEnd});\n      case 'CharacterClassHyphen':\n        return parseCharacterClassHyphen(token, context, state);\n      case 'CharacterClassOpen':\n        return parseCharacterClassOpen(token, context, state);\n      case 'CharacterSet':\n        return parseCharacterSet(token, context);\n      case 'Directive':\n        return createDirective(token.kind, {flags: token.flags});\n      case 'GroupOpen':\n        return parseGroupOpen(token, context, state);\n      case 'NamedCallout':\n        return createNamedCallout(token.kind, token.tag, token.arguments);\n      case 'Quantifier':\n        return parseQuantifier(token, context);\n      case 'Subroutine':\n        return parseSubroutine(token, context);\n      default:\n        throw new Error(`Unexpected token type \"${token.type}\"`);\n    }\n  }\n  const context: Context = {\n    capturingGroups: [],\n    hasNumberedRef: false,\n    namedGroupsByName: new Map(),\n    nextIndex: 0,\n    normalizeUnknownPropertyNames: opts.normalizeUnknownPropertyNames,\n    parent: null!, // Assigned by `walk`\n    skipBackrefValidation: opts.skipBackrefValidation,\n    skipLookbehindValidation: opts.skipLookbehindValidation,\n    skipPropertyNameValidation: opts.skipPropertyNameValidation,\n    subroutines: [],\n    tokens: tokenized.tokens,\n    unicodePropertyMap: opts.unicodePropertyMap,\n    walk,\n  };\n\n  // ## AST construction from tokens\n  const ast = createRegex(createFlags(tokenized.flags));\n  let top = ast.body[0]; // First alt\n  while (context.nextIndex < tokenized.tokens.length) {\n    const node = walk(top, {});\n    if (node.type === 'Alternative') {\n      ast.body.push(node);\n      top = node;\n    } else {\n      top.body.push(node as AlternativeElementNode);\n    }\n  }\n\n  // ## Validation that requires knowledge about the complete pattern\n  // `context` updated by the preceding `walk` loop\n  const {capturingGroups, hasNumberedRef, namedGroupsByName, subroutines} = context;\n  if (hasNumberedRef && namedGroupsByName.size && !opts.rules.captureGroup) {\n    throw new Error('Numbered backref/subroutine not allowed when using named capture');\n  }\n  for (const {ref} of subroutines) {\n    if (typeof ref === 'number') {\n      // Relative nums are already resolved\n      if (ref > capturingGroups.length) {\n        throw new Error(`Subroutine uses a group number that's not defined`);\n      }\n      if (ref) {\n        capturingGroups[ref - 1].isSubroutined = true;\n      }\n    } else if (!namedGroupsByName.has(ref)) {\n      throw new Error(r`Subroutine uses a group name that's not defined \"\\g<${ref}>\"`);\n    } else if (namedGroupsByName.get(ref)!.length > 1) {\n      throw new Error(r`Subroutine uses a duplicate group name \"\\g<${ref}>\"`);\n    } else {\n      namedGroupsByName.get(ref)![0].isSubroutined = true;\n    }\n  }\n\n  return ast;\n}\n\nfunction parseAssertion({kind}: AssertionToken): AssertionNode {\n  return createAssertion(\n    throwIfNullish({\n      '^': 'line_start',\n      '$': 'line_end',\n      '\\\\A': 'string_start',\n      '\\\\b': 'word_boundary',\n      '\\\\B': 'word_boundary',\n      '\\\\G': 'search_start',\n      '\\\\y': 'text_segment_boundary',\n      '\\\\Y': 'text_segment_boundary',\n      '\\\\z': 'string_end',\n      '\\\\Z': 'string_end_newline',\n    }[kind], `Unexpected assertion kind \"${kind}\"`) as NodeAssertionKind,\n    {negate: kind === r`\\B` || kind === r`\\Y`}\n  );\n}\n\n// Supported (if the backref appears to the right of the reffed capture's opening paren):\n// - `\\k<name>`, `\\k'name'`\n// - When named capture not used:\n//   - `\\n`, `\\nn`, `\\nnn`\n//   - `\\k<n>`, `\\k'n'\n//   - `\\k<-n>`, `\\k'-n'`\n// Unsupported:\n// - `\\k<+n>`, `\\k'+n'` - Note that, Unlike Oniguruma, Onigmo doesn't support this as special\n//   syntax and therefore considers it a valid group name.\n// - Backref with recursion level (with num or name): `\\k<n+level>`, `\\k<n-level>`, etc.\n//   (Onigmo also supports `\\k<-n+level>`, `\\k<-n-level>`, etc.)\n// Backrefs in Onig use multiplexing for duplicate group names (the rules can be complicated when\n// overlapping with subroutines), but a `Backreference`'s simple `ref` prop doesn't capture these\n// details so multiplexed ref pointers need to be derived when working with the AST\nfunction parseBackreference({raw}: BackreferenceToken, context: Context): BackreferenceNode {\n  const hasKWrapper = /^\\\\k[<']/.test(raw);\n  const ref = hasKWrapper ? raw.slice(3, -1) : raw.slice(1);\n  const fromNum = (num: number, isRelative = false) => {\n    const numCapturesToLeft = context.capturingGroups.length;\n    let orphan = false;\n    // Note: It's not an error for numbered backrefs to come before their referenced group in Onig,\n    // but it's currently an error in this library.\n    // - Most such placements are mistakes and can never match, due to Onig's behavior for backrefs\n    //   to nonparticipating groups.\n    //   - The edge cases where they're matchable rely on rules for backref resetting within\n    //     quantified groups that are different in JS (thus not emulatable in `oniguruma-to-es`).\n    // - Erroring matches the correct behavior of named backrefs.\n    // - For unenclosed backrefs, this only affects `\\1`-`\\9` since it's not a backref in the first\n    //   place if using `\\10` or higher and not as many capturing groups are defined to the left\n    //   (it's an octal or identity escape).\n    // TODO: Ideally this would be refactored to include the backref in the AST when it's not an\n    // error in Onig (due to the reffed group being defined to the right), and the error handling\n    // would move to the `oniguruma-to-es` transformer\n    if (num > numCapturesToLeft) {\n      // Skipping the error breaks assumptions and might create edge case issues, since backrefs\n      // are required to come after their captures; unfortunately this option is needed for\n      // TextMate grammars\n      if (context.skipBackrefValidation) {\n        orphan = true;\n      } else {\n        throw new Error(`Not enough capturing groups defined to the left \"${raw}\"`);\n      }\n    }\n    context.hasNumberedRef = true;\n    return createBackreference(isRelative ? numCapturesToLeft + 1 - num : num, {orphan});\n  };\n  if (hasKWrapper) {\n    const numberedRef = /^(?<sign>-?)0*(?<num>[1-9]\\d*)$/.exec(ref);\n    if (numberedRef) {\n      return fromNum(+numberedRef.groups!.num, !!numberedRef.groups!.sign);\n    }\n    // Invalid in a backref name even when valid in a group name\n    if (/[-+]/.test(ref)) {\n      throw new Error(`Invalid backref name \"${raw}\"`);\n    }\n    if (!context.namedGroupsByName.has(ref)) {\n      throw new Error(`Group name not defined to the left \"${raw}\"`);\n    }\n    return createBackreference(ref);\n  }\n  return fromNum(+ref);\n}\n\nfunction parseCharacterClassHyphen(_: CharacterClassHyphenToken, context: Context, state: State): CharacterNode | CharacterClassRangeNode {\n  const {tokens, walk} = context;\n  const parent = context.parent as CharacterClassNode;\n  const prevSiblingNode = parent.body.at(-1);\n  const nextToken = tokens[context.nextIndex];\n  if (\n    !state.isCheckingRangeEnd &&\n    prevSiblingNode &&\n    prevSiblingNode.type !== 'CharacterClass' &&\n    prevSiblingNode.type !== 'CharacterClassRange' &&\n    nextToken &&\n    nextToken.type !== 'CharacterClassOpen' &&\n    nextToken.type !== 'CharacterClassClose' &&\n    nextToken.type !== 'CharacterClassIntersector'\n  ) {\n    const nextNode = walk(parent, {\n      ...state,\n      isCheckingRangeEnd: true,\n    });\n    if (prevSiblingNode.type === 'Character' && nextNode.type === 'Character') {\n      parent.body.pop();\n      return createCharacterClassRange(prevSiblingNode, nextNode);\n    }\n    throw new Error('Invalid character class range');\n  }\n  return createCharacter(cpOf('-'));\n}\n\nfunction parseCharacterClassOpen({negate}: CharacterClassOpenToken, context: Context, state: State): CharacterClassNode {\n  const {tokens, walk} = context;\n  const firstClassToken = tokens[context.nextIndex];\n  const intersections = [createCharacterClass()];\n  let nextToken = throwIfUnclosedCharacterClass(firstClassToken);\n  while (nextToken.type !== 'CharacterClassClose') {\n    if (nextToken.type === 'CharacterClassIntersector') {\n      intersections.push(createCharacterClass());\n      // Skip the intersector\n      context.nextIndex++;\n    } else {\n      const cc = intersections.at(-1)!; // Always at least one\n      cc.body.push(walk(cc, state) as CharacterClassElementNode);\n    }\n    nextToken = throwIfUnclosedCharacterClass(tokens[context.nextIndex], firstClassToken);\n  }\n  const node = createCharacterClass({negate});\n  if (intersections.length === 1) {\n    node.body = intersections[0].body;\n  } else {\n    node.kind = 'intersection';\n    node.body = intersections.map(cc => cc.body.length === 1 ? cc.body[0] : cc);\n  }\n  // Skip the closing square bracket\n  context.nextIndex++;\n  return node;\n}\n\nfunction parseCharacterSet({kind, negate, value}: CharacterSetToken, context: Context): CharacterSetNode {\n  const {normalizeUnknownPropertyNames, skipPropertyNameValidation, unicodePropertyMap} = context;\n  if (kind === 'property') {\n    const normalized = slug(value!);\n    // Don't treat as POSIX if it's in the provided list of Unicode property names\n    if (PosixClassNames.has(normalized) && !unicodePropertyMap?.has(normalized)) {\n      kind = 'posix';\n      value = normalized;\n    } else {\n      return createUnicodeProperty(value!, {\n        negate,\n        normalizeUnknownPropertyNames,\n        skipPropertyNameValidation,\n        unicodePropertyMap,\n      });\n    }\n  }\n  if (kind === 'posix') {\n    return createPosixClass(value!, {negate});\n  }\n  return createCharacterSet(kind, {negate});\n}\n\nfunction parseGroupOpen(token: GroupOpenToken, context: Context, state: State): AbsenceFunctionNode | CapturingGroupNode | GroupNode | LookaroundAssertionNode {\n  const {tokens, capturingGroups, namedGroupsByName, skipLookbehindValidation, walk} = context;\n  const node = createByGroupKind(token);\n  const isThisAbsenceFunction = node.type === 'AbsenceFunction';\n  const isThisLookbehind = isLookbehind(node);\n  const isThisNegLookbehind = isThisLookbehind && node.negate;\n  // Track capturing group details for backrefs and subroutines (before parsing the group's\n  // contents so nested groups with the same name are tracked in order)\n  if (node.type === 'CapturingGroup') {\n    capturingGroups.push(node);\n    if (node.name) {\n      getOrInsert(namedGroupsByName, node.name, []).push(node);\n    }\n  }\n  // Don't allow nested absence functions\n  if (isThisAbsenceFunction && state.isInAbsenceFunction) {\n    // Is officially unsupported in Onig but doesn't throw, gives strange results\n    throw new Error('Nested absence function not supported by Oniguruma');\n  }\n  let nextToken = throwIfUnclosedGroup(tokens[context.nextIndex]);\n  while (nextToken.type !== 'GroupClose') {\n    if (nextToken.type === 'Alternator') {\n      node.body.push(createAlternative());\n      // Skip the alternator\n      context.nextIndex++;\n    } else {\n      const alt = node.body.at(-1)!; // Always at least one\n      const child = walk(alt, {\n        ...state,\n        isInAbsenceFunction: state.isInAbsenceFunction || isThisAbsenceFunction,\n        isInLookbehind: state.isInLookbehind || isThisLookbehind,\n        isInNegLookbehind: state.isInNegLookbehind || isThisNegLookbehind,\n      }) as AlternativeElementNode;\n      alt.body.push(child);\n      // Centralized validation of lookbehind contents\n      if ((isThisLookbehind || state.isInLookbehind) && !skipLookbehindValidation) {\n        // JS supports all features within lookbehind, but Onig doesn't. Absence functions of form\n        // `(?~|)` and `(?~|…)` are also invalid in lookbehind (the `(?~…)` and `(?~|…|…)` forms\n        // are allowed), but all forms with `(?~|` throw since they aren't yet supported\n        const msg = 'Lookbehind includes a pattern not allowed by Oniguruma';\n        if (isThisNegLookbehind || state.isInNegLookbehind) {\n          // - Invalid: `(?=…)`, `(?!…)`, capturing groups\n          // - Valid: `(?<=…)`, `(?<!…)`\n          if (isLookahead(child) || child.type === 'CapturingGroup') {\n            throw new Error(msg);\n          }\n        } else {\n          // - Invalid: `(?=…)`, `(?!…)`, `(?<!…)`\n          // - Valid: `(?<=…)`, capturing groups\n          if (isLookahead(child) || (isLookbehind(child) && child.negate)) {\n            throw new Error(msg);\n          }\n        }\n      }\n    }\n    nextToken = throwIfUnclosedGroup(tokens[context.nextIndex]);\n  }\n  // Skip the closing parenthesis\n  context.nextIndex++;\n  return node;\n}\n\nfunction parseQuantifier({kind, min, max}: QuantifierToken, context: Context): QuantifierNode {\n  const parent = context.parent as AlternativeNode;\n  const quantifiedNode = parent.body.at(-1);\n  if (!quantifiedNode || !isQuantifiable(quantifiedNode)) {\n    throw new Error('Quantifier requires a repeatable token');\n  }\n  const node = createQuantifier(kind, min, max, quantifiedNode);\n  parent.body.pop();\n  return node;\n}\n\n// Onig subroutine behavior:\n// - Subroutines can appear before the groups they reference; ex: `\\g<1>(a)` is valid.\n// - Multiple subroutines can reference the same group.\n// - Subroutines can reference groups that themselves contain subroutines, followed to any depth.\n// - Subroutines can be used recursively, and `\\g<0>` recursively references the whole pattern.\n// - Subroutines can use relative references (backward or forward); ex: `\\g<+1>(.)\\g<-1>`.\n// - Subroutines don't get their own capturing group numbers; ex: `(.)\\g<1>\\2` is invalid.\n// - Subroutines use the flags that apply to their referenced group, so e.g.\n//   `(?-i)(?<a>a)(?i)\\g<a>` is fully case sensitive.\n// - Differences from PCRE/Perl/Regex+ subroutines:\n//   - Subroutines can't reference duplicate group names (though duplicate names are valid if no\n//     subroutines reference them).\n//   - Subroutines can't use absolute or relative numbers if named capture is used anywhere.\n//   - Named backrefs must be to the right of their group definition, so the backref in\n//     `\\g<a>\\k<a>(?<a>)` is invalid (not directly related to subroutines).\n//   - Subroutines don't restore capturing group match values (for backrefs) upon exit, so e.g.\n//     `(?<a>(?<b>[ab]))\\g<a>\\k<b>` matches `abb` but not `aba`; same for numbered.\n// The interaction of backref multiplexing (an Onig-specific feature) and subroutines is complex:\n// - Only the most recent value matched by a capturing group and its subroutines is considered for\n//   backref multiplexing, and this also applies to capturing groups nested within a group that's\n//   referenced by a subroutine.\n// - Although a subroutine can't reference a group with a duplicate name, it can reference a group\n//   with a nested capture whose name is duplicated (e.g. outside of the referenced group).\n//   - These duplicate names can then multiplex; but only the most recent value matched from within\n//     the outer group (or the subroutines that reference it) is available for multiplexing.\n//   - Ex: With `(?<a>(?<b>[123]))\\g<a>\\g<a>(?<b>0)\\k<b>`, the backref `\\k<b>` can only match `0`\n//     or whatever was matched by the most recently matched subroutine. If you took out `(?<b>0)`,\n//     no multiplexing would occur.\nfunction parseSubroutine({raw}: SubroutineToken, context: Context): SubroutineNode {\n  const {capturingGroups, subroutines} = context;\n  let ref: string | number = raw.slice(3, -1);\n  const numberedRef = /^(?<sign>[-+]?)0*(?<num>[1-9]\\d*)$/.exec(ref);\n  if (numberedRef) {\n    const num = +numberedRef.groups!.num;\n    const numCapturesToLeft = capturingGroups.length;\n    context.hasNumberedRef = true;\n    ref = {\n      '': num,\n      '+': numCapturesToLeft + num,\n      '-': numCapturesToLeft + 1 - num,\n    }[numberedRef.groups!.sign]!;\n    if (ref < 1) {\n      throw new Error('Invalid subroutine number');\n    }\n  // Special case for full-pattern recursion; can't be `+0`, `-0`, `00`, etc.\n  } else if (ref === '0') {\n    ref = 0;\n  }\n  const node = createSubroutine(ref);\n  subroutines.push(node);\n  return node;\n}\n\n// -------------------------------\n// --- Node creation and types ---\n// -------------------------------\n\ntype AbsenceFunctionNode = {\n  type: 'AbsenceFunction';\n  kind: NodeAbsenceFunctionKind;\n  body: Array<AlternativeNode>;\n};\nfunction createAbsenceFunction(kind: NodeAbsenceFunctionKind, options?: {\n  body?: Array<AlternativeNode>;\n}): AbsenceFunctionNode {\n  if (kind !== 'repeater') {\n    throw new Error(`Unexpected absence function kind \"${kind}\"`);\n  }\n  return {\n    type: 'AbsenceFunction',\n    kind,\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype AlternativeNode = {\n  type: 'Alternative';\n  body: Array<AlternativeElementNode>;\n};\nfunction createAlternative(options?: {\n  body?: Array<AlternativeElementNode>;\n}): AlternativeNode {\n  return {\n    type: 'Alternative',\n    body: getBodyForElementContainer(options?.body) as Array<AlternativeElementNode>,\n  };\n}\n\ntype AssertionNode = {\n  type: 'Assertion';\n  kind: NodeAssertionKind;\n  negate?: boolean;\n};\nfunction createAssertion(kind: NodeAssertionKind, options?: {\n  negate?: boolean;\n}): AssertionNode {\n  const node: AssertionNode = {\n    type: 'Assertion',\n    kind,\n  };\n  if (kind === 'word_boundary' || kind === 'text_segment_boundary') {\n    node.negate = !!options?.negate;\n  }\n  return node;\n}\n\ntype BackreferenceNode = {\n  type: 'Backreference';\n  ref: string | number;\n  orphan?: boolean;\n};\nfunction createBackreference(ref: string | number, options?: {\n  orphan?: boolean;\n}): BackreferenceNode {\n  const orphan = !!options?.orphan;\n  return {\n    type: 'Backreference',\n    ref,\n    ...(orphan && {orphan}),\n  };\n}\n\ntype CapturingGroupNode = {\n  type: 'CapturingGroup';\n  kind?: never;\n  number: number;\n  name?: string;\n  // One or more subroutines in the regex reference this group\n  isSubroutined?: boolean;\n  body: Array<AlternativeNode>;\n};\nfunction createCapturingGroup(number: number, options?: {\n  name?: string;\n  isSubroutined?: boolean;\n  body?: Array<AlternativeNode>;\n}): CapturingGroupNode {\n  const opts = {\n    name: undefined,\n    isSubroutined: false,\n    ...options,\n  };\n  if (opts.name !== undefined && !isValidGroupName(opts.name)) {\n    throw new Error(`Group name \"${opts.name}\" invalid in Oniguruma`);\n  }\n  return {\n    type: 'CapturingGroup',\n    number,\n    ...(opts.name && {name: opts.name}),\n    ...(opts.isSubroutined && {isSubroutined: opts.isSubroutined}),\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype CharacterNode = {\n  type: 'Character';\n  value: number;\n};\nfunction createCharacter(charCode: number, options?: {\n  useLastValid?: boolean;\n}): CharacterNode {\n  const opts = {\n    useLastValid: false,\n    ...options,\n  };\n  if (charCode > 0x10FFFF) {\n    const hex = charCode.toString(16);\n    if (opts.useLastValid) {\n      charCode = 0x10FFFF;\n    } else if (charCode > 0x13FFFF) {\n      throw new Error(`Invalid code point out of range \"\\\\x{${hex}}\"`);\n    } else {\n      throw new Error(`Invalid code point out of range in JS \"\\\\x{${hex}}\"`);\n    }\n  }\n  return {\n    type: 'Character',\n    value: charCode,\n  };\n}\n\ntype CharacterClassNode = {\n  type: 'CharacterClass';\n  kind: NodeCharacterClassKind;\n  negate: boolean;\n  body: Array<CharacterClassElementNode>;\n};\nfunction createCharacterClass(options?: {\n  kind?: NodeCharacterClassKind;\n  negate?: boolean;\n  body?: Array<CharacterClassElementNode>;\n}): CharacterClassNode {\n  const opts = {\n    kind: 'union' as NodeCharacterClassKind,\n    negate: false,\n    ...options,\n  };\n  return {\n    type: 'CharacterClass',\n    kind: opts.kind,\n    negate: opts.negate,\n    body: getBodyForElementContainer(options?.body) as Array<CharacterClassElementNode>,\n  };\n}\n\ntype CharacterClassRangeNode = {\n  type: 'CharacterClassRange';\n  min: CharacterNode;\n  max: CharacterNode;\n};\nfunction createCharacterClassRange(min: CharacterNode, max: CharacterNode): CharacterClassRangeNode {\n  if (max.value < min.value) {\n    throw new Error('Character class range out of order');\n  }\n  return {\n    type: 'CharacterClassRange',\n    min,\n    max,\n  };\n}\n\ntype NamedCharacterSetNode = {\n  type: 'CharacterSet';\n  kind: 'posix' | 'property';\n  value: string;\n  negate: boolean;\n  variableLength?: never;\n};\ntype UnnamedCharacterSetNode = {\n  type: 'CharacterSet';\n  kind: Exclude<NodeCharacterSetKind, NamedCharacterSetNode['kind']>;\n  value?: never;\n  negate?: boolean;\n  variableLength?: boolean;\n};\ntype CharacterSetNode = NamedCharacterSetNode | UnnamedCharacterSetNode;\n/**\nUse `createUnicodeProperty` and `createPosixClass` for `kind` values `'property'` and `'posix'`.\n*/\nfunction createCharacterSet(kind: UnnamedCharacterSetNode['kind'], options?: {\n  negate?: boolean;\n}): UnnamedCharacterSetNode {\n  const negate = !!options?.negate;\n  const node: UnnamedCharacterSetNode = {\n    type: 'CharacterSet',\n    kind,\n  };\n  if (\n    kind === 'digit' ||\n    kind === 'hex' ||\n    kind === 'newline' ||\n    kind === 'space' ||\n    kind === 'word'\n  ) {\n    node.negate = negate;\n  }\n  if (\n    kind === 'text_segment' ||\n    (kind === 'newline' && !negate)\n  ) {\n    node.variableLength = true;\n  }\n  return node;\n}\n\ntype DirectiveNode = {\n  type: 'Directive';\n} & ({\n  kind: 'keep';\n  flags?: never;\n} | {\n  kind: 'flags';\n  flags: FlagGroupModifiers;\n});\nfunction createDirective(kind: NodeDirectiveKind, options: {flags?: FlagGroupModifiers} = {}): DirectiveNode {\n  if (kind === 'keep') {\n    return {\n      type: 'Directive',\n      kind,\n    };\n  }\n  if (kind === 'flags') {\n    // Note: Flag effects might extend across alternation; ex: `a(?i)b|c` is equivalent to\n    // `a(?i:b)|(?i:c)`, not `a(?i:b|c)`\n    return {\n      type: 'Directive',\n      kind,\n      flags: throwIfNullish(options.flags),\n    };\n  }\n  throw new Error(`Unexpected directive kind \"${kind}\"`);\n}\n\ntype FlagsNode = {\n  type: 'Flags';\n} & FlagProperties;\nfunction createFlags(flags: FlagProperties): FlagsNode {\n  return {\n    type: 'Flags',\n    ...flags,\n  };\n}\n\ntype GroupNode = {\n  type: 'Group';\n  kind?: never;\n  atomic?: boolean;\n  flags?: FlagGroupModifiers;\n  body: Array<AlternativeNode>;\n};\nfunction createGroup(options?: {\n  atomic?: boolean;\n  flags?: FlagGroupModifiers;\n  body?: Array<AlternativeNode>;\n}): GroupNode {\n  const atomic = options?.atomic;\n  const flags = options?.flags;\n  if (atomic && flags) {\n    throw new Error('Atomic group cannot have flags');\n  }\n  return {\n    type: 'Group',\n    ...(atomic && {atomic}),\n    ...(flags && {flags}),\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype LookaroundAssertionNode = {\n  type: 'LookaroundAssertion';\n  kind: NodeLookaroundAssertionKind;\n  negate: boolean;\n  body: Array<AlternativeNode>;\n};\nfunction createLookaroundAssertion(options?: {\n  behind?: boolean;\n  negate?: boolean;\n  body?: Array<AlternativeNode>;\n}): LookaroundAssertionNode {\n  const opts = {\n    behind: false,\n    negate: false,\n    ...options,\n  };\n  return {\n    type: 'LookaroundAssertion',\n    kind: opts.behind ? 'lookbehind' : 'lookahead',\n    negate: opts.negate,\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype NamedCalloutNode = {\n  type: 'NamedCallout';\n  kind: NodeNamedCalloutKind;\n  tag: string | null;\n  arguments: Array<string | number> | null;\n};\nfunction createNamedCallout(\n  kind: NodeNamedCalloutKind,\n  tag: string | null,\n  args: Array<string | number> | null\n): NamedCalloutNode {\n  return {\n    type: 'NamedCallout',\n    kind,\n    tag,\n    arguments: args,\n  };\n}\n\nfunction createPosixClass(name: string, options?: {\n  negate?: boolean;\n}): NamedCharacterSetNode & {kind: 'posix'} {\n  const negate = !!options?.negate;\n  if (!PosixClassNames.has(name)) {\n    throw new Error(`Invalid POSIX class \"${name}\"`);\n  }\n  return {\n    type: 'CharacterSet',\n    kind: 'posix',\n    value: name,\n    negate,\n  };\n}\n\ntype QuantifierNode = {\n  type: 'Quantifier';\n  kind: NodeQuantifierKind;\n  min: number;\n  max: number;\n  body: QuantifiableNode;\n};\nfunction createQuantifier(kind: NodeQuantifierKind, min: number, max: number, body: QuantifiableNode): QuantifierNode {\n  if (min > max) {\n    throw new Error('Invalid reversed quantifier range');\n  }\n  return {\n    type: 'Quantifier',\n    kind,\n    min,\n    max,\n    body,\n  };\n}\n\ntype RegexNode = {\n  type: 'Regex';\n  body: Array<AlternativeNode>;\n  flags: FlagsNode;\n};\nfunction createRegex(flags: FlagsNode, options?: {\n  body?: Array<AlternativeNode>;\n}): RegexNode {\n  return {\n    type: 'Regex',\n    body: getBodyForAlternativeContainer(options?.body),\n    flags,\n  };\n}\n\ntype SubroutineNode = {\n  type: 'Subroutine';\n  ref: string | number;\n};\nfunction createSubroutine(ref: string | number): SubroutineNode {\n  return {\n    type: 'Subroutine',\n    ref,\n  };\n}\n\ntype CreateUnicodePropertyOptions = {\n  negate?: boolean;\n  normalizeUnknownPropertyNames?: boolean;\n  skipPropertyNameValidation?: boolean;\n  unicodePropertyMap?: UnicodePropertyMap | null;\n};\nfunction createUnicodeProperty(name: string, options?: CreateUnicodePropertyOptions): NamedCharacterSetNode & {kind: 'property'} {\n  const opts: Required<CreateUnicodePropertyOptions> = {\n    negate: false,\n    normalizeUnknownPropertyNames: false,\n    skipPropertyNameValidation: false,\n    unicodePropertyMap: null,\n    ...options,\n  };\n  let normalized = opts.unicodePropertyMap?.get(slug(name));\n  if (!normalized) {\n    if (opts.normalizeUnknownPropertyNames) {\n      normalized = normalizeUnicodePropertyName(name);\n    // Let the name through as-is if no map provided and normalization not requested\n    } else if (opts.unicodePropertyMap && !opts.skipPropertyNameValidation) {\n      throw new Error(r`Invalid Unicode property \"\\p{${name}}\"`);\n    }\n  }\n  return {\n    type: 'CharacterSet',\n    kind: 'property',\n    value: normalized ?? name,\n    negate: opts.negate,\n  };\n}\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\nfunction createByGroupKind({flags, kind, name, negate, number}: GroupOpenToken): AbsenceFunctionNode | CapturingGroupNode | GroupNode | LookaroundAssertionNode {\n  switch (kind) {\n    case 'absence_repeater':\n      return createAbsenceFunction('repeater');\n    case 'atomic':\n      return createGroup({atomic: true});\n    case 'capturing':\n      return createCapturingGroup(number!, {name});\n    case 'group':\n      return createGroup({flags});\n    case 'lookahead':\n    case 'lookbehind':\n      return createLookaroundAssertion({\n        behind: kind === 'lookbehind',\n        negate,\n      });\n    default:\n      throw new Error(`Unexpected group kind \"${kind}\"`);\n  }\n}\n\nfunction getBodyForAlternativeContainer(body: unknown): Array<AlternativeNode> {\n  if (body === undefined) {\n    body = [createAlternative()];\n  } else if (!Array.isArray(body) || !body.length || !body.every(node => (node as Node).type === 'Alternative')) {\n    throw new Error('Invalid body; expected array of one or more Alternative nodes');\n  }\n  return body as Array<AlternativeNode>;\n}\n\nfunction getBodyForElementContainer(body: unknown): Array<Node> {\n  if (body === undefined) {\n    body = [];\n  } else if (!Array.isArray(body) || !body.every(node => !!(node as Node).type)) {\n    throw new Error('Invalid body; expected array of nodes');\n  }\n  return body as Array<Node>;\n}\n\nfunction isLookahead(node: Node): node is (LookaroundAssertionNode & {kind: 'lookahead'}) {\n  return node.type === 'LookaroundAssertion' && node.kind === 'lookahead';\n}\n\nfunction isLookbehind(node: Node): node is (LookaroundAssertionNode & {kind: 'lookbehind'}) {\n  return node.type === 'LookaroundAssertion' && node.kind === 'lookbehind';\n}\n\nfunction isValidGroupName(name: string): boolean {\n  // Note that backrefs and subroutines might contextually use `-` and `+` to indicate relative\n  // index or recursion level\n  return /^[\\p{Alpha}\\p{Pc}][^)]*$/u.test(name);\n}\n\nfunction normalizeUnicodePropertyName(name: string): string {\n  // In Onig, Unicode property names ignore case, spaces, hyphens, and underscores. Use best effort\n  // to reformat the name to follow official values (covers a lot, but isn't able to map for all\n  // possible formatting differences)\n  return name.\n    trim().\n    replace(/[- _]+/g, '_').\n    replace(/[A-Z][a-z]+(?=[A-Z])/g, '$&_'). // `PropertyName` to `Property_Name`\n    replace(/[A-Za-z]+/g, m => m[0].toUpperCase() + m.slice(1).toLowerCase());\n}\n\n/**\nGenerates a Unicode property lookup name: lowercase, without spaces, hyphens, or underscores.\n*/\nfunction slug(name: string): string {\n  return name.replace(/[- _]+/g, '').toLowerCase();\n}\n\nfunction throwIfUnclosedCharacterClass<T>(token: T, firstClassToken?: Token): NonNullable<T> {\n  return throwIfNullish(\n    token,\n    // Easier to understand the error if it says \"empty\" when the unclosed class starts with\n    // literal `]`; ex: `[]` or `[]a`\n    `${firstClassToken?.type === 'Character' && firstClassToken.value === 93 ?\n      'Empty' : 'Unclosed'} character class`\n  );\n}\n\nfunction throwIfUnclosedGroup<T>(token: T): NonNullable<T> {\n  return throwIfNullish(token, 'Unclosed group');\n}\n\nexport {\n  type AbsenceFunctionNode,\n  type AlternativeNode,\n  type AlternativeContainerNode,\n  type AlternativeElementNode,\n  type AssertionNode,\n  type BackreferenceNode,\n  type CapturingGroupNode,\n  type CharacterClassElementNode,\n  type CharacterClassNode,\n  type CharacterClassRangeNode,\n  type CharacterNode,\n  type CharacterSetNode,\n  type DirectiveNode,\n  type FlagsNode,\n  type GroupNode,\n  type LookaroundAssertionNode,\n  type NamedCalloutNode,\n  type Node,\n  type NodeAbsenceFunctionKind,\n  type NodeAssertionKind,\n  type NodeCharacterClassKind,\n  type NodeCharacterSetKind,\n  type NodeDirectiveKind,\n  type NodeLookaroundAssertionKind,\n  type NodeQuantifierKind,\n  type OnigurumaAst,\n  type ParentNode,\n  type ParseOptions,\n  type QuantifiableNode,\n  type QuantifierNode,\n  type RegexNode,\n  type SubroutineNode,\n  type UnicodePropertyMap,\n  createAbsenceFunction,\n  createAlternative,\n  createAssertion,\n  createBackreference,\n  createCapturingGroup,\n  createCharacter,\n  createCharacterClass,\n  createCharacterClassRange,\n  createCharacterSet,\n  createDirective,\n  createFlags,\n  createGroup,\n  createLookaroundAssertion,\n  createNamedCallout,\n  createPosixClass,\n  createQuantifier,\n  createRegex,\n  createSubroutine,\n  createUnicodeProperty,\n  hasOnlyChild,\n  isAlternativeContainer,\n  isQuantifiable,\n  parse,\n  slug,\n};\n"], "mappings": "aACA,OAAQ,YAAAA,MAAe,2BACvB,OAAQ,QAAAC,EAAM,eAAAC,EAAa,mBAAAC,EAAiB,KAAAC,EAAG,kBAAAC,MAAqB,cACpE,OAAQ,gBAAAC,EAAc,0BAAAC,EAAwB,kBAAAC,MAAqB,kBA6InE,SAASC,EAAMC,EAAiBC,EAAwB,CAAC,EAAiB,CACxE,MAAMC,EAA+B,CACnC,MAAO,GACP,8BAA+B,GAC/B,sBAAuB,GACvB,yBAA0B,GAC1B,2BAA4B,GAE5B,mBAAoB,KACpB,GAAGD,EACH,MAAO,CACL,aAAc,GACd,WAAY,GACZ,GAAGA,EAAQ,KACb,CACF,EACME,EAAYb,EAASU,EAAS,CAElC,MAAOE,EAAK,MACZ,MAAO,CACL,aAAcA,EAAK,MAAM,aACzB,WAAYA,EAAK,MAAM,UACzB,CACF,CAAC,EACKE,EAAwB,CAACC,EAAQC,IAAU,CAC/C,MAAMC,EAAQJ,EAAU,OAAOK,EAAQ,SAAS,EAIhD,OAHAA,EAAQ,OAASH,EAEjBG,EAAQ,YACAD,EAAM,KAAM,CAClB,IAAK,aAEH,OAAOE,EAAkB,EAC3B,IAAK,YACH,OAAOC,EAAeH,CAAK,EAC7B,IAAK,gBACH,OAAOI,EAAmBJ,EAAOC,CAAO,EAC1C,IAAK,YACH,OAAOI,EAAgBL,EAAM,MAAO,CAAC,aAAc,CAAC,CAACD,EAAM,kBAAkB,CAAC,EAChF,IAAK,uBACH,OAAOO,GAA0BN,EAAOC,EAASF,CAAK,EACxD,IAAK,qBACH,OAAOQ,GAAwBP,EAAOC,EAASF,CAAK,EACtD,IAAK,eACH,OAAOS,GAAkBR,EAAOC,CAAO,EACzC,IAAK,YACH,OAAOQ,EAAgBT,EAAM,KAAM,CAAC,MAAOA,EAAM,KAAK,CAAC,EACzD,IAAK,YACH,OAAOU,GAAeV,EAAOC,EAASF,CAAK,EAC7C,IAAK,eACH,OAAOY,EAAmBX,EAAM,KAAMA,EAAM,IAAKA,EAAM,SAAS,EAClE,IAAK,aACH,OAAOY,GAAgBZ,EAAOC,CAAO,EACvC,IAAK,aACH,OAAOY,GAAgBb,EAAOC,CAAO,EACvC,QACE,MAAM,IAAI,MAAM,0BAA0BD,EAAM,IAAI,GAAG,CAC3D,CACF,EACMC,EAAmB,CACvB,gBAAiB,CAAC,EAClB,eAAgB,GAChB,kBAAmB,IAAI,IACvB,UAAW,EACX,8BAA+BN,EAAK,8BACpC,OAAQ,KACR,sBAAuBA,EAAK,sBAC5B,yBAA0BA,EAAK,yBAC/B,2BAA4BA,EAAK,2BACjC,YAAa,CAAC,EACd,OAAQC,EAAU,OAClB,mBAAoBD,EAAK,mBACzB,KAAAE,CACF,EAGMiB,EAAMC,EAAYC,EAAYpB,EAAU,KAAK,CAAC,EACpD,IAAIqB,EAAMH,EAAI,KAAK,CAAC,EACpB,KAAOb,EAAQ,UAAYL,EAAU,OAAO,QAAQ,CAClD,MAAMsB,EAAOrB,EAAKoB,EAAK,CAAC,CAAC,EACrBC,EAAK,OAAS,eAChBJ,EAAI,KAAK,KAAKI,CAAI,EAClBD,EAAMC,GAEND,EAAI,KAAK,KAAKC,CAA8B,CAEhD,CAIA,KAAM,CAAC,gBAAAC,EAAiB,eAAAC,EAAgB,kBAAAC,EAAmB,YAAAC,CAAW,EAAIrB,EAC1E,GAAImB,GAAkBC,EAAkB,MAAQ,CAAC1B,EAAK,MAAM,aAC1D,MAAM,IAAI,MAAM,kEAAkE,EAEpF,SAAW,CAAC,IAAA4B,CAAG,IAAKD,EAClB,GAAI,OAAOC,GAAQ,SAAU,CAE3B,GAAIA,EAAMJ,EAAgB,OACxB,MAAM,IAAI,MAAM,mDAAmD,EAEjEI,IACFJ,EAAgBI,EAAM,CAAC,EAAE,cAAgB,GAE7C,SAAYF,EAAkB,IAAIE,CAAG,EAE9B,IAAIF,EAAkB,IAAIE,CAAG,EAAG,OAAS,EAC9C,MAAM,IAAI,MAAMpC,+CAA+CoC,CAAG,IAAI,EAEtEF,EAAkB,IAAIE,CAAG,EAAG,CAAC,EAAE,cAAgB,OAJ/C,OAAM,IAAI,MAAMpC,wDAAwDoC,CAAG,IAAI,EAQnF,OAAOT,CACT,CAEA,SAASX,EAAe,CAAC,KAAAqB,CAAI,EAAkC,CAC7D,OAAOC,EACLrC,EAAe,CACb,IAAK,aACL,EAAK,WACL,MAAO,eACP,MAAO,gBACP,MAAO,gBACP,MAAO,eACP,MAAO,wBACP,MAAO,wBACP,MAAO,aACP,MAAO,oBACT,EAAEoC,CAAI,EAAG,8BAA8BA,CAAI,GAAG,EAC9C,CAAC,OAAQA,IAASrC,OAASqC,IAASrC,KAAK,CAC3C,CACF,CAgBA,SAASiB,EAAmB,CAAC,IAAAsB,CAAG,EAAuBzB,EAAqC,CAC1F,MAAM0B,EAAc,WAAW,KAAKD,CAAG,EACjCH,EAAMI,EAAcD,EAAI,MAAM,EAAG,EAAE,EAAIA,EAAI,MAAM,CAAC,EAClDE,EAAU,CAACC,EAAaC,EAAa,KAAU,CACnD,MAAMC,EAAoB9B,EAAQ,gBAAgB,OAClD,IAAI+B,EAAS,GAcb,GAAIH,EAAME,EAIR,GAAI9B,EAAQ,sBACV+B,EAAS,OAET,OAAM,IAAI,MAAM,oDAAoDN,CAAG,GAAG,EAG9E,OAAAzB,EAAQ,eAAiB,GAClBgC,EAAoBH,EAAaC,EAAoB,EAAIF,EAAMA,EAAK,CAAC,OAAAG,CAAM,CAAC,CACrF,EACA,GAAIL,EAAa,CACf,MAAMO,EAAc,kCAAkC,KAAKX,CAAG,EAC9D,GAAIW,EACF,OAAON,EAAQ,CAACM,EAAY,OAAQ,IAAK,CAAC,CAACA,EAAY,OAAQ,IAAI,EAGrE,GAAI,OAAO,KAAKX,CAAG,EACjB,MAAM,IAAI,MAAM,yBAAyBG,CAAG,GAAG,EAEjD,GAAI,CAACzB,EAAQ,kBAAkB,IAAIsB,CAAG,EACpC,MAAM,IAAI,MAAM,uCAAuCG,CAAG,GAAG,EAE/D,OAAOO,EAAoBV,CAAG,CAChC,CACA,OAAOK,EAAQ,CAACL,CAAG,CACrB,CAEA,SAASjB,GAA0B6B,EAA8BlC,EAAkBF,EAAuD,CACxI,KAAM,CAAC,OAAAqC,EAAQ,KAAAvC,CAAI,EAAII,EACjBH,EAASG,EAAQ,OACjBoC,EAAkBvC,EAAO,KAAK,GAAG,EAAE,EACnCwC,EAAYF,EAAOnC,EAAQ,SAAS,EAC1C,GACE,CAACF,EAAM,oBACPsC,GACAA,EAAgB,OAAS,kBACzBA,EAAgB,OAAS,uBACzBC,GACAA,EAAU,OAAS,sBACnBA,EAAU,OAAS,uBACnBA,EAAU,OAAS,4BACnB,CACA,MAAMC,EAAW1C,EAAKC,EAAQ,CAC5B,GAAGC,EACH,mBAAoB,EACtB,CAAC,EACD,GAAIsC,EAAgB,OAAS,aAAeE,EAAS,OAAS,YAC5D,OAAAzC,EAAO,KAAK,IAAI,EACT0C,EAA0BH,EAAiBE,CAAQ,EAE5D,MAAM,IAAI,MAAM,+BAA+B,CACjD,CACA,OAAOlC,EAAgBrB,EAAK,GAAG,CAAC,CAClC,CAEA,SAASuB,GAAwB,CAAC,OAAAkC,CAAM,EAA4BxC,EAAkBF,EAAkC,CACtH,KAAM,CAAC,OAAAqC,EAAQ,KAAAvC,CAAI,EAAII,EACjByC,EAAkBN,EAAOnC,EAAQ,SAAS,EAC1C0C,EAAgB,CAACC,EAAqB,CAAC,EAC7C,IAAIN,EAAYO,EAA8BH,CAAe,EAC7D,KAAOJ,EAAU,OAAS,uBAAuB,CAC/C,GAAIA,EAAU,OAAS,4BACrBK,EAAc,KAAKC,EAAqB,CAAC,EAEzC3C,EAAQ,gBACH,CACL,MAAM6C,EAAKH,EAAc,GAAG,EAAE,EAC9BG,EAAG,KAAK,KAAKjD,EAAKiD,EAAI/C,CAAK,CAA8B,CAC3D,CACAuC,EAAYO,EAA8BT,EAAOnC,EAAQ,SAAS,EAAGyC,CAAe,CACtF,CACA,MAAMxB,EAAO0B,EAAqB,CAAC,OAAAH,CAAM,CAAC,EAC1C,OAAIE,EAAc,SAAW,EAC3BzB,EAAK,KAAOyB,EAAc,CAAC,EAAE,MAE7BzB,EAAK,KAAO,eACZA,EAAK,KAAOyB,EAAc,IAAIG,GAAMA,EAAG,KAAK,SAAW,EAAIA,EAAG,KAAK,CAAC,EAAIA,CAAE,GAG5E7C,EAAQ,YACDiB,CACT,CAEA,SAASV,GAAkB,CAAC,KAAAgB,EAAM,OAAAiB,EAAQ,MAAAM,CAAK,EAAsB9C,EAAoC,CACvG,KAAM,CAAC,8BAAA+C,EAA+B,2BAAAC,EAA4B,mBAAAC,CAAkB,EAAIjD,EACxF,GAAIuB,IAAS,WAAY,CACvB,MAAM2B,EAAaC,EAAKL,CAAM,EAE9B,GAAI7D,EAAgB,IAAIiE,CAAU,GAAK,CAACD,GAAoB,IAAIC,CAAU,EACxE3B,EAAO,QACPuB,EAAQI,MAER,QAAOE,EAAsBN,EAAQ,CACnC,OAAAN,EACA,8BAAAO,EACA,2BAAAC,EACA,mBAAAC,CACF,CAAC,CAEL,CACA,OAAI1B,IAAS,QACJ8B,EAAiBP,EAAQ,CAAC,OAAAN,CAAM,CAAC,EAEnCc,EAAmB/B,EAAM,CAAC,OAAAiB,CAAM,CAAC,CAC1C,CAEA,SAAS/B,GAAeV,EAAuBC,EAAkBF,EAA8F,CAC7J,KAAM,CAAC,OAAAqC,EAAQ,gBAAAjB,EAAiB,kBAAAE,EAAmB,yBAAAmC,EAA0B,KAAA3D,CAAI,EAAII,EAC/EiB,EAAOuC,GAAkBzD,CAAK,EAC9B0D,EAAwBxC,EAAK,OAAS,kBACtCyC,EAAmBC,EAAa1C,CAAI,EACpC2C,EAAsBF,GAAoBzC,EAAK,OAUrD,GAPIA,EAAK,OAAS,mBAChBC,EAAgB,KAAKD,CAAI,EACrBA,EAAK,MACPjC,EAAYoC,EAAmBH,EAAK,KAAM,CAAC,CAAC,EAAE,KAAKA,CAAI,GAIvDwC,GAAyB3D,EAAM,oBAEjC,MAAM,IAAI,MAAM,oDAAoD,EAEtE,IAAIuC,EAAYwB,EAAqB1B,EAAOnC,EAAQ,SAAS,CAAC,EAC9D,KAAOqC,EAAU,OAAS,cAAc,CACtC,GAAIA,EAAU,OAAS,aACrBpB,EAAK,KAAK,KAAKhB,EAAkB,CAAC,EAElCD,EAAQ,gBACH,CACL,MAAM8D,EAAM7C,EAAK,KAAK,GAAG,EAAE,EACrB8C,EAAQnE,EAAKkE,EAAK,CACtB,GAAGhE,EACH,oBAAqBA,EAAM,qBAAuB2D,EAClD,eAAgB3D,EAAM,gBAAkB4D,EACxC,kBAAmB5D,EAAM,mBAAqB8D,CAChD,CAAC,EAGD,GAFAE,EAAI,KAAK,KAAKC,CAAK,GAEdL,GAAoB5D,EAAM,iBAAmB,CAACyD,EAA0B,CAI3E,MAAMS,EAAM,yDACZ,GAAIJ,GAAuB9D,EAAM,mBAG/B,GAAImE,EAAYF,CAAK,GAAKA,EAAM,OAAS,iBACvC,MAAM,IAAI,MAAMC,CAAG,UAKjBC,EAAYF,CAAK,GAAMJ,EAAaI,CAAK,GAAKA,EAAM,OACtD,MAAM,IAAI,MAAMC,CAAG,CAGzB,CACF,CACA3B,EAAYwB,EAAqB1B,EAAOnC,EAAQ,SAAS,CAAC,CAC5D,CAEA,OAAAA,EAAQ,YACDiB,CACT,CAEA,SAASN,GAAgB,CAAC,KAAAY,EAAM,IAAA2C,EAAK,IAAAC,CAAG,EAAoBnE,EAAkC,CAC5F,MAAMH,EAASG,EAAQ,OACjBoE,EAAiBvE,EAAO,KAAK,GAAG,EAAE,EACxC,GAAI,CAACuE,GAAkB,CAAC9E,EAAe8E,CAAc,EACnD,MAAM,IAAI,MAAM,wCAAwC,EAE1D,MAAMnD,EAAOoD,EAAiB9C,EAAM2C,EAAKC,EAAKC,CAAc,EAC5D,OAAAvE,EAAO,KAAK,IAAI,EACToB,CACT,CA8BA,SAASL,GAAgB,CAAC,IAAAa,CAAG,EAAoBzB,EAAkC,CACjF,KAAM,CAAC,gBAAAkB,EAAiB,YAAAG,CAAW,EAAIrB,EACvC,IAAIsB,EAAuBG,EAAI,MAAM,EAAG,EAAE,EAC1C,MAAMQ,EAAc,qCAAqC,KAAKX,CAAG,EACjE,GAAIW,EAAa,CACf,MAAML,EAAM,CAACK,EAAY,OAAQ,IAC3BH,EAAoBZ,EAAgB,OAO1C,GANAlB,EAAQ,eAAiB,GACzBsB,EAAM,CACJ,GAAIM,EACJ,IAAKE,EAAoBF,EACzB,IAAKE,EAAoB,EAAIF,CAC/B,EAAEK,EAAY,OAAQ,IAAI,EACtBX,EAAM,EACR,MAAM,IAAI,MAAM,2BAA2B,CAG/C,MAAWA,IAAQ,MACjBA,EAAM,GAER,MAAML,EAAOqD,EAAiBhD,CAAG,EACjC,OAAAD,EAAY,KAAKJ,CAAI,EACdA,CACT,CAWA,SAASsD,EAAsBhD,EAA+B9B,EAEtC,CACtB,GAAI8B,IAAS,WACX,MAAM,IAAI,MAAM,qCAAqCA,CAAI,GAAG,EAE9D,MAAO,CACL,KAAM,kBACN,KAAAA,EACA,KAAMiD,EAA+B/E,GAAS,IAAI,CACpD,CACF,CAMA,SAASQ,EAAkBR,EAEP,CAClB,MAAO,CACL,KAAM,cACN,KAAMgF,EAA2BhF,GAAS,IAAI,CAChD,CACF,CAOA,SAAS+B,EAAgBD,EAAyB9B,EAEhC,CAChB,MAAMwB,EAAsB,CAC1B,KAAM,YACN,KAAAM,CACF,EACA,OAAIA,IAAS,iBAAmBA,IAAS,2BACvCN,EAAK,OAAS,CAAC,CAACxB,GAAS,QAEpBwB,CACT,CAOA,SAASe,EAAoBV,EAAsB7B,EAE7B,CACpB,MAAMsC,EAAS,CAAC,CAACtC,GAAS,OAC1B,MAAO,CACL,KAAM,gBACN,IAAA6B,EACA,GAAIS,GAAU,CAAC,OAAAA,CAAM,CACvB,CACF,CAWA,SAAS2C,EAAqBC,EAAgBlF,EAIvB,CACrB,MAAMC,EAAO,CACX,KAAM,OACN,cAAe,GACf,GAAGD,CACL,EACA,GAAIC,EAAK,OAAS,QAAa,CAACkF,GAAiBlF,EAAK,IAAI,EACxD,MAAM,IAAI,MAAM,eAAeA,EAAK,IAAI,wBAAwB,EAElE,MAAO,CACL,KAAM,iBACN,OAAAiF,EACA,GAAIjF,EAAK,MAAQ,CAAC,KAAMA,EAAK,IAAI,EACjC,GAAIA,EAAK,eAAiB,CAAC,cAAeA,EAAK,aAAa,EAC5D,KAAM8E,EAA+B/E,GAAS,IAAI,CACpD,CACF,CAMA,SAASW,EAAgByE,EAAkBpF,EAEzB,CAChB,MAAMC,EAAO,CACX,aAAc,GACd,GAAGD,CACL,EACA,GAAIoF,EAAW,QAAU,CACvB,MAAMC,EAAMD,EAAS,SAAS,EAAE,EAChC,GAAInF,EAAK,aACPmF,EAAW,YACN,OAAIA,EAAW,QACd,IAAI,MAAM,wCAAwCC,CAAG,IAAI,EAEzD,IAAI,MAAM,8CAA8CA,CAAG,IAAI,CAEzE,CACA,MAAO,CACL,KAAM,YACN,MAAOD,CACT,CACF,CAQA,SAASlC,EAAqBlD,EAIP,CACrB,MAAMC,EAAO,CACX,KAAM,QACN,OAAQ,GACR,GAAGD,CACL,EACA,MAAO,CACL,KAAM,iBACN,KAAMC,EAAK,KACX,OAAQA,EAAK,OACb,KAAM+E,EAA2BhF,GAAS,IAAI,CAChD,CACF,CAOA,SAAS8C,EAA0B2B,EAAoBC,EAA6C,CAClG,GAAIA,EAAI,MAAQD,EAAI,MAClB,MAAM,IAAI,MAAM,oCAAoC,EAEtD,MAAO,CACL,KAAM,sBACN,IAAAA,EACA,IAAAC,CACF,CACF,CAoBA,SAASb,EAAmB/B,EAAuC9B,EAEvC,CAC1B,MAAM+C,EAAS,CAAC,CAAC/C,GAAS,OACpBwB,EAAgC,CACpC,KAAM,eACN,KAAAM,CACF,EACA,OACEA,IAAS,SACTA,IAAS,OACTA,IAAS,WACTA,IAAS,SACTA,IAAS,UAETN,EAAK,OAASuB,IAGdjB,IAAS,gBACRA,IAAS,WAAa,CAACiB,KAExBvB,EAAK,eAAiB,IAEjBA,CACT,CAWA,SAAST,EAAgBe,EAAyB9B,EAAwC,CAAC,EAAkB,CAC3G,GAAI8B,IAAS,OACX,MAAO,CACL,KAAM,YACN,KAAAA,CACF,EAEF,GAAIA,IAAS,QAGX,MAAO,CACL,KAAM,YACN,KAAAA,EACA,MAAOpC,EAAeM,EAAQ,KAAK,CACrC,EAEF,MAAM,IAAI,MAAM,8BAA8B8B,CAAI,GAAG,CACvD,CAKA,SAASR,EAAYgE,EAAkC,CACrD,MAAO,CACL,KAAM,QACN,GAAGA,CACL,CACF,CASA,SAASC,EAAYvF,EAIP,CACZ,MAAMwF,EAASxF,GAAS,OAClBsF,EAAQtF,GAAS,MACvB,GAAIwF,GAAUF,EACZ,MAAM,IAAI,MAAM,gCAAgC,EAElD,MAAO,CACL,KAAM,QACN,GAAIE,GAAU,CAAC,OAAAA,CAAM,EACrB,GAAIF,GAAS,CAAC,MAAAA,CAAK,EACnB,KAAMP,EAA+B/E,GAAS,IAAI,CACpD,CACF,CAQA,SAASyF,EAA0BzF,EAIP,CAC1B,MAAMC,EAAO,CACX,OAAQ,GACR,OAAQ,GACR,GAAGD,CACL,EACA,MAAO,CACL,KAAM,sBACN,KAAMC,EAAK,OAAS,aAAe,YACnC,OAAQA,EAAK,OACb,KAAM8E,EAA+B/E,GAAS,IAAI,CACpD,CACF,CAQA,SAASiB,EACPa,EACA4D,EACAC,EACkB,CAClB,MAAO,CACL,KAAM,eACN,KAAA7D,EACA,IAAA4D,EACA,UAAWC,CACb,CACF,CAEA,SAAS/B,EAAiBgC,EAAc5F,EAEI,CAC1C,MAAM+C,EAAS,CAAC,CAAC/C,GAAS,OAC1B,GAAI,CAACR,EAAgB,IAAIoG,CAAI,EAC3B,MAAM,IAAI,MAAM,wBAAwBA,CAAI,GAAG,EAEjD,MAAO,CACL,KAAM,eACN,KAAM,QACN,MAAOA,EACP,OAAA7C,CACF,CACF,CASA,SAAS6B,EAAiB9C,EAA0B2C,EAAaC,EAAamB,EAAwC,CACpH,GAAIpB,EAAMC,EACR,MAAM,IAAI,MAAM,mCAAmC,EAErD,MAAO,CACL,KAAM,aACN,KAAA5C,EACA,IAAA2C,EACA,IAAAC,EACA,KAAAmB,CACF,CACF,CAOA,SAASxE,EAAYiE,EAAkBtF,EAEzB,CACZ,MAAO,CACL,KAAM,QACN,KAAM+E,EAA+B/E,GAAS,IAAI,EAClD,MAAAsF,CACF,CACF,CAMA,SAAST,EAAiBhD,EAAsC,CAC9D,MAAO,CACL,KAAM,aACN,IAAAA,CACF,CACF,CAQA,SAAS8B,EAAsBiC,EAAc5F,EAAoF,CAC/H,MAAMC,EAA+C,CACnD,OAAQ,GACR,8BAA+B,GAC/B,2BAA4B,GAC5B,mBAAoB,KACpB,GAAGD,CACL,EACA,IAAIyD,EAAaxD,EAAK,oBAAoB,IAAIyD,EAAKkC,CAAI,CAAC,EACxD,GAAI,CAACnC,GACH,GAAIxD,EAAK,8BACPwD,EAAaqC,GAA6BF,CAAI,UAErC3F,EAAK,oBAAsB,CAACA,EAAK,2BAC1C,MAAM,IAAI,MAAMR,iCAAiCmG,CAAI,IAAI,EAG7D,MAAO,CACL,KAAM,eACN,KAAM,WACN,MAAOnC,GAAcmC,EACrB,OAAQ3F,EAAK,MACf,CACF,CAMA,SAAS8D,GAAkB,CAAC,MAAAuB,EAAO,KAAAxD,EAAM,KAAA8D,EAAM,OAAA7C,EAAQ,OAAAmC,CAAM,EAAmG,CAC9J,OAAQpD,EAAM,CACZ,IAAK,mBACH,OAAOgD,EAAsB,UAAU,EACzC,IAAK,SACH,OAAOS,EAAY,CAAC,OAAQ,EAAI,CAAC,EACnC,IAAK,YACH,OAAON,EAAqBC,EAAS,CAAC,KAAAU,CAAI,CAAC,EAC7C,IAAK,QACH,OAAOL,EAAY,CAAC,MAAAD,CAAK,CAAC,EAC5B,IAAK,YACL,IAAK,aACH,OAAOG,EAA0B,CAC/B,OAAQ3D,IAAS,aACjB,OAAAiB,CACF,CAAC,EACH,QACE,MAAM,IAAI,MAAM,0BAA0BjB,CAAI,GAAG,CACrD,CACF,CAEA,SAASiD,EAA+Bc,EAAuC,CAC7E,GAAIA,IAAS,OACXA,EAAO,CAACrF,EAAkB,CAAC,UAClB,CAAC,MAAM,QAAQqF,CAAI,GAAK,CAACA,EAAK,QAAU,CAACA,EAAK,MAAMrE,GAASA,EAAc,OAAS,aAAa,EAC1G,MAAM,IAAI,MAAM,+DAA+D,EAEjF,OAAOqE,CACT,CAEA,SAASb,EAA2Ba,EAA4B,CAC9D,GAAIA,IAAS,OACXA,EAAO,CAAC,UACC,CAAC,MAAM,QAAQA,CAAI,GAAK,CAACA,EAAK,MAAMrE,GAAQ,CAAC,CAAEA,EAAc,IAAI,EAC1E,MAAM,IAAI,MAAM,uCAAuC,EAEzD,OAAOqE,CACT,CAEA,SAASrB,EAAYhD,EAAqE,CACxF,OAAOA,EAAK,OAAS,uBAAyBA,EAAK,OAAS,WAC9D,CAEA,SAAS0C,EAAa1C,EAAsE,CAC1F,OAAOA,EAAK,OAAS,uBAAyBA,EAAK,OAAS,YAC9D,CAEA,SAAS2D,GAAiBS,EAAuB,CAG/C,MAAO,4BAA4B,KAAKA,CAAI,CAC9C,CAEA,SAASE,GAA6BF,EAAsB,CAI1D,OAAOA,EACL,KAAK,EACL,QAAQ,UAAW,GAAG,EACtB,QAAQ,wBAAyB,KAAK,EACtC,QAAQ,aAAcG,GAAKA,EAAE,CAAC,EAAE,YAAY,EAAIA,EAAE,MAAM,CAAC,EAAE,YAAY,CAAC,CAC5E,CAKA,SAASrC,EAAKkC,EAAsB,CAClC,OAAOA,EAAK,QAAQ,UAAW,EAAE,EAAE,YAAY,CACjD,CAEA,SAASzC,EAAiC7C,EAAU0C,EAAyC,CAC3F,OAAOtD,EACLY,EAGA,GAAG0C,GAAiB,OAAS,aAAeA,EAAgB,QAAU,GACpE,QAAU,UAAU,kBACxB,CACF,CAEA,SAASoB,EAAwB9D,EAA0B,CACzD,OAAOZ,EAAeY,EAAO,gBAAgB,CAC/C,CAEA,OAkCEwE,KAAA,sBACAtE,KAAA,kBACAuB,KAAA,gBACAQ,KAAA,oBACA0C,KAAA,qBACAtE,KAAA,gBACAuC,KAAA,qBACAJ,KAAA,0BACAe,KAAA,mBACA9C,KAAA,gBACAO,KAAA,YACAiE,KAAA,YACAE,KAAA,0BACAxE,KAAA,mBACA2C,KAAA,iBACAgB,KAAA,iBACAvD,KAAA,YACAwD,KAAA,iBACAlB,KAAA,sBACAhE,KAAA,aACAC,KAAA,uBACAC,KAAA,eACAC,KAAA,MACA4D,KAAA", "names": ["tokenize", "cpOf", "getOrInsert", "PosixClassNames", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isAlternativeContainer", "isQuantifiable", "parse", "pattern", "options", "opts", "tokenized", "walk", "parent", "state", "token", "context", "createAlternative", "parseAssertion", "parseBackreference", "createCharacter", "parseCharacterClassHyphen", "parseCharacterClassOpen", "parseCharacterSet", "createDirective", "parseGroupOpen", "createNamedCallout", "parseQuantifier", "parseSubroutine", "ast", "createRegex", "createFlags", "top", "node", "capturingGroups", "hasNumberedRef", "namedGroupsByName", "subroutines", "ref", "kind", "createAssertion", "raw", "hasKWrapper", "fromNum", "num", "isRelative", "numCapturesToLeft", "orphan", "createBackreference", "numberedRef", "_", "tokens", "prevSiblingNode", "nextToken", "nextNode", "createCharacterClassRange", "negate", "firstClassToken", "intersections", "createCharacterClass", "throwIfUnclosedCharacterClass", "cc", "value", "normalizeUnknownPropertyNames", "skipPropertyNameValidation", "unicodePropertyMap", "normalized", "slug", "createUnicodeProperty", "createPosixClass", "createCharacterSet", "skipLookbehindValidation", "createByGroupKind", "isThisAbsenceFunction", "isThisLookbehind", "isLookbehind", "isThisNegLookbehind", "throwIfUnclosedGroup", "alt", "child", "msg", "isLookahead", "min", "max", "quantifiedNode", "createQuantifier", "createSubroutine", "createAbsenceFunction", "getBodyForAlternativeContainer", "getBodyForElementContainer", "createCapturingGroup", "number", "isValidGroupName", "charCode", "hex", "flags", "createGroup", "atomic", "createLookaroundAssertion", "tag", "args", "name", "body", "normalizeUnicodePropertyName", "m"]}