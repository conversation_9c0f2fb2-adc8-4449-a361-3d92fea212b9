(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[379],{244:(e,t,r)=>{"use strict";function n(e,t,r){let{includePage:n=!0,includeSeparator:o=!1,includeRoot:i}=r,l=[];return t.forEach((e,r)=>{if("separator"===e.type&&e.name&&o&&l.push({name:e.name}),"folder"===e.type){let n=t.at(r+1);if(n&&e.index===n)return;if(e.root){l=[];return}l.push({name:e.name,url:e.index?.url})}"page"===e.type&&n&&l.push({name:e.name,url:e.url})}),i&&l.unshift({name:e.name,url:"object"==typeof i?i.url:void 0}),l}r.d(t,{Pp:()=>n,oe:()=>function e(t,r){let n;for(let o of(r.endsWith("/")&&(r=r.slice(0,-1)),t)){if("separator"===o.type&&(n=o),"folder"===o.type){if(o.index?.url===r){let e=[];return n&&e.push(n),e.push(o,o.index),e}let t=e(o.children,r);if(t)return t.unshift(o),n&&t.unshift(n),t}if("page"===o.type&&o.url===r){let e=[];return n&&e.push(n),e.push(o),e}}return null}}),r(7505),r(2115)},263:(e,t,r)=>{"use strict";r.d(t,{G:()=>u,c:()=>s});var n=r(5155),o=r(2115),i=r(344),l=r(3259);let a=(0,i.q6)("SidebarContext");function s(){return a.use()}function u(e){let{children:t}=e,r=(0,o.useRef)(!0),[s,u]=(0,o.useState)(!1),[c,d]=(0,o.useState)(!1),f=(0,i.a8)();return(0,l.T)(f,()=>{r.current&&u(!1),r.current=!0}),(0,n.jsx)(a.Provider,{value:(0,o.useMemo)(()=>({open:s,setOpen:u,collapsed:c,setCollapsed:d,closeOnRedirect:r}),[s,c]),children:t})}},901:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext(null)},1193:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:o,quality:i}=e,l=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+o+"&q="+l+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},getImageProps:function(){return a}});let n=r(8229),o=r(8883),i=r(3063),l=n._(r(1193));function a(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=i.Image},2119:(e,t,r)=>{"use strict";r.d(t,{Rc:()=>eo,hx:()=>ea,Ct:()=>ec,iz:()=>es,i:()=>ei,y:()=>ep,vo:()=>el,FI:()=>ed,Xq:()=>eu,Sk:()=>eg,dZ:()=>eb});var n=r(5155),o=r(8686),i=r(2115),l=r(9697),a=r(9688),s=r(5185),u=r(6101),c=r(6081),d=r(1285),f=r(5845),h=r(9178),p=r(7900),m=(r(4378),r(8905)),g=r(3655),b=r(2293),y=r(3795),v=r(8168),S=r(9708),w="Dialog",[I,x]=(0,c.A)(w),[O,_]=I(w),N=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:l,onOpenChange:a,modal:s=!0}=e,u=i.useRef(null),c=i.useRef(null),[h,p]=(0,f.i)({prop:o,defaultProp:null!=l&&l,onChange:a,caller:w});return(0,n.jsx)(O,{scope:t,triggerRef:u,contentRef:c,contentId:(0,d.B)(),titleId:(0,d.B)(),descriptionId:(0,d.B)(),open:h,onOpenChange:p,onOpenToggle:i.useCallback(()=>p(e=>!e),[p]),modal:s,children:r})};N.displayName=w;var D="DialogTrigger";i.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,i=_(D,r),l=(0,u.s)(t,i.triggerRef);return(0,n.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":V(i.open),...o,ref:l,onClick:(0,s.mK)(e.onClick,i.onOpenToggle)})}).displayName=D;var[T,E]=I("DialogPortal",{forceMount:void 0}),P="DialogOverlay",A=i.forwardRef((e,t)=>{let r=E(P,e.__scopeDialog),{forceMount:o=r.forceMount,...i}=e,l=_(P,e.__scopeDialog);return l.modal?(0,n.jsx)(m.C,{present:o||l.open,children:(0,n.jsx)(k,{...i,ref:t})}):null});A.displayName=P;var R=(0,S.TL)("DialogOverlay.RemoveScroll"),k=i.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,i=_(P,r);return(0,n.jsx)(y.A,{as:R,allowPinchZoom:!0,shards:[i.contentRef],children:(0,n.jsx)(g.sG.div,{"data-state":V(i.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),M="DialogContent",C=i.forwardRef((e,t)=>{let r=E(M,e.__scopeDialog),{forceMount:o=r.forceMount,...i}=e,l=_(M,e.__scopeDialog);return(0,n.jsx)(m.C,{present:o||l.open,children:l.modal?(0,n.jsx)(j,{...i,ref:t}):(0,n.jsx)(z,{...i,ref:t})})});C.displayName=M;var j=i.forwardRef((e,t)=>{let r=_(M,e.__scopeDialog),o=i.useRef(null),l=(0,u.s)(t,r.contentRef,o);return i.useEffect(()=>{let e=o.current;if(e)return(0,v.Eq)(e)},[]),(0,n.jsx)(L,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.mK)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,s.mK)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,s.mK)(e.onFocusOutside,e=>e.preventDefault())})}),z=i.forwardRef((e,t)=>{let r=_(M,e.__scopeDialog),o=i.useRef(!1),l=i.useRef(!1);return(0,n.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(i=r.triggerRef.current)||i.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var n,i;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let a=t.target;(null==(i=r.triggerRef.current)?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),L=i.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:a,...s}=e,c=_(M,r),d=i.useRef(null),f=(0,u.s)(t,d);return(0,b.Oh)(),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(p.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,n.jsx)(h.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":V(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(J,{titleId:c.titleId}),(0,n.jsx)(Y,{contentRef:d,descriptionId:c.descriptionId})]})]})}),U="DialogTitle",B=i.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,i=_(U,r);return(0,n.jsx)(g.sG.h2,{id:i.titleId,...o,ref:t})});B.displayName=U;var F="DialogDescription";i.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,i=_(F,r);return(0,n.jsx)(g.sG.p,{id:i.descriptionId,...o,ref:t})}).displayName=F;var W="DialogClose";function V(e){return e?"open":"closed"}i.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,i=_(W,r);return(0,n.jsx)(g.sG.button,{type:"button",...o,ref:t,onClick:(0,s.mK)(e.onClick,()=>i.onOpenChange(!1))})}).displayName=W;var $="DialogTitleWarning",[G,H]=(0,c.q)($,{contentName:M,titleName:U,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,r=H($),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return i.useEffect(()=>{t&&(document.getElementById(t)||console.error(n))},[n,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,n=H("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return i.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(o))},[o,t,r]),null},K=r(3243),q=r(8265),X=r(344),Z=r(3259),Q=r(8048),ee=r(7936);let et=(0,i.createContext)(null),er=(0,i.createContext)(null),en=(0,i.createContext)(null);function eo(e){let{open:t,onOpenChange:r,search:o,onSearchChange:l,isLoading:a=!1,children:s}=e,[u,c]=(0,i.useState)(null);return(0,n.jsx)(N,{open:t,onOpenChange:r,children:(0,n.jsx)(et.Provider,{value:(0,i.useMemo)(()=>({open:t,onOpenChange:r,search:o,onSearchChange:l,active:u,setActive:c,isLoading:a}),[u,a,r,l,t,o]),children:s})})}function ei(e){return(0,n.jsx)("div",{...e,className:(0,a.QP)("flex flex-row items-center gap-2 p-3",e.className)})}function el(e){let{text:t}=(0,l.useI18n)(),{search:r,onSearchChange:o}=ev();return(0,n.jsx)("input",{...e,value:r,onChange:e=>o(e.target.value),placeholder:t.search,className:"w-0 flex-1 bg-transparent text-lg placeholder:text-fd-muted-foreground focus-visible:outline-none"})}function ea(e){let{children:t="ESC",className:r,...o}=e,{onOpenChange:i}=ev();return(0,n.jsx)("button",{type:"button",onClick:()=>i(!1),className:(0,a.QP)((0,ee.r)({color:"outline",size:"sm",className:"font-mono text-fd-muted-foreground"}),r),...o,children:t})}function es(e){return(0,n.jsx)("div",{...e,className:(0,a.QP)("bg-fd-secondary/50 p-3 empty:hidden",e.className)})}function eu(e){return(0,n.jsx)(A,{...e,className:(0,a.QP)("fixed inset-0 z-50 max-md:backdrop-blur-xs data-[state=open]:animate-fd-fade-in data-[state=closed]:animate-fd-fade-out",e.className)})}function ec(e){let{children:t,...r}=e,{text:o}=(0,l.useI18n)();return(0,n.jsxs)(C,{"aria-describedby":void 0,...r,className:(0,a.QP)("fixed left-1/2 top-4 md:top-[calc(50%-250px)] z-50 w-[calc(100%-1rem)] max-w-screen-sm -translate-x-1/2 rounded-2xl border bg-fd-popover/80 backdrop-blur-xl text-fd-popover-foreground shadow-2xl shadow-black/50 overflow-hidden data-[state=closed]:animate-fd-dialog-out data-[state=open]:animate-fd-dialog-in","*:border-b *:has-[+:last-child[data-empty=true]]:border-b-0 *:data-[empty=true]:border-b-0 *:last:border-b-0",r.className),children:[(0,n.jsx)(B,{className:"hidden",children:o.search}),t]})}function ed(e){let{items:t=null,Empty:r=()=>(0,n.jsx)("div",{className:"py-12 text-center text-sm text-fd-muted-foreground",children:(0,n.jsx)(l.I18nLabel,{label:"searchNoResult"})}),Item:o=e=>(0,n.jsx)(eh,{...e}),...s}=e,u=(0,i.useRef)(null),[c,d]=(0,i.useState)(()=>t&&t.length>0?t[0].id:null),{onOpenChange:f}=ev(),h=(0,X.rd)(),p=e=>{var t;let{external:r,url:n}=e;r?null==(t=window.open(n,"_blank"))||t.focus():h.push(n),f(!1)},m=(0,q.J)(e=>{if(t&&!e.isComposing){if("ArrowDown"===e.key||"ArrowUp"==e.key){var r,n;let o=t.findIndex(e=>e.id===c);-1===o?o=0:"ArrowDown"===e.key?o++:o--,d(null!=(n=null==(r=t.at(o%t.length))?void 0:r.id)?n:null),e.preventDefault()}if("Enter"===e.key){let r=t.find(e=>e.id===c);r&&p(r),e.preventDefault()}}});return(0,i.useEffect)(()=>{let e=u.current;if(!e)return;let t=new ResizeObserver(()=>{let t=e.firstElementChild;e.style.setProperty("--fd-animated-height","".concat(t.clientHeight,"px"))}),r=e.firstElementChild;return r&&t.observe(r),window.addEventListener("keydown",m),()=>{t.disconnect(),window.removeEventListener("keydown",m)}},[m]),(0,Z.T)(t,()=>{t&&t.length>0&&d(t[0].id)}),(0,n.jsx)("div",{...s,ref:u,"data-empty":null===t,className:(0,a.QP)("overflow-hidden h-(--fd-animated-height) transition-[height]",s.className),children:(0,n.jsx)("div",{className:(0,a.QP)("w-full flex flex-col overflow-y-auto max-h-[460px] p-1",!t&&"hidden"),children:(0,n.jsxs)(er.Provider,{value:(0,i.useMemo)(()=>({active:c,setActive:d}),[c]),children:[(null==t?void 0:t.length)===0&&r(),null==t?void 0:t.map(e=>(0,n.jsx)(i.Fragment,{children:o({item:e,onClick:()=>p(e)})},e.id))]})})})}let ef={text:null,heading:(0,n.jsx)(o.Vw,{className:"size-4 shrink-0 text-fd-muted-foreground"}),page:(0,n.jsx)(o.iU,{className:"size-6 text-fd-muted-foreground bg-fd-muted border p-0.5 rounded-sm shadow-sm shrink-0"})};function eh(e){let{item:t,className:r,children:o,renderHighlights:l=ey,...s}=e,{active:u,setActive:c}=function(){let e=(0,i.useContext)(er);if(!e)throw Error("Missing <SearchDialogList />");return e}(),d=t.id===u;return(0,n.jsx)("button",{type:"button",ref:(0,i.useCallback)(e=>{d&&e&&(0,Q.A)(e,{scrollMode:"if-needed",block:"nearest",boundary:e.parentElement})},[d]),"aria-selected":d,className:(0,a.QP)("relative flex select-none flex-row items-center gap-2 p-2 text-start text-sm rounded-lg","page"!==t.type&&"ps-8","page"===t.type||"heading"===t.type?"font-medium":"text-fd-popover-foreground/80",d&&"bg-fd-accent text-fd-accent-foreground",r),onPointerMove:()=>c(t.id),...s,children:null!=o?o:(0,n.jsxs)(n.Fragment,{children:["page"!==t.type&&(0,n.jsx)("div",{role:"none",className:"absolute start-4.5 inset-y-0 w-px bg-fd-border"}),ef[t.type],(0,n.jsx)("p",{className:"min-w-0 truncate",children:t.contentWithHighlights?l(t.contentWithHighlights):t.content})]})})}function ep(e){let{isLoading:t}=ev();return(0,n.jsx)(o.vj,{...e,className:(0,a.QP)("size-5 text-fd-muted-foreground",t&&"animate-pulse duration-400",e.className)})}let em=(0,K.F)("rounded-md border px-2 py-0.5 text-xs font-medium text-fd-muted-foreground transition-colors",{variants:{active:{true:"bg-fd-accent text-fd-accent-foreground"}}});function eg(e){let{tag:t,onTagChange:r,allowClear:o=!1,...l}=e;return(0,n.jsx)("div",{...l,className:(0,a.QP)("flex items-center gap-1 flex-wrap",l.className),children:(0,n.jsx)(en.Provider,{value:(0,i.useMemo)(()=>({value:t,onValueChange:r,allowClear:o}),[o,r,t]),children:l.children})})}function eb(e){let{value:t,className:r,...o}=e,{onValueChange:l,value:s,allowClear:u}=function(){let e=(0,i.useContext)(en);if(!e)throw Error("Missing <TagsList />");return e}(),c=t===s;return(0,n.jsx)("button",{type:"button","data-active":c,className:(0,a.QP)(em({active:c,className:r})),onClick:()=>{l(c&&u?void 0:t)},tabIndex:-1,...o,children:o.children})}function ey(e){return e.map((e,t)=>{var r;return(null==(r=e.styles)?void 0:r.highlight)?(0,n.jsx)("span",{className:"text-fd-primary bg-fd-primary/10",children:e.content},t):(0,n.jsx)(i.Fragment,{children:e.content},t)})}function ev(){let e=(0,i.useContext)(et);if(!e)throw Error("Missing <SearchDialog />");return e}},2464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext({})},3063:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return S}});let n=r(8229),o=r(6966),i=r(5155),l=o._(r(2115)),a=n._(r(7650)),s=n._(r(5564)),u=r(8883),c=r(5840),d=r(6752);r(3230);let f=r(901),h=n._(r(1193)),p=r(6654),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function g(e,t,r,n,o,i,l){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,o=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function b(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}let y=(0,l.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:o,height:a,width:s,decoding:u,className:c,style:d,fetchPriority:f,placeholder:h,loading:m,unoptimized:y,fill:v,onLoadRef:S,onLoadingCompleteRef:w,setBlurComplete:I,setShowAltText:x,sizesInput:O,onLoad:_,onError:N,...D}=e,T=(0,l.useCallback)(e=>{e&&(N&&(e.src=e.src),e.complete&&g(e,h,S,w,I,y,O))},[r,h,S,w,I,N,y,O]),E=(0,p.useMergedRef)(t,T);return(0,i.jsx)("img",{...D,...b(f),loading:m,width:s,height:a,decoding:u,"data-nimg":v?"fill":"1",className:c,style:d,sizes:o,srcSet:n,src:r,ref:E,onLoad:e=>{g(e.currentTarget,h,S,w,I,y,O)},onError:e=>{x(!0),"empty"!==h&&I(!0),N&&N(e)}})});function v(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...b(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,n),null):(0,i.jsx)(s.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let S=(0,l.forwardRef)((e,t)=>{let r=(0,l.useContext)(f.RouterContext),n=(0,l.useContext)(d.ImageConfigContext),o=(0,l.useMemo)(()=>{var e;let t=m||n||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:o,qualities:i}},[n]),{onLoad:a,onLoadingComplete:s}=e,p=(0,l.useRef)(a);(0,l.useEffect)(()=>{p.current=a},[a]);let g=(0,l.useRef)(s);(0,l.useEffect)(()=>{g.current=s},[s]);let[b,S]=(0,l.useState)(!1),[w,I]=(0,l.useState)(!1),{props:x,meta:O}=(0,u.getImgProps)(e,{defaultLoader:h.default,imgConf:o,blurComplete:b,showAltText:w});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(y,{...x,unoptimized:O.unoptimized,placeholder:O.placeholder,fill:O.fill,onLoadRef:p,onLoadingCompleteRef:g,setBlurComplete:S,setShowAltText:I,sizesInput:e.sizes,ref:t}),O.priority?(0,i.jsx)(v,{isAppRouter:!r,imgAttributes:x}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3259:(e,t,r)=>{"use strict";r.d(t,{T:()=>n.T});var n=r(8011);r(7505)},3279:(e,t,r)=>{"use strict";r.d(t,{vt:()=>e0,VT:()=>e1,Hh:()=>ta,$P:()=>ti});let n={arabic:"ar",armenian:"am",bulgarian:"bg",czech:"cz",danish:"dk",dutch:"nl",english:"en",finnish:"fi",french:"fr",german:"de",greek:"gr",hungarian:"hu",indian:"in",indonesian:"id",irish:"ie",italian:"it",lithuanian:"lt",nepali:"np",norwegian:"no",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"rs",slovenian:"ru",spanish:"es",swedish:"se",tamil:"ta",turkish:"tr",ukrainian:"uk",sanskrit:"sk"},o={dutch:/[^A-Za-zàèéìòóù0-9_'-]+/gim,english:/[^A-Za-zàèéìòóù0-9_'-]+/gim,french:/[^a-z0-9äâàéèëêïîöôùüûœç-]+/gim,italian:/[^A-Za-zàèéìòóù0-9_'-]+/gim,norwegian:/[^a-z0-9_æøåÆØÅäÄöÖüÜ]+/gim,portuguese:/[^a-z0-9à-úÀ-Ú]/gim,russian:/[^a-z0-9а-яА-ЯёЁ]+/gim,spanish:/[^a-z0-9A-Zá-úÁ-ÚñÑüÜ]+/gim,swedish:/[^a-z0-9_åÅäÄöÖüÜ-]+/gim,german:/[^a-z0-9A-ZäöüÄÖÜß]+/gim,finnish:/[^a-z0-9äöÄÖ]+/gim,danish:/[^a-z0-9æøåÆØÅ]+/gim,hungarian:/[^a-z0-9áéíóöőúüűÁÉÍÓÖŐÚÜŰ]+/gim,romanian:/[^a-z0-9ăâîșțĂÂÎȘȚ]+/gim,serbian:/[^a-z0-9čćžšđČĆŽŠĐ]+/gim,turkish:/[^a-z0-9çÇğĞıİöÖşŞüÜ]+/gim,lithuanian:/[^a-z0-9ąčęėįšųūžĄČĘĖĮŠŲŪŽ]+/gim,arabic:/[^a-z0-9أ-ي]+/gim,nepali:/[^a-z0-9अ-ह]+/gim,irish:/[^a-z0-9áéíóúÁÉÍÓÚ]+/gim,indian:/[^a-z0-9अ-ह]+/gim,armenian:/[^a-z0-9ա-ֆ]+/gim,greek:/[^a-z0-9α-ωά-ώ]+/gim,indonesian:/[^a-z0-9]+/gim,ukrainian:/[^a-z0-9а-яА-ЯіїєІЇЄ]+/gim,slovenian:/[^a-z0-9čžšČŽŠ]+/gim,bulgarian:/[^a-z0-9а-яА-Я]+/gim,tamil:/[^a-z0-9அ-ஹ]+/gim,sanskrit:/[^a-z0-9A-Zāīūṛḷṃṁḥśṣṭḍṇṅñḻḹṝ]+/gim,czech:/[^A-Z0-9a-zěščřžýáíéúůóťďĚŠČŘŽÝÁÍÉÓÚŮŤĎ-]+/gim},i=Object.keys(n);var l=r(9509);let a=Date.now().toString().slice(5),s=0,u=BigInt(1e3),c=BigInt(1e6),d=BigInt(1e9);function f(e,t){if(t.length<65535)Array.prototype.push.apply(e,t);else{let r=t.length;for(let n=0;n<r;n+=65535)Array.prototype.push.apply(e,t.slice(n,n+65535))}}function h(){return BigInt(Math.floor(1e6*performance.now()))}function p(e){return("number"==typeof e&&(e=BigInt(e)),e<u)?`${e}ns`:e<c?`${e/u}μs`:e<d?`${e/c}ms`:`${e/d}s`}function m(){return"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?h():void 0!==l&&l.release&&"node"===l.release.name||void 0!==l&&"function"==typeof l?.hrtime?.bigint?l.hrtime.bigint():"undefined"!=typeof performance?h():BigInt(0)}function g(){return`${a}-${s++}`}function b(e,t){return void 0===Object.hasOwn?Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0:Object.hasOwn(e,t)?e[t]:void 0}function y(e,t){return t[1]===e[1]?e[0]-t[0]:t[1]-e[1]}function v(e,t){let r={},n=t.length;for(let o=0;o<n;o++){let n=t[o],i=n.split("."),l=e,a=i.length;for(let e=0;e<a;e++)if("object"==typeof(l=l[i[e]])){if(null!==l&&"lat"in l&&"lon"in l&&"number"==typeof l.lat&&"number"==typeof l.lon){l=r[n]=l;break}else if(!Array.isArray(l)&&null!==l&&e===a-1){l=void 0;break}}else if((null===l||"object"!=typeof l)&&e<a-1){l=void 0;break}void 0!==l&&(r[n]=l)}return r}function S(e,t){return v(e,[t])[t]}let w={cm:.01,m:1,km:1e3,ft:.3048,yd:.9144,mi:1609.344};function I(e,t){e.hits=e.hits.map(e=>({...e,document:{...e.document,...t.reduce((e,t)=>{let r=t.split("."),n=r.pop(),o=e;for(let e of r)o[e]=o[e]??{},o=o[e];return o[n]=null,e},e.document)}}))}function x(e){return Array.isArray(e)?e.some(e=>x(e)):e?.constructor?.name==="AsyncFunction"}let O="intersection"in new Set;function _(...e){if(0===e.length)return new Set;if(1===e.length)return e[0];if(2===e.length){let t=e[0],r=e[1];if(O)return t.intersection(r);let n=new Set,o=t.size<r.size?t:r,i=o===t?r:t;for(let e of o)i.has(e)&&n.add(e);return n}let t={index:0,size:e[0].size};for(let r=1;r<e.length;r++)e[r].size<t.size&&(t.index=r,t.size=e[r].size);if(O){let r=e[t.index];for(let n=0;n<e.length;n++)n!==t.index&&(r=r.intersection(e[n]));return r}let r=e[t.index];for(let n=0;n<e.length;n++){if(n===t.index)continue;let o=e[n];for(let e of r)o.has(e)||r.delete(e)}return r}let N="union"in new Set;function D(e,t){return N?e?e.union(t):t:new Set(e?[...e,...t]:t)}let T=i.join("\n - "),E={NO_LANGUAGE_WITH_CUSTOM_TOKENIZER:"Do not pass the language option to create when using a custom tokenizer.",LANGUAGE_NOT_SUPPORTED:`Language "%s" is not supported.
Supported languages are:
 - ${T}`,INVALID_STEMMER_FUNCTION_TYPE:"config.stemmer property must be a function.",MISSING_STEMMER:'As of version 1.0.0 @orama/orama does not ship non English stemmers by default. To solve this, please explicitly import and specify the "%s" stemmer from the package @orama/stemmers. See https://docs.orama.com/open-source/text-analysis/stemming for more information.',CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY:"Custom stop words array must only contain strings.",UNSUPPORTED_COMPONENT:'Unsupported component "%s".',COMPONENT_MUST_BE_FUNCTION:'The component "%s" must be a function.',COMPONENT_MUST_BE_FUNCTION_OR_ARRAY_FUNCTIONS:'The component "%s" must be a function or an array of functions.',INVALID_SCHEMA_TYPE:'Unsupported schema type "%s" at "%s". Expected "string", "boolean" or "number" or array of them.',DOCUMENT_ID_MUST_BE_STRING:'Document id must be of type "string". Got "%s" instead.',DOCUMENT_ALREADY_EXISTS:'A document with id "%s" already exists.',DOCUMENT_DOES_NOT_EXIST:'A document with id "%s" does not exists.',MISSING_DOCUMENT_PROPERTY:'Missing searchable property "%s".',INVALID_DOCUMENT_PROPERTY:'Invalid document property "%s": expected "%s", got "%s"',UNKNOWN_INDEX:'Invalid property name "%s". Expected a wildcard string ("*") or array containing one of the following properties: %s',INVALID_BOOST_VALUE:"Boost value must be a number greater than, or less than 0.",INVALID_FILTER_OPERATION:"You can only use one operation per filter, you requested %d.",SCHEMA_VALIDATION_FAILURE:'Cannot insert document due schema validation failure on "%s" property.',INVALID_SORT_SCHEMA_TYPE:'Unsupported sort schema type "%s" at "%s". Expected "string" or "number".',CANNOT_SORT_BY_ARRAY:'Cannot configure sort for "%s" because it is an array (%s).',UNABLE_TO_SORT_ON_UNKNOWN_FIELD:'Unable to sort on unknown field "%s". Allowed fields: %s',SORT_DISABLED:"Sort is disabled. Please read the documentation at https://docs.oramasearch for more information.",UNKNOWN_GROUP_BY_PROPERTY:'Unknown groupBy property "%s".',INVALID_GROUP_BY_PROPERTY:'Invalid groupBy property "%s". Allowed types: "%s", but given "%s".',UNKNOWN_FILTER_PROPERTY:'Unknown filter property "%s".',INVALID_VECTOR_SIZE:'Vector size must be a number greater than 0. Got "%s" instead.',INVALID_VECTOR_VALUE:'Vector value must be a number greater than 0. Got "%s" instead.',INVALID_INPUT_VECTOR:`Property "%s" was declared as a %s-dimensional vector, but got a %s-dimensional vector instead.
Input vectors must be of the size declared in the schema, as calculating similarity between vectors of different sizes can lead to unexpected results.`,WRONG_SEARCH_PROPERTY_TYPE:'Property "%s" is not searchable. Only "string" properties are searchable.',FACET_NOT_SUPPORTED:'Facet doens\'t support the type "%s".',INVALID_DISTANCE_SUFFIX:'Invalid distance suffix "%s". Valid suffixes are: cm, m, km, mi, yd, ft.',INVALID_SEARCH_MODE:'Invalid search mode "%s". Valid modes are: "fulltext", "vector", "hybrid".',MISSING_VECTOR_AND_SECURE_PROXY:"No vector was provided and no secure proxy was configured. Please provide a vector or configure an Orama Secure Proxy to perform hybrid search.",MISSING_TERM:'"term" is a required parameter when performing hybrid search. Please provide a search term.',INVALID_VECTOR_INPUT:'Invalid "vector" property. Expected an object with "value" and "property" properties, but got "%s" instead.',PLUGIN_CRASHED:"A plugin crashed during initialization. Please check the error message for more information:",PLUGIN_SECURE_PROXY_NOT_FOUND:`Could not find '@orama/secure-proxy-plugin' installed in your Orama instance.
Please install it before proceeding with creating an answer session.
Read more at https://docs.orama.com/open-source/plugins/plugin-secure-proxy#plugin-secure-proxy
`,PLUGIN_SECURE_PROXY_MISSING_CHAT_MODEL:`Could not find a chat model defined in the secure proxy plugin configuration.
Please provide a chat model before proceeding with creating an answer session.
Read more at https://docs.orama.com/open-source/plugins/plugin-secure-proxy#plugin-secure-proxy
`,ANSWER_SESSION_LAST_MESSAGE_IS_NOT_ASSISTANT:"The last message in the session is not an assistant message. Cannot regenerate non-assistant messages.",PLUGIN_COMPONENT_CONFLICT:'The component "%s" is already defined. The plugin "%s" is trying to redefine it.'};function P(e,...t){let r=Error(function(e,...t){return e.replace(/%(?:(?<position>\d+)\$)?(?<width>-?\d*\.?\d*)(?<type>[dfs])/g,function(...e){let{width:r,type:n,position:o}=e[e.length-1],i=o?t[Number.parseInt(o)-1]:t.shift(),l=""===r?0:Number.parseInt(r);switch(n){case"d":return i.toString().padStart(l,"0");case"f":{let e=i,[t,n]=r.split(".").map(e=>Number.parseFloat(e));return"number"==typeof n&&n>=0&&(e=e.toFixed(n)),"number"==typeof t&&t>=0?e.toString().padStart(l,"0"):e.toString()}case"s":return l<0?i.toString().padEnd(-l," "):i.toString().padStart(l," ");default:return i}})}(E[e]??`Unsupported Orama Error code: ${e}`,...t));return r.code=e,"captureStackTrace"in Error.prototype&&Error.captureStackTrace(r),r}function A(e){return{raw:Number(e),formatted:p(e)}}function R(e){if(e.id){if("string"!=typeof e.id)throw P("DOCUMENT_ID_MUST_BE_STRING",typeof e.id);return e.id}return g()}let k={string:!1,number:!1,boolean:!1,enum:!1,geopoint:!1,"string[]":!0,"number[]":!0,"boolean[]":!0,"enum[]":!0},M={"string[]":"string","number[]":"number","boolean[]":"boolean","enum[]":"enum"};function C(e){return"string"==typeof e&&/^vector\[\d+\]$/.test(e)}function j(e){return"string"==typeof e&&k[e]}function z(e){let t=Number(e.slice(7,-1));switch(!0){case isNaN(t):throw P("INVALID_VECTOR_VALUE",e);case t<=0:throw P("INVALID_VECTOR_SIZE",e);default:return t}}function L(e){return{internalIdToId:e.internalIdToId}}function U(e,t){let{internalIdToId:r}=t;e.internalDocumentIDStore.idToInternalId.clear(),e.internalDocumentIDStore.internalIdToId=[];let n=r.length;for(let t=0;t<n;t++){let n=r[t];e.internalDocumentIDStore.idToInternalId.set(n,t+1),e.internalDocumentIDStore.internalIdToId.push(n)}}function B(e,t){if("string"==typeof t){let r=e.idToInternalId.get(t);if(r)return r;let n=e.idToInternalId.size+1;return e.idToInternalId.set(t,n),e.internalIdToId.push(t),n}return t>e.internalIdToId.length?B(e,t.toString()):t}function F(e,t){if(e.internalIdToId.length<t)throw Error(`Invalid internalId ${t}`);return e.internalIdToId[t-1]}function W(e,t){return{sharedInternalDocumentStore:t,docs:{},count:0}}function V(e,t){let r=B(e.sharedInternalDocumentStore,t);return e.docs[r]}function $(e,t){let r=t.length,n=Array.from({length:r});for(let o=0;o<r;o++){let r=B(e.sharedInternalDocumentStore,t[o]);n[o]=e.docs[r]}return n}function G(e){return e.docs}function H(e,t,r,n){return void 0===e.docs[r]&&(e.docs[r]=n,e.count++,!0)}function J(e,t){let r=B(e.sharedInternalDocumentStore,t);return void 0!==e.docs[r]&&(delete e.docs[r],e.count--,!0)}function Y(e){return e.count}function K(e,t){return{docs:t.docs,count:t.count,sharedInternalDocumentStore:e}}function q(e){return{docs:e.docs,count:e.count}}let X=["beforeInsert","afterInsert","beforeRemove","afterRemove","beforeUpdate","afterUpdate","beforeUpsert","afterUpsert","beforeSearch","afterSearch","beforeInsertMultiple","afterInsertMultiple","beforeRemoveMultiple","afterRemoveMultiple","beforeUpdateMultiple","afterUpdateMultiple","beforeUpsertMultiple","afterUpsertMultiple","beforeLoad","afterLoad","afterCreate"],Z=["tokenizer","index","documentsStore","sorter"],Q=["validateSchema","getDocumentIndexId","getDocumentProperties","formatElapsedTime"];function ee(e,t,r,n,o){if(e.some(x))return(async()=>{for(let i of e)await i(t,r,n,o)})();for(let i of e)i(t,r,n,o)}function et(e,t,r,n){if(e.some(x))return(async()=>{for(let o of e)await o(t,r,n)})();for(let o of e)o(t,r,n)}class er{k;v;l=null;r=null;h=1;constructor(e,t){this.k=e,this.v=new Set(t)}updateHeight(){this.h=Math.max(er.getHeight(this.l),er.getHeight(this.r))+1}static getHeight(e){return e?e.h:0}getBalanceFactor(){return er.getHeight(this.l)-er.getHeight(this.r)}rotateLeft(){let e=this.r;return this.r=e.l,e.l=this,this.updateHeight(),e.updateHeight(),e}rotateRight(){let e=this.l;return this.l=e.r,e.r=this,this.updateHeight(),e.updateHeight(),e}toJSON(){return{k:this.k,v:Array.from(this.v),l:this.l?this.l.toJSON():null,r:this.r?this.r.toJSON():null,h:this.h}}static fromJSON(e){let t=new er(e.k,e.v);return t.l=e.l?er.fromJSON(e.l):null,t.r=e.r?er.fromJSON(e.r):null,t.h=e.h,t}}class en{root=null;insertCount=0;constructor(e,t){void 0!==e&&void 0!==t&&(this.root=new er(e,t))}insert(e,t,r=1e3){this.root=this.insertNode(this.root,e,t,r)}insertMultiple(e,t,r=1e3){for(let n of t)this.insert(e,n,r)}rebalance(){this.root&&(this.root=this.rebalanceNode(this.root))}toJSON(){return{root:this.root?this.root.toJSON():null,insertCount:this.insertCount}}static fromJSON(e){let t=new en;return t.root=e.root?er.fromJSON(e.root):null,t.insertCount=e.insertCount||0,t}insertNode(e,t,r,n){if(null===e)return new er(t,[r]);let o=[],i=e,l=null;for(;null!==i;)if(o.push({parent:l,node:i}),t<i.k)if(null===i.l){i.l=new er(t,[r]),o.push({parent:i,node:i.l});break}else l=i,i=i.l;else if(!(t>i.k))return i.v.add(r),e;else if(null===i.r){i.r=new er(t,[r]),o.push({parent:i,node:i.r});break}else l=i,i=i.r;let a=!1;this.insertCount++%n==0&&(a=!0);for(let t=o.length-1;t>=0;t--){let{parent:r,node:n}=o[t];if(n.updateHeight(),a){let t=this.rebalanceNode(n);r?r.l===n?r.l=t:r.r===n&&(r.r=t):e=t}}return e}rebalanceNode(e){let t=e.getBalanceFactor();if(t>1){if(e.l&&e.l.getBalanceFactor()>=0)return e.rotateRight();else if(e.l)return e.l=e.l.rotateLeft(),e.rotateRight()}if(t<-1){if(e.r&&0>=e.r.getBalanceFactor())return e.rotateLeft();else if(e.r)return e.r=e.r.rotateRight(),e.rotateLeft()}return e}find(e){let t=this.findNodeByKey(e);return t?t.v:null}contains(e){return null!==this.find(e)}getSize(){let e=0,t=[],r=this.root;for(;r||t.length>0;){for(;r;)t.push(r),r=r.l;r=t.pop(),e++,r=r.r}return e}isBalanced(){if(!this.root)return!0;let e=[this.root];for(;e.length>0;){let t=e.pop();if(Math.abs(t.getBalanceFactor())>1)return!1;t.l&&e.push(t.l),t.r&&e.push(t.r)}return!0}remove(e){this.root=this.removeNode(this.root,e)}removeDocument(e,t){let r=this.findNodeByKey(e);r&&(1===r.v.size?this.root=this.removeNode(this.root,e):r.v=new Set([...r.v.values()].filter(e=>e!==t)))}findNodeByKey(e){let t=this.root;for(;t;)if(e<t.k)t=t.l;else{if(!(e>t.k))return t;t=t.r}return null}removeNode(e,t){if(null===e)return null;let r=[],n=e;for(;null!==n&&n.k!==t;)r.push(n),n=t<n.k?n.l:n.r;if(null===n)return e;if(null===n.l||null===n.r){let t=n.l?n.l:n.r;if(0===r.length)e=t;else{let e=r[r.length-1];e.l===n?e.l=t:e.r=t}}else{let e=n,t=n.r;for(;null!==t.l;)e=t,t=t.l;n.k=t.k,n.v=t.v,e.l===t?e.l=t.r:e.r=t.r,n=e}r.push(n);for(let t=r.length-1;t>=0;t--){let n=r[t];n.updateHeight();let o=this.rebalanceNode(n);if(t>0){let e=r[t-1];e.l===n?e.l=o:e.r===n&&(e.r=o)}else e=o}return e}rangeSearch(e,t){let r=new Set,n=[],o=this.root;for(;o||n.length>0;){for(;o;)n.push(o),o=o.l;if((o=n.pop()).k>=e&&o.k<=t&&(r=D(r,o.v)),o.k>t)break;o=o.r}return r}greaterThan(e,t=!1){let r=new Set,n=[],o=this.root;for(;o||n.length>0;){for(;o;)n.push(o),o=o.r;if(o=n.pop(),t&&o.k>=e||!t&&o.k>e)r=D(r,o.v);else if(o.k<=e)break;o=o.l}return r}lessThan(e,t=!1){let r=new Set,n=[],o=this.root;for(;o||n.length>0;){for(;o;)n.push(o),o=o.l;if(o=n.pop(),t&&o.k<=e||!t&&o.k<e)r=D(r,o.v);else if(o.k>e)break;o=o.r}return r}}class eo{numberToDocumentId;constructor(){this.numberToDocumentId=new Map}insert(e,t){this.numberToDocumentId.has(e)?this.numberToDocumentId.get(e).add(t):this.numberToDocumentId.set(e,new Set([t]))}find(e){let t=this.numberToDocumentId.get(e);return t?Array.from(t):null}remove(e){this.numberToDocumentId.delete(e)}removeDocument(e,t){let r=this.numberToDocumentId.get(t);r&&(r.delete(e),0===r.size&&this.numberToDocumentId.delete(t))}contains(e){return this.numberToDocumentId.has(e)}getSize(){let e=0;for(let t of this.numberToDocumentId.values())e+=t.size;return e}filter(e){let t=Object.keys(e);if(1!==t.length)throw Error("Invalid operation");let r=t[0];switch(r){case"eq":{let t=e[r],n=this.numberToDocumentId.get(t);return n?Array.from(n):[]}case"in":{let t=e[r],n=new Set;for(let e of t){let t=this.numberToDocumentId.get(e);if(t)for(let e of t)n.add(e)}return Array.from(n)}case"nin":{let t=new Set(e[r]),n=new Set;for(let[e,r]of this.numberToDocumentId.entries())if(!t.has(e))for(let e of r)n.add(e);return Array.from(n)}default:throw Error("Invalid operation")}}filterArr(e){let t=Object.keys(e);if(1!==t.length)throw Error("Invalid operation");let r=t[0];switch(r){case"containsAll":{let t=e[r].map(e=>this.numberToDocumentId.get(e)??new Set);if(0===t.length)return[];return Array.from(t.reduce((e,t)=>new Set([...e].filter(e=>t.has(e)))))}case"containsAny":{let t=e[r].map(e=>this.numberToDocumentId.get(e)??new Set);if(0===t.length)return[];return Array.from(t.reduce((e,t)=>new Set([...e,...t])))}default:throw Error("Invalid operation")}}static fromJSON(e){if(!e.numberToDocumentId)throw Error("Invalid Flat Tree JSON");let t=new eo;for(let[r,n]of e.numberToDocumentId)t.numberToDocumentId.set(r,new Set(n));return t}toJSON(){return{numberToDocumentId:Array.from(this.numberToDocumentId.entries()).map(([e,t])=>[e,Array.from(t)])}}}function ei(e,t,r){let n=function(e,t,r){if(r<0)return -1;if(e===t)return 0;let n=e.length,o=t.length;if(0===n)return o<=r?o:-1;if(0===o)return n<=r?n:-1;let i=Math.abs(n-o);if(e.startsWith(t))return i<=r?i:-1;if(t.startsWith(e))return 0;if(i>r)return -1;let l=[];for(let e=0;e<=n;e++){l[e]=[e];for(let t=1;t<=o;t++)l[e][t]=0===e?t:0}for(let i=1;i<=n;i++){let n=1/0;for(let r=1;r<=o;r++)e[i-1]===t[r-1]?l[i][r]=l[i-1][r-1]:l[i][r]=Math.min(l[i-1][r]+1,l[i][r-1]+1,l[i-1][r-1]+1),n=Math.min(n,l[i][r]);if(n>r)return -1}return l[n][o]<=r?l[n][o]:-1}(e,t,r);return{distance:n,isBounded:n>=0}}class el{k;s;c=new Map;d=new Set;e;w="";constructor(e,t,r){this.k=e,this.s=t,this.e=r}updateParent(e){this.w=e.w+this.s}addDocument(e){this.d.add(e)}removeDocument(e){return this.d.delete(e)}findAllWords(e,t,r,n){let o=[this];for(;o.length>0;){let i=o.pop();if(i.e){let{w:o,d:l}=i;if(r&&o!==t)continue;if(null!==b(e,o))if(n){if(!(Math.abs(t.length-o.length)<=n)||!ei(t,o,n).isBounded)continue;e[o]=[]}else e[o]=[];if(null!=b(e,o)&&l.size>0){let t=e[o];for(let e of l)t.includes(e)||t.push(e)}}i.c.size>0&&o.push(...i.c.values())}return e}insert(e,t){let r=this,n=0,o=e.length;for(;n<o;){let i=e[n],l=r.c.get(i);if(l){let i=l.s,a=i.length,s=0;for(;s<a&&n+s<o&&i[s]===e[n+s];)s++;if(s===a){if(r=l,(n+=s)===o){l.e||(l.e=!0),l.addDocument(t);return}continue}let u=i.slice(0,s),c=i.slice(s),d=e.slice(n+s),f=new el(u[0],u,!1);if(r.c.set(u[0],f),f.updateParent(r),l.s=c,l.k=c[0],f.c.set(c[0],l),l.updateParent(f),d){let e=new el(d[0],d,!0);e.addDocument(t),f.c.set(d[0],e),e.updateParent(f)}else f.e=!0,f.addDocument(t);return}{let o=new el(i,e.slice(n),!0);o.addDocument(t),r.c.set(i,o),o.updateParent(r);return}}r.e||(r.e=!0),r.addDocument(t)}_findLevenshtein(e,t,r,n,o){let i=[{node:this,index:t,tolerance:r}];for(;i.length>0;){let{node:t,index:r,tolerance:l}=i.pop();if(t.w.startsWith(e)){t.findAllWords(o,e,!1,0);continue}if(l<0)continue;if(t.e){let{w:r,d:i}=t;if(r&&(ei(e,r,n).isBounded&&(o[r]=[]),void 0!==b(o,r)&&i.size>0)){let e=new Set(o[r]);for(let t of i)e.add(t);o[r]=Array.from(e)}}if(r>=e.length)continue;let a=e[r];if(t.c.has(a)){let e=t.c.get(a);i.push({node:e,index:r+1,tolerance:l})}for(let[e,n]of(i.push({node:t,index:r+1,tolerance:l-1}),t.c))i.push({node:n,index:r,tolerance:l-1}),e!==a&&i.push({node:n,index:r+1,tolerance:l-1})}}find(e){let{term:t,exact:r,tolerance:n}=e;if(n&&!r){let e={};return this._findLevenshtein(t,0,n,n,e),e}{let e=this,o=0,i=t.length;for(;o<i;){let l=t[o],a=e.c.get(l);if(!a)return{};{let l=a.s,s=l.length,u=0;for(;u<s&&o+u<i&&l[u]===t[o+u];)u++;if(u===s)e=a,o+=u;else{if(o+u!==i||u!==i-o||r)return{};let e={};return a.findAllWords(e,t,r,n),e}}}let l={};return e.findAllWords(l,t,r,n),l}}contains(e){let t=this,r=0,n=e.length;for(;r<n;){let o=e[r],i=t.c.get(o);if(!i)return!1;{let o=i.s,l=o.length,a=0;for(;a<l&&r+a<n&&o[a]===e[r+a];)a++;if(a<l)return!1;r+=l,t=i}}return!0}removeWord(e){if(!e)return!1;let t=this,r=e.length,n=[];for(let o=0;o<r;o++){let r=e[o];if(!t.c.has(r))return!1;{let e=t.c.get(r);n.push({parent:t,character:r}),o+=e.s.length-1,t=e}}for(t.d.clear(),t.e=!1;n.length>0&&0===t.c.size&&!t.e&&0===t.d.size;){let{parent:e,character:r}=n.pop();e.c.delete(r),t=e}return!0}removeDocumentByWord(e,t,r=!0){if(!e)return!0;let n=this,o=e.length;for(let i=0;i<o;i++){let o=e[i];if(!n.c.has(o))return!1;{let l=n.c.get(o);i+=l.s.length-1,n=l,r&&n.w!==e||n.removeDocument(t)}}return!0}static getCommonPrefix(e,t){let r=Math.min(e.length,t.length),n=0;for(;n<r&&e.charCodeAt(n)===t.charCodeAt(n);)n++;return e.slice(0,n)}toJSON(){return{w:this.w,s:this.s,e:this.e,k:this.k,d:Array.from(this.d),c:Array.from(this.c?.entries())?.map(([e,t])=>[e,t.toJSON()])}}static fromJSON(e){let t=new el(e.k,e.s,e.e);return t.w=e.w,t.d=new Set(e.d),t.c=new Map(e?.c?.map(([e,t])=>[e,el.fromJSON(t)])),t}}class ea extends el{constructor(){super("","",!1)}static fromJSON(e){let t=new ea;return t.w=e.w,t.s=e.s,t.e=e.e,t.k=e.k,t.d=new Set(e.d),t.c=new Map(e.c?.map(([e,t])=>[e,el.fromJSON(t)])),t}toJSON(){return super.toJSON()}}class es{point;docIDs;left;right;parent;constructor(e,t){this.point=e,this.docIDs=new Set(t),this.left=null,this.right=null,this.parent=null}toJSON(){return{point:this.point,docIDs:Array.from(this.docIDs),left:this.left?this.left.toJSON():null,right:this.right?this.right.toJSON():null}}static fromJSON(e,t=null){let r=new es(e.point,e.docIDs);return r.parent=t,e.left&&(r.left=es.fromJSON(e.left,r)),e.right&&(r.right=es.fromJSON(e.right,r)),r}}class eu{root;nodeMap;constructor(){this.root=null,this.nodeMap=new Map}getPointKey(e){return`${e.lon},${e.lat}`}insert(e,t){let r=this.getPointKey(e),n=this.nodeMap.get(r);if(n)return void t.forEach(e=>n.docIDs.add(e));let o=new es(e,t);if(this.nodeMap.set(r,o),null==this.root){this.root=o;return}let i=this.root,l=0;for(;;){if(0==l%2)if(e.lon<i.point.lon){if(null==i.left){i.left=o,o.parent=i;return}i=i.left}else{if(null==i.right){i.right=o,o.parent=i;return}i=i.right}else if(e.lat<i.point.lat){if(null==i.left){i.left=o,o.parent=i;return}i=i.left}else{if(null==i.right){i.right=o,o.parent=i;return}i=i.right}l++}}contains(e){let t=this.getPointKey(e);return this.nodeMap.has(t)}getDocIDsByCoordinates(e){let t=this.getPointKey(e),r=this.nodeMap.get(t);return r?Array.from(r.docIDs):null}removeDocByID(e,t){let r=this.getPointKey(e),n=this.nodeMap.get(r);n&&(n.docIDs.delete(t),0===n.docIDs.size&&(this.nodeMap.delete(r),this.deleteNode(n)))}deleteNode(e){let t=e.parent,r=e.left?e.left:e.right;r&&(r.parent=t),t?t.left===e?t.left=r:t.right===e&&(t.right=r):(this.root=r,this.root&&(this.root.parent=null))}searchByRadius(e,t,r=!0,n="asc",o=!1){let i=o?eu.vincentyDistance:eu.haversineDistance,l=[{node:this.root,depth:0}],a=[];for(;l.length>0;){let{node:n,depth:o}=l.pop();if(null==n)continue;let s=i(e,n.point);(r?s<=t:s>t)&&a.push({point:n.point,docIDs:Array.from(n.docIDs)}),null!=n.left&&l.push({node:n.left,depth:o+1}),null!=n.right&&l.push({node:n.right,depth:o+1})}return n&&a.sort((t,r)=>{let o=i(e,t.point),l=i(e,r.point);return"asc"===n.toLowerCase()?o-l:l-o}),a}searchByPolygon(e,t=!0,r=null,n=!1){let o=[{node:this.root,depth:0}],i=[];for(;o.length>0;){let{node:r,depth:n}=o.pop();if(null==r)continue;null!=r.left&&o.push({node:r.left,depth:n+1}),null!=r.right&&o.push({node:r.right,depth:n+1});let l=eu.isPointInPolygon(e,r.point);(l&&t||!l&&!t)&&i.push({point:r.point,docIDs:Array.from(r.docIDs)})}let l=eu.calculatePolygonCentroid(e);if(r){let e=n?eu.vincentyDistance:eu.haversineDistance;i.sort((t,n)=>{let o=e(l,t.point),i=e(l,n.point);return"asc"===r.toLowerCase()?o-i:i-o})}return i}toJSON(){return{root:this.root?this.root.toJSON():null}}static fromJSON(e){let t=new eu;return e.root&&(t.root=es.fromJSON(e.root),t.buildNodeMap(t.root)),t}buildNodeMap(e){if(null==e)return;let t=this.getPointKey(e.point);this.nodeMap.set(t,e),e.left&&this.buildNodeMap(e.left),e.right&&this.buildNodeMap(e.right)}static calculatePolygonCentroid(e){let t=0,r=0,n=0,o=e.length;for(let i=0,l=o-1;i<o;l=i++){let o=e[i].lon,a=e[i].lat,s=e[l].lon,u=e[l].lat,c=o*u-s*a;t+=c,r+=(o+s)*c,n+=(a+u)*c}let i=6*(t/=2);return{lon:r/=i,lat:n/=i}}static isPointInPolygon(e,t){let r=!1,n=t.lon,o=t.lat,i=e.length;for(let t=0,l=i-1;t<i;l=t++){let i=e[t].lon,a=e[t].lat,s=e[l].lon,u=e[l].lat;a>o!=u>o&&n<(s-i)*(o-a)/(u-a)+i&&(r=!r)}return r}static haversineDistance(e,t){let r=Math.PI/180,n=e.lat*r,o=t.lat*r,i=(t.lat-e.lat)*r,l=(t.lon-e.lon)*r,a=Math.sin(i/2)*Math.sin(i/2)+Math.cos(n)*Math.cos(o)*Math.sin(l/2)*Math.sin(l/2);return 2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))*6371e3}static vincentyDistance(e,t){let r,n,o,i,l,a,s,u=1/298.257223563,c=(1-1/298.257223563)*6378137,d=Math.PI/180,f=e.lat*d,h=t.lat*d,p=(t.lon-e.lon)*d,m=Math.atan((1-u)*Math.tan(f)),g=Math.atan((1-u)*Math.tan(h)),b=Math.sin(m),y=Math.cos(m),v=Math.sin(g),S=Math.cos(g),w=p,I=1e3;do{let e=Math.sin(w),t=Math.cos(w);if(0===(n=Math.sqrt(S*e*(S*e)+(y*v-b*S*t)*(y*v-b*S*t))))return 0;i=Math.atan2(n,o=b*v+y*S*t),isNaN(s=o-2*b*v/(a=1-(l=y*S*e/n)*l))&&(s=0);let c=u/16*a*(4+u*(4-3*a));r=w,w=p+(1-c)*u*l*(i+c*n*(s+c*o*(-1+2*s*s)))}while(Math.abs(w-r)>1e-12&&--I>0);if(0===I)return NaN;let x=a*(0x24ffb2985f71-c*c)/(c*c),O=1+x/16384*(4096+x*(-768+x*(320-175*x))),_=x/1024*(256+x*(-128+x*(74-47*x)));return c*O*(i-_*n*(s+_/4*(o*(-1+2*s*s)-_/6*s*(-3+4*n*n)*(-3+4*s*s))))}}class ec{true;false;constructor(){this.true=new Set,this.false=new Set}insert(e,t){t?this.true.add(e):this.false.add(e)}delete(e,t){t?this.true.delete(e):this.false.delete(e)}getSize(){return this.true.size+this.false.size}toJSON(){return{true:Array.from(this.true),false:Array.from(this.false)}}static fromJSON(e){let t=new ec;return t.true=new Set(e.true),t.false=new Set(e.false),t}}class ed{size;vectors=new Map;constructor(e){this.size=e}add(e,t){t instanceof Float32Array||(t=new Float32Array(t));let r=ef(t,this.size);this.vectors.set(e,[r,t])}remove(e){this.vectors.delete(e)}find(e,t,r){return e instanceof Float32Array||(e=new Float32Array(e)),function(e,t,r,n,o){let i=ef(e,n),l=[];for(let a of t||r.keys()){let t=r.get(a);if(!t)continue;let s=t[0],u=t[1],c=0;for(let t=0;t<n;t++)c+=e[t]*u[t];let d=c/(i*s);d>=o&&l.push([a,d])}return l}(e,r,this.vectors,this.size,t)}toJSON(){let e=[];for(let[t,[r,n]]of this.vectors)e.push([t,[r,Array.from(n)]]);return{size:this.size,vectors:e}}static fromJSON(e){let t=new ed(e.size);for(let[r,[n,o]]of e.vectors)t.vectors.set(r,[n,new Float32Array(o)]);return t}}function ef(e,t){let r=0;for(let n=0;n<t;n++)r+=e[n]*e[n];return Math.sqrt(r)}function eh(e,t,r,n,o){let i=B(e.sharedInternalDocumentStore,r);e.avgFieldLength[t]=((e.avgFieldLength[t]??0)*(o-1)+n.length)/o,e.fieldLengths[t][i]=n.length,e.frequencies[t][i]={}}function ep(e,t,r,n,o){let i=0;for(let e of n)e===o&&i++;let l=B(e.sharedInternalDocumentStore,r),a=i/n.length;e.frequencies[t][l][o]=a,o in e.tokenOccurrences[t]||(e.tokenOccurrences[t][o]=0),e.tokenOccurrences[t][o]=(e.tokenOccurrences[t][o]??0)+1}function em(e,t,r,n){let o=B(e.sharedInternalDocumentStore,r);n>1?e.avgFieldLength[t]=(e.avgFieldLength[t]*n-e.fieldLengths[t][o])/(n-1):e.avgFieldLength[t]=void 0,e.fieldLengths[t][o]=void 0,e.frequencies[t][o]=void 0}function eg(e,t,r){e.tokenOccurrences[t][r]--}function eb(e,t,r,n,o,i,l,a,s,u,c){if(C(l)){var d,f,h,p;return d=t,f=r,h=i,p=o,void d.vectorIndexes[f].node.add(p,h)}let m=n=>{let{type:i,node:l}=t.indexes[r];switch(i){case"Bool":l[n?"true":"false"].add(o);break;case"AVL":{let e=c?.avlRebalanceThreshold??1;l.insert(n,o,e);break}case"Radix":{let i=s.tokenize(n,a,r,!1);for(let n of(e.insertDocumentScoreParameters(t,r,o,i,u),i))e.insertTokenScoreParameters(t,r,o,i,n),l.insert(n,o);break}case"Flat":l.insert(n,o);break;case"BKD":l.insert(n,[o])}};if(!j(l))return m(i);let g=i.length;for(let e=0;e<g;e++)m(i[e])}function ey(e,t,r,n,o,i,l,a,s,u){if(C(l))return t.vectorIndexes[r].node.remove(o),!0;let{type:c,node:d}=t.indexes[r];switch(c){case"AVL":return d.removeDocument(i,o),!0;case"Bool":return d[i?"true":"false"].delete(o),!0;case"Radix":{let l=s.tokenize(i,a,r);for(let i of(e.removeDocumentScoreParameters(t,r,n,u),l))e.removeTokenScoreParameters(t,r,i),d.removeDocumentByWord(i,o);return!0}case"Flat":return d.removeDocument(o,i),!0;case"BKD":return d.removeDocByID(i,o),!1}}function ev(e,t,r,n,o,i,l,a,s,u){if(!j(l))return ey(e,t,r,n,o,i,l,a,s,u);let c=M[l],d=i.length;for(let l=0;l<d;l++)ey(e,t,r,n,o,i[l],c,a,s,u);return!0}function eS(e,t,r,n,o,i,l,a,s,u){let c=Array.from(n),d=e.avgFieldLength[t],f=e.fieldLengths[t],h=e.tokenOccurrences[t],p=e.frequencies[t],m="number"==typeof h[r]?h[r]??0:0,g=c.length;for(let e=0;e<g;e++){let n=c[e];if(s&&!s.has(n))continue;u.has(n)||u.set(n,new Map);let h=u.get(n);h.set(t,(h.get(t)||0)+1);let g=function(e,t,r,n,o,{k:i,b:l,d:a}){return Math.log(1+(r-t+.5)/(t+.5))*(a+e*(i+1))/(e+i*(1-l+l*n/o))}(p?.[n]?.[r]??0,m,o,f[n],d,i);l.has(n)?l.set(n,l.get(n)+g*a):l.set(n,g*a)}}function ew(e,t,r,n,o,i,l,a,s,u,c,d=0){let f=r.tokenize(t,n),h=f.length||1,p=new Map,m=new Map,g=new Map;for(let r of o){if(!(r in e.indexes))continue;let n=e.indexes[r],{type:o}=n;if("Radix"!==o)throw P("WRONG_SEARCH_PROPERTY_TYPE",r);let d=a[r]??1;if(d<=0)throw P("INVALID_BOOST_VALUE",d);0!==f.length||t||f.push("");let h=f.length;for(let t=0;t<h;t++){let o=f[t],a=n.node.find({term:o,exact:i,tolerance:l}),h=Object.keys(a);h.length>0&&m.set(o,!0);let b=h.length;for(let t=0;t<b;t++){let n=h[t],o=a[n];eS(e,r,n,o,u,s,g,d,c,p)}}}let b=Array.from(g.entries()).map(([e,t])=>[e,t]).sort((e,t)=>t[1]-e[1]);if(0===b.length)return[];if(1===d)return b;if(0===d){if(1===h)return b;for(let e of f)if(!m.get(e))return[];return b.filter(([e])=>{let t=p.get(e);return!!t&&Array.from(t.values()).some(e=>e===h)})}let y=b.filter(([e])=>{let t=p.get(e);return!!t&&Array.from(t.values()).some(e=>e===h)});if(y.length>0){let e=b.filter(([e])=>!y.some(([t])=>t===e)),t=Math.ceil(e.length*d);return[...y,...e.slice(0,t)]}return b}function eI(e){return e.searchableProperties}function ex(e){return e.searchablePropertiesWithTypes}function eO(e,t){let{indexes:r,vectorIndexes:n,searchableProperties:o,searchablePropertiesWithTypes:i,frequencies:l,tokenOccurrences:a,avgFieldLength:s,fieldLengths:u}=t,c={},d={};for(let e of Object.keys(r)){let{node:t,type:n,isArray:o}=r[e];switch(n){case"Radix":c[e]={type:"Radix",node:ea.fromJSON(t),isArray:o};break;case"Flat":c[e]={type:"Flat",node:eo.fromJSON(t),isArray:o};break;case"AVL":c[e]={type:"AVL",node:en.fromJSON(t),isArray:o};break;case"BKD":c[e]={type:"BKD",node:eu.fromJSON(t),isArray:o};break;case"Bool":c[e]={type:"Bool",node:ec.fromJSON(t),isArray:o};break;default:c[e]=r[e]}}for(let e of Object.keys(n))d[e]={type:"Vector",isArray:!1,node:ed.fromJSON(n[e])};return{sharedInternalDocumentStore:e,indexes:c,vectorIndexes:d,searchableProperties:o,searchablePropertiesWithTypes:i,frequencies:l,tokenOccurrences:a,avgFieldLength:s,fieldLengths:u}}function e_(e){let{indexes:t,vectorIndexes:r,searchableProperties:n,searchablePropertiesWithTypes:o,frequencies:i,tokenOccurrences:l,avgFieldLength:a,fieldLengths:s}=e,u={};for(let e of Object.keys(r))u[e]=r[e].node.toJSON();let c={};for(let e of Object.keys(t)){let{type:r,node:n,isArray:o}=t[e];"Flat"===r||"Radix"===r||"AVL"===r||"BKD"===r||"Bool"===r?c[e]={type:r,node:n.toJSON(),isArray:o}:(c[e]=t[e],c[e].node=c[e].node.toJSON())}return{indexes:c,vectorIndexes:u,searchableProperties:n,searchablePropertiesWithTypes:o,frequencies:i,tokenOccurrences:l,avgFieldLength:a,fieldLengths:s}}function eN(e,t){e||(e=new Set);let r=t.length;for(let n=0;n<r;n++){let r=t[n].docIDs,o=r.length;for(let t=0;t<o;t++)e.add(r[t])}return e}function eD(e,t,r,n){return n?.enabled===!1?{disabled:!0}:function e(t,r,n,o,i){let l={language:t.tokenizer.language,sharedInternalDocumentStore:r,enabled:!0,isSorted:!0,sortableProperties:[],sortablePropertiesWithTypes:{},sorts:{}};for(let[a,s]of Object.entries(n)){let n=`${i}${i?".":""}${a}`;if(!o.includes(n)){if("object"==typeof s&&!Array.isArray(s)){let i=e(t,r,s,o,n);f(l.sortableProperties,i.sortableProperties),l.sorts={...l.sorts,...i.sorts},l.sortablePropertiesWithTypes={...l.sortablePropertiesWithTypes,...i.sortablePropertiesWithTypes};continue}if(!C(s))switch(s){case"boolean":case"number":case"string":l.sortableProperties.push(n),l.sortablePropertiesWithTypes[n]=s,l.sorts[n]={docs:new Map,orderedDocsToRemove:new Map,orderedDocs:[],type:s};break;case"geopoint":case"enum":case"enum[]":case"boolean[]":case"number[]":case"string[]":continue;default:throw P("INVALID_SORT_SCHEMA_TYPE",Array.isArray(s)?"array":s,n)}}}return l}(e,t,r,(n||{}).unsortableProperties||[],"")}function eT(e,t,r,n){if(!e.enabled)return;e.isSorted=!1;let o=B(e.sharedInternalDocumentStore,r),i=e.sorts[t];i.orderedDocsToRemove.has(o)&&ek(e,t),i.docs.set(o,i.orderedDocs.length),i.orderedDocs.push([o,n])}function eE(e){if(!e.isSorted&&e.enabled){for(let t of Object.keys(e.sorts))!function(e,t){let r,n=e.sorts[t];switch(n.type){case"string":r=eP.bind(null,e.language);break;case"number":r=eA.bind(null);break;case"boolean":r=eR.bind(null)}n.orderedDocs.sort(r);let o=n.orderedDocs.length;for(let e=0;e<o;e++){let t=n.orderedDocs[e][0];n.docs.set(t,e)}}(e,t);e.isSorted=!0}}function eP(e,t,r){return t[1].localeCompare(r[1],void 0!==e&&i.includes(e)?n[e]:void 0)}function eA(e,t){return e[1]-t[1]}function eR(e,t){return t[1]?-1:1}function ek(e,t){let r=e.sorts[t];r.orderedDocsToRemove.size&&(r.orderedDocs=r.orderedDocs.filter(e=>!r.orderedDocsToRemove.has(e[0])),r.orderedDocsToRemove.clear())}function eM(e,t,r){if(!e.enabled)return;let n=e.sorts[t],o=B(e.sharedInternalDocumentStore,r);n.docs.get(o)&&(n.docs.delete(o),n.orderedDocsToRemove.set(o,!0))}function eC(e,t,r){if(!e.enabled)throw P("SORT_DISABLED");let n=r.property,o="DESC"===r.order,i=e.sorts[n];if(!i)throw P("UNABLE_TO_SORT_ON_UNKNOWN_FIELD",n,e.sortableProperties.join(", "));return ek(e,n),eE(e),t.sort((t,r)=>{let n=i.docs.get(B(e.sharedInternalDocumentStore,t[0])),l=i.docs.get(B(e.sharedInternalDocumentStore,r[0])),a=void 0!==n,s=void 0!==l;return a||s?a?s?o?l-n:n-l:-1:1:0}),t}function ej(e){return e.enabled?e.sortableProperties:[]}function ez(e){return e.enabled?e.sortablePropertiesWithTypes:{}}function eL(e,t){if(!t.enabled)return{enabled:!1};let r=Object.keys(t.sorts).reduce((e,r)=>{let{docs:n,orderedDocs:o,type:i}=t.sorts[r];return e[r]={docs:new Map(Object.entries(n).map(([e,t])=>[+e,t])),orderedDocsToRemove:new Map,orderedDocs:o,type:i},e},{});return{sharedInternalDocumentStore:e,language:t.language,sortableProperties:t.sortableProperties,sortablePropertiesWithTypes:t.sortablePropertiesWithTypes,sorts:r,enabled:!0,isSorted:t.isSorted}}function eU(e){if(!e.enabled)return{enabled:!1};for(let t of Object.keys(e.sorts))ek(e,t);eE(e);let t=Object.keys(e.sorts).reduce((t,r)=>{let{docs:n,orderedDocs:o,type:i}=e.sorts[r];return t[r]={docs:Object.fromEntries(n.entries()),orderedDocs:o,type:i},t},{});return{language:e.language,sortableProperties:e.sortableProperties,sortablePropertiesWithTypes:e.sortablePropertiesWithTypes,sorts:t,enabled:e.enabled,isSorted:e.isSorted}}let eB=[65,65,65,65,65,65,65,67,69,69,69,69,73,73,73,73,69,78,79,79,79,79,79,null,79,85,85,85,85,89,80,115,97,97,97,97,97,97,97,99,101,101,101,101,105,105,105,105,101,110,111,111,111,111,111,null,111,117,117,117,117,121,112,121,65,97,65,97,65,97,67,99,67,99,67,99,67,99,68,100,68,100,69,101,69,101,69,101,69,101,69,101,71,103,71,103,71,103,71,103,72,104,72,104,73,105,73,105,73,105,73,105,73,105,73,105,74,106,75,107,107,76,108,76,108,76,108,76,108,76,108,78,110,78,110,78,110,110,78,110,79,111,79,111,79,111,79,111,82,114,82,114,82,114,83,115,83,115,83,115,83,115,84,116,84,116,84,116,85,117,85,117,85,117,85,117,85,117,85,117,87,119,89,121,89,90,122,90,122,90,122,115],eF={ational:"ate",tional:"tion",enci:"ence",anci:"ance",izer:"ize",bli:"ble",alli:"al",entli:"ent",eli:"e",ousli:"ous",ization:"ize",ation:"ate",ator:"ate",alism:"al",iveness:"ive",fulness:"ful",ousness:"ous",aliti:"al",iviti:"ive",biliti:"ble",logi:"log"},eW={icate:"ic",ative:"",alize:"al",iciti:"ic",ical:"ic",ful:"",ness:""},eV="[aeiouy]",e$="[^aeiou][^aeiouy]*",eG=eV+"[aeiou]*",eH="^("+e$+")?"+eG+e$,eJ="^("+e$+")?"+eG+e$+"("+eG+")?$",eY="^("+e$+")?"+eG+e$+eG+e$,eK="^("+e$+")?"+eV;function eq(e){let t,r,n,o,i,l;if(e.length<3)return e;let a=e.substring(0,1);if("y"==a&&(e=a.toUpperCase()+e.substring(1)),o=/^(.+?)([^s])s$/,(n=/^(.+?)(ss|i)es$/).test(e)?e=e.replace(n,"$1$2"):o.test(e)&&(e=e.replace(o,"$1$2")),o=/^(.+?)(ed|ing)$/,(n=/^(.+?)eed$/).test(e)){let t=n.exec(e);(n=new RegExp(eH)).test(t[1])&&(n=/.$/,e=e.replace(n,""))}else o.test(e)&&(t=o.exec(e)[1],(o=new RegExp(eK)).test(t)&&(e=t,o=/(at|bl|iz)$/,i=RegExp("([^aeiouylsz])\\1$"),l=RegExp("^"+e$+eV+"[^aeiouwxy]$"),o.test(e)?e+="e":i.test(e)?(n=/.$/,e=e.replace(n,"")):l.test(e)&&(e+="e")));if((n=/^(.+?)y$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(eK),t&&n.test(t)&&(e=t+"i")}if((n=/^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/).test(e)){let o=n.exec(e);t=o?.[1],r=o?.[2],n=new RegExp(eH),t&&n.test(t)&&(e=t+eF[r])}if((n=/^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/).test(e)){let o=n.exec(e);t=o?.[1],r=o?.[2],n=new RegExp(eH),t&&n.test(t)&&(e=t+eW[r])}if(o=/^(.+?)(s|t)(ion)$/,(n=/^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(eY),t&&n.test(t)&&(e=t)}else if(o.test(e)){let r=o.exec(e);t=r?.[1]??""+r?.[2]??"",(o=new RegExp(eY)).test(t)&&(e=t)}if((n=/^(.+?)e$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(eY),o=new RegExp(eJ),i=RegExp("^"+e$+eV+"[^aeiouwxy]$"),t&&(n.test(t)||o.test(t)&&!i.test(t))&&(e=t)}return n=/ll$/,o=new RegExp(eY),n.test(e)&&o.test(e)&&(n=/.$/,e=e.replace(n,"")),"y"==a&&(e=a.toLowerCase()+e.substring(1)),e}function eX(e,t,r=!0){let n=`${this.language}:${e}:${t}`;return r&&this.normalizationCache.has(n)?this.normalizationCache.get(n):this.stopWords?.includes(t)?(r&&this.normalizationCache.set(n,""),""):(this.stemmer&&!this.stemmerSkipProperties.has(e)&&(t=this.stemmer(t)),t=function(e){let t=[];for(let n=0;n<e.length;n++){var r;t[n]=(r=e.charCodeAt(n))<192||r>383?r:eB[r-192]||r}return String.fromCharCode(...t)}(t),r&&this.normalizationCache.set(n,t),t)}function eZ(e,t,r,n=!0){let i;if(t&&t!==this.language)throw P("LANGUAGE_NOT_SUPPORTED",t);if("string"!=typeof e)return[e];let l=this.normalizeToken.bind(this,r??"");if(r&&this.tokenizeSkipProperties.has(r))i=[l(e,n)];else{let t=o[this.language];i=e.toLowerCase().split(t).map(e=>l(e,n)).filter(Boolean)}let a=function(e){for(;""===e[e.length-1];)e.pop();for(;""===e[0];)e.shift();return e}(i);return this.allowDuplicates?a:Array.from(new Set(a))}function eQ(e={}){let t,r;if(e.language){if(!i.includes(e.language))throw P("LANGUAGE_NOT_SUPPORTED",e.language)}else e.language="english";if(e.stemming||e.stemmer&&!("stemming"in e))if(e.stemmer){if("function"!=typeof e.stemmer)throw P("INVALID_STEMMER_FUNCTION_TYPE");t=e.stemmer}else if("english"===e.language)t=eq;else throw P("MISSING_STEMMER",e.language);if(!1!==e.stopWords){if(r=[],Array.isArray(e.stopWords))r=e.stopWords;else if("function"==typeof e.stopWords)r=e.stopWords(r);else if(e.stopWords)throw P("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY");if(!Array.isArray(r))throw P("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY");for(let e of r)if("string"!=typeof e)throw P("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY")}let n={tokenize:eZ,language:e.language,stemmer:t,stemmerSkipProperties:new Set(e.stemmerSkipProperties?[e.stemmerSkipProperties].flat():[]),tokenizeSkipProperties:new Set(e.tokenizeSkipProperties?[e.tokenizeSkipProperties].flat():[]),stopWords:r,allowDuplicates:!!e.allowDuplicates,normalizeToken:eX,normalizationCache:new Map};return n.tokenize=eZ.bind(n),n.normalizeToken=eX,n}function e0({schema:e,sort:t,language:r,components:n,id:o,plugins:i}){for(let t of(n||(n={}),i??[])){if(!("getComponents"in t)||"function"!=typeof t.getComponents)continue;let r=t.getComponents(e);for(let e of Object.keys(r))if(n[e])throw P("PLUGIN_COMPONENT_CONFLICT",e,t.name);n={...n,...r}}o||(o=g());let l=n.tokenizer,a=n.index,s=n.documentsStore,u=n.sorter;if(l=l?l.tokenize?l:eQ(l):eQ({language:r??"english"}),n.tokenizer&&r)throw P("NO_LANGUAGE_WITH_CUSTOM_TOKENIZER");let c={idToInternalId:new Map,internalIdToId:[],save:L,load:U};a||={create:function e(t,r,n,o,i=""){for(let[l,a]of(o||(o={sharedInternalDocumentStore:r,indexes:{},vectorIndexes:{},searchableProperties:[],searchablePropertiesWithTypes:{},frequencies:{},tokenOccurrences:{},avgFieldLength:{},fieldLengths:{}}),Object.entries(n))){let n=`${i}${i?".":""}${l}`;if("object"==typeof a&&!Array.isArray(a)){e(t,r,a,o,n);continue}if(C(a))o.searchableProperties.push(n),o.searchablePropertiesWithTypes[n]=a,o.vectorIndexes[n]={type:"Vector",node:new ed(z(a)),isArray:!1};else{let e=/\[/.test(a);switch(a){case"boolean":case"boolean[]":o.indexes[n]={type:"Bool",node:new ec,isArray:e};break;case"number":case"number[]":o.indexes[n]={type:"AVL",node:new en(0,[]),isArray:e};break;case"string":case"string[]":o.indexes[n]={type:"Radix",node:new ea,isArray:e},o.avgFieldLength[n]=0,o.frequencies[n]={},o.tokenOccurrences[n]={},o.fieldLengths[n]={};break;case"enum":case"enum[]":o.indexes[n]={type:"Flat",node:new eo,isArray:e};break;case"geopoint":o.indexes[n]={type:"BKD",node:new eu,isArray:e};break;default:throw P("INVALID_SCHEMA_TYPE",Array.isArray(a)?"array":a,n)}o.searchableProperties.push(n),o.searchablePropertiesWithTypes[n]=a}}return o},insert:eb,remove:ev,insertDocumentScoreParameters:eh,insertTokenScoreParameters:ep,removeDocumentScoreParameters:em,removeTokenScoreParameters:eg,calculateResultScores:eS,search:ew,searchByWhereClause:function e(t,r,n,o){if("and"in n&&n.and&&Array.isArray(n.and)){let i=n.and;return 0===i.length?new Set:_(...i.map(n=>e(t,r,n,o)))}if("or"in n&&n.or&&Array.isArray(n.or)){let i=n.or;return 0===i.length?new Set:i.map(n=>e(t,r,n,o)).reduce((e,t)=>D(e,t),new Set)}if("not"in n&&n.not){let i=n.not,l=new Set,a=t.sharedInternalDocumentStore;for(let e=1;e<=a.internalIdToId.length;e++)l.add(e);let s=e(t,r,i,o),u=new Set;for(let e of l)s.has(e)||u.add(e);return u}let i=Object.keys(n),l=i.reduce((e,t)=>({[t]:new Set,...e}),{});for(let e of i){let i=n[e];if(void 0===t.indexes[e])throw P("UNKNOWN_FILTER_PROPERTY",e);let{node:a,type:s,isArray:u}=t.indexes[e];if("Bool"===s){let t=i?a.true:a.false;l[e]=D(l[e],t);continue}if("BKD"===s){let t;if("radius"in i)t="radius";else if("polygon"in i)t="polygon";else throw Error(`Invalid operation ${i}`);if("radius"===t){let{value:r,coordinates:n,unit:o="m",inside:s=!0,highPrecision:u=!1}=i[t],c=function(e,t){let r=w[t];if(void 0===r)throw Error(P("INVALID_DISTANCE_SUFFIX",e).message);return e*r}(r,o),d=a.searchByRadius(n,c,s,void 0,u);l[e]=eN(l[e],d)}else{let{coordinates:r,inside:n=!0,highPrecision:o=!1}=i[t],s=a.searchByPolygon(r,n,void 0,o);l[e]=eN(l[e],s)}continue}if("Radix"===s&&("string"==typeof i||Array.isArray(i))){for(let t of[i].flat())for(let n of r.tokenize(t,o,e)){let t=a.find({term:n,exact:!0});l[e]=function(e,t){e||(e=new Set);let r=Object.keys(t),n=r.length;for(let o=0;o<n;o++){let n=t[r[o]],i=n.length;for(let t=0;t<i;t++)e.add(n[t])}return e}(l[e],t)}continue}let c=Object.keys(i);if(c.length>1)throw P("INVALID_FILTER_OPERATION",c.length);if("Flat"===s){let t=new Set(u?a.filterArr(i):a.filter(i));l[e]=D(l[e],t);continue}if("AVL"===s){let t,r=c[0],n=i[r];switch(r){case"gt":t=a.greaterThan(n,!1);break;case"gte":t=a.greaterThan(n,!0);break;case"lt":t=a.lessThan(n,!1);break;case"lte":t=a.lessThan(n,!0);break;case"eq":t=a.find(n)??new Set;break;case"between":{let[e,r]=n;t=a.rangeSearch(e,r);break}default:throw P("INVALID_FILTER_OPERATION",r)}l[e]=D(l[e],t)}}return _(...Object.values(l))},getSearchableProperties:eI,getSearchablePropertiesWithTypes:ex,load:eO,save:e_},u||={create:eD,insert:eT,remove:eM,save:eU,load:eL,sortBy:eC,getSortableProperties:ej,getSortablePropertiesWithTypes:ez},s||={create:W,get:V,getMultiple:$,getAll:G,store:H,remove:J,count:Y,load:K,save:q};var d=n;let f={formatElapsedTime:A,getDocumentIndexId:R,getDocumentProperties:v,validateSchema:function e(t,r){for(let[n,o]of Object.entries(r)){let r=t[n];if(void 0!==r&&("geopoint"!==o||"object"!=typeof r||"number"!=typeof r.lon||"number"!=typeof r.lat)&&("enum"!==o||"string"!=typeof r&&"number"!=typeof r)){if("enum[]"===o&&Array.isArray(r)){let e=r.length;for(let t=0;t<e;t++)if("string"!=typeof r[t]&&"number"!=typeof r[t])return n+"."+t;continue}if(C(o)){let e=z(o);if(!Array.isArray(r)||r.length!==e)throw P("INVALID_INPUT_VECTOR",n,e,r.length);continue}if(j(o)){if(!Array.isArray(r))return n;let e=M[o],t=r.length;for(let o=0;o<t;o++)if(typeof r[o]!==e)return n+"."+o;continue}if("object"==typeof o){if(!r||"object"!=typeof r)return n;let t=e(r,o);if(t)return n+"."+t;continue}if(typeof r!==o)return n}}}};for(let e of Q)if(d[e]){if("function"!=typeof d[e])throw P("COMPONENT_MUST_BE_FUNCTION",e)}else d[e]=f[e];for(let e of Object.keys(d))if(!Z.includes(e)&&!Q.includes(e))throw P("UNSUPPORTED_COMPONENT",e);let{getDocumentProperties:h,getDocumentIndexId:p,validateSchema:m,formatElapsedTime:b}=n,y={data:{},caches:{},schema:e,tokenizer:l,index:a,sorter:u,documentsStore:s,internalDocumentIDStore:c,getDocumentProperties:h,getDocumentIndexId:p,validateSchema:m,beforeInsert:[],afterInsert:[],beforeRemove:[],afterRemove:[],beforeUpdate:[],afterUpdate:[],beforeUpsert:[],afterUpsert:[],beforeSearch:[],afterSearch:[],beforeInsertMultiple:[],afterInsertMultiple:[],beforeRemoveMultiple:[],afterRemoveMultiple:[],beforeUpdateMultiple:[],afterUpdateMultiple:[],beforeUpsertMultiple:[],afterUpsertMultiple:[],afterCreate:[],formatElapsedTime:b,id:o,plugins:i,version:"{{VERSION}}"};for(let r of(y.data={index:y.index.create(y,c,e),docs:y.documentsStore.create(y,c),sorting:y.sorter.create(y,c,e,t)},X))y[r]=(y[r]??[]).concat(function(e,t){let r=[],n=e.plugins?.length;if(!n)return r;for(let o=0;o<n;o++)try{let n=e.plugins[o];"function"==typeof n[t]&&r.push(n[t])}catch(e){throw console.error("Caught error in getAllPluginsByHook:",e),P("PLUGIN_CRASHED")}return r}(y,r));let S=y.afterCreate;return S&&function(e,t){if(e.some(x))return(async()=>{for(let r of e)await r(t)})();for(let r of e)r(t)}(S,y),y}function e1(e,t){return e.documentsStore.get(e.data.docs,t)}let e2="fulltext";function e5(e,t){return e[1]-t[1]}function e9(e,t){return t[1]-e[1]}function e8(e,t,r){let n={},o=t.map(([e])=>e),i=e.documentsStore.getMultiple(e.data.docs,o),l=Object.keys(r),a=e.index.getSearchablePropertiesWithTypes(e.data.index);for(let e of l){let t;if("number"===a[e]){let{ranges:n}=r[e],o=n.length,i=Array.from({length:o});for(let e=0;e<o;e++){let t=n[e];i[e]=[`${t.from}-${t.to}`,0]}t=Object.fromEntries(i)}n[e]={count:0,values:t??{}}}let s=i.length;for(let e=0;e<s;e++){let t=i[e];for(let e of l){let o=e.includes(".")?S(t,e):t[e],i=a[e],l=n[e].values;switch(i){case"number":e3(r[e].ranges,l)(o);break;case"number[]":{let t=new Set,n=e3(r[e].ranges,l,t);for(let e of o)n(e);break}case"boolean":case"enum":case"string":e6(l,i)(o);break;case"boolean[]":case"enum[]":case"string[]":{let e=e6(l,"boolean[]"===i?"boolean":"string",new Set);for(let t of o)e(t);break}default:throw P("FACET_NOT_SUPPORTED",i)}}}for(let e of l){let t=n[e];if(t.count=Object.keys(t.values).length,"string"===a[e]){let n=r[e],o=function(e="desc"){return"asc"===e.toLowerCase()?e5:e9}(n.sort);t.values=Object.fromEntries(Object.entries(t.values).sort(o).slice(n.offset??0,n.limit??10))}}return n}function e3(e,t,r){return n=>{for(let o of e){let e=`${o.from}-${o.to}`;!r?.has(e)&&n>=o.from&&n<=o.to&&(void 0===t[e]?t[e]=1:(t[e]++,r?.add(e)))}}}function e6(e,t,r){let n="boolean"===t?"false":"";return t=>{let o=t?.toString()??n;r?.has(o)||(e[o]=(e[o]??0)+1,r?.add(o))}}let e7={reducer:(e,t,r,n)=>(t[n]=r,t),getInitialValue:e=>Array.from({length:e})},e4=["string","number","boolean"];function te(e,t,r){let n=r.properties,o=n.length,i=e.index.getSearchablePropertiesWithTypes(e.data.index);for(let e=0;e<o;e++){let t=n[e];if(void 0===i[t])throw P("UNKNOWN_GROUP_BY_PROPERTY",t);if(!e4.includes(i[t]))throw P("INVALID_GROUP_BY_PROPERTY",t,e4.join(", "),i[t])}let l=t.map(([t])=>F(e.internalDocumentIDStore,t)),a=e.documentsStore.getMultiple(e.data.docs,l),s=a.length,u=r.maxResult||Number.MAX_SAFE_INTEGER,c=[],d={};for(let e=0;e<o;e++){let t=n[e],r={property:t,perValue:{}},o=new Set;for(let e=0;e<s;e++){let n=S(a[e],t);if(void 0===n)continue;let i="boolean"!=typeof n?n:""+n,l=r.perValue[i]??{indexes:[],count:0};l.count>=u||(l.indexes.push(e),l.count++,r.perValue[i]=l,o.add(n))}c.push(Array.from(o)),d[t]=r}let h=function e(t,r=0){if(r+1===t.length)return t[r].map(e=>[e]);let n=t[r],o=e(t,r+1),i=[];for(let e of n)for(let t of o){let r=[e];f(r,t),i.push(r)}return i}(c),p=h.length,m=[];for(let e=0;e<p;e++){let t=h[e],r=t.length,o={values:[],indexes:[]},i=[];for(let e=0;e<r;e++){let r=t[e],l=n[e];i.push(d[l].perValue["boolean"!=typeof r?r:""+r].indexes),o.values.push(r)}o.indexes=(function(e){if(0===e.length)return[];if(1===e.length)return e[0];for(let t=1;t<e.length;t++)if(e[t].length<e[0].length){let r=e[0];e[0]=e[t],e[t]=r}let t=new Map;for(let r of e[0])t.set(r,1);for(let r=1;r<e.length;r++){let n=0;for(let o of e[r]){let e=t.get(o);e===r&&(t.set(o,e+1),n++)}if(0===n)return[]}return e[0].filter(r=>{let n=t.get(r);return void 0!==n&&t.set(r,0),n===e.length})})(i).sort((e,t)=>e-t),0!==o.indexes.length&&m.push(o)}let g=m.length,b=Array.from({length:g});for(let e=0;e<g;e++){let n=m[e],o=r.reduce||e7,i=n.indexes.map(e=>({id:l[e],score:t[e][1],document:a[e]})),s=o.reducer.bind(null,n.values),u=o.getInitialValue(n.indexes.length),c=i.reduce(s,u);b[e]={values:n.values,result:c}}return b}function tt(e,t,r){let n,o,{term:i,properties:l}=t,a=e.data.index,s=e.caches.propertiesToSearch;if(!s){let t=e.index.getSearchablePropertiesWithTypes(a);s=(s=e.index.getSearchableProperties(a)).filter(e=>t[e].startsWith("string")),e.caches.propertiesToSearch=s}if(l&&"*"!==l){for(let e of l)if(!s.includes(e))throw P("UNKNOWN_INDEX",e,s.join(", "));s=s.filter(e=>l.includes(e))}Object.keys(t.where??{}).length>0&&(n=e.index.searchByWhereClause(a,e.tokenizer,t.where,r));let u=void 0!==t.threshold&&null!==t.threshold?t.threshold:1;if(i||l){let l=e.documentsStore.count(e.data.docs);o=e.index.search(a,i||"",e.tokenizer,r,s,t.exact||!1,t.tolerance||0,t.boost||{},function(e){let t=e??{};return t.k=t.k??tr.k,t.b=t.b??tr.b,t.d=t.d??tr.d,t}(t.relevance),l,n,u)}else o=(n?Array.from(n):Object.keys(e.documentsStore.getAll(e.data.docs))).map(e=>[+e,0]);return o}let tr={k:1.2,b:.75,d:.5};function tn(e,t,r){let n,o=t.vector;if(o&&(!("value"in o)||!("property"in o)))throw P("INVALID_VECTOR_INPUT",Object.keys(o).join(", "));let i=e.data.index.vectorIndexes[o.property],l=i.node.size;if(o?.value.length!==l){if(o?.property===void 0||o?.value.length===void 0)throw P("INVALID_INPUT_VECTOR","undefined",l,"undefined");throw P("INVALID_INPUT_VECTOR",o.property,l,o.value.length)}let a=e.data.index;return Object.keys(t.where??{}).length>0&&(n=e.index.searchByWhereClause(a,e.tokenizer,t.where,r)),i.node.find(o.value,t.similarity??.8,n)}function to(e){return e[1]}function ti(e,t,r){let n=t.mode??e2;if(n===e2){let n=m();function o(){let o,i=Object.keys(e.data.index.vectorIndexes),l=t.facets&&Object.keys(t.facets).length>0,{limit:a=10,offset:s=0,distinctOn:u,includeVectors:c=!1}=t,d=!0===t.preflight,f=tt(e,t,r);if(t.sortBy)if("function"==typeof t.sortBy){let r=f.map(([e])=>e),n=e.documentsStore.getMultiple(e.data.docs,r).map((e,t)=>[f[t][0],f[t][1],e]);n.sort(t.sortBy),f=n.map(([e,t])=>[e,t])}else f=e.sorter.sortBy(e.data.sorting,f,t.sortBy).map(([t,r])=>[B(e.internalDocumentIDStore,t),r]);else f=f.sort(y);d||(o=u?function(e,t,r,n,o){let i=e.data.docs,l=new Map,a=[],s=new Set,u=t.length,c=0;for(let d=0;d<u;d++){let u=t[d];if(void 0===u)continue;let[f,h]=u;if(s.has(f))continue;let p=e.documentsStore.get(i,f),m=S(p,o);if(!(void 0===m||l.has(m))&&(l.set(m,!0),!(++c<=r)&&(a.push({id:F(e.internalDocumentIDStore,f),score:h,document:p}),s.add(f),c>=r+n)))break}return a}(e,f,s,a,u):tl(e,f,s,a));let h={elapsed:{formatted:"",raw:0},hits:[],count:f.length};return void 0!==o&&(h.hits=o.filter(Boolean),c||I(h,i)),l&&(h.facets=e8(e,f,t.facets)),t.groupBy&&(h.groups=te(e,f,t.groupBy)),h.elapsed=e.formatElapsedTime(m()-n),h}async function i(){e.beforeSearch&&await et(e.beforeSearch,e,t,r);let n=o();return e.afterSearch&&await ee(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?i():o()}if("vector"===n)return function(e,t,r="english"){let n=m();function o(){let o=tn(e,t,r).sort(y),i=[];t.facets&&Object.keys(t.facets).length>0&&(i=e8(e,o,t.facets));let l=t.vector.property,a=t.includeVectors??!1,s=t.limit??10,u=t.offset??0,c=Array.from({length:s});for(let t=0;t<s;t++){let r=o[t+u];if(!r)break;let n=e.data.docs.docs[r[0]];if(n){a||(n[l]=null);let o={id:F(e.internalDocumentIDStore,r[0]),score:r[1],document:n};c[t]=o}}let d=[];t.groupBy&&(d=te(e,o,t.groupBy));let f=m()-n;return{count:o.length,hits:c.filter(Boolean),elapsed:{raw:Number(f),formatted:p(f)},...i?{facets:i}:{},...d?{groups:d}:{}}}async function i(){e.beforeSearch&&await et(e.beforeSearch,e,t,r);let n=o();return e.afterSearch&&await ee(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?i():o()}(e,t);if("hybrid"===n)return function(e,t,r){let n=m();function o(){let r,o,i=function(e,t,r){let n=function(e){let t=Math.max.apply(Math,e.map(to));return e.map(([e,r])=>[e,r/t])}(tt(e,t,r)),o=tn(e,t,r),i=t.hybridWeights;return function(e,t,r,n){let o=Math.max.apply(Math,e.map(to)),i=Math.max.apply(Math,t.map(to)),{text:l,vector:a}=n&&n.text&&n.vector?n:{text:.5,vector:.5},s=new Map,u=e.length,c=(e,t)=>e*l+t*a;for(let t=0;t<u;t++){let[r,n]=e[t],i=c(n/o,0);s.set(r,i)}let d=t.length;for(let e=0;e<d;e++){let[r,n]=t[e],o=n/i,l=s.get(r)??0;s.set(r,l+c(0,o))}return[...s].sort((e,t)=>t[1]-e[1])}(n,o,t.term??"",i)}(e,t,void 0);t.facets&&Object.keys(t.facets).length>0&&(r=e8(e,i,t.facets)),t.groupBy&&(o=te(e,i,t.groupBy));let l=tl(e,i,t.offset??0,t.limit??10).filter(Boolean),a=m(),s={count:i.length,elapsed:{raw:Number(a-n),formatted:p(a-n)},hits:l,...r?{facets:r}:{},...o?{groups:o}:{}};return t.includeVectors||I(s,Object.keys(e.data.index.vectorIndexes)),s}async function i(){e.beforeSearch&&await et(e.beforeSearch,e,t,void 0);let n=o();return e.afterSearch&&await ee(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?i():o()}(e,t);throw P("INVALID_SEARCH_MODE",n)}function tl(e,t,r,n){let o=e.data.docs,i=Array.from({length:n}),l=new Set;for(let a=r;a<n+r;a++){let r=t[a];if(void 0===r)break;let[n,s]=r;if(!l.has(n)){let t=e.documentsStore.get(o,n);i[a]={id:F(e.internalDocumentIDStore,n),score:s,document:t},l.add(n)}}return i}function ta(e,t){e.internalDocumentIDStore.load(e,t.internalDocumentIDStore),e.data.index=e.index.load(e.internalDocumentIDStore,t.index),e.data.docs=e.documentsStore.load(e.internalDocumentIDStore,t.docs),e.data.sorting=e.sorter.load(e.internalDocumentIDStore,t.sorting),e.tokenizer.language=t.language}Symbol("orama.insertions"),Symbol("orama.removals")},3834:(e,t,r)=>{"use strict";r.d(t,{u4:()=>w});var n=r(5155),o=r(1362),i=r(2115),l=r(4315),a=r(263),s=r(8169),u=r(8265),c=r(9697),d=r(344);let f=(0,i.lazy)(()=>r.e(702).then(r.bind(r,5702)));function h(e){let{children:t,dir:r="ltr",theme:i={},search:u,i18n:c}=e,d=t;return(null==u?void 0:u.enabled)!==!1&&(d=(0,n.jsx)(s.YL,{SearchDialog:f,...u,children:d})),(null==i?void 0:i.enabled)!==!1&&(d=(0,n.jsx)(o.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,...i,children:d})),c&&(d=(0,n.jsx)(p,{...c,children:d})),(0,n.jsx)(l.FX,{dir:r,children:(0,n.jsx)(a.G,{children:d})})}function p(e){let{locales:t=[],locale:r,onLocaleChange:o,...l}=e,a=(0,d.rd)(),s=(0,d.a8)(),f=(0,u.J)(e=>{if(o)return o(e);let t=s.split("/").filter(e=>e.length>0);t[0]!==r?t.unshift(e):t[0]=e,a.push("/".concat(t.join("/"))),a.refresh()});return(0,n.jsx)(c.I18nContext.Provider,{value:(0,i.useMemo)(()=>({locale:r,locales:t,text:{...c.defaultTranslations,...l.translations},onChange:f}),[r,t,f,l.translations]),children:l.children})}var m=r(7429);r(7505);var g=r(8999),b=r(6874),y=r(1469),v=r.n(y);function S(e){let{children:t}=e;return(0,n.jsx)(m.Uy,{usePathname:g.usePathname,useRouter:g.useRouter,useParams:g.useParams,Link:b,Image:v(),children:t})}function w(e){return(0,n.jsx)(S,{children:(0,n.jsx)(h,{...e,children:e.children})})}r(8693),r(1339)},5029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(2115),o=n.useLayoutEffect,i=n.useEffect;function l(e){let{headManager:t,reduceComponentsToState:r}=e;function l(){if(t&&t.mountedInstances){let o=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(o,e))}}return o(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=l),()=>{t&&(t._pendingUpdate=l)})),i(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:o,blurDataURL:i,objectFit:l}=e,a=n?40*n:t,s=o?40*o:r,u=a&&s?"viewBox='0 0 "+a+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},5564:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},defaultHead:function(){return d}});let n=r(8229),o=r(6966),i=r(5155),l=o._(r(2115)),a=n._(r(5029)),s=r(2464),u=r(2830),c=r(7544);function d(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===l.default.Fragment?e.concat(l.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(3230);let h=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(d(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let i=!0,l=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){l=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?i=!1:t.add(o.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!l)&&r.has(e)?i=!1:(r.add(e),n[t]=r)}}}return i}}()).reverse().map((e,t)=>{let r=e.key||t;return l.default.cloneElement(e,{key:r})})}let m=function(e){let{children:t}=e,r=(0,l.useContext)(s.AmpStateContext),n=(0,l.useContext)(u.HeadManagerContext);return(0,i.jsx)(a.default,{reduceComponentsToState:p,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5840:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return i}});let n=r(8229)._(r(2115)),o=r(5840),i=n.default.createContext(o.imageConfigDefault)},7544:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},8011:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var n=r(2115);function o(e,t,r=function e(t,r){return Array.isArray(t)&&Array.isArray(r)?r.length!==t.length||t.some((t,n)=>e(t,r[n])):t!==r}){let[i,l]=(0,n.useState)(e);r(i,e)&&(t(e,i),l(e))}},8048:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});let n=e=>"object"==typeof e&&null!=e&&1===e.nodeType,o=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,i=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){let r=getComputedStyle(e,null);return o(r.overflowY,t)||o(r.overflowX,t)||(e=>{let t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},l=(e,t,r,n,o,i,l,a)=>i<e&&l>t||i>e&&l<t?0:i<=e&&a<=r||l>=t&&a>=r?i-e-n:l>t&&a<r||i<e&&a>r?l-t+o:0,a=e=>{let t=e.parentElement;return null==t?e.getRootNode().host||null:t},s=(e,t)=>{var r,o,s,u;if("undefined"==typeof document)return[];let{scrollMode:c,block:d,inline:f,boundary:h,skipOverflowHiddenElements:p}=t,m="function"==typeof h?h:e=>e!==h;if(!n(e))throw TypeError("Invalid target");let g=document.scrollingElement||document.documentElement,b=[],y=e;for(;n(y)&&m(y);){if((y=a(y))===g){b.push(y);break}null!=y&&y===document.body&&i(y)&&!i(document.documentElement)||null!=y&&i(y,p)&&b.push(y)}let v=null!=(o=null==(r=window.visualViewport)?void 0:r.width)?o:innerWidth,S=null!=(u=null==(s=window.visualViewport)?void 0:s.height)?u:innerHeight,{scrollX:w,scrollY:I}=window,{height:x,width:O,top:_,right:N,bottom:D,left:T}=e.getBoundingClientRect(),{top:E,right:P,bottom:A,left:R}=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e),k="start"===d||"nearest"===d?_-E:"end"===d?D+A:_+x/2-E+A,M="center"===f?T+O/2-R+P:"end"===f?N+P:T-R,C=[];for(let e=0;e<b.length;e++){let t=b[e],{height:r,width:n,top:o,right:a,bottom:s,left:u}=t.getBoundingClientRect();if("if-needed"===c&&_>=0&&T>=0&&D<=S&&N<=v&&(t===g&&!i(t)||_>=o&&D<=s&&T>=u&&N<=a))break;let h=getComputedStyle(t),p=parseInt(h.borderLeftWidth,10),m=parseInt(h.borderTopWidth,10),y=parseInt(h.borderRightWidth,10),E=parseInt(h.borderBottomWidth,10),P=0,A=0,R="offsetWidth"in t?t.offsetWidth-t.clientWidth-p-y:0,j="offsetHeight"in t?t.offsetHeight-t.clientHeight-m-E:0,z="offsetWidth"in t?0===t.offsetWidth?0:n/t.offsetWidth:0,L="offsetHeight"in t?0===t.offsetHeight?0:r/t.offsetHeight:0;if(g===t)P="start"===d?k:"end"===d?k-S:"nearest"===d?l(I,I+S,S,m,E,I+k,I+k+x,x):k-S/2,A="start"===f?M:"center"===f?M-v/2:"end"===f?M-v:l(w,w+v,v,p,y,w+M,w+M+O,O),P=Math.max(0,P+I),A=Math.max(0,A+w);else{P="start"===d?k-o-m:"end"===d?k-s+E+j:"nearest"===d?l(o,s,r,m,E+j,k,k+x,x):k-(o+r/2)+j/2,A="start"===f?M-u-p:"center"===f?M-(u+n/2)+R/2:"end"===f?M-a+y+R:l(u,a,n,p,y+R,M,M+O,O);let{scrollLeft:e,scrollTop:i}=t;P=0===L?0:Math.max(0,Math.min(i+P/L,t.scrollHeight-r/L+j)),A=0===z?0:Math.max(0,Math.min(e+A/z,t.scrollWidth-n/z+R)),k+=i-P,M+=e-A}C.push({el:t,top:P,left:A})}return C};function u(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;let r=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if("object"==typeof t&&"function"==typeof t.behavior)return t.behavior(s(e,t));let n="boolean"==typeof t||null==t?void 0:t.behavior;for(let{el:o,top:i,left:l}of s(e,!1===t?{block:"end",inline:"nearest"}:t===Object(t)&&0!==Object.keys(t).length?t:{block:"start",inline:"nearest"})){let e=i-r.top+r.bottom,t=l-r.left+r.right;o.scroll({top:e,left:t,behavior:n})}}},8265:(e,t,r)=>{"use strict";r.d(t,{J:()=>o}),r(7505);var n=r(2115);function o(e){let t=(0,n.useRef)(e);return t.current=e,(0,n.useCallback)(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return t.current(...r)},[])}},8693:(e,t,r)=>{"use strict";r.d(t,{L:()=>c,TreeContextProvider:()=>u,t:()=>d});var n=r(5155),o=r(344),i=r(2115),l=r(244);let a=(0,o.q6)("TreeContext"),s=(0,o.q6)("PathContext",[]);function u(e){var t,r;let u=(0,i.useRef)(0),c=(0,o.a8)(),d=(0,i.useMemo)(()=>e.tree,[null!=(t=e.tree.$id)?t:e.tree]),f=(0,i.useMemo)(()=>{var e;return null!=(e=(0,l.oe)(d.children,c))?e:[]},[d,c]),h=null!=(r=f.findLast(e=>"folder"===e.type&&e.root))?r:d;return null!=h.$id||(h.$id=String(u.current++)),(0,n.jsx)(a.Provider,{value:(0,i.useMemo)(()=>({root:h}),[h]),children:(0,n.jsx)(s.Provider,{value:f,children:e.children})})}function c(){return s.use()}function d(){return a.use("You must wrap this component under <DocsLayout />")}},8883:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),r(3230);let n=r(5100),o=r(5840),i=["-moz-initial","fill","none","scale-down",void 0];function l(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r,s;let u,c,d,{src:f,sizes:h,unoptimized:p=!1,priority:m=!1,loading:g,className:b,quality:y,width:v,height:S,fill:w=!1,style:I,overrideSrc:x,onLoad:O,onLoadingComplete:_,placeholder:N="empty",blurDataURL:D,fetchPriority:T,decoding:E="async",layout:P,objectFit:A,objectPosition:R,lazyBoundary:k,lazyRoot:M,...C}=e,{imgConf:j,showAltText:z,blurComplete:L,defaultLoader:U}=t,B=j||o.imageConfigDefault;if("allSizes"in B)u=B;else{let e=[...B.deviceSizes,...B.imageSizes].sort((e,t)=>e-t),t=B.deviceSizes.sort((e,t)=>e-t),n=null==(r=B.qualities)?void 0:r.sort((e,t)=>e-t);u={...B,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===U)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=C.loader||U;delete C.loader,delete C.srcSet;let W="__next_img_default"in F;if(W){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...n}=t;return e(n)}}if(P){"fill"===P&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[P];e&&(I={...I,...e});let t={responsive:"100vw",fill:"100vw"}[P];t&&!h&&(h=t)}let V="",$=a(v),G=a(S);if((s=f)&&"object"==typeof s&&(l(s)||void 0!==s.src)){let e=l(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,d=e.blurHeight,D=D||e.blurDataURL,V=e.src,!w)if($||G){if($&&!G){let t=$/e.width;G=Math.round(e.height*t)}else if(!$&&G){let t=G/e.height;$=Math.round(e.width*t)}}else $=e.width,G=e.height}let H=!m&&("lazy"===g||void 0===g);(!(f="string"==typeof f?f:V)||f.startsWith("data:")||f.startsWith("blob:"))&&(p=!0,H=!1),u.unoptimized&&(p=!0),W&&!u.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(p=!0);let J=a(y),Y=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:A,objectPosition:R}:{},z?{}:{color:"transparent"},I),K=L||"empty"===N?null:"blur"===N?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:$,heightInt:G,blurWidth:c,blurHeight:d,blurDataURL:D||"",objectFit:Y.objectFit})+'")':'url("'+N+'")',q=i.includes(Y.objectFit)?"fill"===Y.objectFit?"100% 100%":"cover":Y.objectFit,X=K?{backgroundSize:q,backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},Z=function(e){let{config:t,src:r,unoptimized:n,width:o,quality:i,sizes:l,loader:a}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:s,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,l),c=s.length-1;return{sizes:l||"w"!==u?l:"100vw",srcSet:s.map((e,n)=>a({config:t,src:r,quality:i,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:a({config:t,src:r,quality:i,width:s[c]})}}({config:u,src:f,unoptimized:p,width:$,quality:J,sizes:h,loader:F});return{props:{...C,loading:H?"lazy":g,fetchPriority:T,width:$,height:G,decoding:E,className:b,style:{...Y,...X},sizes:Z.sizes,srcSet:Z.srcSet,src:x||Z.src},meta:{unoptimized:p,priority:m,placeholder:N,fill:w}}}},9309:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var n=r(8011);r(7505);var o=r(2115);function i(e,t,l,a=100,s=!1,u){let{delayMs:c=a??100,allowEmpty:d=s??!1,...f}=e;f.tag??=l,f.locale??=t;let[h,p]=(0,o.useState)(""),[m,g]=(0,o.useState)("empty"),[b,y]=(0,o.useState)(),[v,S]=(0,o.useState)(!1),w=function(e,t=1e3){let[r,n]=(0,o.useState)(e),i=(0,o.useRef)(void 0);if(0===t)return e;if(e!==r&&i.current?.value!==e){i.current&&clearTimeout(i.current.handler);let r=window.setTimeout(()=>{n(e)},t);i.current={value:e,handler:r}}return r}(h,c),I=(0,o.useRef)(void 0);return(0,n.T)([f,w],()=>{I.current&&(I.current(),I.current=void 0),S(!0);let e=!1;I.current=()=>{e=!0},(async function(){if(0===w.length&&!d)return"empty";if("fetch"===f.type){let{fetchDocs:e}=await r.e(996).then(r.bind(r,5996));return e(w,f)}if("algolia"===f.type){let{searchDocs:e}=await r.e(411).then(r.bind(r,7792));return e(w,f)}if("orama-cloud"===f.type){let{searchDocs:e}=await r.e(119).then(r.bind(r,7119));return e(w,f)}if("static"===f.type){let{search:e}=await r.e(366).then(r.bind(r,4366));return e(w,f)}if("mixedbread"===f.type){let{search:e}=await r.e(573).then(r.bind(r,2573));return e(w,f)}throw Error("unknown search client")})().then(t=>{e||(y(void 0),g(t))}).catch(e=>{y(e)}).finally(()=>{S(!1)})},function e(t,r){if(Array.isArray(t)&&Array.isArray(r))return r.length!==t.length||t.some((t,n)=>e(t,r[n]));if("object"==typeof t&&t&&"object"==typeof r&&r){let n=Object.keys(t),o=Object.keys(r);return n.length!==o.length||n.some(n=>e(t[n],r[n]))}return t!==r}),{search:h,setSearch:p,query:{isLoading:v,data:m,error:b}}}},9840:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}}}]);