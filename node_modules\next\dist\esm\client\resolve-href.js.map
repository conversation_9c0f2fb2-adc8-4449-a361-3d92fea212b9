{"version": 3, "sources": ["../../src/client/resolve-href.ts"], "sourcesContent": ["import type { NextRouter, Url } from '../shared/lib/router/router'\n\nimport { searchParamsToUrlQuery } from '../shared/lib/router/utils/querystring'\nimport { formatWithValidation } from '../shared/lib/router/utils/format-url'\nimport { omit } from '../shared/lib/router/utils/omit'\nimport { normalizeRepeatedSlashes } from '../shared/lib/utils'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\nimport { isLocalURL } from '../shared/lib/router/utils/is-local-url'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { interpolateAs } from '../shared/lib/router/utils/interpolate-as'\nimport { getRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\n\n/**\n * Resolves a given hyperlink with a certain router state (basePath not included).\n * Preserves absolute urls.\n */\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs: true\n): [string, string] | [string]\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: false\n): string\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: boolean\n): [string, string] | [string] | string {\n  // we use a dummy base url for relative urls\n  let base: URL\n  let urlAsString = typeof href === 'string' ? href : formatWithValidation(href)\n\n  // repeated slashes and backslashes in the URL are considered\n  // invalid and will never match a Next.js page/file\n  // https://www.rfc-editor.org/rfc/rfc3986.html#section-3.1\n  const urlProtoMatch = urlAsString.match(/^[a-z][a-z0-9+.-]*:\\/\\//i)\n  const urlAsStringNoProto = urlProtoMatch\n    ? urlAsString.slice(urlProtoMatch[0].length)\n    : urlAsString\n\n  const urlParts = urlAsStringNoProto.split('?', 1)\n\n  if ((urlParts[0] || '').match(/(\\/\\/|\\\\)/)) {\n    console.error(\n      `Invalid href '${urlAsString}' passed to next/router in page: '${router.pathname}'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.`\n    )\n    const normalizedUrl = normalizeRepeatedSlashes(urlAsStringNoProto)\n    urlAsString = (urlProtoMatch ? urlProtoMatch[0] : '') + normalizedUrl\n  }\n\n  // Return because it cannot be routed by the Next.js router\n  if (!isLocalURL(urlAsString)) {\n    return (resolveAs ? [urlAsString] : urlAsString) as string\n  }\n\n  try {\n    let baseBase = urlAsString.startsWith('#') ? router.asPath : router.pathname\n\n    // If the provided href is only a query string, it is safer to use the asPath\n    // considering rewrites.\n    if (urlAsString.startsWith('?')) {\n      baseBase = router.asPath\n\n      // However, if is a dynamic route, we need to use the pathname to preserve the\n      // query interpolation and rewrites (router.pathname will look like \"/[slug]\").\n      if (isDynamicRoute(router.pathname)) {\n        baseBase = router.pathname\n\n        const routeRegex = getRouteRegex(router.pathname)\n        const match = getRouteMatcher(routeRegex)(router.asPath)\n\n        // For dynamic routes, if asPath doesn't match the pathname regex, it is a rewritten path.\n        // In this case, should use asPath to preserve the current URL.\n        if (!match) {\n          baseBase = router.asPath\n        }\n\n        // Note: There is an edge case where the pathname is dynamic, and also a rewrite path to the same segment.\n        // E.g. in \"/[slug]\" path, rewrite \"/foo\" -> \"/bar\"\n\n        // In this case, it will be treated as a non-rewritten path and possibly interpolate the query string.\n        // E.g., \"/any?slug=foo\" will become the content of \"/foo\", not rewritten as \"/bar\"\n\n        // This is currently a trade-off of not resolving rewrite paths on every Router/Link call,\n        // but using a lighter route regex pattern check.\n      }\n    }\n\n    base = new URL(baseBase, 'http://n')\n  } catch (_) {\n    // fallback to / for invalid asPath values e.g. //\n    base = new URL('/', 'http://n')\n  }\n\n  try {\n    const finalUrl = new URL(urlAsString, base)\n    finalUrl.pathname = normalizePathTrailingSlash(finalUrl.pathname)\n    let interpolatedAs = ''\n\n    if (\n      isDynamicRoute(finalUrl.pathname) &&\n      finalUrl.searchParams &&\n      resolveAs\n    ) {\n      const query = searchParamsToUrlQuery(finalUrl.searchParams)\n\n      const { result, params } = interpolateAs(\n        finalUrl.pathname,\n        finalUrl.pathname,\n        query\n      )\n\n      if (result) {\n        interpolatedAs = formatWithValidation({\n          pathname: result,\n          hash: finalUrl.hash,\n          query: omit(query, params),\n        })\n      }\n    }\n\n    // if the origin didn't change, it means we received a relative href\n    const resolvedHref =\n      finalUrl.origin === base.origin\n        ? finalUrl.href.slice(finalUrl.origin.length)\n        : finalUrl.href\n\n    return resolveAs\n      ? [resolvedHref, interpolatedAs || resolvedHref]\n      : resolvedHref\n  } catch (_) {\n    return resolveAs ? [urlAsString] : urlAsString\n  }\n}\n"], "names": ["searchParamsToUrlQuery", "formatWithValidation", "omit", "normalizeRepeatedSlashes", "normalizePathTrailingSlash", "isLocalURL", "isDynamicRoute", "interpolateAs", "getRouteRegex", "getRouteMatcher", "resolveHref", "router", "href", "resolveAs", "base", "urlAsString", "urlProtoMatch", "match", "urlAsStringNoProto", "slice", "length", "urlParts", "split", "console", "error", "pathname", "normalizedUrl", "baseBase", "startsWith", "<PERSON><PERSON><PERSON>", "routeRegex", "URL", "_", "finalUrl", "interpolatedAs", "searchParams", "query", "result", "params", "hash", "resolvedHref", "origin"], "mappings": "AAEA,SAASA,sBAAsB,QAAQ,yCAAwC;AAC/E,SAASC,oBAAoB,QAAQ,wCAAuC;AAC5E,SAASC,IAAI,QAAQ,kCAAiC;AACtD,SAASC,wBAAwB,QAAQ,sBAAqB;AAC9D,SAASC,0BAA0B,QAAQ,6BAA4B;AACvE,SAASC,UAAU,QAAQ,0CAAyC;AACpE,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,aAAa,QAAQ,4CAA2C;AACzE,SAASC,aAAa,QAAQ,yCAAwC;AACtE,SAASC,eAAe,QAAQ,2CAA0C;AAgB1E,OAAO,SAASC,YACdC,MAAkB,EAClBC,IAAS,EACTC,SAAmB;IAEnB,4CAA4C;IAC5C,IAAIC;IACJ,IAAIC,cAAc,OAAOH,SAAS,WAAWA,OAAOX,qBAAqBW;IAEzE,6DAA6D;IAC7D,mDAAmD;IACnD,0DAA0D;IAC1D,MAAMI,gBAAgBD,YAAYE,KAAK,CAAC;IACxC,MAAMC,qBAAqBF,gBACvBD,YAAYI,KAAK,CAACH,aAAa,CAAC,EAAE,CAACI,MAAM,IACzCL;IAEJ,MAAMM,WAAWH,mBAAmBI,KAAK,CAAC,KAAK;IAE/C,IAAI,AAACD,CAAAA,QAAQ,CAAC,EAAE,IAAI,EAAC,EAAGJ,KAAK,CAAC,cAAc;QAC1CM,QAAQC,KAAK,CACX,AAAC,mBAAgBT,cAAY,uCAAoCJ,OAAOc,QAAQ,GAAC;QAEnF,MAAMC,gBAAgBvB,yBAAyBe;QAC/CH,cAAc,AAACC,CAAAA,gBAAgBA,aAAa,CAAC,EAAE,GAAG,EAAC,IAAKU;IAC1D;IAEA,2DAA2D;IAC3D,IAAI,CAACrB,WAAWU,cAAc;QAC5B,OAAQF,YAAY;YAACE;SAAY,GAAGA;IACtC;IAEA,IAAI;QACF,IAAIY,WAAWZ,YAAYa,UAAU,CAAC,OAAOjB,OAAOkB,MAAM,GAAGlB,OAAOc,QAAQ;QAE5E,6EAA6E;QAC7E,wBAAwB;QACxB,IAAIV,YAAYa,UAAU,CAAC,MAAM;YAC/BD,WAAWhB,OAAOkB,MAAM;YAExB,8EAA8E;YAC9E,+EAA+E;YAC/E,IAAIvB,eAAeK,OAAOc,QAAQ,GAAG;gBACnCE,WAAWhB,OAAOc,QAAQ;gBAE1B,MAAMK,aAAatB,cAAcG,OAAOc,QAAQ;gBAChD,MAAMR,QAAQR,gBAAgBqB,YAAYnB,OAAOkB,MAAM;gBAEvD,0FAA0F;gBAC1F,+DAA+D;gBAC/D,IAAI,CAACZ,OAAO;oBACVU,WAAWhB,OAAOkB,MAAM;gBAC1B;YAEA,0GAA0G;YAC1G,mDAAmD;YAEnD,sGAAsG;YACtG,mFAAmF;YAEnF,0FAA0F;YAC1F,iDAAiD;YACnD;QACF;QAEAf,OAAO,IAAIiB,IAAIJ,UAAU;IAC3B,EAAE,OAAOK,GAAG;QACV,kDAAkD;QAClDlB,OAAO,IAAIiB,IAAI,KAAK;IACtB;IAEA,IAAI;QACF,MAAME,WAAW,IAAIF,IAAIhB,aAAaD;QACtCmB,SAASR,QAAQ,GAAGrB,2BAA2B6B,SAASR,QAAQ;QAChE,IAAIS,iBAAiB;QAErB,IACE5B,eAAe2B,SAASR,QAAQ,KAChCQ,SAASE,YAAY,IACrBtB,WACA;YACA,MAAMuB,QAAQpC,uBAAuBiC,SAASE,YAAY;YAE1D,MAAM,EAAEE,MAAM,EAAEC,MAAM,EAAE,GAAG/B,cACzB0B,SAASR,QAAQ,EACjBQ,SAASR,QAAQ,EACjBW;YAGF,IAAIC,QAAQ;gBACVH,iBAAiBjC,qBAAqB;oBACpCwB,UAAUY;oBACVE,MAAMN,SAASM,IAAI;oBACnBH,OAAOlC,KAAKkC,OAAOE;gBACrB;YACF;QACF;QAEA,oEAAoE;QACpE,MAAME,eACJP,SAASQ,MAAM,KAAK3B,KAAK2B,MAAM,GAC3BR,SAASrB,IAAI,CAACO,KAAK,CAACc,SAASQ,MAAM,CAACrB,MAAM,IAC1Ca,SAASrB,IAAI;QAEnB,OAAOC,YACH;YAAC2B;YAAcN,kBAAkBM;SAAa,GAC9CA;IACN,EAAE,OAAOR,GAAG;QACV,OAAOnB,YAAY;YAACE;SAAY,GAAGA;IACrC;AACF", "ignoreList": [0]}