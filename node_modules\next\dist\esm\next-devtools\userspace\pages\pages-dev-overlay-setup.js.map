{"version": 3, "sources": ["../../../../src/next-devtools/userspace/pages/pages-dev-overlay-setup.tsx"], "sourcesContent": ["import React from 'react'\nimport { renderPagesDevOverlay } from 'next/dist/compiled/next-devtools'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport {\n  attachHydrationErrorState,\n  storeHydrationErrorStateFromConsoleArgs,\n} from './hydration-error-state'\nimport { Router } from '../../../client/router'\nimport { getOwnerStack } from '../app/errors/stitched-error'\nimport { isRecoverableError } from '../../../client/react-client-callbacks/on-recoverable-error'\nimport { getSquashedHydrationErrorDetails } from './hydration-error-state'\nimport { PagesDevOverlayErrorBoundary } from './pages-dev-overlay-error-boundary'\nimport {\n  initializeDebugLogForwarding,\n  forwardUnhandledError,\n  logUnhandledRejection,\n  forwardErrorLog,\n  isTerminalLoggingEnabled,\n} from '../app/forward-logs'\n\nconst usePagesDevOverlayBridge = () => {\n  React.useInsertionEffect(() => {\n    // NDT uses a different React instance so it's not technically a state update\n    // scheduled from useInsertionEffect.\n    renderPagesDevOverlay(\n      getOwnerStack,\n      getSquashedHydrationErrorDetails,\n      isRecoverableError\n    )\n  }, [])\n\n  React.useEffect(() => {\n    const { handleStaticIndicator } =\n      require('../../../client/dev/hot-reloader/pages/hot-reloader-pages') as typeof import('../../../client/dev/hot-reloader/pages/hot-reloader-pages')\n\n    Router.events.on('routeChangeComplete', handleStaticIndicator)\n\n    return function () {\n      Router.events.off('routeChangeComplete', handleStaticIndicator)\n    }\n  }, [])\n}\n\nexport type PagesDevOverlayBridgeType = typeof PagesDevOverlayBridge\n\ninterface PagesDevOverlayBridgeProps {\n  children?: React.ReactNode\n}\n\nexport function PagesDevOverlayBridge({\n  children,\n}: PagesDevOverlayBridgeProps) {\n  usePagesDevOverlayBridge()\n\n  return <PagesDevOverlayErrorBoundary>{children}</PagesDevOverlayErrorBoundary>\n}\n\nlet isRegistered = false\n\nfunction handleError(error: unknown) {\n  if (!error || !(error instanceof Error) || typeof error.stack !== 'string') {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  attachHydrationErrorState(error)\n\n  // Skip ModuleBuildError and ModuleNotFoundError, as it will be sent through onBuildError callback.\n  // This is to avoid same error as different type showing up on client to cause flashing.\n  if (\n    error.name !== 'ModuleBuildError' &&\n    error.name !== 'ModuleNotFoundError'\n  ) {\n    dispatcher.onUnhandledError(error)\n  }\n}\n\nlet origConsoleError = console.error\nfunction nextJsHandleConsoleError(...args: any[]) {\n  // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n  const maybeError = process.env.NODE_ENV !== 'production' ? args[1] : args[0]\n  storeHydrationErrorStateFromConsoleArgs(...args)\n  // TODO: Surfaces non-errors logged via `console.error`.\n  handleError(maybeError)\n  if (isTerminalLoggingEnabled) {\n    forwardErrorLog(args)\n  }\n  origConsoleError.apply(window.console, args)\n}\n\nfunction onUnhandledError(event: ErrorEvent) {\n  const error = event?.error\n  handleError(error)\n\n  if (error && isTerminalLoggingEnabled) {\n    forwardUnhandledError(error as Error)\n  }\n}\n\nfunction onUnhandledRejection(ev: PromiseRejectionEvent) {\n  const reason = ev?.reason\n  if (\n    !reason ||\n    !(reason instanceof Error) ||\n    typeof reason.stack !== 'string'\n  ) {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  dispatcher.onUnhandledRejection(reason)\n  if (isTerminalLoggingEnabled) {\n    logUnhandledRejection(reason)\n  }\n}\n\nexport function register() {\n  if (isRegistered) {\n    return\n  }\n  isRegistered = true\n\n  try {\n    Error.stackTraceLimit = 50\n  } catch {}\n\n  if (isTerminalLoggingEnabled) {\n    initializeDebugLogForwarding('pages')\n  }\n  window.addEventListener('error', onUnhandledError)\n  window.addEventListener('unhandledrejection', onUnhandledRejection)\n  window.console.error = nextJsHandleConsoleError\n}\n"], "names": ["React", "renderPagesDevOverlay", "dispatcher", "attachHydrationErrorState", "storeHydrationErrorStateFromConsoleArgs", "Router", "getOwnerStack", "isRecoverableError", "getSquashedHydrationErrorDetails", "PagesDevOverlayErrorBoundary", "initializeDebugLogForwarding", "forwardUnhandledError", "logUnhandledRejection", "forward<PERSON><PERSON><PERSON><PERSON><PERSON>", "isTerminalLoggingEnabled", "usePagesDevOverlayBridge", "useInsertionEffect", "useEffect", "handleStaticIndicator", "require", "events", "on", "off", "PagesDevOverlayBridge", "children", "isRegistered", "handleError", "error", "Error", "stack", "name", "onUnhandledError", "origConsoleError", "console", "nextJsHandleConsoleError", "args", "maybeError", "process", "env", "NODE_ENV", "apply", "window", "event", "onUnhandledRejection", "ev", "reason", "register", "stackTraceLimit", "addEventListener"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,qBAAqB,QAAQ,mCAAkC;AACxE,SAASC,UAAU,QAAQ,mCAAkC;AAC7D,SACEC,yBAAyB,EACzBC,uCAAuC,QAClC,0BAAyB;AAChC,SAASC,MAAM,QAAQ,yBAAwB;AAC/C,SAASC,aAAa,QAAQ,+BAA8B;AAC5D,SAASC,kBAAkB,QAAQ,8DAA6D;AAChG,SAASC,gCAAgC,QAAQ,0BAAyB;AAC1E,SAASC,4BAA4B,QAAQ,qCAAoC;AACjF,SACEC,4BAA4B,EAC5BC,qBAAqB,EACrBC,qBAAqB,EACrBC,eAAe,EACfC,wBAAwB,QACnB,sBAAqB;AAE5B,MAAMC,2BAA2B;IAC/Bf,MAAMgB,kBAAkB,CAAC;QACvB,6EAA6E;QAC7E,qCAAqC;QACrCf,sBACEK,eACAE,kCACAD;IAEJ,GAAG,EAAE;IAELP,MAAMiB,SAAS,CAAC;QACd,MAAM,EAAEC,qBAAqB,EAAE,GAC7BC,QAAQ;QAEVd,OAAOe,MAAM,CAACC,EAAE,CAAC,uBAAuBH;QAExC,OAAO;YACLb,OAAOe,MAAM,CAACE,GAAG,CAAC,uBAAuBJ;QAC3C;IACF,GAAG,EAAE;AACP;AAQA,OAAO,SAASK,sBAAsB,KAET;IAFS,IAAA,EACpCC,QAAQ,EACmB,GAFS;IAGpCT;IAEA,qBAAO,KAACN;kBAA8Be;;AACxC;AAEA,IAAIC,eAAe;AAEnB,SAASC,YAAYC,KAAc;IACjC,IAAI,CAACA,SAAS,CAAEA,CAAAA,iBAAiBC,KAAI,KAAM,OAAOD,MAAME,KAAK,KAAK,UAAU;QAC1E,8DAA8D;QAC9D;IACF;IAEA1B,0BAA0BwB;IAE1B,mGAAmG;IACnG,wFAAwF;IACxF,IACEA,MAAMG,IAAI,KAAK,sBACfH,MAAMG,IAAI,KAAK,uBACf;QACA5B,WAAW6B,gBAAgB,CAACJ;IAC9B;AACF;AAEA,IAAIK,mBAAmBC,QAAQN,KAAK;AACpC,SAASO;IAAyB,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAc;;IAC9C,iJAAiJ;IACjJ,MAAMC,aAAaC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAeJ,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;IAC5E/B,2CAA2C+B;IAC3C,wDAAwD;IACxDT,YAAYU;IACZ,IAAItB,0BAA0B;QAC5BD,gBAAgBsB;IAClB;IACAH,iBAAiBQ,KAAK,CAACC,OAAOR,OAAO,EAAEE;AACzC;AAEA,SAASJ,iBAAiBW,KAAiB;IACzC,MAAMf,QAAQe,yBAAAA,MAAOf,KAAK;IAC1BD,YAAYC;IAEZ,IAAIA,SAASb,0BAA0B;QACrCH,sBAAsBgB;IACxB;AACF;AAEA,SAASgB,qBAAqBC,EAAyB;IACrD,MAAMC,SAASD,sBAAAA,GAAIC,MAAM;IACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBjB,KAAI,KACxB,OAAOiB,OAAOhB,KAAK,KAAK,UACxB;QACA,8DAA8D;QAC9D;IACF;IAEA3B,WAAWyC,oBAAoB,CAACE;IAChC,IAAI/B,0BAA0B;QAC5BF,sBAAsBiC;IACxB;AACF;AAEA,OAAO,SAASC;IACd,IAAIrB,cAAc;QAChB;IACF;IACAA,eAAe;IAEf,IAAI;QACFG,MAAMmB,eAAe,GAAG;IAC1B,EAAE,UAAM,CAAC;IAET,IAAIjC,0BAA0B;QAC5BJ,6BAA6B;IAC/B;IACA+B,OAAOO,gBAAgB,CAAC,SAASjB;IACjCU,OAAOO,gBAAgB,CAAC,sBAAsBL;IAC9CF,OAAOR,OAAO,CAACN,KAAK,GAAGO;AACzB", "ignoreList": [0]}