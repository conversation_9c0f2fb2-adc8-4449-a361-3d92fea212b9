{"version": 3, "sources": ["../../../src/server/lib/router-server.ts"], "sourcesContent": ["// this must come first as it includes require hooks\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './types'\nimport type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Fields } from './router-utils/setup-dev-bundler'\nimport type { NextUrlWithParsedQuery, RequestMeta } from '../request-meta'\n\n// This is required before other imports to ensure the require hook is setup.\nimport '../node-environment'\nimport '../require-hook'\n\nimport url from 'url'\nimport path from 'path'\nimport loadConfig from '../config'\nimport { serveStatic } from '../serve-static'\nimport setupDebug from 'next/dist/compiled/debug'\nimport * as Log from '../../build/output/log'\nimport { DecodeError } from '../../shared/lib/utils'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport { setupFsCheck } from './router-utils/filesystem'\nimport { proxyRequest } from './router-utils/proxy-request'\nimport { isAbortError, pipeToNodeResponse } from '../pipe-readable'\nimport { getResolveRoutes } from './router-utils/resolve-routes'\nimport { addRequestMeta, getRequestMeta } from '../request-meta'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport setupCompression from 'next/dist/compiled/compression'\nimport { signalFromNodeResponse } from '../web/spec-extension/adapters/next-request'\nimport { isPostpone } from './router-utils/is-postpone'\nimport { parseUrl as parseUrlUtil } from '../../shared/lib/router/utils/parse-url'\n\nimport {\n  PHASE_PRODUCTION_SERVER,\n  PHASE_DEVELOPMENT_SERVER,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n} from '../../shared/lib/constants'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { DevBundlerService } from './dev-bundler-service'\nimport { type Span, trace } from '../../trace'\nimport { ensureLeadingSlash } from '../../shared/lib/page-path/ensure-leading-slash'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { MockedResponse } from './mock-request'\nimport {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type AppIsrManifestAction,\n} from '../dev/hot-reloader-types'\nimport { normalizedAssetPrefix } from '../../shared/lib/normalized-asset-prefix'\nimport { NEXT_PATCH_SYMBOL } from './patch-fetch'\nimport type { ServerInitResult } from './render-server'\nimport { filterInternalHeaders } from './server-ipc/utils'\nimport { blockCrossSite } from './router-utils/block-cross-site'\nimport { traceGlobals } from '../../trace/shared'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\nimport {\n  RouterServerContextSymbol,\n  routerServerGlobal,\n} from './router-utils/router-server-context'\nimport {\n  handleChromeDevtoolsWorkspaceRequest,\n  isChromeDevtoolsWorkspaceUrl,\n} from './chrome-devtools-workspace'\n\nconst debug = setupDebug('next:router-server:main')\nconst isNextFont = (pathname: string | null) =>\n  pathname && /\\/media\\/[^/]+\\.(woff|woff2|eot|ttf|otf)$/.test(pathname)\n\nexport type RenderServer = Pick<\n  typeof import('./render-server'),\n  | 'initialize'\n  | 'clearModuleContext'\n  | 'propagateServerField'\n  | 'getServerField'\n>\n\nexport interface LazyRenderServerInstance {\n  instance?: RenderServer\n}\n\nconst requestHandlers: Record<string, WorkerRequestHandler> = {}\n\nexport async function initialize(opts: {\n  dir: string\n  port: number\n  dev: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  server?: import('http').Server\n  minimalMode?: boolean\n  hostname?: string\n  keepAliveTimeout?: number\n  customServer?: boolean\n  experimentalHttpsServer?: boolean\n  startServerSpan?: Span\n  quiet?: boolean\n}): Promise<ServerInitResult> {\n  if (!process.env.NODE_ENV) {\n    // @ts-ignore not readonly\n    process.env.NODE_ENV = opts.dev ? 'development' : 'production'\n  }\n\n  const config = await loadConfig(\n    opts.dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_SERVER,\n    opts.dir,\n    { silent: false }\n  )\n\n  let compress: ReturnType<typeof setupCompression> | undefined\n\n  if (config?.compress !== false) {\n    compress = setupCompression()\n  }\n\n  const fsChecker = await setupFsCheck({\n    dev: opts.dev,\n    dir: opts.dir,\n    config,\n    minimalMode: opts.minimalMode,\n  })\n\n  const renderServer: LazyRenderServerInstance = {}\n\n  let developmentBundler: DevBundler | undefined\n\n  let devBundlerService: DevBundlerService | undefined\n\n  let originalFetch = globalThis.fetch\n\n  if (opts.dev) {\n    const { Telemetry } =\n      require('../../telemetry/storage') as typeof import('../../telemetry/storage')\n\n    const telemetry = new Telemetry({\n      distDir: path.join(opts.dir, config.distDir),\n    })\n    traceGlobals.set('telemetry', telemetry)\n\n    const { pagesDir, appDir } = findPagesDir(opts.dir)\n\n    const { setupDevBundler } =\n      require('./router-utils/setup-dev-bundler') as typeof import('./router-utils/setup-dev-bundler')\n\n    const resetFetch = () => {\n      globalThis.fetch = originalFetch\n      ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = false\n    }\n\n    const setupDevBundlerSpan = opts.startServerSpan\n      ? opts.startServerSpan.traceChild('setup-dev-bundler')\n      : trace('setup-dev-bundler')\n    developmentBundler = await setupDevBundlerSpan.traceAsyncFn(() =>\n      setupDevBundler({\n        // Passed here but the initialization of this object happens below, doing the initialization before the setupDev call breaks.\n        renderServer,\n        appDir,\n        pagesDir,\n        telemetry,\n        fsChecker,\n        dir: opts.dir,\n        nextConfig: config,\n        isCustomServer: opts.customServer,\n        turbo: !!process.env.TURBOPACK,\n        port: opts.port,\n        onDevServerCleanup: opts.onDevServerCleanup,\n        resetFetch,\n      })\n    )\n\n    devBundlerService = new DevBundlerService(\n      developmentBundler,\n      // The request handler is assigned below, this allows us to create a lazy\n      // reference to it.\n      (req, res) => {\n        return requestHandlers[opts.dir](req, res)\n      }\n    )\n  }\n\n  renderServer.instance =\n    require('./render-server') as typeof import('./render-server')\n\n  const requestHandlerImpl: WorkerRequestHandler = async (req, res) => {\n    addRequestMeta(req, 'relativeProjectDir', relativeProjectDir)\n\n    // internal headers should not be honored by the request handler\n    if (!process.env.NEXT_PRIVATE_TEST_HEADERS) {\n      filterInternalHeaders(req.headers)\n    }\n\n    if (\n      !opts.minimalMode &&\n      config.i18n &&\n      config.i18n.localeDetection !== false\n    ) {\n      const urlParts = (req.url || '').split('?', 1)\n      let urlNoQuery = urlParts[0] || ''\n\n      if (config.basePath) {\n        urlNoQuery = removePathPrefix(urlNoQuery, config.basePath)\n      }\n\n      const pathnameInfo = getNextPathnameInfo(urlNoQuery, {\n        nextConfig: config,\n      })\n\n      const domainLocale = detectDomainLocale(\n        config.i18n.domains,\n        getHostname({ hostname: urlNoQuery }, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || config.i18n.defaultLocale\n\n      const { getLocaleRedirect } =\n        require('../../shared/lib/i18n/get-locale-redirect') as typeof import('../../shared/lib/i18n/get-locale-redirect')\n\n      const parsedUrl = parseUrlUtil((req.url || '')?.replace(/^\\/+/, '/'))\n\n      const redirect = getLocaleRedirect({\n        defaultLocale,\n        domainLocale,\n        headers: req.headers,\n        nextConfig: config,\n        pathLocale: pathnameInfo.locale,\n        urlParsed: {\n          ...parsedUrl,\n          pathname: pathnameInfo.locale\n            ? `/${pathnameInfo.locale}${urlNoQuery}`\n            : urlNoQuery,\n        },\n      })\n\n      if (redirect) {\n        res.setHeader('Location', redirect)\n        res.statusCode = RedirectStatusCode.TemporaryRedirect\n        res.end(redirect)\n        return\n      }\n    }\n\n    if (compress) {\n      // @ts-expect-error not express req/res\n      compress(req, res, () => {})\n    }\n    req.on('error', (_err) => {\n      // TODO: log socket errors?\n    })\n    res.on('error', (_err) => {\n      // TODO: log socket errors?\n    })\n\n    const invokedOutputs = new Set<string>()\n\n    async function invokeRender(\n      parsedUrl: NextUrlWithParsedQuery,\n      invokePath: string,\n      handleIndex: number,\n      additionalRequestMeta?: RequestMeta\n    ) {\n      // invokeRender expects /api routes to not be locale prefixed\n      // so normalize here before continuing\n      if (\n        config.i18n &&\n        removePathPrefix(invokePath, config.basePath).startsWith(\n          `/${getRequestMeta(req, 'locale')}/api`\n        )\n      ) {\n        invokePath = fsChecker.handleLocale(\n          removePathPrefix(invokePath, config.basePath)\n        ).pathname\n      }\n\n      if (\n        req.headers['x-nextjs-data'] &&\n        fsChecker.getMiddlewareMatchers()?.length &&\n        removePathPrefix(invokePath, config.basePath) === '/404'\n      ) {\n        res.setHeader('x-nextjs-matched-path', parsedUrl.pathname || '')\n        res.statusCode = 404\n        res.setHeader('content-type', 'application/json')\n        res.end('{}')\n        return null\n      }\n\n      if (!handlers) {\n        throw new Error('Failed to initialize render server')\n      }\n\n      addRequestMeta(req, 'invokePath', invokePath)\n      addRequestMeta(req, 'invokeQuery', parsedUrl.query)\n      addRequestMeta(req, 'middlewareInvoke', false)\n\n      for (const key in additionalRequestMeta || {}) {\n        addRequestMeta(\n          req,\n          key as keyof RequestMeta,\n          additionalRequestMeta![key as keyof RequestMeta]\n        )\n      }\n\n      debug('invokeRender', req.url, req.headers)\n\n      try {\n        const initResult =\n          await renderServer?.instance?.initialize(renderServerOpts)\n        try {\n          await initResult?.requestHandler(req, res)\n        } catch (err) {\n          if (err instanceof NoFallbackError) {\n            // eslint-disable-next-line\n            await handleRequest(handleIndex + 1)\n            return\n          }\n          throw err\n        }\n        return\n      } catch (e) {\n        // If the client aborts before we can receive a response object (when\n        // the headers are flushed), then we can early exit without further\n        // processing.\n        if (isAbortError(e)) {\n          return\n        }\n        throw e\n      }\n    }\n\n    const handleRequest = async (handleIndex: number) => {\n      if (handleIndex > 5) {\n        throw new Error(`Attempted to handle request too many times ${req.url}`)\n      }\n\n      // handle hot-reloader first\n      if (developmentBundler) {\n        if (blockCrossSite(req, res, config.allowedDevOrigins, opts.hostname)) {\n          return\n        }\n\n        const origUrl = req.url || '/'\n\n        // both the basePath and assetPrefix need to be stripped from the URL\n        // so that the development bundler can find the correct file\n        if (config.basePath && pathHasPrefix(origUrl, config.basePath)) {\n          req.url = removePathPrefix(origUrl, config.basePath)\n        } else if (\n          config.assetPrefix &&\n          pathHasPrefix(origUrl, config.assetPrefix)\n        ) {\n          req.url = removePathPrefix(origUrl, config.assetPrefix)\n        }\n\n        const parsedUrl = url.parse(req.url || '/')\n\n        const hotReloaderResult = await developmentBundler.hotReloader.run(\n          req,\n          res,\n          parsedUrl\n        )\n\n        if (hotReloaderResult.finished) {\n          return hotReloaderResult\n        }\n\n        req.url = origUrl\n      }\n\n      const {\n        finished,\n        parsedUrl,\n        statusCode,\n        resHeaders,\n        bodyStream,\n        matchedOutput,\n      } = await resolveRoutes({\n        req,\n        res,\n        isUpgradeReq: false,\n        signal: signalFromNodeResponse(res),\n        invokedOutputs,\n      })\n\n      if (res.closed || res.finished) {\n        return\n      }\n\n      if (developmentBundler && matchedOutput?.type === 'devVirtualFsItem') {\n        const origUrl = req.url || '/'\n\n        if (config.basePath && pathHasPrefix(origUrl, config.basePath)) {\n          req.url = removePathPrefix(origUrl, config.basePath)\n        } else if (\n          config.assetPrefix &&\n          pathHasPrefix(origUrl, config.assetPrefix)\n        ) {\n          req.url = removePathPrefix(origUrl, config.assetPrefix)\n        }\n\n        if (resHeaders) {\n          for (const key of Object.keys(resHeaders)) {\n            res.setHeader(key, resHeaders[key])\n          }\n        }\n        const result = await developmentBundler.requestHandler(req, res)\n\n        if (result.finished) {\n          return\n        }\n        // TODO: throw invariant if we resolved to this but it wasn't handled?\n        req.url = origUrl\n      }\n\n      debug('requestHandler!', req.url, {\n        matchedOutput,\n        statusCode,\n        resHeaders,\n        bodyStream: !!bodyStream,\n        parsedUrl: {\n          pathname: parsedUrl.pathname,\n          query: parsedUrl.query,\n        },\n        finished,\n      })\n\n      // apply any response headers from routing\n      for (const key of Object.keys(resHeaders || {})) {\n        res.setHeader(key, resHeaders[key])\n      }\n\n      // handle redirect\n      if (!bodyStream && statusCode && statusCode > 300 && statusCode < 400) {\n        const destination = url.format(parsedUrl)\n        res.statusCode = statusCode\n        res.setHeader('location', destination)\n\n        if (statusCode === RedirectStatusCode.PermanentRedirect) {\n          res.setHeader('Refresh', `0;url=${destination}`)\n        }\n        return res.end(destination)\n      }\n\n      // handle middleware body response\n      if (bodyStream) {\n        res.statusCode = statusCode || 200\n        return await pipeToNodeResponse(bodyStream, res)\n      }\n\n      if (finished && parsedUrl.protocol) {\n        return await proxyRequest(\n          req,\n          res,\n          parsedUrl,\n          undefined,\n          getRequestMeta(req, 'clonableBody')?.cloneBodyStream(),\n          config.experimental.proxyTimeout\n        )\n      }\n\n      if (matchedOutput?.fsPath && matchedOutput.itemPath) {\n        if (\n          opts.dev &&\n          (fsChecker.appFiles.has(matchedOutput.itemPath) ||\n            fsChecker.pageFiles.has(matchedOutput.itemPath))\n        ) {\n          res.statusCode = 500\n          const message = `A conflicting public file and page file was found for path ${matchedOutput.itemPath} https://nextjs.org/docs/messages/conflicting-public-file-page`\n          await invokeRender(parsedUrl, '/_error', handleIndex, {\n            invokeStatus: 500,\n            invokeError: new Error(message),\n          })\n          Log.error(message)\n          return\n        }\n\n        if (\n          !res.getHeader('cache-control') &&\n          matchedOutput.type === 'nextStaticFolder'\n        ) {\n          if (opts.dev && !isNextFont(parsedUrl.pathname)) {\n            res.setHeader('Cache-Control', 'no-store, must-revalidate')\n          } else {\n            res.setHeader(\n              'Cache-Control',\n              'public, max-age=31536000, immutable'\n            )\n          }\n        }\n        if (!(req.method === 'GET' || req.method === 'HEAD')) {\n          res.setHeader('Allow', ['GET', 'HEAD'])\n          res.statusCode = 405\n          return await invokeRender(\n            url.parse('/405', true),\n            '/405',\n            handleIndex,\n            {\n              invokeStatus: 405,\n            }\n          )\n        }\n\n        try {\n          return await serveStatic(req, res, matchedOutput.itemPath, {\n            root: matchedOutput.itemsRoot,\n            // Ensures that etags are not generated for static files when disabled.\n            etag: config.generateEtags,\n          })\n        } catch (err: any) {\n          /**\n           * Hardcoded every possible error status code that could be thrown by \"serveStatic\" method\n           * This is done by searching \"this.error\" inside \"send\" module's source code:\n           * https://github.com/pillarjs/send/blob/master/index.js\n           * https://github.com/pillarjs/send/blob/develop/index.js\n           */\n          const POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC = new Set([\n            // send module will throw 500 when header is already sent or fs.stat error happens\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L392\n            // Note: we will use Next.js built-in 500 page to handle 500 errors\n            // 500,\n\n            // send module will throw 404 when file is missing\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L421\n            // Note: we will use Next.js built-in 404 page to handle 404 errors\n            // 404,\n\n            // send module will throw 403 when redirecting to a directory without enabling directory listing\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L484\n            // Note: Next.js throws a different error (without status code) for directory listing\n            // 403,\n\n            // send module will throw 400 when fails to normalize the path\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L520\n            400,\n\n            // send module will throw 412 with conditional GET request\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L632\n            412,\n\n            // send module will throw 416 when range is not satisfiable\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L669\n            416,\n          ])\n\n          let validErrorStatus = POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC.has(\n            err.statusCode\n          )\n\n          // normalize non-allowed status codes\n          if (!validErrorStatus) {\n            ;(err as any).statusCode = 400\n          }\n\n          if (typeof err.statusCode === 'number') {\n            const invokePath = `/${err.statusCode}`\n            const invokeStatus = err.statusCode\n            res.statusCode = err.statusCode\n            return await invokeRender(\n              url.parse(invokePath, true),\n              invokePath,\n              handleIndex,\n              {\n                invokeStatus,\n              }\n            )\n          }\n          throw err\n        }\n      }\n\n      if (matchedOutput) {\n        invokedOutputs.add(matchedOutput.itemPath)\n\n        return await invokeRender(\n          parsedUrl,\n          parsedUrl.pathname || '/',\n          handleIndex,\n          {\n            invokeOutput: matchedOutput.itemPath,\n          }\n        )\n      }\n\n      if (opts.dev && isChromeDevtoolsWorkspaceUrl(parsedUrl)) {\n        await handleChromeDevtoolsWorkspaceRequest(res, opts, config)\n        return\n      }\n\n      // 404 case\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n\n      let realRequestPathname = parsedUrl.pathname ?? ''\n      if (realRequestPathname) {\n        if (config.basePath) {\n          realRequestPathname = removePathPrefix(\n            realRequestPathname,\n            config.basePath\n          )\n        }\n        if (config.assetPrefix) {\n          realRequestPathname = removePathPrefix(\n            realRequestPathname,\n            config.assetPrefix\n          )\n        }\n        if (config.i18n) {\n          realRequestPathname = removePathPrefix(\n            realRequestPathname,\n            '/' + (getRequestMeta(req, 'locale') ?? '')\n          )\n        }\n      }\n      // For not found static assets, return plain text 404 instead of\n      // full HTML 404 pages to save bandwidth.\n      if (realRequestPathname.startsWith('/_next/static/')) {\n        res.statusCode = 404\n        res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n        res.end('Not Found')\n        return null\n      }\n\n      // Short-circuit favicon.ico serving so that the 404 page doesn't get built as favicon is requested by the browser when loading any route.\n      if (opts.dev && !matchedOutput && parsedUrl.pathname === '/favicon.ico') {\n        res.statusCode = 404\n        res.end('')\n        return null\n      }\n\n      const appNotFound = opts.dev\n        ? developmentBundler?.serverFields.hasAppNotFound\n        : await fsChecker.getItem(UNDERSCORE_NOT_FOUND_ROUTE)\n\n      res.statusCode = 404\n\n      if (appNotFound) {\n        return await invokeRender(\n          parsedUrl,\n          UNDERSCORE_NOT_FOUND_ROUTE,\n          handleIndex,\n          {\n            invokeStatus: 404,\n          }\n        )\n      }\n\n      await invokeRender(parsedUrl, '/404', handleIndex, {\n        invokeStatus: 404,\n      })\n    }\n\n    try {\n      await handleRequest(0)\n    } catch (err) {\n      try {\n        let invokePath = '/500'\n        let invokeStatus = '500'\n\n        if (err instanceof DecodeError) {\n          invokePath = '/400'\n          invokeStatus = '400'\n        } else {\n          console.error(err)\n        }\n        res.statusCode = Number(invokeStatus)\n        return await invokeRender(url.parse(invokePath, true), invokePath, 0, {\n          invokeStatus: res.statusCode,\n        })\n      } catch (err2) {\n        console.error(err2)\n      }\n      res.statusCode = 500\n      res.end('Internal Server Error')\n    }\n  }\n\n  let requestHandler: WorkerRequestHandler = requestHandlerImpl\n  if (config.experimental.testProxy) {\n    // Intercept fetch and other testmode apis.\n    const { wrapRequestHandlerWorker, interceptTestApis } =\n      // eslint-disable-next-line @next/internal/typechecked-require -- experimental/testmode is not built ins next/dist/esm\n      require('next/dist/experimental/testmode/server') as typeof import('../../experimental/testmode/server')\n    requestHandler = wrapRequestHandlerWorker(requestHandler)\n    interceptTestApis()\n    // We treat the intercepted fetch as \"original\" fetch that should be reset to during HMR.\n    originalFetch = globalThis.fetch\n  }\n  requestHandlers[opts.dir] = requestHandler\n\n  const renderServerOpts: Parameters<RenderServer['initialize']>[0] = {\n    port: opts.port,\n    dir: opts.dir,\n    hostname: opts.hostname,\n    minimalMode: opts.minimalMode,\n    dev: !!opts.dev,\n    server: opts.server,\n    serverFields: {\n      ...(developmentBundler?.serverFields || {}),\n      setIsrStatus: devBundlerService?.setIsrStatus.bind(devBundlerService),\n    } satisfies ServerFields,\n    experimentalTestProxy: !!config.experimental.testProxy,\n    experimentalHttpsServer: !!opts.experimentalHttpsServer,\n    bundlerService: devBundlerService,\n    startServerSpan: opts.startServerSpan,\n    quiet: opts.quiet,\n    onDevServerCleanup: opts.onDevServerCleanup,\n  }\n  renderServerOpts.serverFields.routerServerHandler = requestHandlerImpl\n\n  // pre-initialize workers\n  const handlers = await renderServer.instance.initialize(renderServerOpts)\n\n  // this must come after initialize of render server since it's\n  // using initialized methods\n  if (!routerServerGlobal[RouterServerContextSymbol]) {\n    routerServerGlobal[RouterServerContextSymbol] = {}\n  }\n  const relativeProjectDir = path.relative(process.cwd(), opts.dir)\n\n  routerServerGlobal[RouterServerContextSymbol][relativeProjectDir] = {\n    nextConfig: config,\n    hostname: handlers.server.hostname,\n    revalidate: handlers.server.revalidate.bind(handlers.server),\n    render404: handlers.server.render404.bind(handlers.server),\n    experimentalTestProxy: renderServerOpts.experimentalTestProxy,\n    logErrorWithOriginalStack: opts.dev\n      ? handlers.server.logErrorWithOriginalStack.bind(handlers.server)\n      : (err: unknown) => !opts.quiet && Log.error(err),\n    setIsrStatus: devBundlerService?.setIsrStatus.bind(devBundlerService),\n  }\n\n  const logError = async (\n    type: 'uncaughtException' | 'unhandledRejection',\n    err: Error | undefined\n  ) => {\n    if (isPostpone(err)) {\n      // React postpones that are unhandled might end up logged here but they're\n      // not really errors. They're just part of rendering.\n      return\n    }\n    if (type === 'unhandledRejection') {\n      Log.error('unhandledRejection: ', err)\n    } else if (type === 'uncaughtException') {\n      Log.error('uncaughtException: ', err)\n    }\n  }\n\n  process.on('uncaughtException', logError.bind(null, 'uncaughtException'))\n  process.on('unhandledRejection', logError.bind(null, 'unhandledRejection'))\n\n  const resolveRoutes = getResolveRoutes(\n    fsChecker,\n    config,\n    opts,\n    renderServer.instance,\n    renderServerOpts,\n    developmentBundler?.ensureMiddleware\n  )\n\n  const upgradeHandler: WorkerUpgradeHandler = async (req, socket, head) => {\n    try {\n      req.on('error', (_err) => {\n        // TODO: log socket errors?\n        // console.error(_err);\n      })\n      socket.on('error', (_err) => {\n        // TODO: log socket errors?\n        // console.error(_err);\n      })\n\n      if (opts.dev && developmentBundler && req.url) {\n        if (\n          blockCrossSite(req, socket, config.allowedDevOrigins, opts.hostname)\n        ) {\n          return\n        }\n        const { basePath, assetPrefix } = config\n\n        let hmrPrefix = basePath\n\n        // assetPrefix overrides basePath for HMR path\n        if (assetPrefix) {\n          hmrPrefix = normalizedAssetPrefix(assetPrefix)\n\n          if (URL.canParse(hmrPrefix)) {\n            // remove trailing slash from pathname\n            // return empty string if pathname is '/'\n            // to avoid conflicts with '/_next' below\n            hmrPrefix = new URL(hmrPrefix).pathname.replace(/\\/$/, '')\n          }\n        }\n\n        const isHMRRequest = req.url.startsWith(\n          ensureLeadingSlash(`${hmrPrefix}/_next/webpack-hmr`)\n        )\n\n        // only handle HMR requests if the basePath in the request\n        // matches the basePath for the handler responding to the request\n        if (isHMRRequest) {\n          return developmentBundler.hotReloader.onHMR(\n            req,\n            socket,\n            head,\n            (client) => {\n              client.send(\n                JSON.stringify({\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST,\n                  data: devBundlerService?.appIsrManifest || {},\n                } satisfies AppIsrManifestAction)\n              )\n            }\n          )\n        }\n      }\n\n      const res = new MockedResponse({\n        resWriter: () => {\n          throw new Error(\n            'Invariant: did not expect response writer to be written to for upgrade request'\n          )\n        },\n      })\n      const { matchedOutput, parsedUrl } = await resolveRoutes({\n        req,\n        res,\n        isUpgradeReq: true,\n        signal: signalFromNodeResponse(socket),\n      })\n\n      // TODO: allow upgrade requests to pages/app paths?\n      // this was not previously supported\n      if (matchedOutput) {\n        return socket.end()\n      }\n\n      if (parsedUrl.protocol) {\n        return await proxyRequest(req, socket, parsedUrl, head)\n      }\n\n      // If there's no matched output, we don't handle the request as user's\n      // custom WS server may be listening on the same path.\n    } catch (err) {\n      console.error('Error handling upgrade request', err)\n      socket.end()\n    }\n  }\n\n  return {\n    requestHandler,\n    upgradeHandler,\n    server: handlers.server,\n    closeUpgraded() {\n      developmentBundler?.hotReloader?.close()\n    },\n  }\n}\n"], "names": ["url", "path", "loadConfig", "serveStatic", "setupDebug", "Log", "DecodeError", "findPagesDir", "setupFsCheck", "proxyRequest", "isAbortError", "pipeToNodeResponse", "getResolveRoutes", "addRequestMeta", "getRequestMeta", "pathHasPrefix", "removePathPrefix", "setupCompression", "signalFromNodeResponse", "isPostpone", "parseUrl", "parseUrlUtil", "PHASE_PRODUCTION_SERVER", "PHASE_DEVELOPMENT_SERVER", "UNDERSCORE_NOT_FOUND_ROUTE", "RedirectStatusCode", "DevBundlerService", "trace", "ensureLeadingSlash", "getNextPathnameInfo", "getHostname", "detectDomainLocale", "MockedResponse", "HMR_ACTIONS_SENT_TO_BROWSER", "normalizedAssetPrefix", "NEXT_PATCH_SYMBOL", "filterInternalHeaders", "blockCrossSite", "traceGlobals", "NoFallbackError", "RouterServerContextSymbol", "routerServerGlobal", "handleChromeDevtoolsWorkspaceRequest", "isChromeDevtoolsWorkspaceUrl", "debug", "isNextFont", "pathname", "test", "requestHandlers", "initialize", "opts", "process", "env", "NODE_ENV", "dev", "config", "dir", "silent", "compress", "fs<PERSON><PERSON><PERSON>", "minimalMode", "renderServer", "developmentBundler", "devBundlerService", "originalFetch", "globalThis", "fetch", "Telemetry", "require", "telemetry", "distDir", "join", "set", "pagesDir", "appDir", "setupDevBundler", "resetFetch", "setupDevBundlerSpan", "startServerSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "nextConfig", "isCustomServer", "customServer", "turbo", "TURBOPACK", "port", "onDevServerCleanup", "req", "res", "instance", "requestHandlerImpl", "relativeProjectDir", "NEXT_PRIVATE_TEST_HEADERS", "headers", "i18n", "localeDetection", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basePath", "pathnameInfo", "domainLocale", "domains", "hostname", "defaultLocale", "getLocaleRedirect", "parsedUrl", "replace", "redirect", "pathLocale", "locale", "urlParsed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "TemporaryRedirect", "end", "on", "_err", "invokedOutputs", "Set", "invokeRender", "invoke<PERSON><PERSON>", "handleIndex", "additionalRequestMeta", "startsWith", "handleLocale", "getMiddlewareMatchers", "length", "handlers", "Error", "query", "key", "initResult", "renderServerOpts", "requestHandler", "err", "handleRequest", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "origUrl", "assetPrefix", "parse", "hotReloaderResult", "hotReloader", "run", "finished", "resHeaders", "bodyStream", "matchedOutput", "resolveRoutes", "isUpgradeReq", "signal", "closed", "type", "Object", "keys", "result", "destination", "format", "PermanentRedirect", "protocol", "undefined", "cloneBodyStream", "experimental", "proxyTimeout", "fsPath", "itemPath", "appFiles", "has", "pageFiles", "message", "invoke<PERSON>tatus", "invokeError", "error", "<PERSON><PERSON><PERSON><PERSON>", "method", "root", "itemsRoot", "etag", "generateEtags", "POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC", "validErrorStatus", "add", "invokeOutput", "realRequestPathname", "appNotFound", "serverFields", "hasAppNotFound", "getItem", "console", "Number", "err2", "testProxy", "wrapRequestHandlerWorker", "interceptTestApis", "server", "setIsrStatus", "bind", "experimentalTestProxy", "experimentalHttpsServer", "bundlerService", "quiet", "routerServerHandler", "relative", "cwd", "revalidate", "render404", "logErrorWithOriginalStack", "logError", "ensureMiddleware", "upgradeHandler", "socket", "head", "hmrPrefix", "URL", "canParse", "isHMRRequest", "onHMR", "client", "send", "JSON", "stringify", "action", "ISR_MANIFEST", "data", "appIsrManifest", "resWriter", "closeUpgraded", "close"], "mappings": "AAAA,oDAAoD;AAKpD,6EAA6E;AAC7E,OAAO,sBAAqB;AAC5B,OAAO,kBAAiB;AAExB,OAAOA,SAAS,MAAK;AACrB,OAAOC,UAAU,OAAM;AACvB,OAAOC,gBAAgB,YAAW;AAClC,SAASC,WAAW,QAAQ,kBAAiB;AAC7C,OAAOC,gBAAgB,2BAA0B;AACjD,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,YAAY,QAAQ,2BAA0B;AACvD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,YAAY,QAAQ,+BAA8B;AAC3D,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,mBAAkB;AACnE,SAASC,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,cAAc,EAAEC,cAAc,QAAQ,kBAAiB;AAChE,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,OAAOC,sBAAsB,iCAAgC;AAC7D,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,UAAU,QAAQ,6BAA4B;AACvD,SAASC,YAAYC,YAAY,QAAQ,0CAAyC;AAElF,SACEC,uBAAuB,EACvBC,wBAAwB,EACxBC,0BAA0B,QACrB,6BAA4B;AACnC,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,iBAAiB,QAAQ,wBAAuB;AACzD,SAAoBC,KAAK,QAAQ,cAAa;AAC9C,SAASC,kBAAkB,QAAQ,kDAAiD;AACpF,SAASC,mBAAmB,QAAQ,uDAAsD;AAC1F,SAASC,WAAW,QAAQ,gCAA+B;AAC3D,SAASC,kBAAkB,QAAQ,6CAA4C;AAC/E,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,SACEC,2BAA2B,QAEtB,4BAA2B;AAClC,SAASC,qBAAqB,QAAQ,2CAA0C;AAChF,SAASC,iBAAiB,QAAQ,gBAAe;AAEjD,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,cAAc,QAAQ,kCAAiC;AAChE,SAASC,YAAY,QAAQ,qBAAoB;AACjD,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SACEC,yBAAyB,EACzBC,kBAAkB,QACb,uCAAsC;AAC7C,SACEC,oCAAoC,EACpCC,4BAA4B,QACvB,8BAA6B;AAEpC,MAAMC,QAAQxC,WAAW;AACzB,MAAMyC,aAAa,CAACC,WAClBA,YAAY,4CAA4CC,IAAI,CAACD;AAc/D,MAAME,kBAAwD,CAAC;AAE/D,OAAO,eAAeC,WAAWC,IAahC;IACC,IAAI,CAACC,QAAQC,GAAG,CAACC,QAAQ,EAAE;QACzB,0BAA0B;QAC1BF,QAAQC,GAAG,CAACC,QAAQ,GAAGH,KAAKI,GAAG,GAAG,gBAAgB;IACpD;IAEA,MAAMC,SAAS,MAAMrD,WACnBgD,KAAKI,GAAG,GAAG/B,2BAA2BD,yBACtC4B,KAAKM,GAAG,EACR;QAAEC,QAAQ;IAAM;IAGlB,IAAIC;IAEJ,IAAIH,CAAAA,0BAAAA,OAAQG,QAAQ,MAAK,OAAO;QAC9BA,WAAWzC;IACb;IAEA,MAAM0C,YAAY,MAAMnD,aAAa;QACnC8C,KAAKJ,KAAKI,GAAG;QACbE,KAAKN,KAAKM,GAAG;QACbD;QACAK,aAAaV,KAAKU,WAAW;IAC/B;IAEA,MAAMC,eAAyC,CAAC;IAEhD,IAAIC;IAEJ,IAAIC;IAEJ,IAAIC,gBAAgBC,WAAWC,KAAK;IAEpC,IAAIhB,KAAKI,GAAG,EAAE;QACZ,MAAM,EAAEa,SAAS,EAAE,GACjBC,QAAQ;QAEV,MAAMC,YAAY,IAAIF,UAAU;YAC9BG,SAASrE,KAAKsE,IAAI,CAACrB,KAAKM,GAAG,EAAED,OAAOe,OAAO;QAC7C;QACAhC,aAAakC,GAAG,CAAC,aAAaH;QAE9B,MAAM,EAAEI,QAAQ,EAAEC,MAAM,EAAE,GAAGnE,aAAa2C,KAAKM,GAAG;QAElD,MAAM,EAAEmB,eAAe,EAAE,GACvBP,QAAQ;QAEV,MAAMQ,aAAa;YACjBX,WAAWC,KAAK,GAAGF;YACjBC,UAAsC,CAAC9B,kBAAkB,GAAG;QAChE;QAEA,MAAM0C,sBAAsB3B,KAAK4B,eAAe,GAC5C5B,KAAK4B,eAAe,CAACC,UAAU,CAAC,uBAChCpD,MAAM;QACVmC,qBAAqB,MAAMe,oBAAoBG,YAAY,CAAC,IAC1DL,gBAAgB;gBACd,6HAA6H;gBAC7Hd;gBACAa;gBACAD;gBACAJ;gBACAV;gBACAH,KAAKN,KAAKM,GAAG;gBACbyB,YAAY1B;gBACZ2B,gBAAgBhC,KAAKiC,YAAY;gBACjCC,OAAO,CAAC,CAACjC,QAAQC,GAAG,CAACiC,SAAS;gBAC9BC,MAAMpC,KAAKoC,IAAI;gBACfC,oBAAoBrC,KAAKqC,kBAAkB;gBAC3CX;YACF;QAGFb,oBAAoB,IAAIrC,kBACtBoC,oBACA,yEAAyE;QACzE,mBAAmB;QACnB,CAAC0B,KAAKC;YACJ,OAAOzC,eAAe,CAACE,KAAKM,GAAG,CAAC,CAACgC,KAAKC;QACxC;IAEJ;IAEA5B,aAAa6B,QAAQ,GACnBtB,QAAQ;IAEV,MAAMuB,qBAA2C,OAAOH,KAAKC;QAC3D5E,eAAe2E,KAAK,sBAAsBI;QAE1C,gEAAgE;QAChE,IAAI,CAACzC,QAAQC,GAAG,CAACyC,yBAAyB,EAAE;YAC1CzD,sBAAsBoD,IAAIM,OAAO;QACnC;QAEA,IACE,CAAC5C,KAAKU,WAAW,IACjBL,OAAOwC,IAAI,IACXxC,OAAOwC,IAAI,CAACC,eAAe,KAAK,OAChC;gBAuBgCR;YAtBhC,MAAMS,WAAW,AAACT,CAAAA,IAAIxF,GAAG,IAAI,EAAC,EAAGkG,KAAK,CAAC,KAAK;YAC5C,IAAIC,aAAaF,QAAQ,CAAC,EAAE,IAAI;YAEhC,IAAI1C,OAAO6C,QAAQ,EAAE;gBACnBD,aAAanF,iBAAiBmF,YAAY5C,OAAO6C,QAAQ;YAC3D;YAEA,MAAMC,eAAexE,oBAAoBsE,YAAY;gBACnDlB,YAAY1B;YACd;YAEA,MAAM+C,eAAevE,mBACnBwB,OAAOwC,IAAI,CAACQ,OAAO,EACnBzE,YAAY;gBAAE0E,UAAUL;YAAW,GAAGX,IAAIM,OAAO;YAGnD,MAAMW,gBACJH,CAAAA,gCAAAA,aAAcG,aAAa,KAAIlD,OAAOwC,IAAI,CAACU,aAAa;YAE1D,MAAM,EAAEC,iBAAiB,EAAE,GACzBtC,QAAQ;YAEV,MAAMuC,YAAYtF,cAAcmE,QAAAA,IAAIxF,GAAG,IAAI,uBAAZ,AAACwF,MAAgBoB,OAAO,CAAC,QAAQ;YAEhE,MAAMC,WAAWH,kBAAkB;gBACjCD;gBACAH;gBACAR,SAASN,IAAIM,OAAO;gBACpBb,YAAY1B;gBACZuD,YAAYT,aAAaU,MAAM;gBAC/BC,WAAW;oBACT,GAAGL,SAAS;oBACZ7D,UAAUuD,aAAaU,MAAM,GACzB,CAAC,CAAC,EAAEV,aAAaU,MAAM,GAAGZ,YAAY,GACtCA;gBACN;YACF;YAEA,IAAIU,UAAU;gBACZpB,IAAIwB,SAAS,CAAC,YAAYJ;gBAC1BpB,IAAIyB,UAAU,GAAGzF,mBAAmB0F,iBAAiB;gBACrD1B,IAAI2B,GAAG,CAACP;gBACR;YACF;QACF;QAEA,IAAInD,UAAU;YACZ,uCAAuC;YACvCA,SAAS8B,KAAKC,KAAK,KAAO;QAC5B;QACAD,IAAI6B,EAAE,CAAC,SAAS,CAACC;QACf,2BAA2B;QAC7B;QACA7B,IAAI4B,EAAE,CAAC,SAAS,CAACC;QACf,2BAA2B;QAC7B;QAEA,MAAMC,iBAAiB,IAAIC;QAE3B,eAAeC,aACbd,SAAiC,EACjCe,UAAkB,EAClBC,WAAmB,EACnBC,qBAAmC;gBAiBjCjE;YAfF,6DAA6D;YAC7D,sCAAsC;YACtC,IACEJ,OAAOwC,IAAI,IACX/E,iBAAiB0G,YAAYnE,OAAO6C,QAAQ,EAAEyB,UAAU,CACtD,CAAC,CAAC,EAAE/G,eAAe0E,KAAK,UAAU,IAAI,CAAC,GAEzC;gBACAkC,aAAa/D,UAAUmE,YAAY,CACjC9G,iBAAiB0G,YAAYnE,OAAO6C,QAAQ,GAC5CtD,QAAQ;YACZ;YAEA,IACE0C,IAAIM,OAAO,CAAC,gBAAgB,MAC5BnC,mCAAAA,UAAUoE,qBAAqB,uBAA/BpE,iCAAmCqE,MAAM,KACzChH,iBAAiB0G,YAAYnE,OAAO6C,QAAQ,MAAM,QAClD;gBACAX,IAAIwB,SAAS,CAAC,yBAAyBN,UAAU7D,QAAQ,IAAI;gBAC7D2C,IAAIyB,UAAU,GAAG;gBACjBzB,IAAIwB,SAAS,CAAC,gBAAgB;gBAC9BxB,IAAI2B,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,IAAI,CAACa,UAAU;gBACb,MAAM,qBAA+C,CAA/C,IAAIC,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEArH,eAAe2E,KAAK,cAAckC;YAClC7G,eAAe2E,KAAK,eAAemB,UAAUwB,KAAK;YAClDtH,eAAe2E,KAAK,oBAAoB;YAExC,IAAK,MAAM4C,OAAOR,yBAAyB,CAAC,EAAG;gBAC7C/G,eACE2E,KACA4C,KACAR,qBAAsB,CAACQ,IAAyB;YAEpD;YAEAxF,MAAM,gBAAgB4C,IAAIxF,GAAG,EAAEwF,IAAIM,OAAO;YAE1C,IAAI;oBAEMjC;gBADR,MAAMwE,aACJ,OAAMxE,iCAAAA,yBAAAA,aAAc6B,QAAQ,qBAAtB7B,uBAAwBZ,UAAU,CAACqF;gBAC3C,IAAI;oBACF,OAAMD,8BAAAA,WAAYE,cAAc,CAAC/C,KAAKC;gBACxC,EAAE,OAAO+C,KAAK;oBACZ,IAAIA,eAAejG,iBAAiB;wBAClC,2BAA2B;wBAC3B,MAAMkG,cAAcd,cAAc;wBAClC;oBACF;oBACA,MAAMa;gBACR;gBACA;YACF,EAAE,OAAOE,GAAG;gBACV,qEAAqE;gBACrE,mEAAmE;gBACnE,cAAc;gBACd,IAAIhI,aAAagI,IAAI;oBACnB;gBACF;gBACA,MAAMA;YACR;QACF;QAEA,MAAMD,gBAAgB,OAAOd;YAC3B,IAAIA,cAAc,GAAG;gBACnB,MAAM,qBAAkE,CAAlE,IAAIO,MAAM,CAAC,2CAA2C,EAAE1C,IAAIxF,GAAG,EAAE,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;YAEA,4BAA4B;YAC5B,IAAI8D,oBAAoB;gBACtB,IAAIzB,eAAemD,KAAKC,KAAKlC,OAAOoF,iBAAiB,EAAEzF,KAAKsD,QAAQ,GAAG;oBACrE;gBACF;gBAEA,MAAMoC,UAAUpD,IAAIxF,GAAG,IAAI;gBAE3B,qEAAqE;gBACrE,4DAA4D;gBAC5D,IAAIuD,OAAO6C,QAAQ,IAAIrF,cAAc6H,SAASrF,OAAO6C,QAAQ,GAAG;oBAC9DZ,IAAIxF,GAAG,GAAGgB,iBAAiB4H,SAASrF,OAAO6C,QAAQ;gBACrD,OAAO,IACL7C,OAAOsF,WAAW,IAClB9H,cAAc6H,SAASrF,OAAOsF,WAAW,GACzC;oBACArD,IAAIxF,GAAG,GAAGgB,iBAAiB4H,SAASrF,OAAOsF,WAAW;gBACxD;gBAEA,MAAMlC,YAAY3G,IAAI8I,KAAK,CAACtD,IAAIxF,GAAG,IAAI;gBAEvC,MAAM+I,oBAAoB,MAAMjF,mBAAmBkF,WAAW,CAACC,GAAG,CAChEzD,KACAC,KACAkB;gBAGF,IAAIoC,kBAAkBG,QAAQ,EAAE;oBAC9B,OAAOH;gBACT;gBAEAvD,IAAIxF,GAAG,GAAG4I;YACZ;YAEA,MAAM,EACJM,QAAQ,EACRvC,SAAS,EACTO,UAAU,EACViC,UAAU,EACVC,UAAU,EACVC,aAAa,EACd,GAAG,MAAMC,cAAc;gBACtB9D;gBACAC;gBACA8D,cAAc;gBACdC,QAAQtI,uBAAuBuE;gBAC/B8B;YACF;YAEA,IAAI9B,IAAIgE,MAAM,IAAIhE,IAAIyD,QAAQ,EAAE;gBAC9B;YACF;YAEA,IAAIpF,sBAAsBuF,CAAAA,iCAAAA,cAAeK,IAAI,MAAK,oBAAoB;gBACpE,MAAMd,UAAUpD,IAAIxF,GAAG,IAAI;gBAE3B,IAAIuD,OAAO6C,QAAQ,IAAIrF,cAAc6H,SAASrF,OAAO6C,QAAQ,GAAG;oBAC9DZ,IAAIxF,GAAG,GAAGgB,iBAAiB4H,SAASrF,OAAO6C,QAAQ;gBACrD,OAAO,IACL7C,OAAOsF,WAAW,IAClB9H,cAAc6H,SAASrF,OAAOsF,WAAW,GACzC;oBACArD,IAAIxF,GAAG,GAAGgB,iBAAiB4H,SAASrF,OAAOsF,WAAW;gBACxD;gBAEA,IAAIM,YAAY;oBACd,KAAK,MAAMf,OAAOuB,OAAOC,IAAI,CAACT,YAAa;wBACzC1D,IAAIwB,SAAS,CAACmB,KAAKe,UAAU,CAACf,IAAI;oBACpC;gBACF;gBACA,MAAMyB,SAAS,MAAM/F,mBAAmByE,cAAc,CAAC/C,KAAKC;gBAE5D,IAAIoE,OAAOX,QAAQ,EAAE;oBACnB;gBACF;gBACA,sEAAsE;gBACtE1D,IAAIxF,GAAG,GAAG4I;YACZ;YAEAhG,MAAM,mBAAmB4C,IAAIxF,GAAG,EAAE;gBAChCqJ;gBACAnC;gBACAiC;gBACAC,YAAY,CAAC,CAACA;gBACdzC,WAAW;oBACT7D,UAAU6D,UAAU7D,QAAQ;oBAC5BqF,OAAOxB,UAAUwB,KAAK;gBACxB;gBACAe;YACF;YAEA,0CAA0C;YAC1C,KAAK,MAAMd,OAAOuB,OAAOC,IAAI,CAACT,cAAc,CAAC,GAAI;gBAC/C1D,IAAIwB,SAAS,CAACmB,KAAKe,UAAU,CAACf,IAAI;YACpC;YAEA,kBAAkB;YAClB,IAAI,CAACgB,cAAclC,cAAcA,aAAa,OAAOA,aAAa,KAAK;gBACrE,MAAM4C,cAAc9J,IAAI+J,MAAM,CAACpD;gBAC/BlB,IAAIyB,UAAU,GAAGA;gBACjBzB,IAAIwB,SAAS,CAAC,YAAY6C;gBAE1B,IAAI5C,eAAezF,mBAAmBuI,iBAAiB,EAAE;oBACvDvE,IAAIwB,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE6C,aAAa;gBACjD;gBACA,OAAOrE,IAAI2B,GAAG,CAAC0C;YACjB;YAEA,kCAAkC;YAClC,IAAIV,YAAY;gBACd3D,IAAIyB,UAAU,GAAGA,cAAc;gBAC/B,OAAO,MAAMvG,mBAAmByI,YAAY3D;YAC9C;YAEA,IAAIyD,YAAYvC,UAAUsD,QAAQ,EAAE;oBAMhCnJ;gBALF,OAAO,MAAML,aACX+E,KACAC,KACAkB,WACAuD,YACApJ,kBAAAA,eAAe0E,KAAK,oCAApB1E,gBAAqCqJ,eAAe,IACpD5G,OAAO6G,YAAY,CAACC,YAAY;YAEpC;YAEA,IAAIhB,CAAAA,iCAAAA,cAAeiB,MAAM,KAAIjB,cAAckB,QAAQ,EAAE;gBACnD,IACErH,KAAKI,GAAG,IACPK,CAAAA,UAAU6G,QAAQ,CAACC,GAAG,CAACpB,cAAckB,QAAQ,KAC5C5G,UAAU+G,SAAS,CAACD,GAAG,CAACpB,cAAckB,QAAQ,CAAA,GAChD;oBACA9E,IAAIyB,UAAU,GAAG;oBACjB,MAAMyD,UAAU,CAAC,2DAA2D,EAAEtB,cAAckB,QAAQ,CAAC,8DAA8D,CAAC;oBACpK,MAAM9C,aAAad,WAAW,WAAWgB,aAAa;wBACpDiD,cAAc;wBACdC,aAAa,qBAAkB,CAAlB,IAAI3C,MAAMyC,UAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAiB;oBAChC;oBACAtK,IAAIyK,KAAK,CAACH;oBACV;gBACF;gBAEA,IACE,CAAClF,IAAIsF,SAAS,CAAC,oBACf1B,cAAcK,IAAI,KAAK,oBACvB;oBACA,IAAIxG,KAAKI,GAAG,IAAI,CAACT,WAAW8D,UAAU7D,QAAQ,GAAG;wBAC/C2C,IAAIwB,SAAS,CAAC,iBAAiB;oBACjC,OAAO;wBACLxB,IAAIwB,SAAS,CACX,iBACA;oBAEJ;gBACF;gBACA,IAAI,CAAEzB,CAAAA,IAAIwF,MAAM,KAAK,SAASxF,IAAIwF,MAAM,KAAK,MAAK,GAAI;oBACpDvF,IAAIwB,SAAS,CAAC,SAAS;wBAAC;wBAAO;qBAAO;oBACtCxB,IAAIyB,UAAU,GAAG;oBACjB,OAAO,MAAMO,aACXzH,IAAI8I,KAAK,CAAC,QAAQ,OAClB,QACAnB,aACA;wBACEiD,cAAc;oBAChB;gBAEJ;gBAEA,IAAI;oBACF,OAAO,MAAMzK,YAAYqF,KAAKC,KAAK4D,cAAckB,QAAQ,EAAE;wBACzDU,MAAM5B,cAAc6B,SAAS;wBAC7B,uEAAuE;wBACvEC,MAAM5H,OAAO6H,aAAa;oBAC5B;gBACF,EAAE,OAAO5C,KAAU;oBACjB;;;;;WAKC,GACD,MAAM6C,wCAAwC,IAAI7D,IAAI;wBACpD,kFAAkF;wBAClF,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,kDAAkD;wBAClD,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,gGAAgG;wBAChG,+FAA+F;wBAC/F,qFAAqF;wBACrF,OAAO;wBAEP,8DAA8D;wBAC9D,+FAA+F;wBAC/F;wBAEA,0DAA0D;wBAC1D,+FAA+F;wBAC/F;wBAEA,2DAA2D;wBAC3D,+FAA+F;wBAC/F;qBACD;oBAED,IAAI8D,mBAAmBD,sCAAsCZ,GAAG,CAC9DjC,IAAItB,UAAU;oBAGhB,qCAAqC;oBACrC,IAAI,CAACoE,kBAAkB;;wBACnB9C,IAAYtB,UAAU,GAAG;oBAC7B;oBAEA,IAAI,OAAOsB,IAAItB,UAAU,KAAK,UAAU;wBACtC,MAAMQ,aAAa,CAAC,CAAC,EAAEc,IAAItB,UAAU,EAAE;wBACvC,MAAM0D,eAAepC,IAAItB,UAAU;wBACnCzB,IAAIyB,UAAU,GAAGsB,IAAItB,UAAU;wBAC/B,OAAO,MAAMO,aACXzH,IAAI8I,KAAK,CAACpB,YAAY,OACtBA,YACAC,aACA;4BACEiD;wBACF;oBAEJ;oBACA,MAAMpC;gBACR;YACF;YAEA,IAAIa,eAAe;gBACjB9B,eAAegE,GAAG,CAAClC,cAAckB,QAAQ;gBAEzC,OAAO,MAAM9C,aACXd,WACAA,UAAU7D,QAAQ,IAAI,KACtB6E,aACA;oBACE6D,cAAcnC,cAAckB,QAAQ;gBACtC;YAEJ;YAEA,IAAIrH,KAAKI,GAAG,IAAIX,6BAA6BgE,YAAY;gBACvD,MAAMjE,qCAAqC+C,KAAKvC,MAAMK;gBACtD;YACF;YAEA,WAAW;YACXkC,IAAIwB,SAAS,CACX,iBACA;YAGF,IAAIwE,sBAAsB9E,UAAU7D,QAAQ,IAAI;YAChD,IAAI2I,qBAAqB;gBACvB,IAAIlI,OAAO6C,QAAQ,EAAE;oBACnBqF,sBAAsBzK,iBACpByK,qBACAlI,OAAO6C,QAAQ;gBAEnB;gBACA,IAAI7C,OAAOsF,WAAW,EAAE;oBACtB4C,sBAAsBzK,iBACpByK,qBACAlI,OAAOsF,WAAW;gBAEtB;gBACA,IAAItF,OAAOwC,IAAI,EAAE;oBACf0F,sBAAsBzK,iBACpByK,qBACA,MAAO3K,CAAAA,eAAe0E,KAAK,aAAa,EAAC;gBAE7C;YACF;YACA,gEAAgE;YAChE,yCAAyC;YACzC,IAAIiG,oBAAoB5D,UAAU,CAAC,mBAAmB;gBACpDpC,IAAIyB,UAAU,GAAG;gBACjBzB,IAAIwB,SAAS,CAAC,gBAAgB;gBAC9BxB,IAAI2B,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,0IAA0I;YAC1I,IAAIlE,KAAKI,GAAG,IAAI,CAAC+F,iBAAiB1C,UAAU7D,QAAQ,KAAK,gBAAgB;gBACvE2C,IAAIyB,UAAU,GAAG;gBACjBzB,IAAI2B,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,MAAMsE,cAAcxI,KAAKI,GAAG,GACxBQ,sCAAAA,mBAAoB6H,YAAY,CAACC,cAAc,GAC/C,MAAMjI,UAAUkI,OAAO,CAACrK;YAE5BiE,IAAIyB,UAAU,GAAG;YAEjB,IAAIwE,aAAa;gBACf,OAAO,MAAMjE,aACXd,WACAnF,4BACAmG,aACA;oBACEiD,cAAc;gBAChB;YAEJ;YAEA,MAAMnD,aAAad,WAAW,QAAQgB,aAAa;gBACjDiD,cAAc;YAChB;QACF;QAEA,IAAI;YACF,MAAMnC,cAAc;QACtB,EAAE,OAAOD,KAAK;YACZ,IAAI;gBACF,IAAId,aAAa;gBACjB,IAAIkD,eAAe;gBAEnB,IAAIpC,eAAelI,aAAa;oBAC9BoH,aAAa;oBACbkD,eAAe;gBACjB,OAAO;oBACLkB,QAAQhB,KAAK,CAACtC;gBAChB;gBACA/C,IAAIyB,UAAU,GAAG6E,OAAOnB;gBACxB,OAAO,MAAMnD,aAAazH,IAAI8I,KAAK,CAACpB,YAAY,OAAOA,YAAY,GAAG;oBACpEkD,cAAcnF,IAAIyB,UAAU;gBAC9B;YACF,EAAE,OAAO8E,MAAM;gBACbF,QAAQhB,KAAK,CAACkB;YAChB;YACAvG,IAAIyB,UAAU,GAAG;YACjBzB,IAAI2B,GAAG,CAAC;QACV;IACF;IAEA,IAAImB,iBAAuC5C;IAC3C,IAAIpC,OAAO6G,YAAY,CAAC6B,SAAS,EAAE;QACjC,2CAA2C;QAC3C,MAAM,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAE,GACnD,sHAAsH;QACtH/H,QAAQ;QACVmE,iBAAiB2D,yBAAyB3D;QAC1C4D;QACA,yFAAyF;QACzFnI,gBAAgBC,WAAWC,KAAK;IAClC;IACAlB,eAAe,CAACE,KAAKM,GAAG,CAAC,GAAG+E;IAE5B,MAAMD,mBAA8D;QAClEhD,MAAMpC,KAAKoC,IAAI;QACf9B,KAAKN,KAAKM,GAAG;QACbgD,UAAUtD,KAAKsD,QAAQ;QACvB5C,aAAaV,KAAKU,WAAW;QAC7BN,KAAK,CAAC,CAACJ,KAAKI,GAAG;QACf8I,QAAQlJ,KAAKkJ,MAAM;QACnBT,cAAc;YACZ,GAAI7H,CAAAA,sCAAAA,mBAAoB6H,YAAY,KAAI,CAAC,CAAC;YAC1CU,YAAY,EAAEtI,qCAAAA,kBAAmBsI,YAAY,CAACC,IAAI,CAACvI;QACrD;QACAwI,uBAAuB,CAAC,CAAChJ,OAAO6G,YAAY,CAAC6B,SAAS;QACtDO,yBAAyB,CAAC,CAACtJ,KAAKsJ,uBAAuB;QACvDC,gBAAgB1I;QAChBe,iBAAiB5B,KAAK4B,eAAe;QACrC4H,OAAOxJ,KAAKwJ,KAAK;QACjBnH,oBAAoBrC,KAAKqC,kBAAkB;IAC7C;IACA+C,iBAAiBqD,YAAY,CAACgB,mBAAmB,GAAGhH;IAEpD,yBAAyB;IACzB,MAAMsC,WAAW,MAAMpE,aAAa6B,QAAQ,CAACzC,UAAU,CAACqF;IAExD,8DAA8D;IAC9D,4BAA4B;IAC5B,IAAI,CAAC7F,kBAAkB,CAACD,0BAA0B,EAAE;QAClDC,kBAAkB,CAACD,0BAA0B,GAAG,CAAC;IACnD;IACA,MAAMoD,qBAAqB3F,KAAK2M,QAAQ,CAACzJ,QAAQ0J,GAAG,IAAI3J,KAAKM,GAAG;IAEhEf,kBAAkB,CAACD,0BAA0B,CAACoD,mBAAmB,GAAG;QAClEX,YAAY1B;QACZiD,UAAUyB,SAASmE,MAAM,CAAC5F,QAAQ;QAClCsG,YAAY7E,SAASmE,MAAM,CAACU,UAAU,CAACR,IAAI,CAACrE,SAASmE,MAAM;QAC3DW,WAAW9E,SAASmE,MAAM,CAACW,SAAS,CAACT,IAAI,CAACrE,SAASmE,MAAM;QACzDG,uBAAuBjE,iBAAiBiE,qBAAqB;QAC7DS,2BAA2B9J,KAAKI,GAAG,GAC/B2E,SAASmE,MAAM,CAACY,yBAAyB,CAACV,IAAI,CAACrE,SAASmE,MAAM,IAC9D,CAAC5D,MAAiB,CAACtF,KAAKwJ,KAAK,IAAIrM,IAAIyK,KAAK,CAACtC;QAC/C6D,YAAY,EAAEtI,qCAAAA,kBAAmBsI,YAAY,CAACC,IAAI,CAACvI;IACrD;IAEA,MAAMkJ,WAAW,OACfvD,MACAlB;QAEA,IAAIrH,WAAWqH,MAAM;YACnB,0EAA0E;YAC1E,qDAAqD;YACrD;QACF;QACA,IAAIkB,SAAS,sBAAsB;YACjCrJ,IAAIyK,KAAK,CAAC,wBAAwBtC;QACpC,OAAO,IAAIkB,SAAS,qBAAqB;YACvCrJ,IAAIyK,KAAK,CAAC,uBAAuBtC;QACnC;IACF;IAEArF,QAAQkE,EAAE,CAAC,qBAAqB4F,SAASX,IAAI,CAAC,MAAM;IACpDnJ,QAAQkE,EAAE,CAAC,sBAAsB4F,SAASX,IAAI,CAAC,MAAM;IAErD,MAAMhD,gBAAgB1I,iBACpB+C,WACAJ,QACAL,MACAW,aAAa6B,QAAQ,EACrB4C,kBACAxE,sCAAAA,mBAAoBoJ,gBAAgB;IAGtC,MAAMC,iBAAuC,OAAO3H,KAAK4H,QAAQC;QAC/D,IAAI;YACF7H,IAAI6B,EAAE,CAAC,SAAS,CAACC;YACf,2BAA2B;YAC3B,uBAAuB;YACzB;YACA8F,OAAO/F,EAAE,CAAC,SAAS,CAACC;YAClB,2BAA2B;YAC3B,uBAAuB;YACzB;YAEA,IAAIpE,KAAKI,GAAG,IAAIQ,sBAAsB0B,IAAIxF,GAAG,EAAE;gBAC7C,IACEqC,eAAemD,KAAK4H,QAAQ7J,OAAOoF,iBAAiB,EAAEzF,KAAKsD,QAAQ,GACnE;oBACA;gBACF;gBACA,MAAM,EAAEJ,QAAQ,EAAEyC,WAAW,EAAE,GAAGtF;gBAElC,IAAI+J,YAAYlH;gBAEhB,8CAA8C;gBAC9C,IAAIyC,aAAa;oBACfyE,YAAYpL,sBAAsB2G;oBAElC,IAAI0E,IAAIC,QAAQ,CAACF,YAAY;wBAC3B,sCAAsC;wBACtC,yCAAyC;wBACzC,yCAAyC;wBACzCA,YAAY,IAAIC,IAAID,WAAWxK,QAAQ,CAAC8D,OAAO,CAAC,OAAO;oBACzD;gBACF;gBAEA,MAAM6G,eAAejI,IAAIxF,GAAG,CAAC6H,UAAU,CACrCjG,mBAAmB,GAAG0L,UAAU,kBAAkB,CAAC;gBAGrD,0DAA0D;gBAC1D,iEAAiE;gBACjE,IAAIG,cAAc;oBAChB,OAAO3J,mBAAmBkF,WAAW,CAAC0E,KAAK,CACzClI,KACA4H,QACAC,MACA,CAACM;wBACCA,OAAOC,IAAI,CACTC,KAAKC,SAAS,CAAC;4BACbC,QAAQ9L,4BAA4B+L,YAAY;4BAChDC,MAAMlK,CAAAA,qCAAAA,kBAAmBmK,cAAc,KAAI,CAAC;wBAC9C;oBAEJ;gBAEJ;YACF;YAEA,MAAMzI,MAAM,IAAIzD,eAAe;gBAC7BmM,WAAW;oBACT,MAAM,qBAEL,CAFK,IAAIjG,MACR,mFADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YACA,MAAM,EAAEmB,aAAa,EAAE1C,SAAS,EAAE,GAAG,MAAM2C,cAAc;gBACvD9D;gBACAC;gBACA8D,cAAc;gBACdC,QAAQtI,uBAAuBkM;YACjC;YAEA,mDAAmD;YACnD,oCAAoC;YACpC,IAAI/D,eAAe;gBACjB,OAAO+D,OAAOhG,GAAG;YACnB;YAEA,IAAIT,UAAUsD,QAAQ,EAAE;gBACtB,OAAO,MAAMxJ,aAAa+E,KAAK4H,QAAQzG,WAAW0G;YACpD;QAEA,sEAAsE;QACtE,sDAAsD;QACxD,EAAE,OAAO7E,KAAK;YACZsD,QAAQhB,KAAK,CAAC,kCAAkCtC;YAChD4E,OAAOhG,GAAG;QACZ;IACF;IAEA,OAAO;QACLmB;QACA4E;QACAf,QAAQnE,SAASmE,MAAM;QACvBgC;gBACEtK;YAAAA,uCAAAA,kCAAAA,mBAAoBkF,WAAW,qBAA/BlF,gCAAiCuK,KAAK;QACxC;IACF;AACF", "ignoreList": [0]}