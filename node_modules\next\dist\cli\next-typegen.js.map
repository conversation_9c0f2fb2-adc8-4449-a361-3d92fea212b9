{"version": 3, "sources": ["../../src/cli/next-typegen.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport { existsSync } from 'fs'\nimport path, { join } from 'path'\nimport { mkdir } from 'fs/promises'\n\nimport loadConfig from '../server/config'\nimport { printAndExit } from '../server/lib/utils'\nimport { PHASE_PRODUCTION_BUILD } from '../shared/lib/constants'\nimport { getProjectDir } from '../lib/get-project-dir'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport { verifyTypeScriptSetup } from '../lib/verify-typescript-setup'\nimport {\n  createPagesMapping,\n  collectAppFiles,\n  collectPagesFiles,\n  processPageRoutes,\n  processAppRoutes,\n  processLayoutRoutes,\n  extractSlotsFromAppRoutes,\n  extractSlotsFromDefaultFiles,\n  combineSlots,\n  type RouteInfo,\n  type SlotInfo,\n} from '../build/entries'\nimport { PAGE_TYPES } from '../lib/page-types'\n\nimport {\n  createRouteTypesManifest,\n  writeRouteTypesManifest,\n  writeValidatorFile,\n} from '../server/lib/router-utils/route-types-utils'\nimport { createValidFileMatcher } from '../server/lib/find-page-file'\n\nexport type NextTypegenOptions = {\n  dir?: string\n}\n\nconst nextTypegen = async (\n  _options: NextTypegenOptions,\n  directory?: string\n) => {\n  const baseDir = getProjectDir(directory)\n\n  // Check if the provided directory exists\n  if (!existsSync(baseDir)) {\n    printAndExit(`> No such directory exists as the project root: ${baseDir}`)\n  }\n\n  const nextConfig = await loadConfig(PHASE_PRODUCTION_BUILD, baseDir)\n  const distDir = join(baseDir, nextConfig.distDir)\n  const { pagesDir, appDir } = findPagesDir(baseDir)\n\n  await verifyTypeScriptSetup({\n    dir: baseDir,\n    distDir: nextConfig.distDir,\n    intentDirs: [pagesDir, appDir].filter(Boolean) as string[],\n    typeCheckPreflight: false,\n    tsconfigPath: nextConfig.typescript.tsconfigPath,\n    disableStaticImages: nextConfig.images.disableStaticImages,\n    hasAppDir: !!appDir,\n    hasPagesDir: !!pagesDir,\n  })\n\n  console.log('Generating route types...')\n\n  const routeTypesFilePath = join(distDir, 'types', 'routes.d.ts')\n  const validatorFilePath = join(distDir, 'types', 'validator.ts')\n  await mkdir(join(distDir, 'types'), { recursive: true })\n\n  let pageRoutes: RouteInfo[] = []\n  let appRoutes: RouteInfo[] = []\n  let appRouteHandlers: RouteInfo[] = []\n  let layoutRoutes: RouteInfo[] = []\n  let slots: SlotInfo[] = []\n\n  let pageApiRoutes: RouteInfo[] = []\n\n  let mappedPages: { [page: string]: string } = {}\n  let mappedAppPages: { [page: string]: string } = {}\n  let mappedAppLayouts: { [page: string]: string } = {}\n\n  // Helper function to reduce createPagesMapping duplication\n  const createMapping = (pagePaths: string[], pagesType: any) =>\n    createPagesMapping({\n      pagePaths,\n      isDev: false,\n      pagesType,\n      pageExtensions: nextConfig.pageExtensions,\n      pagesDir,\n      appDir,\n    })\n\n  const validFileMatcher = createValidFileMatcher(\n    nextConfig.pageExtensions,\n    appDir\n  )\n\n  const isSrcDir = path\n    .relative(baseDir, pagesDir || appDir || '')\n    .startsWith('src')\n\n  // Build pages routes\n  if (pagesDir) {\n    const pagePaths = await collectPagesFiles(pagesDir, validFileMatcher)\n\n    mappedPages = await createMapping(pagePaths, PAGE_TYPES.PAGES)\n\n    // Process pages routes\n    const processedPages = processPageRoutes(mappedPages, baseDir, isSrcDir)\n    pageRoutes = processedPages.pageRoutes\n    pageApiRoutes = processedPages.pageApiRoutes\n  }\n\n  // Build app routes\n  if (appDir) {\n    // Collect app pages, layouts, and default files in a single directory traversal\n    const { appPaths, layoutPaths, defaultPaths } = await collectAppFiles(\n      appDir,\n      validFileMatcher\n    )\n\n    mappedAppPages = await createMapping(appPaths, PAGE_TYPES.APP)\n    mappedAppLayouts = await createMapping(layoutPaths, PAGE_TYPES.APP)\n    const mappedDefaultFiles = await createMapping(defaultPaths, PAGE_TYPES.APP)\n\n    // Process app routes and extract slots from both pages and default files\n    const slotsFromPages = extractSlotsFromAppRoutes(mappedAppPages)\n    const slotsFromDefaults = extractSlotsFromDefaultFiles(mappedDefaultFiles)\n\n    // Combine slots and deduplicate using Set\n    slots = combineSlots(slotsFromPages, slotsFromDefaults)\n\n    const result = processAppRoutes(\n      mappedAppPages,\n      validFileMatcher,\n      baseDir,\n      isSrcDir\n    )\n    appRoutes = result.appRoutes\n    appRouteHandlers = result.appRouteHandlers\n\n    // Process layout routes\n    layoutRoutes = processLayoutRoutes(mappedAppLayouts, baseDir, isSrcDir)\n  }\n\n  const routeTypesManifest = await createRouteTypesManifest({\n    dir: baseDir,\n    pageRoutes,\n    appRoutes,\n    appRouteHandlers,\n    pageApiRoutes,\n    layoutRoutes,\n    slots,\n    redirects: nextConfig.redirects,\n    rewrites: nextConfig.rewrites,\n    validatorFilePath,\n  })\n\n  await writeRouteTypesManifest(\n    routeTypesManifest,\n    routeTypesFilePath,\n    nextConfig\n  )\n\n  await writeValidatorFile(routeTypesManifest, validatorFilePath)\n\n  console.log('✓ Route types generated successfully')\n}\n\nexport { nextTypegen }\n"], "names": ["nextTypegen", "_options", "directory", "baseDir", "getProjectDir", "existsSync", "printAndExit", "nextConfig", "loadConfig", "PHASE_PRODUCTION_BUILD", "distDir", "join", "pagesDir", "appDir", "findPagesDir", "verifyTypeScriptSetup", "dir", "intentDirs", "filter", "Boolean", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "console", "log", "routeTypesFilePath", "validatorFilePath", "mkdir", "recursive", "pageRoutes", "appRoutes", "appRouteHandlers", "layoutRoutes", "slots", "pageApiRoutes", "mappedPages", "mappedAppPages", "mappedAppLayouts", "createMapping", "pagePaths", "pagesType", "createPagesMapping", "isDev", "pageExtensions", "validFile<PERSON><PERSON><PERSON>", "createValidFileMatcher", "isSrcDir", "path", "relative", "startsWith", "collectPagesFiles", "PAGE_TYPES", "PAGES", "processedPages", "processPageRoutes", "appPaths", "layoutPaths", "defaultPaths", "collectAppFiles", "APP", "mappedDefaultFiles", "slotsFromPages", "extractSlotsFromAppRoutes", "slotsFromDefaults", "extractSlotsFromDefaultFiles", "combineSlots", "result", "processAppRoutes", "processLayoutRoutes", "routeTypesManifest", "createRouteTypesManifest", "redirects", "rewrites", "writeRouteTypesManifest", "writeValidatorFile"], "mappings": ";;;;;+BA0KSA;;;eAAAA;;;oBAxKkB;8DACA;0BACL;+DAEC;uBACM;2BACU;+BACT;8BACD;uCACS;yBAa/B;2BACoB;iCAMpB;8BACgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvC,MAAMA,cAAc,OAClBC,UACAC;IAEA,MAAMC,UAAUC,IAAAA,4BAAa,EAACF;IAE9B,yCAAyC;IACzC,IAAI,CAACG,IAAAA,cAAU,EAACF,UAAU;QACxBG,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAEH,SAAS;IAC3E;IAEA,MAAMI,aAAa,MAAMC,IAAAA,eAAU,EAACC,iCAAsB,EAAEN;IAC5D,MAAMO,UAAUC,IAAAA,UAAI,EAACR,SAASI,WAAWG,OAAO;IAChD,MAAM,EAAEE,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAACX;IAE1C,MAAMY,IAAAA,4CAAqB,EAAC;QAC1BC,KAAKb;QACLO,SAASH,WAAWG,OAAO;QAC3BO,YAAY;YAACL;YAAUC;SAAO,CAACK,MAAM,CAACC;QACtCC,oBAAoB;QACpBC,cAAcd,WAAWe,UAAU,CAACD,YAAY;QAChDE,qBAAqBhB,WAAWiB,MAAM,CAACD,mBAAmB;QAC1DE,WAAW,CAAC,CAACZ;QACba,aAAa,CAAC,CAACd;IACjB;IAEAe,QAAQC,GAAG,CAAC;IAEZ,MAAMC,qBAAqBlB,IAAAA,UAAI,EAACD,SAAS,SAAS;IAClD,MAAMoB,oBAAoBnB,IAAAA,UAAI,EAACD,SAAS,SAAS;IACjD,MAAMqB,IAAAA,eAAK,EAACpB,IAAAA,UAAI,EAACD,SAAS,UAAU;QAAEsB,WAAW;IAAK;IAEtD,IAAIC,aAA0B,EAAE;IAChC,IAAIC,YAAyB,EAAE;IAC/B,IAAIC,mBAAgC,EAAE;IACtC,IAAIC,eAA4B,EAAE;IAClC,IAAIC,QAAoB,EAAE;IAE1B,IAAIC,gBAA6B,EAAE;IAEnC,IAAIC,cAA0C,CAAC;IAC/C,IAAIC,iBAA6C,CAAC;IAClD,IAAIC,mBAA+C,CAAC;IAEpD,2DAA2D;IAC3D,MAAMC,gBAAgB,CAACC,WAAqBC,YAC1CC,IAAAA,2BAAkB,EAAC;YACjBF;YACAG,OAAO;YACPF;YACAG,gBAAgBxC,WAAWwC,cAAc;YACzCnC;YACAC;QACF;IAEF,MAAMmC,mBAAmBC,IAAAA,oCAAsB,EAC7C1C,WAAWwC,cAAc,EACzBlC;IAGF,MAAMqC,WAAWC,aAAI,CAClBC,QAAQ,CAACjD,SAASS,YAAYC,UAAU,IACxCwC,UAAU,CAAC;IAEd,qBAAqB;IACrB,IAAIzC,UAAU;QACZ,MAAM+B,YAAY,MAAMW,IAAAA,0BAAiB,EAAC1C,UAAUoC;QAEpDT,cAAc,MAAMG,cAAcC,WAAWY,qBAAU,CAACC,KAAK;QAE7D,uBAAuB;QACvB,MAAMC,iBAAiBC,IAAAA,0BAAiB,EAACnB,aAAapC,SAAS+C;QAC/DjB,aAAawB,eAAexB,UAAU;QACtCK,gBAAgBmB,eAAenB,aAAa;IAC9C;IAEA,mBAAmB;IACnB,IAAIzB,QAAQ;QACV,gFAAgF;QAChF,MAAM,EAAE8C,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAE,GAAG,MAAMC,IAAAA,wBAAe,EACnEjD,QACAmC;QAGFR,iBAAiB,MAAME,cAAciB,UAAUJ,qBAAU,CAACQ,GAAG;QAC7DtB,mBAAmB,MAAMC,cAAckB,aAAaL,qBAAU,CAACQ,GAAG;QAClE,MAAMC,qBAAqB,MAAMtB,cAAcmB,cAAcN,qBAAU,CAACQ,GAAG;QAE3E,yEAAyE;QACzE,MAAME,iBAAiBC,IAAAA,kCAAyB,EAAC1B;QACjD,MAAM2B,oBAAoBC,IAAAA,qCAA4B,EAACJ;QAEvD,0CAA0C;QAC1C3B,QAAQgC,IAAAA,qBAAY,EAACJ,gBAAgBE;QAErC,MAAMG,SAASC,IAAAA,yBAAgB,EAC7B/B,gBACAQ,kBACA7C,SACA+C;QAEFhB,YAAYoC,OAAOpC,SAAS;QAC5BC,mBAAmBmC,OAAOnC,gBAAgB;QAE1C,wBAAwB;QACxBC,eAAeoC,IAAAA,4BAAmB,EAAC/B,kBAAkBtC,SAAS+C;IAChE;IAEA,MAAMuB,qBAAqB,MAAMC,IAAAA,yCAAwB,EAAC;QACxD1D,KAAKb;QACL8B;QACAC;QACAC;QACAG;QACAF;QACAC;QACAsC,WAAWpE,WAAWoE,SAAS;QAC/BC,UAAUrE,WAAWqE,QAAQ;QAC7B9C;IACF;IAEA,MAAM+C,IAAAA,wCAAuB,EAC3BJ,oBACA5C,oBACAtB;IAGF,MAAMuE,IAAAA,mCAAkB,EAACL,oBAAoB3C;IAE7CH,QAAQC,GAAG,CAAC;AACd", "ignoreList": [0]}