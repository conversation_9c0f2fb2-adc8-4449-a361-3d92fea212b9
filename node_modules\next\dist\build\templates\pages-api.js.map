{"version": 3, "sources": ["../../../src/build/templates/pages-api.ts"], "sourcesContent": ["import type { NextApiResponse } from '../../types'\nimport type { IncomingMessage, ServerResponse } from 'node:http'\n\nimport { sendError } from '../../server/api-utils'\nimport { RouteKind } from '../../server/route-kind'\nimport type { Span } from '../../server/lib/trace/tracer'\nimport { PagesAPIRouteModule } from '../../server/route-modules/pages-api/module.compiled'\n\nimport { hoist } from './helpers'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\nimport { getTracer, SpanKind } from '../../server/lib/trace/tracer'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport type { InstrumentationOnRequestError } from '../../server/instrumentation/types'\n\n// Re-export the handler (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export config.\nexport const config = hoist(userland, 'config')\n\n// Create and export the route module that will be consumed.\nconst routeModule = new PagesAPIRouteModule({\n  definition: {\n    kind: RouteKind.PAGES_API,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  userland,\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n})\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil?: (prom: Promise<void>) => void\n  }\n): Promise<void> {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  }\n\n  const prepareResult = await routeModule.prepare(req, res, { srcPage })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return\n  }\n\n  const { query, params, prerenderManifest, routerServerContext } =\n    prepareResult\n\n  try {\n    const method = req.method || 'GET'\n    const tracer = getTracer()\n\n    const activeSpan = tracer.getActiveScopeSpan()\n    const onRequestError =\n      routeModule.instrumentationOnRequestError.bind(routeModule)\n\n    const invokeRouteModule = async (span?: Span) =>\n      routeModule\n        .render(req, res, {\n          query: {\n            ...query,\n            ...params,\n          },\n          params,\n          allowedRevalidateHeaderKeys: process.env\n            .__NEXT_ALLOWED_REVALIDATE_HEADERS as any as string[],\n          multiZoneDraftMode: Boolean(process.env.__NEXT_MULTI_ZONE_DRAFT_MODE),\n          trustHostHeader: process.env\n            .__NEXT_TRUST_HOST_HEADER as any as boolean,\n          // TODO: get this from from runtime env so manifest\n          // doesn't need to load\n          previewProps: prerenderManifest.preview,\n          propagateError: false,\n          dev: routeModule.isDev,\n          page: 'VAR_DEFINITION_PAGE',\n\n          internalRevalidate: routerServerContext?.revalidate,\n\n          onError: (...args: Parameters<InstrumentationOnRequestError>) =>\n            onRequestError(req, ...args),\n        })\n        .finally(() => {\n          if (!span) return\n\n          span.setAttributes({\n            'http.status_code': res.statusCode,\n            'next.rsc': false,\n          })\n\n          const rootSpanAttributes = tracer.getRootSpanAttributes()\n          // We were unable to get attributes, probably OTEL is not enabled\n          if (!rootSpanAttributes) {\n            return\n          }\n\n          if (\n            rootSpanAttributes.get('next.span_type') !==\n            BaseServerSpan.handleRequest\n          ) {\n            console.warn(\n              `Unexpected root span type '${rootSpanAttributes.get(\n                'next.span_type'\n              )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n            )\n            return\n          }\n\n          const route = rootSpanAttributes.get('next.route')\n          if (route) {\n            const name = `${method} ${route}`\n\n            span.setAttributes({\n              'next.route': route,\n              'http.route': route,\n              'next.span_name': name,\n            })\n            span.updateName(name)\n          } else {\n            span.updateName(`${method} ${req.url}`)\n          }\n        })\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await invokeRouteModule(activeSpan)\n    } else {\n      await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          invokeRouteModule\n        )\n      )\n    }\n  } catch (err) {\n    // we re-throw in dev to show the error overlay\n    if (routeModule.isDev) {\n      throw err\n    }\n    // this is technically an invariant as error handling\n    // should be done inside of api-resolver onError\n    sendError(res as NextApiResponse, 500, 'Internal Server Error')\n  } finally {\n    // We don't allow any waitUntil work in pages API routes currently\n    // so if callback is present return with resolved promise since no\n    // pending work\n    ctx.waitUntil?.(Promise.resolve())\n  }\n}\n"], "names": ["config", "handler", "hoist", "userland", "routeModule", "PagesAPIRouteModule", "definition", "kind", "RouteKind", "PAGES_API", "page", "pathname", "bundlePath", "filename", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "relativeProjectDir", "__NEXT_RELATIVE_PROJECT_DIR", "req", "res", "ctx", "srcPage", "TURBOPACK", "replace", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "query", "params", "prerenderManifest", "routerServerContext", "method", "tracer", "getTracer", "activeSpan", "getActiveScopeSpan", "onRequestError", "instrumentationOnRequestError", "bind", "invokeRouteModule", "span", "render", "allowedRevalidateHeaderKeys", "__NEXT_ALLOWED_REVALIDATE_HEADERS", "multiZoneDraftMode", "Boolean", "__NEXT_MULTI_ZONE_DRAFT_MODE", "trustHostHeader", "__NEXT_TRUST_HOST_HEADER", "previewProps", "preview", "propagateError", "dev", "isDev", "internalRevalidate", "revalidate", "onError", "args", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "BaseServerSpan", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "withPropagatedContext", "headers", "trace", "spanName", "SpanKind", "SERVER", "attributes", "err", "sendError"], "mappings": ";;;;;;;;;;;;;;;;IAoBaA,MAAM;eAANA;;IAJb,wDAAwD;IACxD,OAAyC;eAAzC;;IAoBsBC,OAAO;eAAPA;;;0BAlCI;2BACA;gCAEU;yBAEd;sEAGI;wBACU;2BACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAI/B,WAAeC,IAAAA,cAAK,EAACC,eAAU;AAGxB,MAAMH,SAASE,IAAAA,cAAK,EAACC,eAAU;AAEtC,4DAA4D;AAC5D,MAAMC,cAAc,IAAIC,mCAAmB,CAAC;IAC1CC,YAAY;QACVC,MAAMC,oBAAS,CAACC,SAAS;QACzBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAV,UAAAA;IACAW,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,oBAAoBH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;AACjE;AAEO,eAAelB,QACpBmB,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;IAED,IAAIC,UAAU;IAEd,wDAAwD;IACxD,mDAAmD;IACnD,6DAA6D;IAC7D,IAAIR,QAAQC,GAAG,CAACQ,SAAS,EAAE;QACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;IAC/C;IAEA,MAAMC,gBAAgB,MAAMtB,YAAYuB,OAAO,CAACP,KAAKC,KAAK;QAAEE;IAAQ;IAEpE,IAAI,CAACG,eAAe;QAClBL,IAAIO,UAAU,GAAG;QACjBP,IAAIQ,GAAG,CAAC;QACRP,IAAIQ,SAAS,oBAAbR,IAAIQ,SAAS,MAAbR,KAAgBS,QAAQC,OAAO;QAC/B;IACF;IAEA,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAE,GAC7DV;IAEF,IAAI;QACF,MAAMW,SAASjB,IAAIiB,MAAM,IAAI;QAC7B,MAAMC,SAASC,IAAAA,iBAAS;QAExB,MAAMC,aAAaF,OAAOG,kBAAkB;QAC5C,MAAMC,iBACJtC,YAAYuC,6BAA6B,CAACC,IAAI,CAACxC;QAEjD,MAAMyC,oBAAoB,OAAOC,OAC/B1C,YACG2C,MAAM,CAAC3B,KAAKC,KAAK;gBAChBY,OAAO;oBACL,GAAGA,KAAK;oBACR,GAAGC,MAAM;gBACX;gBACAA;gBACAc,6BAA6BjC,QAAQC,GAAG,CACrCiC,iCAAiC;gBACpCC,oBAAoBC,QAAQpC,QAAQC,GAAG,CAACoC,4BAA4B;gBACpEC,iBAAiBtC,QAAQC,GAAG,CACzBsC,wBAAwB;gBAC3B,mDAAmD;gBACnD,uBAAuB;gBACvBC,cAAcpB,kBAAkBqB,OAAO;gBACvCC,gBAAgB;gBAChBC,KAAKtD,YAAYuD,KAAK;gBACtBjD,MAAM;gBAENkD,kBAAkB,EAAExB,uCAAAA,oBAAqByB,UAAU;gBAEnDC,SAAS,CAAC,GAAGC,OACXrB,eAAetB,QAAQ2C;YAC3B,GACCC,OAAO,CAAC;gBACP,IAAI,CAAClB,MAAM;gBAEXA,KAAKmB,aAAa,CAAC;oBACjB,oBAAoB5C,IAAIO,UAAU;oBAClC,YAAY;gBACd;gBAEA,MAAMsC,qBAAqB5B,OAAO6B,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACD,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBC,yBAAc,CAACC,aAAa,EAC5B;oBACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEN,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAMK,QAAQP,mBAAmBE,GAAG,CAAC;gBACrC,IAAIK,OAAO;oBACT,MAAMC,OAAO,GAAGrC,OAAO,CAAC,EAAEoC,OAAO;oBAEjC3B,KAAKmB,aAAa,CAAC;wBACjB,cAAcQ;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACA5B,KAAK6B,UAAU,CAACD;gBAClB,OAAO;oBACL5B,KAAK6B,UAAU,CAAC,GAAGtC,OAAO,CAAC,EAAEjB,IAAIwD,GAAG,EAAE;gBACxC;YACF;QAEJ,oDAAoD;QACpD,yDAAyD;QACzD,IAAIpC,YAAY;YACd,MAAMK,kBAAkBL;QAC1B,OAAO;YACL,MAAMF,OAAOuC,qBAAqB,CAACzD,IAAI0D,OAAO,EAAE,IAC9CxC,OAAOyC,KAAK,CACVV,yBAAc,CAACC,aAAa,EAC5B;oBACEU,UAAU,GAAG3C,OAAO,CAAC,EAAEjB,IAAIwD,GAAG,EAAE;oBAChCrE,MAAM0E,gBAAQ,CAACC,MAAM;oBACrBC,YAAY;wBACV,eAAe9C;wBACf,eAAejB,IAAIwD,GAAG;oBACxB;gBACF,GACA/B;QAGN;IACF,EAAE,OAAOuC,KAAK;QACZ,+CAA+C;QAC/C,IAAIhF,YAAYuD,KAAK,EAAE;YACrB,MAAMyB;QACR;QACA,qDAAqD;QACrD,gDAAgD;QAChDC,IAAAA,mBAAS,EAAChE,KAAwB,KAAK;IACzC,SAAU;QACR,kEAAkE;QAClE,kEAAkE;QAClE,eAAe;QACfC,IAAIQ,SAAS,oBAAbR,IAAIQ,SAAS,MAAbR,KAAgBS,QAAQC,OAAO;IACjC;AACF", "ignoreList": [0]}