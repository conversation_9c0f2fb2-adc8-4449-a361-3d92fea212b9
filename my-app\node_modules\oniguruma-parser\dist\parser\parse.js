"use strict";import{tokenize as q}from"../tokenizer/tokenize.js";import{cpOf as H,getOrInsert as Z,PosixClassNames as x,r as y,throwIfNullish as g}from"../utils.js";import{hasOnlyChild as Y,isAlternativeContainer as j,isQuantifiable as S}from"./node-utils.js";function J(e,r={}){const n={flags:"",normalizeUnknownPropertyNames:!1,skipBackrefValidation:!1,skipLookbehindValidation:!1,skipPropertyNameValidation:!1,unicodePropertyMap:null,...r,rules:{captureGroup:!1,singleline:!1,...r.rules}},t=q(e,{flags:n.flags,rules:{captureGroup:n.rules.captureGroup,singleline:n.rules.singleline}}),s=(p,N)=>{const u=t.tokens[o.nextIndex];switch(o.parent=p,o.nextIndex++,u.type){case"Alternator":return b();case"Assertion":return W(u);case"Backreference":return X(u,o);case"Character":return m(u.value,{useLastValid:!!N.isCheckingRangeEnd});case"CharacterClassHyphen":return ee(u,o,N);case"CharacterClassOpen":return re(u,o,N);case"CharacterSet":return ne(u,o);case"Directive":return I(u.kind,{flags:u.flags});case"GroupOpen":return te(u,o,N);case"NamedCallout":return U(u.kind,u.tag,u.arguments);case"Quantifier":return oe(u,o);case"Subroutine":return ae(u,o);default:throw new Error(`Unexpected token type "${u.type}"`)}},o={capturingGroups:[],hasNumberedRef:!1,namedGroupsByName:new Map,nextIndex:0,normalizeUnknownPropertyNames:n.normalizeUnknownPropertyNames,parent:null,skipBackrefValidation:n.skipBackrefValidation,skipLookbehindValidation:n.skipLookbehindValidation,skipPropertyNameValidation:n.skipPropertyNameValidation,subroutines:[],tokens:t.tokens,unicodePropertyMap:n.unicodePropertyMap,walk:s},i=B(T(t.flags));let d=i.body[0];for(;o.nextIndex<t.tokens.length;){const p=s(d,{});p.type==="Alternative"?(i.body.push(p),d=p):d.body.push(p)}const{capturingGroups:a,hasNumberedRef:l,namedGroupsByName:c,subroutines:f}=o;if(l&&c.size&&!n.rules.captureGroup)throw new Error("Numbered backref/subroutine not allowed when using named capture");for(const{ref:p}of f)if(typeof p=="number"){if(p>a.length)throw new Error("Subroutine uses a group number that's not defined");p&&(a[p-1].isSubroutined=!0)}else if(c.has(p)){if(c.get(p).length>1)throw new Error(y`Subroutine uses a duplicate group name "\g<${p}>"`);c.get(p)[0].isSubroutined=!0}else throw new Error(y`Subroutine uses a group name that's not defined "\g<${p}>"`);return i}function W({kind:e}){return F(g({"^":"line_start",$:"line_end","\\A":"string_start","\\b":"word_boundary","\\B":"word_boundary","\\G":"search_start","\\y":"text_segment_boundary","\\Y":"text_segment_boundary","\\z":"string_end","\\Z":"string_end_newline"}[e],`Unexpected assertion kind "${e}"`),{negate:e===y`\B`||e===y`\Y`})}function X({raw:e},r){const n=/^\\k[<']/.test(e),t=n?e.slice(3,-1):e.slice(1),s=(o,i=!1)=>{const d=r.capturingGroups.length;let a=!1;if(o>d)if(r.skipBackrefValidation)a=!0;else throw new Error(`Not enough capturing groups defined to the left "${e}"`);return r.hasNumberedRef=!0,k(i?d+1-o:o,{orphan:a})};if(n){const o=/^(?<sign>-?)0*(?<num>[1-9]\d*)$/.exec(t);if(o)return s(+o.groups.num,!!o.groups.sign);if(/[-+]/.test(t))throw new Error(`Invalid backref name "${e}"`);if(!r.namedGroupsByName.has(t))throw new Error(`Group name not defined to the left "${e}"`);return k(t)}return s(+t)}function ee(e,r,n){const{tokens:t,walk:s}=r,o=r.parent,i=o.body.at(-1),d=t[r.nextIndex];if(!n.isCheckingRangeEnd&&i&&i.type!=="CharacterClass"&&i.type!=="CharacterClassRange"&&d&&d.type!=="CharacterClassOpen"&&d.type!=="CharacterClassClose"&&d.type!=="CharacterClassIntersector"){const a=s(o,{...n,isCheckingRangeEnd:!0});if(i.type==="Character"&&a.type==="Character")return o.body.pop(),L(i,a);throw new Error("Invalid character class range")}return m(H("-"))}function re({negate:e},r,n){const{tokens:t,walk:s}=r,o=t[r.nextIndex],i=[C()];let d=z(o);for(;d.type!=="CharacterClassClose";){if(d.type==="CharacterClassIntersector")i.push(C()),r.nextIndex++;else{const l=i.at(-1);l.body.push(s(l,n))}d=z(t[r.nextIndex],o)}const a=C({negate:e});return i.length===1?a.body=i[0].body:(a.kind="intersection",a.body=i.map(l=>l.body.length===1?l.body[0]:l)),r.nextIndex++,a}function ne({kind:e,negate:r,value:n},t){const{normalizeUnknownPropertyNames:s,skipPropertyNameValidation:o,unicodePropertyMap:i}=t;if(e==="property"){const d=w(n);if(x.has(d)&&!i?.has(d))e="posix",n=d;else return Q(n,{negate:r,normalizeUnknownPropertyNames:s,skipPropertyNameValidation:o,unicodePropertyMap:i})}return e==="posix"?R(n,{negate:r}):E(e,{negate:r})}function te(e,r,n){const{tokens:t,capturingGroups:s,namedGroupsByName:o,skipLookbehindValidation:i,walk:d}=r,a=ie(e),l=a.type==="AbsenceFunction",c=$(a),f=c&&a.negate;if(a.type==="CapturingGroup"&&(s.push(a),a.name&&Z(o,a.name,[]).push(a)),l&&n.isInAbsenceFunction)throw new Error("Nested absence function not supported by Oniguruma");let p=D(t[r.nextIndex]);for(;p.type!=="GroupClose";){if(p.type==="Alternator")a.body.push(b()),r.nextIndex++;else{const N=a.body.at(-1),u=d(N,{...n,isInAbsenceFunction:n.isInAbsenceFunction||l,isInLookbehind:n.isInLookbehind||c,isInNegLookbehind:n.isInNegLookbehind||f});if(N.body.push(u),(c||n.isInLookbehind)&&!i){const v="Lookbehind includes a pattern not allowed by Oniguruma";if(f||n.isInNegLookbehind){if(M(u)||u.type==="CapturingGroup")throw new Error(v)}else if(M(u)||$(u)&&u.negate)throw new Error(v)}}p=D(t[r.nextIndex])}return r.nextIndex++,a}function oe({kind:e,min:r,max:n},t){const s=t.parent,o=s.body.at(-1);if(!o||!S(o))throw new Error("Quantifier requires a repeatable token");const i=_(e,r,n,o);return s.body.pop(),i}function ae({raw:e},r){const{capturingGroups:n,subroutines:t}=r;let s=e.slice(3,-1);const o=/^(?<sign>[-+]?)0*(?<num>[1-9]\d*)$/.exec(s);if(o){const d=+o.groups.num,a=n.length;if(r.hasNumberedRef=!0,s={"":d,"+":a+d,"-":a+1-d}[o.groups.sign],s<1)throw new Error("Invalid subroutine number")}else s==="0"&&(s=0);const i=O(s);return t.push(i),i}function G(e,r){if(e!=="repeater")throw new Error(`Unexpected absence function kind "${e}"`);return{type:"AbsenceFunction",kind:e,body:h(r?.body)}}function b(e){return{type:"Alternative",body:V(e?.body)}}function F(e,r){const n={type:"Assertion",kind:e};return(e==="word_boundary"||e==="text_segment_boundary")&&(n.negate=!!r?.negate),n}function k(e,r){const n=!!r?.orphan;return{type:"Backreference",ref:e,...n&&{orphan:n}}}function P(e,r){const n={name:void 0,isSubroutined:!1,...r};if(n.name!==void 0&&!se(n.name))throw new Error(`Group name "${n.name}" invalid in Oniguruma`);return{type:"CapturingGroup",number:e,...n.name&&{name:n.name},...n.isSubroutined&&{isSubroutined:n.isSubroutined},body:h(r?.body)}}function m(e,r){const n={useLastValid:!1,...r};if(e>1114111){const t=e.toString(16);if(n.useLastValid)e=1114111;else throw e>1310719?new Error(`Invalid code point out of range "\\x{${t}}"`):new Error(`Invalid code point out of range in JS "\\x{${t}}"`)}return{type:"Character",value:e}}function C(e){const r={kind:"union",negate:!1,...e};return{type:"CharacterClass",kind:r.kind,negate:r.negate,body:V(e?.body)}}function L(e,r){if(r.value<e.value)throw new Error("Character class range out of order");return{type:"CharacterClassRange",min:e,max:r}}function E(e,r){const n=!!r?.negate,t={type:"CharacterSet",kind:e};return(e==="digit"||e==="hex"||e==="newline"||e==="space"||e==="word")&&(t.negate=n),(e==="text_segment"||e==="newline"&&!n)&&(t.variableLength=!0),t}function I(e,r={}){if(e==="keep")return{type:"Directive",kind:e};if(e==="flags")return{type:"Directive",kind:e,flags:g(r.flags)};throw new Error(`Unexpected directive kind "${e}"`)}function T(e){return{type:"Flags",...e}}function A(e){const r=e?.atomic,n=e?.flags;if(r&&n)throw new Error("Atomic group cannot have flags");return{type:"Group",...r&&{atomic:r},...n&&{flags:n},body:h(e?.body)}}function K(e){const r={behind:!1,negate:!1,...e};return{type:"LookaroundAssertion",kind:r.behind?"lookbehind":"lookahead",negate:r.negate,body:h(e?.body)}}function U(e,r,n){return{type:"NamedCallout",kind:e,tag:r,arguments:n}}function R(e,r){const n=!!r?.negate;if(!x.has(e))throw new Error(`Invalid POSIX class "${e}"`);return{type:"CharacterSet",kind:"posix",value:e,negate:n}}function _(e,r,n,t){if(r>n)throw new Error("Invalid reversed quantifier range");return{type:"Quantifier",kind:e,min:r,max:n,body:t}}function B(e,r){return{type:"Regex",body:h(r?.body),flags:e}}function O(e){return{type:"Subroutine",ref:e}}function Q(e,r){const n={negate:!1,normalizeUnknownPropertyNames:!1,skipPropertyNameValidation:!1,unicodePropertyMap:null,...r};let t=n.unicodePropertyMap?.get(w(e));if(!t){if(n.normalizeUnknownPropertyNames)t=de(e);else if(n.unicodePropertyMap&&!n.skipPropertyNameValidation)throw new Error(y`Invalid Unicode property "\p{${e}}"`)}return{type:"CharacterSet",kind:"property",value:t??e,negate:n.negate}}function ie({flags:e,kind:r,name:n,negate:t,number:s}){switch(r){case"absence_repeater":return G("repeater");case"atomic":return A({atomic:!0});case"capturing":return P(s,{name:n});case"group":return A({flags:e});case"lookahead":case"lookbehind":return K({behind:r==="lookbehind",negate:t});default:throw new Error(`Unexpected group kind "${r}"`)}}function h(e){if(e===void 0)e=[b()];else if(!Array.isArray(e)||!e.length||!e.every(r=>r.type==="Alternative"))throw new Error("Invalid body; expected array of one or more Alternative nodes");return e}function V(e){if(e===void 0)e=[];else if(!Array.isArray(e)||!e.every(r=>!!r.type))throw new Error("Invalid body; expected array of nodes");return e}function M(e){return e.type==="LookaroundAssertion"&&e.kind==="lookahead"}function $(e){return e.type==="LookaroundAssertion"&&e.kind==="lookbehind"}function se(e){return/^[\p{Alpha}\p{Pc}][^)]*$/u.test(e)}function de(e){return e.trim().replace(/[- _]+/g,"_").replace(/[A-Z][a-z]+(?=[A-Z])/g,"$&_").replace(/[A-Za-z]+/g,r=>r[0].toUpperCase()+r.slice(1).toLowerCase())}function w(e){return e.replace(/[- _]+/g,"").toLowerCase()}function z(e,r){return g(e,`${r?.type==="Character"&&r.value===93?"Empty":"Unclosed"} character class`)}function D(e){return g(e,"Unclosed group")}export{G as createAbsenceFunction,b as createAlternative,F as createAssertion,k as createBackreference,P as createCapturingGroup,m as createCharacter,C as createCharacterClass,L as createCharacterClassRange,E as createCharacterSet,I as createDirective,T as createFlags,A as createGroup,K as createLookaroundAssertion,U as createNamedCallout,R as createPosixClass,_ as createQuantifier,B as createRegex,O as createSubroutine,Q as createUnicodeProperty,Y as hasOnlyChild,j as isAlternativeContainer,S as isQuantifiable,J as parse,w as slug};
//# sourceMappingURL=parse.js.map
