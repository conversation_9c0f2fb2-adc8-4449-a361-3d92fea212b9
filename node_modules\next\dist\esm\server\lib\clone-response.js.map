{"version": 3, "sources": ["../../../src/server/lib/clone-response.ts"], "sourcesContent": ["const noop = () => {}\n\nlet registry: FinalizationRegistry<WeakRef<ReadableStream>> | undefined\n\nif (globalThis.FinalizationRegistry) {\n  registry = new FinalizationRegistry((weakRef: WeakRef<ReadableStream>) => {\n    const stream = weakRef.deref()\n    if (stream && !stream.locked) {\n      stream.cancel('Response object has been garbage collected').then(noop)\n    }\n  })\n}\n\n/**\n * Clones a response by teeing the body so we can return two independent\n * ReadableStreams from it. This avoids the bug in the undici library around\n * response cloning.\n *\n * After cloning, the original response's body will be consumed and closed.\n *\n * @see https://github.com/vercel/next.js/pull/73274\n *\n * @param original - The original response to clone.\n * @returns A tuple containing two independent clones of the original response.\n */\nexport function cloneResponse(original: Response): [Response, Response] {\n  // If the response has no body, then we can just return the original response\n  // twice because it's immutable.\n  if (!original.body) {\n    return [original, original]\n  }\n\n  const [body1, body2] = original.body.tee()\n\n  const cloned1 = new Response(body1, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned1, 'url', {\n    value: original.url,\n    // How the original response.url behaves\n    configurable: true,\n    enumerable: true,\n    writable: false,\n  })\n\n  // The Fetch Standard allows users to skip consuming the response body by\n  // relying on garbage collection to release connection resources.\n  // https://github.com/nodejs/undici?tab=readme-ov-file#garbage-collection\n  //\n  // To cancel the stream you then need to cancel both resulting branches.\n  // Teeing a stream will generally lock it for the duration, preventing other\n  // readers from locking it.\n  // https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/tee\n\n  // cloned2 is stored in a react cache and cloned for subsequent requests.\n  // It is the original request, and is is garbage collected by a\n  // FinalizationRegistry in Undici, but since we're tee-ing the stream\n  // ourselves, we need to cancel clone1's stream (the response returned from\n  // our dedupe fetch) when clone1 is reclaimed, otherwise we leak memory.\n  if (registry && cloned1.body) {\n    registry.register(cloned1, new WeakRef(cloned1.body))\n  }\n\n  const cloned2 = new Response(body2, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned2, 'url', {\n    value: original.url,\n    // How the original response.url behaves\n    configurable: true,\n    enumerable: true,\n    writable: false,\n  })\n\n  return [cloned1, cloned2]\n}\n"], "names": ["noop", "registry", "globalThis", "FinalizationRegistry", "weakRef", "stream", "deref", "locked", "cancel", "then", "cloneResponse", "original", "body", "body1", "body2", "tee", "cloned1", "Response", "status", "statusText", "headers", "Object", "defineProperty", "value", "url", "configurable", "enumerable", "writable", "register", "WeakRef", "cloned2"], "mappings": "AAAA,MAAMA,OAAO,KAAO;AAEpB,IAAIC;AAEJ,IAAIC,WAAWC,oBAAoB,EAAE;IACnCF,WAAW,IAAIE,qBAAqB,CAACC;QACnC,MAAMC,SAASD,QAAQE,KAAK;QAC5B,IAAID,UAAU,CAACA,OAAOE,MAAM,EAAE;YAC5BF,OAAOG,MAAM,CAAC,8CAA8CC,IAAI,CAACT;QACnE;IACF;AACF;AAEA;;;;;;;;;;;CAWC,GACD,OAAO,SAASU,cAAcC,QAAkB;IAC9C,6EAA6E;IAC7E,gCAAgC;IAChC,IAAI,CAACA,SAASC,IAAI,EAAE;QAClB,OAAO;YAACD;YAAUA;SAAS;IAC7B;IAEA,MAAM,CAACE,OAAOC,MAAM,GAAGH,SAASC,IAAI,CAACG,GAAG;IAExC,MAAMC,UAAU,IAAIC,SAASJ,OAAO;QAClCK,QAAQP,SAASO,MAAM;QACvBC,YAAYR,SAASQ,UAAU;QAC/BC,SAAST,SAASS,OAAO;IAC3B;IAEAC,OAAOC,cAAc,CAACN,SAAS,OAAO;QACpCO,OAAOZ,SAASa,GAAG;QACnB,wCAAwC;QACxCC,cAAc;QACdC,YAAY;QACZC,UAAU;IACZ;IAEA,yEAAyE;IACzE,iEAAiE;IACjE,yEAAyE;IACzE,EAAE;IACF,wEAAwE;IACxE,4EAA4E;IAC5E,2BAA2B;IAC3B,sEAAsE;IAEtE,yEAAyE;IACzE,+DAA+D;IAC/D,qEAAqE;IACrE,2EAA2E;IAC3E,wEAAwE;IACxE,IAAI1B,YAAYe,QAAQJ,IAAI,EAAE;QAC5BX,SAAS2B,QAAQ,CAACZ,SAAS,IAAIa,QAAQb,QAAQJ,IAAI;IACrD;IAEA,MAAMkB,UAAU,IAAIb,SAASH,OAAO;QAClCI,QAAQP,SAASO,MAAM;QACvBC,YAAYR,SAASQ,UAAU;QAC/BC,SAAST,SAASS,OAAO;IAC3B;IAEAC,OAAOC,cAAc,CAACQ,SAAS,OAAO;QACpCP,OAAOZ,SAASa,GAAG;QACnB,wCAAwC;QACxCC,cAAc;QACdC,YAAY;QACZC,UAAU;IACZ;IAEA,OAAO;QAACX;QAASc;KAAQ;AAC3B", "ignoreList": [0]}