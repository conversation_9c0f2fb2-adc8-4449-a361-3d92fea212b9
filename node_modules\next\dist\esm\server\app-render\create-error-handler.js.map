{"version": 3, "sources": ["../../../src/server/app-render/create-error-handler.tsx"], "sourcesContent": ["import type { ErrorInfo } from 'react'\n\nimport stringHash from 'next/dist/compiled/string-hash'\nimport { formatServerError } from '../../lib/format-server-error'\nimport { SpanStatusCode, getTracer } from '../lib/trace/tracer'\nimport { isAbortError } from '../pipe-readable'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\nimport { isPrerenderInterruptedError } from './dynamic-rendering'\nimport { getProperError } from '../../lib/is-error'\nimport { createDigestWithErrorCode } from '../../lib/error-telemetry-utils'\nimport { isReactLargeShellError } from './react-large-shell-error'\n\ndeclare global {\n  var __next_log_error__: undefined | ((err: unknown) => void)\n}\n\ntype RSCErrorHandler = (err: unknown) => string | undefined\ntype SSRErrorHandler = (\n  err: unknown,\n  errorInfo?: ErrorInfo\n) => string | undefined\n\nexport type DigestedError = Error & { digest: string }\n\n/**\n * Returns a digest for well-known Next.js errors, otherwise `undefined`. If a\n * digest is returned this also means that the error does not need to be\n * reported.\n */\nexport function getDigestForWellKnownError(error: unknown): string | undefined {\n  // If we're bailing out to CSR, we don't need to log the error.\n  if (isBailoutToCSRError(error)) return error.digest\n\n  // If this is a navigation error, we don't need to log the error.\n  if (isNextRouterError(error)) return error.digest\n\n  // If this error occurs, we know that we should be stopping the static\n  // render. This is only thrown in static generation when PPR is not enabled,\n  // which causes the whole page to be marked as dynamic. We don't need to\n  // tell the user about this error, as it's not actionable.\n  if (isDynamicServerError(error)) return error.digest\n\n  // If this is a prerender interrupted error, we don't need to log the error.\n  if (isPrerenderInterruptedError(error)) return error.digest\n\n  return undefined\n}\n\nexport function createFlightReactServerErrorHandler(\n  shouldFormatError: boolean,\n  onReactServerRenderError: (err: DigestedError) => void\n): RSCErrorHandler {\n  return (thrownValue: unknown) => {\n    if (typeof thrownValue === 'string') {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      return stringHash(thrownValue).toString()\n    }\n\n    // If the response was closed, we don't need to log the error.\n    if (isAbortError(thrownValue)) return\n\n    const digest = getDigestForWellKnownError(thrownValue)\n\n    if (digest) {\n      return digest\n    }\n\n    if (isReactLargeShellError(thrownValue)) {\n      // TODO: Aggregate\n      console.error(thrownValue)\n      return undefined\n    }\n\n    const err = getProperError(thrownValue) as DigestedError\n\n    // If the error already has a digest, respect the original digest,\n    // so it won't get re-generated into another new error.\n    if (!err.digest) {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      err.digest = stringHash(err.message + err.stack || '').toString()\n    }\n\n    // Format server errors in development to add more helpful error messages\n    if (shouldFormatError) {\n      formatServerError(err)\n    }\n\n    // Record exception in an active span, if available.\n    const span = getTracer().getActiveScopeSpan()\n    if (span) {\n      span.recordException(err)\n      span.setAttribute('error.type', err.name)\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      })\n    }\n\n    onReactServerRenderError(err)\n\n    return createDigestWithErrorCode(thrownValue, err.digest)\n  }\n}\n\nexport function createHTMLReactServerErrorHandler(\n  shouldFormatError: boolean,\n  isNextExport: boolean,\n  reactServerErrors: Map<string, DigestedError>,\n  silenceLogger: boolean,\n  onReactServerRenderError: undefined | ((err: DigestedError) => void)\n): RSCErrorHandler {\n  return (thrownValue: unknown) => {\n    if (typeof thrownValue === 'string') {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      return stringHash(thrownValue).toString()\n    }\n\n    // If the response was closed, we don't need to log the error.\n    if (isAbortError(thrownValue)) return\n\n    const digest = getDigestForWellKnownError(thrownValue)\n\n    if (digest) {\n      return digest\n    }\n\n    if (isReactLargeShellError(thrownValue)) {\n      // TODO: Aggregate\n      console.error(thrownValue)\n      return undefined\n    }\n\n    const err = getProperError(thrownValue) as DigestedError\n\n    // If the error already has a digest, respect the original digest,\n    // so it won't get re-generated into another new error.\n    if (!err.digest) {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      err.digest = stringHash(err.message + (err.stack || '')).toString()\n    }\n\n    // @TODO by putting this here and not at the top it is possible that\n    // we don't error the build in places we actually expect to\n    if (!reactServerErrors.has(err.digest)) {\n      reactServerErrors.set(err.digest, err)\n    }\n\n    // Format server errors in development to add more helpful error messages\n    if (shouldFormatError) {\n      formatServerError(err)\n    }\n\n    // Don't log the suppressed error during export\n    if (\n      !(\n        isNextExport &&\n        err?.message?.includes(\n          'The specific message is omitted in production builds to avoid leaking sensitive details.'\n        )\n      )\n    ) {\n      // Record exception in an active span, if available.\n      const span = getTracer().getActiveScopeSpan()\n      if (span) {\n        span.recordException(err)\n        span.setAttribute('error.type', err.name)\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: err.message,\n        })\n      }\n\n      if (!silenceLogger) {\n        onReactServerRenderError?.(err)\n      }\n    }\n\n    return createDigestWithErrorCode(thrownValue, err.digest)\n  }\n}\n\nexport function createHTMLErrorHandler(\n  shouldFormatError: boolean,\n  isNextExport: boolean,\n  reactServerErrors: Map<string, DigestedError>,\n  allCapturedErrors: Array<unknown>,\n  silenceLogger: boolean,\n  onHTMLRenderSSRError: (err: DigestedError, errorInfo?: ErrorInfo) => void\n): SSRErrorHandler {\n  return (thrownValue: unknown, errorInfo?: ErrorInfo) => {\n    if (isReactLargeShellError(thrownValue)) {\n      // TODO: Aggregate\n      console.error(thrownValue)\n      return undefined\n    }\n\n    let isSSRError = true\n\n    allCapturedErrors.push(thrownValue)\n\n    // If the response was closed, we don't need to log the error.\n    if (isAbortError(thrownValue)) return\n\n    const digest = getDigestForWellKnownError(thrownValue)\n\n    if (digest) {\n      return digest\n    }\n\n    const err = getProperError(thrownValue) as DigestedError\n    // If the error already has a digest, respect the original digest,\n    // so it won't get re-generated into another new error.\n    if (err.digest) {\n      if (reactServerErrors.has(err.digest)) {\n        // This error is likely an obfuscated error from react-server.\n        // We recover the original error here.\n        thrownValue = reactServerErrors.get(err.digest)\n        isSSRError = false\n      } else {\n        // The error is not from react-server but has a digest\n        // from other means so we don't need to produce a new one\n      }\n    } else {\n      err.digest = stringHash(\n        err.message + (errorInfo?.componentStack || err.stack || '')\n      ).toString()\n    }\n\n    // Format server errors in development to add more helpful error messages\n    if (shouldFormatError) {\n      formatServerError(err)\n    }\n\n    // Don't log the suppressed error during export\n    if (\n      !(\n        isNextExport &&\n        err?.message?.includes(\n          'The specific message is omitted in production builds to avoid leaking sensitive details.'\n        )\n      )\n    ) {\n      // Record exception in an active span, if available.\n      const span = getTracer().getActiveScopeSpan()\n      if (span) {\n        span.recordException(err)\n        span.setAttribute('error.type', err.name)\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: err.message,\n        })\n      }\n\n      if (\n        !silenceLogger &&\n        // HTML errors contain RSC errors as well, filter them out before reporting\n        isSSRError\n      ) {\n        onHTMLRenderSSRError(err, errorInfo)\n      }\n    }\n\n    return createDigestWithErrorCode(thrownValue, err.digest)\n  }\n}\n\nexport function isUserLandError(err: any): boolean {\n  return (\n    !isAbortError(err) && !isBailoutToCSRError(err) && !isNextRouterError(err)\n  )\n}\n"], "names": ["stringHash", "formatServerError", "SpanStatusCode", "getTracer", "isAbortError", "isBailoutToCSRError", "isDynamicServerError", "isNextRouterError", "isPrerenderInterruptedError", "getProperError", "createDigestWithErrorCode", "isReactLargeShellError", "getDigestForWellKnownError", "error", "digest", "undefined", "createFlightReactServerErrorHandler", "shouldFormatError", "onReactServerRenderError", "thrownValue", "toString", "console", "err", "message", "stack", "span", "getActiveScopeSpan", "recordException", "setAttribute", "name", "setStatus", "code", "ERROR", "createHTMLReactServerErrorHandler", "isNextExport", "reactServerErrors", "silenceLogger", "has", "set", "includes", "createHTMLErrorHandler", "allCapturedErrors", "onHTMLRenderSSRError", "errorInfo", "isSSRError", "push", "get", "componentStack", "isUserLandError"], "mappings": "AAEA,OAAOA,gBAAgB,iCAAgC;AACvD,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,SAASC,cAAc,EAAEC,SAAS,QAAQ,sBAAqB;AAC/D,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,oBAAoB,QAAQ,+CAA8C;AACnF,SAASC,iBAAiB,QAAQ,+CAA8C;AAChF,SAASC,2BAA2B,QAAQ,sBAAqB;AACjE,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,sBAAsB,QAAQ,4BAA2B;AAclE;;;;CAIC,GACD,OAAO,SAASC,2BAA2BC,KAAc;IACvD,+DAA+D;IAC/D,IAAIR,oBAAoBQ,QAAQ,OAAOA,MAAMC,MAAM;IAEnD,iEAAiE;IACjE,IAAIP,kBAAkBM,QAAQ,OAAOA,MAAMC,MAAM;IAEjD,sEAAsE;IACtE,4EAA4E;IAC5E,wEAAwE;IACxE,0DAA0D;IAC1D,IAAIR,qBAAqBO,QAAQ,OAAOA,MAAMC,MAAM;IAEpD,4EAA4E;IAC5E,IAAIN,4BAA4BK,QAAQ,OAAOA,MAAMC,MAAM;IAE3D,OAAOC;AACT;AAEA,OAAO,SAASC,oCACdC,iBAA0B,EAC1BC,wBAAsD;IAEtD,OAAO,CAACC;QACN,IAAI,OAAOA,gBAAgB,UAAU;YACnC,+EAA+E;YAC/E,OAAOnB,WAAWmB,aAAaC,QAAQ;QACzC;QAEA,8DAA8D;QAC9D,IAAIhB,aAAae,cAAc;QAE/B,MAAML,SAASF,2BAA2BO;QAE1C,IAAIL,QAAQ;YACV,OAAOA;QACT;QAEA,IAAIH,uBAAuBQ,cAAc;YACvC,kBAAkB;YAClBE,QAAQR,KAAK,CAACM;YACd,OAAOJ;QACT;QAEA,MAAMO,MAAMb,eAAeU;QAE3B,kEAAkE;QAClE,uDAAuD;QACvD,IAAI,CAACG,IAAIR,MAAM,EAAE;YACf,+EAA+E;YAC/EQ,IAAIR,MAAM,GAAGd,WAAWsB,IAAIC,OAAO,GAAGD,IAAIE,KAAK,IAAI,IAAIJ,QAAQ;QACjE;QAEA,yEAAyE;QACzE,IAAIH,mBAAmB;YACrBhB,kBAAkBqB;QACpB;QAEA,oDAAoD;QACpD,MAAMG,OAAOtB,YAAYuB,kBAAkB;QAC3C,IAAID,MAAM;YACRA,KAAKE,eAAe,CAACL;YACrBG,KAAKG,YAAY,CAAC,cAAcN,IAAIO,IAAI;YACxCJ,KAAKK,SAAS,CAAC;gBACbC,MAAM7B,eAAe8B,KAAK;gBAC1BT,SAASD,IAAIC,OAAO;YACtB;QACF;QAEAL,yBAAyBI;QAEzB,OAAOZ,0BAA0BS,aAAaG,IAAIR,MAAM;IAC1D;AACF;AAEA,OAAO,SAASmB,kCACdhB,iBAA0B,EAC1BiB,YAAqB,EACrBC,iBAA6C,EAC7CC,aAAsB,EACtBlB,wBAAoE;IAEpE,OAAO,CAACC;YA6CFG;QA5CJ,IAAI,OAAOH,gBAAgB,UAAU;YACnC,+EAA+E;YAC/E,OAAOnB,WAAWmB,aAAaC,QAAQ;QACzC;QAEA,8DAA8D;QAC9D,IAAIhB,aAAae,cAAc;QAE/B,MAAML,SAASF,2BAA2BO;QAE1C,IAAIL,QAAQ;YACV,OAAOA;QACT;QAEA,IAAIH,uBAAuBQ,cAAc;YACvC,kBAAkB;YAClBE,QAAQR,KAAK,CAACM;YACd,OAAOJ;QACT;QAEA,MAAMO,MAAMb,eAAeU;QAE3B,kEAAkE;QAClE,uDAAuD;QACvD,IAAI,CAACG,IAAIR,MAAM,EAAE;YACf,+EAA+E;YAC/EQ,IAAIR,MAAM,GAAGd,WAAWsB,IAAIC,OAAO,GAAID,CAAAA,IAAIE,KAAK,IAAI,EAAC,GAAIJ,QAAQ;QACnE;QAEA,oEAAoE;QACpE,2DAA2D;QAC3D,IAAI,CAACe,kBAAkBE,GAAG,CAACf,IAAIR,MAAM,GAAG;YACtCqB,kBAAkBG,GAAG,CAAChB,IAAIR,MAAM,EAAEQ;QACpC;QAEA,yEAAyE;QACzE,IAAIL,mBAAmB;YACrBhB,kBAAkBqB;QACpB;QAEA,+CAA+C;QAC/C,IACE,CACEY,CAAAA,iBACAZ,wBAAAA,eAAAA,IAAKC,OAAO,qBAAZD,aAAciB,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMd,OAAOtB,YAAYuB,kBAAkB;YAC3C,IAAID,MAAM;gBACRA,KAAKE,eAAe,CAACL;gBACrBG,KAAKG,YAAY,CAAC,cAAcN,IAAIO,IAAI;gBACxCJ,KAAKK,SAAS,CAAC;oBACbC,MAAM7B,eAAe8B,KAAK;oBAC1BT,SAASD,IAAIC,OAAO;gBACtB;YACF;YAEA,IAAI,CAACa,eAAe;gBAClBlB,4CAAAA,yBAA2BI;YAC7B;QACF;QAEA,OAAOZ,0BAA0BS,aAAaG,IAAIR,MAAM;IAC1D;AACF;AAEA,OAAO,SAAS0B,uBACdvB,iBAA0B,EAC1BiB,YAAqB,EACrBC,iBAA6C,EAC7CM,iBAAiC,EACjCL,aAAsB,EACtBM,oBAAyE;IAEzE,OAAO,CAACvB,aAAsBwB;YAgDxBrB;QA/CJ,IAAIX,uBAAuBQ,cAAc;YACvC,kBAAkB;YAClBE,QAAQR,KAAK,CAACM;YACd,OAAOJ;QACT;QAEA,IAAI6B,aAAa;QAEjBH,kBAAkBI,IAAI,CAAC1B;QAEvB,8DAA8D;QAC9D,IAAIf,aAAae,cAAc;QAE/B,MAAML,SAASF,2BAA2BO;QAE1C,IAAIL,QAAQ;YACV,OAAOA;QACT;QAEA,MAAMQ,MAAMb,eAAeU;QAC3B,kEAAkE;QAClE,uDAAuD;QACvD,IAAIG,IAAIR,MAAM,EAAE;YACd,IAAIqB,kBAAkBE,GAAG,CAACf,IAAIR,MAAM,GAAG;gBACrC,8DAA8D;gBAC9D,sCAAsC;gBACtCK,cAAcgB,kBAAkBW,GAAG,CAACxB,IAAIR,MAAM;gBAC9C8B,aAAa;YACf,OAAO;YACL,sDAAsD;YACtD,yDAAyD;YAC3D;QACF,OAAO;YACLtB,IAAIR,MAAM,GAAGd,WACXsB,IAAIC,OAAO,GAAIoB,CAAAA,CAAAA,6BAAAA,UAAWI,cAAc,KAAIzB,IAAIE,KAAK,IAAI,EAAC,GAC1DJ,QAAQ;QACZ;QAEA,yEAAyE;QACzE,IAAIH,mBAAmB;YACrBhB,kBAAkBqB;QACpB;QAEA,+CAA+C;QAC/C,IACE,CACEY,CAAAA,iBACAZ,wBAAAA,eAAAA,IAAKC,OAAO,qBAAZD,aAAciB,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMd,OAAOtB,YAAYuB,kBAAkB;YAC3C,IAAID,MAAM;gBACRA,KAAKE,eAAe,CAACL;gBACrBG,KAAKG,YAAY,CAAC,cAAcN,IAAIO,IAAI;gBACxCJ,KAAKK,SAAS,CAAC;oBACbC,MAAM7B,eAAe8B,KAAK;oBAC1BT,SAASD,IAAIC,OAAO;gBACtB;YACF;YAEA,IACE,CAACa,iBACD,2EAA2E;YAC3EQ,YACA;gBACAF,qBAAqBpB,KAAKqB;YAC5B;QACF;QAEA,OAAOjC,0BAA0BS,aAAaG,IAAIR,MAAM;IAC1D;AACF;AAEA,OAAO,SAASkC,gBAAgB1B,GAAQ;IACtC,OACE,CAAClB,aAAakB,QAAQ,CAACjB,oBAAoBiB,QAAQ,CAACf,kBAAkBe;AAE1E", "ignoreList": [0]}