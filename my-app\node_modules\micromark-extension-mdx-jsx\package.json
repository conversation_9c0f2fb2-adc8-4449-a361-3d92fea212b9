{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/micromark/micromark-extension-mdx-jsx/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/estree": "^1.0.0", "devlop": "^1.0.0", "estree-util-is-identifier-name": "^3.0.0", "micromark-factory-mdx-expression": "^2.0.0", "micromark-factory-space": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-events-to-acorn": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0", "vfile-message": "^4.0.0"}, "description": "micromark extension to support MDX or MDX.js JSX", "devDependencies": {"@types/estree": "^1.0.0", "@types/estree-jsx": "^1.0.0", "@types/mdast": "^4.0.0", "@types/node": "^22.0.0", "acorn": "^8.0.0", "acorn-jsx": "^5.0.0", "c8": "^10.0.0", "estree-util-visit": "^2.0.0", "mdast-zone": "^6.0.0", "micromark": "^4.0.0", "micromark-build": "^2.0.0", "micromark-extension-mdx-expression": "^3.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^11.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.60.0"}, "exports": {"development": "./dev/index.js", "default": "./index.js"}, "files": ["dev/", "index.d.ts", "index.js", "lib/"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["ecmascript", "es", "javascript", "jsx", "js", "markdown", "mdxjs", "mdx", "micromark-extension", "micromark", "unified"], "license": "MIT", "name": "micromark-extension-mdx-jsx", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm", "./script/grammar.js", ["remark-lint-fenced-code-flag", false]]}, "repository": "micromark/micromark-extension-mdx-jsx", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage && micromark-build", "format": "remark --frail --output --quiet -- . && prettier --log-level warn --write -- . && xo --fix", "test-api-dev": "node --conditions development test/index.js", "test-api-prod": "node --conditions production test/index.js", "test-api": "npm run test-api-dev && npm run test-api-prod", "test-coverage": "c8 --100 --reporter lcov -- npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "strict": true}, "type": "module", "version": "3.0.2", "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}], "prettier": true, "rules": {"unicorn/no-this-assignment": "off"}}}