"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[996],{5996:(e,t,a)=>{a.d(t,{fetchDocs:()=>s}),a(7505);var r=new Map;async function s(e,{api:t="/api/search",locale:a,tag:s}){let c=new URLSearchParams;c.set("query",e),a&&c.set("locale",a),s&&c.set("tag",Array.isArray(s)?s.join(","):s);let n=`${t}?${c}`,i=r.get(n);if(i)return i;let h=await fetch(n);if(!h.ok)throw Error(await h.text());let l=await h.json();return r.set(n,l),l}}}]);