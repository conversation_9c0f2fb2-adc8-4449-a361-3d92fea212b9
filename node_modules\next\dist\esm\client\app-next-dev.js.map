{"version": 3, "sources": ["../../src/client/app-next-dev.ts"], "sourcesContent": ["// TODO-APP: hydration warning\n\nimport './app-webpack'\n\nimport { renderAppDevOverlay } from 'next/dist/compiled/next-devtools'\nimport { appBootstrap } from './app-bootstrap'\nimport { getOwnerStack } from '../next-devtools/userspace/app/errors/stitched-error'\nimport { isRecoverableError } from './react-client-callbacks/on-recoverable-error'\n\n// eslint-disable-next-line @next/internal/typechecked-require\nconst instrumentationHooks = require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index') as typeof import('./app-index')\n  try {\n    hydrate(instrumentationHooks)\n  } finally {\n    renderAppDevOverlay(getOwnerStack, isRecoverableError)\n  }\n})\n"], "names": ["renderAppDevOverlay", "appBootstrap", "getOwnerStack", "isRecoverableError", "<PERSON><PERSON><PERSON><PERSON>", "require", "hydrate"], "mappings": "AAAA,8BAA8B;AAE9B,OAAO,gBAAe;AAEtB,SAASA,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,aAAa,QAAQ,uDAAsD;AACpF,SAASC,kBAAkB,QAAQ,gDAA+C;AAElF,8DAA8D;AAC9D,MAAMC,uBAAuBC,QAAQ;AAErCJ,aAAa;IACX,MAAM,EAAEK,OAAO,EAAE,GAAGD,QAAQ;IAC5B,IAAI;QACFC,QAAQF;IACV,SAAU;QACRJ,oBAAoBE,eAAeC;IACrC;AACF", "ignoreList": [0]}