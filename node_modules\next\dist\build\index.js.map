{"version": 3, "sources": ["../../src/build/index.ts"], "sourcesContent": ["import type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from './webpack/plugins/pages-manifest-plugin'\nimport type { ExportPathMap, NextConfigComplete } from '../server/config-shared'\nimport type { MiddlewareManifest } from './webpack/plugins/middleware-plugin'\nimport type { ActionManifest } from './webpack/plugins/flight-client-entry-plugin'\nimport type { CacheControl, Revalidate } from '../server/lib/cache-control'\n\nimport '../lib/setup-exception-listeners'\n\nimport { loadEnvConfig, type LoadedEnvFiles } from '@next/env'\nimport { bold, yellow } from '../lib/picocolors'\nimport { makeRe } from 'next/dist/compiled/picomatch'\nimport { existsSync, promises as fs } from 'fs'\nimport os from 'os'\nimport { Worker } from '../lib/worker'\nimport { defaultConfig } from '../server/config-shared'\nimport devalue from 'next/dist/compiled/devalue'\nimport findUp from 'next/dist/compiled/find-up'\nimport { nanoid } from 'next/dist/compiled/nanoid/index.cjs'\nimport path from 'path'\nimport {\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  PAGES_DIR_ALIAS,\n  INSTRUMENTATION_HOOK_FILENAME,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SUFFIX,\n  NEXT_RESUME_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  MATCHED_PATH_HEADER,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n} from '../lib/constants'\nimport { FileType, fileExists } from '../lib/file-exists'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport loadCustomRoutes, {\n  normalizeRouteRegex,\n} from '../lib/load-custom-routes'\nimport type {\n  CustomRoutes,\n  Header,\n  Redirect,\n  Rewrite,\n  RouteHas,\n} from '../lib/load-custom-routes'\nimport { nonNullable } from '../lib/non-nullable'\nimport { recursiveDelete } from '../lib/recursive-delete'\nimport { verifyPartytownSetup } from '../lib/verify-partytown-setup'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  IMAGES_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_FILES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  MIDDLEWARE_MANIFEST,\n  APP_PATHS_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  APP_BUILD_MANIFEST,\n  RSC_MODULE_TYPES,\n  NEXT_FONT_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  DYNAMIC_CSS_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport loadConfig from '../server/config'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getPagePath } from '../server/require'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n  writeTurborepoAccessTraceResult,\n} from './turborepo-access-trace'\n\nimport {\n  eventBuildOptimize,\n  eventCliSession,\n  eventBuildFeatureUsage,\n  eventNextPlugins,\n  EVENT_BUILD_FEATURE_USAGE,\n  eventPackageUsedInGetServerSideProps,\n  eventBuildCompleted,\n  eventBuildFailed,\n} from '../telemetry/events'\nimport type { EventBuildFeatureUsage } from '../telemetry/events'\nimport { Telemetry } from '../telemetry/storage'\nimport {\n  createPagesMapping,\n  collectAppFiles,\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n  processPageRoutes,\n  processAppRoutes,\n  processLayoutRoutes,\n  extractSlotsFromAppRoutes,\n  extractSlotsFromDefaultFiles,\n  combineSlots,\n  type RouteInfo,\n  type SlotInfo,\n  collectPagesFiles,\n} from './entries'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { generateBuildId } from './generate-build-id'\nimport { isWriteable } from './is-writeable'\nimport * as Log from './output/log'\nimport createSpinner from './spinner'\nimport { trace, flushAllTraces, setGlobal, type Span } from '../trace'\nimport {\n  detectConflictingPaths,\n  computeFromManifest,\n  getJsPageSizeInKb,\n  printCustomRoutes,\n  printTreeView,\n  copyTracedFiles,\n  isReservedPage,\n  isAppBuiltinNotFoundPage,\n  collectRoutesUsingEdgeRuntime,\n  collectMeta,\n} from './utils'\nimport type { PageInfo, PageInfos } from './utils'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport { writeBuildId } from './write-build-id'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport isError from '../lib/is-error'\nimport type { NextError } from '../lib/is-error'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport { lockfilePatchPromise, teardownTraceSubscriber } from './swc'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getFilesInDir } from '../lib/get-files-in-dir'\nimport { eventSwcPlugins } from '../telemetry/events/swc-plugins'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport {\n  ACTION_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n} from '../client/components/app-router-headers'\nimport { webpackBuild } from './webpack-build'\nimport { NextBuildContext, type MappedPages } from './build-context'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { createClientRouterFilter } from '../lib/create-client-router-filter'\nimport { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { startTypeChecking } from './type-check'\nimport { generateInterceptionRoutesRewrites } from '../lib/generate-interception-routes-rewrites'\n\nimport { buildDataRoute } from '../server/lib/router-utils/build-data-route'\nimport { collectBuildTraces } from './collect-build-traces'\nimport type { BuildTraceContext } from './webpack/plugins/next-trace-entrypoints-plugin'\nimport { formatManifest } from './manifests/formatter/format-manifest'\nimport {\n  recordFrameworkVersion,\n  updateBuildDiagnostics,\n  recordFetchMetrics,\n} from '../diagnostics/build-diagnostics'\nimport { getStartServerInfo, logStartInfo } from '../server/lib/app-info-log'\nimport type { NextEnabledDirectories } from '../server/base-server'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { traceMemoryUsage } from '../lib/memory/trace'\nimport { generateEncryptionKeyBase64 } from '../server/app-render/encryption-utils-server'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport uploadTrace from '../trace/upload-trace'\nimport {\n  checkIsAppPPREnabled,\n  checkIsRoutePPREnabled,\n} from '../server/lib/experimental/ppr'\nimport { FallbackMode, fallbackModeToFallbackField } from '../lib/fallback'\nimport { RenderingMode } from './rendering-mode'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport type { UseCacheTrackerKey } from './webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport {\n  buildInversePrefetchSegmentDataRoute,\n  buildPrefetchSegmentDataRoute,\n  type PrefetchSegmentDataRoute,\n} from '../server/lib/router-utils/build-prefetch-segment-data-route'\n\nimport { turbopackBuild } from './turbopack-build'\nimport { isPersistentCachingEnabled } from '../shared/lib/turbopack/utils'\nimport { inlineStaticEnv } from '../lib/inline-static-env'\nimport { populateStaticEnv } from '../lib/static-env'\nimport { durationToString } from './duration-to-string'\nimport { traceGlobals } from '../trace/shared'\nimport { extractNextErrorCode } from '../lib/error-telemetry-utils'\nimport { runAfterProductionCompile } from './after-production-compile'\nimport { generatePreviewKeys } from './preview-key-utils'\nimport { handleBuildComplete } from './adapter/build-complete'\nimport {\n  sortPageObjects,\n  sortPages,\n  sortSortableRouteObjects,\n} from '../shared/lib/router/utils/sortable-routes'\nimport { mkdir } from 'fs/promises'\nimport {\n  createRouteTypesManifest,\n  writeRouteTypesManifest,\n  writeValidatorFile,\n} from '../server/lib/router-utils/route-types-utils'\n\ntype Fallback = null | boolean | string\n\nexport interface PrerenderManifestRoute {\n  dataRoute: string | null\n  experimentalBypassFor?: RouteHas[]\n\n  /**\n   * The headers that should be served along side this prerendered route.\n   */\n  initialHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be served along side this prerendered route.\n   */\n  initialStatus?: number\n\n  /**\n   * The revalidate value for this route. This might be inferred from:\n   * - route segment configs\n   * - fetch calls\n   * - unstable_cache\n   * - \"use cache\"\n   */\n  initialRevalidateSeconds: Revalidate\n\n  /**\n   * The expire value for this route, which is inferred from the \"use cache\"\n   * functions that are used by the route, or the expireTime config.\n   */\n  initialExpireSeconds: number | undefined\n\n  /**\n   * The prefetch data route associated with this page. If not defined, this\n   * page does not support prefetching.\n   */\n  prefetchDataRoute: string | null | undefined\n\n  /**\n   * The dynamic route that this statically prerendered route is based on. If\n   * this is null, then the route was not based on a dynamic route.\n   */\n  srcRoute: string | null\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\nexport interface DynamicPrerenderManifestRoute {\n  dataRoute: string | null\n  dataRouteRegex: string | null\n  experimentalBypassFor?: RouteHas[]\n  fallback: Fallback\n\n  /**\n   * When defined, it describes the revalidation configuration for the fallback\n   * route.\n   */\n  fallbackRevalidate: Revalidate | undefined\n\n  /**\n   * When defined, it describes the expire configuration for the fallback route.\n   */\n  fallbackExpire: number | undefined\n\n  /**\n   * The headers that should used when serving the fallback.\n   */\n  fallbackHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be used when serving the fallback.\n   */\n  fallbackStatus?: number\n\n  /**\n   * The root params that are unknown for this fallback route.\n   */\n  fallbackRootParams: readonly string[] | undefined\n\n  /**\n   * The source route that this fallback route is based on. This is a reference\n   * so that we can associate this dynamic route with the correct source.\n   */\n  fallbackSourceRoute: string | undefined\n\n  prefetchDataRoute: string | null | undefined\n  prefetchDataRouteRegex: string | null | undefined\n  routeRegex: string\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\n/**\n * The headers that are allowed to be used when revalidating routes. Currently\n * this includes both headers used by the pages and app routers.\n */\nconst ALLOWED_HEADERS: string[] = [\n  'host',\n  MATCHED_PATH_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n]\n\nexport type PrerenderManifest = {\n  version: 4\n  routes: { [route: string]: PrerenderManifestRoute }\n  dynamicRoutes: { [route: string]: DynamicPrerenderManifestRoute }\n  notFoundRoutes: string[]\n  preview: __ApiPreviewProps\n}\n\ntype ManifestBuiltRoute = {\n  /**\n   * The route pattern used to match requests for this route.\n   */\n  regex: string\n}\n\nexport type ManifestRewriteRoute = ManifestBuiltRoute & Rewrite\nexport type ManifestRedirectRoute = ManifestBuiltRoute & Redirect\nexport type ManifestHeaderRoute = ManifestBuiltRoute & Header\n\nexport type ManifestRoute = ManifestBuiltRoute & {\n  page: string\n  namedRegex?: string\n  routeKeys?: { [key: string]: string }\n\n  /**\n   * If true, this indicates that the route has fallback root params. This is\n   * used to simplify the route regex for matching.\n   */\n  hasFallbackRootParams?: boolean\n\n  /**\n   * The prefetch segment data routes for this route. This is used to rewrite\n   * the prefetch segment data routes (or the inverse) to the correct\n   * destination.\n   */\n  prefetchSegmentDataRoutes?: PrefetchSegmentDataRoute[]\n\n  /**\n   * If true, this indicates that the route should not be considered for routing\n   * for the internal router, and instead has been added to support external\n   * routers.\n   */\n  skipInternalRouting?: boolean\n}\n\ntype DynamicManifestRoute = ManifestRoute & {\n  /**\n   * The source page that this route is based on. This is used to determine the\n   * source page for the route and is only relevant for app pages where PPR is\n   * enabled and the page differs from the source page.\n   */\n  sourcePage: string | undefined\n}\n\ntype ManifestDataRoute = {\n  page: string\n  routeKeys?: { [key: string]: string }\n  dataRouteRegex: string\n  namedDataRouteRegex?: string\n}\n\nexport type RoutesManifest = {\n  version: number\n  pages404: boolean\n  basePath: string\n  redirects: Array<ManifestRedirectRoute>\n  rewrites: {\n    beforeFiles: Array<ManifestRewriteRoute>\n    afterFiles: Array<ManifestRewriteRoute>\n    fallback: Array<ManifestRewriteRoute>\n  }\n  headers: Array<ManifestHeaderRoute>\n  staticRoutes: Array<ManifestRoute>\n  dynamicRoutes: ReadonlyArray<DynamicManifestRoute>\n  dataRoutes: Array<ManifestDataRoute>\n  i18n?: {\n    domains?: ReadonlyArray<{\n      http?: true\n      domain: string\n      locales?: readonly string[]\n      defaultLocale: string\n    }>\n    locales: readonly string[]\n    defaultLocale: string\n    localeDetection?: false\n  }\n  rsc: {\n    header: typeof RSC_HEADER\n    didPostponeHeader: typeof NEXT_DID_POSTPONE_HEADER\n    contentTypeHeader: typeof RSC_CONTENT_TYPE_HEADER\n    varyHeader: string\n    prefetchHeader: typeof NEXT_ROUTER_PREFETCH_HEADER\n    suffix: typeof RSC_SUFFIX\n    prefetchSuffix: typeof RSC_PREFETCH_SUFFIX\n    prefetchSegmentHeader: typeof NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n    prefetchSegmentDirSuffix: typeof RSC_SEGMENTS_DIR_SUFFIX\n    prefetchSegmentSuffix: typeof RSC_SEGMENT_SUFFIX\n  }\n  rewriteHeaders: {\n    pathHeader: typeof NEXT_REWRITTEN_PATH_HEADER\n    queryHeader: typeof NEXT_REWRITTEN_QUERY_HEADER\n  }\n  skipMiddlewareUrlNormalize?: boolean\n  caseSensitive?: boolean\n  /**\n   * Configuration related to Partial Prerendering.\n   */\n  ppr?: {\n    /**\n     * The chained response for the PPR resume.\n     */\n    chain: {\n      /**\n       * The headers that will indicate to Next.js that the request is for a PPR\n       * resume.\n       */\n      headers: Record<string, string>\n    }\n  }\n}\n\n/**\n * Converts a page to a manifest route.\n *\n * @param page The page to convert to a route.\n * @returns A route object.\n */\nfunction pageToRoute(page: string): ManifestRoute\n/**\n * Converts a page to a dynamic manifest route.\n *\n * @param page The page to convert to a route.\n * @param sourcePage The source page that this route is based on. This is used\n * to determine the source page for the route and is only relevant for app\n * pages when PPR is enabled on them.\n * @returns A route object.\n */\nfunction pageToRoute(\n  page: string,\n  sourcePage: string | undefined\n): DynamicManifestRoute\nfunction pageToRoute(\n  page: string,\n  sourcePage?: string\n): DynamicManifestRoute | ManifestRoute {\n  const routeRegex = getNamedRouteRegex(page, {\n    prefixRouteKeys: true,\n  })\n  return {\n    sourcePage,\n    page,\n    regex: normalizeRouteRegex(routeRegex.re.source),\n    routeKeys: routeRegex.routeKeys,\n    namedRegex: routeRegex.namedRegex,\n  }\n}\n\nfunction getCacheDir(distDir: string): string {\n  const cacheDir = path.join(distDir, 'cache')\n  if (ciEnvironment.isCI && !ciEnvironment.hasNextSupport) {\n    const hasCache = existsSync(cacheDir)\n\n    if (!hasCache) {\n      // Intentionally not piping to stderr which is what `Log.warn` does in case people fail in CI when\n      // stderr is detected.\n      console.log(\n        `${Log.prefixes.warn} No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache`\n      )\n    }\n  }\n  return cacheDir\n}\n\nasync function writeFileUtf8(filePath: string, content: string): Promise<void> {\n  await fs.writeFile(filePath, content, 'utf-8')\n}\n\nfunction readFileUtf8(filePath: string): Promise<string> {\n  return fs.readFile(filePath, 'utf8')\n}\n\nasync function writeManifest<T extends object>(\n  filePath: string,\n  manifest: T\n): Promise<void> {\n  await writeFileUtf8(filePath, formatManifest(manifest))\n}\n\nasync function readManifest<T extends object>(filePath: string): Promise<T> {\n  return JSON.parse(await readFileUtf8(filePath))\n}\n\nasync function writePrerenderManifest(\n  distDir: string,\n  manifest: DeepReadonly<PrerenderManifest>\n): Promise<void> {\n  await writeManifest(path.join(distDir, PRERENDER_MANIFEST), manifest)\n}\n\nasync function writeClientSsgManifest(\n  prerenderManifest: DeepReadonly<PrerenderManifest>,\n  {\n    buildId,\n    distDir,\n    locales,\n  }: {\n    buildId: string\n    distDir: string\n    locales: readonly string[] | undefined\n  }\n) {\n  const ssgPages = new Set<string>(\n    [\n      ...Object.entries(prerenderManifest.routes)\n        // Filter out dynamic routes\n        .filter(([, { srcRoute }]) => srcRoute == null)\n        .map(([route]) => normalizeLocalePath(route, locales).pathname),\n      ...Object.keys(prerenderManifest.dynamicRoutes),\n    ].sort()\n  )\n\n  const clientSsgManifestContent = `self.__SSG_MANIFEST=${devalue(\n    ssgPages\n  )};self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n  await writeFileUtf8(\n    path.join(distDir, CLIENT_STATIC_FILES_PATH, buildId, '_ssgManifest.js'),\n    clientSsgManifestContent\n  )\n}\n\nexport interface FunctionsConfigManifest {\n  version: number\n  functions: Record<\n    string,\n    {\n      maxDuration?: number | undefined\n      runtime?: 'nodejs'\n      regions?: string[] | string\n      matchers?: Array<{\n        regexp: string\n        originalSource: string\n        has?: Rewrite['has']\n        missing?: Rewrite['has']\n      }>\n    }\n  >\n}\n\nasync function writeFunctionsConfigManifest(\n  distDir: string,\n  manifest: FunctionsConfigManifest\n): Promise<void> {\n  await writeManifest(\n    path.join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n    manifest\n  )\n}\n\nexport interface RequiredServerFilesManifest {\n  version: number\n  config: NextConfigComplete\n  appDir: string\n  relativeAppDir: string\n  files: string[]\n  ignore: string[]\n}\n\nasync function writeRequiredServerFilesManifest(\n  distDir: string,\n  requiredServerFiles: RequiredServerFilesManifest\n) {\n  await writeManifest(\n    path.join(distDir, SERVER_FILES_MANIFEST),\n    requiredServerFiles\n  )\n}\n\nasync function writeImagesManifest(\n  distDir: string,\n  config: NextConfigComplete\n): Promise<void> {\n  const images = { ...config.images }\n  const { deviceSizes, imageSizes } = images\n  ;(images as any).sizes = [...deviceSizes, ...imageSizes]\n\n  // By default, remotePatterns will allow no remote images ([])\n  images.remotePatterns = (config?.images?.remotePatterns || []).map((p) => ({\n    // Modifying the manifest should also modify matchRemotePattern()\n    protocol: p.protocol?.replace(/:$/, '') as 'http' | 'https' | undefined,\n    hostname: makeRe(p.hostname).source,\n    port: p.port,\n    pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n    search: p.search,\n  }))\n\n  // By default, localPatterns will allow all local images (undefined)\n  if (config?.images?.localPatterns) {\n    images.localPatterns = config.images.localPatterns.map((p) => ({\n      // Modifying the manifest should also modify matchLocalPattern()\n      pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n      search: p.search,\n    }))\n  }\n\n  await writeManifest(path.join(distDir, IMAGES_MANIFEST), {\n    version: 1,\n    images,\n  })\n}\n\nconst STANDALONE_DIRECTORY = 'standalone' as const\nasync function writeStandaloneDirectory(\n  nextBuildSpan: Span,\n  distDir: string,\n  pageKeys: { pages: string[]; app: string[] | undefined },\n  denormalizedAppPages: string[] | undefined,\n  outputFileTracingRoot: string,\n  requiredServerFiles: RequiredServerFilesManifest,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>,\n  loadedEnvFiles: LoadedEnvFiles,\n  appDir: string | undefined\n) {\n  await nextBuildSpan\n    .traceChild('write-standalone-directory')\n    .traceAsyncFn(async () => {\n      await copyTracedFiles(\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        requiredServerFiles.appDir,\n        distDir,\n        pageKeys.pages,\n        denormalizedAppPages,\n        outputFileTracingRoot,\n        requiredServerFiles.config,\n        middlewareManifest,\n        hasNodeMiddleware,\n        hasInstrumentationHook,\n        staticPages\n      )\n\n      for (const file of [\n        ...requiredServerFiles.files,\n        path.join(requiredServerFiles.config.distDir, SERVER_FILES_MANIFEST),\n        ...loadedEnvFiles.reduce<string[]>((acc, envFile) => {\n          if (['.env', '.env.production'].includes(envFile.path)) {\n            acc.push(envFile.path)\n          }\n          return acc\n        }, []),\n      ]) {\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        const filePath = path.join(requiredServerFiles.appDir, file)\n        const outputPath = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, filePath)\n        )\n        await fs.mkdir(path.dirname(outputPath), {\n          recursive: true,\n        })\n        await fs.copyFile(filePath, outputPath)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareOutput = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'middleware.js'\n        )\n\n        await fs.mkdir(path.dirname(middlewareOutput), { recursive: true })\n        await fs.copyFile(\n          path.join(distDir, SERVER_DIRECTORY, 'middleware.js'),\n          middlewareOutput\n        )\n      }\n\n      await recursiveCopy(\n        path.join(distDir, SERVER_DIRECTORY, 'pages'),\n        path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'pages'\n        ),\n        { overwrite: true }\n      )\n      if (appDir) {\n        const originalServerApp = path.join(distDir, SERVER_DIRECTORY, 'app')\n        if (existsSync(originalServerApp)) {\n          await recursiveCopy(\n            originalServerApp,\n            path.join(\n              distDir,\n              STANDALONE_DIRECTORY,\n              path.relative(outputFileTracingRoot, distDir),\n              SERVER_DIRECTORY,\n              'app'\n            ),\n            { overwrite: true }\n          )\n        }\n      }\n    })\n}\n\nfunction getNumberOfWorkers(config: NextConfigComplete) {\n  if (\n    config.experimental.cpus &&\n    config.experimental.cpus !== defaultConfig.experimental!.cpus\n  ) {\n    return config.experimental.cpus\n  }\n\n  if (config.experimental.memoryBasedWorkersCount) {\n    return Math.max(\n      Math.min(config.experimental.cpus || 1, Math.floor(os.freemem() / 1e9)),\n      // enforce a minimum of 4 workers\n      4\n    )\n  }\n\n  if (config.experimental.cpus) {\n    return config.experimental.cpus\n  }\n\n  // Fall back to 4 workers if a count is not specified\n  return 4\n}\n\nconst staticWorkerPath = require.resolve('./worker')\nconst staticWorkerExposedMethods = [\n  'hasCustomGetInitialProps',\n  'isPageStatic',\n  'getDefinedNamedExports',\n  'exportPages',\n] as const\nexport type StaticWorker = typeof import('./worker') & Worker\nexport function createStaticWorker(\n  config: NextConfigComplete,\n  options: {\n    debuggerPortOffset: number\n    progress?: {\n      run: () => void\n      clear: () => void\n    }\n  }\n): StaticWorker {\n  const { debuggerPortOffset, progress } = options\n  return new Worker(staticWorkerPath, {\n    logger: Log,\n    numWorkers: getNumberOfWorkers(config),\n    onActivity: () => {\n      progress?.run()\n    },\n    onActivityAbort: () => {\n      progress?.clear()\n    },\n    debuggerPortOffset,\n    enableSourceMaps: config.experimental.enablePrerenderSourceMaps,\n    // remove --max-old-space-size flag as it can cause memory issues.\n    isolatedMemory: true,\n    enableWorkerThreads: config.experimental.workerThreads,\n    exposedMethods: staticWorkerExposedMethods,\n  }) as StaticWorker\n}\n\nasync function writeFullyStaticExport(\n  config: NextConfigComplete,\n  dir: string,\n  enabledDirectories: NextEnabledDirectories,\n  configOutDir: string,\n  nextBuildSpan: Span\n): Promise<void> {\n  const exportApp = (require('../export') as typeof import('../export'))\n    .default as typeof import('../export').default\n\n  await exportApp(\n    dir,\n    {\n      buildExport: false,\n      nextConfig: config,\n      enabledDirectories,\n      silent: true,\n      outdir: path.join(dir, configOutDir),\n      numWorkers: getNumberOfWorkers(config),\n    },\n    nextBuildSpan\n  )\n}\n\nasync function getBuildId(\n  isGenerateMode: boolean,\n  distDir: string,\n  nextBuildSpan: Span,\n  config: NextConfigComplete\n) {\n  if (isGenerateMode) {\n    return await fs.readFile(path.join(distDir, 'BUILD_ID'), 'utf8')\n  }\n  return await nextBuildSpan\n    .traceChild('generate-buildid')\n    .traceAsyncFn(() => generateBuildId(config.generateBuildId, nanoid))\n}\n\nexport default async function build(\n  dir: string,\n  reactProductionProfiling = false,\n  debugOutput = false,\n  debugPrerender = false,\n  runLint = true,\n  noMangling = false,\n  appDirOnly = false,\n  isTurbopack = false,\n  experimentalBuildMode: 'default' | 'compile' | 'generate' | 'generate-env',\n  traceUploadUrl: string | undefined\n): Promise<void> {\n  const isCompileMode = experimentalBuildMode === 'compile'\n  const isGenerateMode = experimentalBuildMode === 'generate'\n  NextBuildContext.isCompileMode = isCompileMode\n  const buildStartTime = Date.now()\n\n  let loadedConfig: NextConfigComplete | undefined\n  try {\n    const nextBuildSpan = trace('next-build', undefined, {\n      buildMode: experimentalBuildMode,\n      isTurboBuild: String(isTurbopack),\n      version: process.env.__NEXT_VERSION as string,\n    })\n\n    NextBuildContext.nextBuildSpan = nextBuildSpan\n    NextBuildContext.dir = dir\n    NextBuildContext.appDirOnly = appDirOnly\n    NextBuildContext.reactProductionProfiling = reactProductionProfiling\n    NextBuildContext.noMangling = noMangling\n    NextBuildContext.debugPrerender = debugPrerender\n\n    await nextBuildSpan.traceAsyncFn(async () => {\n      // attempt to load global env values so they are available in next.config.js\n      const { loadedEnvFiles } = nextBuildSpan\n        .traceChild('load-dotenv')\n        .traceFn(() => loadEnvConfig(dir, false, Log))\n      NextBuildContext.loadedEnvFiles = loadedEnvFiles\n\n      const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n      const config: NextConfigComplete = await nextBuildSpan\n        .traceChild('load-next-config')\n        .traceAsyncFn(() =>\n          turborepoTraceAccess(\n            () =>\n              loadConfig(PHASE_PRODUCTION_BUILD, dir, {\n                // Log for next.config loading process\n                silent: false,\n                reactProductionProfiling,\n                debugPrerender,\n              }),\n            turborepoAccessTraceResult\n          )\n        )\n      loadedConfig = config\n\n      process.env.NEXT_DEPLOYMENT_ID = config.deploymentId || ''\n      NextBuildContext.config = config\n\n      let configOutDir = 'out'\n      if (hasCustomExportOutput(config)) {\n        configOutDir = config.distDir\n        config.distDir = '.next'\n      }\n      const distDir = path.join(dir, config.distDir)\n      NextBuildContext.distDir = distDir\n      setGlobal('phase', PHASE_PRODUCTION_BUILD)\n      setGlobal('distDir', distDir)\n\n      const buildId = await getBuildId(\n        isGenerateMode,\n        distDir,\n        nextBuildSpan,\n        config\n      )\n      NextBuildContext.buildId = buildId\n\n      if (experimentalBuildMode === 'generate-env') {\n        if (isTurbopack) {\n          Log.warn('generate-env is not needed with turbopack')\n          process.exit(0)\n        }\n        Log.info('Inlining static env ...')\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n\n        Log.info('Complete')\n        await flushAllTraces()\n        teardownTraceSubscriber()\n        process.exit(0)\n      }\n\n      // when using compile mode static env isn't inlined so we\n      // need to populate in normal runtime env\n      if (isCompileMode || isGenerateMode) {\n        populateStaticEnv(config)\n      }\n\n      const customRoutes: CustomRoutes = await nextBuildSpan\n        .traceChild('load-custom-routes')\n        .traceAsyncFn(() => loadCustomRoutes(config))\n\n      const { headers, rewrites, redirects } = customRoutes\n      const combinedRewrites: Rewrite[] = [\n        ...rewrites.beforeFiles,\n        ...rewrites.afterFiles,\n        ...rewrites.fallback,\n      ]\n      const hasRewrites = combinedRewrites.length > 0\n      NextBuildContext.hasRewrites = hasRewrites\n      NextBuildContext.originalRewrites = config._originalRewrites\n      NextBuildContext.originalRedirects = config._originalRedirects\n\n      const cacheDir = getCacheDir(distDir)\n\n      const telemetry = new Telemetry({ distDir })\n\n      setGlobal('telemetry', telemetry)\n\n      const publicDir = path.join(dir, 'public')\n      const { pagesDir, appDir } = findPagesDir(dir)\n      NextBuildContext.pagesDir = pagesDir\n      NextBuildContext.appDir = appDir\n\n      const enabledDirectories: NextEnabledDirectories = {\n        app: typeof appDir === 'string',\n        pages: typeof pagesDir === 'string',\n      }\n\n      // Generate a random encryption key for this build.\n      // This key is used to encrypt cross boundary values and can be used to generate hashes.\n      const encryptionKey = await generateEncryptionKeyBase64({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.encryptionKey = encryptionKey\n\n      const isSrcDir = path\n        .relative(dir, pagesDir || appDir || '')\n        .startsWith('src')\n      const hasPublicDir = existsSync(publicDir)\n\n      telemetry.record(\n        eventCliSession(dir, config, {\n          webpackVersion: 5,\n          cliCommand: 'build',\n          isSrcDir,\n          hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n          isCustomServer: null,\n          turboFlag: false,\n          pagesDir: !!pagesDir,\n          appDir: !!appDir,\n        })\n      )\n\n      eventNextPlugins(path.resolve(dir)).then((events) =>\n        telemetry.record(events)\n      )\n\n      eventSwcPlugins(path.resolve(dir), config).then((events) =>\n        telemetry.record(events)\n      )\n\n      // Always log next version first then start rest jobs\n      const { envInfo, experimentalFeatures } = await getStartServerInfo({\n        dir,\n        dev: false,\n        debugPrerender,\n      })\n\n      logStartInfo({\n        networkUrl: null,\n        appUrl: null,\n        envInfo,\n        experimentalFeatures,\n      })\n\n      const ignoreESLint = Boolean(config.eslint.ignoreDuringBuilds)\n      const shouldLint = !ignoreESLint && runLint\n\n      const typeCheckingOptions: Parameters<typeof startTypeChecking>[0] = {\n        dir,\n        appDir,\n        pagesDir,\n        runLint,\n        shouldLint,\n        ignoreESLint,\n        telemetry,\n        nextBuildSpan,\n        config,\n        cacheDir,\n      }\n\n      const distDirCreated = await nextBuildSpan\n        .traceChild('create-dist-dir')\n        .traceAsyncFn(async () => {\n          try {\n            await fs.mkdir(distDir, { recursive: true })\n            return true\n          } catch (err) {\n            if (isError(err) && err.code === 'EPERM') {\n              return false\n            }\n            throw err\n          }\n        })\n\n      if (!distDirCreated || !(await isWriteable(distDir))) {\n        throw new Error(\n          '> Build directory is not writeable. https://nextjs.org/docs/messages/build-dir-not-writeable'\n        )\n      }\n\n      if (config.cleanDistDir && !isGenerateMode) {\n        await recursiveDelete(distDir, /^cache/)\n      }\n\n      if (appDir && 'exportPathMap' in config) {\n        Log.error(\n          'The \"exportPathMap\" configuration cannot be used with the \"app\" directory. Please use generateStaticParams() instead.'\n        )\n        await telemetry.flush()\n        process.exit(1)\n      }\n\n      const buildLintEvent: EventBuildFeatureUsage = {\n        featureName: 'build-lint',\n        invocationCount: shouldLint ? 1 : 0,\n      }\n      telemetry.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: buildLintEvent,\n      })\n\n      const validFileMatcher = createValidFileMatcher(\n        config.pageExtensions,\n        appDir\n      )\n\n      const providedPagePaths: string[] = JSON.parse(\n        process.env.NEXT_PRIVATE_PAGE_PATHS || '[]'\n      )\n\n      let pagesPaths = Boolean(process.env.NEXT_PRIVATE_PAGE_PATHS)\n        ? providedPagePaths\n        : !appDirOnly && pagesDir\n          ? await nextBuildSpan\n              .traceChild('collect-pages')\n              .traceAsyncFn(() => collectPagesFiles(pagesDir, validFileMatcher))\n          : []\n\n      const middlewareDetectionRegExp = new RegExp(\n        `^${MIDDLEWARE_FILENAME}\\\\.(?:${config.pageExtensions.join('|')})$`\n      )\n\n      const instrumentationHookDetectionRegExp = new RegExp(\n        `^${INSTRUMENTATION_HOOK_FILENAME}\\\\.(?:${config.pageExtensions.join(\n          '|'\n        )})$`\n      )\n\n      const rootDir = path.join((pagesDir || appDir)!, '..')\n      const includes = [\n        middlewareDetectionRegExp,\n        instrumentationHookDetectionRegExp,\n      ]\n\n      const rootPaths = Array.from(await getFilesInDir(rootDir))\n        .filter((file) => includes.some((include) => include.test(file)))\n        .sort(sortByPageExts(config.pageExtensions))\n        .map((file) => path.join(rootDir, file).replace(dir, ''))\n\n      const hasInstrumentationHook = rootPaths.some((p) =>\n        p.includes(INSTRUMENTATION_HOOK_FILENAME)\n      )\n      const hasMiddlewareFile = rootPaths.some((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n\n      NextBuildContext.hasInstrumentationHook = hasInstrumentationHook\n\n      const previewProps: __ApiPreviewProps = await generatePreviewKeys({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.previewProps = previewProps\n\n      const mappedPages = await nextBuildSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: false,\n            pageExtensions: config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagesPaths,\n            pagesDir,\n            appDir,\n          })\n        )\n      NextBuildContext.mappedPages = mappedPages\n\n      let mappedAppPages: MappedPages | undefined\n      let mappedAppLayouts: MappedPages | undefined\n      let denormalizedAppPages: string[] | undefined\n\n      if (appDir) {\n        const providedAppPaths: string[] = JSON.parse(\n          process.env.NEXT_PRIVATE_APP_PATHS || '[]'\n        )\n\n        let appPaths: string[]\n        let layoutPaths: string[]\n\n        if (Boolean(process.env.NEXT_PRIVATE_APP_PATHS)) {\n          // used for testing?\n          appPaths = providedAppPaths\n          layoutPaths = []\n        } else {\n          // Collect app pages, layouts, and default files in a single directory traversal\n          const result = await nextBuildSpan\n            .traceChild('collect-app-files')\n            .traceAsyncFn(() => collectAppFiles(appDir, validFileMatcher))\n\n          appPaths = result.appPaths\n          layoutPaths = result.layoutPaths\n          // Note: defaultPaths are not used in the build process, only for slot detection in generating route types\n        }\n\n        mappedAppPages = await nextBuildSpan\n          .traceChild('create-app-mapping')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: appPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        mappedAppLayouts = await nextBuildSpan\n          .traceChild('create-app-layouts')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: layoutPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        NextBuildContext.mappedAppPages = mappedAppPages\n      }\n\n      const mappedRootPaths = await createPagesMapping({\n        isDev: false,\n        pageExtensions: config.pageExtensions,\n        pagePaths: rootPaths,\n        pagesType: PAGE_TYPES.ROOT,\n        pagesDir: pagesDir,\n        appDir,\n      })\n      NextBuildContext.mappedRootPaths = mappedRootPaths\n\n      const pagesPageKeys = Object.keys(mappedPages)\n\n      const conflictingAppPagePaths: [pagePath: string, appPath: string][] = []\n      const appPageKeys = new Set<string>()\n      if (mappedAppPages) {\n        denormalizedAppPages = Object.keys(mappedAppPages)\n        for (const appKey of denormalizedAppPages) {\n          const normalizedAppPageKey = normalizeAppPath(appKey)\n          const pagePath = mappedPages[normalizedAppPageKey]\n          if (pagePath) {\n            const appPath = mappedAppPages[appKey]\n            conflictingAppPagePaths.push([\n              pagePath.replace(/^private-next-pages/, 'pages'),\n              appPath.replace(/^private-next-app-dir/, 'app'),\n            ])\n          }\n          appPageKeys.add(normalizedAppPageKey)\n        }\n      }\n\n      const appPaths = Array.from(appPageKeys)\n      // Interception routes are modelled as beforeFiles rewrites\n      rewrites.beforeFiles.push(\n        ...generateInterceptionRoutesRewrites(appPaths, config.basePath)\n      )\n\n      NextBuildContext.rewrites = rewrites\n\n      const totalAppPagesCount = appPaths.length\n\n      const pageKeys = {\n        pages: pagesPageKeys,\n        app: appPaths.length > 0 ? appPaths : undefined,\n      }\n\n      await nextBuildSpan\n        .traceChild('generate-route-types')\n        .traceAsyncFn(async () => {\n          const routeTypesFilePath = path.join(distDir, 'types', 'routes.d.ts')\n          const validatorFilePath = path.join(distDir, 'types', 'validator.ts')\n          await mkdir(path.dirname(routeTypesFilePath), { recursive: true })\n\n          let appRoutes: RouteInfo[] = []\n          let appRouteHandlers: RouteInfo[] = []\n          let layoutRoutes: RouteInfo[] = []\n          let slots: SlotInfo[] = []\n\n          const { pageRoutes, pageApiRoutes } = processPageRoutes(\n            mappedPages,\n            dir,\n            isSrcDir\n          )\n\n          // Build app routes\n          if (appDir && mappedAppPages) {\n            // Extract slots from both pages and default files\n            const slotsFromPages = extractSlotsFromAppRoutes(mappedAppPages)\n            let slotsFromDefaults: SlotInfo[] = []\n\n            // Collect and map default files for slot extraction\n            const { defaultPaths } = await nextBuildSpan\n              .traceChild('collect-default-files')\n              .traceAsyncFn(() => collectAppFiles(appDir, validFileMatcher))\n\n            if (defaultPaths.length > 0) {\n              const mappedDefaultFiles = await nextBuildSpan\n                .traceChild('create-default-mapping')\n                .traceAsyncFn(() =>\n                  createPagesMapping({\n                    pagePaths: defaultPaths,\n                    isDev: false,\n                    pagesType: PAGE_TYPES.APP,\n                    pageExtensions: config.pageExtensions,\n                    pagesDir,\n                    appDir,\n                  })\n                )\n              slotsFromDefaults =\n                extractSlotsFromDefaultFiles(mappedDefaultFiles)\n            }\n\n            // Combine slots and deduplicate using Set\n            slots = combineSlots(slotsFromPages, slotsFromDefaults)\n\n            const result = processAppRoutes(\n              mappedAppPages,\n              validFileMatcher,\n              dir,\n              isSrcDir\n            )\n            appRoutes = result.appRoutes\n            appRouteHandlers = result.appRouteHandlers\n          }\n\n          // Build app layouts\n          if (appDir && mappedAppLayouts) {\n            layoutRoutes = processLayoutRoutes(mappedAppLayouts, dir, isSrcDir)\n          }\n\n          const routeTypesManifest = await createRouteTypesManifest({\n            dir,\n            pageRoutes,\n            appRoutes,\n            appRouteHandlers,\n            pageApiRoutes,\n            layoutRoutes,\n            slots,\n            redirects: config.redirects,\n            rewrites: config.rewrites,\n            validatorFilePath,\n          })\n\n          await writeRouteTypesManifest(\n            routeTypesManifest,\n            routeTypesFilePath,\n            config\n          )\n          await writeValidatorFile(routeTypesManifest, validatorFilePath)\n        })\n\n      // Turbopack already handles conflicting app and page routes.\n      if (!isTurbopack) {\n        const numConflictingAppPaths = conflictingAppPagePaths.length\n        if (mappedAppPages && numConflictingAppPaths > 0) {\n          Log.error(\n            `Conflicting app and page file${\n              numConflictingAppPaths === 1 ? ' was' : 's were'\n            } found, please remove the conflicting files to continue:`\n          )\n          for (const [pagePath, appPath] of conflictingAppPagePaths) {\n            Log.error(`  \"${pagePath}\" - \"${appPath}\"`)\n          }\n          await telemetry.flush()\n          process.exit(1)\n        }\n      }\n\n      const conflictingPublicFiles: string[] = []\n      const hasPages404 = mappedPages['/404']?.startsWith(PAGES_DIR_ALIAS)\n      const hasApp404 = !!mappedAppPages?.[UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]\n      const hasCustomErrorPage =\n        mappedPages['/_error'].startsWith(PAGES_DIR_ALIAS)\n\n      if (hasPublicDir) {\n        const hasPublicUnderScoreNextDir = existsSync(\n          path.join(publicDir, '_next')\n        )\n        if (hasPublicUnderScoreNextDir) {\n          throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n        }\n      }\n\n      await nextBuildSpan\n        .traceChild('public-dir-conflict-check')\n        .traceAsyncFn(async () => {\n          // Check if pages conflict with files in `public`\n          // Only a page of public file can be served, not both.\n          for (const page in mappedPages) {\n            const hasPublicPageFile = await fileExists(\n              path.join(publicDir, page === '/' ? '/index' : page),\n              FileType.File\n            )\n            if (hasPublicPageFile) {\n              conflictingPublicFiles.push(page)\n            }\n          }\n\n          const numConflicting = conflictingPublicFiles.length\n\n          if (numConflicting) {\n            throw new Error(\n              `Conflicting public and page file${\n                numConflicting === 1 ? ' was' : 's were'\n              } found. https://nextjs.org/docs/messages/conflicting-public-file-page\\n${conflictingPublicFiles.join(\n                '\\n'\n              )}`\n            )\n          }\n        })\n\n      const nestedReservedPages = pageKeys.pages.filter((page) => {\n        return (\n          page.match(/\\/(_app|_document|_error)$/) && path.dirname(page) !== '/'\n        )\n      })\n\n      if (nestedReservedPages.length) {\n        Log.warn(\n          `The following reserved Next.js pages were detected not directly under the pages directory:\\n` +\n            nestedReservedPages.join('\\n') +\n            `\\nSee more info here: https://nextjs.org/docs/messages/nested-reserved-page\\n`\n        )\n      }\n\n      const restrictedRedirectPaths = ['/_next'].map((p) =>\n        config.basePath ? `${config.basePath}${p}` : p\n      )\n\n      const isAppCacheComponentsEnabled = Boolean(\n        config.experimental.cacheComponents\n      )\n      const isAuthInterruptsEnabled = Boolean(\n        config.experimental.authInterrupts\n      )\n      const isAppPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n\n      const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n      const dynamicRoutes: Array<DynamicManifestRoute> = []\n\n      /**\n       * A map of all the pages to their sourcePage value. This is only used for\n       * routes that have PPR enabled and clientSegmentEnabled is true.\n       */\n      const sourcePages = new Map<string, string>()\n      const routesManifest: RoutesManifest = nextBuildSpan\n        .traceChild('generate-routes-manifest')\n        .traceFn(() => {\n          const sortedRoutes = sortPages([\n            ...pageKeys.pages,\n            ...(pageKeys.app ?? []),\n          ])\n          const staticRoutes: Array<ManifestRoute> = []\n\n          for (const route of sortedRoutes) {\n            if (isDynamicRoute(route)) {\n              dynamicRoutes.push(\n                pageToRoute(\n                  route,\n                  // This property is only relevant when PPR is enabled.\n                  undefined\n                )\n              )\n            } else if (!isReservedPage(route)) {\n              staticRoutes.push(pageToRoute(route))\n            }\n          }\n\n          return {\n            version: 3,\n            pages404: true,\n            caseSensitive: !!config.experimental.caseSensitiveRoutes,\n            basePath: config.basePath,\n            redirects: redirects.map((r) =>\n              buildCustomRoute('redirect', r, restrictedRedirectPaths)\n            ),\n            headers: headers.map((r) => buildCustomRoute('header', r)),\n            rewrites: {\n              beforeFiles: rewrites.beforeFiles.map((r) =>\n                buildCustomRoute('rewrite', r)\n              ),\n              afterFiles: rewrites.afterFiles.map((r) =>\n                buildCustomRoute('rewrite', r)\n              ),\n              fallback: rewrites.fallback.map((r) =>\n                buildCustomRoute('rewrite', r)\n              ),\n            },\n            dynamicRoutes,\n            staticRoutes,\n            dataRoutes: [],\n            i18n: config.i18n || undefined,\n            rsc: {\n              header: RSC_HEADER,\n              // This vary header is used as a default. It is technically re-assigned in `base-server`,\n              // and may include an additional Vary option for `Next-URL`.\n              varyHeader: `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`,\n              prefetchHeader: NEXT_ROUTER_PREFETCH_HEADER,\n              didPostponeHeader: NEXT_DID_POSTPONE_HEADER,\n              contentTypeHeader: RSC_CONTENT_TYPE_HEADER,\n              suffix: RSC_SUFFIX,\n              prefetchSuffix: RSC_PREFETCH_SUFFIX,\n              prefetchSegmentHeader: NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n              prefetchSegmentSuffix: RSC_SEGMENT_SUFFIX,\n              prefetchSegmentDirSuffix: RSC_SEGMENTS_DIR_SUFFIX,\n            },\n            rewriteHeaders: {\n              pathHeader: NEXT_REWRITTEN_PATH_HEADER,\n              queryHeader: NEXT_REWRITTEN_QUERY_HEADER,\n            },\n            skipMiddlewareUrlNormalize: config.skipMiddlewareUrlNormalize,\n            ppr: isAppPPREnabled\n              ? {\n                  chain: {\n                    headers: {\n                      [NEXT_RESUME_HEADER]: '1',\n                    },\n                  },\n                }\n              : undefined,\n          } satisfies RoutesManifest\n        })\n\n      // For pages directory, we run type checking after route collection but before build.\n      if (!appDir && !isCompileMode) {\n        await startTypeChecking(typeCheckingOptions)\n      }\n\n      let clientRouterFilters:\n        | undefined\n        | ReturnType<typeof createClientRouterFilter>\n\n      if (config.experimental.clientRouterFilter) {\n        const nonInternalRedirects = (config._originalRedirects || []).filter(\n          (r: any) => !r.internal\n        )\n        clientRouterFilters = createClientRouterFilter(\n          [...appPaths],\n          config.experimental.clientRouterFilterRedirects\n            ? nonInternalRedirects\n            : [],\n          config.experimental.clientRouterFilterAllowedRate\n        )\n        NextBuildContext.clientRouterFilters = clientRouterFilters\n      }\n\n      // Ensure commonjs handling is used for files in the distDir (generally .next)\n      // Files outside of the distDir can be \"type\": \"module\"\n      await writeFileUtf8(\n        path.join(distDir, 'package.json'),\n        '{\"type\": \"commonjs\"}'\n      )\n\n      // These are written to distDir, so they need to come after creating and cleaning distDr.\n      await recordFrameworkVersion(process.env.__NEXT_VERSION as string)\n      await updateBuildDiagnostics({\n        buildStage: 'start',\n      })\n\n      const outputFileTracingRoot = config.outputFileTracingRoot || dir\n\n      const pagesManifestPath = path.join(\n        distDir,\n        SERVER_DIRECTORY,\n        PAGES_MANIFEST\n      )\n\n      let buildTraceContext: undefined | BuildTraceContext\n      let buildTracesPromise: Promise<any> | undefined = undefined\n\n      // If there's has a custom webpack config and disable the build worker.\n      // Otherwise respect the option if it's set.\n      const useBuildWorker =\n        config.experimental.webpackBuildWorker ||\n        (config.experimental.webpackBuildWorker === undefined &&\n          !config.webpack)\n      const runServerAndEdgeInParallel =\n        config.experimental.parallelServerCompiles\n      const collectServerBuildTracesInParallel =\n        config.experimental.parallelServerBuildTraces ||\n        (config.experimental.parallelServerBuildTraces === undefined &&\n          isCompileMode)\n\n      nextBuildSpan.setAttribute(\n        'has-custom-webpack-config',\n        String(!!config.webpack)\n      )\n      nextBuildSpan.setAttribute('use-build-worker', String(useBuildWorker))\n\n      if (\n        !useBuildWorker &&\n        (runServerAndEdgeInParallel || collectServerBuildTracesInParallel)\n      ) {\n        throw new Error(\n          'The \"parallelServerBuildTraces\" and \"parallelServerCompiles\" options may only be used when build workers can be used. Read more: https://nextjs.org/docs/messages/parallel-build-without-worker'\n        )\n      }\n\n      Log.info('Creating an optimized production build ...')\n      traceMemoryUsage('Starting build', nextBuildSpan)\n\n      await updateBuildDiagnostics({\n        buildStage: 'compile',\n        buildOptions: {\n          useBuildWorker: String(useBuildWorker),\n        },\n      })\n\n      let shutdownPromise = Promise.resolve()\n      if (!isGenerateMode) {\n        if (isTurbopack) {\n          const {\n            duration: compilerDuration,\n            shutdownPromise: p,\n            ...rest\n          } = await turbopackBuild(\n            process.env.NEXT_TURBOPACK_USE_WORKER === undefined ||\n              process.env.NEXT_TURBOPACK_USE_WORKER !== '0'\n          )\n          shutdownPromise = p\n          traceMemoryUsage('Finished build', nextBuildSpan)\n\n          buildTraceContext = rest.buildTraceContext\n\n          const durationString = durationToString(compilerDuration)\n          Log.event(`Compiled successfully in ${durationString}`)\n\n          telemetry.record(\n            eventBuildCompleted(pagesPaths, {\n              bundler: 'turbopack',\n              durationInSeconds: Math.round(compilerDuration),\n              totalAppPagesCount,\n            })\n          )\n        } else {\n          if (\n            runServerAndEdgeInParallel ||\n            collectServerBuildTracesInParallel\n          ) {\n            let durationInSeconds = 0\n\n            await updateBuildDiagnostics({\n              buildStage: 'compile-server',\n            })\n\n            const serverBuildPromise = webpackBuild(useBuildWorker, [\n              'server',\n            ]).then((res) => {\n              traceMemoryUsage('Finished server compilation', nextBuildSpan)\n              buildTraceContext = res.buildTraceContext\n              durationInSeconds += res.duration\n\n              if (collectServerBuildTracesInParallel) {\n                const buildTraceWorker = new Worker(\n                  require.resolve('./collect-build-traces'),\n                  {\n                    debuggerPortOffset: -1,\n                    isolatedMemory: false,\n                    numWorkers: 1,\n                    exposedMethods: ['collectBuildTraces'],\n                  }\n                ) as Worker & typeof import('./collect-build-traces')\n\n                buildTracesPromise = buildTraceWorker\n                  .collectBuildTraces({\n                    dir,\n                    config,\n                    distDir,\n                    // Serialize Map as this is sent to the worker.\n                    edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(new Map()),\n                    staticPages: [],\n                    hasSsrAmpPages: false,\n                    buildTraceContext,\n                    outputFileTracingRoot,\n                    isTurbopack: false,\n                  })\n                  .catch((err) => {\n                    console.error(err)\n                    process.exit(1)\n                  })\n              }\n            })\n            if (!runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n\n            const edgeBuildPromise = webpackBuild(useBuildWorker, [\n              'edge-server',\n            ]).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage(\n                'Finished edge-server compilation',\n                nextBuildSpan\n              )\n            })\n            if (runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n            await edgeBuildPromise\n\n            await updateBuildDiagnostics({\n              buildStage: 'webpack-compile-client',\n            })\n\n            await webpackBuild(useBuildWorker, ['client']).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage('Finished client compilation', nextBuildSpan)\n            })\n\n            const durationString = durationToString(durationInSeconds)\n            Log.event(`Compiled successfully in ${durationString}`)\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds,\n                totalAppPagesCount,\n              })\n            )\n          } else {\n            const { duration: compilerDuration, ...rest } = await webpackBuild(\n              useBuildWorker,\n              null\n            )\n            traceMemoryUsage('Finished build', nextBuildSpan)\n\n            buildTraceContext = rest.buildTraceContext\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds: compilerDuration,\n                totalAppPagesCount,\n              })\n            )\n          }\n        }\n        await runAfterProductionCompile({\n          config,\n          buildSpan: nextBuildSpan,\n          telemetry,\n          metadata: {\n            projectDir: dir,\n            distDir,\n          },\n        })\n      }\n\n      // For app directory, we run type checking after build.\n      if (appDir && !isCompileMode && !isGenerateMode) {\n        await updateBuildDiagnostics({\n          buildStage: 'type-checking',\n        })\n        await startTypeChecking(typeCheckingOptions)\n        traceMemoryUsage('Finished type checking', nextBuildSpan)\n      }\n\n      const postCompileSpinner = createSpinner('Collecting page data')\n\n      const buildManifestPath = path.join(distDir, BUILD_MANIFEST)\n      const appBuildManifestPath = path.join(distDir, APP_BUILD_MANIFEST)\n\n      let staticAppPagesCount = 0\n      let serverAppPagesCount = 0\n      let edgeRuntimeAppCount = 0\n      let edgeRuntimePagesCount = 0\n      const ssgPages = new Set<string>()\n      const ssgStaticFallbackPages = new Set<string>()\n      const ssgBlockingFallbackPages = new Set<string>()\n      const staticPages = new Set<string>()\n      const invalidPages = new Set<string>()\n      const hybridAmpPages = new Set<string>()\n      const serverPropsPages = new Set<string>()\n      const additionalPaths = new Map<string, PrerenderedRoute[]>()\n      const staticPaths = new Map<string, PrerenderedRoute[]>()\n      const appNormalizedPaths = new Map<string, string>()\n      const fallbackModes = new Map<string, FallbackMode>()\n      const appDefaultConfigs = new Map<string, AppSegmentConfig>()\n      const pageInfos: PageInfos = new Map<string, PageInfo>()\n      let pagesManifest = await readManifest<PagesManifest>(pagesManifestPath)\n      const buildManifest = await readManifest<BuildManifest>(buildManifestPath)\n      const appBuildManifest = appDir\n        ? await readManifest<AppBuildManifest>(appBuildManifestPath)\n        : undefined\n\n      const appPathRoutes: Record<string, string> = {}\n\n      if (appDir) {\n        const appPathsManifest = await readManifest<Record<string, string>>(\n          path.join(distDir, SERVER_DIRECTORY, APP_PATHS_MANIFEST)\n        )\n\n        for (const key in appPathsManifest) {\n          appPathRoutes[key] = normalizeAppPath(key)\n        }\n\n        await writeManifest(\n          path.join(distDir, APP_PATH_ROUTES_MANIFEST),\n          appPathRoutes\n        )\n      }\n\n      process.env.NEXT_PHASE = PHASE_PRODUCTION_BUILD\n\n      const worker = createStaticWorker(config, { debuggerPortOffset: -1 })\n\n      const analysisBegin = process.hrtime()\n      const staticCheckSpan = nextBuildSpan.traceChild('static-check')\n\n      const functionsConfigManifest: FunctionsConfigManifest = {\n        version: 1,\n        functions: {},\n      }\n\n      const {\n        customAppGetInitialProps,\n        namedExports,\n        isNextImageImported,\n        hasSsrAmpPages,\n        hasNonStaticErrorPage,\n      } = await staticCheckSpan.traceAsyncFn(async () => {\n        if (isCompileMode) {\n          return {\n            customAppGetInitialProps: false,\n            namedExports: [],\n            isNextImageImported: true,\n            hasSsrAmpPages: !!pagesDir,\n            hasNonStaticErrorPage: true,\n          }\n        }\n\n        const { configFileName, publicRuntimeConfig, serverRuntimeConfig } =\n          config\n        const runtimeEnvConfig = { publicRuntimeConfig, serverRuntimeConfig }\n        const sriEnabled = Boolean(config.experimental.sri?.algorithm)\n\n        const nonStaticErrorPageSpan = staticCheckSpan.traceChild(\n          'check-static-error-page'\n        )\n        const errorPageHasCustomGetInitialProps =\n          nonStaticErrorPageSpan.traceAsyncFn(\n            async () =>\n              hasCustomErrorPage &&\n              (await worker.hasCustomGetInitialProps({\n                page: '/_error',\n                distDir,\n                runtimeEnvConfig,\n                checkingApp: false,\n                sriEnabled,\n              }))\n          )\n\n        const errorPageStaticResult = nonStaticErrorPageSpan.traceAsyncFn(\n          async () =>\n            hasCustomErrorPage &&\n            worker.isPageStatic({\n              dir,\n              page: '/_error',\n              distDir,\n              configFileName,\n              runtimeEnvConfig,\n              cacheComponents: isAppCacheComponentsEnabled,\n              authInterrupts: isAuthInterruptsEnabled,\n              httpAgentOptions: config.httpAgentOptions,\n              locales: config.i18n?.locales,\n              defaultLocale: config.i18n?.defaultLocale,\n              nextConfigOutput: config.output,\n              pprConfig: config.experimental.ppr,\n              cacheLifeProfiles: config.experimental.cacheLife,\n              buildId,\n              sriEnabled,\n            })\n        )\n\n        const appPageToCheck = '/_app'\n\n        const customAppGetInitialPropsPromise = worker.hasCustomGetInitialProps(\n          {\n            page: appPageToCheck,\n            distDir,\n            runtimeEnvConfig,\n            checkingApp: true,\n            sriEnabled,\n          }\n        )\n\n        const namedExportsPromise = worker.getDefinedNamedExports({\n          page: appPageToCheck,\n          distDir,\n          runtimeEnvConfig,\n          sriEnabled,\n        })\n\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let isNextImageImported: boolean | undefined\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let hasSsrAmpPages = false\n\n        const computedManifestData = await computeFromManifest(\n          { build: buildManifest, app: appBuildManifest },\n          distDir,\n          config.experimental.gzipSize\n        )\n\n        const middlewareManifest: MiddlewareManifest = require(\n          path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n        )\n\n        const actionManifest = appDir\n          ? (require(\n              path.join(\n                distDir,\n                SERVER_DIRECTORY,\n                SERVER_REFERENCE_MANIFEST + '.json'\n              )\n            ) as ActionManifest)\n          : null\n        const entriesWithAction = actionManifest ? new Set() : null\n        if (actionManifest && entriesWithAction) {\n          for (const id in actionManifest.node) {\n            for (const entry in actionManifest.node[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n          for (const id in actionManifest.edge) {\n            for (const entry in actionManifest.edge[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n        }\n\n        for (const key of Object.keys(middlewareManifest?.functions)) {\n          if (key.startsWith('/api')) {\n            edgeRuntimePagesCount++\n          }\n        }\n\n        await Promise.all(\n          Object.entries(pageKeys)\n            .reduce<Array<{ pageType: keyof typeof pageKeys; page: string }>>(\n              (acc, [key, files]) => {\n                if (!files) {\n                  return acc\n                }\n\n                const pageType = key as keyof typeof pageKeys\n\n                for (const page of files) {\n                  acc.push({ pageType, page })\n                }\n\n                return acc\n              },\n              []\n            )\n            .map(({ pageType, page }) => {\n              const checkPageSpan = staticCheckSpan.traceChild('check-page', {\n                page,\n              })\n              return checkPageSpan.traceAsyncFn(async () => {\n                const actualPage = normalizePagePath(page)\n                const [size, totalSize] = await getJsPageSizeInKb(\n                  pageType,\n                  actualPage,\n                  distDir,\n                  buildManifest,\n                  appBuildManifest,\n                  config.experimental.gzipSize,\n                  computedManifestData\n                )\n\n                let isRoutePPREnabled = false\n                let isSSG = false\n                let isStatic = false\n                let isServerComponent = false\n                let isHybridAmp = false\n                let ssgPageRoutes: string[] | null = null\n                let pagePath = ''\n\n                if (pageType === 'pages') {\n                  pagePath =\n                    pagesPaths.find((p) => {\n                      p = normalizePathSep(p)\n                      return (\n                        p.startsWith(actualPage + '.') ||\n                        p.startsWith(actualPage + '/index.')\n                      )\n                    }) || ''\n                }\n                let originalAppPath: string | undefined\n\n                if (pageType === 'app' && mappedAppPages) {\n                  for (const [originalPath, normalizedPath] of Object.entries(\n                    appPathRoutes\n                  )) {\n                    if (normalizedPath === page) {\n                      pagePath = mappedAppPages[originalPath].replace(\n                        /^private-next-app-dir/,\n                        ''\n                      )\n                      originalAppPath = originalPath\n                      break\n                    }\n                  }\n                }\n\n                const pageFilePath = isAppBuiltinNotFoundPage(pagePath)\n                  ? require.resolve(\n                      'next/dist/client/components/builtin/not-found'\n                    )\n                  : path.join(\n                      (pageType === 'pages' ? pagesDir : appDir) || '',\n                      pagePath\n                    )\n\n                const isInsideAppDir = pageType === 'app'\n                const staticInfo = pagePath\n                  ? await getStaticInfoIncludingLayouts({\n                      isInsideAppDir,\n                      pageFilePath,\n                      pageExtensions: config.pageExtensions,\n                      appDir,\n                      config,\n                      isDev: false,\n                      // If this route is an App Router page route, inherit the\n                      // route segment configs (e.g. `runtime`) from the layout by\n                      // passing the `originalAppPath`, which should end with `/page`.\n                      page: isInsideAppDir ? originalAppPath! : page,\n                    })\n                  : undefined\n\n                if (staticInfo?.hadUnsupportedValue) {\n                  errorFromUnsupportedSegmentConfig()\n                }\n\n                // If there's any thing that would contribute to the functions\n                // configuration, we need to add it to the manifest.\n                if (\n                  typeof staticInfo?.runtime !== 'undefined' ||\n                  typeof staticInfo?.maxDuration !== 'undefined' ||\n                  typeof staticInfo?.preferredRegion !== 'undefined'\n                ) {\n                  const regions = staticInfo?.preferredRegion\n                    ? typeof staticInfo.preferredRegion === 'string'\n                      ? [staticInfo.preferredRegion]\n                      : staticInfo.preferredRegion\n                    : undefined\n\n                  functionsConfigManifest.functions[page] = {\n                    maxDuration: staticInfo?.maxDuration,\n                    ...(regions && { regions }),\n                  }\n                }\n\n                const pageRuntime = middlewareManifest.functions[\n                  originalAppPath || page\n                ]\n                  ? 'edge'\n                  : staticInfo?.runtime\n\n                if (!isCompileMode) {\n                  isServerComponent =\n                    pageType === 'app' &&\n                    staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n                  if (pageType === 'app' || !isReservedPage(page)) {\n                    try {\n                      let edgeInfo: any\n\n                      if (isEdgeRuntime(pageRuntime)) {\n                        if (pageType === 'app') {\n                          edgeRuntimeAppCount++\n                        } else {\n                          edgeRuntimePagesCount++\n                        }\n\n                        const manifestKey =\n                          pageType === 'pages' ? page : originalAppPath || ''\n\n                        edgeInfo = middlewareManifest.functions[manifestKey]\n                      }\n\n                      let isPageStaticSpan =\n                        checkPageSpan.traceChild('is-page-static')\n                      let workerResult = await isPageStaticSpan.traceAsyncFn(\n                        () => {\n                          return worker.isPageStatic({\n                            dir,\n                            page,\n                            originalAppPath,\n                            distDir,\n                            configFileName,\n                            runtimeEnvConfig,\n                            httpAgentOptions: config.httpAgentOptions,\n                            locales: config.i18n?.locales,\n                            defaultLocale: config.i18n?.defaultLocale,\n                            parentId: isPageStaticSpan.getId(),\n                            pageRuntime,\n                            edgeInfo,\n                            pageType,\n                            cacheComponents: isAppCacheComponentsEnabled,\n                            authInterrupts: isAuthInterruptsEnabled,\n                            cacheHandler: config.cacheHandler,\n                            cacheHandlers: config.experimental.cacheHandlers,\n                            isrFlushToDisk: ciEnvironment.hasNextSupport\n                              ? false\n                              : config.experimental.isrFlushToDisk,\n                            maxMemoryCacheSize: config.cacheMaxMemorySize,\n                            nextConfigOutput: config.output,\n                            pprConfig: config.experimental.ppr,\n                            cacheLifeProfiles: config.experimental.cacheLife,\n                            buildId,\n                            sriEnabled,\n                          })\n                        }\n                      )\n\n                      if (pageType === 'app' && originalAppPath) {\n                        appNormalizedPaths.set(originalAppPath, page)\n                        // TODO-APP: handle prerendering with edge\n                        if (isEdgeRuntime(pageRuntime)) {\n                          isStatic = false\n                          isSSG = false\n\n                          Log.warnOnce(\n                            `Using edge runtime on a page currently disables static generation for that page`\n                          )\n                        } else {\n                          const isDynamic = isDynamicRoute(page)\n\n                          if (\n                            typeof workerResult.isRoutePPREnabled === 'boolean'\n                          ) {\n                            isRoutePPREnabled = workerResult.isRoutePPREnabled\n                          }\n\n                          // If this route can be partially pre-rendered, then\n                          // mark it as such and mark that it can be\n                          // generated server-side.\n                          if (workerResult.isRoutePPREnabled) {\n                            isSSG = true\n                            isStatic = true\n\n                            staticPaths.set(originalAppPath, [])\n                          }\n\n                          if (workerResult.prerenderedRoutes) {\n                            staticPaths.set(\n                              originalAppPath,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                            isSSG = true\n                          }\n\n                          const appConfig = workerResult.appConfig || {}\n                          if (appConfig.revalidate !== 0) {\n                            const hasGenerateStaticParams =\n                              workerResult.prerenderedRoutes &&\n                              workerResult.prerenderedRoutes.length > 0\n\n                            if (\n                              config.output === 'export' &&\n                              isDynamic &&\n                              !hasGenerateStaticParams\n                            ) {\n                              throw new Error(\n                                `Page \"${page}\" is missing \"generateStaticParams()\" so it cannot be used with \"output: export\" config.`\n                              )\n                            }\n\n                            // Mark the app as static if:\n                            // - It has no dynamic param\n                            // - It doesn't have generateStaticParams but `dynamic` is set to\n                            //   `error` or `force-static`\n                            if (!isDynamic) {\n                              staticPaths.set(originalAppPath, [\n                                {\n                                  params: {},\n                                  pathname: page,\n                                  encodedPathname: page,\n                                  fallbackRouteParams: [],\n                                  fallbackMode:\n                                    workerResult.prerenderFallbackMode,\n                                  fallbackRootParams: [],\n                                  throwOnEmptyStaticShell: true,\n                                },\n                              ])\n                              isStatic = true\n                            } else if (\n                              !hasGenerateStaticParams &&\n                              (appConfig.dynamic === 'error' ||\n                                appConfig.dynamic === 'force-static')\n                            ) {\n                              staticPaths.set(originalAppPath, [])\n                              isStatic = true\n                              isRoutePPREnabled = false\n                            }\n                          }\n\n                          if (workerResult.prerenderFallbackMode) {\n                            fallbackModes.set(\n                              originalAppPath,\n                              workerResult.prerenderFallbackMode\n                            )\n                          }\n\n                          appDefaultConfigs.set(originalAppPath, appConfig)\n                        }\n                      } else {\n                        if (isEdgeRuntime(pageRuntime)) {\n                          if (workerResult.hasStaticProps) {\n                            console.warn(\n                              `\"getStaticProps\" is not yet supported fully with \"experimental-edge\", detected on ${page}`\n                            )\n                          }\n                          workerResult.isStatic = false\n                          workerResult.hasStaticProps = false\n                        }\n\n                        if (\n                          workerResult.isStatic === false &&\n                          (workerResult.isHybridAmp || workerResult.isAmpOnly)\n                        ) {\n                          hasSsrAmpPages = true\n                        }\n\n                        if (workerResult.isHybridAmp) {\n                          isHybridAmp = true\n                          hybridAmpPages.add(page)\n                        }\n\n                        if (workerResult.isNextImageImported) {\n                          isNextImageImported = true\n                        }\n\n                        if (workerResult.hasStaticProps) {\n                          ssgPages.add(page)\n                          isSSG = true\n\n                          if (\n                            workerResult.prerenderedRoutes &&\n                            workerResult.prerenderedRoutes.length > 0\n                          ) {\n                            additionalPaths.set(\n                              page,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                          }\n\n                          if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.BLOCKING_STATIC_RENDER\n                          ) {\n                            ssgBlockingFallbackPages.add(page)\n                          } else if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.PRERENDER\n                          ) {\n                            ssgStaticFallbackPages.add(page)\n                          }\n                        } else if (workerResult.hasServerProps) {\n                          serverPropsPages.add(page)\n                        } else if (\n                          workerResult.isStatic &&\n                          !isServerComponent &&\n                          (await customAppGetInitialPropsPromise) === false\n                        ) {\n                          staticPages.add(page)\n                          isStatic = true\n                        } else if (isServerComponent) {\n                          // This is a static server component page that doesn't have\n                          // gSP or gSSP. We still treat it as a SSG page.\n                          ssgPages.add(page)\n                          isSSG = true\n                        }\n\n                        if (hasPages404 && page === '/404') {\n                          if (\n                            !workerResult.isStatic &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            throw new Error(\n                              `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                            )\n                          }\n                          // we need to ensure the 404 lambda is present since we use\n                          // it when _app has getInitialProps\n                          if (\n                            (await customAppGetInitialPropsPromise) &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            staticPages.delete(page)\n                          }\n                        }\n\n                        if (\n                          STATIC_STATUS_PAGES.includes(page) &&\n                          !workerResult.isStatic &&\n                          !workerResult.hasStaticProps\n                        ) {\n                          throw new Error(\n                            `\\`pages${page}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                          )\n                        }\n                      }\n                    } catch (err) {\n                      if (\n                        !isError(err) ||\n                        err.message !== 'INVALID_DEFAULT_EXPORT'\n                      )\n                        throw err\n                      invalidPages.add(page)\n                    }\n                  }\n\n                  if (pageType === 'app') {\n                    if (isSSG || isStatic) {\n                      staticAppPagesCount++\n                    } else {\n                      serverAppPagesCount++\n                    }\n                  }\n                }\n\n                pageInfos.set(page, {\n                  originalAppPath,\n                  size,\n                  totalSize,\n                  isStatic,\n                  isSSG,\n                  isRoutePPREnabled,\n                  isHybridAmp,\n                  ssgPageRoutes,\n                  initialCacheControl: undefined,\n                  runtime: pageRuntime,\n                  pageDuration: undefined,\n                  ssgPageDurations: undefined,\n                  hasEmptyStaticShell: undefined,\n                })\n              })\n            })\n        )\n\n        const errorPageResult = await errorPageStaticResult\n        const nonStaticErrorPage =\n          (await errorPageHasCustomGetInitialProps) ||\n          (errorPageResult && errorPageResult.hasServerProps)\n\n        const returnValue = {\n          customAppGetInitialProps: await customAppGetInitialPropsPromise,\n          namedExports: await namedExportsPromise,\n          isNextImageImported,\n          hasSsrAmpPages,\n          hasNonStaticErrorPage: nonStaticErrorPage,\n        }\n\n        return returnValue\n      })\n\n      if (postCompileSpinner) postCompileSpinner.stopAndPersist()\n      traceMemoryUsage('Finished collecting page data', nextBuildSpan)\n\n      if (customAppGetInitialProps) {\n        console.warn(\n          bold(yellow(`Warning: `)) +\n            yellow(\n              `You have opted-out of Automatic Static Optimization due to \\`getInitialProps\\` in \\`pages/_app\\`. This does not opt-out pages with \\`getStaticProps\\``\n            )\n        )\n        console.warn(\n          'Read more: https://nextjs.org/docs/messages/opt-out-auto-static-optimization\\n'\n        )\n      }\n\n      const { cacheHandler } = config\n\n      const instrumentationHookEntryFiles: string[] = []\n      if (hasInstrumentationHook) {\n        instrumentationHookEntryFiles.push(\n          path.join(SERVER_DIRECTORY, `${INSTRUMENTATION_HOOK_FILENAME}.js`)\n        )\n        // If there's edge routes, append the edge instrumentation hook\n        // Turbopack generates this chunk with a hashed name and references it in middleware-manifest.\n        if (!isTurbopack && (edgeRuntimeAppCount || edgeRuntimePagesCount)) {\n          instrumentationHookEntryFiles.push(\n            path.join(\n              SERVER_DIRECTORY,\n              `edge-${INSTRUMENTATION_HOOK_FILENAME}.js`\n            )\n          )\n        }\n      }\n\n      const requiredServerFilesManifest = nextBuildSpan\n        .traceChild('generate-required-server-files')\n        .traceFn(() => {\n          const normalizedCacheHandlers: Record<string, string> = {}\n\n          for (const [key, value] of Object.entries(\n            config.experimental.cacheHandlers || {}\n          )) {\n            if (key && value) {\n              normalizedCacheHandlers[key] = path.relative(distDir, value)\n            }\n          }\n\n          const serverFilesManifest: RequiredServerFilesManifest = {\n            version: 1,\n            config: {\n              ...config,\n              configFile: undefined,\n              ...(ciEnvironment.hasNextSupport\n                ? {\n                    compress: false,\n                  }\n                : {}),\n              cacheHandler: cacheHandler\n                ? path.relative(distDir, cacheHandler)\n                : config.cacheHandler,\n              experimental: {\n                ...config.experimental,\n                cacheHandlers: normalizedCacheHandlers,\n                trustHostHeader: ciEnvironment.hasNextSupport,\n                isExperimentalCompile: isCompileMode,\n              },\n            },\n            appDir: dir,\n            relativeAppDir: path.relative(outputFileTracingRoot, dir),\n            files: [\n              ROUTES_MANIFEST,\n              path.relative(distDir, pagesManifestPath),\n              BUILD_MANIFEST,\n              PRERENDER_MANIFEST,\n              path.join(SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_BUILD_MANIFEST + '.js'),\n              ...(!isTurbopack\n                ? [\n                    path.join(\n                      SERVER_DIRECTORY,\n                      MIDDLEWARE_REACT_LOADABLE_MANIFEST + '.js'\n                    ),\n                    REACT_LOADABLE_MANIFEST,\n                  ]\n                : []),\n              ...(appDir\n                ? [\n                    ...(config.experimental.sri\n                      ? [\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.js'\n                          ),\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.json'\n                          ),\n                        ]\n                      : []),\n                    path.join(SERVER_DIRECTORY, APP_PATHS_MANIFEST),\n                    path.join(APP_PATH_ROUTES_MANIFEST),\n                    APP_BUILD_MANIFEST,\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.js'\n                    ),\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.json'\n                    ),\n                  ]\n                : []),\n              ...(pagesDir && !isTurbopack\n                ? [\n                    DYNAMIC_CSS_MANIFEST + '.json',\n                    path.join(SERVER_DIRECTORY, DYNAMIC_CSS_MANIFEST + '.js'),\n                  ]\n                : []),\n              BUILD_ID_FILE,\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.js'),\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.json'),\n              SERVER_FILES_MANIFEST,\n              ...instrumentationHookEntryFiles,\n            ]\n              .filter(nonNullable)\n              .map((file) => path.join(config.distDir, file)),\n            ignore: [] as string[],\n          }\n\n          return serverFilesManifest\n        })\n\n      if (!hasSsrAmpPages) {\n        requiredServerFilesManifest.ignore.push(\n          path.relative(\n            dir,\n            path.join(\n              path.dirname(\n                require.resolve(\n                  'next/dist/compiled/@ampproject/toolbox-optimizer'\n                )\n              ),\n              '**/*'\n            )\n          )\n        )\n      }\n\n      const middlewareFile = rootPaths.find((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n      let hasNodeMiddleware = false\n\n      if (middlewareFile) {\n        const staticInfo = await getStaticInfoIncludingLayouts({\n          isInsideAppDir: false,\n          pageFilePath: path.join(dir, middlewareFile),\n          config,\n          appDir,\n          pageExtensions: config.pageExtensions,\n          isDev: false,\n          page: 'middleware',\n        })\n\n        if (staticInfo.hadUnsupportedValue) {\n          errorFromUnsupportedSegmentConfig()\n        }\n\n        if (staticInfo.runtime === 'nodejs') {\n          hasNodeMiddleware = true\n          functionsConfigManifest.functions['/_middleware'] = {\n            runtime: staticInfo.runtime,\n            matchers: staticInfo.middleware?.matchers ?? [\n              {\n                regexp: '^.*$',\n                originalSource: '/:path*',\n              },\n            ],\n          }\n\n          if (isTurbopack) {\n            await writeManifest(\n              path.join(\n                distDir,\n                'static',\n                buildId,\n                TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST\n              ),\n              functionsConfigManifest.functions['/_middleware'].matchers || []\n            )\n          }\n        }\n      }\n\n      await writeFunctionsConfigManifest(distDir, functionsConfigManifest)\n\n      if (!isGenerateMode && !buildTracesPromise) {\n        buildTracesPromise = collectBuildTraces({\n          dir,\n          config,\n          distDir,\n          edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(pageInfos),\n          staticPages: [...staticPages],\n          nextBuildSpan,\n          hasSsrAmpPages,\n          buildTraceContext,\n          outputFileTracingRoot,\n          isTurbopack: true,\n        }).catch((err) => {\n          console.error(err)\n          process.exit(1)\n        })\n      }\n\n      if (serverPropsPages.size > 0 || ssgPages.size > 0) {\n        // We update the routes manifest after the build with the\n        // data routes since we can't determine these until after build\n        routesManifest.dataRoutes = sortPages([\n          ...serverPropsPages,\n          ...ssgPages,\n        ]).map((page) => {\n          return buildDataRoute(page, buildId)\n        })\n      }\n\n      // We need to write the manifest with rewrites before build\n      await nextBuildSpan\n        .traceChild('write-routes-manifest')\n        .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n\n      // Since custom _app.js can wrap the 404 page we have to opt-out of static optimization if it has getInitialProps\n      // Only export the static 404 when there is no /_error present\n      const useStaticPages404 =\n        !customAppGetInitialProps && (!hasNonStaticErrorPage || hasPages404)\n\n      if (invalidPages.size > 0) {\n        const err = new Error(\n          `Build optimization failed: found page${\n            invalidPages.size === 1 ? '' : 's'\n          } without a React Component as default export in \\n${[...invalidPages]\n            .map((pg) => `pages${pg}`)\n            .join(\n              '\\n'\n            )}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.\\n`\n        ) as NextError\n        err.code = 'BUILD_OPTIMIZATION_FAILED'\n        throw err\n      }\n\n      await writeBuildId(distDir, buildId)\n\n      if (config.experimental.optimizeCss) {\n        const globOrig =\n          require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n\n        const cssFilePaths = await new Promise<string[]>((resolve, reject) => {\n          globOrig(\n            '**/*.css',\n            { cwd: path.join(distDir, 'static') },\n            (err, files) => {\n              if (err) {\n                return reject(err)\n              }\n              resolve(files)\n            }\n          )\n        })\n\n        requiredServerFilesManifest.files.push(\n          ...cssFilePaths.map((filePath) =>\n            path.join(config.distDir, 'static', filePath)\n          )\n        )\n      }\n\n      const features: EventBuildFeatureUsage[] = [\n        {\n          featureName: 'experimental/cacheComponents',\n          invocationCount: config.experimental.cacheComponents ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/optimizeCss',\n          invocationCount: config.experimental.optimizeCss ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/nextScriptWorkers',\n          invocationCount: config.experimental.nextScriptWorkers ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/ppr',\n          invocationCount: config.experimental.ppr ? 1 : 0,\n        },\n        {\n          featureName: 'turbopackPersistentCaching',\n          invocationCount: isPersistentCachingEnabled(config) ? 1 : 0,\n        },\n      ]\n      telemetry.record(\n        features.map((feature) => {\n          return {\n            eventName: EVENT_BUILD_FEATURE_USAGE,\n            payload: feature,\n          }\n        })\n      )\n\n      await writeRequiredServerFilesManifest(\n        distDir,\n        requiredServerFilesManifest\n      )\n\n      // we don't need to inline for turbopack build as\n      // it will handle it's own caching separate of compile\n      if (isGenerateMode && !isTurbopack) {\n        Log.info('Inlining static env ...')\n\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n      }\n\n      const middlewareManifest: MiddlewareManifest = await readManifest(\n        path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      )\n\n      const prerenderManifest: PrerenderManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: previewProps,\n      }\n\n      const tbdPrerenderRoutes: string[] = []\n\n      const { i18n } = config\n\n      const usedStaticStatusPages = STATIC_STATUS_PAGES.filter(\n        (page) =>\n          mappedPages[page] &&\n          mappedPages[page].startsWith('private-next-pages')\n      )\n      usedStaticStatusPages.forEach((page) => {\n        if (!ssgPages.has(page) && !customAppGetInitialProps) {\n          staticPages.add(page)\n        }\n      })\n\n      const hasPages500 = usedStaticStatusPages.includes('/500')\n      const useDefaultStatic500 =\n        !hasPages500 && !hasNonStaticErrorPage && !customAppGetInitialProps\n\n      const combinedPages = [...staticPages, ...ssgPages]\n      const isApp404Static = staticPaths.has(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      const hasStaticApp404 = hasApp404 && isApp404Static\n\n      await updateBuildDiagnostics({\n        buildStage: 'static-generation',\n      })\n\n      // we need to trigger automatic exporting when we have\n      // - static 404/500\n      // - getStaticProps paths\n      // - experimental app is enabled\n      if (\n        !isCompileMode &&\n        (combinedPages.length > 0 ||\n          useStaticPages404 ||\n          useDefaultStatic500 ||\n          appDir)\n      ) {\n        const staticGenerationSpan =\n          nextBuildSpan.traceChild('static-generation')\n        await staticGenerationSpan.traceAsyncFn(async () => {\n          detectConflictingPaths(\n            [\n              ...combinedPages,\n              ...pageKeys.pages.filter((page) => !combinedPages.includes(page)),\n            ],\n            ssgPages,\n            new Map(\n              Array.from(additionalPaths.entries()).map(\n                ([page, routes]): [string, string[]] => {\n                  return [page, routes.map((route) => route.pathname)]\n                }\n              )\n            )\n          )\n\n          const exportApp = (require('../export') as typeof import('../export'))\n            .default as typeof import('../export').default\n\n          const exportConfig: NextConfigComplete = {\n            ...config,\n            // Default map will be the collection of automatic statically exported\n            // pages and incremental pages.\n            // n.b. we cannot handle this above in combinedPages because the dynamic\n            // page must be in the `pages` array, but not in the mapping.\n            exportPathMap: (defaultMap: ExportPathMap) => {\n              // Dynamically routed pages should be prerendered to be used as\n              // a client-side skeleton (fallback) while data is being fetched.\n              // This ensures the end-user never sees a 500 or slow response from the\n              // server.\n              //\n              // Note: prerendering disables automatic static optimization.\n              ssgPages.forEach((page) => {\n                if (isDynamicRoute(page)) {\n                  tbdPrerenderRoutes.push(page)\n\n                  if (ssgStaticFallbackPages.has(page)) {\n                    // Override the rendering for the dynamic page to be treated as a\n                    // fallback render.\n                    if (i18n) {\n                      defaultMap[`/${i18n.defaultLocale}${page}`] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    } else {\n                      defaultMap[page] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    }\n                  } else {\n                    // Remove dynamically routed pages from the default path map when\n                    // fallback behavior is disabled.\n                    delete defaultMap[page]\n                  }\n                }\n              })\n\n              // Append the \"well-known\" routes we should prerender for, e.g. blog\n              // post slugs.\n              additionalPaths.forEach((routes, page) => {\n                routes.forEach((route) => {\n                  defaultMap[route.pathname] = {\n                    page,\n                    _ssgPath: route.encodedPathname,\n                  }\n                })\n              })\n\n              if (useStaticPages404) {\n                defaultMap['/404'] = {\n                  page: hasPages404 ? '/404' : '/_error',\n                }\n              }\n\n              if (useDefaultStatic500) {\n                defaultMap['/500'] = {\n                  page: '/_error',\n                }\n              }\n\n              // TODO: output manifest specific to app paths and their\n              // revalidate periods and dynamicParams settings\n              staticPaths.forEach((routes, originalAppPath) => {\n                const appConfig = appDefaultConfigs.get(originalAppPath)\n                const isDynamicError = appConfig?.dynamic === 'error'\n\n                const isRoutePPREnabled: boolean = appConfig\n                  ? checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                  : false\n\n                routes.forEach((route) => {\n                  // If the route has any dynamic root segments, we need to skip\n                  // rendering the route. This is because we don't support\n                  // revalidating the shells without the parameters present.\n                  if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    return\n                  }\n\n                  defaultMap[route.pathname] = {\n                    page: originalAppPath,\n                    _ssgPath: route.encodedPathname,\n                    _fallbackRouteParams: route.fallbackRouteParams,\n                    _isDynamicError: isDynamicError,\n                    _isAppDir: true,\n                    _isRoutePPREnabled: isRoutePPREnabled,\n                    _allowEmptyStaticShell: !route.throwOnEmptyStaticShell,\n                  }\n                })\n              })\n\n              if (i18n) {\n                for (const page of [\n                  ...staticPages,\n                  ...ssgPages,\n                  ...(useStaticPages404 ? ['/404'] : []),\n                  ...(useDefaultStatic500 ? ['/500'] : []),\n                ]) {\n                  const isSsg = ssgPages.has(page)\n                  const isDynamic = isDynamicRoute(page)\n                  const isFallback = isSsg && ssgStaticFallbackPages.has(page)\n\n                  for (const locale of i18n.locales) {\n                    // skip fallback generation for SSG pages without fallback mode\n                    if (isSsg && isDynamic && !isFallback) continue\n                    const outputPath = `/${locale}${page === '/' ? '' : page}`\n\n                    defaultMap[outputPath] = {\n                      page: defaultMap[page]?.page || page,\n                      _locale: locale,\n                      _pagesFallback: isFallback,\n                    }\n                  }\n\n                  if (isSsg) {\n                    // remove non-locale prefixed variant from defaultMap\n                    delete defaultMap[page]\n                  }\n                }\n              }\n\n              return defaultMap\n            },\n          }\n\n          const outdir = path.join(distDir, 'export')\n          const exportResult = await exportApp(\n            dir,\n            {\n              nextConfig: exportConfig,\n              enabledDirectories,\n              silent: true,\n              buildExport: true,\n              debugOutput,\n              debugPrerender,\n              pages: combinedPages,\n              outdir,\n              statusMessage: 'Generating static pages',\n              numWorkers: getNumberOfWorkers(exportConfig),\n            },\n            nextBuildSpan\n          )\n\n          // If there was no result, there's nothing more to do.\n          if (!exportResult) return\n\n          const getFallbackMode = (route: PrerenderedRoute) => {\n            const hasEmptyStaticShell = exportResult.byPath.get(\n              route.pathname\n            )?.hasEmptyStaticShell\n\n            // If the route has an empty static shell and is not configured to\n            // throw on empty static shell, then we should use the blocking\n            // static render mode.\n            if (\n              hasEmptyStaticShell &&\n              !route.throwOnEmptyStaticShell &&\n              route.fallbackMode === FallbackMode.PRERENDER\n            ) {\n              return FallbackMode.BLOCKING_STATIC_RENDER\n            }\n\n            // If the route has no fallback mode, then we should use the\n            // `NOT_FOUND` fallback mode.\n            if (!route.fallbackMode) {\n              return FallbackMode.NOT_FOUND\n            }\n\n            return route.fallbackMode\n          }\n\n          const getCacheControl = (\n            exportPath: string,\n            defaultRevalidate: Revalidate = false\n          ): CacheControl => {\n            const cacheControl =\n              exportResult.byPath.get(exportPath)?.cacheControl\n\n            if (!cacheControl) {\n              return { revalidate: defaultRevalidate, expire: undefined }\n            }\n\n            if (\n              cacheControl.revalidate !== false &&\n              cacheControl.revalidate > 0 &&\n              cacheControl.expire === undefined\n            ) {\n              return {\n                revalidate: cacheControl.revalidate,\n                expire: config.expireTime,\n              }\n            }\n\n            return cacheControl\n          }\n\n          if (debugOutput || process.env.NEXT_SSG_FETCH_METRICS === '1') {\n            recordFetchMetrics(exportResult)\n          }\n\n          writeTurborepoAccessTraceResult({\n            distDir: config.distDir,\n            traces: [\n              turborepoAccessTraceResult,\n              ...exportResult.turborepoAccessTraceResults.values(),\n            ],\n          })\n\n          prerenderManifest.notFoundRoutes = Array.from(\n            exportResult.ssgNotFoundPaths\n          )\n\n          // remove server bundles that were exported\n          for (const page of staticPages) {\n            const serverBundle = getPagePath(page, distDir, undefined, false)\n            await fs.unlink(serverBundle)\n          }\n\n          staticPaths.forEach((prerenderedRoutes, originalAppPath) => {\n            const page = appNormalizedPaths.get(originalAppPath)\n            if (!page) throw new InvariantError('Page not found')\n\n            const appConfig = appDefaultConfigs.get(originalAppPath)\n            if (!appConfig) throw new InvariantError('App config not found')\n\n            let hasRevalidateZero =\n              appConfig.revalidate === 0 ||\n              getCacheControl(page).revalidate === 0\n\n            if (hasRevalidateZero && pageInfos.get(page)?.isStatic) {\n              // if the page was marked as being static, but it contains dynamic data\n              // (ie, in the case of a static generation bailout), then it should be marked dynamic\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                isStatic: false,\n                isSSG: false,\n              })\n            }\n\n            const isAppRouteHandler = isAppRouteRoute(originalAppPath)\n\n            // When this is an app page and PPR is enabled, the route supports\n            // partial pre-rendering.\n            const isRoutePPREnabled: true | undefined =\n              !isAppRouteHandler &&\n              checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                ? true\n                : undefined\n\n            const htmlBotsRegexString =\n              // The htmlLimitedBots has been converted to a string during loadConfig\n              config.htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING\n\n            // this flag is used to selectively bypass the static cache and invoke the lambda directly\n            // to enable server actions on static routes\n            const bypassFor: RouteHas[] = [\n              { type: 'header', key: ACTION_HEADER },\n              {\n                type: 'header',\n                key: 'content-type',\n                value: 'multipart/form-data;.*',\n              },\n              // If it's PPR rendered non-static page, bypass the PPR cache when streaming metadata is enabled.\n              // This will skip the postpone data for those bots requests and instead produce a dynamic render.\n              ...(isRoutePPREnabled\n                ? [\n                    {\n                      type: 'header',\n                      key: 'user-agent',\n                      value: htmlBotsRegexString,\n                    },\n                  ]\n                : []),\n            ]\n\n            // We should collect all the dynamic routes into a single array for\n            // this page. Including the full fallback route (the original\n            // route), any routes that were generated with unknown route params\n            // should be collected and included in the dynamic routes part\n            // of the manifest instead.\n            const staticPrerenderedRoutes: PrerenderedRoute[] = []\n            const dynamicPrerenderedRoutes: PrerenderedRoute[] = []\n\n            // Sort the outputted routes to ensure consistent output. Any route\n            // though that has unknown route params will be pulled and sorted\n            // independently. This is because the routes with unknown route\n            // params will contain the dynamic path parameters, some of which\n            // may conflict with the actual prerendered routes.\n            const unsortedUnknownPrerenderRoutes: PrerenderedRoute[] = []\n            const unsortedKnownPrerenderRoutes: PrerenderedRoute[] = []\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                unsortedUnknownPrerenderRoutes.push(prerenderedRoute)\n              } else {\n                unsortedKnownPrerenderRoutes.push(prerenderedRoute)\n              }\n            }\n\n            const sortedUnknownPrerenderRoutes = sortPageObjects(\n              unsortedUnknownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n            const sortedKnownPrerenderRoutes = sortPageObjects(\n              unsortedKnownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n\n            prerenderedRoutes = [\n              ...sortedKnownPrerenderRoutes,\n              ...sortedUnknownPrerenderRoutes,\n            ]\n\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                isRoutePPREnabled &&\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                // If the route has unknown params, then we need to add it to\n                // the list of dynamic routes.\n                dynamicPrerenderedRoutes.push(prerenderedRoute)\n              } else {\n                // If the route doesn't have unknown params, then we need to\n                // add it to the list of static routes.\n                staticPrerenderedRoutes.push(prerenderedRoute)\n              }\n            }\n\n            // Handle all the static routes.\n            for (const route of staticPrerenderedRoutes) {\n              if (isDynamicRoute(page) && route.pathname === page) continue\n\n              const {\n                metadata = {},\n                hasEmptyStaticShell,\n                hasPostponed,\n              } = exportResult.byPath.get(route.pathname) ?? {}\n\n              const cacheControl = getCacheControl(\n                route.pathname,\n                appConfig.revalidate\n              )\n\n              pageInfos.set(route.pathname, {\n                ...(pageInfos.get(route.pathname) as PageInfo),\n                hasPostponed,\n                hasEmptyStaticShell,\n                initialCacheControl: cacheControl,\n              })\n\n              // update the page (eg /blog/[slug]) to also have the postpone metadata\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                hasPostponed,\n                hasEmptyStaticShell,\n                initialCacheControl: cacheControl,\n              })\n\n              if (cacheControl.revalidate !== 0) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                let dataRoute: string | null\n                if (isAppRouteHandler) {\n                  dataRoute = null\n                } else {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | null | undefined\n                // While we may only write the `.rsc` when the route does not\n                // have PPR enabled, we still want to generate the route when\n                // deployed so it doesn't 404. If the app has PPR enabled, we\n                // should add this key.\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                const meta = collectMeta(metadata)\n                const status =\n                  route.pathname === UNDERSCORE_NOT_FOUND_ROUTE\n                    ? 404\n                    : meta.status\n\n                prerenderManifest.routes[route.pathname] = {\n                  initialStatus: status,\n                  initialHeaders: meta.headers,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalPPR: isRoutePPREnabled,\n                  experimentalBypassFor: bypassFor,\n                  initialRevalidateSeconds: cacheControl.revalidate,\n                  initialExpireSeconds: cacheControl.expire,\n                  srcRoute: page,\n                  dataRoute,\n                  prefetchDataRoute,\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              } else {\n                hasRevalidateZero = true\n                // we might have determined during prerendering that this page\n                // used dynamic data\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isSSG: false,\n                  isStatic: false,\n                })\n              }\n            }\n\n            if (!hasRevalidateZero && isDynamicRoute(page)) {\n              // When PPR fallbacks aren't used, we need to include it here. If\n              // they are enabled, then it'll already be included in the\n              // prerendered routes.\n              if (!isRoutePPREnabled) {\n                dynamicPrerenderedRoutes.push({\n                  params: {},\n                  pathname: page,\n                  encodedPathname: page,\n                  fallbackRouteParams: [],\n                  fallbackMode:\n                    fallbackModes.get(originalAppPath) ??\n                    FallbackMode.NOT_FOUND,\n                  fallbackRootParams: [],\n                  throwOnEmptyStaticShell: true,\n                })\n              }\n\n              for (const route of dynamicPrerenderedRoutes) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                const metadata = exportResult.byPath.get(\n                  route.pathname\n                )?.metadata\n\n                const cacheControl = getCacheControl(route.pathname)\n\n                let dataRoute: string | null = null\n                if (!isAppRouteHandler) {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | undefined\n                let dynamicRoute = routesManifest.dynamicRoutes.find(\n                  (r) => r.page === route.pathname\n                )\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n\n                  // If the dynamic route wasn't found, then we need to create\n                  // it. This ensures that for each fallback shell there's an\n                  // entry in the app routes manifest which enables routing for\n                  // this fallback shell.\n                  if (!dynamicRoute) {\n                    dynamicRoute = pageToRoute(route.pathname, page)\n                    sourcePages.set(route.pathname, page)\n\n                    // This route is not for the internal router, but instead\n                    // for external routers.\n                    dynamicRoute.skipInternalRouting = true\n\n                    // Push this to the end of the array. The dynamic routes are\n                    // sorted by page later.\n                    dynamicRoutes.push(dynamicRoute)\n                  }\n                }\n\n                if (\n                  !isAppRouteHandler &&\n                  (metadata?.segmentPaths ||\n                    (route.fallbackRootParams &&\n                      route.fallbackRootParams.length > 0))\n                ) {\n                  // If PPR isn't enabled, then we might not find the dynamic\n                  // route by pathname. If that's the case, we need to find the\n                  // route by page.\n                  if (!dynamicRoute) {\n                    dynamicRoute = dynamicRoutes.find((r) => r.page === page)\n\n                    // If it can't be found by page, we must throw an error.\n                    if (!dynamicRoute) {\n                      throw new InvariantError('Dynamic route not found')\n                    }\n                  }\n\n                  if (metadata?.segmentPaths) {\n                    const pageSegmentPath = metadata.segmentPaths.find((item) =>\n                      item.endsWith('__PAGE__')\n                    )\n                    if (!pageSegmentPath) {\n                      throw new Error(`Invariant: missing __PAGE__ segmentPath`)\n                    }\n\n                    // We build a combined segment data route from the\n                    // page segment as we need to limit the number of\n                    // routes we output and they can be shared\n                    const builtSegmentDataRoute = buildPrefetchSegmentDataRoute(\n                      route.pathname,\n                      pageSegmentPath\n                    )\n\n                    builtSegmentDataRoute.source =\n                      builtSegmentDataRoute.source.replace(\n                        '/__PAGE__\\\\.segment\\\\.rsc$',\n                        `(?<segment>/__PAGE__\\\\.segment\\\\.rsc|\\\\.segment\\\\.rsc)(?:/)?$`\n                      )\n                    builtSegmentDataRoute.destination =\n                      builtSegmentDataRoute.destination.replace(\n                        '/__PAGE__.segment.rsc',\n                        '$segment'\n                      )\n                    dynamicRoute.prefetchSegmentDataRoutes ??= []\n                    dynamicRoute.prefetchSegmentDataRoutes.push(\n                      builtSegmentDataRoute\n                    )\n                  }\n                  // If the route has fallback root params, and we don't have\n                  // any segment paths, we need to write the inverse prefetch\n                  // segment data route so that it can first rewrite the /_tree\n                  // request to the prefetch RSC route. We also need to set the\n                  // `hasFallbackRootParams` flag so that we can simplify the\n                  // route regex for matching.\n                  else if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    dynamicRoute.hasFallbackRootParams = true\n                    dynamicRoute.prefetchSegmentDataRoutes = [\n                      buildInversePrefetchSegmentDataRoute(\n                        dynamicRoute.page,\n                        // We use the special segment path of `/_tree` because it's\n                        // the first one sent by the client router so it's the only\n                        // one we need to rewrite to the regular prefetch RSC route.\n                        '/_tree'\n                      ),\n                    ]\n                  }\n                }\n\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isDynamicAppRoute: true,\n                  // if PPR is turned on and the route contains a dynamic segment,\n                  // we assume it'll be partially prerendered\n                  hasPostponed: isRoutePPREnabled,\n                })\n\n                const fallbackMode = getFallbackMode(route)\n\n                // When the route is configured to serve a prerender, we should\n                // use the cache control from the export result. If it can't be\n                // found, mark that we should keep the shell forever\n                // (revalidate: `false` via `getCacheControl()`).\n                const fallbackCacheControl =\n                  isRoutePPREnabled && fallbackMode === FallbackMode.PRERENDER\n                    ? cacheControl\n                    : undefined\n\n                const fallback: Fallback = fallbackModeToFallbackField(\n                  fallbackMode,\n                  route.pathname\n                )\n\n                const meta =\n                  metadata &&\n                  isRoutePPREnabled &&\n                  fallbackMode === FallbackMode.PRERENDER\n                    ? collectMeta(metadata)\n                    : {}\n\n                prerenderManifest.dynamicRoutes[route.pathname] = {\n                  experimentalPPR: isRoutePPREnabled,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalBypassFor: bypassFor,\n                  routeRegex: normalizeRouteRegex(\n                    getNamedRouteRegex(route.pathname, {\n                      prefixRouteKeys: false,\n                    }).re.source\n                  ),\n                  dataRoute,\n                  fallback,\n                  fallbackRevalidate: fallbackCacheControl?.revalidate,\n                  fallbackExpire: fallbackCacheControl?.expire,\n                  fallbackStatus: meta.status,\n                  fallbackHeaders: meta.headers,\n                  fallbackRootParams: fallback\n                    ? route.fallbackRootParams\n                    : undefined,\n                  fallbackSourceRoute: route.fallbackRouteParams?.length\n                    ? page\n                    : undefined,\n                  dataRouteRegex: !dataRoute\n                    ? null\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(dataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  prefetchDataRoute,\n                  prefetchDataRouteRegex: !prefetchDataRoute\n                    ? undefined\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(prefetchDataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              }\n            }\n          })\n\n          const moveExportedPage = async (\n            originPage: string,\n            page: string,\n            file: string,\n            isSsg: boolean,\n            ext: 'html' | 'json',\n            additionalSsgFile = false\n          ) => {\n            return staticGenerationSpan\n              .traceChild('move-exported-page')\n              .traceAsyncFn(async () => {\n                file = `${file}.${ext}`\n                const orig = path.join(outdir, file)\n                const pagePath = getPagePath(\n                  originPage,\n                  distDir,\n                  undefined,\n                  false\n                )\n\n                const relativeDest = path\n                  .relative(\n                    path.join(distDir, SERVER_DIRECTORY),\n                    path.join(\n                      path.join(\n                        pagePath,\n                        // strip leading / and then recurse number of nested dirs\n                        // to place from base folder\n                        originPage\n                          .slice(1)\n                          .split('/')\n                          .map(() => '..')\n                          .join('/')\n                      ),\n                      file\n                    )\n                  )\n                  .replace(/\\\\/g, '/')\n\n                if (\n                  !isSsg &&\n                  !(\n                    // don't add static status page to manifest if it's\n                    // the default generated version e.g. no pages/500\n                    (\n                      STATIC_STATUS_PAGES.includes(page) &&\n                      !usedStaticStatusPages.includes(page)\n                    )\n                  )\n                ) {\n                  pagesManifest[page] = relativeDest\n                }\n\n                const dest = path.join(distDir, SERVER_DIRECTORY, relativeDest)\n                const isNotFound =\n                  prerenderManifest.notFoundRoutes.includes(page)\n\n                // for SSG files with i18n the non-prerendered variants are\n                // output with the locale prefixed so don't attempt moving\n                // without the prefix\n                if ((!i18n || additionalSsgFile) && !isNotFound) {\n                  await fs.mkdir(path.dirname(dest), { recursive: true })\n                  await fs.rename(orig, dest)\n                } else if (i18n && !isSsg) {\n                  // this will be updated with the locale prefixed variant\n                  // since all files are output with the locale prefix\n                  delete pagesManifest[page]\n                }\n\n                if (i18n) {\n                  if (additionalSsgFile) return\n\n                  const localeExt = page === '/' ? path.extname(file) : ''\n                  const relativeDestNoPages = relativeDest.slice(\n                    'pages/'.length\n                  )\n\n                  for (const locale of i18n.locales) {\n                    const curPath = `/${locale}${page === '/' ? '' : page}`\n\n                    if (\n                      isSsg &&\n                      prerenderManifest.notFoundRoutes.includes(curPath)\n                    ) {\n                      continue\n                    }\n\n                    const updatedRelativeDest = path\n                      .join(\n                        'pages',\n                        locale + localeExt,\n                        // if it's the top-most index page we want it to be locale.EXT\n                        // instead of locale/index.html\n                        page === '/' ? '' : relativeDestNoPages\n                      )\n                      .replace(/\\\\/g, '/')\n\n                    const updatedOrig = path.join(\n                      outdir,\n                      locale + localeExt,\n                      page === '/' ? '' : file\n                    )\n                    const updatedDest = path.join(\n                      distDir,\n                      SERVER_DIRECTORY,\n                      updatedRelativeDest\n                    )\n\n                    if (!isSsg) {\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                    await fs.mkdir(path.dirname(updatedDest), {\n                      recursive: true,\n                    })\n                    await fs.rename(updatedOrig, updatedDest)\n                  }\n                }\n              })\n          }\n\n          async function moveExportedAppNotFoundTo404() {\n            return staticGenerationSpan\n              .traceChild('move-exported-app-not-found-')\n              .traceAsyncFn(async () => {\n                const orig = path.join(\n                  distDir,\n                  'server',\n                  'app',\n                  '_not-found.html'\n                )\n                const updatedRelativeDest = path\n                  .join('pages', '404.html')\n                  .replace(/\\\\/g, '/')\n\n                if (existsSync(orig)) {\n                  await fs.copyFile(\n                    orig,\n                    path.join(distDir, 'server', updatedRelativeDest)\n                  )\n\n                  // since the app router not found is prioritized over pages router,\n                  // we have to ensure the app router entries are available for all locales\n                  if (i18n) {\n                    for (const locale of i18n.locales) {\n                      const curPath = `/${locale}/404`\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                  }\n\n                  pagesManifest['/404'] = updatedRelativeDest\n                }\n              })\n          }\n\n          // If there's /not-found inside app, we prefer it over the pages 404\n          if (hasStaticApp404) {\n            await moveExportedAppNotFoundTo404()\n          } else {\n            // Only move /404 to /404 when there is no custom 404 as in that case we don't know about the 404 page\n            if (!hasPages404 && !hasApp404 && useStaticPages404) {\n              await moveExportedPage('/_error', '/404', '/404', false, 'html')\n            }\n          }\n\n          if (useDefaultStatic500) {\n            await moveExportedPage('/_error', '/500', '/500', false, 'html')\n          }\n\n          for (const page of combinedPages) {\n            const isSsg = ssgPages.has(page)\n            const isStaticSsgFallback = ssgStaticFallbackPages.has(page)\n            const isDynamic = isDynamicRoute(page)\n            const hasAmp = hybridAmpPages.has(page)\n            const file = normalizePagePath(page)\n\n            const pageInfo = pageInfos.get(page)\n            const durationInfo = exportResult.byPage.get(page)\n            if (pageInfo && durationInfo) {\n              // Set Build Duration\n              if (pageInfo.ssgPageRoutes) {\n                pageInfo.ssgPageDurations = pageInfo.ssgPageRoutes.map(\n                  (pagePath) => {\n                    const duration = durationInfo.durationsByPath.get(pagePath)\n                    if (typeof duration === 'undefined') {\n                      throw new Error(\"Invariant: page wasn't built\")\n                    }\n\n                    return duration\n                  }\n                )\n              }\n              pageInfo.pageDuration = durationInfo.durationsByPath.get(page)\n            }\n\n            // The dynamic version of SSG pages are only prerendered if the\n            // fallback is enabled. Below, we handle the specific prerenders\n            // of these.\n            const hasHtmlOutput = !(isSsg && isDynamic && !isStaticSsgFallback)\n\n            if (hasHtmlOutput) {\n              await moveExportedPage(page, page, file, isSsg, 'html')\n            }\n\n            if (hasAmp && (!isSsg || (isSsg && !isDynamic))) {\n              const ampPage = `${file}.amp`\n              await moveExportedPage(page, ampPage, ampPage, isSsg, 'html')\n\n              if (isSsg) {\n                await moveExportedPage(page, ampPage, ampPage, isSsg, 'json')\n              }\n            }\n\n            if (isSsg) {\n              // For a non-dynamic SSG page, we must copy its data file\n              // from export, we already moved the HTML file above\n              if (!isDynamic) {\n                await moveExportedPage(page, page, file, isSsg, 'json')\n\n                if (i18n) {\n                  // TODO: do we want to show all locale variants in build output\n                  for (const locale of i18n.locales) {\n                    const localePage = `/${locale}${page === '/' ? '' : page}`\n\n                    const cacheControl = getCacheControl(localePage)\n\n                    prerenderManifest.routes[localePage] = {\n                      initialRevalidateSeconds: cacheControl.revalidate,\n                      initialExpireSeconds: cacheControl.expire,\n                      experimentalPPR: undefined,\n                      renderingMode: undefined,\n                      srcRoute: null,\n                      dataRoute: path.posix.join(\n                        '/_next/data',\n                        buildId,\n                        `${file}.json`\n                      ),\n                      prefetchDataRoute: undefined,\n                      allowHeader: ALLOWED_HEADERS,\n                    }\n                  }\n                } else {\n                  const cacheControl = getCacheControl(page)\n\n                  prerenderManifest.routes[page] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: null,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${file}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n                }\n                if (pageInfo) {\n                  pageInfo.initialCacheControl = getCacheControl(page)\n                }\n              } else {\n                // For a dynamic SSG page, we did not copy its data exports and only\n                // copy the fallback HTML file (if present).\n                // We must also copy specific versions of this page as defined by\n                // `getStaticPaths` (additionalSsgPaths).\n                for (const route of additionalPaths.get(page) ?? []) {\n                  const pageFile = normalizePagePath(route.pathname)\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'html',\n                    true\n                  )\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'json',\n                    true\n                  )\n\n                  if (hasAmp) {\n                    const ampPage = `${pageFile}.amp`\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'html',\n                      true\n                    )\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'json',\n                      true\n                    )\n                  }\n\n                  const cacheControl = getCacheControl(route.pathname)\n\n                  prerenderManifest.routes[route.pathname] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: page,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${normalizePagePath(route.pathname)}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n\n                  if (pageInfo) {\n                    pageInfo.initialCacheControl = cacheControl\n                  }\n                }\n              }\n            }\n          }\n\n          // remove temporary export folder\n          await fs.rm(outdir, { recursive: true, force: true })\n          await writeManifest(pagesManifestPath, pagesManifest)\n        })\n\n        // As we may have modified the dynamicRoutes, we need to sort the\n        // dynamic routes by page.\n        routesManifest.dynamicRoutes = sortSortableRouteObjects(\n          dynamicRoutes,\n          (route) => ({\n            // If the route is PPR enabled, and has an associated source page,\n            // use it. Otherwise fallback to the page which should be the same.\n            sourcePage: sourcePages.get(route.page) ?? route.page,\n            page: route.page,\n          })\n        )\n\n        // Now write the routes manifest out.\n        await nextBuildSpan\n          .traceChild('write-routes-manifest')\n          .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n      }\n\n      const postBuildSpinner = createSpinner('Finalizing page optimization')\n      let buildTracesSpinner = createSpinner(`Collecting build traces`)\n\n      // ensure the worker is not left hanging\n      worker.end()\n\n      const analysisEnd = process.hrtime(analysisBegin)\n      telemetry.record(\n        eventBuildOptimize(pagesPaths, {\n          durationInSeconds: analysisEnd[0],\n          staticPageCount: staticPages.size,\n          staticPropsPageCount: ssgPages.size,\n          serverPropsPageCount: serverPropsPages.size,\n          ssrPageCount:\n            pagesPaths.length -\n            (staticPages.size + ssgPages.size + serverPropsPages.size),\n          hasStatic404: useStaticPages404,\n          hasReportWebVitals:\n            namedExports?.includes('reportWebVitals') ?? false,\n          rewritesCount: combinedRewrites.length,\n          headersCount: headers.length,\n          redirectsCount: redirects.length - 1, // reduce one for trailing slash\n          headersWithHasCount: headers.filter((r: any) => !!r.has).length,\n          rewritesWithHasCount: combinedRewrites.filter((r: any) => !!r.has)\n            .length,\n          redirectsWithHasCount: redirects.filter((r: any) => !!r.has).length,\n          middlewareCount: hasMiddlewareFile ? 1 : 0,\n          totalAppPagesCount,\n          staticAppPagesCount,\n          serverAppPagesCount,\n          edgeRuntimeAppCount,\n          edgeRuntimePagesCount,\n        })\n      )\n\n      if (NextBuildContext.telemetryState) {\n        const events = eventBuildFeatureUsage(\n          NextBuildContext.telemetryState.usages\n        )\n        telemetry.record(events)\n        telemetry.record(\n          eventPackageUsedInGetServerSideProps(\n            NextBuildContext.telemetryState.packagesUsedInServerSideProps\n          )\n        )\n        const useCacheTracker = NextBuildContext.telemetryState.useCacheTracker\n\n        for (const [key, value] of Object.entries(useCacheTracker)) {\n          telemetry.record(\n            eventBuildFeatureUsage([\n              {\n                featureName: key as UseCacheTrackerKey,\n                invocationCount: value,\n              },\n            ])\n          )\n        }\n      }\n\n      if (ssgPages.size > 0 || appDir) {\n        tbdPrerenderRoutes.forEach((tbdRoute) => {\n          const normalizedRoute = normalizePagePath(tbdRoute)\n          const dataRoute = path.posix.join(\n            '/_next/data',\n            buildId,\n            `${normalizedRoute}.json`\n          )\n\n          prerenderManifest.dynamicRoutes[tbdRoute] = {\n            routeRegex: normalizeRouteRegex(\n              getNamedRouteRegex(tbdRoute, {\n                prefixRouteKeys: false,\n              }).re.source\n            ),\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            dataRoute,\n            fallback: ssgBlockingFallbackPages.has(tbdRoute)\n              ? null\n              : ssgStaticFallbackPages.has(tbdRoute)\n                ? `${normalizedRoute}.html`\n                : false,\n            fallbackRevalidate: undefined,\n            fallbackExpire: undefined,\n            fallbackSourceRoute: undefined,\n            fallbackRootParams: undefined,\n            dataRouteRegex: normalizeRouteRegex(\n              getNamedRouteRegex(dataRoute, {\n                prefixRouteKeys: true,\n                includeSuffix: true,\n                excludeOptionalTrailingSlash: true,\n              }).re.source\n            ),\n            // Pages does not have a prefetch data route.\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            allowHeader: ALLOWED_HEADERS,\n          }\n        })\n\n        NextBuildContext.previewModeId = previewProps.previewModeId\n        NextBuildContext.fetchCacheKeyPrefix =\n          config.experimental.fetchCacheKeyPrefix\n        NextBuildContext.allowedRevalidateHeaderKeys =\n          config.experimental.allowedRevalidateHeaderKeys\n\n        await writePrerenderManifest(distDir, prerenderManifest)\n        await writeClientSsgManifest(prerenderManifest, {\n          distDir,\n          buildId,\n          locales: config.i18n?.locales,\n        })\n      } else {\n        await writePrerenderManifest(distDir, {\n          version: 4,\n          routes: {},\n          dynamicRoutes: {},\n          preview: previewProps,\n          notFoundRoutes: [],\n        })\n      }\n\n      await writeImagesManifest(distDir, config)\n      await writeManifest(path.join(distDir, EXPORT_MARKER), {\n        version: 1,\n        hasExportPathMap: typeof config.exportPathMap === 'function',\n        exportTrailingSlash: config.trailingSlash === true,\n        isNextImageImported: isNextImageImported === true,\n      })\n      await fs.unlink(path.join(distDir, EXPORT_DETAIL)).catch((err) => {\n        if (err.code === 'ENOENT') {\n          return Promise.resolve()\n        }\n        return Promise.reject(err)\n      })\n\n      if (Boolean(config.experimental.nextScriptWorkers)) {\n        await nextBuildSpan\n          .traceChild('verify-partytown-setup')\n          .traceAsyncFn(async () => {\n            await verifyPartytownSetup(\n              dir,\n              path.join(distDir, CLIENT_STATIC_FILES_PATH)\n            )\n          })\n      }\n\n      await buildTracesPromise\n\n      if (buildTracesSpinner) {\n        buildTracesSpinner.stopAndPersist()\n        buildTracesSpinner = undefined\n      }\n\n      if (isCompileMode) {\n        Log.info(\n          `Build ran with \"compile\" mode, to finalize the build run either \"generate\" or \"generate-env\" mode as well`\n        )\n      }\n\n      if (config.output === 'export') {\n        await writeFullyStaticExport(\n          config,\n          dir,\n          enabledDirectories,\n          configOutDir,\n          nextBuildSpan\n        )\n      }\n\n      if (config.experimental.adapterPath) {\n        await handleBuildComplete({\n          dir,\n          distDir,\n          tracingRoot: outputFileTracingRoot,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          adapterPath: config.experimental.adapterPath,\n          pageKeys: pageKeys.pages,\n          appPageKeys: denormalizedAppPages,\n          routesManifest,\n          prerenderManifest,\n          middlewareManifest,\n          functionsConfigManifest,\n          hasStatic404: useStaticPages404,\n          requiredServerFiles: requiredServerFilesManifest.files,\n        })\n      }\n\n      if (config.output === 'standalone') {\n        await writeStandaloneDirectory(\n          nextBuildSpan,\n          distDir,\n          pageKeys,\n          denormalizedAppPages,\n          outputFileTracingRoot,\n          requiredServerFilesManifest,\n          middlewareManifest,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          staticPages,\n          loadedEnvFiles,\n          appDir\n        )\n      }\n\n      if (postBuildSpinner) postBuildSpinner.stopAndPersist()\n      console.log()\n\n      if (debugOutput) {\n        nextBuildSpan\n          .traceChild('print-custom-routes')\n          .traceFn(() => printCustomRoutes({ redirects, rewrites, headers }))\n      }\n\n      await nextBuildSpan.traceChild('print-tree-view').traceAsyncFn(() =>\n        printTreeView(pageKeys, pageInfos, {\n          distPath: distDir,\n          buildId: buildId,\n          pagesDir,\n          useStaticPages404,\n          pageExtensions: config.pageExtensions,\n          appBuildManifest,\n          buildManifest,\n          middlewareManifest,\n          gzipSize: config.experimental.gzipSize,\n        })\n      )\n\n      await nextBuildSpan\n        .traceChild('telemetry-flush')\n        .traceAsyncFn(() => telemetry.flush())\n\n      await shutdownPromise\n    })\n  } catch (e) {\n    const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n    if (telemetry) {\n      telemetry.record(\n        eventBuildFailed({\n          bundler: getBundlerForTelemetry(isTurbopack),\n          errorCode: getErrorCodeForTelemetry(e),\n          durationInSeconds: Math.floor((Date.now() - buildStartTime) / 1000),\n        })\n      )\n    }\n    throw e\n  } finally {\n    // Ensure we wait for lockfile patching if present\n    await lockfilePatchPromise.cur\n\n    // Ensure all traces are flushed before finishing the command\n    await flushAllTraces()\n    teardownTraceSubscriber()\n\n    if (traceUploadUrl && loadedConfig) {\n      uploadTrace({\n        traceUploadUrl,\n        mode: 'build',\n        projectDir: dir,\n        distDir: loadedConfig.distDir,\n        isTurboSession: isTurbopack,\n        sync: true,\n      })\n    }\n  }\n}\n\nfunction errorFromUnsupportedSegmentConfig(): never {\n  Log.error(\n    `Invalid segment configuration export detected. This can cause unexpected behavior from the configs not being applied. You should see the relevant failures in the logs above. Please fix them to continue.`\n  )\n  process.exit(1)\n}\n\nfunction getBundlerForTelemetry(isTurbopack: boolean) {\n  if (isTurbopack) {\n    return 'turbopack'\n  }\n\n  if (process.env.NEXT_RSPACK) {\n    return 'rspack'\n  }\n\n  return 'webpack'\n}\n\nfunction getErrorCodeForTelemetry(err: unknown) {\n  const code = extractNextErrorCode(err)\n  if (code != null) {\n    return code\n  }\n\n  if (err instanceof Error && 'code' in err && typeof err.code === 'string') {\n    return err.code\n  }\n\n  if (err instanceof Error) {\n    return err.name\n  }\n\n  return 'Unknown'\n}\n"], "names": ["createStaticWorker", "build", "ALLOWED_HEADERS", "MATCHED_PATH_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "pageToRoute", "page", "sourcePage", "routeRegex", "getNamedRouteRegex", "prefixRouteKeys", "regex", "normalizeRouteRegex", "re", "source", "routeKeys", "namedRegex", "getCacheDir", "distDir", "cacheDir", "path", "join", "ciEnvironment", "isCI", "hasNextSupport", "<PERSON><PERSON><PERSON>", "existsSync", "console", "log", "Log", "prefixes", "warn", "writeFileUtf8", "filePath", "content", "fs", "writeFile", "readFileUtf8", "readFile", "writeManifest", "manifest", "formatManifest", "readManifest", "JSON", "parse", "writePrerenderManifest", "PRERENDER_MANIFEST", "writeClientSsgManifest", "prerenderManifest", "buildId", "locales", "ssgPages", "Set", "Object", "entries", "routes", "filter", "srcRoute", "map", "route", "normalizeLocalePath", "pathname", "keys", "dynamicRoutes", "sort", "clientSsgManifestContent", "devalue", "CLIENT_STATIC_FILES_PATH", "writeFunctionsConfigManifest", "SERVER_DIRECTORY", "FUNCTIONS_CONFIG_MANIFEST", "writeRequiredServerFilesManifest", "requiredServerFiles", "SERVER_FILES_MANIFEST", "writeImagesManifest", "config", "images", "deviceSizes", "imageSizes", "sizes", "remotePatterns", "p", "protocol", "replace", "hostname", "makeRe", "port", "dot", "search", "localPatterns", "IMAGES_MANIFEST", "version", "STANDALONE_DIRECTORY", "writeStandaloneDirectory", "nextBuildSpan", "pageKeys", "denormalizedAppPages", "outputFileTracingRoot", "middlewareManifest", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "loadedEnvFiles", "appDir", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "copyTracedFiles", "pages", "file", "files", "reduce", "acc", "envFile", "includes", "push", "outputPath", "relative", "mkdir", "dirname", "recursive", "copyFile", "middlewareOutput", "recursiveCopy", "overwrite", "originalServerApp", "getNumberOfWorkers", "experimental", "cpus", "defaultConfig", "memoryBasedWorkersCount", "Math", "max", "min", "floor", "os", "freemem", "staticWorkerPath", "require", "resolve", "staticWorkerExposedMethods", "options", "debuggerPortOffset", "progress", "Worker", "logger", "numWorkers", "onActivity", "run", "onActivityAbort", "clear", "enableSourceMaps", "enablePrerenderSourceMaps", "isolated<PERSON><PERSON><PERSON>", "enableWorkerThreads", "workerThreads", "exposedMethods", "writeFullyStaticExport", "dir", "enabledDirectories", "configOutDir", "exportApp", "default", "buildExport", "nextConfig", "silent", "outdir", "getBuildId", "isGenerateMode", "generateBuildId", "nanoid", "reactProductionProfiling", "debugOutput", "debugPrerender", "runLint", "noMangling", "appDirOnly", "isTurbopack", "experimentalBuildMode", "traceUploadUrl", "isCompileMode", "NextBuildContext", "buildStartTime", "Date", "now", "loadedConfig", "trace", "undefined", "buildMode", "isTurboBuild", "String", "process", "env", "__NEXT_VERSION", "mappedPages", "traceFn", "loadEnvConfig", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "turborepoTraceAccess", "loadConfig", "PHASE_PRODUCTION_BUILD", "NEXT_DEPLOYMENT_ID", "deploymentId", "hasCustomExportOutput", "setGlobal", "exit", "info", "inlineStaticEnv", "flushAllTraces", "teardownTraceSubscriber", "populateStaticEnv", "customRoutes", "loadCustomRoutes", "headers", "rewrites", "redirects", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "hasRewrites", "length", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "telemetry", "Telemetry", "publicDir", "pagesDir", "findPagesDir", "app", "<PERSON><PERSON><PERSON>", "generateEncryptionKeyBase64", "isBuild", "isSrcDir", "startsWith", "hasPublicDir", "record", "eventCliSession", "webpackVersion", "cliCommand", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "isCustomServer", "turboFlag", "eventNextPlugins", "then", "events", "eventSwcPlugins", "envInfo", "experimentalFeatures", "getStartServerInfo", "dev", "logStartInfo", "networkUrl", "appUrl", "ignoreESLint", "Boolean", "eslint", "ignoreDuringBuilds", "shouldLint", "typeCheckingOptions", "distDirCreated", "err", "isError", "code", "isWriteable", "Error", "cleanDistDir", "recursiveDelete", "error", "flush", "buildLintEvent", "featureName", "invocationCount", "eventName", "EVENT_BUILD_FEATURE_USAGE", "payload", "validFile<PERSON><PERSON><PERSON>", "createValidFileMatcher", "pageExtensions", "providedPagePaths", "NEXT_PRIVATE_PAGE_PATHS", "pagesPaths", "collectPagesFiles", "middlewareDetectionRegExp", "RegExp", "MIDDLEWARE_FILENAME", "instrumentationHookDetectionRegExp", "INSTRUMENTATION_HOOK_FILENAME", "rootDir", "rootPaths", "Array", "from", "getFilesInDir", "some", "include", "test", "sortByPageExts", "hasMiddlewareFile", "previewProps", "generatePreviewKeys", "createPagesMapping", "isDev", "pagesType", "PAGE_TYPES", "PAGES", "pagePaths", "mappedAppPages", "mappedAppLayouts", "providedAppPaths", "NEXT_PRIVATE_APP_PATHS", "appPaths", "layoutPaths", "result", "collectAppFiles", "APP", "mappedRootPaths", "ROOT", "pagesPageKeys", "conflictingAppPagePaths", "appPageKeys", "appKey", "normalizedAppPageKey", "normalizeAppPath", "pagePath", "appPath", "add", "generateInterceptionRoutesRewrites", "basePath", "totalAppPagesCount", "routeTypesFilePath", "validatorFilePath", "appRoutes", "appRouteHandlers", "layoutRoutes", "slots", "pageRoutes", "pageApiRoutes", "processPageRoutes", "slotsFromPages", "extractSlotsFromAppRoutes", "slotsFromDefaults", "defaultPaths", "mappedDefaultFiles", "extractSlotsFromDefaultFiles", "combineSlots", "processAppRoutes", "processLayoutRoutes", "routeTypesManifest", "createRouteTypesManifest", "writeRouteTypesManifest", "writeValidatorFile", "numConflictingAppPaths", "conflictingPublicFiles", "hasPages404", "PAGES_DIR_ALIAS", "hasApp404", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "hasCustomErrorPage", "hasPublicUnderScoreNextDir", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "hasPublicPageFile", "fileExists", "FileType", "File", "numConflicting", "nestedReservedPages", "match", "restrictedRedirectPaths", "isAppCacheComponentsEnabled", "cacheComponents", "isAuthInterruptsEnabled", "authInterrupts", "isAppPPREnabled", "checkIsAppPPREnabled", "ppr", "routesManifestPath", "ROUTES_MANIFEST", "sourcePages", "Map", "routesManifest", "sortedRoutes", "sortPages", "staticRoutes", "isDynamicRoute", "isReservedPage", "pages404", "caseSensitive", "caseSensitiveRoutes", "r", "buildCustomRoute", "dataRoutes", "i18n", "rsc", "header", "RSC_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "prefetch<PERSON><PERSON><PERSON>", "didPostponeHeader", "NEXT_DID_POSTPONE_HEADER", "contentTypeHeader", "RSC_CONTENT_TYPE_HEADER", "suffix", "RSC_SUFFIX", "prefetchSuffix", "RSC_PREFETCH_SUFFIX", "prefetchSegmentHeader", "prefetchSegmentSuffix", "RSC_SEGMENT_SUFFIX", "prefetchSegmentDirSuffix", "RSC_SEGMENTS_DIR_SUFFIX", "rewriteHeaders", "pathHeader", "NEXT_REWRITTEN_PATH_HEADER", "query<PERSON>eader", "NEXT_REWRITTEN_QUERY_HEADER", "skipMiddlewareUrlNormalize", "chain", "NEXT_RESUME_HEADER", "startTypeChecking", "clientRouterFilters", "clientRouterFilter", "nonInternalRedirects", "internal", "createClientRouterFilter", "clientRouterFilterRedirects", "clientRouterFilterAllowedRate", "recordFrameworkVersion", "updateBuildDiagnostics", "buildStage", "pagesManifestPath", "PAGES_MANIFEST", "buildTraceContext", "buildTracesPromise", "useBuildWorker", "webpackBuildWorker", "webpack", "runServerAndEdgeInParallel", "parallelServerCompiles", "collectServerBuildTracesInParallel", "parallelServerBuildTraces", "setAttribute", "traceMemoryUsage", "buildOptions", "shutdownPromise", "Promise", "duration", "compilerDuration", "rest", "turbopackBuild", "NEXT_TURBOPACK_USE_WORKER", "durationString", "durationToString", "event", "eventBuildCompleted", "bundler", "durationInSeconds", "round", "serverBuildPromise", "webpackBuild", "res", "buildTraceWorker", "collectBuildTraces", "edgeRuntimeRoutes", "collectRoutesUsingEdgeRuntime", "hasSsrAmpPages", "catch", "edgeBuildPromise", "getBundlerForTelemetry", "runAfterProductionCompile", "buildSpan", "metadata", "projectDir", "postCompileSpinner", "createSpinner", "buildManifestPath", "BUILD_MANIFEST", "appBuildManifestPath", "APP_BUILD_MANIFEST", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "ssgStaticFallbackPages", "ssgBlockingFallbackPages", "invalidPages", "hybridAmpPages", "serverPropsPages", "additionalPaths", "staticPaths", "appNormalizedPaths", "fallbackModes", "appDefaultConfigs", "pageInfos", "pagesManifest", "buildManifest", "appBuildManifest", "appPathRoutes", "appPathsManifest", "APP_PATHS_MANIFEST", "key", "APP_PATH_ROUTES_MANIFEST", "NEXT_PHASE", "worker", "analysisBegin", "hrtime", "staticCheckSpan", "functionsConfigManifest", "functions", "customAppGetInitialProps", "namedExports", "isNextImageImported", "hasNonStaticErrorPage", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "runtimeEnvConfig", "sriEnabled", "sri", "algorithm", "nonStaticErrorPageSpan", "errorPageHasCustomGetInitialProps", "hasCustomGetInitialProps", "checkingApp", "errorPageStaticResult", "isPageStatic", "httpAgentOptions", "defaultLocale", "nextConfigOutput", "output", "pprConfig", "cacheLifeProfiles", "cacheLife", "appPageToCheck", "customAppGetInitialPropsPromise", "namedExportsPromise", "getDefinedNamedExports", "computedManifestData", "computeFromManifest", "gzipSize", "MIDDLEWARE_MANIFEST", "actionManifest", "SERVER_REFERENCE_MANIFEST", "entriesWithAction", "id", "node", "entry", "workers", "edge", "all", "pageType", "checkPageSpan", "actualPage", "normalizePagePath", "size", "totalSize", "getJsPageSizeInKb", "isRoutePPREnabled", "isSSG", "isStatic", "isServerComponent", "isHybridAmp", "ssgPageRoutes", "find", "normalizePathSep", "originalAppPath", "originalPath", "normalizedPath", "pageFilePath", "isAppBuiltinNotFoundPage", "isInsideAppDir", "staticInfo", "getStaticInfoIncludingLayouts", "hadUnsupportedValue", "errorFromUnsupportedSegmentConfig", "runtime", "maxDuration", "preferredRegion", "regions", "pageRuntime", "RSC_MODULE_TYPES", "client", "edgeInfo", "isEdgeRuntime", "manifest<PERSON>ey", "isPageStaticSpan", "workerResult", "parentId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "isrFlushToDisk", "maxMemoryCacheSize", "cacheMaxMemorySize", "set", "warnOnce", "isDynamic", "prerenderedRoutes", "appConfig", "revalidate", "hasGenerateStaticParams", "params", "encodedPathname", "fallbackRouteParams", "fallbackMode", "prerenderFallbackMode", "fallbackRootParams", "throwOnEmptyStaticShell", "dynamic", "hasStaticProps", "isAmpOnly", "FallbackMode", "BLOCKING_STATIC_RENDER", "PRERENDER", "hasServerProps", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "delete", "STATIC_STATUS_PAGES", "message", "initialCacheControl", "pageDuration", "ssgPageDurations", "hasEmptyStaticShell", "errorPageResult", "nonStaticErrorPage", "returnValue", "stopAndPersist", "bold", "yellow", "instrumentationHookEntryFiles", "requiredServerFilesManifest", "normalizedCacheHandlers", "value", "serverFilesManifest", "configFile", "compress", "trustHostHeader", "isExperimentalCompile", "relativeAppDir", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "REACT_LOADABLE_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "DYNAMIC_CSS_MANIFEST", "BUILD_ID_FILE", "NEXT_FONT_MANIFEST", "nonNullable", "ignore", "middlewareFile", "matchers", "middleware", "regexp", "originalSource", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "buildDataRoute", "useStaticPages404", "pg", "writeBuildId", "optimizeCss", "globOrig", "cssFilePaths", "reject", "features", "nextScriptWorkers", "isPersistentCachingEnabled", "feature", "notFoundRoutes", "preview", "tbdPrerenderRoutes", "usedStaticStatusPages", "for<PERSON>ach", "has", "hasPages500", "useDefaultStatic500", "combinedPages", "isApp404Static", "hasStaticApp404", "staticGenerationSpan", "detectConflictingPaths", "exportConfig", "exportPathMap", "defaultMap", "_pagesFallback", "_ssgPath", "get", "isDynamicError", "checkIsRoutePPREnabled", "_fallbackRouteParams", "_isDynamicError", "_isAppDir", "_isRoutePPREnabled", "_allowEmptyStaticShell", "isSsg", "<PERSON><PERSON><PERSON><PERSON>", "locale", "_locale", "exportResult", "statusMessage", "getFallbackMode", "by<PERSON><PERSON>", "NOT_FOUND", "getCacheControl", "exportPath", "defaultRevalidate", "cacheControl", "expire", "expireTime", "NEXT_SSG_FETCH_METRICS", "recordFetchMetrics", "writeTurborepoAccessTraceResult", "traces", "turborepoAccessTraceResults", "values", "ssgNotFoundPaths", "serverBundle", "getPagePath", "unlink", "InvariantError", "hasRevalidateZero", "isAppRouteHandler", "isAppRouteRoute", "htmlBotsRegexString", "htmlLimitedBots", "HTML_LIMITED_BOT_UA_RE_STRING", "bypassFor", "type", "ACTION_HEADER", "staticPrerenderedRoutes", "dynamicPrerenderedRoutes", "unsortedUnknownPrerenderRoutes", "unsortedKnownPrerenderRoutes", "prerenderedRoute", "sortedUnknownPrerenderRoutes", "sortPageObjects", "sortedKnownPrerenderRoutes", "hasPostponed", "normalizedRoute", "dataRoute", "posix", "prefetchDataRoute", "meta", "collectMeta", "status", "UNDERSCORE_NOT_FOUND_ROUTE", "initialStatus", "initialHeaders", "renderingMode", "RenderingMode", "PARTIALLY_STATIC", "STATIC", "experimentalPPR", "experimentalBypassFor", "initialRevalidateSeconds", "initialExpireSeconds", "allow<PERSON>eader", "dynamicRoute", "skipInternalRouting", "segmentPaths", "pageSegmentPath", "item", "endsWith", "builtSegmentDataRoute", "buildPrefetchSegmentDataRoute", "destination", "prefetchSegmentDataRoutes", "hasFallbackRootParams", "buildInversePrefetchSegmentDataRoute", "isDynamicAppRoute", "fallbackCacheControl", "fallbackModeToFallbackField", "fallbackRevalidate", "fallbackExpire", "fallback<PERSON><PERSON><PERSON>", "fallbackHeaders", "fallbackSourceRoute", "dataRouteRegex", "includeSuffix", "excludeOptionalTrailingSlash", "prefetchDataRouteRegex", "moveExportedPage", "originPage", "ext", "additionalSsgFile", "orig", "relativeDest", "slice", "split", "dest", "isNotFound", "rename", "localeExt", "extname", "relativeDestNoPages", "curPath", "updatedRelativeDest", "updatedOrig", "updatedDest", "moveExportedAppNotFoundTo404", "isStaticSsgFallback", "hasAmp", "pageInfo", "durationInfo", "byPage", "durationsByPath", "hasHtmlOutput", "ampPage", "localePage", "pageFile", "rm", "force", "sortSortableRouteObjects", "postBuildSpinner", "buildTracesSpinner", "end", "analysisEnd", "eventBuildOptimize", "staticPageCount", "staticPropsPageCount", "serverPropsPageCount", "ssrPageCount", "hasStatic404", "hasReportWebVitals", "rewritesCount", "headersCount", "redirectsCount", "headersWithHasCount", "rewritesWithHasCount", "redirectsWithHasCount", "middlewareCount", "telemetryState", "eventBuildFeatureUsage", "usages", "eventPackageUsedInGetServerSideProps", "packagesUsedInServerSideProps", "useCacheTracker", "tbdRoute", "previewModeId", "fetchCacheKeyPrefix", "allowedRevalidateHeaderKeys", "EXPORT_MARKER", "hasExportPathMap", "exportTrailingSlash", "trailingSlash", "EXPORT_DETAIL", "verifyPartytownSetup", "adapterPath", "handleBuildComplete", "tracingRoot", "printCustomRoutes", "printTreeView", "distPath", "e", "traceGlobals", "eventBuildFailed", "errorCode", "getErrorCodeForTelemetry", "lockfilePatchPromise", "cur", "uploadTrace", "mode", "isTurboSession", "sync", "NEXT_RSPACK", "extractNextErrorCode", "name"], "mappings": ";;;;;;;;;;;;;;;IAuyBgBA,kBAAkB;eAAlBA;;IAmEhB,OA6lGC;eA7lG6BC;;;QAn2BvB;qBAE4C;4BACtB;2BACN;oBACoB;2DAC5B;wBACQ;8BACO;gEACV;+DACD;0BACI;6DACN;2BAiBV;4BAC8B;8BACR;0EAGtB;6BAQqB;iCACI;sCACK;4BA+B9B;uBACwB;+DAER;mCAEW;yBACN;gEACG;sCAKxB;wBAWA;yBAEmB;yBAenB;2BACoB;iCACK;6BACJ;6DACP;gEACK;uBACkC;wBAYrD;8BAIsB;qCACO;gEAChB;+BAEU;+BACA;qBACgC;4BAC3B;+BACL;4BACE;0BACC;kCAW1B;8BACsB;8BACsB;kCAClB;iCACD;0CACS;8BACF;2BACL;oDACiB;gCAEpB;oCACI;gCAEJ;kCAKxB;4BAC0C;wBAEX;kCACL;wBACA;uCACW;oEAEpB;qBAIjB;0BACmD;+BAC5B;gCACC;uBACe;+CAMvC;gCAEwB;wBACY;iCACX;2BACE;kCACD;wBACJ;qCACQ;wCACK;iCACN;+BACA;gCAK7B;0BACe;iCAKf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HP;;;CAGC,GACD,MAAMC,kBAA4B;IAChC;IACAC,8BAAmB;IACnBC,sCAA2B;IAC3BC,qDAA0C;IAC1CC,6CAAkC;IAClCC,iDAAsC;CACvC;AA+ID,SAASC,YACPC,IAAY,EACZC,UAAmB;IAEnB,MAAMC,aAAaC,IAAAA,8BAAkB,EAACH,MAAM;QAC1CI,iBAAiB;IACnB;IACA,OAAO;QACLH;QACAD;QACAK,OAAOC,IAAAA,qCAAmB,EAACJ,WAAWK,EAAE,CAACC,MAAM;QAC/CC,WAAWP,WAAWO,SAAS;QAC/BC,YAAYR,WAAWQ,UAAU;IACnC;AACF;AAEA,SAASC,YAAYC,OAAe;IAClC,MAAMC,WAAWC,aAAI,CAACC,IAAI,CAACH,SAAS;IACpC,IAAII,QAAcC,IAAI,IAAI,CAACD,QAAcE,cAAc,EAAE;QACvD,MAAMC,WAAWC,IAAAA,cAAU,EAACP;QAE5B,IAAI,CAACM,UAAU;YACb,kGAAkG;YAClG,sBAAsB;YACtBE,QAAQC,GAAG,CACT,GAAGC,KAAIC,QAAQ,CAACC,IAAI,CAAC,+HAA+H,CAAC;QAEzJ;IACF;IACA,OAAOZ;AACT;AAEA,eAAea,cAAcC,QAAgB,EAAEC,OAAe;IAC5D,MAAMC,YAAE,CAACC,SAAS,CAACH,UAAUC,SAAS;AACxC;AAEA,SAASG,aAAaJ,QAAgB;IACpC,OAAOE,YAAE,CAACG,QAAQ,CAACL,UAAU;AAC/B;AAEA,eAAeM,cACbN,QAAgB,EAChBO,QAAW;IAEX,MAAMR,cAAcC,UAAUQ,IAAAA,8BAAc,EAACD;AAC/C;AAEA,eAAeE,aAA+BT,QAAgB;IAC5D,OAAOU,KAAKC,KAAK,CAAC,MAAMP,aAAaJ;AACvC;AAEA,eAAeY,uBACb3B,OAAe,EACfsB,QAAyC;IAEzC,MAAMD,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAAS4B,8BAAkB,GAAGN;AAC9D;AAEA,eAAeO,uBACbC,iBAAkD,EAClD,EACEC,OAAO,EACP/B,OAAO,EACPgC,OAAO,EAKR;IAED,MAAMC,WAAW,IAAIC,IACnB;WACKC,OAAOC,OAAO,CAACN,kBAAkBO,MAAM,CACxC,4BAA4B;SAC3BC,MAAM,CAAC,CAAC,GAAG,EAAEC,QAAQ,EAAE,CAAC,GAAKA,YAAY,MACzCC,GAAG,CAAC,CAAC,CAACC,MAAM,GAAKC,IAAAA,wCAAmB,EAACD,OAAOT,SAASW,QAAQ;WAC7DR,OAAOS,IAAI,CAACd,kBAAkBe,aAAa;KAC/C,CAACC,IAAI;IAGR,MAAMC,2BAA2B,CAAC,oBAAoB,EAAEC,IAAAA,gBAAO,EAC7Df,UACA,iDAAiD,CAAC;IAEpD,MAAMnB,cACJZ,aAAI,CAACC,IAAI,CAACH,SAASiD,oCAAwB,EAAElB,SAAS,oBACtDgB;AAEJ;AAoBA,eAAeG,6BACblD,OAAe,EACfsB,QAAiC;IAEjC,MAAMD,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEC,qCAAyB,GAC9D9B;AAEJ;AAWA,eAAe+B,iCACbrD,OAAe,EACfsD,mBAAgD;IAEhD,MAAMjC,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASuD,iCAAqB,GACxCD;AAEJ;AAEA,eAAeE,oBACbxD,OAAe,EACfyD,MAA0B;QAODA,gBAUrBA;IAfJ,MAAMC,SAAS;QAAE,GAAGD,OAAOC,MAAM;IAAC;IAClC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAE,GAAGF;IAClCA,OAAeG,KAAK,GAAG;WAAIF;WAAgBC;KAAW;IAExD,8DAA8D;IAC9DF,OAAOI,cAAc,GAAG,AAACL,CAAAA,CAAAA,2BAAAA,iBAAAA,OAAQC,MAAM,qBAAdD,eAAgBK,cAAc,KAAI,EAAE,AAAD,EAAGtB,GAAG,CAAC,CAACuB;YAExDA;eAF+D;YACzE,iEAAiE;YACjEC,QAAQ,GAAED,cAAAA,EAAEC,QAAQ,qBAAVD,YAAYE,OAAO,CAAC,MAAM;YACpCC,UAAUC,IAAAA,iBAAM,EAACJ,EAAEG,QAAQ,EAAEtE,MAAM;YACnCwE,MAAML,EAAEK,IAAI;YACZzB,UAAUwB,IAAAA,iBAAM,EAACJ,EAAEpB,QAAQ,IAAI,MAAM;gBAAE0B,KAAK;YAAK,GAAGzE,MAAM;YAC1D0E,QAAQP,EAAEO,MAAM;QAClB;;IAEA,oEAAoE;IACpE,IAAIb,2BAAAA,kBAAAA,OAAQC,MAAM,qBAAdD,gBAAgBc,aAAa,EAAE;QACjCb,OAAOa,aAAa,GAAGd,OAAOC,MAAM,CAACa,aAAa,CAAC/B,GAAG,CAAC,CAACuB,IAAO,CAAA;gBAC7D,gEAAgE;gBAChEpB,UAAUwB,IAAAA,iBAAM,EAACJ,EAAEpB,QAAQ,IAAI,MAAM;oBAAE0B,KAAK;gBAAK,GAAGzE,MAAM;gBAC1D0E,QAAQP,EAAEO,MAAM;YAClB,CAAA;IACF;IAEA,MAAMjD,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAASwE,2BAAe,GAAG;QACvDC,SAAS;QACTf;IACF;AACF;AAEA,MAAMgB,uBAAuB;AAC7B,eAAeC,yBACbC,aAAmB,EACnB5E,OAAe,EACf6E,QAAwD,EACxDC,oBAA0C,EAC1CC,qBAA6B,EAC7BzB,mBAAgD,EAChD0B,kBAAsC,EACtCC,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB,EACxBC,cAA8B,EAC9BC,MAA0B;IAE1B,MAAMT,cACHU,UAAU,CAAC,8BACXC,YAAY,CAAC;QACZ,MAAMC,IAAAA,uBAAe,EACnB,kFAAkF;QAClFlC,oBAAoB+B,MAAM,EAC1BrF,SACA6E,SAASY,KAAK,EACdX,sBACAC,uBACAzB,oBAAoBG,MAAM,EAC1BuB,oBACAC,mBACAC,wBACAC;QAGF,KAAK,MAAMO,QAAQ;eACdpC,oBAAoBqC,KAAK;YAC5BzF,aAAI,CAACC,IAAI,CAACmD,oBAAoBG,MAAM,CAACzD,OAAO,EAAEuD,iCAAqB;eAChE6B,eAAeQ,MAAM,CAAW,CAACC,KAAKC;gBACvC,IAAI;oBAAC;oBAAQ;iBAAkB,CAACC,QAAQ,CAACD,QAAQ5F,IAAI,GAAG;oBACtD2F,IAAIG,IAAI,CAACF,QAAQ5F,IAAI;gBACvB;gBACA,OAAO2F;YACT,GAAG,EAAE;SACN,CAAE;YACD,kFAAkF;YAClF,MAAM9E,WAAWb,aAAI,CAACC,IAAI,CAACmD,oBAAoB+B,MAAM,EAAEK;YACvD,MAAMO,aAAa/F,aAAI,CAACC,IAAI,CAC1BH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuBhE;YAEvC,MAAME,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAACH,aAAa;gBACvCI,WAAW;YACb;YACA,MAAMpF,YAAE,CAACqF,QAAQ,CAACvF,UAAUkF;QAC9B;QAEA,IAAIhB,mBAAmB;YACrB,MAAMsB,mBAAmBrG,aAAI,CAACC,IAAI,CAChCH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB/E,UACrCmD,4BAAgB,EAChB;YAGF,MAAMlC,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAACG,mBAAmB;gBAAEF,WAAW;YAAK;YACjE,MAAMpF,YAAE,CAACqF,QAAQ,CACfpG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE,kBACrCoD;QAEJ;QAEA,MAAMC,IAAAA,4BAAa,EACjBtG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE,UACrCjD,aAAI,CAACC,IAAI,CACPH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB/E,UACrCmD,4BAAgB,EAChB,UAEF;YAAEsD,WAAW;QAAK;QAEpB,IAAIpB,QAAQ;YACV,MAAMqB,oBAAoBxG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE;YAC/D,IAAI3C,IAAAA,cAAU,EAACkG,oBAAoB;gBACjC,MAAMF,IAAAA,4BAAa,EACjBE,mBACAxG,aAAI,CAACC,IAAI,CACPH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB/E,UACrCmD,4BAAgB,EAChB,QAEF;oBAAEsD,WAAW;gBAAK;YAEtB;QACF;IACF;AACJ;AAEA,SAASE,mBAAmBlD,MAA0B;IACpD,IACEA,OAAOmD,YAAY,CAACC,IAAI,IACxBpD,OAAOmD,YAAY,CAACC,IAAI,KAAKC,2BAAa,CAACF,YAAY,CAAEC,IAAI,EAC7D;QACA,OAAOpD,OAAOmD,YAAY,CAACC,IAAI;IACjC;IAEA,IAAIpD,OAAOmD,YAAY,CAACG,uBAAuB,EAAE;QAC/C,OAAOC,KAAKC,GAAG,CACbD,KAAKE,GAAG,CAACzD,OAAOmD,YAAY,CAACC,IAAI,IAAI,GAAGG,KAAKG,KAAK,CAACC,WAAE,CAACC,OAAO,KAAK,OAClE,iCAAiC;QACjC;IAEJ;IAEA,IAAI5D,OAAOmD,YAAY,CAACC,IAAI,EAAE;QAC5B,OAAOpD,OAAOmD,YAAY,CAACC,IAAI;IACjC;IAEA,qDAAqD;IACrD,OAAO;AACT;AAEA,MAAMS,mBAAmBC,QAAQC,OAAO,CAAC;AACzC,MAAMC,6BAA6B;IACjC;IACA;IACA;IACA;CACD;AAEM,SAAS9I,mBACd8E,MAA0B,EAC1BiE,OAMC;IAED,MAAM,EAAEC,kBAAkB,EAAEC,QAAQ,EAAE,GAAGF;IACzC,OAAO,IAAIG,cAAM,CAACP,kBAAkB;QAClCQ,QAAQnH;QACRoH,YAAYpB,mBAAmBlD;QAC/BuE,YAAY;YACVJ,4BAAAA,SAAUK,GAAG;QACf;QACAC,iBAAiB;YACfN,4BAAAA,SAAUO,KAAK;QACjB;QACAR;QACAS,kBAAkB3E,OAAOmD,YAAY,CAACyB,yBAAyB;QAC/D,kEAAkE;QAClEC,gBAAgB;QAChBC,qBAAqB9E,OAAOmD,YAAY,CAAC4B,aAAa;QACtDC,gBAAgBhB;IAClB;AACF;AAEA,eAAeiB,uBACbjF,MAA0B,EAC1BkF,GAAW,EACXC,kBAA0C,EAC1CC,YAAoB,EACpBjE,aAAmB;IAEnB,MAAMkE,YAAY,AAACvB,QAAQ,aACxBwB,OAAO;IAEV,MAAMD,UACJH,KACA;QACEK,aAAa;QACbC,YAAYxF;QACZmF;QACAM,QAAQ;QACRC,QAAQjJ,aAAI,CAACC,IAAI,CAACwI,KAAKE;QACvBd,YAAYpB,mBAAmBlD;IACjC,GACAmB;AAEJ;AAEA,eAAewE,WACbC,cAAuB,EACvBrJ,OAAe,EACf4E,aAAmB,EACnBnB,MAA0B;IAE1B,IAAI4F,gBAAgB;QAClB,OAAO,MAAMpI,YAAE,CAACG,QAAQ,CAAClB,aAAI,CAACC,IAAI,CAACH,SAAS,aAAa;IAC3D;IACA,OAAO,MAAM4E,cACVU,UAAU,CAAC,oBACXC,YAAY,CAAC,IAAM+D,IAAAA,gCAAe,EAAC7F,OAAO6F,eAAe,EAAEC,gBAAM;AACtE;AAEe,eAAe3K,MAC5B+J,GAAW,EACXa,2BAA2B,KAAK,EAChCC,cAAc,KAAK,EACnBC,iBAAiB,KAAK,EACtBC,UAAU,IAAI,EACdC,aAAa,KAAK,EAClBC,aAAa,KAAK,EAClBC,cAAc,KAAK,EACnBC,qBAA0E,EAC1EC,cAAkC;IAElC,MAAMC,gBAAgBF,0BAA0B;IAChD,MAAMV,iBAAiBU,0BAA0B;IACjDG,8BAAgB,CAACD,aAAa,GAAGA;IACjC,MAAME,iBAAiBC,KAAKC,GAAG;IAE/B,IAAIC;IACJ,IAAI;QACF,MAAM1F,gBAAgB2F,IAAAA,YAAK,EAAC,cAAcC,WAAW;YACnDC,WAAWV;YACXW,cAAcC,OAAOb;YACrBrF,SAASmG,QAAQC,GAAG,CAACC,cAAc;QACrC;QAEAZ,8BAAgB,CAACtF,aAAa,GAAGA;QACjCsF,8BAAgB,CAACvB,GAAG,GAAGA;QACvBuB,8BAAgB,CAACL,UAAU,GAAGA;QAC9BK,8BAAgB,CAACV,wBAAwB,GAAGA;QAC5CU,8BAAgB,CAACN,UAAU,GAAGA;QAC9BM,8BAAgB,CAACR,cAAc,GAAGA;QAElC,MAAM9E,cAAcW,YAAY,CAAC;gBA+dXwF;YA9dpB,4EAA4E;YAC5E,MAAM,EAAE3F,cAAc,EAAE,GAAGR,cACxBU,UAAU,CAAC,eACX0F,OAAO,CAAC,IAAMC,IAAAA,kBAAa,EAACtC,KAAK,OAAOhI;YAC3CuJ,8BAAgB,CAAC9E,cAAc,GAAGA;YAElC,MAAM8F,6BAA6B,IAAIC,gDAA0B;YACjE,MAAM1H,SAA6B,MAAMmB,cACtCU,UAAU,CAAC,oBACXC,YAAY,CAAC,IACZ6F,IAAAA,0CAAoB,EAClB,IACEC,IAAAA,eAAU,EAACC,kCAAsB,EAAE3C,KAAK;wBACtC,sCAAsC;wBACtCO,QAAQ;wBACRM;wBACAE;oBACF,IACFwB;YAGNZ,eAAe7G;YAEfmH,QAAQC,GAAG,CAACU,kBAAkB,GAAG9H,OAAO+H,YAAY,IAAI;YACxDtB,8BAAgB,CAACzG,MAAM,GAAGA;YAE1B,IAAIoF,eAAe;YACnB,IAAI4C,IAAAA,6BAAqB,EAAChI,SAAS;gBACjCoF,eAAepF,OAAOzD,OAAO;gBAC7ByD,OAAOzD,OAAO,GAAG;YACnB;YACA,MAAMA,UAAUE,aAAI,CAACC,IAAI,CAACwI,KAAKlF,OAAOzD,OAAO;YAC7CkK,8BAAgB,CAAClK,OAAO,GAAGA;YAC3B0L,IAAAA,gBAAS,EAAC,SAASJ,kCAAsB;YACzCI,IAAAA,gBAAS,EAAC,WAAW1L;YAErB,MAAM+B,UAAU,MAAMqH,WACpBC,gBACArJ,SACA4E,eACAnB;YAEFyG,8BAAgB,CAACnI,OAAO,GAAGA;YAE3B,IAAIgI,0BAA0B,gBAAgB;gBAC5C,IAAID,aAAa;oBACfnJ,KAAIE,IAAI,CAAC;oBACT+J,QAAQe,IAAI,CAAC;gBACf;gBACAhL,KAAIiL,IAAI,CAAC;gBACT,MAAMhH,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMsG,IAAAA,gCAAe,EAAC;wBACpB7L;wBACAyD;oBACF;gBACF;gBAEF9C,KAAIiL,IAAI,CAAC;gBACT,MAAME,IAAAA,qBAAc;gBACpBC,IAAAA,4BAAuB;gBACvBnB,QAAQe,IAAI,CAAC;YACf;YAEA,yDAAyD;YACzD,yCAAyC;YACzC,IAAI1B,iBAAiBZ,gBAAgB;gBACnC2C,IAAAA,4BAAiB,EAACvI;YACpB;YAEA,MAAMwI,eAA6B,MAAMrH,cACtCU,UAAU,CAAC,sBACXC,YAAY,CAAC,IAAM2G,IAAAA,yBAAgB,EAACzI;YAEvC,MAAM,EAAE0I,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGJ;YACzC,MAAMK,mBAA8B;mBAC/BF,SAASG,WAAW;mBACpBH,SAASI,UAAU;mBACnBJ,SAASK,QAAQ;aACrB;YACD,MAAMC,cAAcJ,iBAAiBK,MAAM,GAAG;YAC9CzC,8BAAgB,CAACwC,WAAW,GAAGA;YAC/BxC,8BAAgB,CAAC0C,gBAAgB,GAAGnJ,OAAOoJ,iBAAiB;YAC5D3C,8BAAgB,CAAC4C,iBAAiB,GAAGrJ,OAAOsJ,kBAAkB;YAE9D,MAAM9M,WAAWF,YAAYC;YAE7B,MAAMgN,YAAY,IAAIC,kBAAS,CAAC;gBAAEjN;YAAQ;YAE1C0L,IAAAA,gBAAS,EAAC,aAAasB;YAEvB,MAAME,YAAYhN,aAAI,CAACC,IAAI,CAACwI,KAAK;YACjC,MAAM,EAAEwE,QAAQ,EAAE9H,MAAM,EAAE,GAAG+H,IAAAA,0BAAY,EAACzE;YAC1CuB,8BAAgB,CAACiD,QAAQ,GAAGA;YAC5BjD,8BAAgB,CAAC7E,MAAM,GAAGA;YAE1B,MAAMuD,qBAA6C;gBACjDyE,KAAK,OAAOhI,WAAW;gBACvBI,OAAO,OAAO0H,aAAa;YAC7B;YAEA,mDAAmD;YACnD,wFAAwF;YACxF,MAAMG,gBAAgB,MAAMC,IAAAA,kDAA2B,EAAC;gBACtDC,SAAS;gBACTxN;YACF;YACAkK,8BAAgB,CAACoD,aAAa,GAAGA;YAEjC,MAAMG,WAAWvN,aAAI,CAClBgG,QAAQ,CAACyC,KAAKwE,YAAY9H,UAAU,IACpCqI,UAAU,CAAC;YACd,MAAMC,eAAenN,IAAAA,cAAU,EAAC0M;YAEhCF,UAAUY,MAAM,CACdC,IAAAA,uBAAe,EAAClF,KAAKlF,QAAQ;gBAC3BqK,gBAAgB;gBAChBC,YAAY;gBACZN;gBACAO,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;oBAAEC,KAAKvF;gBAAI;gBACnDwF,gBAAgB;gBAChBC,WAAW;gBACXjB,UAAU,CAAC,CAACA;gBACZ9H,QAAQ,CAAC,CAACA;YACZ;YAGFgJ,IAAAA,wBAAgB,EAACnO,aAAI,CAACsH,OAAO,CAACmB,MAAM2F,IAAI,CAAC,CAACC,SACxCvB,UAAUY,MAAM,CAACW;YAGnBC,IAAAA,2BAAe,EAACtO,aAAI,CAACsH,OAAO,CAACmB,MAAMlF,QAAQ6K,IAAI,CAAC,CAACC,SAC/CvB,UAAUY,MAAM,CAACW;YAGnB,qDAAqD;YACrD,MAAM,EAAEE,OAAO,EAAEC,oBAAoB,EAAE,GAAG,MAAMC,IAAAA,8BAAkB,EAAC;gBACjEhG;gBACAiG,KAAK;gBACLlF;YACF;YAEAmF,IAAAA,wBAAY,EAAC;gBACXC,YAAY;gBACZC,QAAQ;gBACRN;gBACAC;YACF;YAEA,MAAMM,eAAeC,QAAQxL,OAAOyL,MAAM,CAACC,kBAAkB;YAC7D,MAAMC,aAAa,CAACJ,gBAAgBrF;YAEpC,MAAM0F,sBAA+D;gBACnE1G;gBACAtD;gBACA8H;gBACAxD;gBACAyF;gBACAJ;gBACAhC;gBACApI;gBACAnB;gBACAxD;YACF;YAEA,MAAMqP,iBAAiB,MAAM1K,cAC1BU,UAAU,CAAC,mBACXC,YAAY,CAAC;gBACZ,IAAI;oBACF,MAAMtE,YAAE,CAACkF,KAAK,CAACnG,SAAS;wBAAEqG,WAAW;oBAAK;oBAC1C,OAAO;gBACT,EAAE,OAAOkJ,KAAK;oBACZ,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,IAAI,KAAK,SAAS;wBACxC,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YAEF,IAAI,CAACD,kBAAkB,CAAE,MAAMI,IAAAA,wBAAW,EAAC1P,UAAW;gBACpD,MAAM,qBAEL,CAFK,IAAI2P,MACR,iGADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIlM,OAAOmM,YAAY,IAAI,CAACvG,gBAAgB;gBAC1C,MAAMwG,IAAAA,gCAAe,EAAC7P,SAAS;YACjC;YAEA,IAAIqF,UAAU,mBAAmB5B,QAAQ;gBACvC9C,KAAImP,KAAK,CACP;gBAEF,MAAM9C,UAAU+C,KAAK;gBACrBnF,QAAQe,IAAI,CAAC;YACf;YAEA,MAAMqE,iBAAyC;gBAC7CC,aAAa;gBACbC,iBAAiBd,aAAa,IAAI;YACpC;YACApC,UAAUY,MAAM,CAAC;gBACfuC,WAAWC,iCAAyB;gBACpCC,SAASL;YACX;YAEA,MAAMM,mBAAmBC,IAAAA,oCAAsB,EAC7C9M,OAAO+M,cAAc,EACrBnL;YAGF,MAAMoL,oBAA8BhP,KAAKC,KAAK,CAC5CkJ,QAAQC,GAAG,CAAC6F,uBAAuB,IAAI;YAGzC,IAAIC,aAAa1B,QAAQrE,QAAQC,GAAG,CAAC6F,uBAAuB,IACxDD,oBACA,CAAC5G,cAAcsD,WACb,MAAMvI,cACHU,UAAU,CAAC,iBACXC,YAAY,CAAC,IAAMqL,IAAAA,0BAAiB,EAACzD,UAAUmD,qBAClD,EAAE;YAER,MAAMO,4BAA4B,IAAIC,OACpC,CAAC,CAAC,EAAEC,8BAAmB,CAAC,MAAM,EAAEtN,OAAO+M,cAAc,CAACrQ,IAAI,CAAC,KAAK,EAAE,CAAC;YAGrE,MAAM6Q,qCAAqC,IAAIF,OAC7C,CAAC,CAAC,EAAEG,wCAA6B,CAAC,MAAM,EAAExN,OAAO+M,cAAc,CAACrQ,IAAI,CAClE,KACA,EAAE,CAAC;YAGP,MAAM+Q,UAAUhR,aAAI,CAACC,IAAI,CAAEgN,YAAY9H,QAAU;YACjD,MAAMU,WAAW;gBACf8K;gBACAG;aACD;YAED,MAAMG,YAAYC,MAAMC,IAAI,CAAC,MAAMC,IAAAA,4BAAa,EAACJ,UAC9C5O,MAAM,CAAC,CAACoD,OAASK,SAASwL,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAAC/L,QACzD5C,IAAI,CAAC4O,IAAAA,uBAAc,EAACjO,OAAO+M,cAAc,GACzChO,GAAG,CAAC,CAACkD,OAASxF,aAAI,CAACC,IAAI,CAAC+Q,SAASxL,MAAMzB,OAAO,CAAC0E,KAAK;YAEvD,MAAMzD,yBAAyBiM,UAAUI,IAAI,CAAC,CAACxN,IAC7CA,EAAEgC,QAAQ,CAACkL,wCAA6B;YAE1C,MAAMU,oBAAoBR,UAAUI,IAAI,CAAC,CAACxN,IACxCA,EAAEgC,QAAQ,CAACgL,8BAAmB;YAGhC7G,8BAAgB,CAAChF,sBAAsB,GAAGA;YAE1C,MAAM0M,eAAkC,MAAMC,IAAAA,oCAAmB,EAAC;gBAChErE,SAAS;gBACTxN;YACF;YACAkK,8BAAgB,CAAC0H,YAAY,GAAGA;YAEhC,MAAM7G,cAAc,MAAMnG,cACvBU,UAAU,CAAC,wBACXC,YAAY,CAAC,IACZuM,IAAAA,2BAAkB,EAAC;oBACjBC,OAAO;oBACPvB,gBAAgB/M,OAAO+M,cAAc;oBACrCwB,WAAWC,qBAAU,CAACC,KAAK;oBAC3BC,WAAWxB;oBACXxD;oBACA9H;gBACF;YAEJ6E,8BAAgB,CAACa,WAAW,GAAGA;YAE/B,IAAIqH;YACJ,IAAIC;YACJ,IAAIvN;YAEJ,IAAIO,QAAQ;gBACV,MAAMiN,mBAA6B7Q,KAAKC,KAAK,CAC3CkJ,QAAQC,GAAG,CAAC0H,sBAAsB,IAAI;gBAGxC,IAAIC;gBACJ,IAAIC;gBAEJ,IAAIxD,QAAQrE,QAAQC,GAAG,CAAC0H,sBAAsB,GAAG;oBAC/C,oBAAoB;oBACpBC,WAAWF;oBACXG,cAAc,EAAE;gBAClB,OAAO;oBACL,gFAAgF;oBAChF,MAAMC,SAAS,MAAM9N,cAClBU,UAAU,CAAC,qBACXC,YAAY,CAAC,IAAMoN,IAAAA,wBAAe,EAACtN,QAAQiL;oBAE9CkC,WAAWE,OAAOF,QAAQ;oBAC1BC,cAAcC,OAAOD,WAAW;gBAChC,0GAA0G;gBAC5G;gBAEAL,iBAAiB,MAAMxN,cACpBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZuM,IAAAA,2BAAkB,EAAC;wBACjBK,WAAWK;wBACXT,OAAO;wBACPC,WAAWC,qBAAU,CAACW,GAAG;wBACzBpC,gBAAgB/M,OAAO+M,cAAc;wBACrCrD;wBACA9H;oBACF;gBAGJgN,mBAAmB,MAAMzN,cACtBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZuM,IAAAA,2BAAkB,EAAC;wBACjBK,WAAWM;wBACXV,OAAO;wBACPC,WAAWC,qBAAU,CAACW,GAAG;wBACzBpC,gBAAgB/M,OAAO+M,cAAc;wBACrCrD;wBACA9H;oBACF;gBAGJ6E,8BAAgB,CAACkI,cAAc,GAAGA;YACpC;YAEA,MAAMS,kBAAkB,MAAMf,IAAAA,2BAAkB,EAAC;gBAC/CC,OAAO;gBACPvB,gBAAgB/M,OAAO+M,cAAc;gBACrC2B,WAAWhB;gBACXa,WAAWC,qBAAU,CAACa,IAAI;gBAC1B3F,UAAUA;gBACV9H;YACF;YACA6E,8BAAgB,CAAC2I,eAAe,GAAGA;YAEnC,MAAME,gBAAgB5Q,OAAOS,IAAI,CAACmI;YAElC,MAAMiI,0BAAiE,EAAE;YACzE,MAAMC,cAAc,IAAI/Q;YACxB,IAAIkQ,gBAAgB;gBAClBtN,uBAAuB3C,OAAOS,IAAI,CAACwP;gBACnC,KAAK,MAAMc,UAAUpO,qBAAsB;oBACzC,MAAMqO,uBAAuBC,IAAAA,0BAAgB,EAACF;oBAC9C,MAAMG,WAAWtI,WAAW,CAACoI,qBAAqB;oBAClD,IAAIE,UAAU;wBACZ,MAAMC,UAAUlB,cAAc,CAACc,OAAO;wBACtCF,wBAAwBhN,IAAI,CAAC;4BAC3BqN,SAASpP,OAAO,CAAC,uBAAuB;4BACxCqP,QAAQrP,OAAO,CAAC,yBAAyB;yBAC1C;oBACH;oBACAgP,YAAYM,GAAG,CAACJ;gBAClB;YACF;YAEA,MAAMX,WAAWpB,MAAMC,IAAI,CAAC4B;YAC5B,2DAA2D;YAC3D7G,SAASG,WAAW,CAACvG,IAAI,IACpBwN,IAAAA,sEAAkC,EAAChB,UAAU/O,OAAOgQ,QAAQ;YAGjEvJ,8BAAgB,CAACkC,QAAQ,GAAGA;YAE5B,MAAMsH,qBAAqBlB,SAAS7F,MAAM;YAE1C,MAAM9H,WAAW;gBACfY,OAAOsN;gBACP1F,KAAKmF,SAAS7F,MAAM,GAAG,IAAI6F,WAAWhI;YACxC;YAEA,MAAM5F,cACHU,UAAU,CAAC,wBACXC,YAAY,CAAC;gBACZ,MAAMoO,qBAAqBzT,aAAI,CAACC,IAAI,CAACH,SAAS,SAAS;gBACvD,MAAM4T,oBAAoB1T,aAAI,CAACC,IAAI,CAACH,SAAS,SAAS;gBACtD,MAAMmG,IAAAA,eAAK,EAACjG,aAAI,CAACkG,OAAO,CAACuN,qBAAqB;oBAAEtN,WAAW;gBAAK;gBAEhE,IAAIwN,YAAyB,EAAE;gBAC/B,IAAIC,mBAAgC,EAAE;gBACtC,IAAIC,eAA4B,EAAE;gBAClC,IAAIC,QAAoB,EAAE;gBAE1B,MAAM,EAAEC,UAAU,EAAEC,aAAa,EAAE,GAAGC,IAAAA,0BAAiB,EACrDpJ,aACApC,KACA8E;gBAGF,mBAAmB;gBACnB,IAAIpI,UAAU+M,gBAAgB;oBAC5B,kDAAkD;oBAClD,MAAMgC,iBAAiBC,IAAAA,kCAAyB,EAACjC;oBACjD,IAAIkC,oBAAgC,EAAE;oBAEtC,oDAAoD;oBACpD,MAAM,EAAEC,YAAY,EAAE,GAAG,MAAM3P,cAC5BU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMoN,IAAAA,wBAAe,EAACtN,QAAQiL;oBAE9C,IAAIiE,aAAa5H,MAAM,GAAG,GAAG;wBAC3B,MAAM6H,qBAAqB,MAAM5P,cAC9BU,UAAU,CAAC,0BACXC,YAAY,CAAC,IACZuM,IAAAA,2BAAkB,EAAC;gCACjBK,WAAWoC;gCACXxC,OAAO;gCACPC,WAAWC,qBAAU,CAACW,GAAG;gCACzBpC,gBAAgB/M,OAAO+M,cAAc;gCACrCrD;gCACA9H;4BACF;wBAEJiP,oBACEG,IAAAA,qCAA4B,EAACD;oBACjC;oBAEA,0CAA0C;oBAC1CR,QAAQU,IAAAA,qBAAY,EAACN,gBAAgBE;oBAErC,MAAM5B,SAASiC,IAAAA,yBAAgB,EAC7BvC,gBACA9B,kBACA3H,KACA8E;oBAEFoG,YAAYnB,OAAOmB,SAAS;oBAC5BC,mBAAmBpB,OAAOoB,gBAAgB;gBAC5C;gBAEA,oBAAoB;gBACpB,IAAIzO,UAAUgN,kBAAkB;oBAC9B0B,eAAea,IAAAA,4BAAmB,EAACvC,kBAAkB1J,KAAK8E;gBAC5D;gBAEA,MAAMoH,qBAAqB,MAAMC,IAAAA,yCAAwB,EAAC;oBACxDnM;oBACAsL;oBACAJ;oBACAC;oBACAI;oBACAH;oBACAC;oBACA3H,WAAW5I,OAAO4I,SAAS;oBAC3BD,UAAU3I,OAAO2I,QAAQ;oBACzBwH;gBACF;gBAEA,MAAMmB,IAAAA,wCAAuB,EAC3BF,oBACAlB,oBACAlQ;gBAEF,MAAMuR,IAAAA,mCAAkB,EAACH,oBAAoBjB;YAC/C;YAEF,6DAA6D;YAC7D,IAAI,CAAC9J,aAAa;gBAChB,MAAMmL,yBAAyBjC,wBAAwBrG,MAAM;gBAC7D,IAAIyF,kBAAkB6C,yBAAyB,GAAG;oBAChDtU,KAAImP,KAAK,CACP,CAAC,6BAA6B,EAC5BmF,2BAA2B,IAAI,SAAS,SACzC,wDAAwD,CAAC;oBAE5D,KAAK,MAAM,CAAC5B,UAAUC,QAAQ,IAAIN,wBAAyB;wBACzDrS,KAAImP,KAAK,CAAC,CAAC,GAAG,EAAEuD,SAAS,KAAK,EAAEC,QAAQ,CAAC,CAAC;oBAC5C;oBACA,MAAMtG,UAAU+C,KAAK;oBACrBnF,QAAQe,IAAI,CAAC;gBACf;YACF;YAEA,MAAMuJ,yBAAmC,EAAE;YAC3C,MAAMC,eAAcpK,mBAAAA,WAAW,CAAC,OAAO,qBAAnBA,iBAAqB2C,UAAU,CAAC0H,0BAAe;YACnE,MAAMC,YAAY,CAAC,EAACjD,kCAAAA,cAAgB,CAACkD,4CAAgC,CAAC;YACtE,MAAMC,qBACJxK,WAAW,CAAC,UAAU,CAAC2C,UAAU,CAAC0H,0BAAe;YAEnD,IAAIzH,cAAc;gBAChB,MAAM6H,6BAA6BhV,IAAAA,cAAU,EAC3CN,aAAI,CAACC,IAAI,CAAC+M,WAAW;gBAEvB,IAAIsI,4BAA4B;oBAC9B,MAAM,qBAAyC,CAAzC,IAAI7F,MAAM8F,yCAA8B,GAAxC,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwC;gBAChD;YACF;YAEA,MAAM7Q,cACHU,UAAU,CAAC,6BACXC,YAAY,CAAC;gBACZ,iDAAiD;gBACjD,sDAAsD;gBACtD,IAAK,MAAMnG,QAAQ2L,YAAa;oBAC9B,MAAM2K,oBAAoB,MAAMC,IAAAA,sBAAU,EACxCzV,aAAI,CAACC,IAAI,CAAC+M,WAAW9N,SAAS,MAAM,WAAWA,OAC/CwW,oBAAQ,CAACC,IAAI;oBAEf,IAAIH,mBAAmB;wBACrBR,uBAAuBlP,IAAI,CAAC5G;oBAC9B;gBACF;gBAEA,MAAM0W,iBAAiBZ,uBAAuBvI,MAAM;gBAEpD,IAAImJ,gBAAgB;oBAClB,MAAM,qBAML,CANK,IAAInG,MACR,CAAC,gCAAgC,EAC/BmG,mBAAmB,IAAI,SAAS,SACjC,uEAAuE,EAAEZ,uBAAuB/U,IAAI,CACnG,OACC,GALC,qBAAA;+BAAA;oCAAA;sCAAA;oBAMN;gBACF;YACF;YAEF,MAAM4V,sBAAsBlR,SAASY,KAAK,CAACnD,MAAM,CAAC,CAAClD;gBACjD,OACEA,KAAK4W,KAAK,CAAC,iCAAiC9V,aAAI,CAACkG,OAAO,CAAChH,UAAU;YAEvE;YAEA,IAAI2W,oBAAoBpJ,MAAM,EAAE;gBAC9BhM,KAAIE,IAAI,CACN,CAAC,4FAA4F,CAAC,GAC5FkV,oBAAoB5V,IAAI,CAAC,QACzB,CAAC,6EAA6E,CAAC;YAErF;YAEA,MAAM8V,0BAA0B;gBAAC;aAAS,CAACzT,GAAG,CAAC,CAACuB,IAC9CN,OAAOgQ,QAAQ,GAAG,GAAGhQ,OAAOgQ,QAAQ,GAAG1P,GAAG,GAAGA;YAG/C,MAAMmS,8BAA8BjH,QAClCxL,OAAOmD,YAAY,CAACuP,eAAe;YAErC,MAAMC,0BAA0BnH,QAC9BxL,OAAOmD,YAAY,CAACyP,cAAc;YAEpC,MAAMC,kBAAkBC,IAAAA,yBAAoB,EAAC9S,OAAOmD,YAAY,CAAC4P,GAAG;YAEpE,MAAMC,qBAAqBvW,aAAI,CAACC,IAAI,CAACH,SAAS0W,2BAAe;YAC7D,MAAM7T,gBAA6C,EAAE;YAErD;;;OAGC,GACD,MAAM8T,cAAc,IAAIC;YACxB,MAAMC,iBAAiCjS,cACpCU,UAAU,CAAC,4BACX0F,OAAO,CAAC;gBACP,MAAM8L,eAAeC,IAAAA,yBAAS,EAAC;uBAC1BlS,SAASY,KAAK;uBACbZ,SAASwI,GAAG,IAAI,EAAE;iBACvB;gBACD,MAAM2J,eAAqC,EAAE;gBAE7C,KAAK,MAAMvU,SAASqU,aAAc;oBAChC,IAAIG,IAAAA,qBAAc,EAACxU,QAAQ;wBACzBI,cAAcmD,IAAI,CAChB7G,YACEsD,OACA,sDAAsD;wBACtD+H;oBAGN,OAAO,IAAI,CAAC0M,IAAAA,sBAAc,EAACzU,QAAQ;wBACjCuU,aAAahR,IAAI,CAAC7G,YAAYsD;oBAChC;gBACF;gBAEA,OAAO;oBACLgC,SAAS;oBACT0S,UAAU;oBACVC,eAAe,CAAC,CAAC3T,OAAOmD,YAAY,CAACyQ,mBAAmB;oBACxD5D,UAAUhQ,OAAOgQ,QAAQ;oBACzBpH,WAAWA,UAAU7J,GAAG,CAAC,CAAC8U,IACxBC,IAAAA,kCAAgB,EAAC,YAAYD,GAAGrB;oBAElC9J,SAASA,QAAQ3J,GAAG,CAAC,CAAC8U,IAAMC,IAAAA,kCAAgB,EAAC,UAAUD;oBACvDlL,UAAU;wBACRG,aAAaH,SAASG,WAAW,CAAC/J,GAAG,CAAC,CAAC8U,IACrCC,IAAAA,kCAAgB,EAAC,WAAWD;wBAE9B9K,YAAYJ,SAASI,UAAU,CAAChK,GAAG,CAAC,CAAC8U,IACnCC,IAAAA,kCAAgB,EAAC,WAAWD;wBAE9B7K,UAAUL,SAASK,QAAQ,CAACjK,GAAG,CAAC,CAAC8U,IAC/BC,IAAAA,kCAAgB,EAAC,WAAWD;oBAEhC;oBACAzU;oBACAmU;oBACAQ,YAAY,EAAE;oBACdC,MAAMhU,OAAOgU,IAAI,IAAIjN;oBACrBkN,KAAK;wBACHC,QAAQC,4BAAU;wBAClB,yFAAyF;wBACzF,4DAA4D;wBAC5DC,YAAY,GAAGD,4BAAU,CAAC,EAAE,EAAEE,+CAA6B,CAAC,EAAE,EAAEC,6CAA2B,CAAC,EAAE,EAAEC,qDAAmC,EAAE;wBACrIC,gBAAgBF,6CAA2B;wBAC3CG,mBAAmBC,0CAAwB;wBAC3CC,mBAAmBC,yCAAuB;wBAC1CC,QAAQC,qBAAU;wBAClBC,gBAAgBC,8BAAmB;wBACnCC,uBAAuBV,qDAAmC;wBAC1DW,uBAAuBC,6BAAkB;wBACzCC,0BAA0BC,kCAAuB;oBACnD;oBACAC,gBAAgB;wBACdC,YAAYC,4CAA0B;wBACtCC,aAAaC,6CAA2B;oBAC1C;oBACAC,4BAA4B3V,OAAO2V,0BAA0B;oBAC7D5C,KAAKF,kBACD;wBACE+C,OAAO;4BACLlN,SAAS;gCACP,CAACmN,6BAAkB,CAAC,EAAE;4BACxB;wBACF;oBACF,IACA9O;gBACN;YACF;YAEF,qFAAqF;YACrF,IAAI,CAACnF,UAAU,CAAC4E,eAAe;gBAC7B,MAAMsP,IAAAA,4BAAiB,EAAClK;YAC1B;YAEA,IAAImK;YAIJ,IAAI/V,OAAOmD,YAAY,CAAC6S,kBAAkB,EAAE;gBAC1C,MAAMC,uBAAuB,AAACjW,CAAAA,OAAOsJ,kBAAkB,IAAI,EAAE,AAAD,EAAGzK,MAAM,CACnE,CAACgV,IAAW,CAACA,EAAEqC,QAAQ;gBAEzBH,sBAAsBI,IAAAA,kDAAwB,EAC5C;uBAAIpH;iBAAS,EACb/O,OAAOmD,YAAY,CAACiT,2BAA2B,GAC3CH,uBACA,EAAE,EACNjW,OAAOmD,YAAY,CAACkT,6BAA6B;gBAEnD5P,8BAAgB,CAACsP,mBAAmB,GAAGA;YACzC;YAEA,8EAA8E;YAC9E,uDAAuD;YACvD,MAAM1Y,cACJZ,aAAI,CAACC,IAAI,CAACH,SAAS,iBACnB;YAGF,yFAAyF;YACzF,MAAM+Z,IAAAA,wCAAsB,EAACnP,QAAQC,GAAG,CAACC,cAAc;YACvD,MAAMkP,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;YACd;YAEA,MAAMlV,wBAAwBtB,OAAOsB,qBAAqB,IAAI4D;YAE9D,MAAMuR,oBAAoBha,aAAI,CAACC,IAAI,CACjCH,SACAmD,4BAAgB,EAChBgX,0BAAc;YAGhB,IAAIC;YACJ,IAAIC,qBAA+C7P;YAEnD,uEAAuE;YACvE,4CAA4C;YAC5C,MAAM8P,iBACJ7W,OAAOmD,YAAY,CAAC2T,kBAAkB,IACrC9W,OAAOmD,YAAY,CAAC2T,kBAAkB,KAAK/P,aAC1C,CAAC/G,OAAO+W,OAAO;YACnB,MAAMC,6BACJhX,OAAOmD,YAAY,CAAC8T,sBAAsB;YAC5C,MAAMC,qCACJlX,OAAOmD,YAAY,CAACgU,yBAAyB,IAC5CnX,OAAOmD,YAAY,CAACgU,yBAAyB,KAAKpQ,aACjDP;YAEJrF,cAAciW,YAAY,CACxB,6BACAlQ,OAAO,CAAC,CAAClH,OAAO+W,OAAO;YAEzB5V,cAAciW,YAAY,CAAC,oBAAoBlQ,OAAO2P;YAEtD,IACE,CAACA,kBACAG,CAAAA,8BAA8BE,kCAAiC,GAChE;gBACA,MAAM,qBAEL,CAFK,IAAIhL,MACR,oMADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAhP,KAAIiL,IAAI,CAAC;YACTkP,IAAAA,wBAAgB,EAAC,kBAAkBlW;YAEnC,MAAMoV,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;gBACZc,cAAc;oBACZT,gBAAgB3P,OAAO2P;gBACzB;YACF;YAEA,IAAIU,kBAAkBC,QAAQzT,OAAO;YACrC,IAAI,CAAC6B,gBAAgB;gBACnB,IAAIS,aAAa;oBACf,MAAM,EACJoR,UAAUC,gBAAgB,EAC1BH,iBAAiBjX,CAAC,EAClB,GAAGqX,MACJ,GAAG,MAAMC,IAAAA,8BAAc,EACtBzQ,QAAQC,GAAG,CAACyQ,yBAAyB,KAAK9Q,aACxCI,QAAQC,GAAG,CAACyQ,yBAAyB,KAAK;oBAE9CN,kBAAkBjX;oBAClB+W,IAAAA,wBAAgB,EAAC,kBAAkBlW;oBAEnCwV,oBAAoBgB,KAAKhB,iBAAiB;oBAE1C,MAAMmB,iBAAiBC,IAAAA,kCAAgB,EAACL;oBACxCxa,KAAI8a,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;oBAEtDvO,UAAUY,MAAM,CACd8N,IAAAA,2BAAmB,EAAC/K,YAAY;wBAC9BgL,SAAS;wBACTC,mBAAmB5U,KAAK6U,KAAK,CAACV;wBAC9BzH;oBACF;gBAEJ,OAAO;oBACL,IACE+G,8BACAE,oCACA;wBACA,IAAIiB,oBAAoB;wBAExB,MAAM5B,IAAAA,wCAAsB,EAAC;4BAC3BC,YAAY;wBACd;wBAEA,MAAM6B,qBAAqBC,IAAAA,0BAAY,EAACzB,gBAAgB;4BACtD;yBACD,EAAEhM,IAAI,CAAC,CAAC0N;4BACPlB,IAAAA,wBAAgB,EAAC,+BAA+BlW;4BAChDwV,oBAAoB4B,IAAI5B,iBAAiB;4BACzCwB,qBAAqBI,IAAId,QAAQ;4BAEjC,IAAIP,oCAAoC;gCACtC,MAAMsB,mBAAmB,IAAIpU,cAAM,CACjCN,QAAQC,OAAO,CAAC,2BAChB;oCACEG,oBAAoB,CAAC;oCACrBW,gBAAgB;oCAChBP,YAAY;oCACZU,gBAAgB;wCAAC;qCAAqB;gCACxC;gCAGF4R,qBAAqB4B,iBAClBC,kBAAkB,CAAC;oCAClBvT;oCACAlF;oCACAzD;oCACA,+CAA+C;oCAC/Cmc,mBAAmBC,IAAAA,qCAA6B,EAAC,IAAIxF;oCACrDzR,aAAa,EAAE;oCACfkX,gBAAgB;oCAChBjC;oCACArV;oCACA+E,aAAa;gCACf,GACCwS,KAAK,CAAC,CAAC/M;oCACN9O,QAAQqP,KAAK,CAACP;oCACd3E,QAAQe,IAAI,CAAC;gCACf;4BACJ;wBACF;wBACA,IAAI,CAAC8O,4BAA4B;4BAC/B,MAAMqB;4BACN,MAAM9B,IAAAA,wCAAsB,EAAC;gCAC3BC,YAAY;4BACd;wBACF;wBAEA,MAAMsC,mBAAmBR,IAAAA,0BAAY,EAACzB,gBAAgB;4BACpD;yBACD,EAAEhM,IAAI,CAAC,CAAC0N;4BACPJ,qBAAqBI,IAAId,QAAQ;4BACjCJ,IAAAA,wBAAgB,EACd,oCACAlW;wBAEJ;wBACA,IAAI6V,4BAA4B;4BAC9B,MAAMqB;4BACN,MAAM9B,IAAAA,wCAAsB,EAAC;gCAC3BC,YAAY;4BACd;wBACF;wBACA,MAAMsC;wBAEN,MAAMvC,IAAAA,wCAAsB,EAAC;4BAC3BC,YAAY;wBACd;wBAEA,MAAM8B,IAAAA,0BAAY,EAACzB,gBAAgB;4BAAC;yBAAS,EAAEhM,IAAI,CAAC,CAAC0N;4BACnDJ,qBAAqBI,IAAId,QAAQ;4BACjCJ,IAAAA,wBAAgB,EAAC,+BAA+BlW;wBAClD;wBAEA,MAAM2W,iBAAiBC,IAAAA,kCAAgB,EAACI;wBACxCjb,KAAI8a,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;wBAEtDvO,UAAUY,MAAM,CACd8N,IAAAA,2BAAmB,EAAC/K,YAAY;4BAC9BgL,SAASa,uBAAuB1S;4BAChC8R;4BACAlI;wBACF;oBAEJ,OAAO;wBACL,MAAM,EAAEwH,UAAUC,gBAAgB,EAAE,GAAGC,MAAM,GAAG,MAAMW,IAAAA,0BAAY,EAChEzB,gBACA;wBAEFQ,IAAAA,wBAAgB,EAAC,kBAAkBlW;wBAEnCwV,oBAAoBgB,KAAKhB,iBAAiB;wBAE1CpN,UAAUY,MAAM,CACd8N,IAAAA,2BAAmB,EAAC/K,YAAY;4BAC9BgL,SAASa,uBAAuB1S;4BAChC8R,mBAAmBT;4BACnBzH;wBACF;oBAEJ;gBACF;gBACA,MAAM+I,IAAAA,iDAAyB,EAAC;oBAC9BhZ;oBACAiZ,WAAW9X;oBACXoI;oBACA2P,UAAU;wBACRC,YAAYjU;wBACZ3I;oBACF;gBACF;YACF;YAEA,uDAAuD;YACvD,IAAIqF,UAAU,CAAC4E,iBAAiB,CAACZ,gBAAgB;gBAC/C,MAAM2Q,IAAAA,wCAAsB,EAAC;oBAC3BC,YAAY;gBACd;gBACA,MAAMV,IAAAA,4BAAiB,EAAClK;gBACxByL,IAAAA,wBAAgB,EAAC,0BAA0BlW;YAC7C;YAEA,MAAMiY,qBAAqBC,IAAAA,gBAAa,EAAC;YAEzC,MAAMC,oBAAoB7c,aAAI,CAACC,IAAI,CAACH,SAASgd,0BAAc;YAC3D,MAAMC,uBAAuB/c,aAAI,CAACC,IAAI,CAACH,SAASkd,8BAAkB;YAElE,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,wBAAwB;YAC5B,MAAMrb,WAAW,IAAIC;YACrB,MAAMqb,yBAAyB,IAAIrb;YACnC,MAAMsb,2BAA2B,IAAItb;YACrC,MAAMiD,cAAc,IAAIjD;YACxB,MAAMub,eAAe,IAAIvb;YACzB,MAAMwb,iBAAiB,IAAIxb;YAC3B,MAAMyb,mBAAmB,IAAIzb;YAC7B,MAAM0b,kBAAkB,IAAIhH;YAC5B,MAAMiH,cAAc,IAAIjH;YACxB,MAAMkH,qBAAqB,IAAIlH;YAC/B,MAAMmH,gBAAgB,IAAInH;YAC1B,MAAMoH,oBAAoB,IAAIpH;YAC9B,MAAMqH,YAAuB,IAAIrH;YACjC,IAAIsH,gBAAgB,MAAM1c,aAA4B0Y;YACtD,MAAMiE,gBAAgB,MAAM3c,aAA4Bub;YACxD,MAAMqB,mBAAmB/Y,SACrB,MAAM7D,aAA+Byb,wBACrCzS;YAEJ,MAAM6T,gBAAwC,CAAC;YAE/C,IAAIhZ,QAAQ;gBACV,MAAMiZ,mBAAmB,MAAM9c,aAC7BtB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEob,8BAAkB;gBAGzD,IAAK,MAAMC,OAAOF,iBAAkB;oBAClCD,aAAa,CAACG,IAAI,GAAGpL,IAAAA,0BAAgB,EAACoL;gBACxC;gBAEA,MAAMnd,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASye,oCAAwB,GAC3CJ;YAEJ;YAEAzT,QAAQC,GAAG,CAAC6T,UAAU,GAAGpT,kCAAsB;YAE/C,MAAMqT,SAAShgB,mBAAmB8E,QAAQ;gBAAEkE,oBAAoB,CAAC;YAAE;YAEnE,MAAMiX,gBAAgBhU,QAAQiU,MAAM;YACpC,MAAMC,kBAAkBla,cAAcU,UAAU,CAAC;YAEjD,MAAMyZ,0BAAmD;gBACvDta,SAAS;gBACTua,WAAW,CAAC;YACd;YAEA,MAAM,EACJC,wBAAwB,EACxBC,YAAY,EACZC,mBAAmB,EACnB9C,cAAc,EACd+C,qBAAqB,EACtB,GAAG,MAAMN,gBAAgBvZ,YAAY,CAAC;oBAcV9B;gBAb3B,IAAIwG,eAAe;oBACjB,OAAO;wBACLgV,0BAA0B;wBAC1BC,cAAc,EAAE;wBAChBC,qBAAqB;wBACrB9C,gBAAgB,CAAC,CAAClP;wBAClBiS,uBAAuB;oBACzB;gBACF;gBAEA,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE,GAChE9b;gBACF,MAAM+b,mBAAmB;oBAAEF;oBAAqBC;gBAAoB;gBACpE,MAAME,aAAaxQ,SAAQxL,2BAAAA,OAAOmD,YAAY,CAAC8Y,GAAG,qBAAvBjc,yBAAyBkc,SAAS;gBAE7D,MAAMC,yBAAyBd,gBAAgBxZ,UAAU,CACvD;gBAEF,MAAMua,oCACJD,uBAAuBra,YAAY,CACjC,UACEgQ,sBACC,MAAMoJ,OAAOmB,wBAAwB,CAAC;wBACrC1gB,MAAM;wBACNY;wBACAwf;wBACAO,aAAa;wBACbN;oBACF;gBAGN,MAAMO,wBAAwBJ,uBAAuBra,YAAY,CAC/D;wBAWa9B,cACMA;2BAXjB8R,sBACAoJ,OAAOsB,YAAY,CAAC;wBAClBtX;wBACAvJ,MAAM;wBACNY;wBACAqf;wBACAG;wBACArJ,iBAAiBD;wBACjBG,gBAAgBD;wBAChB8J,kBAAkBzc,OAAOyc,gBAAgB;wBACzCle,OAAO,GAAEyB,eAAAA,OAAOgU,IAAI,qBAAXhU,aAAazB,OAAO;wBAC7Bme,aAAa,GAAE1c,gBAAAA,OAAOgU,IAAI,qBAAXhU,cAAa0c,aAAa;wBACzCC,kBAAkB3c,OAAO4c,MAAM;wBAC/BC,WAAW7c,OAAOmD,YAAY,CAAC4P,GAAG;wBAClC+J,mBAAmB9c,OAAOmD,YAAY,CAAC4Z,SAAS;wBAChDze;wBACA0d;oBACF;;gBAGJ,MAAMgB,iBAAiB;gBAEvB,MAAMC,kCAAkC/B,OAAOmB,wBAAwB,CACrE;oBACE1gB,MAAMqhB;oBACNzgB;oBACAwf;oBACAO,aAAa;oBACbN;gBACF;gBAGF,MAAMkB,sBAAsBhC,OAAOiC,sBAAsB,CAAC;oBACxDxhB,MAAMqhB;oBACNzgB;oBACAwf;oBACAC;gBACF;gBAEA,wDAAwD;gBACxD,IAAIN;gBACJ,wDAAwD;gBACxD,IAAI9C,iBAAiB;gBAErB,MAAMwE,uBAAuB,MAAMC,IAAAA,2BAAmB,EACpD;oBAAEliB,OAAOuf;oBAAe9Q,KAAK+Q;gBAAiB,GAC9Cpe,SACAyD,OAAOmD,YAAY,CAACma,QAAQ;gBAG9B,MAAM/b,qBAAyCuC,QAC7CrH,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE6d,+BAAmB;gBAG1D,MAAMC,iBAAiB5b,SAClBkC,QACCrH,aAAI,CAACC,IAAI,CACPH,SACAmD,4BAAgB,EAChB+d,qCAAyB,GAAG,YAGhC;gBACJ,MAAMC,oBAAoBF,iBAAiB,IAAI/e,QAAQ;gBACvD,IAAI+e,kBAAkBE,mBAAmB;oBACvC,IAAK,MAAMC,MAAMH,eAAeI,IAAI,CAAE;wBACpC,IAAK,MAAMC,SAASL,eAAeI,IAAI,CAACD,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkB5N,GAAG,CAAC+N;wBACxB;oBACF;oBACA,IAAK,MAAMF,MAAMH,eAAeO,IAAI,CAAE;wBACpC,IAAK,MAAMF,SAASL,eAAeO,IAAI,CAACJ,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkB5N,GAAG,CAAC+N;wBACxB;oBACF;gBACF;gBAEA,KAAK,MAAM9C,OAAOrc,OAAOS,IAAI,CAACoC,sCAAAA,mBAAoBga,SAAS,EAAG;oBAC5D,IAAIR,IAAI9Q,UAAU,CAAC,SAAS;wBAC1B4P;oBACF;gBACF;gBAEA,MAAMrC,QAAQwG,GAAG,CACftf,OAAOC,OAAO,CAACyC,UACZe,MAAM,CACL,CAACC,KAAK,CAAC2Y,KAAK7Y,MAAM;oBAChB,IAAI,CAACA,OAAO;wBACV,OAAOE;oBACT;oBAEA,MAAM6b,WAAWlD;oBAEjB,KAAK,MAAMpf,QAAQuG,MAAO;wBACxBE,IAAIG,IAAI,CAAC;4BAAE0b;4BAAUtiB;wBAAK;oBAC5B;oBAEA,OAAOyG;gBACT,GACA,EAAE,EAEHrD,GAAG,CAAC,CAAC,EAAEkf,QAAQ,EAAEtiB,IAAI,EAAE;oBACtB,MAAMuiB,gBAAgB7C,gBAAgBxZ,UAAU,CAAC,cAAc;wBAC7DlG;oBACF;oBACA,OAAOuiB,cAAcpc,YAAY,CAAC;wBAChC,MAAMqc,aAAaC,IAAAA,oCAAiB,EAACziB;wBACrC,MAAM,CAAC0iB,MAAMC,UAAU,GAAG,MAAMC,IAAAA,yBAAiB,EAC/CN,UACAE,YACA5hB,SACAme,eACAC,kBACA3a,OAAOmD,YAAY,CAACma,QAAQ,EAC5BF;wBAGF,IAAIoB,oBAAoB;wBACxB,IAAIC,QAAQ;wBACZ,IAAIC,WAAW;wBACf,IAAIC,oBAAoB;wBACxB,IAAIC,cAAc;wBAClB,IAAIC,gBAAiC;wBACrC,IAAIjP,WAAW;wBAEf,IAAIqO,aAAa,SAAS;4BACxBrO,WACE1C,WAAW4R,IAAI,CAAC,CAACxe;gCACfA,IAAIye,IAAAA,kCAAgB,EAACze;gCACrB,OACEA,EAAE2J,UAAU,CAACkU,aAAa,QAC1B7d,EAAE2J,UAAU,CAACkU,aAAa;4BAE9B,MAAM;wBACV;wBACA,IAAIa;wBAEJ,IAAIf,aAAa,SAAStP,gBAAgB;4BACxC,KAAK,MAAM,CAACsQ,cAAcC,eAAe,IAAIxgB,OAAOC,OAAO,CACzDic,eACC;gCACD,IAAIsE,mBAAmBvjB,MAAM;oCAC3BiU,WAAWjB,cAAc,CAACsQ,aAAa,CAACze,OAAO,CAC7C,yBACA;oCAEFwe,kBAAkBC;oCAClB;gCACF;4BACF;wBACF;wBAEA,MAAME,eAAeC,IAAAA,gCAAwB,EAACxP,YAC1C9L,QAAQC,OAAO,CACb,mDAEFtH,aAAI,CAACC,IAAI,CACP,AAACuhB,CAAAA,aAAa,UAAUvU,WAAW9H,MAAK,KAAM,IAC9CgO;wBAGN,MAAMyP,iBAAiBpB,aAAa;wBACpC,MAAMqB,aAAa1P,WACf,MAAM2P,IAAAA,sCAA6B,EAAC;4BAClCF;4BACAF;4BACApS,gBAAgB/M,OAAO+M,cAAc;4BACrCnL;4BACA5B;4BACAsO,OAAO;4BACP,yDAAyD;4BACzD,4DAA4D;4BAC5D,gEAAgE;4BAChE3S,MAAM0jB,iBAAiBL,kBAAmBrjB;wBAC5C,KACAoL;wBAEJ,IAAIuY,8BAAAA,WAAYE,mBAAmB,EAAE;4BACnCC;wBACF;wBAEA,8DAA8D;wBAC9D,oDAAoD;wBACpD,IACE,QAAOH,8BAAAA,WAAYI,OAAO,MAAK,eAC/B,QAAOJ,8BAAAA,WAAYK,WAAW,MAAK,eACnC,QAAOL,8BAAAA,WAAYM,eAAe,MAAK,aACvC;4BACA,MAAMC,UAAUP,CAAAA,8BAAAA,WAAYM,eAAe,IACvC,OAAON,WAAWM,eAAe,KAAK,WACpC;gCAACN,WAAWM,eAAe;6BAAC,GAC5BN,WAAWM,eAAe,GAC5B7Y;4BAEJuU,wBAAwBC,SAAS,CAAC5f,KAAK,GAAG;gCACxCgkB,WAAW,EAAEL,8BAAAA,WAAYK,WAAW;gCACpC,GAAIE,WAAW;oCAAEA;gCAAQ,CAAC;4BAC5B;wBACF;wBAEA,MAAMC,cAAcve,mBAAmBga,SAAS,CAC9CyD,mBAAmBrjB,KACpB,GACG,SACA2jB,8BAAAA,WAAYI,OAAO;wBAEvB,IAAI,CAAClZ,eAAe;4BAClBmY,oBACEV,aAAa,SACbqB,CAAAA,8BAAAA,WAAYrL,GAAG,MAAK8L,4BAAgB,CAACC,MAAM;4BAE7C,IAAI/B,aAAa,SAAS,CAACxK,IAAAA,sBAAc,EAAC9X,OAAO;gCAC/C,IAAI;oCACF,IAAIskB;oCAEJ,IAAIC,IAAAA,4BAAa,EAACJ,cAAc;wCAC9B,IAAI7B,aAAa,OAAO;4CACtBrE;wCACF,OAAO;4CACLC;wCACF;wCAEA,MAAMsG,cACJlC,aAAa,UAAUtiB,OAAOqjB,mBAAmB;wCAEnDiB,WAAW1e,mBAAmBga,SAAS,CAAC4E,YAAY;oCACtD;oCAEA,IAAIC,mBACFlC,cAAcrc,UAAU,CAAC;oCAC3B,IAAIwe,eAAe,MAAMD,iBAAiBte,YAAY,CACpD;4CASa9B,cACMA;wCATjB,OAAOkb,OAAOsB,YAAY,CAAC;4CACzBtX;4CACAvJ;4CACAqjB;4CACAziB;4CACAqf;4CACAG;4CACAU,kBAAkBzc,OAAOyc,gBAAgB;4CACzCle,OAAO,GAAEyB,eAAAA,OAAOgU,IAAI,qBAAXhU,aAAazB,OAAO;4CAC7Bme,aAAa,GAAE1c,gBAAAA,OAAOgU,IAAI,qBAAXhU,cAAa0c,aAAa;4CACzC4D,UAAUF,iBAAiBG,KAAK;4CAChCT;4CACAG;4CACAhC;4CACAvL,iBAAiBD;4CACjBG,gBAAgBD;4CAChB6N,cAAcxgB,OAAOwgB,YAAY;4CACjCC,eAAezgB,OAAOmD,YAAY,CAACsd,aAAa;4CAChDC,gBAAgB/jB,QAAcE,cAAc,GACxC,QACAmD,OAAOmD,YAAY,CAACud,cAAc;4CACtCC,oBAAoB3gB,OAAO4gB,kBAAkB;4CAC7CjE,kBAAkB3c,OAAO4c,MAAM;4CAC/BC,WAAW7c,OAAOmD,YAAY,CAAC4P,GAAG;4CAClC+J,mBAAmB9c,OAAOmD,YAAY,CAAC4Z,SAAS;4CAChDze;4CACA0d;wCACF;oCACF;oCAGF,IAAIiC,aAAa,SAASe,iBAAiB;wCACzC3E,mBAAmBwG,GAAG,CAAC7B,iBAAiBrjB;wCACxC,0CAA0C;wCAC1C,IAAIukB,IAAAA,4BAAa,EAACJ,cAAc;4CAC9BpB,WAAW;4CACXD,QAAQ;4CAERvhB,KAAI4jB,QAAQ,CACV,CAAC,+EAA+E,CAAC;wCAErF,OAAO;4CACL,MAAMC,YAAYvN,IAAAA,qBAAc,EAAC7X;4CAEjC,IACE,OAAO0kB,aAAa7B,iBAAiB,KAAK,WAC1C;gDACAA,oBAAoB6B,aAAa7B,iBAAiB;4CACpD;4CAEA,oDAAoD;4CACpD,0CAA0C;4CAC1C,yBAAyB;4CACzB,IAAI6B,aAAa7B,iBAAiB,EAAE;gDAClCC,QAAQ;gDACRC,WAAW;gDAEXtE,YAAYyG,GAAG,CAAC7B,iBAAiB,EAAE;4CACrC;4CAEA,IAAIqB,aAAaW,iBAAiB,EAAE;gDAClC5G,YAAYyG,GAAG,CACb7B,iBACAqB,aAAaW,iBAAiB;gDAEhCnC,gBAAgBwB,aAAaW,iBAAiB,CAACjiB,GAAG,CAChD,CAACC,QAAUA,MAAME,QAAQ;gDAE3Buf,QAAQ;4CACV;4CAEA,MAAMwC,YAAYZ,aAAaY,SAAS,IAAI,CAAC;4CAC7C,IAAIA,UAAUC,UAAU,KAAK,GAAG;gDAC9B,MAAMC,0BACJd,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC9X,MAAM,GAAG;gDAE1C,IACElJ,OAAO4c,MAAM,KAAK,YAClBmE,aACA,CAACI,yBACD;oDACA,MAAM,qBAEL,CAFK,IAAIjV,MACR,CAAC,MAAM,EAAEvQ,KAAK,wFAAwF,CAAC,GADnG,qBAAA;+DAAA;oEAAA;sEAAA;oDAEN;gDACF;gDAEA,6BAA6B;gDAC7B,4BAA4B;gDAC5B,iEAAiE;gDACjE,8BAA8B;gDAC9B,IAAI,CAAColB,WAAW;oDACd3G,YAAYyG,GAAG,CAAC7B,iBAAiB;wDAC/B;4DACEoC,QAAQ,CAAC;4DACTliB,UAAUvD;4DACV0lB,iBAAiB1lB;4DACjB2lB,qBAAqB,EAAE;4DACvBC,cACElB,aAAamB,qBAAqB;4DACpCC,oBAAoB,EAAE;4DACtBC,yBAAyB;wDAC3B;qDACD;oDACDhD,WAAW;gDACb,OAAO,IACL,CAACyC,2BACAF,CAAAA,UAAUU,OAAO,KAAK,WACrBV,UAAUU,OAAO,KAAK,cAAa,GACrC;oDACAvH,YAAYyG,GAAG,CAAC7B,iBAAiB,EAAE;oDACnCN,WAAW;oDACXF,oBAAoB;gDACtB;4CACF;4CAEA,IAAI6B,aAAamB,qBAAqB,EAAE;gDACtClH,cAAcuG,GAAG,CACf7B,iBACAqB,aAAamB,qBAAqB;4CAEtC;4CAEAjH,kBAAkBsG,GAAG,CAAC7B,iBAAiBiC;wCACzC;oCACF,OAAO;wCACL,IAAIf,IAAAA,4BAAa,EAACJ,cAAc;4CAC9B,IAAIO,aAAauB,cAAc,EAAE;gDAC/B5kB,QAAQI,IAAI,CACV,CAAC,kFAAkF,EAAEzB,MAAM;4CAE/F;4CACA0kB,aAAa3B,QAAQ,GAAG;4CACxB2B,aAAauB,cAAc,GAAG;wCAChC;wCAEA,IACEvB,aAAa3B,QAAQ,KAAK,SACzB2B,CAAAA,aAAazB,WAAW,IAAIyB,aAAawB,SAAS,AAAD,GAClD;4CACAjJ,iBAAiB;wCACnB;wCAEA,IAAIyH,aAAazB,WAAW,EAAE;4CAC5BA,cAAc;4CACd3E,eAAenK,GAAG,CAACnU;wCACrB;wCAEA,IAAI0kB,aAAa3E,mBAAmB,EAAE;4CACpCA,sBAAsB;wCACxB;wCAEA,IAAI2E,aAAauB,cAAc,EAAE;4CAC/BpjB,SAASsR,GAAG,CAACnU;4CACb8iB,QAAQ;4CAER,IACE4B,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC9X,MAAM,GAAG,GACxC;gDACAiR,gBAAgB0G,GAAG,CACjBllB,MACA0kB,aAAaW,iBAAiB;gDAEhCnC,gBAAgBwB,aAAaW,iBAAiB,CAACjiB,GAAG,CAChD,CAACC,QAAUA,MAAME,QAAQ;4CAE7B;4CAEA,IACEmhB,aAAamB,qBAAqB,KAClCM,sBAAY,CAACC,sBAAsB,EACnC;gDACAhI,yBAAyBjK,GAAG,CAACnU;4CAC/B,OAAO,IACL0kB,aAAamB,qBAAqB,KAClCM,sBAAY,CAACE,SAAS,EACtB;gDACAlI,uBAAuBhK,GAAG,CAACnU;4CAC7B;wCACF,OAAO,IAAI0kB,aAAa4B,cAAc,EAAE;4CACtC/H,iBAAiBpK,GAAG,CAACnU;wCACvB,OAAO,IACL0kB,aAAa3B,QAAQ,IACrB,CAACC,qBACD,AAAC,MAAM1B,oCAAqC,OAC5C;4CACAvb,YAAYoO,GAAG,CAACnU;4CAChB+iB,WAAW;wCACb,OAAO,IAAIC,mBAAmB;4CAC5B,2DAA2D;4CAC3D,gDAAgD;4CAChDngB,SAASsR,GAAG,CAACnU;4CACb8iB,QAAQ;wCACV;wCAEA,IAAI/M,eAAe/V,SAAS,QAAQ;4CAClC,IACE,CAAC0kB,aAAa3B,QAAQ,IACtB,CAAC2B,aAAauB,cAAc,EAC5B;gDACA,MAAM,qBAEL,CAFK,IAAI1V,MACR,CAAC,cAAc,EAAEgW,qDAA0C,EAAE,GADzD,qBAAA;2DAAA;gEAAA;kEAAA;gDAEN;4CACF;4CACA,2DAA2D;4CAC3D,mCAAmC;4CACnC,IACE,AAAC,MAAMjF,mCACP,CAACoD,aAAauB,cAAc,EAC5B;gDACAlgB,YAAYygB,MAAM,CAACxmB;4CACrB;wCACF;wCAEA,IACEymB,+BAAmB,CAAC9f,QAAQ,CAAC3G,SAC7B,CAAC0kB,aAAa3B,QAAQ,IACtB,CAAC2B,aAAauB,cAAc,EAC5B;4CACA,MAAM,qBAEL,CAFK,IAAI1V,MACR,CAAC,OAAO,EAAEvQ,KAAK,GAAG,EAAEumB,qDAA0C,EAAE,GAD5D,qBAAA;uDAAA;4DAAA;8DAAA;4CAEN;wCACF;oCACF;gCACF,EAAE,OAAOpW,KAAK;oCACZ,IACE,CAACC,IAAAA,gBAAO,EAACD,QACTA,IAAIuW,OAAO,KAAK,0BAEhB,MAAMvW;oCACRkO,aAAalK,GAAG,CAACnU;gCACnB;4BACF;4BAEA,IAAIsiB,aAAa,OAAO;gCACtB,IAAIQ,SAASC,UAAU;oCACrBhF;gCACF,OAAO;oCACLC;gCACF;4BACF;wBACF;wBAEAa,UAAUqG,GAAG,CAACllB,MAAM;4BAClBqjB;4BACAX;4BACAC;4BACAI;4BACAD;4BACAD;4BACAI;4BACAC;4BACAyD,qBAAqBvb;4BACrB2Y,SAASI;4BACTyC,cAAcxb;4BACdyb,kBAAkBzb;4BAClB0b,qBAAqB1b;wBACvB;oBACF;gBACF;gBAGJ,MAAM2b,kBAAkB,MAAMnG;gBAC9B,MAAMoG,qBACJ,AAAC,MAAMvG,qCACNsG,mBAAmBA,gBAAgBT,cAAc;gBAEpD,MAAMW,cAAc;oBAClBpH,0BAA0B,MAAMyB;oBAChCxB,cAAc,MAAMyB;oBACpBxB;oBACA9C;oBACA+C,uBAAuBgH;gBACzB;gBAEA,OAAOC;YACT;YAEA,IAAIxJ,oBAAoBA,mBAAmByJ,cAAc;YACzDxL,IAAAA,wBAAgB,EAAC,iCAAiClW;YAElD,IAAIqa,0BAA0B;gBAC5Bxe,QAAQI,IAAI,CACV0lB,IAAAA,gBAAI,EAACC,IAAAA,kBAAM,EAAC,CAAC,SAAS,CAAC,KACrBA,IAAAA,kBAAM,EACJ,CAAC,qJAAqJ,CAAC;gBAG7J/lB,QAAQI,IAAI,CACV;YAEJ;YAEA,MAAM,EAAEojB,YAAY,EAAE,GAAGxgB;YAEzB,MAAMgjB,gCAA0C,EAAE;YAClD,IAAIvhB,wBAAwB;gBAC1BuhB,8BAA8BzgB,IAAI,CAChC9F,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAE,GAAG8N,wCAA6B,CAAC,GAAG,CAAC;gBAEnE,+DAA+D;gBAC/D,8FAA8F;gBAC9F,IAAI,CAACnH,eAAgBuT,CAAAA,uBAAuBC,qBAAoB,GAAI;oBAClEmJ,8BAA8BzgB,IAAI,CAChC9F,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB,CAAC,KAAK,EAAE8N,wCAA6B,CAAC,GAAG,CAAC;gBAGhD;YACF;YAEA,MAAMyV,8BAA8B9hB,cACjCU,UAAU,CAAC,kCACX0F,OAAO,CAAC;gBACP,MAAM2b,0BAAkD,CAAC;gBAEzD,KAAK,MAAM,CAACnI,KAAKoI,MAAM,IAAIzkB,OAAOC,OAAO,CACvCqB,OAAOmD,YAAY,CAACsd,aAAa,IAAI,CAAC,GACrC;oBACD,IAAI1F,OAAOoI,OAAO;wBAChBD,uBAAuB,CAACnI,IAAI,GAAGte,aAAI,CAACgG,QAAQ,CAAClG,SAAS4mB;oBACxD;gBACF;gBAEA,MAAMC,sBAAmD;oBACvDpiB,SAAS;oBACThB,QAAQ;wBACN,GAAGA,MAAM;wBACTqjB,YAAYtc;wBACZ,GAAIpK,QAAcE,cAAc,GAC5B;4BACEymB,UAAU;wBACZ,IACA,CAAC,CAAC;wBACN9C,cAAcA,eACV/jB,aAAI,CAACgG,QAAQ,CAAClG,SAASikB,gBACvBxgB,OAAOwgB,YAAY;wBACvBrd,cAAc;4BACZ,GAAGnD,OAAOmD,YAAY;4BACtBsd,eAAeyC;4BACfK,iBAAiB5mB,QAAcE,cAAc;4BAC7C2mB,uBAAuBhd;wBACzB;oBACF;oBACA5E,QAAQsD;oBACRue,gBAAgBhnB,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB4D;oBACrDhD,OAAO;wBACL+Q,2BAAe;wBACfxW,aAAI,CAACgG,QAAQ,CAAClG,SAASka;wBACvB8C,0BAAc;wBACdpb,8BAAkB;wBAClB1B,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEC,qCAAyB;wBACrDlD,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAE6d,+BAAmB;wBAC/C9gB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEgkB,qCAAyB,GAAG;2BACpD,CAACrd,cACD;4BACE5J,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChBikB,8CAAkC,GAAG;4BAEvCC,mCAAuB;yBACxB,GACD,EAAE;2BACFhiB,SACA;+BACM5B,OAAOmD,YAAY,CAAC8Y,GAAG,GACvB;gCACExf,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChBmkB,0CAA8B,GAAG;gCAEnCpnB,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChBmkB,0CAA8B,GAAG;6BAEpC,GACD,EAAE;4BACNpnB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEob,8BAAkB;4BAC9Cre,aAAI,CAACC,IAAI,CAACse,oCAAwB;4BAClCvB,8BAAkB;4BAClBhd,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB+d,qCAAyB,GAAG;4BAE9BhhB,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB+d,qCAAyB,GAAG;yBAE/B,GACD,EAAE;2BACF/T,YAAY,CAACrD,cACb;4BACEyd,gCAAoB,GAAG;4BACvBrnB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEokB,gCAAoB,GAAG;yBACpD,GACD,EAAE;wBACNC,yBAAa;wBACbtnB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEskB,8BAAkB,GAAG;wBACjDvnB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEskB,8BAAkB,GAAG;wBACjDlkB,iCAAqB;2BAClBkjB;qBACJ,CACEnkB,MAAM,CAAColB,wBAAW,EAClBllB,GAAG,CAAC,CAACkD,OAASxF,aAAI,CAACC,IAAI,CAACsD,OAAOzD,OAAO,EAAE0F;oBAC3CiiB,QAAQ,EAAE;gBACZ;gBAEA,OAAOd;YACT;YAEF,IAAI,CAACxK,gBAAgB;gBACnBqK,4BAA4BiB,MAAM,CAAC3hB,IAAI,CACrC9F,aAAI,CAACgG,QAAQ,CACXyC,KACAzI,aAAI,CAACC,IAAI,CACPD,aAAI,CAACkG,OAAO,CACVmB,QAAQC,OAAO,CACb,sDAGJ;YAIR;YAEA,MAAMogB,iBAAiBzW,UAAUoR,IAAI,CAAC,CAACxe,IACrCA,EAAEgC,QAAQ,CAACgL,8BAAmB;YAEhC,IAAI9L,oBAAoB;YAExB,IAAI2iB,gBAAgB;gBAClB,MAAM7E,aAAa,MAAMC,IAAAA,sCAA6B,EAAC;oBACrDF,gBAAgB;oBAChBF,cAAc1iB,aAAI,CAACC,IAAI,CAACwI,KAAKif;oBAC7BnkB;oBACA4B;oBACAmL,gBAAgB/M,OAAO+M,cAAc;oBACrCuB,OAAO;oBACP3S,MAAM;gBACR;gBAEA,IAAI2jB,WAAWE,mBAAmB,EAAE;oBAClCC;gBACF;gBAEA,IAAIH,WAAWI,OAAO,KAAK,UAAU;wBAIvBJ;oBAHZ9d,oBAAoB;oBACpB8Z,wBAAwBC,SAAS,CAAC,eAAe,GAAG;wBAClDmE,SAASJ,WAAWI,OAAO;wBAC3B0E,UAAU9E,EAAAA,yBAAAA,WAAW+E,UAAU,qBAArB/E,uBAAuB8E,QAAQ,KAAI;4BAC3C;gCACEE,QAAQ;gCACRC,gBAAgB;4BAClB;yBACD;oBACH;oBAEA,IAAIle,aAAa;wBACf,MAAMzI,cACJnB,aAAI,CAACC,IAAI,CACPH,SACA,UACA+B,SACAkmB,gDAAoC,GAEtClJ,wBAAwBC,SAAS,CAAC,eAAe,CAAC6I,QAAQ,IAAI,EAAE;oBAEpE;gBACF;YACF;YAEA,MAAM3kB,6BAA6BlD,SAAS+e;YAE5C,IAAI,CAAC1V,kBAAkB,CAACgR,oBAAoB;gBAC1CA,qBAAqB6B,IAAAA,sCAAkB,EAAC;oBACtCvT;oBACAlF;oBACAzD;oBACAmc,mBAAmBC,IAAAA,qCAA6B,EAAC6B;oBACjD9Y,aAAa;2BAAIA;qBAAY;oBAC7BP;oBACAyX;oBACAjC;oBACArV;oBACA+E,aAAa;gBACf,GAAGwS,KAAK,CAAC,CAAC/M;oBACR9O,QAAQqP,KAAK,CAACP;oBACd3E,QAAQe,IAAI,CAAC;gBACf;YACF;YAEA,IAAIgS,iBAAiBmE,IAAI,GAAG,KAAK7f,SAAS6f,IAAI,GAAG,GAAG;gBAClD,yDAAyD;gBACzD,+DAA+D;gBAC/DjL,eAAeW,UAAU,GAAGT,IAAAA,yBAAS,EAAC;uBACjC4G;uBACA1b;iBACJ,EAAEO,GAAG,CAAC,CAACpD;oBACN,OAAO8oB,IAAAA,8BAAc,EAAC9oB,MAAM2C;gBAC9B;YACF;YAEA,2DAA2D;YAC3D,MAAM6C,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMlE,cAAcoV,oBAAoBI;YAExD,iHAAiH;YACjH,8DAA8D;YAC9D,MAAMsR,oBACJ,CAAClJ,4BAA6B,CAAA,CAACG,yBAAyBjK,WAAU;YAEpE,IAAIsI,aAAaqE,IAAI,GAAG,GAAG;gBACzB,MAAMvS,MAAM,qBAQX,CARW,IAAII,MACd,CAAC,qCAAqC,EACpC8N,aAAaqE,IAAI,KAAK,IAAI,KAAK,IAChC,kDAAkD,EAAE;uBAAIrE;iBAAa,CACnEjb,GAAG,CAAC,CAAC4lB,KAAO,CAAC,KAAK,EAAEA,IAAI,EACxBjoB,IAAI,CACH,MACA,sFAAsF,CAAC,GAPjF,qBAAA;2BAAA;gCAAA;kCAAA;gBAQZ;gBACAoP,IAAIE,IAAI,GAAG;gBACX,MAAMF;YACR;YAEA,MAAM8Y,IAAAA,0BAAY,EAACroB,SAAS+B;YAE5B,IAAI0B,OAAOmD,YAAY,CAAC0hB,WAAW,EAAE;gBACnC,MAAMC,WACJhhB,QAAQ;gBAEV,MAAMihB,eAAe,MAAM,IAAIvN,QAAkB,CAACzT,SAASihB;oBACzDF,SACE,YACA;wBAAEra,KAAKhO,aAAI,CAACC,IAAI,CAACH,SAAS;oBAAU,GACpC,CAACuP,KAAK5J;wBACJ,IAAI4J,KAAK;4BACP,OAAOkZ,OAAOlZ;wBAChB;wBACA/H,QAAQ7B;oBACV;gBAEJ;gBAEA+gB,4BAA4B/gB,KAAK,CAACK,IAAI,IACjCwiB,aAAahmB,GAAG,CAAC,CAACzB,WACnBb,aAAI,CAACC,IAAI,CAACsD,OAAOzD,OAAO,EAAE,UAAUe;YAG1C;YAEA,MAAM2nB,WAAqC;gBACzC;oBACEzY,aAAa;oBACbC,iBAAiBzM,OAAOmD,YAAY,CAACuP,eAAe,GAAG,IAAI;gBAC7D;gBACA;oBACElG,aAAa;oBACbC,iBAAiBzM,OAAOmD,YAAY,CAAC0hB,WAAW,GAAG,IAAI;gBACzD;gBACA;oBACErY,aAAa;oBACbC,iBAAiBzM,OAAOmD,YAAY,CAAC+hB,iBAAiB,GAAG,IAAI;gBAC/D;gBACA;oBACE1Y,aAAa;oBACbC,iBAAiBzM,OAAOmD,YAAY,CAAC4P,GAAG,GAAG,IAAI;gBACjD;gBACA;oBACEvG,aAAa;oBACbC,iBAAiB0Y,IAAAA,kCAA0B,EAACnlB,UAAU,IAAI;gBAC5D;aACD;YACDuJ,UAAUY,MAAM,CACd8a,SAASlmB,GAAG,CAAC,CAACqmB;gBACZ,OAAO;oBACL1Y,WAAWC,iCAAyB;oBACpCC,SAASwY;gBACX;YACF;YAGF,MAAMxlB,iCACJrD,SACA0mB;YAGF,iDAAiD;YACjD,sDAAsD;YACtD,IAAIrd,kBAAkB,CAACS,aAAa;gBAClCnJ,KAAIiL,IAAI,CAAC;gBAET,MAAMhH,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMsG,IAAAA,gCAAe,EAAC;wBACpB7L;wBACAyD;oBACF;gBACF;YACJ;YAEA,MAAMuB,qBAAyC,MAAMxD,aACnDtB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE6d,+BAAmB;YAG1D,MAAMlf,oBAAuC;gBAC3C2C,SAAS;gBACTpC,QAAQ,CAAC;gBACTQ,eAAe,CAAC;gBAChBimB,gBAAgB,EAAE;gBAClBC,SAASnX;YACX;YAEA,MAAMoX,qBAA+B,EAAE;YAEvC,MAAM,EAAEvR,IAAI,EAAE,GAAGhU;YAEjB,MAAMwlB,wBAAwBpD,+BAAmB,CAACvjB,MAAM,CACtD,CAAClD,OACC2L,WAAW,CAAC3L,KAAK,IACjB2L,WAAW,CAAC3L,KAAK,CAACsO,UAAU,CAAC;YAEjCub,sBAAsBC,OAAO,CAAC,CAAC9pB;gBAC7B,IAAI,CAAC6C,SAASknB,GAAG,CAAC/pB,SAAS,CAAC6f,0BAA0B;oBACpD9Z,YAAYoO,GAAG,CAACnU;gBAClB;YACF;YAEA,MAAMgqB,cAAcH,sBAAsBljB,QAAQ,CAAC;YACnD,MAAMsjB,sBACJ,CAACD,eAAe,CAAChK,yBAAyB,CAACH;YAE7C,MAAMqK,gBAAgB;mBAAInkB;mBAAgBlD;aAAS;YACnD,MAAMsnB,iBAAiB1L,YAAYsL,GAAG,CAAC7T,4CAAgC;YACvE,MAAMkU,kBAAkBnU,aAAakU;YAErC,MAAMvP,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;YACd;YAEA,sDAAsD;YACtD,mBAAmB;YACnB,yBAAyB;YACzB,gCAAgC;YAChC,IACE,CAAChQ,iBACAqf,CAAAA,cAAc3c,MAAM,GAAG,KACtBwb,qBACAkB,uBACAhkB,MAAK,GACP;gBACA,MAAMokB,uBACJ7kB,cAAcU,UAAU,CAAC;gBAC3B,MAAMmkB,qBAAqBlkB,YAAY,CAAC;oBACtCmkB,IAAAA,8BAAsB,EACpB;2BACKJ;2BACAzkB,SAASY,KAAK,CAACnD,MAAM,CAAC,CAAClD,OAAS,CAACkqB,cAAcvjB,QAAQ,CAAC3G;qBAC5D,EACD6C,UACA,IAAI2U,IACFxF,MAAMC,IAAI,CAACuM,gBAAgBxb,OAAO,IAAII,GAAG,CACvC,CAAC,CAACpD,MAAMiD,OAAO;wBACb,OAAO;4BAACjD;4BAAMiD,OAAOG,GAAG,CAAC,CAACC,QAAUA,MAAME,QAAQ;yBAAE;oBACtD;oBAKN,MAAMmG,YAAY,AAACvB,QAAQ,aACxBwB,OAAO;oBAEV,MAAM4gB,eAAmC;wBACvC,GAAGlmB,MAAM;wBACT,sEAAsE;wBACtE,+BAA+B;wBAC/B,wEAAwE;wBACxE,6DAA6D;wBAC7DmmB,eAAe,CAACC;4BACd,+DAA+D;4BAC/D,iEAAiE;4BACjE,uEAAuE;4BACvE,UAAU;4BACV,EAAE;4BACF,6DAA6D;4BAC7D5nB,SAASinB,OAAO,CAAC,CAAC9pB;gCAChB,IAAI6X,IAAAA,qBAAc,EAAC7X,OAAO;oCACxB4pB,mBAAmBhjB,IAAI,CAAC5G;oCAExB,IAAIme,uBAAuB4L,GAAG,CAAC/pB,OAAO;wCACpC,iEAAiE;wCACjE,mBAAmB;wCACnB,IAAIqY,MAAM;4CACRoS,UAAU,CAAC,CAAC,CAAC,EAAEpS,KAAK0I,aAAa,GAAG/gB,MAAM,CAAC,GAAG;gDAC5CA;gDACA0qB,gBAAgB;4CAClB;wCACF,OAAO;4CACLD,UAAU,CAACzqB,KAAK,GAAG;gDACjBA;gDACA0qB,gBAAgB;4CAClB;wCACF;oCACF,OAAO;wCACL,iEAAiE;wCACjE,iCAAiC;wCACjC,OAAOD,UAAU,CAACzqB,KAAK;oCACzB;gCACF;4BACF;4BAEA,oEAAoE;4BACpE,cAAc;4BACdwe,gBAAgBsL,OAAO,CAAC,CAAC7mB,QAAQjD;gCAC/BiD,OAAO6mB,OAAO,CAAC,CAACzmB;oCACdonB,UAAU,CAACpnB,MAAME,QAAQ,CAAC,GAAG;wCAC3BvD;wCACA2qB,UAAUtnB,MAAMqiB,eAAe;oCACjC;gCACF;4BACF;4BAEA,IAAIqD,mBAAmB;gCACrB0B,UAAU,CAAC,OAAO,GAAG;oCACnBzqB,MAAM+V,cAAc,SAAS;gCAC/B;4BACF;4BAEA,IAAIkU,qBAAqB;gCACvBQ,UAAU,CAAC,OAAO,GAAG;oCACnBzqB,MAAM;gCACR;4BACF;4BAEA,wDAAwD;4BACxD,gDAAgD;4BAChDye,YAAYqL,OAAO,CAAC,CAAC7mB,QAAQogB;gCAC3B,MAAMiC,YAAY1G,kBAAkBgM,GAAG,CAACvH;gCACxC,MAAMwH,iBAAiBvF,CAAAA,6BAAAA,UAAWU,OAAO,MAAK;gCAE9C,MAAMnD,oBAA6ByC,YAC/BwF,IAAAA,2BAAsB,EAACzmB,OAAOmD,YAAY,CAAC4P,GAAG,EAAEkO,aAChD;gCAEJriB,OAAO6mB,OAAO,CAAC,CAACzmB;oCACd,8DAA8D;oCAC9D,wDAAwD;oCACxD,0DAA0D;oCAC1D,IACEA,MAAMyiB,kBAAkB,IACxBziB,MAAMyiB,kBAAkB,CAACvY,MAAM,GAAG,GAClC;wCACA;oCACF;oCAEAkd,UAAU,CAACpnB,MAAME,QAAQ,CAAC,GAAG;wCAC3BvD,MAAMqjB;wCACNsH,UAAUtnB,MAAMqiB,eAAe;wCAC/BqF,sBAAsB1nB,MAAMsiB,mBAAmB;wCAC/CqF,iBAAiBH;wCACjBI,WAAW;wCACXC,oBAAoBrI;wCACpBsI,wBAAwB,CAAC9nB,MAAM0iB,uBAAuB;oCACxD;gCACF;4BACF;4BAEA,IAAI1N,MAAM;gCACR,KAAK,MAAMrY,QAAQ;uCACd+F;uCACAlD;uCACCkmB,oBAAoB;wCAAC;qCAAO,GAAG,EAAE;uCACjCkB,sBAAsB;wCAAC;qCAAO,GAAG,EAAE;iCACxC,CAAE;oCACD,MAAMmB,QAAQvoB,SAASknB,GAAG,CAAC/pB;oCAC3B,MAAMolB,YAAYvN,IAAAA,qBAAc,EAAC7X;oCACjC,MAAMqrB,aAAaD,SAASjN,uBAAuB4L,GAAG,CAAC/pB;oCAEvD,KAAK,MAAMsrB,UAAUjT,KAAKzV,OAAO,CAAE;4CAMzB6nB;wCALR,+DAA+D;wCAC/D,IAAIW,SAAShG,aAAa,CAACiG,YAAY;wCACvC,MAAMxkB,aAAa,CAAC,CAAC,EAAEykB,SAAStrB,SAAS,MAAM,KAAKA,MAAM;wCAE1DyqB,UAAU,CAAC5jB,WAAW,GAAG;4CACvB7G,MAAMyqB,EAAAA,mBAAAA,UAAU,CAACzqB,KAAK,qBAAhByqB,iBAAkBzqB,IAAI,KAAIA;4CAChCurB,SAASD;4CACTZ,gBAAgBW;wCAClB;oCACF;oCAEA,IAAID,OAAO;wCACT,qDAAqD;wCACrD,OAAOX,UAAU,CAACzqB,KAAK;oCACzB;gCACF;4BACF;4BAEA,OAAOyqB;wBACT;oBACF;oBAEA,MAAM1gB,SAASjJ,aAAI,CAACC,IAAI,CAACH,SAAS;oBAClC,MAAM4qB,eAAe,MAAM9hB,UACzBH,KACA;wBACEM,YAAY0gB;wBACZ/gB;wBACAM,QAAQ;wBACRF,aAAa;wBACbS;wBACAC;wBACAjE,OAAO6jB;wBACPngB;wBACA0hB,eAAe;wBACf9iB,YAAYpB,mBAAmBgjB;oBACjC,GACA/kB;oBAGF,sDAAsD;oBACtD,IAAI,CAACgmB,cAAc;oBAEnB,MAAME,kBAAkB,CAACroB;4BACKmoB;wBAA5B,MAAM1E,uBAAsB0E,2BAAAA,aAAaG,MAAM,CAACf,GAAG,CACjDvnB,MAAME,QAAQ,sBADYioB,yBAEzB1E,mBAAmB;wBAEtB,kEAAkE;wBAClE,+DAA+D;wBAC/D,sBAAsB;wBACtB,IACEA,uBACA,CAACzjB,MAAM0iB,uBAAuB,IAC9B1iB,MAAMuiB,YAAY,KAAKO,sBAAY,CAACE,SAAS,EAC7C;4BACA,OAAOF,sBAAY,CAACC,sBAAsB;wBAC5C;wBAEA,4DAA4D;wBAC5D,6BAA6B;wBAC7B,IAAI,CAAC/iB,MAAMuiB,YAAY,EAAE;4BACvB,OAAOO,sBAAY,CAACyF,SAAS;wBAC/B;wBAEA,OAAOvoB,MAAMuiB,YAAY;oBAC3B;oBAEA,MAAMiG,kBAAkB,CACtBC,YACAC,oBAAgC,KAAK;4BAGnCP;wBADF,MAAMQ,gBACJR,2BAAAA,aAAaG,MAAM,CAACf,GAAG,CAACkB,gCAAxBN,yBAAqCQ,YAAY;wBAEnD,IAAI,CAACA,cAAc;4BACjB,OAAO;gCAAEzG,YAAYwG;gCAAmBE,QAAQ7gB;4BAAU;wBAC5D;wBAEA,IACE4gB,aAAazG,UAAU,KAAK,SAC5ByG,aAAazG,UAAU,GAAG,KAC1ByG,aAAaC,MAAM,KAAK7gB,WACxB;4BACA,OAAO;gCACLma,YAAYyG,aAAazG,UAAU;gCACnC0G,QAAQ5nB,OAAO6nB,UAAU;4BAC3B;wBACF;wBAEA,OAAOF;oBACT;oBAEA,IAAI3hB,eAAemB,QAAQC,GAAG,CAAC0gB,sBAAsB,KAAK,KAAK;wBAC7DC,IAAAA,oCAAkB,EAACZ;oBACrB;oBAEAa,IAAAA,qDAA+B,EAAC;wBAC9BzrB,SAASyD,OAAOzD,OAAO;wBACvB0rB,QAAQ;4BACNxgB;+BACG0f,aAAae,2BAA2B,CAACC,MAAM;yBACnD;oBACH;oBAEA9pB,kBAAkBgnB,cAAc,GAAG1X,MAAMC,IAAI,CAC3CuZ,aAAaiB,gBAAgB;oBAG/B,2CAA2C;oBAC3C,KAAK,MAAMzsB,QAAQ+F,YAAa;wBAC9B,MAAM2mB,eAAeC,IAAAA,oBAAW,EAAC3sB,MAAMY,SAASwK,WAAW;wBAC3D,MAAMvJ,YAAE,CAAC+qB,MAAM,CAACF;oBAClB;oBAEAjO,YAAYqL,OAAO,CAAC,CAACzE,mBAAmBhC;4BAWbxE;wBAVzB,MAAM7e,OAAO0e,mBAAmBkM,GAAG,CAACvH;wBACpC,IAAI,CAACrjB,MAAM,MAAM,qBAAoC,CAApC,IAAI6sB,8BAAc,CAAC,mBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAmC;wBAEpD,MAAMvH,YAAY1G,kBAAkBgM,GAAG,CAACvH;wBACxC,IAAI,CAACiC,WAAW,MAAM,qBAA0C,CAA1C,IAAIuH,8BAAc,CAAC,yBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyC;wBAE/D,IAAIC,oBACFxH,UAAUC,UAAU,KAAK,KACzBsG,gBAAgB7rB,MAAMulB,UAAU,KAAK;wBAEvC,IAAIuH,uBAAqBjO,iBAAAA,UAAU+L,GAAG,CAAC5qB,0BAAd6e,eAAqBkE,QAAQ,GAAE;4BACtD,uEAAuE;4BACvE,qFAAqF;4BACrFlE,UAAUqG,GAAG,CAACllB,MAAM;gCAClB,GAAI6e,UAAU+L,GAAG,CAAC5qB,KAAK;gCACvB+iB,UAAU;gCACVD,OAAO;4BACT;wBACF;wBAEA,MAAMiK,oBAAoBC,IAAAA,gCAAe,EAAC3J;wBAE1C,kEAAkE;wBAClE,yBAAyB;wBACzB,MAAMR,oBACJ,CAACkK,qBACDjC,IAAAA,2BAAsB,EAACzmB,OAAOmD,YAAY,CAAC4P,GAAG,EAAEkO,aAC5C,OACAla;wBAEN,MAAM6hB,sBACJ,uEAAuE;wBACvE5oB,OAAO6oB,eAAe,IAAIC,oCAA6B;wBAEzD,0FAA0F;wBAC1F,4CAA4C;wBAC5C,MAAMC,YAAwB;4BAC5B;gCAAEC,MAAM;gCAAUjO,KAAKkO,+BAAa;4BAAC;4BACrC;gCACED,MAAM;gCACNjO,KAAK;gCACLoI,OAAO;4BACT;4BACA,iGAAiG;4BACjG,iGAAiG;+BAC7F3E,oBACA;gCACE;oCACEwK,MAAM;oCACNjO,KAAK;oCACLoI,OAAOyF;gCACT;6BACD,GACD,EAAE;yBACP;wBAED,mEAAmE;wBACnE,6DAA6D;wBAC7D,mEAAmE;wBACnE,8DAA8D;wBAC9D,2BAA2B;wBAC3B,MAAMM,0BAA8C,EAAE;wBACtD,MAAMC,2BAA+C,EAAE;wBAEvD,mEAAmE;wBACnE,iEAAiE;wBACjE,+DAA+D;wBAC/D,iEAAiE;wBACjE,mDAAmD;wBACnD,MAAMC,iCAAqD,EAAE;wBAC7D,MAAMC,+BAAmD,EAAE;wBAC3D,KAAK,MAAMC,oBAAoBtI,kBAAmB;4BAChD,IACEsI,iBAAiBhI,mBAAmB,IACpCgI,iBAAiBhI,mBAAmB,CAACpY,MAAM,GAAG,GAC9C;gCACAkgB,+BAA+B7mB,IAAI,CAAC+mB;4BACtC,OAAO;gCACLD,6BAA6B9mB,IAAI,CAAC+mB;4BACpC;wBACF;wBAEA,MAAMC,+BAA+BC,IAAAA,+BAAe,EAClDJ,gCACA,CAACE,mBAAqBA,iBAAiBpqB,QAAQ;wBAEjD,MAAMuqB,6BAA6BD,IAAAA,+BAAe,EAChDH,8BACA,CAACC,mBAAqBA,iBAAiBpqB,QAAQ;wBAGjD8hB,oBAAoB;+BACfyI;+BACAF;yBACJ;wBAED,KAAK,MAAMD,oBAAoBtI,kBAAmB;4BAChD,IACExC,qBACA8K,iBAAiBhI,mBAAmB,IACpCgI,iBAAiBhI,mBAAmB,CAACpY,MAAM,GAAG,GAC9C;gCACA,6DAA6D;gCAC7D,8BAA8B;gCAC9BigB,yBAAyB5mB,IAAI,CAAC+mB;4BAChC,OAAO;gCACL,4DAA4D;gCAC5D,uCAAuC;gCACvCJ,wBAAwB3mB,IAAI,CAAC+mB;4BAC/B;wBACF;wBAEA,gCAAgC;wBAChC,KAAK,MAAMtqB,SAASkqB,wBAAyB;4BAC3C,IAAI1V,IAAAA,qBAAc,EAAC7X,SAASqD,MAAME,QAAQ,KAAKvD,MAAM;4BAErD,MAAM,EACJud,WAAW,CAAC,CAAC,EACbuJ,mBAAmB,EACnBiH,YAAY,EACb,GAAGvC,aAAaG,MAAM,CAACf,GAAG,CAACvnB,MAAME,QAAQ,KAAK,CAAC;4BAEhD,MAAMyoB,eAAeH,gBACnBxoB,MAAME,QAAQ,EACd+hB,UAAUC,UAAU;4BAGtB1G,UAAUqG,GAAG,CAAC7hB,MAAME,QAAQ,EAAE;gCAC5B,GAAIsb,UAAU+L,GAAG,CAACvnB,MAAME,QAAQ,CAAC;gCACjCwqB;gCACAjH;gCACAH,qBAAqBqF;4BACvB;4BAEA,uEAAuE;4BACvEnN,UAAUqG,GAAG,CAACllB,MAAM;gCAClB,GAAI6e,UAAU+L,GAAG,CAAC5qB,KAAK;gCACvB+tB;gCACAjH;gCACAH,qBAAqBqF;4BACvB;4BAEA,IAAIA,aAAazG,UAAU,KAAK,GAAG;gCACjC,MAAMyI,kBAAkBvL,IAAAA,oCAAiB,EAACpf,MAAME,QAAQ;gCAExD,IAAI0qB;gCACJ,IAAIlB,mBAAmB;oCACrBkB,YAAY;gCACd,OAAO;oCACLA,YAAYntB,aAAI,CAACotB,KAAK,CAACntB,IAAI,CAAC,GAAGitB,kBAAkB7U,qBAAU,EAAE;gCAC/D;gCAEA,IAAIgV;gCACJ,6DAA6D;gCAC7D,6DAA6D;gCAC7D,6DAA6D;gCAC7D,uBAAuB;gCACvB,IAAI,CAACpB,qBAAqB7V,iBAAiB;oCACzCiX,oBAAoBrtB,aAAI,CAACotB,KAAK,CAACntB,IAAI,CACjC,GAAGitB,kBAAkB3U,8BAAmB,EAAE;gCAE9C;gCAEA,MAAM+U,OAAOC,IAAAA,mBAAW,EAAC9Q;gCACzB,MAAM+Q,SACJjrB,MAAME,QAAQ,KAAKgrB,sCAA0B,GACzC,MACAH,KAAKE,MAAM;gCAEjB5rB,kBAAkBO,MAAM,CAACI,MAAME,QAAQ,CAAC,GAAG;oCACzCirB,eAAeF;oCACfG,gBAAgBL,KAAKrhB,OAAO;oCAC5B2hB,eAAexX,kBACX2L,oBACE8L,4BAAa,CAACC,gBAAgB,GAC9BD,4BAAa,CAACE,MAAM,GACtBzjB;oCACJ0jB,iBAAiBjM;oCACjBkM,uBAAuB3B;oCACvB4B,0BAA0BhD,aAAazG,UAAU;oCACjD0J,sBAAsBjD,aAAaC,MAAM;oCACzC9oB,UAAUnD;oCACViuB;oCACAE;oCACAe,aAAazvB;gCACf;4BACF,OAAO;gCACLqtB,oBAAoB;gCACpB,8DAA8D;gCAC9D,oBAAoB;gCACpBjO,UAAUqG,GAAG,CAAC7hB,MAAME,QAAQ,EAAE;oCAC5B,GAAIsb,UAAU+L,GAAG,CAACvnB,MAAME,QAAQ,CAAC;oCACjCuf,OAAO;oCACPC,UAAU;gCACZ;4BACF;wBACF;wBAEA,IAAI,CAAC+J,qBAAqBjV,IAAAA,qBAAc,EAAC7X,OAAO;4BAC9C,iEAAiE;4BACjE,0DAA0D;4BAC1D,sBAAsB;4BACtB,IAAI,CAAC6iB,mBAAmB;gCACtB2K,yBAAyB5mB,IAAI,CAAC;oCAC5B6e,QAAQ,CAAC;oCACTliB,UAAUvD;oCACV0lB,iBAAiB1lB;oCACjB2lB,qBAAqB,EAAE;oCACvBC,cACEjH,cAAciM,GAAG,CAACvH,oBAClB8C,sBAAY,CAACyF,SAAS;oCACxB9F,oBAAoB,EAAE;oCACtBC,yBAAyB;gCAC3B;4BACF;4BAEA,KAAK,MAAM1iB,SAASmqB,yBAA0B;oCAG3BhC,0BAmKMnoB;gCArKvB,MAAM2qB,kBAAkBvL,IAAAA,oCAAiB,EAACpf,MAAME,QAAQ;gCAExD,MAAMga,YAAWiO,2BAAAA,aAAaG,MAAM,CAACf,GAAG,CACtCvnB,MAAME,QAAQ,sBADCioB,yBAEdjO,QAAQ;gCAEX,MAAMyO,eAAeH,gBAAgBxoB,MAAME,QAAQ;gCAEnD,IAAI0qB,YAA2B;gCAC/B,IAAI,CAAClB,mBAAmB;oCACtBkB,YAAYntB,aAAI,CAACotB,KAAK,CAACntB,IAAI,CAAC,GAAGitB,kBAAkB7U,qBAAU,EAAE;gCAC/D;gCAEA,IAAIgV;gCACJ,IAAIgB,eAAe1X,eAAehU,aAAa,CAAC0f,IAAI,CAClD,CAACjL,IAAMA,EAAElY,IAAI,KAAKqD,MAAME,QAAQ;gCAElC,IAAI,CAACwpB,qBAAqB7V,iBAAiB;oCACzCiX,oBAAoBrtB,aAAI,CAACotB,KAAK,CAACntB,IAAI,CACjC,GAAGitB,kBAAkB3U,8BAAmB,EAAE;oCAG5C,4DAA4D;oCAC5D,2DAA2D;oCAC3D,6DAA6D;oCAC7D,uBAAuB;oCACvB,IAAI,CAAC8V,cAAc;wCACjBA,eAAepvB,YAAYsD,MAAME,QAAQ,EAAEvD;wCAC3CuX,YAAY2N,GAAG,CAAC7hB,MAAME,QAAQ,EAAEvD;wCAEhC,yDAAyD;wCACzD,wBAAwB;wCACxBmvB,aAAaC,mBAAmB,GAAG;wCAEnC,4DAA4D;wCAC5D,wBAAwB;wCACxB3rB,cAAcmD,IAAI,CAACuoB;oCACrB;gCACF;gCAEA,IACE,CAACpC,qBACAxP,CAAAA,CAAAA,4BAAAA,SAAU8R,YAAY,KACpBhsB,MAAMyiB,kBAAkB,IACvBziB,MAAMyiB,kBAAkB,CAACvY,MAAM,GAAG,CAAC,GACvC;oCACA,2DAA2D;oCAC3D,6DAA6D;oCAC7D,iBAAiB;oCACjB,IAAI,CAAC4hB,cAAc;wCACjBA,eAAe1rB,cAAc0f,IAAI,CAAC,CAACjL,IAAMA,EAAElY,IAAI,KAAKA;wCAEpD,wDAAwD;wCACxD,IAAI,CAACmvB,cAAc;4CACjB,MAAM,qBAA6C,CAA7C,IAAItC,8BAAc,CAAC,4BAAnB,qBAAA;uDAAA;4DAAA;8DAAA;4CAA4C;wCACpD;oCACF;oCAEA,IAAItP,4BAAAA,SAAU8R,YAAY,EAAE;wCAC1B,MAAMC,kBAAkB/R,SAAS8R,YAAY,CAAClM,IAAI,CAAC,CAACoM,OAClDA,KAAKC,QAAQ,CAAC;wCAEhB,IAAI,CAACF,iBAAiB;4CACpB,MAAM,qBAAoD,CAApD,IAAI/e,MAAM,CAAC,uCAAuC,CAAC,GAAnD,qBAAA;uDAAA;4DAAA;8DAAA;4CAAmD;wCAC3D;wCAEA,kDAAkD;wCAClD,iDAAiD;wCACjD,0CAA0C;wCAC1C,MAAMkf,wBAAwBC,IAAAA,4DAA6B,EACzDrsB,MAAME,QAAQ,EACd+rB;wCAGFG,sBAAsBjvB,MAAM,GAC1BivB,sBAAsBjvB,MAAM,CAACqE,OAAO,CAClC,8BACA,CAAC,6DAA6D,CAAC;wCAEnE4qB,sBAAsBE,WAAW,GAC/BF,sBAAsBE,WAAW,CAAC9qB,OAAO,CACvC,yBACA;wCAEJsqB,aAAaS,yBAAyB,KAAK,EAAE;wCAC7CT,aAAaS,yBAAyB,CAAChpB,IAAI,CACzC6oB;oCAEJ,OAOK,IACHpsB,MAAMyiB,kBAAkB,IACxBziB,MAAMyiB,kBAAkB,CAACvY,MAAM,GAAG,GAClC;wCACA4hB,aAAaU,qBAAqB,GAAG;wCACrCV,aAAaS,yBAAyB,GAAG;4CACvCE,IAAAA,mEAAoC,EAClCX,aAAanvB,IAAI,EACjB,2DAA2D;4CAC3D,2DAA2D;4CAC3D,4DAA4D;4CAC5D;yCAEH;oCACH;gCACF;gCAEA6e,UAAUqG,GAAG,CAAC7hB,MAAME,QAAQ,EAAE;oCAC5B,GAAIsb,UAAU+L,GAAG,CAACvnB,MAAME,QAAQ,CAAC;oCACjCwsB,mBAAmB;oCACnB,gEAAgE;oCAChE,2CAA2C;oCAC3ChC,cAAclL;gCAChB;gCAEA,MAAM+C,eAAe8F,gBAAgBroB;gCAErC,+DAA+D;gCAC/D,+DAA+D;gCAC/D,oDAAoD;gCACpD,iDAAiD;gCACjD,MAAM2sB,uBACJnN,qBAAqB+C,iBAAiBO,sBAAY,CAACE,SAAS,GACxD2F,eACA5gB;gCAEN,MAAMiC,WAAqB4iB,IAAAA,qCAA2B,EACpDrK,cACAviB,MAAME,QAAQ;gCAGhB,MAAM6qB,OACJ7Q,YACAsF,qBACA+C,iBAAiBO,sBAAY,CAACE,SAAS,GACnCgI,IAAAA,mBAAW,EAAC9Q,YACZ,CAAC;gCAEP7a,kBAAkBe,aAAa,CAACJ,MAAME,QAAQ,CAAC,GAAG;oCAChDurB,iBAAiBjM;oCACjB6L,eAAexX,kBACX2L,oBACE8L,4BAAa,CAACC,gBAAgB,GAC9BD,4BAAa,CAACE,MAAM,GACtBzjB;oCACJ2jB,uBAAuB3B;oCACvBltB,YAAYI,IAAAA,qCAAmB,EAC7BH,IAAAA,8BAAkB,EAACkD,MAAME,QAAQ,EAAE;wCACjCnD,iBAAiB;oCACnB,GAAGG,EAAE,CAACC,MAAM;oCAEdytB;oCACA5gB;oCACA6iB,kBAAkB,EAAEF,wCAAAA,qBAAsBzK,UAAU;oCACpD4K,cAAc,EAAEH,wCAAAA,qBAAsB/D,MAAM;oCAC5CmE,gBAAgBhC,KAAKE,MAAM;oCAC3B+B,iBAAiBjC,KAAKrhB,OAAO;oCAC7B+Y,oBAAoBzY,WAChBhK,MAAMyiB,kBAAkB,GACxB1a;oCACJklB,qBAAqBjtB,EAAAA,6BAAAA,MAAMsiB,mBAAmB,qBAAzBtiB,2BAA2BkK,MAAM,IAClDvN,OACAoL;oCACJmlB,gBAAgB,CAACtC,YACb,OACA3tB,IAAAA,qCAAmB,EACjBH,IAAAA,8BAAkB,EAAC8tB,WAAW;wCAC5B7tB,iBAAiB;wCACjBowB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGlwB,EAAE,CAACC,MAAM;oCAElB2tB;oCACAuC,wBAAwB,CAACvC,oBACrB/iB,YACA9K,IAAAA,qCAAmB,EACjBH,IAAAA,8BAAkB,EAACguB,mBAAmB;wCACpC/tB,iBAAiB;wCACjBowB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGlwB,EAAE,CAACC,MAAM;oCAElB0uB,aAAazvB;gCACf;4BACF;wBACF;oBACF;oBAEA,MAAMkxB,mBAAmB,OACvBC,YACA5wB,MACAsG,MACA8kB,OACAyF,KACAC,oBAAoB,KAAK;wBAEzB,OAAOzG,qBACJnkB,UAAU,CAAC,sBACXC,YAAY,CAAC;4BACZG,OAAO,GAAGA,KAAK,CAAC,EAAEuqB,KAAK;4BACvB,MAAME,OAAOjwB,aAAI,CAACC,IAAI,CAACgJ,QAAQzD;4BAC/B,MAAM2N,WAAW0Y,IAAAA,oBAAW,EAC1BiE,YACAhwB,SACAwK,WACA;4BAGF,MAAM4lB,eAAelwB,aAAI,CACtBgG,QAAQ,CACPhG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,GACnCjD,aAAI,CAACC,IAAI,CACPD,aAAI,CAACC,IAAI,CACPkT,UACA,yDAAyD;4BACzD,4BAA4B;4BAC5B2c,WACGK,KAAK,CAAC,GACNC,KAAK,CAAC,KACN9tB,GAAG,CAAC,IAAM,MACVrC,IAAI,CAAC,OAEVuF,OAGHzB,OAAO,CAAC,OAAO;4BAElB,IACE,CAACumB,SACD,CACE,mDAAmD;4BACnD,kDAAkD;4BAEhD3E,CAAAA,+BAAmB,CAAC9f,QAAQ,CAAC3G,SAC7B,CAAC6pB,sBAAsBljB,QAAQ,CAAC3G,KAAI,GAGxC;gCACA8e,aAAa,CAAC9e,KAAK,GAAGgxB;4BACxB;4BAEA,MAAMG,OAAOrwB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEitB;4BAClD,MAAMI,aACJ1uB,kBAAkBgnB,cAAc,CAAC/iB,QAAQ,CAAC3G;4BAE5C,2DAA2D;4BAC3D,0DAA0D;4BAC1D,qBAAqB;4BACrB,IAAI,AAAC,CAAA,CAACqY,QAAQyY,iBAAgB,KAAM,CAACM,YAAY;gCAC/C,MAAMvvB,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAACmqB,OAAO;oCAAElqB,WAAW;gCAAK;gCACrD,MAAMpF,YAAE,CAACwvB,MAAM,CAACN,MAAMI;4BACxB,OAAO,IAAI9Y,QAAQ,CAAC+S,OAAO;gCACzB,wDAAwD;gCACxD,oDAAoD;gCACpD,OAAOtM,aAAa,CAAC9e,KAAK;4BAC5B;4BAEA,IAAIqY,MAAM;gCACR,IAAIyY,mBAAmB;gCAEvB,MAAMQ,YAAYtxB,SAAS,MAAMc,aAAI,CAACywB,OAAO,CAACjrB,QAAQ;gCACtD,MAAMkrB,sBAAsBR,aAAaC,KAAK,CAC5C,SAAS1jB,MAAM;gCAGjB,KAAK,MAAM+d,UAAUjT,KAAKzV,OAAO,CAAE;oCACjC,MAAM6uB,UAAU,CAAC,CAAC,EAAEnG,SAAStrB,SAAS,MAAM,KAAKA,MAAM;oCAEvD,IACEorB,SACA1oB,kBAAkBgnB,cAAc,CAAC/iB,QAAQ,CAAC8qB,UAC1C;wCACA;oCACF;oCAEA,MAAMC,sBAAsB5wB,aAAI,CAC7BC,IAAI,CACH,SACAuqB,SAASgG,WACT,8DAA8D;oCAC9D,+BAA+B;oCAC/BtxB,SAAS,MAAM,KAAKwxB,qBAErB3sB,OAAO,CAAC,OAAO;oCAElB,MAAM8sB,cAAc7wB,aAAI,CAACC,IAAI,CAC3BgJ,QACAuhB,SAASgG,WACTtxB,SAAS,MAAM,KAAKsG;oCAEtB,MAAMsrB,cAAc9wB,aAAI,CAACC,IAAI,CAC3BH,SACAmD,4BAAgB,EAChB2tB;oCAGF,IAAI,CAACtG,OAAO;wCACVtM,aAAa,CAAC2S,QAAQ,GAAGC;oCAC3B;oCACA,MAAM7vB,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAAC4qB,cAAc;wCACxC3qB,WAAW;oCACb;oCACA,MAAMpF,YAAE,CAACwvB,MAAM,CAACM,aAAaC;gCAC/B;4BACF;wBACF;oBACJ;oBAEA,eAAeC;wBACb,OAAOxH,qBACJnkB,UAAU,CAAC,gCACXC,YAAY,CAAC;4BACZ,MAAM4qB,OAAOjwB,aAAI,CAACC,IAAI,CACpBH,SACA,UACA,OACA;4BAEF,MAAM8wB,sBAAsB5wB,aAAI,CAC7BC,IAAI,CAAC,SAAS,YACd8D,OAAO,CAAC,OAAO;4BAElB,IAAIzD,IAAAA,cAAU,EAAC2vB,OAAO;gCACpB,MAAMlvB,YAAE,CAACqF,QAAQ,CACf6pB,MACAjwB,aAAI,CAACC,IAAI,CAACH,SAAS,UAAU8wB;gCAG/B,mEAAmE;gCACnE,yEAAyE;gCACzE,IAAIrZ,MAAM;oCACR,KAAK,MAAMiT,UAAUjT,KAAKzV,OAAO,CAAE;wCACjC,MAAM6uB,UAAU,CAAC,CAAC,EAAEnG,OAAO,IAAI,CAAC;wCAChCxM,aAAa,CAAC2S,QAAQ,GAAGC;oCAC3B;gCACF;gCAEA5S,aAAa,CAAC,OAAO,GAAG4S;4BAC1B;wBACF;oBACJ;oBAEA,oEAAoE;oBACpE,IAAItH,iBAAiB;wBACnB,MAAMyH;oBACR,OAAO;wBACL,sGAAsG;wBACtG,IAAI,CAAC9b,eAAe,CAACE,aAAa8S,mBAAmB;4BACnD,MAAM4H,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;wBAC3D;oBACF;oBAEA,IAAI1G,qBAAqB;wBACvB,MAAM0G,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;oBAC3D;oBAEA,KAAK,MAAM3wB,QAAQkqB,cAAe;wBAChC,MAAMkB,QAAQvoB,SAASknB,GAAG,CAAC/pB;wBAC3B,MAAM8xB,sBAAsB3T,uBAAuB4L,GAAG,CAAC/pB;wBACvD,MAAMolB,YAAYvN,IAAAA,qBAAc,EAAC7X;wBACjC,MAAM+xB,SAASzT,eAAeyL,GAAG,CAAC/pB;wBAClC,MAAMsG,OAAOmc,IAAAA,oCAAiB,EAACziB;wBAE/B,MAAMgyB,WAAWnT,UAAU+L,GAAG,CAAC5qB;wBAC/B,MAAMiyB,eAAezG,aAAa0G,MAAM,CAACtH,GAAG,CAAC5qB;wBAC7C,IAAIgyB,YAAYC,cAAc;4BAC5B,qBAAqB;4BACrB,IAAID,SAAS9O,aAAa,EAAE;gCAC1B8O,SAASnL,gBAAgB,GAAGmL,SAAS9O,aAAa,CAAC9f,GAAG,CACpD,CAAC6Q;oCACC,MAAM6H,WAAWmW,aAAaE,eAAe,CAACvH,GAAG,CAAC3W;oCAClD,IAAI,OAAO6H,aAAa,aAAa;wCACnC,MAAM,qBAAyC,CAAzC,IAAIvL,MAAM,iCAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAwC;oCAChD;oCAEA,OAAOuL;gCACT;4BAEJ;4BACAkW,SAASpL,YAAY,GAAGqL,aAAaE,eAAe,CAACvH,GAAG,CAAC5qB;wBAC3D;wBAEA,+DAA+D;wBAC/D,gEAAgE;wBAChE,YAAY;wBACZ,MAAMoyB,gBAAgB,CAAEhH,CAAAA,SAAShG,aAAa,CAAC0M,mBAAkB;wBAEjE,IAAIM,eAAe;4BACjB,MAAMzB,iBAAiB3wB,MAAMA,MAAMsG,MAAM8kB,OAAO;wBAClD;wBAEA,IAAI2G,UAAW,CAAA,CAAC3G,SAAUA,SAAS,CAAChG,SAAS,GAAI;4BAC/C,MAAMiN,UAAU,GAAG/rB,KAAK,IAAI,CAAC;4BAC7B,MAAMqqB,iBAAiB3wB,MAAMqyB,SAASA,SAASjH,OAAO;4BAEtD,IAAIA,OAAO;gCACT,MAAMuF,iBAAiB3wB,MAAMqyB,SAASA,SAASjH,OAAO;4BACxD;wBACF;wBAEA,IAAIA,OAAO;4BACT,yDAAyD;4BACzD,oDAAoD;4BACpD,IAAI,CAAChG,WAAW;gCACd,MAAMuL,iBAAiB3wB,MAAMA,MAAMsG,MAAM8kB,OAAO;gCAEhD,IAAI/S,MAAM;oCACR,+DAA+D;oCAC/D,KAAK,MAAMiT,UAAUjT,KAAKzV,OAAO,CAAE;wCACjC,MAAM0vB,aAAa,CAAC,CAAC,EAAEhH,SAAStrB,SAAS,MAAM,KAAKA,MAAM;wCAE1D,MAAMgsB,eAAeH,gBAAgByG;wCAErC5vB,kBAAkBO,MAAM,CAACqvB,WAAW,GAAG;4CACrCtD,0BAA0BhD,aAAazG,UAAU;4CACjD0J,sBAAsBjD,aAAaC,MAAM;4CACzC6C,iBAAiB1jB;4CACjBsjB,eAAetjB;4CACfjI,UAAU;4CACV8qB,WAAWntB,aAAI,CAACotB,KAAK,CAACntB,IAAI,CACxB,eACA4B,SACA,GAAG2D,KAAK,KAAK,CAAC;4CAEhB6nB,mBAAmB/iB;4CACnB8jB,aAAazvB;wCACf;oCACF;gCACF,OAAO;oCACL,MAAMusB,eAAeH,gBAAgB7rB;oCAErC0C,kBAAkBO,MAAM,CAACjD,KAAK,GAAG;wCAC/BgvB,0BAA0BhD,aAAazG,UAAU;wCACjD0J,sBAAsBjD,aAAaC,MAAM;wCACzC6C,iBAAiB1jB;wCACjBsjB,eAAetjB;wCACfjI,UAAU;wCACV8qB,WAAWntB,aAAI,CAACotB,KAAK,CAACntB,IAAI,CACxB,eACA4B,SACA,GAAG2D,KAAK,KAAK,CAAC;wCAEhB,6CAA6C;wCAC7C6nB,mBAAmB/iB;wCACnB8jB,aAAazvB;oCACf;gCACF;gCACA,IAAIuyB,UAAU;oCACZA,SAASrL,mBAAmB,GAAGkF,gBAAgB7rB;gCACjD;4BACF,OAAO;gCACL,oEAAoE;gCACpE,4CAA4C;gCAC5C,iEAAiE;gCACjE,yCAAyC;gCACzC,KAAK,MAAMqD,SAASmb,gBAAgBoM,GAAG,CAAC5qB,SAAS,EAAE,CAAE;oCACnD,MAAMuyB,WAAW9P,IAAAA,oCAAiB,EAACpf,MAAME,QAAQ;oCACjD,MAAMotB,iBACJ3wB,MACAqD,MAAME,QAAQ,EACdgvB,UACAnH,OACA,QACA;oCAEF,MAAMuF,iBACJ3wB,MACAqD,MAAME,QAAQ,EACdgvB,UACAnH,OACA,QACA;oCAGF,IAAI2G,QAAQ;wCACV,MAAMM,UAAU,GAAGE,SAAS,IAAI,CAAC;wCACjC,MAAM5B,iBACJ3wB,MACAqyB,SACAA,SACAjH,OACA,QACA;wCAEF,MAAMuF,iBACJ3wB,MACAqyB,SACAA,SACAjH,OACA,QACA;oCAEJ;oCAEA,MAAMY,eAAeH,gBAAgBxoB,MAAME,QAAQ;oCAEnDb,kBAAkBO,MAAM,CAACI,MAAME,QAAQ,CAAC,GAAG;wCACzCyrB,0BAA0BhD,aAAazG,UAAU;wCACjD0J,sBAAsBjD,aAAaC,MAAM;wCACzC6C,iBAAiB1jB;wCACjBsjB,eAAetjB;wCACfjI,UAAUnD;wCACViuB,WAAWntB,aAAI,CAACotB,KAAK,CAACntB,IAAI,CACxB,eACA4B,SACA,GAAG8f,IAAAA,oCAAiB,EAACpf,MAAME,QAAQ,EAAE,KAAK,CAAC;wCAE7C,6CAA6C;wCAC7C4qB,mBAAmB/iB;wCACnB8jB,aAAazvB;oCACf;oCAEA,IAAIuyB,UAAU;wCACZA,SAASrL,mBAAmB,GAAGqF;oCACjC;gCACF;4BACF;wBACF;oBACF;oBAEA,iCAAiC;oBACjC,MAAMnqB,YAAE,CAAC2wB,EAAE,CAACzoB,QAAQ;wBAAE9C,WAAW;wBAAMwrB,OAAO;oBAAK;oBACnD,MAAMxwB,cAAc6Y,mBAAmBgE;gBACzC;gBAEA,iEAAiE;gBACjE,0BAA0B;gBAC1BrH,eAAehU,aAAa,GAAGivB,IAAAA,wCAAwB,EACrDjvB,eACA,CAACJ,QAAW,CAAA;wBACV,kEAAkE;wBAClE,mEAAmE;wBACnEpD,YAAYsX,YAAYqT,GAAG,CAACvnB,MAAMrD,IAAI,KAAKqD,MAAMrD,IAAI;wBACrDA,MAAMqD,MAAMrD,IAAI;oBAClB,CAAA;gBAGF,qCAAqC;gBACrC,MAAMwF,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMlE,cAAcoV,oBAAoBI;YAC1D;YAEA,MAAMkb,mBAAmBjV,IAAAA,gBAAa,EAAC;YACvC,IAAIkV,qBAAqBlV,IAAAA,gBAAa,EAAC,CAAC,uBAAuB,CAAC;YAEhE,wCAAwC;YACxC6B,OAAOsT,GAAG;YAEV,MAAMC,cAActnB,QAAQiU,MAAM,CAACD;YACnC5R,UAAUY,MAAM,CACdukB,IAAAA,0BAAkB,EAACxhB,YAAY;gBAC7BiL,mBAAmBsW,WAAW,CAAC,EAAE;gBACjCE,iBAAiBjtB,YAAY2c,IAAI;gBACjCuQ,sBAAsBpwB,SAAS6f,IAAI;gBACnCwQ,sBAAsB3U,iBAAiBmE,IAAI;gBAC3CyQ,cACE5hB,WAAWhE,MAAM,GAChBxH,CAAAA,YAAY2c,IAAI,GAAG7f,SAAS6f,IAAI,GAAGnE,iBAAiBmE,IAAI,AAAD;gBAC1D0Q,cAAcrK;gBACdsK,oBACEvT,CAAAA,gCAAAA,aAAcnZ,QAAQ,CAAC,uBAAsB;gBAC/C2sB,eAAepmB,iBAAiBK,MAAM;gBACtCgmB,cAAcxmB,QAAQQ,MAAM;gBAC5BimB,gBAAgBvmB,UAAUM,MAAM,GAAG;gBACnCkmB,qBAAqB1mB,QAAQ7J,MAAM,CAAC,CAACgV,IAAW,CAAC,CAACA,EAAE6R,GAAG,EAAExc,MAAM;gBAC/DmmB,sBAAsBxmB,iBAAiBhK,MAAM,CAAC,CAACgV,IAAW,CAAC,CAACA,EAAE6R,GAAG,EAC9Dxc,MAAM;gBACTomB,uBAAuB1mB,UAAU/J,MAAM,CAAC,CAACgV,IAAW,CAAC,CAACA,EAAE6R,GAAG,EAAExc,MAAM;gBACnEqmB,iBAAiBrhB,oBAAoB,IAAI;gBACzC+B;gBACAyJ;gBACAC;gBACAC;gBACAC;YACF;YAGF,IAAIpT,8BAAgB,CAAC+oB,cAAc,EAAE;gBACnC,MAAM1kB,SAAS2kB,IAAAA,8BAAsB,EACnChpB,8BAAgB,CAAC+oB,cAAc,CAACE,MAAM;gBAExCnmB,UAAUY,MAAM,CAACW;gBACjBvB,UAAUY,MAAM,CACdwlB,IAAAA,4CAAoC,EAClClpB,8BAAgB,CAAC+oB,cAAc,CAACI,6BAA6B;gBAGjE,MAAMC,kBAAkBppB,8BAAgB,CAAC+oB,cAAc,CAACK,eAAe;gBAEvE,KAAK,MAAM,CAAC9U,KAAKoI,MAAM,IAAIzkB,OAAOC,OAAO,CAACkxB,iBAAkB;oBAC1DtmB,UAAUY,MAAM,CACdslB,IAAAA,8BAAsB,EAAC;wBACrB;4BACEjjB,aAAauO;4BACbtO,iBAAiB0W;wBACnB;qBACD;gBAEL;YACF;YAEA,IAAI3kB,SAAS6f,IAAI,GAAG,KAAKzc,QAAQ;oBAmDpB5B;gBAlDXulB,mBAAmBE,OAAO,CAAC,CAACqK;oBAC1B,MAAMnG,kBAAkBvL,IAAAA,oCAAiB,EAAC0R;oBAC1C,MAAMlG,YAAYntB,aAAI,CAACotB,KAAK,CAACntB,IAAI,CAC/B,eACA4B,SACA,GAAGqrB,gBAAgB,KAAK,CAAC;oBAG3BtrB,kBAAkBe,aAAa,CAAC0wB,SAAS,GAAG;wBAC1Cj0B,YAAYI,IAAAA,qCAAmB,EAC7BH,IAAAA,8BAAkB,EAACg0B,UAAU;4BAC3B/zB,iBAAiB;wBACnB,GAAGG,EAAE,CAACC,MAAM;wBAEdsuB,iBAAiB1jB;wBACjBsjB,eAAetjB;wBACf6iB;wBACA5gB,UAAU+Q,yBAAyB2L,GAAG,CAACoK,YACnC,OACAhW,uBAAuB4L,GAAG,CAACoK,YACzB,GAAGnG,gBAAgB,KAAK,CAAC,GACzB;wBACNkC,oBAAoB9kB;wBACpB+kB,gBAAgB/kB;wBAChBklB,qBAAqBllB;wBACrB0a,oBAAoB1a;wBACpBmlB,gBAAgBjwB,IAAAA,qCAAmB,EACjCH,IAAAA,8BAAkB,EAAC8tB,WAAW;4BAC5B7tB,iBAAiB;4BACjBowB,eAAe;4BACfC,8BAA8B;wBAChC,GAAGlwB,EAAE,CAACC,MAAM;wBAEd,6CAA6C;wBAC7C2tB,mBAAmB/iB;wBACnBslB,wBAAwBtlB;wBACxB8jB,aAAazvB;oBACf;gBACF;gBAEAqL,8BAAgB,CAACspB,aAAa,GAAG5hB,aAAa4hB,aAAa;gBAC3DtpB,8BAAgB,CAACupB,mBAAmB,GAClChwB,OAAOmD,YAAY,CAAC6sB,mBAAmB;gBACzCvpB,8BAAgB,CAACwpB,2BAA2B,GAC1CjwB,OAAOmD,YAAY,CAAC8sB,2BAA2B;gBAEjD,MAAM/xB,uBAAuB3B,SAAS8B;gBACtC,MAAMD,uBAAuBC,mBAAmB;oBAC9C9B;oBACA+B;oBACAC,OAAO,GAAEyB,eAAAA,OAAOgU,IAAI,qBAAXhU,aAAazB,OAAO;gBAC/B;YACF,OAAO;gBACL,MAAML,uBAAuB3B,SAAS;oBACpCyE,SAAS;oBACTpC,QAAQ,CAAC;oBACTQ,eAAe,CAAC;oBAChBkmB,SAASnX;oBACTkX,gBAAgB,EAAE;gBACpB;YACF;YAEA,MAAMtlB,oBAAoBxD,SAASyD;YACnC,MAAMpC,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAAS2zB,yBAAa,GAAG;gBACrDlvB,SAAS;gBACTmvB,kBAAkB,OAAOnwB,OAAOmmB,aAAa,KAAK;gBAClDiK,qBAAqBpwB,OAAOqwB,aAAa,KAAK;gBAC9C3U,qBAAqBA,wBAAwB;YAC/C;YACA,MAAMle,YAAE,CAAC+qB,MAAM,CAAC9rB,aAAI,CAACC,IAAI,CAACH,SAAS+zB,yBAAa,GAAGzX,KAAK,CAAC,CAAC/M;gBACxD,IAAIA,IAAIE,IAAI,KAAK,UAAU;oBACzB,OAAOwL,QAAQzT,OAAO;gBACxB;gBACA,OAAOyT,QAAQwN,MAAM,CAAClZ;YACxB;YAEA,IAAIN,QAAQxL,OAAOmD,YAAY,CAAC+hB,iBAAiB,GAAG;gBAClD,MAAM/jB,cACHU,UAAU,CAAC,0BACXC,YAAY,CAAC;oBACZ,MAAMyuB,IAAAA,0CAAoB,EACxBrrB,KACAzI,aAAI,CAACC,IAAI,CAACH,SAASiD,oCAAwB;gBAE/C;YACJ;YAEA,MAAMoX;YAEN,IAAI2X,oBAAoB;gBACtBA,mBAAmB1L,cAAc;gBACjC0L,qBAAqBxnB;YACvB;YAEA,IAAIP,eAAe;gBACjBtJ,KAAIiL,IAAI,CACN,CAAC,yGAAyG,CAAC;YAE/G;YAEA,IAAInI,OAAO4c,MAAM,KAAK,UAAU;gBAC9B,MAAM3X,uBACJjF,QACAkF,KACAC,oBACAC,cACAjE;YAEJ;YAEA,IAAInB,OAAOmD,YAAY,CAACqtB,WAAW,EAAE;gBACnC,MAAMC,IAAAA,kCAAmB,EAAC;oBACxBvrB;oBACA3I;oBACAm0B,aAAapvB;oBACbE;oBACAC;oBACA+uB,aAAaxwB,OAAOmD,YAAY,CAACqtB,WAAW;oBAC5CpvB,UAAUA,SAASY,KAAK;oBACxBwN,aAAanO;oBACb+R;oBACA/U;oBACAkD;oBACA+Z;oBACAyT,cAAcrK;oBACd7kB,qBAAqBojB,4BAA4B/gB,KAAK;gBACxD;YACF;YAEA,IAAIlC,OAAO4c,MAAM,KAAK,cAAc;gBAClC,MAAM1b,yBACJC,eACA5E,SACA6E,UACAC,sBACAC,uBACA2hB,6BACA1hB,oBACAC,mBACAC,wBACAC,aACAC,gBACAC;YAEJ;YAEA,IAAI0sB,kBAAkBA,iBAAiBzL,cAAc;YACrD7lB,QAAQC,GAAG;YAEX,IAAI+I,aAAa;gBACf7E,cACGU,UAAU,CAAC,uBACX0F,OAAO,CAAC,IAAMopB,IAAAA,yBAAiB,EAAC;wBAAE/nB;wBAAWD;wBAAUD;oBAAQ;YACpE;YAEA,MAAMvH,cAAcU,UAAU,CAAC,mBAAmBC,YAAY,CAAC,IAC7D8uB,IAAAA,qBAAa,EAACxvB,UAAUoZ,WAAW;oBACjCqW,UAAUt0B;oBACV+B,SAASA;oBACToL;oBACAgb;oBACA3X,gBAAgB/M,OAAO+M,cAAc;oBACrC4N;oBACAD;oBACAnZ;oBACA+b,UAAUtd,OAAOmD,YAAY,CAACma,QAAQ;gBACxC;YAGF,MAAMnc,cACHU,UAAU,CAAC,mBACXC,YAAY,CAAC,IAAMyH,UAAU+C,KAAK;YAErC,MAAMiL;QACR;IACF,EAAE,OAAOuZ,GAAG;QACV,MAAMvnB,YAAmCwnB,oBAAY,CAACxK,GAAG,CAAC;QAC1D,IAAIhd,WAAW;YACbA,UAAUY,MAAM,CACd6mB,IAAAA,wBAAgB,EAAC;gBACf9Y,SAASa,uBAAuB1S;gBAChC4qB,WAAWC,yBAAyBJ;gBACpC3Y,mBAAmB5U,KAAKG,KAAK,CAAC,AAACiD,CAAAA,KAAKC,GAAG,KAAKF,cAAa,IAAK;YAChE;QAEJ;QACA,MAAMoqB;IACR,SAAU;QACR,kDAAkD;QAClD,MAAMK,yBAAoB,CAACC,GAAG;QAE9B,6DAA6D;QAC7D,MAAM/oB,IAAAA,qBAAc;QACpBC,IAAAA,4BAAuB;QAEvB,IAAI/B,kBAAkBM,cAAc;YAClCwqB,IAAAA,oBAAW,EAAC;gBACV9qB;gBACA+qB,MAAM;gBACNnY,YAAYjU;gBACZ3I,SAASsK,aAAatK,OAAO;gBAC7Bg1B,gBAAgBlrB;gBAChBmrB,MAAM;YACR;QACF;IACF;AACF;AAEA,SAAS/R;IACPviB,KAAImP,KAAK,CACP,CAAC,0MAA0M,CAAC;IAE9MlF,QAAQe,IAAI,CAAC;AACf;AAEA,SAAS6Q,uBAAuB1S,WAAoB;IAClD,IAAIA,aAAa;QACf,OAAO;IACT;IAEA,IAAIc,QAAQC,GAAG,CAACqqB,WAAW,EAAE;QAC3B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASP,yBAAyBplB,GAAY;IAC5C,MAAME,OAAO0lB,IAAAA,yCAAoB,EAAC5lB;IAClC,IAAIE,QAAQ,MAAM;QAChB,OAAOA;IACT;IAEA,IAAIF,eAAeI,SAAS,UAAUJ,OAAO,OAAOA,IAAIE,IAAI,KAAK,UAAU;QACzE,OAAOF,IAAIE,IAAI;IACjB;IAEA,IAAIF,eAAeI,OAAO;QACxB,OAAOJ,IAAI6lB,IAAI;IACjB;IAEA,OAAO;AACT", "ignoreList": [0]}