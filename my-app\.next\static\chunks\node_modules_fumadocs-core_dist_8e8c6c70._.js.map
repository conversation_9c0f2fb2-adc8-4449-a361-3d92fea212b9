{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js"], "sourcesContent": ["// src/utils/remove-undefined.ts\nfunction removeUndefined(value, deep = false) {\n  const obj = value;\n  for (const key of Object.keys(obj)) {\n    if (obj[key] === void 0) delete obj[key];\n    if (deep && typeof obj[key] === \"object\" && obj[key] !== null) {\n      removeUndefined(obj[key], deep);\n    } else if (deep && Array.isArray(obj[key])) {\n      obj[key].forEach((v) => removeUndefined(v, deep));\n    }\n  }\n  return value;\n}\n\nexport {\n  removeUndefined\n};\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;AAChC,SAAS,gBAAgB,KAAK;QAAE,OAAA,iEAAO;IACrC,MAAM,MAAM;IACZ,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,KAAM;QAClC,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,CAAC,IAAI;QACxC,IAAI,QAAQ,OAAO,GAAG,CAAC,IAAI,KAAK,YAAY,GAAG,CAAC,IAAI,KAAK,MAAM;YAC7D,gBAAgB,GAAG,CAAC,IAAI,EAAE;QAC5B,OAAO,IAAI,QAAQ,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG;YAC1C,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAM,gBAAgB,GAAG;QAC7C;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js"], "sourcesContent": ["// src/search/shared.ts\nfunction escapeRegExp(input) {\n  return input.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction buildRegexFromQuery(q) {\n  const trimmed = q.trim();\n  if (trimmed.length === 0) return null;\n  const terms = Array.from(\n    new Set(\n      trimmed.split(/\\s+/).map((t) => t.trim()).filter(Boolean)\n    )\n  );\n  if (terms.length === 0) return null;\n  const escaped = terms.map(escapeRegExp).join(\"|\");\n  return new RegExp(`(${escaped})`, \"gi\");\n}\nfunction createContentHighlighter(query) {\n  const regex = typeof query === \"string\" ? buildRegexFromQuery(query) : query;\n  return {\n    highlight(content) {\n      if (!regex) return [{ type: \"text\", content }];\n      const out = [];\n      let i = 0;\n      for (const match of content.matchAll(regex)) {\n        if (i < match.index) {\n          out.push({\n            type: \"text\",\n            content: content.substring(i, match.index)\n          });\n        }\n        out.push({\n          type: \"text\",\n          content: match[0],\n          styles: {\n            highlight: true\n          }\n        });\n        i = match.index + match[0].length;\n      }\n      if (i < content.length) {\n        out.push({\n          type: \"text\",\n          content: content.substring(i)\n        });\n      }\n      return out;\n    }\n  };\n}\n\nexport {\n  createContentHighlighter\n};\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AACvB,SAAS,aAAa,KAAK;IACzB,OAAO,MAAM,OAAO,CAAC,uBAAuB;AAC9C;AACA,SAAS,oBAAoB,CAAC;IAC5B,MAAM,UAAU,EAAE,IAAI;IACtB,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IACjC,MAAM,QAAQ,MAAM,IAAI,CACtB,IAAI,IACF,QAAQ,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,MAAM,CAAC;IAGrD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAC/B,MAAM,UAAU,MAAM,GAAG,CAAC,cAAc,IAAI,CAAC;IAC7C,OAAO,IAAI,OAAO,AAAC,IAAW,OAAR,SAAQ,MAAI;AACpC;AACA,SAAS,yBAAyB,KAAK;IACrC,MAAM,QAAQ,OAAO,UAAU,WAAW,oBAAoB,SAAS;IACvE,OAAO;QACL,WAAU,OAAO;YACf,IAAI,CAAC,OAAO,OAAO;gBAAC;oBAAE,MAAM;oBAAQ;gBAAQ;aAAE;YAC9C,MAAM,MAAM,EAAE;YACd,IAAI,IAAI;YACR,KAAK,MAAM,SAAS,QAAQ,QAAQ,CAAC,OAAQ;gBAC3C,IAAI,IAAI,MAAM,KAAK,EAAE;oBACnB,IAAI,IAAI,CAAC;wBACP,MAAM;wBACN,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,KAAK;oBAC3C;gBACF;gBACA,IAAI,IAAI,CAAC;oBACP,MAAM;oBACN,SAAS,KAAK,CAAC,EAAE;oBACjB,QAAQ;wBACN,WAAW;oBACb;gBACF;gBACA,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;YACnC;YACA,IAAI,IAAI,QAAQ,MAAM,EAAE;gBACtB,IAAI,IAAI,CAAC;oBACP,MAAM;oBACN,SAAS,QAAQ,SAAS,CAAC;gBAC7B;YACF;YACA,OAAO;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js"], "sourcesContent": ["import {\n  removeUndefined\n} from \"./chunk-KAOEMCTI.js\";\nimport {\n  createContentHighlighter\n} from \"./chunk-CNWEGOUF.js\";\nimport \"./chunk-JSBRDJBE.js\";\n\n// src/search/client/orama-cloud.ts\nasync function searchDocs(query, options) {\n  const highlighter = createContentHighlighter(query);\n  const list = [];\n  const { index = \"default\", client, params: extraParams = {}, tag } = options;\n  if (index === \"crawler\") {\n    const result2 = await client.search({\n      ...extraParams,\n      term: query,\n      where: {\n        category: tag ? {\n          eq: tag.slice(0, 1).toUpperCase() + tag.slice(1)\n        } : void 0,\n        ...extraParams.where\n      },\n      limit: 10\n    });\n    if (!result2) return list;\n    for (const hit of result2.hits) {\n      const doc = hit.document;\n      list.push(\n        {\n          id: hit.id,\n          type: \"page\",\n          content: doc.title,\n          contentWithHighlights: highlighter.highlight(doc.title),\n          url: doc.path\n        },\n        {\n          id: \"page\" + hit.id,\n          type: \"text\",\n          content: doc.content,\n          contentWithHighlights: highlighter.highlight(doc.content),\n          url: doc.path\n        }\n      );\n    }\n    return list;\n  }\n  const params = {\n    ...extraParams,\n    term: query,\n    where: removeUndefined({\n      tag,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 7,\n      ...extraParams.groupBy\n    }\n  };\n  const result = await client.search(params);\n  if (!result || !result.groups) return list;\n  for (const item of result.groups) {\n    let addedHead = false;\n    for (const hit of item.result) {\n      const doc = hit.document;\n      if (!addedHead) {\n        list.push({\n          id: doc.page_id,\n          type: \"page\",\n          content: doc.title,\n          contentWithHighlights: highlighter.highlight(doc.title),\n          url: doc.url\n        });\n        addedHead = true;\n      }\n      list.push({\n        id: doc.id,\n        content: doc.content,\n        contentWithHighlights: highlighter.highlight(doc.content),\n        type: doc.content === doc.section ? \"heading\" : \"text\",\n        url: doc.section_id ? `${doc.url}#${doc.section_id}` : doc.url\n      });\n    }\n  }\n  return list;\n}\nexport {\n  searchDocs\n};\n"], "names": [], "mappings": ";;;AAAA;AAGA;AAGA;;;;AAEA,mCAAmC;AACnC,eAAe,WAAW,KAAK,EAAE,OAAO;IACtC,MAAM,cAAc,CAAA,GAAA,gKAAA,CAAA,2BAAwB,AAAD,EAAE;IAC7C,MAAM,OAAO,EAAE;IACf,MAAM,EAAE,QAAQ,SAAS,EAAE,MAAM,EAAE,QAAQ,cAAc,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;IACrE,IAAI,UAAU,WAAW;QACvB,MAAM,UAAU,MAAM,OAAO,MAAM,CAAC;YAClC,GAAG,WAAW;YACd,MAAM;YACN,OAAO;gBACL,UAAU,MAAM;oBACd,IAAI,IAAI,KAAK,CAAC,GAAG,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;gBAChD,IAAI,KAAK;gBACT,GAAG,YAAY,KAAK;YACtB;YACA,OAAO;QACT;QACA,IAAI,CAAC,SAAS,OAAO;QACrB,KAAK,MAAM,OAAO,QAAQ,IAAI,CAAE;YAC9B,MAAM,MAAM,IAAI,QAAQ;YACxB,KAAK,IAAI,CACP;gBACE,IAAI,IAAI,EAAE;gBACV,MAAM;gBACN,SAAS,IAAI,KAAK;gBAClB,uBAAuB,YAAY,SAAS,CAAC,IAAI,KAAK;gBACtD,KAAK,IAAI,IAAI;YACf,GACA;gBACE,IAAI,SAAS,IAAI,EAAE;gBACnB,MAAM;gBACN,SAAS,IAAI,OAAO;gBACpB,uBAAuB,YAAY,SAAS,CAAC,IAAI,OAAO;gBACxD,KAAK,IAAI,IAAI;YACf;QAEJ;QACA,OAAO;IACT;IACA,MAAM,SAAS;QACb,GAAG,WAAW;QACd,MAAM;QACN,OAAO,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;YACrB;YACA,GAAG,YAAY,KAAK;QACtB;QACA,SAAS;YACP,YAAY;gBAAC;aAAU;YACvB,WAAW;YACX,GAAG,YAAY,OAAO;QACxB;IACF;IACA,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC;IACnC,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,EAAE,OAAO;IACtC,KAAK,MAAM,QAAQ,OAAO,MAAM,CAAE;QAChC,IAAI,YAAY;QAChB,KAAK,MAAM,OAAO,KAAK,MAAM,CAAE;YAC7B,MAAM,MAAM,IAAI,QAAQ;YACxB,IAAI,CAAC,WAAW;gBACd,KAAK,IAAI,CAAC;oBACR,IAAI,IAAI,OAAO;oBACf,MAAM;oBACN,SAAS,IAAI,KAAK;oBAClB,uBAAuB,YAAY,SAAS,CAAC,IAAI,KAAK;oBACtD,KAAK,IAAI,GAAG;gBACd;gBACA,YAAY;YACd;YACA,KAAK,IAAI,CAAC;gBACR,IAAI,IAAI,EAAE;gBACV,SAAS,IAAI,OAAO;gBACpB,uBAAuB,YAAY,SAAS,CAAC,IAAI,OAAO;gBACxD,MAAM,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG,YAAY;gBAChD,KAAK,IAAI,UAAU,GAAG,AAAC,GAAa,OAAX,IAAI,GAAG,EAAC,KAAkB,OAAf,IAAI,UAAU,IAAK,IAAI,GAAG;YAChE;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}]}