{"version": 3, "sources": ["../../src/client/app-next-turbopack.ts"], "sourcesContent": ["import { appBootstrap } from './app-bootstrap'\nimport { isRecoverableError } from './react-client-callbacks/on-recoverable-error'\n\nwindow.next.turbopack = true\n;(self as any).__webpack_hash__ = ''\n\n// eslint-disable-next-line @next/internal/typechecked-require\nconst instrumentationHooks = require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index') as typeof import('./app-index')\n  try {\n    hydrate(instrumentationHooks)\n  } finally {\n    if (process.env.NODE_ENV !== 'production') {\n      const { getOwnerStack } =\n        require('../next-devtools/userspace/app/errors/stitched-error') as typeof import('../next-devtools/userspace/app/errors/stitched-error')\n      const { renderAppDevOverlay } =\n        require('next/dist/compiled/next-devtools') as typeof import('next/dist/compiled/next-devtools')\n      renderAppDevOverlay(getOwnerStack, isRecoverableError)\n    }\n  }\n})\n"], "names": ["appBootstrap", "isRecoverableError", "window", "next", "turbopack", "self", "__webpack_hash__", "<PERSON><PERSON><PERSON><PERSON>", "require", "hydrate", "process", "env", "NODE_ENV", "getOwnerStack", "renderAppDevOverlay"], "mappings": "AAAA,SAASA,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,gDAA+C;AAElFC,OAAOC,IAAI,CAACC,SAAS,GAAG;AACtBC,KAAaC,gBAAgB,GAAG;AAElC,8DAA8D;AAC9D,MAAMC,uBAAuBC,QAAQ;AAErCR,aAAa;IACX,MAAM,EAAES,OAAO,EAAE,GAAGD,QAAQ;IAC5B,IAAI;QACFC,QAAQF;IACV,SAAU;QACR,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAM,EAAEC,aAAa,EAAE,GACrBL,QAAQ;YACV,MAAM,EAAEM,mBAAmB,EAAE,GAC3BN,QAAQ;YACVM,oBAAoBD,eAAeZ;QACrC;IACF;AACF", "ignoreList": [0]}