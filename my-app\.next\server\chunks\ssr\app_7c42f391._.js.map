{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/app/layout.config.tsx"], "sourcesContent": ["import type { BaseLayoutProps } from 'fumadocs-ui/layouts/shared';\n\n/**\n * Shared layout configurations\n *\n * you can customise layouts individually from:\n * Home Layout: app/(home)/layout.tsx\n * Docs Layout: app/docs/layout.tsx\n */\nexport const baseOptions: BaseLayoutProps = {\n  nav: {\n    title: (\n      <>\n        <svg\n          width=\"24\"\n          height=\"24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          aria-label=\"Logo\"\n        >\n          <circle cx={12} cy={12} r={12} fill=\"currentColor\" />\n        </svg>\n        My App\n      </>\n    ),\n  },\n  // see https://fumadocs.dev/docs/ui/navigation/links\n  links: [],\n};\n"], "names": [], "mappings": ";;;;;AASO,MAAM,cAA+B;IAC1C,KAAK;QACH,qBACE;;8BACE,8OAAC;oBACC,OAAM;oBACN,QAAO;oBACP,OAAM;oBACN,cAAW;8BAEX,cAAA,8OAAC;wBAAO,IAAI;wBAAI,IAAI;wBAAI,GAAG;wBAAI,MAAK;;;;;;;;;;;gBAChC;;;IAIZ;IACA,oDAAoD;IACpD,OAAO,EAAE;AACX", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/app/%28home%29/layout.tsx"], "sourcesContent": ["import type { ReactNode } from 'react';\nimport { HomeLayout } from 'fumadocs-ui/layouts/home';\nimport { baseOptions } from '@/app/layout.config';\n\nexport default function Layout({ children }: { children: ReactNode }) {\n  return <HomeLayout {...baseOptions}>{children}</HomeLayout>;\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEe,SAAS,OAAO,EAAE,QAAQ,EAA2B;IAClE,qBAAO,8OAAC,kKAAA,CAAA,aAAU;QAAE,GAAG,wHAAA,CAAA,cAAW;kBAAG;;;;;;AACvC", "debugId": null}}]}