{"version": 3, "sources": ["../../../../../src/server/route-modules/pages/builtin/_error.tsx"], "sourcesContent": ["import App from '../../../../pages/_app'\nimport Document from '../../../../pages/_document'\nimport { RouteKind } from '../../../route-kind'\n\nimport * as moduleError from '../../../../pages/_error'\n\nimport PagesRouteModule from '../module'\nimport { getHandler } from '../pages-handler'\n\nexport const routeModule = new PagesRouteModule({\n  // TODO: add descriptor for internal error page\n  definition: {\n    kind: RouteKind.PAGES,\n    page: '/_error',\n    pathname: '/_error',\n    filename: '',\n    bundlePath: '',\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n  components: {\n    App,\n    Document,\n  },\n  userland: moduleError,\n})\n\nexport const handler = getHandler({\n  srcPage: '/_error',\n  routeModule,\n  userland: moduleError,\n  config: {},\n  isFallbackError: true,\n})\n"], "names": ["App", "Document", "RouteKind", "moduleError", "PagesRouteModule", "<PERSON><PERSON><PERSON><PERSON>", "routeModule", "definition", "kind", "PAGES", "page", "pathname", "filename", "bundlePath", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "relativeProjectDir", "__NEXT_RELATIVE_PROJECT_DIR", "components", "userland", "handler", "srcPage", "config", "isFallbackError"], "mappings": "AAAA,OAAOA,SAAS,yBAAwB;AACxC,OAAOC,cAAc,8BAA6B;AAClD,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,YAAYC,iBAAiB,2BAA0B;AAEvD,OAAOC,sBAAsB,YAAW;AACxC,SAASC,UAAU,QAAQ,mBAAkB;AAE7C,OAAO,MAAMC,cAAc,IAAIF,iBAAiB;IAC9C,+CAA+C;IAC/CG,YAAY;QACVC,MAAMN,UAAUO,KAAK;QACrBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,oBAAoBH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;IAC/DC,YAAY;QACVpB;QACAC;IACF;IACAoB,UAAUlB;AACZ,GAAE;AAEF,OAAO,MAAMmB,UAAUjB,WAAW;IAChCkB,SAAS;IACTjB;IACAe,UAAUlB;IACVqB,QAAQ,CAAC;IACTC,iBAAiB;AACnB,GAAE", "ignoreList": [0]}