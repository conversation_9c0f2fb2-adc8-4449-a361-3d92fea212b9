"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[811],{244:(e,t,r)=>{function n(e,t,r){let{includePage:n=!0,includeSeparator:a=!1,includeRoot:l}=r,o=[];return t.forEach((e,r)=>{if("separator"===e.type&&e.name&&a&&o.push({name:e.name}),"folder"===e.type){let n=t.at(r+1);if(n&&e.index===n)return;if(e.root){o=[];return}o.push({name:e.name,url:e.index?.url})}"page"===e.type&&n&&o.push({name:e.name,url:e.url})}),l&&o.unshift({name:e.name,url:"object"==typeof l?l.url:void 0}),o}r.d(t,{Pp:()=>n,oe:()=>function e(t,r){let n;for(let a of(r.endsWith("/")&&(r=r.slice(0,-1)),t)){if("separator"===a.type&&(n=a),"folder"===a.type){if(a.index?.url===r){let e=[];return n&&e.push(n),e.push(a,a.index),e}let t=e(a.children,r);if(t)return t.unshift(a),n&&t.unshift(n),t}if("page"===a.type&&a.url===r){let e=[];return n&&e.push(n),e.push(a),e}}return null}}),r(7505),r(2115)},263:(e,t,r)=>{r.d(t,{G:()=>d,c:()=>s});var n=r(5155),a=r(2115),l=r(344),o=r(3259);let i=(0,l.q6)("SidebarContext");function s(){return i.use()}function d(e){let{children:t}=e,r=(0,a.useRef)(!0),[s,d]=(0,a.useState)(!1),[c,u]=(0,a.useState)(!1),f=(0,l.a8)();return(0,o.T)(f,()=>{r.current&&d(!1),r.current=!0}),(0,n.jsx)(i.Provider,{value:(0,a.useMemo)(()=>({open:s,setOpen:d,collapsed:c,setCollapsed:u,closeOnRedirect:r}),[s,c]),children:t})}},799:(e,t,r)=>{r.d(t,{RootToggle:()=>f});var n=r(5155),a=r(8686),l=r(2115),o=r(2808),i=r(344),s=r(9688),d=r(5455),c=r(263),u=r(408);function f(e){let{options:t,placeholder:r,...f}=e,[p,h]=(0,l.useState)(!1),{closeOnRedirect:m}=(0,c.c)(),v=(0,i.a8)(),x=(0,l.useMemo)(()=>{let e=v.endsWith("/")?v.slice(0,-1):v;return t.findLast(t=>t.urls?t.urls.has(e):(0,d.$)(t.url,v,!0))},[t,v]),b=()=>{m.current=!1,h(!1)},g=x?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"size-9 shrink-0 md:size-5",children:x.icon}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:x.title}),(0,n.jsx)("p",{className:"text-[13px] text-fd-muted-foreground empty:hidden md:hidden",children:x.description})]})]}):r;return(0,n.jsxs)(u.AM,{open:p,onOpenChange:h,children:[g&&(0,n.jsxs)(u.Wv,{...f,className:(0,s.QP)("flex items-center gap-2 rounded-lg p-2 border bg-fd-secondary/50 text-start text-fd-secondary-foreground transition-colors hover:bg-fd-accent data-[state=open]:bg-fd-accent data-[state=open]:text-fd-accent-foreground",f.className),children:[g,(0,n.jsx)(a.Ml,{className:"shrink-0 ms-auto size-4 text-fd-muted-foreground"})]}),(0,n.jsx)(u.hl,{className:"flex flex-col gap-1 w-(--radix-popover-trigger-width) overflow-hidden p-1",children:t.map(e=>{var t;let r=e===x;return(0,n.jsxs)(o.default,{href:e.url,onClick:b,...e.props,className:(0,s.QP)("flex items-center gap-2 rounded-lg p-1.5 hover:bg-fd-accent hover:text-fd-accent-foreground",null==(t=e.props)?void 0:t.className),children:[(0,n.jsx)("div",{className:"shrink-0 size-9 md:mt-1 md:mb-auto md:size-5",children:e.icon}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:e.title}),(0,n.jsx)("p",{className:"text-[13px] text-fd-muted-foreground empty:hidden",children:e.description})]}),(0,n.jsx)(a.Jl,{className:(0,s.QP)("shrink-0 ms-auto size-3.5 text-fd-primary",!r&&"invisible")})]},e.url)})})]})}},3259:(e,t,r)=>{r.d(t,{T:()=>n.T});var n=r(8011);r(7505)},6776:(e,t,r)=>{r.r(t),r.d(t,{Sidebar:()=>es,SidebarCollapseTrigger:()=>ey,SidebarContent:()=>ed,SidebarContentMobile:()=>ec,SidebarFolder:()=>ev,SidebarFolderContent:()=>eg,SidebarFolderLink:()=>eb,SidebarFolderTrigger:()=>ex,SidebarFooter:()=>ef,SidebarHeader:()=>eu,SidebarItem:()=>em,SidebarPageTree:()=>eN,SidebarSeparator:()=>eh,SidebarTrigger:()=>ew,SidebarViewport:()=>ep});var n=r(5155),a=r(8686),l=r(344),o=r(2115),i=r(2808),s=r(3259),d=r(9688),c=r(3655),u=r(8905),f=r(6081),p=r(6101),h=r(9033),m=r(4315),v=r(2712),x=r(5185),b="ScrollArea",[g,w]=(0,f.A)(b),[y,S]=g(b),j=o.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:a="hover",dir:l,scrollHideDelay:i=600,...s}=e,[d,u]=o.useState(null),[f,h]=o.useState(null),[v,x]=o.useState(null),[b,g]=o.useState(null),[w,S]=o.useState(null),[j,N]=o.useState(0),[C,T]=o.useState(0),[E,P]=o.useState(!1),[R,L]=o.useState(!1),_=(0,p.s)(t,e=>u(e)),D=(0,m.jH)(l);return(0,n.jsx)(y,{scope:r,type:a,dir:D,scrollHideDelay:i,scrollArea:d,viewport:f,onViewportChange:h,content:v,onContentChange:x,scrollbarX:b,onScrollbarXChange:g,scrollbarXEnabled:E,onScrollbarXEnabledChange:P,scrollbarY:w,onScrollbarYChange:S,scrollbarYEnabled:R,onScrollbarYEnabledChange:L,onCornerWidthChange:N,onCornerHeightChange:T,children:(0,n.jsx)(c.sG.div,{dir:D,...s,ref:_,style:{position:"relative","--radix-scroll-area-corner-width":j+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});j.displayName=b;var N="ScrollAreaViewport",C=o.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:a,nonce:l,...i}=e,s=S(N,r),d=o.useRef(null),u=(0,p.s)(t,d,s.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,n.jsx)(c.sG.div,{"data-radix-scroll-area-viewport":"",...i,ref:u,style:{overflowX:s.scrollbarXEnabled?"scroll":"hidden",overflowY:s.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,n.jsx)("div",{ref:s.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});C.displayName=N;var T="ScrollAreaScrollbar",E=o.forwardRef((e,t)=>{let{forceMount:r,...a}=e,l=S(T,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=l,d="horizontal"===e.orientation;return o.useEffect(()=>(d?i(!0):s(!0),()=>{d?i(!1):s(!1)}),[d,i,s]),"hover"===l.type?(0,n.jsx)(P,{...a,ref:t,forceMount:r}):"scroll"===l.type?(0,n.jsx)(R,{...a,ref:t,forceMount:r}):"auto"===l.type?(0,n.jsx)(L,{...a,ref:t,forceMount:r}):"always"===l.type?(0,n.jsx)(_,{...a,ref:t}):null});E.displayName=T;var P=o.forwardRef((e,t)=>{let{forceMount:r,...a}=e,l=S(T,e.__scopeScrollArea),[i,s]=o.useState(!1);return o.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,n.jsx)(u.C,{present:r||i,children:(0,n.jsx)(L,{"data-state":i?"visible":"hidden",...a,ref:t})})}),R=o.forwardRef((e,t)=>{var r;let{forceMount:a,...l}=e,i=S(T,e.__scopeScrollArea),s="horizontal"===e.orientation,d=q(()=>f("SCROLL_END"),100),[c,f]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return o.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>f("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,i.scrollHideDelay,f]),o.useEffect(()=>{let e=i.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(f("SCROLL"),d()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[i.viewport,s,f,d]),(0,n.jsx)(u.C,{present:a||"hidden"!==c,children:(0,n.jsx)(_,{"data-state":"hidden"===c?"hidden":"visible",...l,ref:t,onPointerEnter:(0,x.mK)(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:(0,x.mK)(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),L=o.forwardRef((e,t)=>{let r=S(T,e.__scopeScrollArea),{forceMount:a,...l}=e,[i,s]=o.useState(!1),d="horizontal"===e.orientation,c=q(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(d?e:t)}},10);return $(r.viewport,c),$(r.content,c),(0,n.jsx)(u.C,{present:a||i,children:(0,n.jsx)(_,{"data-state":i?"visible":"hidden",...l,ref:t})})}),_=o.forwardRef((e,t)=>{let{orientation:r="vertical",...a}=e,l=S(T,e.__scopeScrollArea),i=o.useRef(null),s=o.useRef(0),[d,c]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=Y(d.viewport,d.content),f={...a,sizes:d,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",a=G(r),l=t||a/2,o=r.scrollbar.paddingStart+l,i=r.scrollbar.size-r.scrollbar.paddingEnd-(a-l),s=r.content-r.viewport;return U([o,i],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,s.current,d,t)}return"horizontal"===r?(0,n.jsx)(D,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=K(l.viewport.scrollLeft,d,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===r?(0,n.jsx)(z,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=K(l.viewport.scrollTop,d);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),D=o.forwardRef((e,t)=>{let{sizes:r,onSizesChange:a,...l}=e,i=S(T,e.__scopeScrollArea),[s,d]=o.useState(),c=o.useRef(null),u=(0,p.s)(t,c,i.onScrollbarXChange);return o.useEffect(()=>{c.current&&d(getComputedStyle(c.current))},[c]),(0,n.jsx)(Q,{"data-orientation":"horizontal",...l,ref:u,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":G(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{c.current&&i.viewport&&s&&a({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:X(s.paddingLeft),paddingEnd:X(s.paddingRight)}})}})}),z=o.forwardRef((e,t)=>{let{sizes:r,onSizesChange:a,...l}=e,i=S(T,e.__scopeScrollArea),[s,d]=o.useState(),c=o.useRef(null),u=(0,p.s)(t,c,i.onScrollbarYChange);return o.useEffect(()=>{c.current&&d(getComputedStyle(c.current))},[c]),(0,n.jsx)(Q,{"data-orientation":"vertical",...l,ref:u,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":G(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{c.current&&i.viewport&&s&&a({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:X(s.paddingTop),paddingEnd:X(s.paddingBottom)}})}})}),[A,k]=g(T),Q=o.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:a,hasThumb:l,onThumbChange:i,onThumbPointerUp:s,onThumbPointerDown:d,onThumbPositionChange:u,onDragScroll:f,onWheelScroll:m,onResize:v,...b}=e,g=S(T,r),[w,y]=o.useState(null),j=(0,p.s)(t,e=>y(e)),N=o.useRef(null),C=o.useRef(""),E=g.viewport,P=a.content-a.viewport,R=(0,h.c)(m),L=(0,h.c)(u),_=q(v,10);function D(e){N.current&&f({x:e.clientX-N.current.left,y:e.clientY-N.current.top})}return o.useEffect(()=>{let e=e=>{let t=e.target;(null==w?void 0:w.contains(t))&&R(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[E,w,P,R]),o.useEffect(L,[a,L]),$(w,_),$(g.content,_),(0,n.jsx)(A,{scope:r,scrollbar:w,hasThumb:l,onThumbChange:(0,h.c)(i),onThumbPointerUp:(0,h.c)(s),onThumbPositionChange:L,onThumbPointerDown:(0,h.c)(d),children:(0,n.jsx)(c.sG.div,{...b,ref:j,style:{position:"absolute",...b.style},onPointerDown:(0,x.mK)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),N.current=w.getBoundingClientRect(),C.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",g.viewport&&(g.viewport.style.scrollBehavior="auto"),D(e))}),onPointerMove:(0,x.mK)(e.onPointerMove,D),onPointerUp:(0,x.mK)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=C.current,g.viewport&&(g.viewport.style.scrollBehavior=""),N.current=null})})})}),I="ScrollAreaThumb",M=o.forwardRef((e,t)=>{let{forceMount:r,...a}=e,l=k(I,e.__scopeScrollArea);return(0,n.jsx)(u.C,{present:r||l.hasThumb,children:(0,n.jsx)(O,{ref:t,...a})})}),O=o.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:a,...l}=e,i=S(I,r),s=k(I,r),{onThumbPositionChange:d}=s,u=(0,p.s)(t,e=>s.onThumbChange(e)),f=o.useRef(void 0),h=q(()=>{f.current&&(f.current(),f.current=void 0)},100);return o.useEffect(()=>{let e=i.viewport;if(e){let t=()=>{h(),f.current||(f.current=B(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[i.viewport,h,d]),(0,n.jsx)(c.sG.div,{"data-state":s.hasThumb?"visible":"hidden",...l,ref:u,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:(0,x.mK)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;s.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,x.mK)(e.onPointerUp,s.onThumbPointerUp)})});M.displayName=I;var H="ScrollAreaCorner",W=o.forwardRef((e,t)=>{let r=S(H,e.__scopeScrollArea),a=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&a?(0,n.jsx)(F,{...e,ref:t}):null});W.displayName=H;var F=o.forwardRef((e,t)=>{let{__scopeScrollArea:r,...a}=e,l=S(H,r),[i,s]=o.useState(0),[d,u]=o.useState(0),f=!!(i&&d);return $(l.scrollbarX,()=>{var e;let t=(null==(e=l.scrollbarX)?void 0:e.offsetHeight)||0;l.onCornerHeightChange(t),u(t)}),$(l.scrollbarY,()=>{var e;let t=(null==(e=l.scrollbarY)?void 0:e.offsetWidth)||0;l.onCornerWidthChange(t),s(t)}),f?(0,n.jsx)(c.sG.div,{...a,ref:t,style:{width:i,height:d,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function X(e){return e?parseInt(e,10):0}function Y(e,t){let r=e/t;return isNaN(r)?0:r}function G(e){let t=Y(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function K(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=G(t),a=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-a,o=t.content-t.viewport,i=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,o]:[-1*o,0]);return U([0,o],[0,l-n])(i)}function U(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var B=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function a(){let l={left:e.scrollLeft,top:e.scrollTop},o=r.left!==l.left,i=r.top!==l.top;(o||i)&&t(),r=l,n=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(n)};function q(e,t){let r=(0,h.c)(e),n=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),o.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(r,t)},[r,t])}function $(e,t){let r=(0,h.c)(t);(0,v.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}let V=o.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(j,{ref:a,type:"scroll",className:(0,d.QP)("overflow-hidden",e),...r,children:[t,(0,n.jsx)(W,{}),(0,n.jsx)(Z,{orientation:"vertical"})]}));V.displayName=j.displayName;let J=o.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsx)(C,{ref:a,className:(0,d.QP)("size-full rounded-[inherit]",e),...r,children:t}));J.displayName=C.displayName;let Z=o.forwardRef(({className:e,orientation:t="vertical",...r},a)=>(0,n.jsx)(E,{ref:a,orientation:t,className:(0,d.QP)("flex select-none data-[state=hidden]:animate-fd-fade-out","vertical"===t&&"h-full w-1.5","horizontal"===t&&"h-1.5 flex-col",e),...r,children:(0,n.jsx)(M,{className:"relative flex-1 rounded-full bg-fd-border"})}));Z.displayName=E.displayName;var ee=r(5455),et=r(8758),er=r(263),en=r(3243),ea=r(8693);r(7505);let el=(0,en.F)("relative flex flex-row items-center gap-2 rounded-xl p-2 ps-(--sidebar-item-offset) text-start text-fd-muted-foreground [overflow-wrap:anywhere] [&_svg]:size-4 [&_svg]:shrink-0",{variants:{active:{true:"bg-fd-primary/10 text-fd-primary",false:"transition-colors hover:bg-fd-accent/50 hover:text-fd-accent-foreground/80 hover:transition-none"}}}),eo=(0,o.createContext)(null),ei=(0,o.createContext)(null);function es(e){var t;let{defaultOpenLevel:r=0,prefetch:a=!0,Mobile:l,Content:i}=e,s=null!=(t=function(e,t=!1){let[r,n]=(0,o.useState)(null);return(0,o.useEffect)(()=>{if(t)return;let r=window.matchMedia(e),a=()=>{n(r.matches)};return a(),r.addEventListener("change",a),()=>{r.removeEventListener("change",a)}},[t,e]),r}("(width < 768px)"))&&t,d=(0,o.useMemo)(()=>({defaultOpenLevel:r,prefetch:a,level:1}),[r,a]);return(0,n.jsx)(eo.Provider,{value:d,children:s&&null!=l?l:i})}function ed(e){let{collapsed:t}=(0,er.c)(),[r,a]=(0,o.useState)(!1),l=(0,o.useRef)(0),i=(0,o.useRef)(0);return(0,s.T)(t,()=>{a(!1),i.current=Date.now()+150}),(0,n.jsx)("aside",{id:"nd-sidebar",...e,"data-collapsed":t,className:(0,d.QP)("fixed start-0 flex flex-col items-end top-(--fd-sidebar-top) bottom-(--fd-sidebar-margin) z-20 bg-fd-card text-sm border-e max-md:hidden *:w-(--fd-sidebar-width)",t&&["rounded-xl border translate-x-(--fd-sidebar-offset) rtl:-translate-x-(--fd-sidebar-offset)",r?"z-50 shadow-lg":"opacity-0"],e.className),style:{transition:["top","opacity","translate","width"].map(e=>"".concat(e," ease 250ms")).join(", "),...e.style,"--fd-sidebar-offset":r?"calc(var(--spacing) * 2)":"calc(16px - 100%)","--fd-sidebar-margin":t?"0.5rem":"0px","--fd-sidebar-top":"calc(var(--fd-banner-height) + var(--fd-nav-height) + var(--fd-sidebar-margin))",width:t?"var(--fd-sidebar-width)":"calc(var(--fd-sidebar-width) + var(--fd-layout-offset))"},onPointerEnter:e=>{!t||"touch"===e.pointerType||i.current>Date.now()||(window.clearTimeout(l.current),a(!0))},onPointerLeave:e=>{t&&"touch"!==e.pointerType&&(window.clearTimeout(l.current),l.current=window.setTimeout(()=>{a(!1),i.current=Date.now()+150},Math.min(e.clientX,document.body.clientWidth-e.clientX)>100?0:500))},children:e.children})}function ec(e){let{className:t,children:r,...a}=e,{open:l,setOpen:o}=(0,er.c)(),i=l?"open":"closed";return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(u.C,{present:l,children:(0,n.jsx)("div",{"data-state":i,className:"fixed z-40 inset-0 backdrop-blur-xs data-[state=open]:animate-fd-fade-in data-[state=closed]:animate-fd-fade-out",onClick:()=>o(!1)})}),(0,n.jsx)(u.C,{present:l,children:e=>{let{present:l}=e;return(0,n.jsx)("aside",{id:"nd-sidebar-mobile",...a,"data-state":i,className:(0,d.QP)("fixed text-[15px] flex flex-col shadow-lg border-s end-0 inset-y-0 w-[85%] max-w-[380px] z-40 bg-fd-background data-[state=open]:animate-fd-sidebar-in data-[state=closed]:animate-fd-sidebar-out",!l&&"invisible",t),children:r})}})]})}function eu(e){return(0,n.jsx)("div",{...e,className:(0,d.QP)("flex flex-col gap-3 p-4 pb-2",e.className),children:e.children})}function ef(e){return(0,n.jsx)("div",{...e,className:(0,d.QP)("flex flex-col border-t px-4 py-3",e.className),children:e.children})}function ep(e){return(0,n.jsx)(V,{...e,className:(0,d.QP)("h-full",e.className),children:(0,n.jsx)(J,{className:"p-4",style:{"--sidebar-item-offset":"calc(var(--spacing) * 2)",maskImage:"linear-gradient(to bottom, transparent, white 12px, white calc(100% - 12px), transparent)"},children:e.children})})}function eh(e){return(0,n.jsx)("p",{...e,className:(0,d.QP)("inline-flex items-center gap-2 mb-1.5 px-2 ps-(--sidebar-item-offset) empty:mb-0 [&_svg]:size-4 [&_svg]:shrink-0",e.className),children:e.children})}function em(e){let{icon:t,...r}=e,o=(0,l.a8)(),s=void 0!==r.href&&(0,ee.$)(r.href,o,!1),{prefetch:c}=ej();return(0,n.jsxs)(i.default,{...r,"data-active":s,className:(0,d.QP)(el({active:s}),r.className),prefetch:c,children:[null!=t?t:r.external?(0,n.jsx)(a.Gr,{}):null,r.children]})}function ev(e){let{defaultOpen:t=!1,...r}=e,[a,l]=(0,o.useState)(t);return(0,s.T)(t,e=>{e&&l(e)}),(0,n.jsx)(et.Nt,{open:a,onOpenChange:l,...r,children:(0,n.jsx)(ei.Provider,{value:(0,o.useMemo)(()=>({open:a,setOpen:l}),[a]),children:r.children})})}function ex(e){let{className:t,...r}=e,{open:l}=eS();return(0,n.jsxs)(et.R6,{className:(0,d.QP)(el({active:!1}),"w-full",t),...r,children:[r.children,(0,n.jsx)(a.yQ,{"data-icon":!0,className:(0,d.QP)("ms-auto transition-transform",!l&&"-rotate-90")})]})}function eb(e){let{open:t,setOpen:r}=eS(),{prefetch:o}=ej(),s=(0,l.a8)(),c=void 0!==e.href&&(0,ee.$)(e.href,s,!1);return(0,n.jsxs)(i.default,{...e,"data-active":c,className:(0,d.QP)(el({active:c}),"w-full",e.className),onClick:e=>{e.target instanceof HTMLElement&&e.target.hasAttribute("data-icon")?(r(!t),e.preventDefault()):r(!c||!t)},prefetch:o,children:[e.children,(0,n.jsx)(a.yQ,{"data-icon":!0,className:(0,d.QP)("ms-auto transition-transform",!t&&"-rotate-90")})]})}function eg(e){let t=ej(),r=t.level+1;return(0,n.jsxs)(et.Ke,{...e,className:(0,d.QP)("relative",2===r&&"**:data-[active=true]:before:content-[''] **:data-[active=true]:before:bg-fd-primary **:data-[active=true]:before:absolute **:data-[active=true]:before:w-px **:data-[active=true]:before:inset-y-2.5 **:data-[active=true]:before:start-2.5",e.className),style:{"--sidebar-item-offset":"calc(var(--spacing) * ".concat(r>1?3*r:2,")"),...e.style},children:[2===r&&(0,n.jsx)("div",{className:"absolute w-px inset-y-1 bg-fd-border start-2.5"}),(0,n.jsx)(eo.Provider,{value:(0,o.useMemo)(()=>({...t,level:r}),[t,r]),children:e.children})]})}function ew(e){let{children:t,...r}=e,{setOpen:a}=(0,er.c)();return(0,n.jsx)("button",{...r,"aria-label":"Open Sidebar",onClick:()=>a(e=>!e),children:t})}function ey(e){let{collapsed:t,setCollapsed:r}=(0,er.c)();return(0,n.jsx)("button",{type:"button","aria-label":"Collapse Sidebar","data-collapsed":t,...e,onClick:()=>{r(e=>!e)},children:e.children})}function eS(){let e=(0,o.useContext)(ei);if(!e)throw Error("Missing sidebar folder");return e}function ej(){let e=(0,o.useContext)(eo);if(!e)throw Error("<Sidebar /> component required.");return e}function eN(e){let{root:t}=(0,ea.t)();return(0,o.useMemo)(()=>{var r;let{Separator:a,Item:l,Folder:i}=null!=(r=e.components)?r:{};return(0,n.jsx)(o.Fragment,{children:function e(t,r){return t.map((t,o)=>{if("separator"===t.type)return a?(0,n.jsx)(a,{item:t},o):(0,n.jsxs)(eh,{className:(0,d.QP)(0!==o&&"mt-6"),children:[t.icon,t.name]},o);if("folder"===t.type){let a=e(t.children,r+1);return i?(0,n.jsx)(i,{item:t,level:r,children:a},o):(0,n.jsx)(eC,{item:t,children:a},o)}return l?(0,n.jsx)(l,{item:t},t.url):(0,n.jsx)(em,{href:t.url,external:t.external,icon:t.icon,children:t.name},t.url)})}(t.children,1)},t.$id)},[e.components,t])}function eC(e){var t;let{item:r,...a}=e,{defaultOpenLevel:l,level:o}=ej(),i=(0,ea.L)();return(0,n.jsxs)(ev,{defaultOpen:(null!=(t=r.defaultOpen)?t:l>=o)||i.includes(r),children:[r.index?(0,n.jsxs)(eb,{href:r.index.url,external:r.index.external,...a,children:[r.icon,r.name]}):(0,n.jsxs)(ex,{...a,children:[r.icon,r.name]}),(0,n.jsx)(eg,{children:a.children})]})}},7479:(e,t,r)=>{r.d(t,{CollapsibleControl:()=>p,LayoutBody:()=>f,Navbar:()=>u});var n=r(5155),a=r(8686),l=r(9688),o=r(7936),i=r(263),s=r(1339),d=r(6776),c=r(5403);function u(e){let{isTransparent:t}=(0,s.hI)();return(0,n.jsx)("header",{id:"nd-subnav",...e,className:(0,l.QP)("fixed top-(--fd-banner-height) inset-x-0 z-30 flex items-center ps-4 pe-2.5 border-b transition-colors backdrop-blur-sm",!t&&"bg-fd-background/80",e.className),children:e.children})}function f(e){let{collapsed:t}=(0,i.c)();return(0,n.jsx)("main",{id:"nd-docs-layout",...e,className:(0,l.QP)("flex flex-1 flex-col pt-(--fd-nav-height) transition-[padding]",e.className),style:{...e.style,paddingInlineStart:t?"min(calc(100vw - var(--fd-page-width)), var(--fd-sidebar-width))":"calc(var(--fd-sidebar-width) + var(--fd-layout-offset))",paddingInlineEnd:t?"0px":"var(--fd-layout-offset)"},children:e.children})}function p(){let{collapsed:e}=(0,i.c)();return(0,n.jsxs)("div",{className:(0,l.QP)("fixed flex shadow-lg transition-opacity rounded-xl p-0.5 border bg-fd-muted text-fd-muted-foreground z-10 max-md:hidden xl:start-4 max-xl:end-4",!e&&"pointer-events-none opacity-0"),style:{top:"calc(var(--fd-banner-height) + var(--fd-tocnav-height) + var(--spacing) * 4)"},children:[(0,n.jsx)(d.SidebarCollapseTrigger,{className:(0,l.QP)((0,o.r)({color:"ghost",size:"icon-sm",className:"rounded-lg"})),children:(0,n.jsx)(a.Bx,{})}),(0,n.jsx)(c.SearchToggle,{className:"rounded-lg",hideIfDisabled:!0})]})}},8011:(e,t,r)=>{r.d(t,{T:()=>a});var n=r(2115);function a(e,t,r=function e(t,r){return Array.isArray(t)&&Array.isArray(r)?r.length!==t.length||t.some((t,n)=>e(t,r[n])):t!==r}){let[l,o]=(0,n.useState)(e);r(l,e)&&(t(e,l),o(e))}},8055:(e,t,r)=>{r.d(t,{HideIfEmpty:()=>s}),r(7505);var n=r(2115),a=r(5155),l=(0,n.createContext)({nonce:void 0});function o(e){return document.querySelector('[data-fd-if-empty="'.concat(e,'"]'))}function i(e){for(let t=0;t<e.childNodes.length;t++){let r=e.childNodes.item(t);if(r.nodeType===Node.TEXT_NODE||r.nodeType===Node.ELEMENT_NODE&&"none"!==window.getComputedStyle(r).display)return!1}return!0}function s(e){let{as:t,...r}=e,s=(0,n.useId)(),{nonce:d}=(0,n.useContext)(l),[c,u]=(0,n.useState)(()=>{let e="undefined"!=typeof window?o(s):null;if(e)return i(e)});return(0,n.useEffect)(()=>{let e=()=>{let e=o(s);e&&u(i(e))};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[s]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(t,{...r,"data-fd-if-empty":s,hidden:null!=c&&c}),void 0===c&&(0,a.jsx)("script",{nonce:d,dangerouslySetInnerHTML:{__html:"{".concat(o,";").concat(i,";(").concat(e=>{var t;let r=o(e);r&&(r.hidden=i(r));let n=document.currentScript;n&&(null==(t=n.parentNode)||t.removeChild(n))},')("').concat(s,'")}')}})]})}},8693:(e,t,r)=>{r.d(t,{L:()=>c,TreeContextProvider:()=>d,t:()=>u});var n=r(5155),a=r(344),l=r(2115),o=r(244);let i=(0,a.q6)("TreeContext"),s=(0,a.q6)("PathContext",[]);function d(e){var t,r;let d=(0,l.useRef)(0),c=(0,a.a8)(),u=(0,l.useMemo)(()=>e.tree,[null!=(t=e.tree.$id)?t:e.tree]),f=(0,l.useMemo)(()=>{var e;return null!=(e=(0,o.oe)(u.children,c))?e:[]},[u,c]),p=null!=(r=f.findLast(e=>"folder"===e.type&&e.root))?r:u;return null!=p.$id||(p.$id=String(d.current++)),(0,n.jsx)(i.Provider,{value:(0,l.useMemo)(()=>({root:p}),[p]),children:(0,n.jsx)(s.Provider,{value:f,children:e.children})})}function c(){return s.use()}function u(){return i.use("You must wrap this component under <DocsLayout />")}},8758:(e,t,r)=>{r.d(t,{Nt:()=>T,Ke:()=>P,R6:()=>E});var n=r(5155),a=r(2115),l=r(5185),o=r(6081),i=r(5845),s=r(2712),d=r(6101),c=r(3655),u=r(8905),f=r(1285),p="Collapsible",[h,m]=(0,o.A)(p),[v,x]=h(p),b=a.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:l,defaultOpen:o,disabled:s,onOpenChange:d,...u}=e,[h,m]=(0,i.i)({prop:l,defaultProp:null!=o&&o,onChange:d,caller:p});return(0,n.jsx)(v,{scope:r,disabled:s,contentId:(0,f.B)(),open:h,onOpenToggle:a.useCallback(()=>m(e=>!e),[m]),children:(0,n.jsx)(c.sG.div,{"data-state":N(h),"data-disabled":s?"":void 0,...u,ref:t})})});b.displayName=p;var g="CollapsibleTrigger",w=a.forwardRef((e,t)=>{let{__scopeCollapsible:r,...a}=e,o=x(g,r);return(0,n.jsx)(c.sG.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":N(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...a,ref:t,onClick:(0,l.mK)(e.onClick,o.onOpenToggle)})});w.displayName=g;var y="CollapsibleContent",S=a.forwardRef((e,t)=>{let{forceMount:r,...a}=e,l=x(y,e.__scopeCollapsible);return(0,n.jsx)(u.C,{present:r||l.open,children:e=>{let{present:r}=e;return(0,n.jsx)(j,{...a,ref:t,present:r})}})});S.displayName=y;var j=a.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:l,children:o,...i}=e,u=x(y,r),[f,p]=a.useState(l),h=a.useRef(null),m=(0,d.s)(t,h),v=a.useRef(0),b=v.current,g=a.useRef(0),w=g.current,S=u.open||f,j=a.useRef(S),C=a.useRef(void 0);return a.useEffect(()=>{let e=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.N)(()=>{let e=h.current;if(e){C.current=C.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();v.current=t.height,g.current=t.width,j.current||(e.style.transitionDuration=C.current.transitionDuration,e.style.animationName=C.current.animationName),p(l)}},[u.open,l]),(0,n.jsx)(c.sG.div,{"data-state":N(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!S,...i,ref:m,style:{"--radix-collapsible-content-height":b?"".concat(b,"px"):void 0,"--radix-collapsible-content-width":w?"".concat(w,"px"):void 0,...e.style},children:S&&o})});function N(e){return e?"open":"closed"}var C=r(9688);let T=b,E=w,P=(0,a.forwardRef)((e,t)=>{let{children:r,...l}=e,[o,i]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{i(!0)},[]),(0,n.jsx)(S,{ref:t,...l,className:(0,C.QP)("overflow-hidden",o&&"data-[state=closed]:animate-fd-collapsible-up data-[state=open]:animate-fd-collapsible-down",l.className),children:r})});P.displayName=S.displayName}}]);