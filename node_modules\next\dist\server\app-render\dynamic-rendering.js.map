{"version": 3, "sources": ["../../../src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n  PrerenderStoreModernRuntime,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  getRuntimeStagePromise,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from '../../lib/framework/boundary-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicErrorWithStack: null | Error\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspenseAboveBody: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasAllowedDynamic: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspenseAboveBody: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasAllowedDynamic: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n        // Inside cache scopes, marking a scope as dynamic has no effect,\n        // because the outer cache scope creates a cache boundary. This is\n        // subtly different from reading a dynamic data source, which is\n        // forbidden inside a cache scope.\n        return\n      case 'private-cache':\n        // A private cache scope is already dynamic by definition.\n        return\n      case 'prerender-legacy':\n      case 'prerender-ppr':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-ppr':\n        return postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      case 'prerender-legacy':\n        workUnitStore.revalidate = 0\n\n        // We aren't prerendering, but we are generating a static page. We need\n        // to bail out of static generation.\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      case 'request':\n        if (process.env.NODE_ENV !== 'production') {\n          workUnitStore.usedDynamic = true\n        }\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\n/**\n * This function is meant to be used when prerendering without cacheComponents or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(workUnitStore: WorkUnitStore) {\n  switch (workUnitStore.type) {\n    case 'cache':\n    case 'unstable-cache':\n      // Inside cache scopes, marking a scope as dynamic has no effect,\n      // because the outer cache scope creates a cache boundary. This is\n      // subtly different from reading a dynamic data source, which is\n      // forbidden inside a cache scope.\n      return\n    case 'private-cache':\n      // A private cache scope is already dynamic by definition.\n      return\n    case 'prerender':\n    case 'prerender-runtime':\n    case 'prerender-legacy':\n    case 'prerender-ppr':\n    case 'prerender-client':\n      break\n    case 'request':\n      if (process.env.NODE_ENV !== 'production') {\n        workUnitStore.usedDynamic = true\n      }\n      break\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  // It is important that we set this tracking value after aborting. Aborts are executed\n  // synchronously except for the case where you abort during render itself. By setting this\n  // value late we can use it to determine if any of the aborted tasks are the task that\n  // called the sync IO expression in the first place.\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with cacheComponents. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in cacheComponents mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      }\n    }\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n/**\n * Use this function when dynamically prerendering with dynamicIO.\n * We don't want to error, because it's better to return something\n * (and we've already aborted the render at the point where the sync dynamic error occured),\n * but we should log an error server-side.\n * @internal\n */\nexport function warnOnSyncDynamicError(dynamicTracking: DynamicTrackingState) {\n  if (dynamicTracking.syncDynamicErrorWithStack) {\n    // the server did something sync dynamic, likely\n    // leading to an early termination of the prerender.\n    console.error(dynamicTracking.syncDynamicErrorWithStack)\n  }\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createRenderInBrowserAbortSignal(): AbortSignal {\n  const controller = new AbortController()\n  controller.abort(new BailoutToCSRError('Render in Browser'))\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: WorkUnitStore\n): AbortSignal | undefined {\n  switch (workUnitStore.type) {\n    case 'prerender':\n    case 'prerender-runtime':\n      const controller = new AbortController()\n\n      if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If\n        // the input we're waiting on is coming from another cache, we do want\n        // to wait for it so that we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(() => {\n          controller.abort()\n        })\n      } else {\n        // Otherwise we're in the final render and we should already have all\n        // our caches filled.\n        // If the prerender uses stages, we have wait until the runtime stage,\n        // at which point all runtime inputs will be resolved.\n        // (otherwise, a runtime prerender might consider `cookies()` hanging\n        //  even though they'd resolve in the next task.)\n        //\n        // We might still be waiting on some microtasks so we\n        // wait one tick before giving up. When we give up, we still want to\n        // render the content of this cache as deeply as we can so that we can\n        // suspend as deeply as possible in the tree or not at all if we don't\n        // end up waiting for the input.\n        const runtimeStagePromise = getRuntimeStagePromise(workUnitStore)\n        if (runtimeStagePromise) {\n          runtimeStagePromise.then(() =>\n            scheduleOnNextTick(() => controller.abort())\n          )\n        } else {\n          scheduleOnNextTick(() => controller.abort())\n        }\n      }\n\n      return controller.signal\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'request':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      return undefined\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workStore && workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-client':\n      case 'prerender': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          // We are in a prerender with cacheComponents semantics. We are going to\n          // hang here and never resolve. This will cause the currently\n          // rendering component to effectively be a dynamic hole.\n          React.use(\n            makeHangingPromise(\n              workUnitStore.renderSignal,\n              workStore.route,\n              expression\n            )\n          )\n        }\n        break\n      }\n      case 'prerender-ppr': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          return postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        }\n        break\n      }\n      case 'prerender-runtime':\n        throw new InvariantError(\n          `\\`${expression}\\` was called during a runtime prerender. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'cache':\n      case 'private-cache':\n        throw new InvariantError(\n          `\\`${expression}\\` was called inside a cache scope. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'prerender-legacy':\n      case 'request':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\n\n// Common implicit body tags that React will treat as body when placed directly in html\nconst bodyAndImplicitTags =\n  'body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6'\n\n// Detects when RootLayoutBoundary (our framework marker component) appears\n// after Suspense in the component stack, indicating the root layout is wrapped\n// within a Suspense boundary. Ensures no body/html/implicit-body components are in between.\n//\n// Example matches:\n//   at Suspense (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\n//\n// Or with other components in between (but not body/html/implicit-body):\n//   at Suspense (<anonymous>)\n//   at SomeComponent (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\nconst hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex = new RegExp(\n  `\\\\n\\\\s+at Suspense \\\\(<anonymous>\\\\)(?:(?!\\\\n\\\\s+at (?:${bodyAndImplicitTags}) \\\\(<anonymous>\\\\))[\\\\s\\\\S])*?\\\\n\\\\s+at ${ROOT_LAYOUT_BOUNDARY_NAME} \\\\([^\\\\n]*\\\\)`\n)\n\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  workStore: WorkStore,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (\n    hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex.test(\n      componentStack\n    )\n  ) {\n    // For Suspense within body, the prelude wouldn't be empty so it wouldn't violate the empty static shells rule.\n    // But if you have Suspense above body, the prelude is empty but we allow that because having Suspense\n    // is an explicit signal from the user that they acknowledge the empty shell and want dynamic rendering.\n    dynamicValidation.hasAllowedDynamic = true\n    dynamicValidation.hasSuspenseAboveBody = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    // this error had a Suspense boundary above it so we don't need to report it as a source\n    // of disallowed\n    dynamicValidation.hasAllowedDynamic = true\n    return\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    // This task was the task that called the sync error.\n    dynamicValidation.dynamicErrors.push(\n      clientDynamic.syncDynamicErrorWithStack\n    )\n    return\n  } else {\n    const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentOrOwnerStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */\nfunction createErrorWithComponentOrOwnerStack(\n  message: string,\n  componentStack: string\n) {\n  const ownerStack =\n    process.env.NODE_ENV !== 'production' && React.captureOwnerStack\n      ? React.captureOwnerStack()\n      : null\n\n  const error = new Error(message)\n  error.stack = error.name + ': ' + message + (ownerStack ?? componentStack)\n  return error\n}\n\nexport enum PreludeState {\n  Full = 0,\n  Empty = 1,\n  Errored = 2,\n}\n\nexport function logDisallowedDynamicError(\n  workStore: WorkStore,\n  error: Error\n): void {\n  console.error(error)\n\n  if (!workStore.dev) {\n    if (workStore.hasReadableErrorStacks) {\n      console.error(\n        `To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`\n      )\n    } else {\n      console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`)\n    }\n  }\n}\n\nexport function throwIfDisallowedDynamic(\n  workStore: WorkStore,\n  prelude: PreludeState,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState\n): void {\n  if (prelude !== PreludeState.Full) {\n    if (dynamicValidation.hasSuspenseAboveBody) {\n      // This route has opted into allowing fully dynamic rendering\n      // by including a Suspense boundary above the body. In this case\n      // a lack of a shell is not considered disallowed so we simply return\n      return\n    }\n\n    if (serverDynamic.syncDynamicErrorWithStack) {\n      // There is no shell and the server did something sync dynamic likely\n      // leading to an early termination of the prerender before the shell\n      // could be completed. We terminate the build/validating render.\n      logDisallowedDynamicError(\n        workStore,\n        serverDynamic.syncDynamicErrorWithStack\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    // We didn't have any sync bailouts but there may be user code which\n    // blocked the root. We would have captured these during the prerender\n    // and can log them here and then terminate the build/validating render\n    const dynamicErrors = dynamicValidation.dynamicErrors\n    if (dynamicErrors.length > 0) {\n      for (let i = 0; i < dynamicErrors.length; i++) {\n        logDisallowedDynamicError(workStore, dynamicErrors[i])\n      }\n\n      throw new StaticGenBailoutError()\n    }\n\n    // If we got this far then the only other thing that could be blocking\n    // the root is dynamic Viewport. If this is dynamic then\n    // you need to opt into that by adding a Suspense boundary above the body\n    // to indicate your are ok with fully dynamic rendering.\n    if (dynamicValidation.hasDynamicViewport) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    if (prelude === PreludeState.Empty) {\n      // If we ever get this far then we messed up the tracking of invalid dynamic.\n      // We still adhere to the constraint that you must produce a shell but invite the\n      // user to report this as a bug in Next.js.\n      console.error(\n        `Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`\n      )\n      throw new StaticGenBailoutError()\n    }\n  } else {\n    if (\n      dynamicValidation.hasAllowedDynamic === false &&\n      dynamicValidation.hasDynamicMetadata\n    ) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`\n      )\n      throw new StaticGenBailoutError()\n    }\n  }\n}\n\nexport function delayUntilRuntimeStage<T>(\n  prerenderStore: PrerenderStoreModernRuntime,\n  result: Promise<T>\n): Promise<T> {\n  if (prerenderStore.runtimeStagePromise) {\n    return prerenderStore.runtimeStagePromise.then(() => result)\n  }\n  return result\n}\n"], "names": ["Postpone", "PreludeState", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createRenderInBrowserAbortSignal", "delayUntilRuntimeStage", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "logDisallowedDynamicError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "warnOnSyncDynamicError", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicErrorWithStack", "hasSuspenseAboveBody", "hasDynamicMetadata", "hasDynamicViewport", "hasAllowedDynamic", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "undefined", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "console", "workUnitAsyncStorage", "getStore", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "BailoutToCSRError", "cacheSignal", "inputReady", "then", "runtimeStagePromise", "getRuntimeStagePromise", "scheduleOnNextTick", "workStore", "workAsyncStorage", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "InvariantError", "hasSuspenseRegex", "bodyAndImplicitTags", "hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex", "RegExp", "ROOT_LAYOUT_BOUNDARY_NAME", "hasMetadataRegex", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentOrOwnerStack", "ownerStack", "captureOwnerStack", "name", "dev", "hasReadableErrorStacks", "prelude", "i", "result"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6VeA,QAAQ;eAARA;;IA4XJC,YAAY;eAAZA;;IApbIC,2CAA2C;eAA3CA;;IArCAC,kCAAkC;eAAlCA;;IAuLAC,mBAAmB;eAAnBA;;IAkIAC,qBAAqB;eAArBA;;IA5HAC,oBAAoB;eAApBA;;IArXAC,0BAA0B;eAA1BA;;IAUAC,4BAA4B;eAA5BA;;IAmbAC,6BAA6B;eAA7BA;;IAXAC,gCAAgC;eAAhCA;;IA8TAC,sBAAsB;eAAtBA;;IAhXAC,wBAAwB;eAAxBA;;IA5WAC,qBAAqB;eAArBA;;IAsSAC,iBAAiB;eAAjBA;;IAwCAC,2BAA2B;eAA3BA;;IAqTAC,yBAAyB;eAAzBA;;IAtnBAC,yBAAyB;eAAzBA;;IA6PAC,oBAAoB;eAApBA;;IA4YAC,wBAAwB;eAAxBA;;IA/jBAC,gCAAgC;eAAhCA;;IAueAC,yBAAyB;eAAzBA;;IA9cAC,+BAA+B;eAA/BA;;IAuEAC,qCAAqC;eAArCA;;IAgEHC,sCAAsC;eAAtCA;;IAqPGC,qBAAqB;eAArBA;;IA9PAC,sBAAsB;eAAtBA;;;8DA9TE;oCAEiB;yCACG;8CAI/B;0CAC0B;uCACE;mCAM5B;2BAC4B;8BACD;gCACH;;;;;;AAE/B,MAAMC,cAAc,OAAOC,cAAK,CAACC,iBAAiB,KAAK;AAwChD,SAAStB,2BACduB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,2BAA2B;IAC7B;AACF;AAEO,SAASxB;IACd,OAAO;QACLyB,sBAAsB;QACtBC,oBAAoB;QACpBC,oBAAoB;QACpBC,mBAAmB;QACnBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASxB,sBACdyB,aAAmC;QAE5BA;IAAP,QAAOA,kCAAAA,cAAcP,eAAe,CAAC,EAAE,qBAAhCO,gCAAkCC,UAAU;AACrD;AASO,SAAStB,0BACduB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,OAAQA,cAAcC,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,iEAAiE;gBACjE,kEAAkE;gBAClE,gEAAgE;gBAChE,kCAAkC;gBAClC;YACF,KAAK;gBACH,0DAA0D;gBAC1D;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACED;QACJ;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAID,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,OAAQA,cAAcC,IAAI;YACxB,KAAK;gBACH,OAAOxB,qBACLsB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;YAEjC,KAAK;gBACHP,cAAcQ,UAAU,GAAG;gBAE3B,uEAAuE;gBACvE,oCAAoC;gBACpC,MAAMC,MAAM,qBAEX,CAFW,IAAIC,sCAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAC,MAAMY,uBAAuB,GAAGb;gBAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;gBAEnC,MAAMJ;YACR,KAAK;gBACH,IAAIK,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;oBACzChB,cAAciB,WAAW,GAAG;gBAC9B;gBACA;YACF;gBACEjB;QACJ;IACF;AACF;AAQO,SAASrB,iCACdmB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,qBAEX,CAFW,IAAIC,sCAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS5B,gCAAgCmB,aAA4B;IAC1E,OAAQA,cAAcC,IAAI;QACxB,KAAK;QACL,KAAK;YACH,iEAAiE;YACjE,kEAAkE;YAClE,gEAAgE;YAChE,kCAAkC;YAClC;QACF,KAAK;YACH,0DAA0D;YAC1D;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH;QACF,KAAK;YACH,IAAIa,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzChB,cAAciB,WAAW,GAAG;YAC9B;YACA;QACF;YACEjB;IACJ;AACF;AAEA,SAASmB,oCACPb,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAME,SAAS,CAAC,MAAM,EAAEd,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAMuB,QAAQC,gCAAgCF;IAE9CF,eAAeK,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMd,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACmC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfZ,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIqC,QAAQb,KAAK,GACjBc;YACJ7B;QACF;IACF;AACF;AAEO,SAASpC,mCACd4C,KAAa,EACbR,UAAkB,EAClB8B,cAAqB,EACrBV,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtDY,oCAAoCb,OAAOR,YAAYoB;IACvD,sFAAsF;IACtF,0FAA0F;IAC1F,sFAAsF;IACtF,oDAAoD;IACpD,IAAIX,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBhB,yBAAyB,GAAGqC;QAC9C;IACF;AACF;AAEO,SAAS9C,sCACd+C,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASrE,4CACd6C,KAAa,EACbR,UAAkB,EAClB8B,cAAqB,EACrBV,cAAoC;IAEpC,MAAMa,kBAAkBb,eAAeK,UAAU,CAACS,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1Bd,oCAAoCb,OAAOR,YAAYoB;QACvD,sFAAsF;QACtF,0FAA0F;QAC1F,sFAAsF;QACtF,oDAAoD;QACpD,MAAMX,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBhB,yBAAyB,GAAGqC;YAC9C;QACF;IACF;IACA,MAAMN,gCACJ,CAAC,MAAM,EAAEhB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AASO,SAASb,uBAAuBsB,eAAqC;IAC1E,IAAIA,gBAAgBhB,yBAAyB,EAAE;QAC7C,gDAAgD;QAChD,oDAAoD;QACpD2C,QAAQb,KAAK,CAACd,gBAAgBhB,yBAAyB;IACzD;AACF;AAGO,MAAMR,yCACXD;AASK,SAASvB,SAAS,EAAE6D,MAAM,EAAEd,KAAK,EAAiB;IACvD,MAAMY,iBAAiBiB,kDAAoB,CAACC,QAAQ;IACpD,MAAM7B,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACN9B,qBAAqB6B,OAAOc,QAAQb;AACtC;AAEO,SAAS9B,qBACd6B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C8B;IACA,IAAI9B,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACmC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfZ,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIqC,QAAQb,KAAK,GACjBc;YACJ7B;QACF;IACF;IAEAX,cAAK,CAACC,iBAAiB,CAACkD,qBAAqBhC,OAAOR;AACtD;AAEA,SAASwC,qBAAqBhC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAASzB,kBAAkBoC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAO,AAACA,IAAY8B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAwB,AAAC/B,IAAY8B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBpB,MAAc;IAC7C,OACEA,OAAOqB,QAAQ,CACb,sEAEFrB,OAAOqB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,qBAEL,CAFK,IAAIZ,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMgB,6BAA6B;AAEnC,SAASpB,gCAAgCiB,OAAe;IACtD,MAAMlB,QAAQ,qBAAkB,CAAlB,IAAIK,MAAMa,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BlB,MAAcsB,MAAM,GAAGD;IACzB,OAAOrB;AACT;AAMO,SAAS/C,4BACd+C,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACV,AAACA,MAAcsB,MAAM,KAAKD,8BAC1B,UAAUrB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAAS/D,oBACd2B,eAAqC;IAErC,OAAOA,gBAAgBsD,MAAM,GAAG;AAClC;AAEO,SAAS/E,qBACdgF,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcvD,eAAe,CAACmC,IAAI,IAAIqB,cAAcxD,eAAe;IACnE,OAAOuD,cAAcvD,eAAe;AACtC;AAEO,SAASnB,yBACdmB,eAAqC;IAErC,OAAOA,gBACJyD,MAAM,CACL,CAACC,SACC,OAAOA,OAAOnC,KAAK,KAAK,YAAYmC,OAAOnC,KAAK,CAAC+B,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEnD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLqC,KAAK,CAAC,KACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAEvD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASwB;IACP,IAAI,CAACnD,aAAa;QAChB,MAAM,qBAEL,CAFK,IAAIwC,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAASzD;IACd,MAAMsD,aAAa,IAAI+B;IACvB/B,WAAWC,KAAK,CAAC,qBAA0C,CAA1C,IAAI+B,+BAAiB,CAAC,sBAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAAyC;IAC1D,OAAOhC,WAAWS,MAAM;AAC1B;AAOO,SAAShE,8BACdgC,aAA4B;IAE5B,OAAQA,cAAcC,IAAI;QACxB,KAAK;QACL,KAAK;YACH,MAAMsB,aAAa,IAAI+B;YAEvB,IAAItD,cAAcwD,WAAW,EAAE;gBAC7B,sEAAsE;gBACtE,sEAAsE;gBACtE,8DAA8D;gBAC9DxD,cAAcwD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;oBAC1CnC,WAAWC,KAAK;gBAClB;YACF,OAAO;gBACL,qEAAqE;gBACrE,qBAAqB;gBACrB,sEAAsE;gBACtE,sDAAsD;gBACtD,qEAAqE;gBACrE,iDAAiD;gBACjD,EAAE;gBACF,qDAAqD;gBACrD,oEAAoE;gBACpE,sEAAsE;gBACtE,sEAAsE;gBACtE,gCAAgC;gBAChC,MAAMmC,sBAAsBC,IAAAA,oDAAsB,EAAC5D;gBACnD,IAAI2D,qBAAqB;oBACvBA,oBAAoBD,IAAI,CAAC,IACvBG,IAAAA,6BAAkB,EAAC,IAAMtC,WAAWC,KAAK;gBAE7C,OAAO;oBACLqC,IAAAA,6BAAkB,EAAC,IAAMtC,WAAWC,KAAK;gBAC3C;YACF;YAEA,OAAOD,WAAWS,MAAM;QAC1B,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOL;QACT;YACE3B;IACJ;AACF;AAEO,SAASpC,sBACdkC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACmC,IAAI,CAAC;YACnCZ,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIqC,QAAQb,KAAK,GACjBc;YACJ7B;QACF;IACF;AACF;AAEO,SAASd,sBAAsBc,UAAkB;IACtD,MAAMgE,YAAYC,0CAAgB,CAAC3B,QAAQ;IAC3C,MAAMpC,gBAAgBmC,kDAAoB,CAACC,QAAQ;IACnD,IAAI0B,aAAa9D,eAAe;QAC9B,OAAQA,cAAcC,IAAI;YACxB,KAAK;YACL,KAAK;gBAAa;oBAChB,MAAM+D,iBAAiBhE,cAAciE,mBAAmB;oBACxD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;wBAC7C,wEAAwE;wBACxE,6DAA6D;wBAC7D,wDAAwD;wBACxD/E,cAAK,CAACgF,GAAG,CACPC,IAAAA,yCAAkB,EAChBpE,cAAcqE,YAAY,EAC1BP,UAAUxD,KAAK,EACfR;oBAGN;oBACA;gBACF;YACA,KAAK;gBAAiB;oBACpB,MAAMkE,iBAAiBhE,cAAciE,mBAAmB;oBACxD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;wBAC7C,OAAOzF,qBACLqF,UAAUxD,KAAK,EACfR,YACAE,cAAcO,eAAe;oBAEjC;oBACA;gBACF;YACA,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAI+D,8BAAc,CACtB,CAAC,EAAE,EAAExE,WAAW,uEAAuE,EAAEA,WAAW,+EAA+E,CAAC,GADhL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIwE,8BAAc,CACtB,CAAC,EAAE,EAAExE,WAAW,iEAAiE,EAAEA,WAAW,+EAA+E,CAAC,GAD1K,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEE;QACJ;IACF;AACF;AAEA,MAAMuE,mBAAmB;AAEzB,uFAAuF;AACvF,MAAMC,sBACJ;AAEF,2EAA2E;AAC3E,+EAA+E;AAC/E,4FAA4F;AAC5F,EAAE;AACF,mBAAmB;AACnB,8BAA8B;AAC9B,mDAAmD;AACnD,EAAE;AACF,yEAAyE;AACzE,8BAA8B;AAC9B,mCAAmC;AACnC,mDAAmD;AACnD,MAAMC,4DAA4D,IAAIC,OACpE,CAAC,uDAAuD,EAAEF,oBAAoB,yCAAyC,EAAEG,4CAAyB,CAAC,cAAc,CAAC;AAGpK,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,yCAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIJ,OAC3B,CAAC,UAAU,EAAEK,yCAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIN,OAAO,CAAC,UAAU,EAAEO,uCAAoB,CAAC,QAAQ,CAAC;AAEtE,SAASrG,0BACdkF,SAAoB,EACpBoB,cAAsB,EACtBC,iBAAyC,EACzCrC,aAAmC;IAEnC,IAAIkC,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIN,iBAAiBQ,IAAI,CAACF,iBAAiB;QAChDC,kBAAkB1F,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIqF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBzF,kBAAkB,GAAG;QACvC;IACF,OAAO,IACL+E,0DAA0DW,IAAI,CAC5DF,iBAEF;QACA,+GAA+G;QAC/G,sGAAsG;QACtG,wGAAwG;QACxGC,kBAAkBxF,iBAAiB,GAAG;QACtCwF,kBAAkB3F,oBAAoB,GAAG;QACzC;IACF,OAAO,IAAI+E,iBAAiBa,IAAI,CAACF,iBAAiB;QAChD,wFAAwF;QACxF,gBAAgB;QAChBC,kBAAkBxF,iBAAiB,GAAG;QACtC;IACF,OAAO,IAAImD,cAAcvD,yBAAyB,EAAE;QAClD,qDAAqD;QACrD4F,kBAAkBvF,aAAa,CAAC6B,IAAI,CAClCqB,cAAcvD,yBAAyB;QAEzC;IACF,OAAO;QACL,MAAMgD,UAAU,CAAC,OAAO,EAAEuB,UAAUxD,KAAK,CAAC,2NAA2N,CAAC;QACtQ,MAAMe,QAAQgE,qCAAqC9C,SAAS2C;QAC5DC,kBAAkBvF,aAAa,CAAC6B,IAAI,CAACJ;QACrC;IACF;AACF;AAEA;;;CAGC,GACD,SAASgE,qCACP9C,OAAe,EACf2C,cAAsB;IAEtB,MAAMI,aACJxE,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB7B,cAAK,CAACoG,iBAAiB,GAC5DpG,cAAK,CAACoG,iBAAiB,KACvB;IAEN,MAAMlE,QAAQ,qBAAkB,CAAlB,IAAIK,MAAMa,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BlB,MAAMR,KAAK,GAAGQ,MAAMmE,IAAI,GAAG,OAAOjD,UAAW+C,CAAAA,cAAcJ,cAAa;IACxE,OAAO7D;AACT;AAEO,IAAA,AAAK7D,sCAAAA;;;;WAAAA;;AAML,SAASe,0BACduF,SAAoB,EACpBzC,KAAY;IAEZa,QAAQb,KAAK,CAACA;IAEd,IAAI,CAACyC,UAAU2B,GAAG,EAAE;QAClB,IAAI3B,UAAU4B,sBAAsB,EAAE;YACpCxD,QAAQb,KAAK,CACX,CAAC,iIAAiI,EAAEyC,UAAUxD,KAAK,CAAC,2CAA2C,CAAC;QAEpM,OAAO;YACL4B,QAAQb,KAAK,CAAC,CAAC;0EACqD,EAAEyC,UAAUxD,KAAK,CAAC;qGACS,CAAC;QAClG;IACF;AACF;AAEO,SAAS5B,yBACdoF,SAAoB,EACpB6B,OAAqB,EACrBR,iBAAyC,EACzCtC,aAAmC;IAEnC,IAAI8C,eAA+B;QACjC,IAAIR,kBAAkB3F,oBAAoB,EAAE;YAC1C,6DAA6D;YAC7D,gEAAgE;YAChE,qEAAqE;YACrE;QACF;QAEA,IAAIqD,cAActD,yBAAyB,EAAE;YAC3C,qEAAqE;YACrE,oEAAoE;YACpE,gEAAgE;YAChEhB,0BACEuF,WACAjB,cAActD,yBAAyB;YAEzC,MAAM,IAAIc,8CAAqB;QACjC;QAEA,oEAAoE;QACpE,sEAAsE;QACtE,uEAAuE;QACvE,MAAMT,gBAAgBuF,kBAAkBvF,aAAa;QACrD,IAAIA,cAAcgD,MAAM,GAAG,GAAG;YAC5B,IAAK,IAAIgD,IAAI,GAAGA,IAAIhG,cAAcgD,MAAM,EAAEgD,IAAK;gBAC7CrH,0BAA0BuF,WAAWlE,aAAa,CAACgG,EAAE;YACvD;YAEA,MAAM,IAAIvF,8CAAqB;QACjC;QAEA,sEAAsE;QACtE,wDAAwD;QACxD,yEAAyE;QACzE,wDAAwD;QACxD,IAAI8E,kBAAkBzF,kBAAkB,EAAE;YACxCwC,QAAQb,KAAK,CACX,CAAC,OAAO,EAAEyC,UAAUxD,KAAK,CAAC,8QAA8Q,CAAC;YAE3S,MAAM,IAAID,8CAAqB;QACjC;QAEA,IAAIsF,eAAgC;YAClC,6EAA6E;YAC7E,iFAAiF;YACjF,2CAA2C;YAC3CzD,QAAQb,KAAK,CACX,CAAC,OAAO,EAAEyC,UAAUxD,KAAK,CAAC,wGAAwG,CAAC;YAErI,MAAM,IAAID,8CAAqB;QACjC;IACF,OAAO;QACL,IACE8E,kBAAkBxF,iBAAiB,KAAK,SACxCwF,kBAAkB1F,kBAAkB,EACpC;YACAyC,QAAQb,KAAK,CACX,CAAC,OAAO,EAAEyC,UAAUxD,KAAK,CAAC,8PAA8P,CAAC;YAE3R,MAAM,IAAID,8CAAqB;QACjC;IACF;AACF;AAEO,SAASnC,uBACdgD,cAA2C,EAC3C2E,MAAkB;IAElB,IAAI3E,eAAeyC,mBAAmB,EAAE;QACtC,OAAOzC,eAAeyC,mBAAmB,CAACD,IAAI,CAAC,IAAMmC;IACvD;IACA,OAAOA;AACT", "ignoreList": [0]}