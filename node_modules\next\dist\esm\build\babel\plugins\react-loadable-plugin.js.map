{"version": 3, "sources": ["../../../../src/build/babel/plugins/react-loadable-plugin.ts"], "sourcesContent": ["/**\nCOPYRIGHT (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWAR\n*/\n// This file is https://github.com/jamiebuilds/react-loadable/blob/master/src/babel.js\n// - Modified to also look for `next/dynamic`\n// - Modified to put `webpack` and `modules` under `loadableGenerated` to be\n//   backwards compatible with next/dynamic which has a `modules` key\n// - Modified to support `dynamic(import('something'))` and\n//   `dynamic(import('something'), options)\n// - Removed `Loadable.Map` support, `next/dynamic` uses overloaded arguments\n//   instead of a separate API\n\nimport type {\n  NodePath,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\nimport type { PluginObj } from 'next/dist/compiled/babel/core'\n\nimport { relative as relativePath } from 'path'\n\nexport default function ({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj {\n  return {\n    visitor: {\n      ImportDeclaration(\n        path: NodePath<BabelTypes.ImportDeclaration>,\n        state: any\n      ) {\n        let source = path.node.source.value\n        if (source !== 'next/dynamic') return\n\n        let defaultSpecifier = path.get('specifiers').find((specifier) => {\n          return specifier.isImportDefaultSpecifier()\n        })\n\n        if (!defaultSpecifier) return\n\n        const bindingName = defaultSpecifier.node.local.name\n        const binding = path.scope.getBinding(bindingName)\n\n        if (!binding) {\n          return\n        }\n\n        binding.referencePaths.forEach((refPath) => {\n          let callExpression = refPath.parentPath\n          if (!callExpression?.isCallExpression()) return\n\n          let args = callExpression.get('arguments')\n          if (args.length > 2) {\n            throw callExpression.buildCodeFrameError(\n              'next/dynamic only accepts 2 arguments'\n            )\n          }\n\n          if (!args[0]) {\n            return\n          }\n\n          let loader\n          let options\n\n          if (args[0].isObjectExpression()) {\n            options = args[0]\n          } else {\n            if (!args[1]) {\n              callExpression.node.arguments.push(t.objectExpression([]))\n            }\n            // This is needed as the code is modified above\n            args = callExpression.get('arguments')\n            loader = args[0]\n            options = args[1]\n          }\n\n          if (!options.isObjectExpression()) return\n          const options_ = options as NodePath<BabelTypes.ObjectExpression>\n\n          let properties = options_.get('properties')\n          let propertiesMap: {\n            [key: string]: NodePath<\n              | BabelTypes.ObjectProperty\n              | BabelTypes.ObjectMethod\n              | BabelTypes.SpreadElement\n              | BabelTypes.BooleanLiteral\n            >\n          } = {}\n\n          properties.forEach((property) => {\n            const key: any = property.get('key')\n            propertiesMap[key.node.name] = property\n          })\n\n          if (propertiesMap.loadableGenerated) {\n            return\n          }\n\n          if (propertiesMap.loader) {\n            loader = propertiesMap.loader.get('value')\n          }\n\n          if (propertiesMap.modules) {\n            loader = propertiesMap.modules.get('value')\n          }\n\n          if (!loader || Array.isArray(loader)) {\n            return\n          }\n          const dynamicImports: BabelTypes.Expression[] = []\n          const dynamicKeys: BabelTypes.Expression[] = []\n\n          if (propertiesMap.ssr) {\n            const ssr = propertiesMap.ssr.get('value')\n            const nodePath = Array.isArray(ssr) ? undefined : ssr\n\n            if (nodePath) {\n              const nonSSR =\n                nodePath.node.type === 'BooleanLiteral' &&\n                nodePath.node.value === false\n              // If `ssr` is set to `false`, erase the loader for server side\n              if (nonSSR && loader && state.file.opts.caller?.isServer) {\n                loader.replaceWith(\n                  t.arrowFunctionExpression([], t.nullLiteral(), true)\n                )\n              }\n            }\n          }\n\n          loader.traverse({\n            Import(importPath) {\n              const importArguments = importPath.parentPath.get('arguments')\n              if (!Array.isArray(importArguments)) return\n              const node: any = importArguments[0].node\n              dynamicImports.push(node)\n              dynamicKeys.push(\n                t.binaryExpression(\n                  '+',\n                  t.stringLiteral(\n                    (state.file.opts.caller?.srcDir\n                      ? relativePath(\n                          state.file.opts.caller.srcDir,\n                          state.file.opts.filename\n                        )\n                      : state.file.opts.filename) + ' -> '\n                  ),\n                  node\n                )\n              )\n            },\n          })\n\n          if (!dynamicImports.length) return\n\n          options.node.properties.push(\n            t.objectProperty(\n              t.identifier('loadableGenerated'),\n              t.objectExpression(\n                state.file.opts.caller?.isDev ||\n                  state.file.opts.caller?.isServer\n                  ? [\n                      t.objectProperty(\n                        t.identifier('modules'),\n                        t.arrayExpression(dynamicKeys)\n                      ),\n                    ]\n                  : [\n                      t.objectProperty(\n                        t.identifier('webpack'),\n                        t.arrowFunctionExpression(\n                          [],\n                          t.arrayExpression(\n                            dynamicImports.map((dynamicImport) => {\n                              return t.callExpression(\n                                t.memberExpression(\n                                  t.identifier('require'),\n                                  t.identifier('resolveWeak')\n                                ),\n                                [dynamicImport]\n                              )\n                            })\n                          )\n                        )\n                      ),\n                    ]\n              )\n            )\n          )\n\n          // Turns `dynamic(import('something'))` into `dynamic(() => import('something'))` for backwards compat.\n          // This is the replicate the behavior in versions below Next.js 7 where we magically handled not executing the `import()` too.\n          // We'll deprecate this behavior and provide a codemod for it in 7.1.\n          if (loader.isCallExpression()) {\n            const arrowFunction = t.arrowFunctionExpression([], loader.node)\n            loader.replaceWith(arrowFunction)\n          }\n        })\n      },\n    },\n  }\n}\n"], "names": ["relative", "relativePath", "types", "t", "visitor", "ImportDeclaration", "path", "state", "source", "node", "value", "defaultSpecifier", "get", "find", "specifier", "isImportDefaultSpecifier", "bindingName", "local", "name", "binding", "scope", "getBinding", "referencePaths", "for<PERSON>ach", "refPath", "callExpression", "parentPath", "isCallExpression", "args", "length", "buildCodeFrameError", "loader", "options", "isObjectExpression", "arguments", "push", "objectExpression", "options_", "properties", "propertiesMap", "property", "key", "loadableGenerated", "modules", "Array", "isArray", "dynamicImports", "dynamic<PERSON>eys", "ssr", "nodePath", "undefined", "nonSSR", "type", "file", "opts", "caller", "isServer", "replaceWith", "arrowFunctionExpression", "nullLiteral", "traverse", "Import", "importPath", "importArguments", "binaryExpression", "stringLiteral", "srcDir", "filename", "objectProperty", "identifier", "isDev", "arrayExpression", "map", "dynamicImport", "memberExpression", "arrowFunction"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;AAmBA,GACA,sFAAsF;AACtF,6CAA6C;AAC7C,4EAA4E;AAC5E,qEAAqE;AACrE,2DAA2D;AAC3D,2CAA2C;AAC3C,6EAA6E;AAC7E,8BAA8B;AAQ9B,SAASA,YAAYC,YAAY,QAAQ,OAAM;AAE/C,eAAe,SAAU,EACvBC,OAAOC,CAAC,EAGT;IACC,OAAO;QACLC,SAAS;YACPC,mBACEC,IAA4C,EAC5CC,KAAU;gBAEV,IAAIC,SAASF,KAAKG,IAAI,CAACD,MAAM,CAACE,KAAK;gBACnC,IAAIF,WAAW,gBAAgB;gBAE/B,IAAIG,mBAAmBL,KAAKM,GAAG,CAAC,cAAcC,IAAI,CAAC,CAACC;oBAClD,OAAOA,UAAUC,wBAAwB;gBAC3C;gBAEA,IAAI,CAACJ,kBAAkB;gBAEvB,MAAMK,cAAcL,iBAAiBF,IAAI,CAACQ,KAAK,CAACC,IAAI;gBACpD,MAAMC,UAAUb,KAAKc,KAAK,CAACC,UAAU,CAACL;gBAEtC,IAAI,CAACG,SAAS;oBACZ;gBACF;gBAEAA,QAAQG,cAAc,CAACC,OAAO,CAAC,CAACC;wBAgHxBjB,yBACEA;oBAhHR,IAAIkB,iBAAiBD,QAAQE,UAAU;oBACvC,IAAI,EAACD,kCAAAA,eAAgBE,gBAAgB,KAAI;oBAEzC,IAAIC,OAAOH,eAAeb,GAAG,CAAC;oBAC9B,IAAIgB,KAAKC,MAAM,GAAG,GAAG;wBACnB,MAAMJ,eAAeK,mBAAmB,CACtC;oBAEJ;oBAEA,IAAI,CAACF,IAAI,CAAC,EAAE,EAAE;wBACZ;oBACF;oBAEA,IAAIG;oBACJ,IAAIC;oBAEJ,IAAIJ,IAAI,CAAC,EAAE,CAACK,kBAAkB,IAAI;wBAChCD,UAAUJ,IAAI,CAAC,EAAE;oBACnB,OAAO;wBACL,IAAI,CAACA,IAAI,CAAC,EAAE,EAAE;4BACZH,eAAehB,IAAI,CAACyB,SAAS,CAACC,IAAI,CAAChC,EAAEiC,gBAAgB,CAAC,EAAE;wBAC1D;wBACA,+CAA+C;wBAC/CR,OAAOH,eAAeb,GAAG,CAAC;wBAC1BmB,SAASH,IAAI,CAAC,EAAE;wBAChBI,UAAUJ,IAAI,CAAC,EAAE;oBACnB;oBAEA,IAAI,CAACI,QAAQC,kBAAkB,IAAI;oBACnC,MAAMI,WAAWL;oBAEjB,IAAIM,aAAaD,SAASzB,GAAG,CAAC;oBAC9B,IAAI2B,gBAOA,CAAC;oBAELD,WAAWf,OAAO,CAAC,CAACiB;wBAClB,MAAMC,MAAWD,SAAS5B,GAAG,CAAC;wBAC9B2B,aAAa,CAACE,IAAIhC,IAAI,CAACS,IAAI,CAAC,GAAGsB;oBACjC;oBAEA,IAAID,cAAcG,iBAAiB,EAAE;wBACnC;oBACF;oBAEA,IAAIH,cAAcR,MAAM,EAAE;wBACxBA,SAASQ,cAAcR,MAAM,CAACnB,GAAG,CAAC;oBACpC;oBAEA,IAAI2B,cAAcI,OAAO,EAAE;wBACzBZ,SAASQ,cAAcI,OAAO,CAAC/B,GAAG,CAAC;oBACrC;oBAEA,IAAI,CAACmB,UAAUa,MAAMC,OAAO,CAACd,SAAS;wBACpC;oBACF;oBACA,MAAMe,iBAA0C,EAAE;oBAClD,MAAMC,cAAuC,EAAE;oBAE/C,IAAIR,cAAcS,GAAG,EAAE;wBACrB,MAAMA,MAAMT,cAAcS,GAAG,CAACpC,GAAG,CAAC;wBAClC,MAAMqC,WAAWL,MAAMC,OAAO,CAACG,OAAOE,YAAYF;wBAElD,IAAIC,UAAU;gCAKY1C;4BAJxB,MAAM4C,SACJF,SAASxC,IAAI,CAAC2C,IAAI,KAAK,oBACvBH,SAASxC,IAAI,CAACC,KAAK,KAAK;4BAC1B,+DAA+D;4BAC/D,IAAIyC,UAAUpB,YAAUxB,2BAAAA,MAAM8C,IAAI,CAACC,IAAI,CAACC,MAAM,qBAAtBhD,yBAAwBiD,QAAQ,GAAE;gCACxDzB,OAAO0B,WAAW,CAChBtD,EAAEuD,uBAAuB,CAAC,EAAE,EAAEvD,EAAEwD,WAAW,IAAI;4BAEnD;wBACF;oBACF;oBAEA5B,OAAO6B,QAAQ,CAAC;wBACdC,QAAOC,UAAU;gCASRvD;4BARP,MAAMwD,kBAAkBD,WAAWpC,UAAU,CAACd,GAAG,CAAC;4BAClD,IAAI,CAACgC,MAAMC,OAAO,CAACkB,kBAAkB;4BACrC,MAAMtD,OAAYsD,eAAe,CAAC,EAAE,CAACtD,IAAI;4BACzCqC,eAAeX,IAAI,CAAC1B;4BACpBsC,YAAYZ,IAAI,CACdhC,EAAE6D,gBAAgB,CAChB,KACA7D,EAAE8D,aAAa,CACb,AAAC1D,CAAAA,EAAAA,0BAAAA,MAAM8C,IAAI,CAACC,IAAI,CAACC,MAAM,qBAAtBhD,wBAAwB2D,MAAM,IAC3BjE,aACEM,MAAM8C,IAAI,CAACC,IAAI,CAACC,MAAM,CAACW,MAAM,EAC7B3D,MAAM8C,IAAI,CAACC,IAAI,CAACa,QAAQ,IAE1B5D,MAAM8C,IAAI,CAACC,IAAI,CAACa,QAAQ,AAAD,IAAK,SAElC1D;wBAGN;oBACF;oBAEA,IAAI,CAACqC,eAAejB,MAAM,EAAE;oBAE5BG,QAAQvB,IAAI,CAAC6B,UAAU,CAACH,IAAI,CAC1BhC,EAAEiE,cAAc,CACdjE,EAAEkE,UAAU,CAAC,sBACblE,EAAEiC,gBAAgB,CAChB7B,EAAAA,0BAAAA,MAAM8C,IAAI,CAACC,IAAI,CAACC,MAAM,qBAAtBhD,wBAAwB+D,KAAK,OAC3B/D,2BAAAA,MAAM8C,IAAI,CAACC,IAAI,CAACC,MAAM,qBAAtBhD,yBAAwBiD,QAAQ,IAC9B;wBACErD,EAAEiE,cAAc,CACdjE,EAAEkE,UAAU,CAAC,YACblE,EAAEoE,eAAe,CAACxB;qBAErB,GACD;wBACE5C,EAAEiE,cAAc,CACdjE,EAAEkE,UAAU,CAAC,YACblE,EAAEuD,uBAAuB,CACvB,EAAE,EACFvD,EAAEoE,eAAe,CACfzB,eAAe0B,GAAG,CAAC,CAACC;4BAClB,OAAOtE,EAAEsB,cAAc,CACrBtB,EAAEuE,gBAAgB,CAChBvE,EAAEkE,UAAU,CAAC,YACblE,EAAEkE,UAAU,CAAC,iBAEf;gCAACI;6BAAc;wBAEnB;qBAIP;oBAKX,uGAAuG;oBACvG,8HAA8H;oBAC9H,qEAAqE;oBACrE,IAAI1C,OAAOJ,gBAAgB,IAAI;wBAC7B,MAAMgD,gBAAgBxE,EAAEuD,uBAAuB,CAAC,EAAE,EAAE3B,OAAOtB,IAAI;wBAC/DsB,OAAO0B,WAAW,CAACkB;oBACrB;gBACF;YACF;QACF;IACF;AACF", "ignoreList": [0]}