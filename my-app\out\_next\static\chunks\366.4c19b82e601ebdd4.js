"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[366],{981:(t,e,n)=>{function r(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function i(t){let e="string"==typeof t?function(t){let e=t.trim();if(0===e.length)return null;let n=Array.from(new Set(e.split(/\s+/).map(t=>t.trim()).filter(Boolean)));if(0===n.length)return null;let i=n.map(r).join("|");return RegExp(`(${i})`,"gi")}(t):t;return{highlight(t){if(!e)return[{type:"text",content:t}];let n=[],r=0;for(let i of t.matchAll(e))r<i.index&&n.push({type:"text",content:t.substring(r,i.index)}),n.push({type:"text",content:i[0],styles:{highlight:!0}}),r=i.index+i[0].length;return r<t.length&&n.push({type:"text",content:t.substring(r)}),n}}}n.d(e,{r:()=>i})},3522:(t,e,n)=>{n.d(e,{B:()=>function t(e,n=!1){for(let r of Object.keys(e))void 0===e[r]&&delete e[r],n&&"object"==typeof e[r]&&null!==e[r]?t(e[r],n):n&&Array.isArray(e[r])&&e[r].forEach(e=>t(e,n));return e}})},4366:(t,e,n)=>{n.d(e,{search:()=>c});var r=n(3522),i=n(981),o=n(3279);async function l(t,e,n={}){let r=(0,i.r)(e);return(await (0,o.$P)(t,{term:e,tolerance:1,...n,boost:{title:2,..."boost"in n?n.boost:void 0}})).hits.map(t=>({type:"page",content:t.document.title,contentWithHighlights:r.highlight(t.document.title),id:t.document.url,url:t.document.url}))}async function a(t,e,n=[],l={}){"string"==typeof n&&(n=[n]);let u={...l,where:(0,r.B)({tags:n.length>0?{containsAll:n}:void 0,...l.where}),groupBy:{properties:["page_id"],maxResult:8,...l.groupBy}};e.length>0&&(u={...u,term:e,properties:["content"]});let s=(0,i.r)(e),c=await (0,o.$P)(t,u),h=[];for(let e of c.groups??[]){let n=e.values[0],r=(0,o.VT)(t,n);if(r)for(let t of(h.push({id:n,type:"page",content:r.content,contentWithHighlights:s.highlight(r.content),url:r.url}),e.result))"page"!==t.document.type&&h.push({id:t.document.id.toString(),content:t.document.content,contentWithHighlights:s.highlight(t.document.content),type:t.document.type,url:t.document.url})}return h}n(7505);var u=new Map;async function s({from:t="/api/search",initOrama:e=t=>(0,o.vt)({schema:{_:"string"},language:t})}){let n=u.get(t);if(n)return n;let r=async function(){let n=await fetch(t);if(!n.ok)throw Error(`failed to fetch exported search indexes from ${t}, make sure the search database is exported and available for client.`);let r=await n.json(),i=new Map;if("i18n"===r.type)return await Promise.all(Object.entries(r.data).map(async([t,n])=>{let r=await e(t);(0,o.Hh)(r,n),i.set(t,{type:n.type,db:r})})),i;let l=await e();return(0,o.Hh)(l,r),i.set("",{type:r.type,db:l}),i}();return u.set(t,r),r}async function c(t,e){let{tag:n,locale:r}=e,i=(await s(e)).get(r??"");return i?"simple"===i.type?l(i,t):a(i.db,t,n):[]}}}]);