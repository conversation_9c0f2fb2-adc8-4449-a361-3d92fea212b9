"use strict";function o(e,t){if(!Array.isArray(e.body))throw new Error("Expected node with body array");if(e.body.length!==1)return!1;const r=e.body[0];return!t||Object.keys(t).every(n=>t[n]===r[n])}function a(e){return!(!i.has(e.type)||e.type==="AbsenceFunction"&&e.kind!=="repeater")}const i=new Set(["AbsenceFunction","CapturingGroup","Group","LookaroundAssertion","Regex"]);function s(e){return y.has(e.type)}const y=new Set(["AbsenceFunction","Backreference","CapturingGroup","Character","CharacterClass","CharacterSet","Group","Quantifier","Subroutine"]);export{o as hasOnlyChild,a as isAlternativeContainer,s as isQuantifiable};
//# sourceMappingURL=node-utils.js.map
