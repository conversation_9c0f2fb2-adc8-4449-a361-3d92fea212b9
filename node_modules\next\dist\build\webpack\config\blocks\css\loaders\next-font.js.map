{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/next-font.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { ConfigurationContext } from '../../../utils'\nimport { getClientStyleLoader } from './client'\nimport { cssFileResolve } from './file-resolve'\n\nexport function getNextFontLoader(\n  ctx: ConfigurationContext,\n  postcss: any,\n  fontLoaderPath: string\n): webpack.RuleSetUseItem[] {\n  const loaders: webpack.RuleSetUseItem[] = []\n\n  if (ctx.isClient) {\n    // Add appropriate development mode or production mode style\n    // loader\n    loaders.push(\n      getClientStyleLoader({\n        hasAppDir: ctx.hasAppDir,\n        isDevelopment: ctx.isDevelopment,\n        assetPrefix: ctx.assetPrefix,\n      })\n    )\n  }\n\n  loaders.push({\n    loader: require.resolve('../../../../loaders/css-loader/src'),\n    options: {\n      postcss,\n      importLoaders: 1,\n      // Use CJS mode for backwards compatibility:\n      esModule: false,\n      url: (url: string, resourcePath: string) =>\n        cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n      import: (url: string, _: any, resourcePath: string) =>\n        cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n      modules: {\n        // Do not transform class names (CJS mode backwards compatibility):\n        exportLocalsConvention: 'asIs',\n        // Server-side (Node.js) rendering support:\n        exportOnlyLocals: ctx.isServer,\n        // Disallow global style exports so we can code-split CSS and\n        // not worry about loading order.\n        mode: 'pure',\n        getLocalIdent: (\n          _context: any,\n          _localIdentName: any,\n          exportName: string,\n          _options: any,\n          meta: any\n        ) => {\n          // hash from next-font-loader\n          return `__${exportName}_${meta.fontFamilyHash}`\n        },\n      },\n      fontLoader: true,\n    },\n  })\n\n  loaders.push({\n    loader: 'next-font-loader',\n    options: {\n      isDev: ctx.isDevelopment,\n      isServer: ctx.isServer,\n      assetPrefix: ctx.assetPrefix,\n      deploymentId: ctx.deploymentId,\n      fontLoaderPath,\n      postcss,\n    },\n  })\n\n  return loaders\n}\n"], "names": ["getNextFontLoader", "ctx", "postcss", "fontLoaderPath", "loaders", "isClient", "push", "getClientStyleLoader", "hasAppDir", "isDevelopment", "assetPrefix", "loader", "require", "resolve", "options", "importLoaders", "esModule", "url", "resourcePath", "cssFileResolve", "experimental", "urlImports", "import", "_", "modules", "exportLocalsConvention", "exportOnlyLocals", "isServer", "mode", "getLocalIdent", "_context", "_localIdentName", "exportName", "_options", "meta", "fontFamilyHash", "fontLoader", "isDev", "deploymentId"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;wBAHqB;6BACN;AAExB,SAASA,kBACdC,GAAyB,EACzBC,OAAY,EACZC,cAAsB;IAEtB,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVC,IAAAA,4BAAoB,EAAC;YACnBC,WAAWP,IAAIO,SAAS;YACxBC,eAAeR,IAAIQ,aAAa;YAChCC,aAAaT,IAAIS,WAAW;QAC9B;IAEJ;IAEAN,QAAQE,IAAI,CAAC;QACXK,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPZ;YACAa,eAAe;YACf,4CAA4C;YAC5CC,UAAU;YACVC,KAAK,CAACA,KAAaC,eACjBC,IAAAA,2BAAc,EAACF,KAAKC,cAAcjB,IAAImB,YAAY,CAACC,UAAU;YAC/DC,QAAQ,CAACL,KAAaM,GAAQL,eAC5BC,IAAAA,2BAAc,EAACF,KAAKC,cAAcjB,IAAImB,YAAY,CAACC,UAAU;YAC/DG,SAAS;gBACP,mEAAmE;gBACnEC,wBAAwB;gBACxB,2CAA2C;gBAC3CC,kBAAkBzB,IAAI0B,QAAQ;gBAC9B,6DAA6D;gBAC7D,iCAAiC;gBACjCC,MAAM;gBACNC,eAAe,CACbC,UACAC,iBACAC,YACAC,UACAC;oBAEA,6BAA6B;oBAC7B,OAAO,CAAC,EAAE,EAAEF,WAAW,CAAC,EAAEE,KAAKC,cAAc,EAAE;gBACjD;YACF;YACAC,YAAY;QACd;IACF;IAEAhC,QAAQE,IAAI,CAAC;QACXK,QAAQ;QACRG,SAAS;YACPuB,OAAOpC,IAAIQ,aAAa;YACxBkB,UAAU1B,IAAI0B,QAAQ;YACtBjB,aAAaT,IAAIS,WAAW;YAC5B4B,cAAcrC,IAAIqC,YAAY;YAC9BnC;YACAD;QACF;IACF;IAEA,OAAOE;AACT", "ignoreList": [0]}