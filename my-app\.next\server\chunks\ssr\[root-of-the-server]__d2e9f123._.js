module.exports = {

"[project]/node_modules/fumadocs-ui/dist/components/dialog/search-default.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_fumadocs-ui_dist_components_dialog_search-default_76dfadcc.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-ui/dist/components/dialog/search-default.js [app-ssr] (ecmascript)");
    });
});
}),
"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/mixedbread-AG5AAOKO.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_f75082cb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/mixedbread-AG5AAOKO.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/static-IWYDJ3C5.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_fumadocs-core_dist_4d887622._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/static-IWYDJ3C5.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_fumadocs-core_dist_6437645c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_fumadocs-core_dist_e5dd523d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_fumadocs-core_dist_fetch-ITPHBPBE_1f8de440.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js [app-ssr] (ecmascript)");
    });
});
}),

};