{"version": 3, "sources": ["../../../src/server/route-modules/route-module.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'node:http'\nimport type {\n  InstrumentationOnRequestError,\n  RequestErrorContext,\n} from '../instrumentation/types'\nimport type { ParsedUrlQuery } from 'node:querystring'\nimport type { UrlWithParsedQuery } from 'node:url'\nimport type {\n  PrerenderManifest,\n  RequiredServerFilesManifest,\n} from '../../build'\nimport type { DevRoutesManifest } from '../lib/router-utils/setup-dev-bundler'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_REFERENCE_MANIFEST,\n  DYNAMIC_CSS_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_FILES_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n} from '../../shared/lib/constants'\nimport { parseReqUrl } from '../../lib/url'\nimport {\n  normalizeLocalePath,\n  type PathLocale,\n} from '../../shared/lib/i18n/normalize-locale-path'\nimport { isDynamicRoute } from '../../shared/lib/router/utils'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport { getServerUtils } from '../server-utils'\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { checkIsOnDemandRevalidate } from '../api-utils'\nimport type { PreviewData } from '../../types'\nimport type { BuildManifest } from '../get-page-files'\nimport type { ReactLoadableManifest } from '../load-components'\nimport type { NextFontManifest } from '../../build/webpack/plugins/next-font-manifest-plugin'\nimport { normalizeDataPath } from '../../shared/lib/page-path/normalize-data-path'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { addRequestMeta, getRequestMeta } from '../request-meta'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { isStaticMetadataRoute } from '../../lib/metadata/is-metadata-route'\nimport { IncrementalCache } from '../lib/incremental-cache'\nimport { initializeCacheHandlers, setCacheHandler } from '../use-cache/handlers'\nimport { interopDefault } from '../app-render/interop-default'\nimport type { RouteKind } from '../route-kind'\nimport type { BaseNextRequest } from '../base-http'\nimport type { I18NConfig, NextConfigComplete } from '../config-shared'\nimport ResponseCache, { type ResponseGenerator } from '../response-cache'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport {\n  RouterServerContextSymbol,\n  routerServerGlobal,\n  type RouterServerContext,\n} from '../lib/router-utils/router-server-context'\nimport { decodePathParams } from '../lib/router-utils/decode-path-params'\nimport { removeTrailingSlash } from '../../shared/lib/router/utils/remove-trailing-slash'\nimport { isInterceptionRouteRewrite } from '../../lib/generate-interception-routes-rewrites'\n\n/**\n * RouteModuleOptions is the options that are passed to the route module, other\n * route modules should extend this class to add specific options for their\n * route.\n */\nexport interface RouteModuleOptions<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  readonly definition: Readonly<D>\n  readonly userland: Readonly<U>\n  readonly distDir: string\n  readonly relativeProjectDir: string\n}\n\n/**\n * RouteHandlerContext is the base context for a route handler.\n */\nexport interface RouteModuleHandleContext {\n  /**\n   * Any matched parameters for the request. This is only defined for dynamic\n   * routes.\n   */\n  params: Record<string, string | string[] | undefined> | undefined\n}\n\nconst dynamicImportEsmDefault = (id: string) =>\n  import(/* webpackIgnore: true */ /* turbopackIgnore: true */ id).then(\n    (mod) => mod.default || mod\n  )\n\n/**\n * RouteModule is the base class for all route modules. This class should be\n * extended by all route modules.\n */\nexport abstract class RouteModule<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  /**\n   * The userland module. This is the module that is exported from the user's\n   * code. This is marked as readonly to ensure that the module is not mutated\n   * because the module (when compiled) only provides getters.\n   */\n  public readonly userland: Readonly<U>\n\n  /**\n   * The definition of the route.\n   */\n  public readonly definition: Readonly<D>\n\n  /**\n   * The shared modules that are exposed and required for the route module.\n   */\n  public static readonly sharedModules: any\n\n  public isDev: boolean\n  public distDir: string\n  public isAppRouter?: boolean\n  public relativeProjectDir: string\n  public incrementCache?: IncrementalCache\n  public responseCache?: ResponseCache\n\n  constructor({\n    userland,\n    definition,\n    distDir,\n    relativeProjectDir,\n  }: RouteModuleOptions<D, U>) {\n    this.userland = userland\n    this.definition = definition\n    this.isDev = process.env.NODE_ENV === 'development'\n    this.distDir = distDir\n    this.relativeProjectDir = relativeProjectDir\n  }\n\n  public async instrumentationOnRequestError(\n    req: IncomingMessage | BaseNextRequest,\n    ...args: Parameters<InstrumentationOnRequestError>\n  ) {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      const { getEdgeInstrumentationModule } = await import('../web/globals')\n      const instrumentation = await getEdgeInstrumentationModule()\n\n      if (instrumentation) {\n        await instrumentation.onRequestError?.(...args)\n      }\n    } else {\n      const { join } = require('node:path') as typeof import('node:path')\n      const absoluteProjectDir = join(\n        process.cwd(),\n        getRequestMeta(req, 'relativeProjectDir') || this.relativeProjectDir\n      )\n\n      const { instrumentationOnRequestError } = await import(\n        '../lib/router-utils/instrumentation-globals.external.js'\n      )\n\n      return instrumentationOnRequestError(\n        absoluteProjectDir,\n        this.distDir,\n        ...args\n      )\n    }\n  }\n\n  private loadManifests(\n    srcPage: string,\n    projectDir?: string\n  ): {\n    buildId: string\n    buildManifest: BuildManifest\n    fallbackBuildManifest: BuildManifest\n    routesManifest: DeepReadonly<DevRoutesManifest>\n    nextFontManifest: DeepReadonly<NextFontManifest>\n    prerenderManifest: DeepReadonly<PrerenderManifest>\n    serverFilesManifest: RequiredServerFilesManifest\n    reactLoadableManifest: DeepReadonly<ReactLoadableManifest>\n    subresourceIntegrityManifest: any\n    clientReferenceManifest: any\n    serverActionsManifest: any\n    dynamicCssManifest: any\n    interceptionRoutePatterns: RegExp[]\n  } {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      const { getEdgePreviewProps } =\n        require('../web/get-edge-preview-props') as typeof import('../web/get-edge-preview-props')\n\n      const maybeJSONParse = (str?: string) =>\n        str ? JSON.parse(str) : undefined\n\n      return {\n        buildId: process.env.__NEXT_BUILD_ID || '',\n        buildManifest: self.__BUILD_MANIFEST as any,\n        fallbackBuildManifest: {} as any,\n        reactLoadableManifest: maybeJSONParse(self.__REACT_LOADABLE_MANIFEST),\n        nextFontManifest: maybeJSONParse(self.__NEXT_FONT_MANIFEST),\n        prerenderManifest: {\n          routes: {},\n          dynamicRoutes: {},\n          notFoundRoutes: [],\n          version: 4,\n          preview: getEdgePreviewProps(),\n        },\n        routesManifest: {\n          version: 4,\n          caseSensitive: Boolean(process.env.__NEXT_CASE_SENSITIVE_ROUTES),\n          basePath: process.env.__NEXT_BASE_PATH || '',\n          rewrites: (process.env.__NEXT_REWRITES as any) || {\n            beforeFiles: [],\n            afterFiles: [],\n            fallback: [],\n          },\n          redirects: [],\n          headers: [],\n          i18n:\n            (process.env.__NEXT_I18N_CONFIG as any as I18NConfig) || undefined,\n          skipMiddlewareUrlNormalize: Boolean(\n            process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n          ),\n        },\n        serverFilesManifest: {\n          config: (globalThis as any).nextConfig || {},\n        } as any,\n        clientReferenceManifest: self.__RSC_MANIFEST?.[srcPage],\n        serverActionsManifest: maybeJSONParse(self.__RSC_SERVER_MANIFEST),\n        subresourceIntegrityManifest: maybeJSONParse(\n          self.__SUBRESOURCE_INTEGRITY_MANIFEST\n        ),\n        dynamicCssManifest: maybeJSONParse(self.__DYNAMIC_CSS_MANIFEST),\n        interceptionRoutePatterns: (\n          maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? []\n        ).map((rewrite: any) => new RegExp(rewrite.regex)),\n      }\n    } else {\n      if (!projectDir) {\n        throw new Error('Invariant: projectDir is required for node runtime')\n      }\n      const { loadManifestFromRelativePath } =\n        require('../load-manifest.external') as typeof import('../load-manifest.external')\n      const normalizedPagePath = normalizePagePath(srcPage)\n\n      const [\n        routesManifest,\n        prerenderManifest,\n        buildManifest,\n        fallbackBuildManifest,\n        reactLoadableManifest,\n        nextFontManifest,\n        clientReferenceManifest,\n        serverActionsManifest,\n        subresourceIntegrityManifest,\n        serverFilesManifest,\n        buildId,\n        dynamicCssManifest,\n      ] = [\n        loadManifestFromRelativePath<DevRoutesManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: ROUTES_MANIFEST,\n          shouldCache: !this.isDev,\n        }),\n        loadManifestFromRelativePath<PrerenderManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: PRERENDER_MANIFEST,\n          shouldCache: !this.isDev,\n        }),\n        loadManifestFromRelativePath<BuildManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: BUILD_MANIFEST,\n          shouldCache: !this.isDev,\n        }),\n        srcPage === '/_error'\n          ? loadManifestFromRelativePath<BuildManifest>({\n              projectDir,\n              distDir: this.distDir,\n              manifest: `fallback-${BUILD_MANIFEST}`,\n              shouldCache: !this.isDev,\n              handleMissing: true,\n            })\n          : ({} as BuildManifest),\n        loadManifestFromRelativePath<ReactLoadableManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: process.env.TURBOPACK\n            ? `server/${this.isAppRouter ? 'app' : 'pages'}${normalizedPagePath}/${REACT_LOADABLE_MANIFEST}`\n            : REACT_LOADABLE_MANIFEST,\n          handleMissing: true,\n          shouldCache: !this.isDev,\n        }),\n        loadManifestFromRelativePath<NextFontManifest>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: `server/${NEXT_FONT_MANIFEST}.json`,\n          shouldCache: !this.isDev,\n        }),\n        this.isAppRouter && !isStaticMetadataRoute(srcPage)\n          ? loadManifestFromRelativePath({\n              distDir: this.distDir,\n              projectDir,\n              useEval: true,\n              handleMissing: true,\n              manifest: `server/app${srcPage.replace(/%5F/g, '_') + '_' + CLIENT_REFERENCE_MANIFEST}.js`,\n              shouldCache: !this.isDev,\n            })\n          : undefined,\n        this.isAppRouter\n          ? loadManifestFromRelativePath<any>({\n              distDir: this.distDir,\n              projectDir,\n              manifest: `server/${SERVER_REFERENCE_MANIFEST}.json`,\n              handleMissing: true,\n              shouldCache: !this.isDev,\n            })\n          : {},\n        loadManifestFromRelativePath<Record<string, string>>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: `server/${SUBRESOURCE_INTEGRITY_MANIFEST}.json`,\n          handleMissing: true,\n          shouldCache: !this.isDev,\n        }),\n        this.isDev\n          ? ({} as any)\n          : loadManifestFromRelativePath<RequiredServerFilesManifest>({\n              projectDir,\n              distDir: this.distDir,\n              manifest: SERVER_FILES_MANIFEST,\n            }),\n        this.isDev\n          ? 'development'\n          : loadManifestFromRelativePath<any>({\n              projectDir,\n              distDir: this.distDir,\n              manifest: BUILD_ID_FILE,\n              skipParse: true,\n            }),\n        loadManifestFromRelativePath<any>({\n          projectDir,\n          distDir: this.distDir,\n          manifest: DYNAMIC_CSS_MANIFEST,\n          handleMissing: true,\n        }),\n      ]\n\n      return {\n        buildId,\n        buildManifest,\n        fallbackBuildManifest,\n        routesManifest,\n        nextFontManifest,\n        prerenderManifest,\n        serverFilesManifest,\n        reactLoadableManifest,\n        clientReferenceManifest: (clientReferenceManifest as any)\n          ?.__RSC_MANIFEST?.[srcPage.replace(/%5F/g, '_')],\n        serverActionsManifest,\n        subresourceIntegrityManifest,\n        dynamicCssManifest,\n        interceptionRoutePatterns: routesManifest.rewrites.beforeFiles\n          .filter(isInterceptionRouteRewrite)\n          .map((rewrite) => new RegExp(rewrite.regex)),\n      }\n    }\n  }\n\n  public async loadCustomCacheHandlers(\n    req: IncomingMessage | BaseNextRequest,\n    nextConfig: NextConfigComplete\n  ) {\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      const { cacheHandlers } = nextConfig.experimental\n      if (!cacheHandlers) return\n\n      // If we've already initialized the cache handlers interface, don't do it\n      // again.\n      if (!initializeCacheHandlers()) return\n\n      for (const [kind, handler] of Object.entries(cacheHandlers)) {\n        if (!handler) continue\n\n        const { formatDynamicImportPath } =\n          require('../../lib/format-dynamic-import-path') as typeof import('../../lib/format-dynamic-import-path')\n\n        const { join } = require('node:path') as typeof import('node:path')\n        const absoluteProjectDir = join(\n          process.cwd(),\n          getRequestMeta(req, 'relativeProjectDir') || this.relativeProjectDir\n        )\n\n        setCacheHandler(\n          kind,\n          interopDefault(\n            await dynamicImportEsmDefault(\n              formatDynamicImportPath(\n                `${absoluteProjectDir}/${this.distDir}`,\n                handler\n              )\n            )\n          )\n        )\n      }\n    }\n  }\n\n  public async getIncrementalCache(\n    req: IncomingMessage | BaseNextRequest,\n    nextConfig: NextConfigComplete,\n    prerenderManifest: DeepReadonly<PrerenderManifest>\n  ): Promise<IncrementalCache> {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      return (globalThis as any).__incrementalCache\n    } else {\n      let CacheHandler: any\n      const { cacheHandler } = nextConfig\n\n      if (cacheHandler) {\n        const { formatDynamicImportPath } =\n          require('../../lib/format-dynamic-import-path') as typeof import('../../lib/format-dynamic-import-path')\n\n        CacheHandler = interopDefault(\n          await dynamicImportEsmDefault(\n            formatDynamicImportPath(this.distDir, cacheHandler)\n          )\n        )\n      }\n      const { join } = require('node:path') as typeof import('node:path')\n      const projectDir = join(\n        process.cwd(),\n        getRequestMeta(req, 'relativeProjectDir') || this.relativeProjectDir\n      )\n\n      await this.loadCustomCacheHandlers(req, nextConfig)\n\n      // incremental-cache is request specific\n      // although can have shared caches in module scope\n      // per-cache handler\n      return new IncrementalCache({\n        fs: (\n          require('../lib/node-fs-methods') as typeof import('../lib/node-fs-methods')\n        ).nodeFs,\n        dev: this.isDev,\n        requestHeaders: req.headers,\n        allowedRevalidateHeaderKeys:\n          nextConfig.experimental.allowedRevalidateHeaderKeys,\n        minimalMode: getRequestMeta(req, 'minimalMode'),\n        serverDistDir: `${projectDir}/${this.distDir}/server`,\n        fetchCacheKeyPrefix: nextConfig.experimental.fetchCacheKeyPrefix,\n        maxMemoryCacheSize: nextConfig.cacheMaxMemorySize,\n        flushToDisk: nextConfig.experimental.isrFlushToDisk,\n        getPrerenderManifest: () => prerenderManifest,\n        CurCacheHandler: CacheHandler,\n      })\n    }\n  }\n\n  public async onRequestError(\n    req: IncomingMessage | BaseNextRequest,\n    err: unknown,\n    errorContext: RequestErrorContext,\n    routerServerContext?: RouterServerContext[string]\n  ) {\n    if (routerServerContext?.logErrorWithOriginalStack) {\n      routerServerContext.logErrorWithOriginalStack(err, 'app-dir')\n    } else {\n      console.error(err)\n    }\n    await this.instrumentationOnRequestError(\n      req,\n      err,\n      {\n        path: req.url || '/',\n        headers: req.headers,\n        method: req.method || 'GET',\n      },\n      errorContext\n    )\n  }\n\n  public async prepare(\n    req: IncomingMessage | BaseNextRequest,\n    res: ServerResponse | null,\n    {\n      srcPage,\n      multiZoneDraftMode,\n    }: {\n      srcPage: string\n      multiZoneDraftMode?: boolean\n    }\n  ): Promise<\n    | {\n        buildId: string\n        locale?: string\n        locales?: readonly string[]\n        defaultLocale?: string\n        query: ParsedUrlQuery\n        originalQuery: ParsedUrlQuery\n        originalPathname: string\n        params?: ParsedUrlQuery\n        parsedUrl: UrlWithParsedQuery\n        previewData: PreviewData\n        pageIsDynamic: boolean\n        isDraftMode: boolean\n        resolvedPathname: string\n        isNextDataRequest: boolean\n        buildManifest: DeepReadonly<BuildManifest>\n        fallbackBuildManifest: DeepReadonly<BuildManifest>\n        nextFontManifest: DeepReadonly<NextFontManifest>\n        serverFilesManifest: DeepReadonly<RequiredServerFilesManifest>\n        reactLoadableManifest: DeepReadonly<ReactLoadableManifest>\n        routesManifest: DeepReadonly<DevRoutesManifest>\n        prerenderManifest: DeepReadonly<PrerenderManifest>\n        // we can't pull in the client reference type or it causes issues with\n        // our pre-compiled types\n        clientReferenceManifest?: any\n        serverActionsManifest?: any\n        dynamicCssManifest?: any\n        subresourceIntegrityManifest?: DeepReadonly<Record<string, string>>\n        isOnDemandRevalidate: boolean\n        revalidateOnlyGenerated: boolean\n        nextConfig: NextConfigComplete\n        routerServerContext?: RouterServerContext[string]\n        interceptionRoutePatterns?: any\n      }\n    | undefined\n  > {\n    let absoluteProjectDir: string | undefined\n\n    // edge runtime handles loading instrumentation at the edge adapter level\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      const { join, relative } =\n        require('node:path') as typeof import('node:path')\n\n      absoluteProjectDir = join(\n        process.cwd(),\n        getRequestMeta(req, 'relativeProjectDir') || this.relativeProjectDir\n      )\n\n      const absoluteDistDir = getRequestMeta(req, 'distDir')\n\n      if (absoluteDistDir) {\n        this.distDir = relative(absoluteProjectDir, absoluteDistDir)\n      }\n      const { ensureInstrumentationRegistered } = await import(\n        '../lib/router-utils/instrumentation-globals.external.js'\n      )\n      // ensure instrumentation is registered and pass\n      // onRequestError below\n      ensureInstrumentationRegistered(absoluteProjectDir, this.distDir)\n    }\n    const manifests = await this.loadManifests(srcPage, absoluteProjectDir)\n    const { routesManifest, prerenderManifest, serverFilesManifest } = manifests\n\n    const { basePath, i18n, rewrites } = routesManifest\n\n    if (basePath) {\n      req.url = removePathPrefix(req.url || '/', basePath)\n    }\n\n    const parsedUrl = parseReqUrl(req.url || '/')\n    // if we couldn't parse the URL we can't continue\n    if (!parsedUrl) {\n      return\n    }\n    let isNextDataRequest = false\n\n    if (pathHasPrefix(parsedUrl.pathname || '/', '/_next/data')) {\n      isNextDataRequest = true\n      parsedUrl.pathname = normalizeDataPath(parsedUrl.pathname || '/')\n    }\n    let originalPathname = parsedUrl.pathname || '/'\n    const originalQuery = { ...parsedUrl.query }\n    const pageIsDynamic = isDynamicRoute(srcPage)\n\n    let localeResult: PathLocale | undefined\n    let detectedLocale: string | undefined\n\n    if (i18n) {\n      localeResult = normalizeLocalePath(\n        parsedUrl.pathname || '/',\n        i18n.locales\n      )\n\n      if (localeResult.detectedLocale) {\n        req.url = `${localeResult.pathname}${parsedUrl.search}`\n        originalPathname = localeResult.pathname\n\n        if (!detectedLocale) {\n          detectedLocale = localeResult.detectedLocale\n        }\n      }\n    }\n\n    const serverUtils = getServerUtils({\n      page: srcPage,\n      i18n,\n      basePath,\n      rewrites,\n      pageIsDynamic,\n      trailingSlash: process.env.__NEXT_TRAILING_SLASH as any as boolean,\n      caseSensitive: Boolean(routesManifest.caseSensitive),\n    })\n\n    const domainLocale = detectDomainLocale(\n      i18n?.domains,\n      getHostname(parsedUrl, req.headers),\n      detectedLocale\n    )\n    addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n    const defaultLocale = domainLocale?.defaultLocale || i18n?.defaultLocale\n\n    // Ensure parsedUrl.pathname includes locale before processing\n    // rewrites or they won't match correctly.\n    if (defaultLocale && !detectedLocale) {\n      parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname === '/' ? '' : parsedUrl.pathname}`\n    }\n    const locale =\n      getRequestMeta(req, 'locale') || detectedLocale || defaultLocale\n\n    const rewriteParamKeys = Object.keys(\n      serverUtils.handleRewrites(req, parsedUrl)\n    )\n\n    // after processing rewrites we want to remove locale\n    // from parsedUrl pathname\n    if (i18n) {\n      parsedUrl.pathname = normalizeLocalePath(\n        parsedUrl.pathname || '/',\n        i18n.locales\n      ).pathname\n    }\n\n    let params: Record<string, undefined | string | string[]> | undefined =\n      getRequestMeta(req, 'params')\n\n    // attempt parsing from pathname\n    if (!params && serverUtils.dynamicRouteMatcher) {\n      const paramsMatch = serverUtils.dynamicRouteMatcher(\n        normalizeDataPath(localeResult?.pathname || parsedUrl.pathname || '/')\n      )\n      const paramsResult = serverUtils.normalizeDynamicRouteParams(\n        paramsMatch || {},\n        true\n      )\n\n      if (paramsResult.hasValidParams) {\n        params = paramsResult.params\n      }\n    }\n\n    // Local \"next start\" expects the routing parsed query values\n    // to not be present in the URL although when deployed proxies\n    // will add query values from resolving the routes to pass to function.\n\n    // TODO: do we want to change expectations for \"next start\"\n    // to include these query values in the URL which affects asPath\n    // but would match deployed behavior, e.g. a rewrite from middleware\n    // that adds a query param would be in asPath as query but locally\n    // it won't be in the asPath but still available in the query object\n    const query = getRequestMeta(req, 'query') || {\n      ...parsedUrl.query,\n    }\n\n    const routeParamKeys = new Set<string>()\n    const combinedParamKeys = []\n\n    // we don't include rewriteParamKeys in the combinedParamKeys\n    // for app router since the searchParams is populated from the\n    // URL so we don't want to strip the rewrite params from the URL\n    // so that searchParams can include them\n    if (!this.isAppRouter) {\n      for (const key of [\n        ...rewriteParamKeys,\n        ...Object.keys(serverUtils.defaultRouteMatches || {}),\n      ]) {\n        // We only want to filter rewrite param keys from the URL\n        // if they are matches from the URL e.g. the key/value matches\n        // before and after applying the rewrites /:path for /hello and\n        // { path: 'hello' } but not for { path: 'another' } and /hello\n        // TODO: we should prefix rewrite param keys the same as we do\n        // for dynamic routes so we can identify them properly\n        const originalValue = Array.isArray(originalQuery[key])\n          ? originalQuery[key].join('')\n          : originalQuery[key]\n\n        const queryValue = Array.isArray(query[key])\n          ? query[key].join('')\n          : query[key]\n\n        if (!(key in originalQuery) || originalValue === queryValue) {\n          combinedParamKeys.push(key)\n        }\n      }\n    }\n\n    serverUtils.normalizeCdnUrl(req, combinedParamKeys)\n    serverUtils.normalizeQueryParams(query, routeParamKeys)\n    serverUtils.filterInternalQuery(originalQuery, combinedParamKeys)\n\n    if (pageIsDynamic) {\n      const queryResult = serverUtils.normalizeDynamicRouteParams(query, true)\n\n      const paramsResult = serverUtils.normalizeDynamicRouteParams(\n        params || {},\n        true\n      )\n      const paramsToInterpolate: ParsedUrlQuery =\n        paramsResult.hasValidParams && params\n          ? params\n          : queryResult.hasValidParams\n            ? query\n            : {}\n\n      req.url = serverUtils.interpolateDynamicPath(\n        req.url || '/',\n        paramsToInterpolate\n      )\n      parsedUrl.pathname = serverUtils.interpolateDynamicPath(\n        parsedUrl.pathname || '/',\n        paramsToInterpolate\n      )\n      originalPathname = serverUtils.interpolateDynamicPath(\n        originalPathname,\n        paramsToInterpolate\n      )\n\n      // try pulling from query if valid\n      if (!params) {\n        if (queryResult.hasValidParams) {\n          params = Object.assign({}, queryResult.params)\n\n          // If we pulled from query remove it so it's\n          // only in params\n          for (const key in serverUtils.defaultRouteMatches) {\n            delete query[key]\n          }\n        } else {\n          // use final params from URL matching\n          const paramsMatch = serverUtils.dynamicRouteMatcher?.(\n            normalizeDataPath(\n              localeResult?.pathname || parsedUrl.pathname || '/'\n            )\n          )\n          // we don't normalize these as they are allowed to be\n          // the literal slug matches here e.g. /blog/[slug]\n          // actually being requested\n          if (paramsMatch) {\n            params = Object.assign({}, paramsMatch)\n          }\n        }\n      }\n    }\n\n    // Remove any normalized params from the query if they\n    // weren't present as non-prefixed query key e.g.\n    // ?search=1&nxtPsearch=hello we don't delete search\n    for (const key of routeParamKeys) {\n      if (!(key in originalQuery)) {\n        delete query[key]\n      }\n    }\n\n    const { isOnDemandRevalidate, revalidateOnlyGenerated } =\n      checkIsOnDemandRevalidate(req, prerenderManifest.preview)\n\n    let isDraftMode = false\n    let previewData: PreviewData\n\n    // preview data relies on non-edge utils\n    if (process.env.NEXT_RUNTIME !== 'edge' && res) {\n      const { tryGetPreviewData } =\n        require('../api-utils/node/try-get-preview-data') as typeof import('../api-utils/node/try-get-preview-data')\n\n      previewData = tryGetPreviewData(\n        req,\n        res,\n        prerenderManifest.preview,\n        Boolean(multiZoneDraftMode)\n      )\n      isDraftMode = previewData !== false\n    }\n\n    const relativeProjectDir =\n      getRequestMeta(req, 'relativeProjectDir') || this.relativeProjectDir\n\n    const routerServerContext =\n      routerServerGlobal[RouterServerContextSymbol]?.[relativeProjectDir]\n    const nextConfig =\n      routerServerContext?.nextConfig || serverFilesManifest.config\n\n    const normalizedSrcPage = normalizeAppPath(srcPage)\n    let resolvedPathname =\n      getRequestMeta(req, 'rewroteURL') || normalizedSrcPage\n\n    if (isDynamicRoute(resolvedPathname) && params) {\n      resolvedPathname = serverUtils.interpolateDynamicPath(\n        resolvedPathname,\n        params\n      )\n    }\n\n    if (resolvedPathname === '/index') {\n      resolvedPathname = '/'\n    }\n    try {\n      resolvedPathname = decodePathParams(resolvedPathname)\n    } catch (_) {}\n\n    resolvedPathname = removeTrailingSlash(resolvedPathname)\n\n    return {\n      query,\n      originalQuery,\n      originalPathname,\n      params,\n      parsedUrl,\n      locale,\n      isNextDataRequest,\n      locales: i18n?.locales,\n      defaultLocale,\n      isDraftMode,\n      previewData,\n      pageIsDynamic,\n      resolvedPathname,\n      isOnDemandRevalidate,\n      revalidateOnlyGenerated,\n      ...manifests,\n      serverActionsManifest: manifests.serverActionsManifest,\n      clientReferenceManifest: manifests.clientReferenceManifest,\n      nextConfig,\n      routerServerContext,\n    }\n  }\n\n  public getResponseCache(req: IncomingMessage | BaseNextRequest) {\n    if (!this.responseCache) {\n      const minimalMode = getRequestMeta(req, 'minimalMode') ?? false\n      this.responseCache = new ResponseCache(minimalMode)\n    }\n    return this.responseCache\n  }\n\n  public async handleResponse({\n    req,\n    nextConfig,\n    cacheKey,\n    routeKind,\n    isFallback,\n    prerenderManifest,\n    isRoutePPREnabled,\n    isOnDemandRevalidate,\n    revalidateOnlyGenerated,\n    responseGenerator,\n    waitUntil,\n  }: {\n    req: IncomingMessage | BaseNextRequest\n    nextConfig: NextConfigComplete\n    cacheKey: string | null\n    routeKind: RouteKind\n    isFallback?: boolean\n    prerenderManifest: DeepReadonly<PrerenderManifest>\n    isRoutePPREnabled?: boolean\n    isOnDemandRevalidate?: boolean\n    revalidateOnlyGenerated?: boolean\n    responseGenerator: ResponseGenerator\n    waitUntil?: (prom: Promise<any>) => void\n  }) {\n    const responseCache = this.getResponseCache(req)\n    const cacheEntry = await responseCache.get(cacheKey, responseGenerator, {\n      routeKind,\n      isFallback,\n      isRoutePPREnabled,\n      isOnDemandRevalidate,\n      isPrefetch: req.headers.purpose === 'prefetch',\n      incrementalCache: await this.getIncrementalCache(\n        req,\n        nextConfig,\n        prerenderManifest\n      ),\n      waitUntil,\n    })\n\n    if (!cacheEntry) {\n      if (\n        cacheKey &&\n        // revalidate only generated can bail even if cacheKey is provided\n        !(isOnDemandRevalidate && revalidateOnlyGenerated)\n      ) {\n        // A cache entry might not be generated if a response is written\n        // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n        // have a cache key. If we do have a cache key but we don't end up\n        // with a cache entry, then either Next.js or the application has a\n        // bug that needs fixing.\n        throw new Error('invariant: cache entry required but not generated')\n      }\n    }\n    return cacheEntry\n  }\n}\n"], "names": ["BUILD_ID_FILE", "BUILD_MANIFEST", "CLIENT_REFERENCE_MANIFEST", "DYNAMIC_CSS_MANIFEST", "NEXT_FONT_MANIFEST", "PRERENDER_MANIFEST", "REACT_LOADABLE_MANIFEST", "ROUTES_MANIFEST", "SERVER_FILES_MANIFEST", "SERVER_REFERENCE_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "parseReqUrl", "normalizeLocalePath", "isDynamicRoute", "removePathPrefix", "getServerUtils", "detectDomainLocale", "getHostname", "checkIsOnDemandRevalidate", "normalizeDataPath", "pathHasPrefix", "addRequestMeta", "getRequestMeta", "normalizePagePath", "isStaticMetadataRoute", "IncrementalCache", "initializeCacheHandlers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interopDefault", "ResponseCache", "normalizeAppPath", "RouterServerContextSymbol", "routerServerGlobal", "decodePathParams", "removeTrailingSlash", "isInterceptionRouteRewrite", "dynamicImportEsmDefault", "id", "then", "mod", "default", "RouteModule", "constructor", "userland", "definition", "distDir", "relativeProjectDir", "isDev", "process", "env", "NODE_ENV", "instrumentationOnRequestError", "req", "args", "NEXT_RUNTIME", "getEdgeInstrumentationModule", "instrumentation", "onRequestError", "join", "require", "absoluteProjectDir", "cwd", "loadManifests", "srcPage", "projectDir", "self", "getEdgePreviewProps", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildId", "__NEXT_BUILD_ID", "buildManifest", "__BUILD_MANIFEST", "fallbackBuildManifest", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "prerenderManifest", "routes", "dynamicRoutes", "notFoundRoutes", "version", "preview", "routesManifest", "caseSensitive", "Boolean", "__NEXT_CASE_SENSITIVE_ROUTES", "basePath", "__NEXT_BASE_PATH", "rewrites", "__NEXT_REWRITES", "beforeFiles", "afterFiles", "fallback", "redirects", "headers", "i18n", "__NEXT_I18N_CONFIG", "skipMiddlewareUrlNormalize", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "serverFilesManifest", "config", "globalThis", "nextConfig", "clientReferenceManifest", "__RSC_MANIFEST", "serverActionsManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "__SUBRESOURCE_INTEGRITY_MANIFEST", "dynamicCssManifest", "__DYNAMIC_CSS_MANIFEST", "interceptionRoutePatterns", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "map", "rewrite", "RegExp", "regex", "Error", "loadManifestFromRelativePath", "normalizedPagePath", "manifest", "shouldCache", "handleMissing", "TURBOPACK", "isAppRouter", "useEval", "replace", "<PERSON><PERSON><PERSON><PERSON>", "filter", "loadCustomCacheHandlers", "cacheHandlers", "experimental", "kind", "handler", "Object", "entries", "formatDynamicImportPath", "getIncrementalCache", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cache<PERSON><PERSON><PERSON>", "fs", "nodeFs", "dev", "requestHeaders", "allowedRevalidateHeaderKeys", "minimalMode", "serverDistDir", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "cacheMaxMemorySize", "flushToDisk", "isrFlushToDisk", "getPrerenderManifest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "errorContext", "routerServerContext", "logErrorWithOriginalStack", "console", "error", "path", "url", "method", "prepare", "res", "multiZoneDraftMode", "relative", "absoluteDistDir", "ensureInstrumentationRegistered", "manifests", "parsedUrl", "isNextDataRequest", "pathname", "originalPathname", "originalQuery", "query", "pageIsDynamic", "localeResult", "detectedLocale", "locales", "search", "serverUtils", "page", "trailingSlash", "__NEXT_TRAILING_SLASH", "domainLocale", "domains", "defaultLocale", "locale", "rewriteParamKeys", "keys", "handleRewrites", "params", "dynamicRouteMatcher", "paramsMatch", "paramsResult", "normalizeDynamicRouteParams", "hasValidParams", "routeParamKeys", "Set", "combinedParamKeys", "key", "defaultRouteMatches", "originalValue", "Array", "isArray", "queryValue", "push", "normalizeCdnUrl", "normalizeQueryParams", "filterInternalQuery", "query<PERSON><PERSON>ult", "paramsToInterpolate", "interpolateDynamicPath", "assign", "isOnDemandRevalidate", "revalidateOnlyGenerated", "isDraftMode", "previewData", "tryGetPreviewData", "normalizedSrcPage", "resolvedPathname", "_", "getResponseCache", "responseCache", "handleResponse", "cache<PERSON>ey", "routeKind", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "responseGenerator", "waitUntil", "cacheEntry", "get", "isPrefetch", "purpose", "incrementalCache"], "mappings": "AAcA,SACEA,aAAa,EACbC,cAAc,EACdC,yBAAyB,EACzBC,oBAAoB,EACpBC,kBAAkB,EAClBC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,qBAAqB,EACrBC,yBAAyB,EACzBC,8BAA8B,QACzB,6BAA4B;AACnC,SAASC,WAAW,QAAQ,gBAAe;AAC3C,SACEC,mBAAmB,QAEd,8CAA6C;AACpD,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,cAAc,QAAQ,kBAAiB;AAChD,SAASC,kBAAkB,QAAQ,6CAA4C;AAC/E,SAASC,WAAW,QAAQ,gCAA+B;AAC3D,SAASC,yBAAyB,QAAQ,eAAc;AAKxD,SAASC,iBAAiB,QAAQ,iDAAgD;AAClF,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,cAAc,EAAEC,cAAc,QAAQ,kBAAiB;AAChE,SAASC,iBAAiB,QAAQ,iDAAgD;AAClF,SAASC,qBAAqB,QAAQ,uCAAsC;AAC5E,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,uBAAuB,EAAEC,eAAe,QAAQ,wBAAuB;AAChF,SAASC,cAAc,QAAQ,gCAA+B;AAI9D,OAAOC,mBAA+C,oBAAmB;AACzE,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SACEC,yBAAyB,EACzBC,kBAAkB,QAEb,4CAA2C;AAClD,SAASC,gBAAgB,QAAQ,yCAAwC;AACzE,SAASC,mBAAmB,QAAQ,sDAAqD;AACzF,SAASC,0BAA0B,QAAQ,kDAAiD;AA4B5F,MAAMC,0BAA0B,CAACC,KAC/B,MAAM,CAAC,uBAAuB,GAAG,yBAAyB,GAAGA,IAAIC,IAAI,CACnE,CAACC,MAAQA,IAAIC,OAAO,IAAID;AAG5B;;;CAGC,GACD,OAAO,MAAeE;IA4BpBC,YAAY,EACVC,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,kBAAkB,EACO,CAAE;QAC3B,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACC,UAAU,GAAGA;QAClB,IAAI,CAACG,KAAK,GAAGC,QAAQC,GAAG,CAACC,QAAQ,KAAK;QACtC,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACC,kBAAkB,GAAGA;IAC5B;IAEA,MAAaK,8BACXC,GAAsC,EACtC,GAAGC,IAA+C,EAClD;QACA,IAAIL,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;YACvC,MAAM,EAAEC,4BAA4B,EAAE,GAAG,MAAM,MAAM,CAAC;YACtD,MAAMC,kBAAkB,MAAMD;YAE9B,IAAIC,iBAAiB;gBACnB,OAAMA,gBAAgBC,cAAc,oBAA9BD,gBAAgBC,cAAc,MAA9BD,oBAAoCH;YAC5C;QACF,OAAO;YACL,MAAM,EAAEK,IAAI,EAAE,GAAGC,QAAQ;YACzB,MAAMC,qBAAqBF,KACzBV,QAAQa,GAAG,IACXvC,eAAe8B,KAAK,yBAAyB,IAAI,CAACN,kBAAkB;YAGtE,MAAM,EAAEK,6BAA6B,EAAE,GAAG,MAAM,MAAM,CACpD;YAGF,OAAOA,8BACLS,oBACA,IAAI,CAACf,OAAO,KACTQ;QAEP;IACF;IAEQS,cACNC,OAAe,EACfC,UAAmB,EAenB;QACA,IAAIhB,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;gBAwCZW;YAvC3B,MAAM,EAAEC,mBAAmB,EAAE,GAC3BP,QAAQ;YAEV,MAAMQ,iBAAiB,CAACC,MACtBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;YAE1B,OAAO;gBACLC,SAASxB,QAAQC,GAAG,CAACwB,eAAe,IAAI;gBACxCC,eAAeT,KAAKU,gBAAgB;gBACpCC,uBAAuB,CAAC;gBACxBC,uBAAuBV,eAAeF,KAAKa,yBAAyB;gBACpEC,kBAAkBZ,eAAeF,KAAKe,oBAAoB;gBAC1DC,mBAAmB;oBACjBC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAAS;oBACTC,SAASpB;gBACX;gBACAqB,gBAAgB;oBACdF,SAAS;oBACTG,eAAeC,QAAQzC,QAAQC,GAAG,CAACyC,4BAA4B;oBAC/DC,UAAU3C,QAAQC,GAAG,CAAC2C,gBAAgB,IAAI;oBAC1CC,UAAU,AAAC7C,QAAQC,GAAG,CAAC6C,eAAe,IAAY;wBAChDC,aAAa,EAAE;wBACfC,YAAY,EAAE;wBACdC,UAAU,EAAE;oBACd;oBACAC,WAAW,EAAE;oBACbC,SAAS,EAAE;oBACXC,MACE,AAACpD,QAAQC,GAAG,CAACoD,kBAAkB,IAA0B9B;oBAC3D+B,4BAA4Bb,QAC1BzC,QAAQC,GAAG,CAACsD,kCAAkC;gBAElD;gBACAC,qBAAqB;oBACnBC,QAAQ,AAACC,WAAmBC,UAAU,IAAI,CAAC;gBAC7C;gBACAC,uBAAuB,GAAE3C,uBAAAA,KAAK4C,cAAc,qBAAnB5C,oBAAqB,CAACF,QAAQ;gBACvD+C,uBAAuB3C,eAAeF,KAAK8C,qBAAqB;gBAChEC,8BAA8B7C,eAC5BF,KAAKgD,gCAAgC;gBAEvCC,oBAAoB/C,eAAeF,KAAKkD,sBAAsB;gBAC9DC,2BAA2B,AACzBjD,CAAAA,eAAeF,KAAKoD,qCAAqC,KAAK,EAAE,AAAD,EAC/DC,GAAG,CAAC,CAACC,UAAiB,IAAIC,OAAOD,QAAQE,KAAK;YAClD;QACF,OAAO;gBA0HsB;YAzH3B,IAAI,CAACzD,YAAY;gBACf,MAAM,qBAA+D,CAA/D,IAAI0D,MAAM,uDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8D;YACtE;YACA,MAAM,EAAEC,4BAA4B,EAAE,GACpChE,QAAQ;YACV,MAAMiE,qBAAqBrG,kBAAkBwC;YAE7C,MAAM,CACJwB,gBACAN,mBACAP,eACAE,uBACAC,uBACAE,kBACA6B,yBACAE,uBACAE,8BACAR,qBACAhC,SACA0C,mBACD,GAAG;gBACFS,6BAAgD;oBAC9C3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAUtH;oBACVuH,aAAa,CAAC,IAAI,CAAC/E,KAAK;gBAC1B;gBACA4E,6BAAgD;oBAC9C3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAUxH;oBACVyH,aAAa,CAAC,IAAI,CAAC/E,KAAK;gBAC1B;gBACA4E,6BAA4C;oBAC1C3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAU5H;oBACV6H,aAAa,CAAC,IAAI,CAAC/E,KAAK;gBAC1B;gBACAgB,YAAY,YACR4D,6BAA4C;oBAC1C3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAU,CAAC,SAAS,EAAE5H,gBAAgB;oBACtC6H,aAAa,CAAC,IAAI,CAAC/E,KAAK;oBACxBgF,eAAe;gBACjB,KACC,CAAC;gBACNJ,6BAAoD;oBAClD3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAU7E,QAAQC,GAAG,CAAC+E,SAAS,GAC3B,CAAC,OAAO,EAAE,IAAI,CAACC,WAAW,GAAG,QAAQ,UAAUL,mBAAmB,CAAC,EAAEtH,yBAAyB,GAC9FA;oBACJyH,eAAe;oBACfD,aAAa,CAAC,IAAI,CAAC/E,KAAK;gBAC1B;gBACA4E,6BAA+C;oBAC7C3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAU,CAAC,OAAO,EAAEzH,mBAAmB,KAAK,CAAC;oBAC7C0H,aAAa,CAAC,IAAI,CAAC/E,KAAK;gBAC1B;gBACA,IAAI,CAACkF,WAAW,IAAI,CAACzG,sBAAsBuC,WACvC4D,6BAA6B;oBAC3B9E,SAAS,IAAI,CAACA,OAAO;oBACrBmB;oBACAkE,SAAS;oBACTH,eAAe;oBACfF,UAAU,CAAC,UAAU,EAAE9D,QAAQoE,OAAO,CAAC,QAAQ,OAAO,MAAMjI,0BAA0B,GAAG,CAAC;oBAC1F4H,aAAa,CAAC,IAAI,CAAC/E,KAAK;gBAC1B,KACAwB;gBACJ,IAAI,CAAC0D,WAAW,GACZN,6BAAkC;oBAChC9E,SAAS,IAAI,CAACA,OAAO;oBACrBmB;oBACA6D,UAAU,CAAC,OAAO,EAAEpH,0BAA0B,KAAK,CAAC;oBACpDsH,eAAe;oBACfD,aAAa,CAAC,IAAI,CAAC/E,KAAK;gBAC1B,KACA,CAAC;gBACL4E,6BAAqD;oBACnD3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAU,CAAC,OAAO,EAAEnH,+BAA+B,KAAK,CAAC;oBACzDqH,eAAe;oBACfD,aAAa,CAAC,IAAI,CAAC/E,KAAK;gBAC1B;gBACA,IAAI,CAACA,KAAK,GACL,CAAC,IACF4E,6BAA0D;oBACxD3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAUrH;gBACZ;gBACJ,IAAI,CAACuC,KAAK,GACN,gBACA4E,6BAAkC;oBAChC3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAU7H;oBACVoI,WAAW;gBACb;gBACJT,6BAAkC;oBAChC3D;oBACAnB,SAAS,IAAI,CAACA,OAAO;oBACrBgF,UAAU1H;oBACV4H,eAAe;gBACjB;aACD;YAED,OAAO;gBACLvD;gBACAE;gBACAE;gBACAW;gBACAR;gBACAE;gBACAuB;gBACA3B;gBACA+B,uBAAuB,EAAGA,4CAAD,0CAAA,AAACA,wBACtBC,cAAc,qBADO,uCACL,CAAC9C,QAAQoE,OAAO,CAAC,QAAQ,KAAK;gBAClDrB;gBACAE;gBACAE;gBACAE,2BAA2B7B,eAAeM,QAAQ,CAACE,WAAW,CAC3DsC,MAAM,CAAClG,4BACPmF,GAAG,CAAC,CAACC,UAAY,IAAIC,OAAOD,QAAQE,KAAK;YAC9C;QACF;IACF;IAEA,MAAaa,wBACXlF,GAAsC,EACtCuD,UAA8B,EAC9B;QACA,IAAI3D,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;YACvC,MAAM,EAAEiF,aAAa,EAAE,GAAG5B,WAAW6B,YAAY;YACjD,IAAI,CAACD,eAAe;YAEpB,yEAAyE;YACzE,SAAS;YACT,IAAI,CAAC7G,2BAA2B;YAEhC,KAAK,MAAM,CAAC+G,MAAMC,QAAQ,IAAIC,OAAOC,OAAO,CAACL,eAAgB;gBAC3D,IAAI,CAACG,SAAS;gBAEd,MAAM,EAAEG,uBAAuB,EAAE,GAC/BlF,QAAQ;gBAEV,MAAM,EAAED,IAAI,EAAE,GAAGC,QAAQ;gBACzB,MAAMC,qBAAqBF,KACzBV,QAAQa,GAAG,IACXvC,eAAe8B,KAAK,yBAAyB,IAAI,CAACN,kBAAkB;gBAGtEnB,gBACE8G,MACA7G,eACE,MAAMQ,wBACJyG,wBACE,GAAGjF,mBAAmB,CAAC,EAAE,IAAI,CAACf,OAAO,EAAE,EACvC6F;YAKV;QACF;IACF;IAEA,MAAaI,oBACX1F,GAAsC,EACtCuD,UAA8B,EAC9B1B,iBAAkD,EACvB;QAC3B,IAAIjC,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;YACvC,OAAO,AAACoD,WAAmBqC,kBAAkB;QAC/C,OAAO;YACL,IAAIC;YACJ,MAAM,EAAEC,YAAY,EAAE,GAAGtC;YAEzB,IAAIsC,cAAc;gBAChB,MAAM,EAAEJ,uBAAuB,EAAE,GAC/BlF,QAAQ;gBAEVqF,eAAepH,eACb,MAAMQ,wBACJyG,wBAAwB,IAAI,CAAChG,OAAO,EAAEoG;YAG5C;YACA,MAAM,EAAEvF,IAAI,EAAE,GAAGC,QAAQ;YACzB,MAAMK,aAAaN,KACjBV,QAAQa,GAAG,IACXvC,eAAe8B,KAAK,yBAAyB,IAAI,CAACN,kBAAkB;YAGtE,MAAM,IAAI,CAACwF,uBAAuB,CAAClF,KAAKuD;YAExC,wCAAwC;YACxC,kDAAkD;YAClD,oBAAoB;YACpB,OAAO,IAAIlF,iBAAiB;gBAC1ByH,IAAI,AACFvF,QAAQ,0BACRwF,MAAM;gBACRC,KAAK,IAAI,CAACrG,KAAK;gBACfsG,gBAAgBjG,IAAI+C,OAAO;gBAC3BmD,6BACE3C,WAAW6B,YAAY,CAACc,2BAA2B;gBACrDC,aAAajI,eAAe8B,KAAK;gBACjCoG,eAAe,GAAGxF,WAAW,CAAC,EAAE,IAAI,CAACnB,OAAO,CAAC,OAAO,CAAC;gBACrD4G,qBAAqB9C,WAAW6B,YAAY,CAACiB,mBAAmB;gBAChEC,oBAAoB/C,WAAWgD,kBAAkB;gBACjDC,aAAajD,WAAW6B,YAAY,CAACqB,cAAc;gBACnDC,sBAAsB,IAAM7E;gBAC5B8E,iBAAiBf;YACnB;QACF;IACF;IAEA,MAAavF,eACXL,GAAsC,EACtC4G,GAAY,EACZC,YAAiC,EACjCC,mBAAiD,EACjD;QACA,IAAIA,uCAAAA,oBAAqBC,yBAAyB,EAAE;YAClDD,oBAAoBC,yBAAyB,CAACH,KAAK;QACrD,OAAO;YACLI,QAAQC,KAAK,CAACL;QAChB;QACA,MAAM,IAAI,CAAC7G,6BAA6B,CACtCC,KACA4G,KACA;YACEM,MAAMlH,IAAImH,GAAG,IAAI;YACjBpE,SAAS/C,IAAI+C,OAAO;YACpBqE,QAAQpH,IAAIoH,MAAM,IAAI;QACxB,GACAP;IAEJ;IAEA,MAAaQ,QACXrH,GAAsC,EACtCsH,GAA0B,EAC1B,EACE3G,OAAO,EACP4G,kBAAkB,EAInB,EAqCD;YAsQE3I;QArQF,IAAI4B;QAEJ,yEAAyE;QACzE,IAAIZ,QAAQC,GAAG,CAACK,YAAY,KAAK,QAAQ;YACvC,MAAM,EAAEI,IAAI,EAAEkH,QAAQ,EAAE,GACtBjH,QAAQ;YAEVC,qBAAqBF,KACnBV,QAAQa,GAAG,IACXvC,eAAe8B,KAAK,yBAAyB,IAAI,CAACN,kBAAkB;YAGtE,MAAM+H,kBAAkBvJ,eAAe8B,KAAK;YAE5C,IAAIyH,iBAAiB;gBACnB,IAAI,CAAChI,OAAO,GAAG+H,SAAShH,oBAAoBiH;YAC9C;YACA,MAAM,EAAEC,+BAA+B,EAAE,GAAG,MAAM,MAAM,CACtD;YAEF,gDAAgD;YAChD,uBAAuB;YACvBA,gCAAgClH,oBAAoB,IAAI,CAACf,OAAO;QAClE;QACA,MAAMkI,YAAY,MAAM,IAAI,CAACjH,aAAa,CAACC,SAASH;QACpD,MAAM,EAAE2B,cAAc,EAAEN,iBAAiB,EAAEuB,mBAAmB,EAAE,GAAGuE;QAEnE,MAAM,EAAEpF,QAAQ,EAAES,IAAI,EAAEP,QAAQ,EAAE,GAAGN;QAErC,IAAII,UAAU;YACZvC,IAAImH,GAAG,GAAGzJ,iBAAiBsC,IAAImH,GAAG,IAAI,KAAK5E;QAC7C;QAEA,MAAMqF,YAAYrK,YAAYyC,IAAImH,GAAG,IAAI;QACzC,iDAAiD;QACjD,IAAI,CAACS,WAAW;YACd;QACF;QACA,IAAIC,oBAAoB;QAExB,IAAI7J,cAAc4J,UAAUE,QAAQ,IAAI,KAAK,gBAAgB;YAC3DD,oBAAoB;YACpBD,UAAUE,QAAQ,GAAG/J,kBAAkB6J,UAAUE,QAAQ,IAAI;QAC/D;QACA,IAAIC,mBAAmBH,UAAUE,QAAQ,IAAI;QAC7C,MAAME,gBAAgB;YAAE,GAAGJ,UAAUK,KAAK;QAAC;QAC3C,MAAMC,gBAAgBzK,eAAekD;QAErC,IAAIwH;QACJ,IAAIC;QAEJ,IAAIpF,MAAM;YACRmF,eAAe3K,oBACboK,UAAUE,QAAQ,IAAI,KACtB9E,KAAKqF,OAAO;YAGd,IAAIF,aAAaC,cAAc,EAAE;gBAC/BpI,IAAImH,GAAG,GAAG,GAAGgB,aAAaL,QAAQ,GAAGF,UAAUU,MAAM,EAAE;gBACvDP,mBAAmBI,aAAaL,QAAQ;gBAExC,IAAI,CAACM,gBAAgB;oBACnBA,iBAAiBD,aAAaC,cAAc;gBAC9C;YACF;QACF;QAEA,MAAMG,cAAc5K,eAAe;YACjC6K,MAAM7H;YACNqC;YACAT;YACAE;YACAyF;YACAO,eAAe7I,QAAQC,GAAG,CAAC6I,qBAAqB;YAChDtG,eAAeC,QAAQF,eAAeC,aAAa;QACrD;QAEA,MAAMuG,eAAe/K,mBACnBoF,wBAAAA,KAAM4F,OAAO,EACb/K,YAAY+J,WAAW5H,IAAI+C,OAAO,GAClCqF;QAEFnK,eAAe+B,KAAK,kBAAkBqC,QAAQsG;QAE9C,MAAME,gBAAgBF,CAAAA,gCAAAA,aAAcE,aAAa,MAAI7F,wBAAAA,KAAM6F,aAAa;QAExE,8DAA8D;QAC9D,0CAA0C;QAC1C,IAAIA,iBAAiB,CAACT,gBAAgB;YACpCR,UAAUE,QAAQ,GAAG,CAAC,CAAC,EAAEe,gBAAgBjB,UAAUE,QAAQ,KAAK,MAAM,KAAKF,UAAUE,QAAQ,EAAE;QACjG;QACA,MAAMgB,SACJ5K,eAAe8B,KAAK,aAAaoI,kBAAkBS;QAErD,MAAME,mBAAmBxD,OAAOyD,IAAI,CAClCT,YAAYU,cAAc,CAACjJ,KAAK4H;QAGlC,qDAAqD;QACrD,0BAA0B;QAC1B,IAAI5E,MAAM;YACR4E,UAAUE,QAAQ,GAAGtK,oBACnBoK,UAAUE,QAAQ,IAAI,KACtB9E,KAAKqF,OAAO,EACZP,QAAQ;QACZ;QAEA,IAAIoB,SACFhL,eAAe8B,KAAK;QAEtB,gCAAgC;QAChC,IAAI,CAACkJ,UAAUX,YAAYY,mBAAmB,EAAE;YAC9C,MAAMC,cAAcb,YAAYY,mBAAmB,CACjDpL,kBAAkBoK,CAAAA,gCAAAA,aAAcL,QAAQ,KAAIF,UAAUE,QAAQ,IAAI;YAEpE,MAAMuB,eAAed,YAAYe,2BAA2B,CAC1DF,eAAe,CAAC,GAChB;YAGF,IAAIC,aAAaE,cAAc,EAAE;gBAC/BL,SAASG,aAAaH,MAAM;YAC9B;QACF;QAEA,6DAA6D;QAC7D,8DAA8D;QAC9D,uEAAuE;QAEvE,2DAA2D;QAC3D,gEAAgE;QAChE,oEAAoE;QACpE,kEAAkE;QAClE,oEAAoE;QACpE,MAAMjB,QAAQ/J,eAAe8B,KAAK,YAAY;YAC5C,GAAG4H,UAAUK,KAAK;QACpB;QAEA,MAAMuB,iBAAiB,IAAIC;QAC3B,MAAMC,oBAAoB,EAAE;QAE5B,6DAA6D;QAC7D,8DAA8D;QAC9D,gEAAgE;QAChE,wCAAwC;QACxC,IAAI,CAAC,IAAI,CAAC7E,WAAW,EAAE;YACrB,KAAK,MAAM8E,OAAO;mBACbZ;mBACAxD,OAAOyD,IAAI,CAACT,YAAYqB,mBAAmB,IAAI,CAAC;aACpD,CAAE;gBACD,yDAAyD;gBACzD,8DAA8D;gBAC9D,+DAA+D;gBAC/D,+DAA+D;gBAC/D,8DAA8D;gBAC9D,sDAAsD;gBACtD,MAAMC,gBAAgBC,MAAMC,OAAO,CAAC/B,aAAa,CAAC2B,IAAI,IAClD3B,aAAa,CAAC2B,IAAI,CAACrJ,IAAI,CAAC,MACxB0H,aAAa,CAAC2B,IAAI;gBAEtB,MAAMK,aAAaF,MAAMC,OAAO,CAAC9B,KAAK,CAAC0B,IAAI,IACvC1B,KAAK,CAAC0B,IAAI,CAACrJ,IAAI,CAAC,MAChB2H,KAAK,CAAC0B,IAAI;gBAEd,IAAI,CAAEA,CAAAA,OAAO3B,aAAY,KAAM6B,kBAAkBG,YAAY;oBAC3DN,kBAAkBO,IAAI,CAACN;gBACzB;YACF;QACF;QAEApB,YAAY2B,eAAe,CAAClK,KAAK0J;QACjCnB,YAAY4B,oBAAoB,CAAClC,OAAOuB;QACxCjB,YAAY6B,mBAAmB,CAACpC,eAAe0B;QAE/C,IAAIxB,eAAe;YACjB,MAAMmC,cAAc9B,YAAYe,2BAA2B,CAACrB,OAAO;YAEnE,MAAMoB,eAAed,YAAYe,2BAA2B,CAC1DJ,UAAU,CAAC,GACX;YAEF,MAAMoB,sBACJjB,aAAaE,cAAc,IAAIL,SAC3BA,SACAmB,YAAYd,cAAc,GACxBtB,QACA,CAAC;YAETjI,IAAImH,GAAG,GAAGoB,YAAYgC,sBAAsB,CAC1CvK,IAAImH,GAAG,IAAI,KACXmD;YAEF1C,UAAUE,QAAQ,GAAGS,YAAYgC,sBAAsB,CACrD3C,UAAUE,QAAQ,IAAI,KACtBwC;YAEFvC,mBAAmBQ,YAAYgC,sBAAsB,CACnDxC,kBACAuC;YAGF,kCAAkC;YAClC,IAAI,CAACpB,QAAQ;gBACX,IAAImB,YAAYd,cAAc,EAAE;oBAC9BL,SAAS3D,OAAOiF,MAAM,CAAC,CAAC,GAAGH,YAAYnB,MAAM;oBAE7C,4CAA4C;oBAC5C,iBAAiB;oBACjB,IAAK,MAAMS,OAAOpB,YAAYqB,mBAAmB,CAAE;wBACjD,OAAO3B,KAAK,CAAC0B,IAAI;oBACnB;gBACF,OAAO;oBACL,qCAAqC;oBACrC,MAAMP,cAAcb,YAAYY,mBAAmB,oBAA/BZ,YAAYY,mBAAmB,MAA/BZ,aAClBxK,kBACEoK,CAAAA,gCAAAA,aAAcL,QAAQ,KAAIF,UAAUE,QAAQ,IAAI;oBAGpD,qDAAqD;oBACrD,kDAAkD;oBAClD,2BAA2B;oBAC3B,IAAIsB,aAAa;wBACfF,SAAS3D,OAAOiF,MAAM,CAAC,CAAC,GAAGpB;oBAC7B;gBACF;YACF;QACF;QAEA,sDAAsD;QACtD,iDAAiD;QACjD,oDAAoD;QACpD,KAAK,MAAMO,OAAOH,eAAgB;YAChC,IAAI,CAAEG,CAAAA,OAAO3B,aAAY,GAAI;gBAC3B,OAAOC,KAAK,CAAC0B,IAAI;YACnB;QACF;QAEA,MAAM,EAAEc,oBAAoB,EAAEC,uBAAuB,EAAE,GACrD5M,0BAA0BkC,KAAK6B,kBAAkBK,OAAO;QAE1D,IAAIyI,cAAc;QAClB,IAAIC;QAEJ,wCAAwC;QACxC,IAAIhL,QAAQC,GAAG,CAACK,YAAY,KAAK,UAAUoH,KAAK;YAC9C,MAAM,EAAEuD,iBAAiB,EAAE,GACzBtK,QAAQ;YAEVqK,cAAcC,kBACZ7K,KACAsH,KACAzF,kBAAkBK,OAAO,EACzBG,QAAQkF;YAEVoD,cAAcC,gBAAgB;QAChC;QAEA,MAAMlL,qBACJxB,eAAe8B,KAAK,yBAAyB,IAAI,CAACN,kBAAkB;QAEtE,MAAMoH,uBACJlI,gDAAAA,kBAAkB,CAACD,0BAA0B,qBAA7CC,6CAA+C,CAACc,mBAAmB;QACrE,MAAM6D,aACJuD,CAAAA,uCAAAA,oBAAqBvD,UAAU,KAAIH,oBAAoBC,MAAM;QAE/D,MAAMyH,oBAAoBpM,iBAAiBiC;QAC3C,IAAIoK,mBACF7M,eAAe8B,KAAK,iBAAiB8K;QAEvC,IAAIrN,eAAesN,qBAAqB7B,QAAQ;YAC9C6B,mBAAmBxC,YAAYgC,sBAAsB,CACnDQ,kBACA7B;QAEJ;QAEA,IAAI6B,qBAAqB,UAAU;YACjCA,mBAAmB;QACrB;QACA,IAAI;YACFA,mBAAmBlM,iBAAiBkM;QACtC,EAAE,OAAOC,GAAG,CAAC;QAEbD,mBAAmBjM,oBAAoBiM;QAEvC,OAAO;YACL9C;YACAD;YACAD;YACAmB;YACAtB;YACAkB;YACAjB;YACAQ,OAAO,EAAErF,wBAAAA,KAAMqF,OAAO;YACtBQ;YACA8B;YACAC;YACA1C;YACA6C;YACAN;YACAC;YACA,GAAG/C,SAAS;YACZjE,uBAAuBiE,UAAUjE,qBAAqB;YACtDF,yBAAyBmE,UAAUnE,uBAAuB;YAC1DD;YACAuD;QACF;IACF;IAEOmE,iBAAiBjL,GAAsC,EAAE;QAC9D,IAAI,CAAC,IAAI,CAACkL,aAAa,EAAE;YACvB,MAAM/E,cAAcjI,eAAe8B,KAAK,kBAAkB;YAC1D,IAAI,CAACkL,aAAa,GAAG,IAAIzM,cAAc0H;QACzC;QACA,OAAO,IAAI,CAAC+E,aAAa;IAC3B;IAEA,MAAaC,eAAe,EAC1BnL,GAAG,EACHuD,UAAU,EACV6H,QAAQ,EACRC,SAAS,EACTC,UAAU,EACVzJ,iBAAiB,EACjB0J,iBAAiB,EACjBd,oBAAoB,EACpBC,uBAAuB,EACvBc,iBAAiB,EACjBC,SAAS,EAaV,EAAE;QACD,MAAMP,gBAAgB,IAAI,CAACD,gBAAgB,CAACjL;QAC5C,MAAM0L,aAAa,MAAMR,cAAcS,GAAG,CAACP,UAAUI,mBAAmB;YACtEH;YACAC;YACAC;YACAd;YACAmB,YAAY5L,IAAI+C,OAAO,CAAC8I,OAAO,KAAK;YACpCC,kBAAkB,MAAM,IAAI,CAACpG,mBAAmB,CAC9C1F,KACAuD,YACA1B;YAEF4J;QACF;QAEA,IAAI,CAACC,YAAY;YACf,IACEN,YACA,kEAAkE;YAClE,CAAEX,CAAAA,wBAAwBC,uBAAsB,GAChD;gBACA,gEAAgE;gBAChE,oEAAoE;gBACpE,kEAAkE;gBAClE,mEAAmE;gBACnE,yBAAyB;gBACzB,MAAM,qBAA8D,CAA9D,IAAIpG,MAAM,sDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;QACF;QACA,OAAOoH;IACT;AACF", "ignoreList": [0]}