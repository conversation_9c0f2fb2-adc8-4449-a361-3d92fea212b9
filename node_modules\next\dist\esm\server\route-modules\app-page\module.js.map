{"version": 3, "sources": ["../../../../src/server/route-modules/app-page/module.ts"], "sourcesContent": ["import type { AppPageRouteDefinition } from '../../route-definitions/app-page-route-definition'\nimport type RenderResult from '../../render-result'\nimport type { RenderOpts } from '../../app-render/types'\nimport type { NextParsedUrlQuery } from '../../request-meta'\nimport type { LoaderTree } from '../../lib/app-dir-module'\nimport type { PrerenderManifest } from '../../../build'\n\nimport {\n  renderToHTMLOrFlight,\n  type AppSharedContext,\n} from '../../app-render/app-render'\nimport {\n  RouteModule,\n  type RouteModuleOptions,\n  type RouteModuleHandleContext,\n} from '../route-module'\nimport * as vendoredContexts from './vendored/contexts/entrypoints'\nimport type { BaseNextRequest, BaseNextResponse } from '../../base-http'\nimport type { ServerComponentsHmrCache } from '../../response-cache'\nimport type { FallbackRouteParams } from '../../request/fallback-params'\nimport { PrerenderManifestMatcher } from './helpers/prerender-manifest-matcher'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n} from '../../../client/components/app-router-headers'\nimport { isInterceptionRouteAppPath } from '../../../shared/lib/router/utils/interception-routes'\n\nlet vendoredReactRSC\nlet vendoredReactSSR\n\n// the vendored Reacts are loaded from their original source in the edge runtime\nif (process.env.NEXT_RUNTIME !== 'edge') {\n  vendoredReactRSC =\n    require('./vendored/rsc/entrypoints') as typeof import('./vendored/rsc/entrypoints')\n  vendoredReactSSR =\n    require('./vendored/ssr/entrypoints') as typeof import('./vendored/ssr/entrypoints')\n}\n\n/**\n * The AppPageModule is the type of the module exported by the bundled app page\n * module.\n */\nexport type AppPageModule = typeof import('../../../build/templates/app-page')\n\ntype AppPageUserlandModule = {\n  /**\n   * The tree created in next-app-loader that holds component segments and modules\n   */\n  loaderTree: LoaderTree\n}\n\nexport interface AppPageRouteHandlerContext extends RouteModuleHandleContext {\n  page: string\n  query: NextParsedUrlQuery\n  fallbackRouteParams: FallbackRouteParams | null\n  renderOpts: RenderOpts\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n  sharedContext: AppSharedContext\n}\n\nexport type AppPageRouteModuleOptions = RouteModuleOptions<\n  AppPageRouteDefinition,\n  AppPageUserlandModule\n>\n\nexport class AppPageRouteModule extends RouteModule<\n  AppPageRouteDefinition,\n  AppPageUserlandModule\n> {\n  constructor(\n    options: RouteModuleOptions<AppPageRouteDefinition, AppPageUserlandModule>\n  ) {\n    super(options)\n    this.isAppRouter = true\n  }\n\n  private matchers = new WeakMap<\n    DeepReadonly<PrerenderManifest>,\n    PrerenderManifestMatcher\n  >()\n  public match(\n    pathname: string,\n    prerenderManifest: DeepReadonly<PrerenderManifest>\n  ) {\n    // Lazily create the matcher based on the provided prerender manifest.\n    let matcher = this.matchers.get(prerenderManifest)\n    if (!matcher) {\n      matcher = new PrerenderManifestMatcher(\n        this.definition.pathname,\n        prerenderManifest\n      )\n      this.matchers.set(prerenderManifest, matcher)\n    }\n\n    // Match the pathname to the dynamic route.\n    return matcher.match(pathname)\n  }\n\n  public render(\n    req: BaseNextRequest,\n    res: BaseNextResponse,\n    context: AppPageRouteHandlerContext\n  ): Promise<RenderResult> {\n    return renderToHTMLOrFlight(\n      req,\n      res,\n      context.page,\n      context.query,\n      context.fallbackRouteParams,\n      context.renderOpts,\n      context.serverComponentsHmrCache,\n      false,\n      context.sharedContext\n    )\n  }\n\n  public warmup(\n    req: BaseNextRequest,\n    res: BaseNextResponse,\n    context: AppPageRouteHandlerContext\n  ): Promise<RenderResult> {\n    return renderToHTMLOrFlight(\n      req,\n      res,\n      context.page,\n      context.query,\n      context.fallbackRouteParams,\n      context.renderOpts,\n      context.serverComponentsHmrCache,\n      true,\n      context.sharedContext\n    )\n  }\n\n  private pathCouldBeIntercepted(\n    resolvedPathname: string,\n    interceptionRoutePatterns: RegExp[]\n  ): boolean {\n    return (\n      isInterceptionRouteAppPath(resolvedPathname) ||\n      interceptionRoutePatterns.some((regexp) => {\n        return regexp.test(resolvedPathname)\n      })\n    )\n  }\n\n  public getVaryHeader(\n    resolvedPathname: string,\n    interceptionRoutePatterns: RegExp[]\n  ): string {\n    const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`\n\n    if (\n      this.pathCouldBeIntercepted(resolvedPathname, interceptionRoutePatterns)\n    ) {\n      // Interception route responses can vary based on the `Next-URL` header.\n      // We use the Vary header to signal this behavior to the client to properly cache the response.\n      return `${baseVaryHeader}, ${NEXT_URL}`\n    } else {\n      // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.\n      // We also set this header for pages to avoid caching issues when navigating between pages and app.\n      return baseVaryHeader\n    }\n  }\n}\n\nconst vendored = {\n  'react-rsc': vendoredReactRSC,\n  'react-ssr': vendoredReactSSR,\n  contexts: vendoredContexts,\n}\n\nexport { renderToHTMLOrFlight, vendored }\n\nexport default AppPageRouteModule\n"], "names": ["renderToHTMLOrFlight", "RouteModule", "vendoredContexts", "PrerenderManifestMatcher", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_URL", "RSC_HEADER", "isInterceptionRouteAppPath", "vendoredReactRSC", "vendoredReactSSR", "process", "env", "NEXT_RUNTIME", "require", "AppPageRouteModule", "constructor", "options", "matchers", "WeakMap", "isAppRouter", "match", "pathname", "prerenderManifest", "matcher", "get", "definition", "set", "render", "req", "res", "context", "page", "query", "fallbackRouteParams", "renderOpts", "serverComponentsHmrCache", "sharedContext", "warmup", "pathCouldBeIntercepted", "resolvedPathname", "interceptionRoutePatterns", "some", "regexp", "test", "getVaryHeader", "baseVaryHeader", "vendored", "contexts"], "mappings": "AAOA,SACEA,oBAAoB,QAEf,8BAA6B;AACpC,SACEC,WAAW,QAGN,kBAAiB;AACxB,YAAYC,sBAAsB,kCAAiC;AAInE,SAASC,wBAAwB,QAAQ,uCAAsC;AAE/E,SACEC,2BAA2B,EAC3BC,mCAAmC,EACnCC,6BAA6B,EAC7BC,QAAQ,EACRC,UAAU,QACL,gDAA+C;AACtD,SAASC,0BAA0B,QAAQ,uDAAsD;AAEjG,IAAIC;AACJ,IAAIC;AAEJ,gFAAgF;AAChF,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCJ,mBACEK,QAAQ;IACVJ,mBACEI,QAAQ;AACZ;AA6BA,OAAO,MAAMC,2BAA2Bf;IAItCgB,YACEC,OAA0E,CAC1E;QACA,KAAK,CAACA,eAIAC,WAAW,IAAIC;QAHrB,IAAI,CAACC,WAAW,GAAG;IACrB;IAMOC,MACLC,QAAgB,EAChBC,iBAAkD,EAClD;QACA,sEAAsE;QACtE,IAAIC,UAAU,IAAI,CAACN,QAAQ,CAACO,GAAG,CAACF;QAChC,IAAI,CAACC,SAAS;YACZA,UAAU,IAAItB,yBACZ,IAAI,CAACwB,UAAU,CAACJ,QAAQ,EACxBC;YAEF,IAAI,CAACL,QAAQ,CAACS,GAAG,CAACJ,mBAAmBC;QACvC;QAEA,2CAA2C;QAC3C,OAAOA,QAAQH,KAAK,CAACC;IACvB;IAEOM,OACLC,GAAoB,EACpBC,GAAqB,EACrBC,OAAmC,EACZ;QACvB,OAAOhC,qBACL8B,KACAC,KACAC,QAAQC,IAAI,EACZD,QAAQE,KAAK,EACbF,QAAQG,mBAAmB,EAC3BH,QAAQI,UAAU,EAClBJ,QAAQK,wBAAwB,EAChC,OACAL,QAAQM,aAAa;IAEzB;IAEOC,OACLT,GAAoB,EACpBC,GAAqB,EACrBC,OAAmC,EACZ;QACvB,OAAOhC,qBACL8B,KACAC,KACAC,QAAQC,IAAI,EACZD,QAAQE,KAAK,EACbF,QAAQG,mBAAmB,EAC3BH,QAAQI,UAAU,EAClBJ,QAAQK,wBAAwB,EAChC,MACAL,QAAQM,aAAa;IAEzB;IAEQE,uBACNC,gBAAwB,EACxBC,yBAAmC,EAC1B;QACT,OACEjC,2BAA2BgC,qBAC3BC,0BAA0BC,IAAI,CAAC,CAACC;YAC9B,OAAOA,OAAOC,IAAI,CAACJ;QACrB;IAEJ;IAEOK,cACLL,gBAAwB,EACxBC,yBAAmC,EAC3B;QACR,MAAMK,iBAAiB,GAAGvC,WAAW,EAAE,EAAEF,8BAA8B,EAAE,EAAEF,4BAA4B,EAAE,EAAEC,qCAAqC;QAEhJ,IACE,IAAI,CAACmC,sBAAsB,CAACC,kBAAkBC,4BAC9C;YACA,wEAAwE;YACxE,+FAA+F;YAC/F,OAAO,GAAGK,eAAe,EAAE,EAAExC,UAAU;QACzC,OAAO;YACL,yHAAyH;YACzH,mGAAmG;YACnG,OAAOwC;QACT;IACF;AACF;AAEA,MAAMC,WAAW;IACf,aAAatC;IACb,aAAaC;IACbsC,UAAU/C;AACZ;AAEA,SAASF,oBAAoB,EAAEgD,QAAQ,GAAE;AAEzC,eAAehC,mBAAkB", "ignoreList": [0]}