{"version": 3, "sources": ["../../../src/server/async-storage/request-store.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { RenderOpts } from '../app-render/types'\nimport type { NextRequest } from '../web/spec-extension/request'\nimport type { __ApiPreviewProps } from '../api-utils'\n\nimport { FLIGHT_HEADERS } from '../../client/components/app-router-headers'\nimport {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport {\n  MutableRequestCookiesAdapter,\n  RequestCookiesAdapter,\n  responseCookiesToRequestCookies,\n  createCookiesWithMutableAccessCheck,\n  type ReadonlyRequestCookies,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { ResponseCookies, RequestCookies } from '../web/spec-extension/cookies'\nimport { DraftModeProvider } from './draft-mode-provider'\nimport { splitCookiesString } from '../web/utils'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RenderResumeDataCache } from '../resume-data-cache/resume-data-cache'\nimport type { Params } from '../request/params'\nimport type { ImplicitTags } from '../lib/implicit-tags'\nimport type { FallbackRouteParams } from '../request/fallback-params'\n\nfunction getHeaders(headers: Headers | IncomingHttpHeaders): ReadonlyHeaders {\n  const cleaned = HeadersAdapter.from(headers)\n  for (const header of FLIGHT_HEADERS) {\n    cleaned.delete(header)\n  }\n\n  return HeadersAdapter.seal(cleaned)\n}\n\nfunction getMutableCookies(\n  headers: Headers | IncomingHttpHeaders,\n  onUpdateCookies?: (cookies: string[]) => void\n): ResponseCookies {\n  const cookies = new RequestCookies(HeadersAdapter.from(headers))\n  return MutableRequestCookiesAdapter.wrap(cookies, onUpdateCookies)\n}\n\nexport type WrapperRenderOpts = Partial<Pick<RenderOpts, 'onUpdateCookies'>> & {\n  previewProps?: __ApiPreviewProps\n}\n\ntype RequestContext = RequestResponsePair & {\n  /**\n   * The URL of the request. This only specifies the pathname and the search\n   * part of the URL. This is only undefined when generating static paths (ie,\n   * there is no request in progress, nor do we know one).\n   */\n  url: {\n    /**\n     * The pathname of the requested URL.\n     */\n    pathname: string\n\n    /**\n     * The search part of the requested URL. If the request did not provide a\n     * search part, this will be an empty string.\n     */\n    search?: string\n  }\n  phase: RequestStore['phase']\n  renderOpts?: WrapperRenderOpts\n  isHmrRefresh?: boolean\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n  implicitTags: ImplicitTags\n}\n\ntype RequestResponsePair =\n  | { req: BaseNextRequest; res: BaseNextResponse } // for an app page\n  | { req: NextRequest; res: undefined } // in an api route or middleware\n\n/**\n * If middleware set cookies in this request (indicated by `x-middleware-set-cookie`),\n * then merge those into the existing cookie object, so that when `cookies()` is accessed\n * it's able to read the newly set cookies.\n */\nfunction mergeMiddlewareCookies(\n  req: RequestContext['req'],\n  existingCookies: RequestCookies | ResponseCookies\n) {\n  if (\n    'x-middleware-set-cookie' in req.headers &&\n    typeof req.headers['x-middleware-set-cookie'] === 'string'\n  ) {\n    const setCookieValue = req.headers['x-middleware-set-cookie']\n    const responseHeaders = new Headers()\n\n    for (const cookie of splitCookiesString(setCookieValue)) {\n      responseHeaders.append('set-cookie', cookie)\n    }\n\n    const responseCookies = new ResponseCookies(responseHeaders)\n\n    // Transfer cookies from ResponseCookies to RequestCookies\n    for (const cookie of responseCookies.getAll()) {\n      existingCookies.set(cookie)\n    }\n  }\n}\n\nexport function createRequestStoreForRender(\n  req: RequestContext['req'],\n  res: RequestContext['res'],\n  url: RequestContext['url'],\n  rootParams: Params,\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  previewProps: WrapperRenderOpts['previewProps'],\n  isHmrRefresh: RequestContext['isHmrRefresh'],\n  serverComponentsHmrCache: RequestContext['serverComponentsHmrCache'],\n  renderResumeDataCache: RenderResumeDataCache | undefined,\n  devFallbackParams: FallbackRouteParams | null\n): RequestStore {\n  return createRequestStoreImpl(\n    // Pages start in render phase by default\n    'render',\n    req,\n    res,\n    url,\n    rootParams,\n    implicitTags,\n    onUpdateCookies,\n    renderResumeDataCache,\n    previewProps,\n    isHmrRefresh,\n    serverComponentsHmrCache,\n    devFallbackParams\n  )\n}\n\nexport function createRequestStoreForAPI(\n  req: RequestContext['req'],\n  url: RequestContext['url'],\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  previewProps: WrapperRenderOpts['previewProps']\n): RequestStore {\n  return createRequestStoreImpl(\n    // API routes start in action phase by default\n    'action',\n    req,\n    undefined,\n    url,\n    {},\n    implicitTags,\n    onUpdateCookies,\n    undefined,\n    previewProps,\n    false,\n    undefined,\n    null\n  )\n}\n\nfunction createRequestStoreImpl(\n  phase: RequestStore['phase'],\n  req: RequestContext['req'],\n  res: RequestContext['res'],\n  url: RequestContext['url'],\n  rootParams: Params,\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  renderResumeDataCache: RenderResumeDataCache | undefined,\n  previewProps: WrapperRenderOpts['previewProps'],\n  isHmrRefresh: RequestContext['isHmrRefresh'],\n  serverComponentsHmrCache: RequestContext['serverComponentsHmrCache'],\n  devFallbackParams: FallbackRouteParams | null | undefined\n): RequestStore {\n  function defaultOnUpdateCookies(cookies: string[]) {\n    if (res) {\n      res.setHeader('Set-Cookie', cookies)\n    }\n  }\n\n  const cache: {\n    headers?: ReadonlyHeaders\n    cookies?: ReadonlyRequestCookies\n    mutableCookies?: ResponseCookies\n    userspaceMutableCookies?: ResponseCookies\n    draftMode?: DraftModeProvider\n  } = {}\n\n  return {\n    type: 'request',\n    phase,\n    implicitTags,\n    // Rather than just using the whole `url` here, we pull the parts we want\n    // to ensure we don't use parts of the URL that we shouldn't. This also\n    // lets us avoid requiring an empty string for `search` in the type.\n    url: { pathname: url.pathname, search: url.search ?? '' },\n    rootParams,\n    get headers() {\n      if (!cache.headers) {\n        // Seal the headers object that'll freeze out any methods that could\n        // mutate the underlying data.\n        cache.headers = getHeaders(req.headers)\n      }\n\n      return cache.headers\n    },\n    get cookies() {\n      if (!cache.cookies) {\n        // if middleware is setting cookie(s), then include those in\n        // the initial cached cookies so they can be read in render\n        const requestCookies = new RequestCookies(\n          HeadersAdapter.from(req.headers)\n        )\n\n        mergeMiddlewareCookies(req, requestCookies)\n\n        // Seal the cookies object that'll freeze out any methods that could\n        // mutate the underlying data.\n        cache.cookies = RequestCookiesAdapter.seal(requestCookies)\n      }\n\n      return cache.cookies\n    },\n    set cookies(value: ReadonlyRequestCookies) {\n      cache.cookies = value\n    },\n    get mutableCookies() {\n      if (!cache.mutableCookies) {\n        const mutableCookies = getMutableCookies(\n          req.headers,\n          onUpdateCookies || (res ? defaultOnUpdateCookies : undefined)\n        )\n\n        mergeMiddlewareCookies(req, mutableCookies)\n\n        cache.mutableCookies = mutableCookies\n      }\n      return cache.mutableCookies\n    },\n    get userspaceMutableCookies() {\n      if (!cache.userspaceMutableCookies) {\n        const userspaceMutableCookies =\n          createCookiesWithMutableAccessCheck(this)\n        cache.userspaceMutableCookies = userspaceMutableCookies\n      }\n      return cache.userspaceMutableCookies\n    },\n    get draftMode() {\n      if (!cache.draftMode) {\n        cache.draftMode = new DraftModeProvider(\n          previewProps,\n          req,\n          this.cookies,\n          this.mutableCookies\n        )\n      }\n\n      return cache.draftMode\n    },\n    renderResumeDataCache: renderResumeDataCache ?? null,\n    isHmrRefresh,\n    serverComponentsHmrCache:\n      serverComponentsHmrCache ||\n      (globalThis as any).__serverComponentsHmrCache,\n    devFallbackParams,\n  }\n}\n\nexport function synchronizeMutableCookies(store: RequestStore) {\n  // TODO: does this need to update headers as well?\n  store.cookies = RequestCookiesAdapter.seal(\n    responseCookiesToRequestCookies(store.mutableCookies)\n  )\n}\n"], "names": ["FLIGHT_HEADERS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MutableRequestCookiesAdapter", "RequestCookiesAdapter", "responseCookiesToRequestCookies", "createCookiesWithMutableAccessCheck", "ResponseCookies", "RequestCookies", "DraftModeProvider", "splitCookiesString", "getHeaders", "headers", "cleaned", "from", "header", "delete", "seal", "getMutableCookies", "onUpdateCookies", "cookies", "wrap", "mergeMiddlewareCookies", "req", "existingCookies", "setCookieValue", "responseHeaders", "Headers", "cookie", "append", "responseCookies", "getAll", "set", "createRequestStoreForRender", "res", "url", "rootParams", "implicitTags", "previewProps", "isHmrRefresh", "serverComponentsHmrCache", "renderResumeDataCache", "devFallbackParams", "createRequestStoreImpl", "createRequestStoreForAPI", "undefined", "phase", "defaultOnUpdateCookies", "<PERSON><PERSON><PERSON><PERSON>", "cache", "type", "pathname", "search", "requestCookies", "value", "mutableCookies", "userspaceMutableCookies", "draftMode", "globalThis", "__serverComponentsHmrCache", "synchronizeMutableCookies", "store"], "mappings": "AAOA,SAASA,cAAc,QAAQ,6CAA4C;AAC3E,SACEC,cAAc,QAET,yCAAwC;AAC/C,SACEC,4BAA4B,EAC5BC,qBAAqB,EACrBC,+BAA+B,EAC/BC,mCAAmC,QAE9B,iDAAgD;AACvD,SAASC,eAAe,EAAEC,cAAc,QAAQ,gCAA+B;AAC/E,SAASC,iBAAiB,QAAQ,wBAAuB;AACzD,SAASC,kBAAkB,QAAQ,eAAc;AAOjD,SAASC,WAAWC,OAAsC;IACxD,MAAMC,UAAUX,eAAeY,IAAI,CAACF;IACpC,KAAK,MAAMG,UAAUd,eAAgB;QACnCY,QAAQG,MAAM,CAACD;IACjB;IAEA,OAAOb,eAAee,IAAI,CAACJ;AAC7B;AAEA,SAASK,kBACPN,OAAsC,EACtCO,eAA6C;IAE7C,MAAMC,UAAU,IAAIZ,eAAeN,eAAeY,IAAI,CAACF;IACvD,OAAOT,6BAA6BkB,IAAI,CAACD,SAASD;AACpD;AAmCA;;;;CAIC,GACD,SAASG,uBACPC,GAA0B,EAC1BC,eAAiD;IAEjD,IACE,6BAA6BD,IAAIX,OAAO,IACxC,OAAOW,IAAIX,OAAO,CAAC,0BAA0B,KAAK,UAClD;QACA,MAAMa,iBAAiBF,IAAIX,OAAO,CAAC,0BAA0B;QAC7D,MAAMc,kBAAkB,IAAIC;QAE5B,KAAK,MAAMC,UAAUlB,mBAAmBe,gBAAiB;YACvDC,gBAAgBG,MAAM,CAAC,cAAcD;QACvC;QAEA,MAAME,kBAAkB,IAAIvB,gBAAgBmB;QAE5C,0DAA0D;QAC1D,KAAK,MAAME,UAAUE,gBAAgBC,MAAM,GAAI;YAC7CP,gBAAgBQ,GAAG,CAACJ;QACtB;IACF;AACF;AAEA,OAAO,SAASK,4BACdV,GAA0B,EAC1BW,GAA0B,EAC1BC,GAA0B,EAC1BC,UAAkB,EAClBC,YAA4C,EAC5ClB,eAA8C,EAC9CmB,YAA+C,EAC/CC,YAA4C,EAC5CC,wBAAoE,EACpEC,qBAAwD,EACxDC,iBAA6C;IAE7C,OAAOC,uBACL,yCAAyC;IACzC,UACApB,KACAW,KACAC,KACAC,YACAC,cACAlB,iBACAsB,uBACAH,cACAC,cACAC,0BACAE;AAEJ;AAEA,OAAO,SAASE,yBACdrB,GAA0B,EAC1BY,GAA0B,EAC1BE,YAA4C,EAC5ClB,eAA8C,EAC9CmB,YAA+C;IAE/C,OAAOK,uBACL,8CAA8C;IAC9C,UACApB,KACAsB,WACAV,KACA,CAAC,GACDE,cACAlB,iBACA0B,WACAP,cACA,OACAO,WACA;AAEJ;AAEA,SAASF,uBACPG,KAA4B,EAC5BvB,GAA0B,EAC1BW,GAA0B,EAC1BC,GAA0B,EAC1BC,UAAkB,EAClBC,YAA4C,EAC5ClB,eAA8C,EAC9CsB,qBAAwD,EACxDH,YAA+C,EAC/CC,YAA4C,EAC5CC,wBAAoE,EACpEE,iBAAyD;IAEzD,SAASK,uBAAuB3B,OAAiB;QAC/C,IAAIc,KAAK;YACPA,IAAIc,SAAS,CAAC,cAAc5B;QAC9B;IACF;IAEA,MAAM6B,QAMF,CAAC;IAEL,OAAO;QACLC,MAAM;QACNJ;QACAT;QACA,yEAAyE;QACzE,uEAAuE;QACvE,oEAAoE;QACpEF,KAAK;YAAEgB,UAAUhB,IAAIgB,QAAQ;YAAEC,QAAQjB,IAAIiB,MAAM,IAAI;QAAG;QACxDhB;QACA,IAAIxB,WAAU;YACZ,IAAI,CAACqC,MAAMrC,OAAO,EAAE;gBAClB,oEAAoE;gBACpE,8BAA8B;gBAC9BqC,MAAMrC,OAAO,GAAGD,WAAWY,IAAIX,OAAO;YACxC;YAEA,OAAOqC,MAAMrC,OAAO;QACtB;QACA,IAAIQ,WAAU;YACZ,IAAI,CAAC6B,MAAM7B,OAAO,EAAE;gBAClB,4DAA4D;gBAC5D,2DAA2D;gBAC3D,MAAMiC,iBAAiB,IAAI7C,eACzBN,eAAeY,IAAI,CAACS,IAAIX,OAAO;gBAGjCU,uBAAuBC,KAAK8B;gBAE5B,oEAAoE;gBACpE,8BAA8B;gBAC9BJ,MAAM7B,OAAO,GAAGhB,sBAAsBa,IAAI,CAACoC;YAC7C;YAEA,OAAOJ,MAAM7B,OAAO;QACtB;QACA,IAAIA,SAAQkC,MAA+B;YACzCL,MAAM7B,OAAO,GAAGkC;QAClB;QACA,IAAIC,kBAAiB;YACnB,IAAI,CAACN,MAAMM,cAAc,EAAE;gBACzB,MAAMA,iBAAiBrC,kBACrBK,IAAIX,OAAO,EACXO,mBAAoBe,CAAAA,MAAMa,yBAAyBF,SAAQ;gBAG7DvB,uBAAuBC,KAAKgC;gBAE5BN,MAAMM,cAAc,GAAGA;YACzB;YACA,OAAON,MAAMM,cAAc;QAC7B;QACA,IAAIC,2BAA0B;YAC5B,IAAI,CAACP,MAAMO,uBAAuB,EAAE;gBAClC,MAAMA,0BACJlD,oCAAoC,IAAI;gBAC1C2C,MAAMO,uBAAuB,GAAGA;YAClC;YACA,OAAOP,MAAMO,uBAAuB;QACtC;QACA,IAAIC,aAAY;YACd,IAAI,CAACR,MAAMQ,SAAS,EAAE;gBACpBR,MAAMQ,SAAS,GAAG,IAAIhD,kBACpB6B,cACAf,KACA,IAAI,CAACH,OAAO,EACZ,IAAI,CAACmC,cAAc;YAEvB;YAEA,OAAON,MAAMQ,SAAS;QACxB;QACAhB,uBAAuBA,yBAAyB;QAChDF;QACAC,0BACEA,4BACA,AAACkB,WAAmBC,0BAA0B;QAChDjB;IACF;AACF;AAEA,OAAO,SAASkB,0BAA0BC,KAAmB;IAC3D,kDAAkD;IAClDA,MAAMzC,OAAO,GAAGhB,sBAAsBa,IAAI,CACxCZ,gCAAgCwD,MAAMN,cAAc;AAExD", "ignoreList": [0]}