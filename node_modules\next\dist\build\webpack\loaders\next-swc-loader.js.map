{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "sourcesContent": ["/*\nCopyright (c) 2017 The swc Project Developers\n\nPermission is hereby granted, free of charge, to any\nperson obtaining a copy of this software and associated\ndocumentation files (the \"Software\"), to deal in the\nSoftware without restriction, including without\nlimitation the rights to use, copy, modify, merge,\npublish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software\nis furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice\nshall be included in all copies or substantial portions\nof the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF\nANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED\nTO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A\nPARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT\nSHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR\nIN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\nDEALINGS IN THE SOFTWARE.\n*/\n\nimport type { NextConfig } from '../../../types'\nimport { type WebpackLayerName, WEBPACK_LAYERS } from '../../../lib/constants'\nimport { isWasm, transform } from '../../swc'\nimport { getLoaderSWCOptions } from '../../swc/options'\nimport path, { isAbsolute } from 'path'\nimport { babelIncludeRegexes } from '../../webpack-config'\nimport { isResourceInPackages } from '../../handle-externals'\nimport type { TelemetryLoaderContext } from '../plugins/telemetry-plugin/telemetry-plugin'\nimport {\n  updateTelemetryLoaderCtxFromTransformOutput,\n  type SwcTransformTelemetryOutput,\n} from '../plugins/telemetry-plugin/update-telemetry-loader-context-from-swc'\nimport type { LoaderContext } from 'webpack'\nimport {\n  COMPILER_NAMES,\n  type CompilerNameValues,\n} from '../../../shared/lib/constants'\n\nconst maybeExclude = (\n  excludePath: string,\n  transpilePackages: string[]\n): boolean => {\n  if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n    return false\n  }\n\n  const shouldBeBundled = isResourceInPackages(excludePath, transpilePackages)\n  if (shouldBeBundled) return false\n\n  return excludePath.includes('node_modules')\n}\n\nexport interface SWCLoaderOptions {\n  rootDir: string\n  isServer: boolean\n  compilerType: CompilerNameValues\n  pagesDir?: string\n  appDir?: string\n  hasReactRefresh: boolean\n  optimizeServerReact?: boolean\n  nextConfig: NextConfig\n  jsConfig: any\n  supportedBrowsers: string[] | undefined\n  swcCacheDir: string\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n  bundleLayer?: WebpackLayerName\n  esm?: boolean\n  transpilePackages?: string[]\n}\n\n// these are exact code conditions checked\n// for to force transpiling a `node_module`\nconst FORCE_TRANSPILE_CONDITIONS =\n  /next\\/font|next\\/dynamic|use server|use client|use cache/\n// same as above, but including `import(...)`.\n// (note the optional whitespace: `import  (...)` is also syntactically valid)\nconst FORCE_TRANSPILE_CONDITIONS_WITH_IMPORT = new RegExp(\n  String.raw`(?:${FORCE_TRANSPILE_CONDITIONS.source})|import\\s*\\(`\n)\n\nasync function loaderTransform(\n  this: LoaderContext<SWCLoaderOptions> & TelemetryLoaderContext,\n  source?: string,\n  inputSourceMap?: any\n) {\n  // Make the loader async\n  const filename = this.resourcePath\n\n  // Ensure `.d.ts` are not processed.\n  if (filename.endsWith('.d.ts')) {\n    return [source, inputSourceMap]\n  }\n\n  let loaderOptions: SWCLoaderOptions = this.getOptions() || {}\n  const shouldMaybeExclude = maybeExclude(\n    filename,\n    loaderOptions.transpilePackages || []\n  )\n\n  const trackDynamicImports = shouldTrackDynamicImports(loaderOptions)\n\n  if (shouldMaybeExclude) {\n    if (!source) {\n      throw new Error(`Invariant might be excluded but missing source`)\n    }\n\n    const forceTranspileConditions = trackDynamicImports\n      ? FORCE_TRANSPILE_CONDITIONS_WITH_IMPORT\n      : FORCE_TRANSPILE_CONDITIONS\n\n    if (!forceTranspileConditions.test(source)) {\n      return [source, inputSourceMap]\n    }\n  }\n\n  const {\n    isServer,\n    rootDir,\n    pagesDir,\n    appDir,\n    hasReactRefresh,\n    nextConfig,\n    jsConfig,\n    supportedBrowsers,\n    swcCacheDir,\n    serverComponents,\n    serverReferenceHashSalt,\n    bundleLayer,\n    esm,\n  } = loaderOptions\n  const isPageFile = pagesDir ? filename.startsWith(pagesDir) : false\n  const relativeFilePathFromRoot = path.relative(rootDir, filename)\n\n  const swcOptions = getLoaderSWCOptions({\n    pagesDir,\n    appDir,\n    filename,\n    isServer,\n    isPageFile,\n    development:\n      this.mode === 'development' ||\n      !!nextConfig.experimental?.allowDevelopmentBuild,\n    isCacheComponents: nextConfig.experimental?.cacheComponents,\n    hasReactRefresh,\n    modularizeImports: nextConfig?.modularizeImports,\n    optimizePackageImports: nextConfig?.experimental?.optimizePackageImports,\n    swcPlugins: nextConfig?.experimental?.swcPlugins,\n    compilerOptions: nextConfig?.compiler,\n    optimizeServerReact: nextConfig?.experimental?.optimizeServerReact,\n    jsConfig,\n    supportedBrowsers,\n    swcCacheDir,\n    relativeFilePathFromRoot,\n    serverComponents,\n    serverReferenceHashSalt,\n    bundleLayer,\n    esm,\n    cacheHandlers: nextConfig.experimental?.cacheHandlers,\n    useCacheEnabled: nextConfig.experimental?.useCache,\n    trackDynamicImports,\n  })\n\n  const programmaticOptions = {\n    ...swcOptions,\n    filename,\n    inputSourceMap: inputSourceMap ? JSON.stringify(inputSourceMap) : undefined,\n\n    // Set the default sourcemap behavior based on Webpack's mapping flag,\n    sourceMaps: this.sourceMap,\n    inlineSourcesContent: this.sourceMap,\n\n    // Ensure that Webpack will get a full absolute path in the sourcemap\n    // so that it can properly map the module back to its internal cached\n    // modules.\n    sourceFileName: filename,\n  }\n\n  if (!programmaticOptions.inputSourceMap) {\n    delete programmaticOptions.inputSourceMap\n  }\n\n  // auto detect development mode\n  if (\n    this.mode &&\n    programmaticOptions.jsc &&\n    programmaticOptions.jsc.transform &&\n    programmaticOptions.jsc.transform.react &&\n    !Object.prototype.hasOwnProperty.call(\n      programmaticOptions.jsc.transform.react,\n      'development'\n    )\n  ) {\n    programmaticOptions.jsc.transform.react.development =\n      this.mode === 'development'\n  }\n\n  return transform(source as any, programmaticOptions).then(\n    (\n      output: {\n        code: string\n        map?: string\n      } & SwcTransformTelemetryOutput\n    ) => {\n      updateTelemetryLoaderCtxFromTransformOutput(this, output)\n      return [output.code, output.map ? JSON.parse(output.map) : undefined]\n    }\n  )\n}\n\nfunction shouldTrackDynamicImports(loaderOptions: SWCLoaderOptions): boolean {\n  // we only need to track `import()` 1. in cacheComponents, 2. on the server (RSC and SSR)\n  // (Note: logic duplicated in crates/next-core/src/next_server/transforms.rs)\n  const { nextConfig, bundleLayer, compilerType } = loaderOptions\n  return (\n    !!nextConfig.experimental?.cacheComponents &&\n    // NOTE: `server` means nodejs. `cacheComponents` is not supported in the edge runtime, so we want to exclude it.\n    // (also, the code generated by the dynamic imports transform relies on `CacheSignal`, which uses nodejs-specific APIs)\n    compilerType === COMPILER_NAMES.server &&\n    (bundleLayer === WEBPACK_LAYERS.reactServerComponents ||\n      bundleLayer === WEBPACK_LAYERS.serverSideRendering)\n  )\n}\n\nconst EXCLUDED_PATHS =\n  /[\\\\/](cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules|__virtual__)[\\\\/]/g\n\nexport function pitch(this: any) {\n  const callback = this.async()\n  let loaderOptions: SWCLoaderOptions = this.getOptions() || {}\n\n  const shouldMaybeExclude = maybeExclude(\n    this.resourcePath,\n    loaderOptions.transpilePackages || []\n  )\n\n  ;(async () => {\n    if (\n      // if it might be excluded/no-op we can't use pitch loader\n      !shouldMaybeExclude &&\n      // TODO: investigate swc file reading in PnP mode?\n      !process.versions.pnp &&\n      !EXCLUDED_PATHS.test(this.resourcePath) &&\n      this.loaders.length - 1 === this.loaderIndex &&\n      isAbsolute(this.resourcePath) &&\n      !(await isWasm())\n    ) {\n      this.addDependency(this.resourcePath)\n      return loaderTransform.call(this)\n    }\n  })().then((r) => {\n    if (r) return callback(null, ...r)\n    callback()\n  }, callback)\n}\n\nexport default function swcLoader(\n  this: any,\n  inputSource: string,\n  inputSourceMap: any\n) {\n  const callback = this.async()\n  loaderTransform.call(this, inputSource, inputSourceMap).then(\n    ([transformedSource, outputSourceMap]: any) => {\n      callback(null, transformedSource, outputSourceMap || inputSourceMap)\n    },\n    (err: Error) => {\n      callback(err)\n    }\n  )\n}\n\n// accept Buffers instead of strings\nexport const raw = true\n"], "names": ["sw<PERSON><PERSON><PERSON><PERSON>", "pitch", "raw", "maybeExclude", "excludePath", "transpilePackages", "babelIncludeRegexes", "some", "r", "test", "shouldBeBundled", "isResourceInPackages", "includes", "FORCE_TRANSPILE_CONDITIONS", "FORCE_TRANSPILE_CONDITIONS_WITH_IMPORT", "RegExp", "String", "source", "loaderTransform", "inputSourceMap", "nextConfig", "filename", "resourcePath", "endsWith", "loaderOptions", "getOptions", "shouldMaybeExclude", "trackDynamicImports", "shouldTrackDynamicImports", "Error", "forceTranspileConditions", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "serverComponents", "serverReferenceHashSalt", "bundleLayer", "esm", "isPageFile", "startsWith", "relativeFilePathFromRoot", "path", "relative", "swcOptions", "getLoaderSWCOptions", "development", "mode", "experimental", "allowDevelopmentBuild", "isCacheComponents", "cacheComponents", "modularizeImports", "optimizePackageImports", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "cacheHandlers", "useCacheEnabled", "useCache", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "transform", "react", "Object", "prototype", "hasOwnProperty", "call", "then", "output", "updateTelemetryLoaderCtxFromTransformOutput", "code", "map", "parse", "compilerType", "COMPILER_NAMES", "server", "WEBPACK_LAYERS", "reactServerComponents", "serverSideRendering", "EXCLUDED_PATHS", "callback", "async", "process", "versions", "pnp", "loaders", "length", "loaderIndex", "isAbsolute", "isWasm", "addDependency", "inputSource", "transformedSource", "outputSourceMap", "err"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;;;;;;;;;;;;;IA8OA,OAcC;eAduBA;;IA7BRC,KAAK;eAALA;;IA8CHC,GAAG;eAAHA;;;2BA5PyC;qBACpB;yBACE;8DACH;+BACG;iCACC;qDAK9B;4BAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,eAAe,CACnBC,aACAC;IAEA,IAAIC,kCAAmB,CAACC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,CAACL,eAAe;QACxD,OAAO;IACT;IAEA,MAAMM,kBAAkBC,IAAAA,qCAAoB,EAACP,aAAaC;IAC1D,IAAIK,iBAAiB,OAAO;IAE5B,OAAON,YAAYQ,QAAQ,CAAC;AAC9B;AAqBA,0CAA0C;AAC1C,2CAA2C;AAC3C,MAAMC,6BACJ;AACF,8CAA8C;AAC9C,8EAA8E;AAC9E,MAAMC,yCAAyC,IAAIC,OACjDC,OAAOd,GAAG,CAAC,GAAG,EAAEW,2BAA2BI,MAAM,CAAC,aAAa,CAAC;AAGlE,eAAeC,gBAEbD,MAAe,EACfE,cAAoB;QA0DdC,0BACeA,2BAGKA,2BACZA,2BAESA,2BASNA,2BACEA;IAzEnB,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,oCAAoC;IACpC,IAAID,SAASE,QAAQ,CAAC,UAAU;QAC9B,OAAO;YAACN;YAAQE;SAAe;IACjC;IAEA,IAAIK,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAC5D,MAAMC,qBAAqBvB,aACzBkB,UACAG,cAAcnB,iBAAiB,IAAI,EAAE;IAGvC,MAAMsB,sBAAsBC,0BAA0BJ;IAEtD,IAAIE,oBAAoB;QACtB,IAAI,CAACT,QAAQ;YACX,MAAM,qBAA2D,CAA3D,IAAIY,MAAM,CAAC,8CAA8C,CAAC,GAA1D,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEA,MAAMC,2BAA2BH,sBAC7Bb,yCACAD;QAEJ,IAAI,CAACiB,yBAAyBrB,IAAI,CAACQ,SAAS;YAC1C,OAAO;gBAACA;gBAAQE;aAAe;QACjC;IACF;IAEA,MAAM,EACJY,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACff,UAAU,EACVgB,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACXC,GAAG,EACJ,GAAGlB;IACJ,MAAMmB,aAAaV,WAAWZ,SAASuB,UAAU,CAACX,YAAY;IAC9D,MAAMY,2BAA2BC,aAAI,CAACC,QAAQ,CAACf,SAASX;IAExD,MAAM2B,aAAaC,IAAAA,4BAAmB,EAAC;QACrChB;QACAC;QACAb;QACAU;QACAY;QACAO,aACE,IAAI,CAACC,IAAI,KAAK,iBACd,CAAC,GAAC/B,2BAAAA,WAAWgC,YAAY,qBAAvBhC,yBAAyBiC,qBAAqB;QAClDC,iBAAiB,GAAElC,4BAAAA,WAAWgC,YAAY,qBAAvBhC,0BAAyBmC,eAAe;QAC3DpB;QACAqB,iBAAiB,EAAEpC,8BAAAA,WAAYoC,iBAAiB;QAChDC,sBAAsB,EAAErC,+BAAAA,4BAAAA,WAAYgC,YAAY,qBAAxBhC,0BAA0BqC,sBAAsB;QACxEC,UAAU,EAAEtC,+BAAAA,4BAAAA,WAAYgC,YAAY,qBAAxBhC,0BAA0BsC,UAAU;QAChDC,eAAe,EAAEvC,8BAAAA,WAAYwC,QAAQ;QACrCC,mBAAmB,EAAEzC,+BAAAA,4BAAAA,WAAYgC,YAAY,qBAAxBhC,0BAA0ByC,mBAAmB;QAClEzB;QACAC;QACAC;QACAO;QACAN;QACAC;QACAC;QACAC;QACAoB,aAAa,GAAE1C,4BAAAA,WAAWgC,YAAY,qBAAvBhC,0BAAyB0C,aAAa;QACrDC,eAAe,GAAE3C,4BAAAA,WAAWgC,YAAY,qBAAvBhC,0BAAyB4C,QAAQ;QAClDrC;IACF;IAEA,MAAMsC,sBAAsB;QAC1B,GAAGjB,UAAU;QACb3B;QACAF,gBAAgBA,iBAAiB+C,KAAKC,SAAS,CAAChD,kBAAkBiD;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBnD;IAClB;IAEA,IAAI,CAAC4C,oBAAoB9C,cAAc,EAAE;QACvC,OAAO8C,oBAAoB9C,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAACgC,IAAI,IACTc,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAACC,SAAS,IACjCT,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCd,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,EACvC,gBAEF;QACAV,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,CAACzB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,OAAOuB,IAAAA,cAAS,EAACzD,QAAegD,qBAAqBe,IAAI,CACvD,CACEC;QAKAC,IAAAA,gFAA2C,EAAC,IAAI,EAAED;QAClD,OAAO;YAACA,OAAOE,IAAI;YAAEF,OAAOG,GAAG,GAAGlB,KAAKmB,KAAK,CAACJ,OAAOG,GAAG,IAAIhB;SAAU;IACvE;AAEJ;AAEA,SAASxC,0BAA0BJ,aAA+B;QAK5DJ;IAJJ,yFAAyF;IACzF,6EAA6E;IAC7E,MAAM,EAAEA,UAAU,EAAEqB,WAAW,EAAE6C,YAAY,EAAE,GAAG9D;IAClD,OACE,CAAC,GAACJ,2BAAAA,WAAWgC,YAAY,qBAAvBhC,yBAAyBmC,eAAe,KAC1C,iHAAiH;IACjH,uHAAuH;IACvH+B,iBAAiBC,0BAAc,CAACC,MAAM,IACrC/C,CAAAA,gBAAgBgD,yBAAc,CAACC,qBAAqB,IACnDjD,gBAAgBgD,yBAAc,CAACE,mBAAmB,AAAD;AAEvD;AAEA,MAAMC,iBACJ;AAEK,SAAS3F;IACd,MAAM4F,WAAW,IAAI,CAACC,KAAK;IAC3B,IAAItE,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAMC,qBAAqBvB,aACzB,IAAI,CAACmB,YAAY,EACjBE,cAAcnB,iBAAiB,IAAI,EAAE;IAGrC,CAAA;QACA,IACE,0DAA0D;QAC1D,CAACqB,sBACD,kDAAkD;QAClD,CAACqE,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACL,eAAenF,IAAI,CAAC,IAAI,CAACa,YAAY,KACtC,IAAI,CAAC4E,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5CC,IAAAA,gBAAU,EAAC,IAAI,CAAC/E,YAAY,KAC5B,CAAE,MAAMgF,IAAAA,WAAM,KACd;YACA,IAAI,CAACC,aAAa,CAAC,IAAI,CAACjF,YAAY;YACpC,OAAOJ,gBAAgB6D,IAAI,CAAC,IAAI;QAClC;IACF,CAAA,IAAKC,IAAI,CAAC,CAACxE;QACT,IAAIA,GAAG,OAAOqF,SAAS,SAASrF;QAChCqF;IACF,GAAGA;AACL;AAEe,SAAS7F,UAEtBwG,WAAmB,EACnBrF,cAAmB;IAEnB,MAAM0E,WAAW,IAAI,CAACC,KAAK;IAC3B5E,gBAAgB6D,IAAI,CAAC,IAAI,EAAEyB,aAAarF,gBAAgB6D,IAAI,CAC1D,CAAC,CAACyB,mBAAmBC,gBAAqB;QACxCb,SAAS,MAAMY,mBAAmBC,mBAAmBvF;IACvD,GACA,CAACwF;QACCd,SAASc;IACX;AAEJ;AAGO,MAAMzG,MAAM", "ignoreList": [0]}