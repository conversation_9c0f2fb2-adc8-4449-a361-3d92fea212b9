"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[607],{1362:(e,t,n)=>{n.d(t,{D:()=>l,N:()=>s});var r=n(2115),o=(e,t,n,r,o,a,i,c)=>{let u=document.documentElement,l=["light","dark"];function s(t){var n;(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&a?o.map(e=>a[e]||e):o;n?(u.classList.remove(...r),u.classList.add(a&&a[t]?a[t]:t)):u.setAttribute(e,t)}),n=t,c&&l.includes(n)&&(u.style.colorScheme=n)}if(r)s(r);else try{let e=localStorage.getItem(t)||n,r=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;s(r)}catch(e){}},a=["light","dark"],i="(prefers-color-scheme: dark)",c=r.createContext(void 0),u={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=r.useContext(c))?e:u},s=e=>r.useContext(c)?r.createElement(r.Fragment,null,e.children):r.createElement(f,{...e}),d=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:o=!0,enableColorScheme:u=!0,storageKey:l="theme",themes:s=d,defaultTheme:f=o?"system":"light",attribute:y="data-theme",value:g,children:E,nonce:b,scriptProps:w}=e,[S,C]=r.useState(()=>v(l,f)),[k,L]=r.useState(()=>"system"===S?p():S),T=g?Object.values(g):s,A=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=p());let r=g?g[t]:t,i=n?h(b):null,c=document.documentElement,l=e=>{"class"===e?(c.classList.remove(...T),r&&c.classList.add(r)):e.startsWith("data-")&&(r?c.setAttribute(e,r):c.removeAttribute(e))};if(Array.isArray(y)?y.forEach(l):l(y),u){let e=a.includes(f)?f:null,n=a.includes(t)?t:e;c.style.colorScheme=n}null==i||i()},[b]),x=r.useCallback(e=>{let t="function"==typeof e?e(S):e;C(t);try{localStorage.setItem(l,t)}catch(e){}},[S]),N=r.useCallback(e=>{L(p(e)),"system"===S&&o&&!t&&A("system")},[S,t]);r.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(N),N(e),()=>e.removeListener(N)},[N]),r.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?C(e.newValue):x(f))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[x]),r.useEffect(()=>{A(null!=t?t:S)},[t,S]);let P=r.useMemo(()=>({theme:S,setTheme:x,forcedTheme:t,resolvedTheme:"system"===S?k:S,themes:o?[...s,"system"]:s,systemTheme:o?k:void 0}),[S,x,t,k,o,s]);return r.createElement(c.Provider,{value:P},r.createElement(m,{forcedTheme:t,storageKey:l,attribute:y,enableSystem:o,enableColorScheme:u,defaultTheme:f,value:g,themes:s,nonce:b,scriptProps:w}),E)},m=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:a,enableSystem:i,enableColorScheme:c,defaultTheme:u,value:l,themes:s,nonce:d,scriptProps:f}=e,m=JSON.stringify([a,n,u,t,s,l,i,c]).slice(1,-1);return r.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(m,")")}})}),v=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},h=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},p=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},2293:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(2115),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:i()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},3795:(e,t,n)=>{n.d(t,{A:()=>H});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var c=("function"==typeof SuppressedError&&SuppressedError,n(2115)),u="right-scroll-bar-position",l="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?c.useLayoutEffect:c.useEffect,f=new WeakMap;function m(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=m),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return o.options=a({async:!0,ssr:!1},e),o}(),h=function(){},p=c.forwardRef(function(e,t){var n,r,o,u,l=c.useRef(null),m=c.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),p=m[0],y=m[1],g=e.forwardProps,E=e.children,b=e.className,w=e.removeScrollBar,S=e.enabled,C=e.shards,k=e.sideCar,L=e.noRelative,T=e.noIsolation,A=e.inert,x=e.allowPinchZoom,N=e.as,P=e.gapMode,O=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=(n=[l,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,c.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),R=a(a({},O),p);return c.createElement(c.Fragment,null,S&&c.createElement(k,{sideCar:v,removeScrollBar:w,shards:C,noRelative:L,noIsolation:T,inert:A,setCallbacks:y,allowPinchZoom:!!x,lockRef:l,gapMode:P}),g?c.cloneElement(c.Children.only(E),a(a({},R),{ref:M})):c.createElement(void 0===N?"div":N,a({},R,{className:b,ref:M}),E))});p.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},p.classNames={fullWidth:l,zeroRight:u};var y=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return c.createElement(r,a({},n))};y.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},E=function(){var e=g();return function(t,n){c.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=E();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},S=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[S(n),S(r),S(o)]},k=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},L=b(),T="data-scroll-locked",A=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(T,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(T,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},x=function(){var e=parseInt(document.body.getAttribute(T)||"0",10);return isFinite(e)?e:0},N=function(){c.useEffect(function(){return document.body.setAttribute(T,(x()+1).toString()),function(){var e=x()-1;e<=0?document.body.removeAttribute(T):document.body.setAttribute(T,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var a=c.useMemo(function(){return k(o)},[o]);return c.createElement(L,{styles:A(a,!t,o,n?"":"!important")})},O=!1;if("undefined"!=typeof window)try{var M=Object.defineProperty({},"passive",{get:function(){return O=!0,!0}});window.addEventListener("test",M,M),window.removeEventListener("test",M,M)}catch(e){O=!1}var R=!!O&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},W=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},j=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),c=i*r,u=n.target,l=t.contains(u),s=!1,d=c>0,f=0,m=0;do{if(!u)break;var v=F(e,u),h=v[0],p=v[1]-v[2]-i*h;(h||p)&&I(e,u)&&(f+=p,m+=h);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&c>f)?s=!0:!d&&(o&&1>Math.abs(m)||!o&&-c>m)&&(s=!0),s},K=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},Y=0,z=[];let X=(r=function(e){var t=c.useRef([]),n=c.useRef([0,0]),r=c.useRef(),o=c.useState(Y++)[0],a=c.useState(b)[0],i=c.useRef(e);c.useEffect(function(){i.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=c.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=K(e),c=n.current,u="deltaX"in e?e.deltaX:c[0]-a[0],l="deltaY"in e?e.deltaY:c[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=W(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=W(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var m=r.current||o;return j(m,t,e,"h"===m?u:l,!0)},[]),l=c.useCallback(function(e){if(z.length&&z[z.length-1]===a){var n="deltaY"in e?B(e):K(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=c.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=c.useCallback(function(e){n.current=K(e),r.current=void 0},[]),f=c.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),m=c.useCallback(function(t){s(t.type,K(t),t.target,u(t,e.lockRef.current))},[]);c.useEffect(function(){return z.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:m}),document.addEventListener("wheel",l,R),document.addEventListener("touchmove",l,R),document.addEventListener("touchstart",d,R),function(){z=z.filter(function(e){return e!==a}),document.removeEventListener("wheel",l,R),document.removeEventListener("touchmove",l,R),document.removeEventListener("touchstart",d,R)}},[]);var v=e.removeScrollBar,h=e.inert;return c.createElement(c.Fragment,null,h?c.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?c.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},v.useMedium(r),y);var q=c.forwardRef(function(e,t){return c.createElement(p,a({},e,{ref:t,sideCar:X}))});q.classNames=p.classNames;let H=q},4378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(2115),o=n(7650),a=n(3655),i=n(2712),c=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:l,...s}=e,[d,f]=r.useState(!1);(0,i.N)(()=>f(!0),[]);let m=l||d&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return m?o.createPortal((0,c.jsx)(a.sG.div,{...s,ref:t}),m):null});u.displayName="Portal"},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),o=n(6101),a=n(3655),i=n(9033),c=n(5155),u="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:p,onUnmountAutoFocus:y,...g}=e,[E,b]=r.useState(null),w=(0,i.c)(p),S=(0,i.c)(y),C=r.useRef(null),k=(0,o.s)(t,e=>b(e)),L=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(L.paused||!E)return;let t=e.target;E.contains(t)?C.current=t:v(C.current,{select:!0})},t=function(e){if(L.paused||!E)return;let t=e.relatedTarget;null!==t&&(E.contains(t)||v(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(E)});return E&&n.observe(E,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,E,L.paused]),r.useEffect(()=>{if(E){h.add(L);let e=document.activeElement;if(!E.contains(e)){let t=new CustomEvent(u,s);E.addEventListener(u,w),E.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(f(E).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(E))}return()=>{E.removeEventListener(u,w),setTimeout(()=>{let t=new CustomEvent(l,s);E.addEventListener(l,S),E.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),E.removeEventListener(l,S),h.remove(L)},0)}}},[E,w,S,L]);let T=r.useCallback(e=>{if(!n&&!d||L.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[m(t,e),m(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&v(a,{select:!0})):(e.preventDefault(),n&&v(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,L.paused]);return(0,c.jsx)(a.sG.div,{tabIndex:-1,...g,ref:k,onKeyDown:T})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function m(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=p(e,t)).unshift(t)},remove(t){var n;null==(n=(e=p(e,t))[0])||n.resume()}}}();function p(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8168:(e,t,n)=>{n.d(t,{Eq:()=>l});var r=new WeakMap,o=new WeakMap,a={},i=0,c=function(e){return e&&(e.host||c(e.parentNode))},u=function(e,t,n,u){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,m=new Set(l),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};l.forEach(v);var h=function(e){!e||m.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(u),a=null!==t&&"false"!==t,i=(r.get(e)||0)+1,c=(s.get(e)||0)+1;r.set(e,i),s.set(e,c),d.push(e),1===i&&a&&o.set(e,!0),1===c&&e.setAttribute(n,"true"),a||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),i++,function(){d.forEach(function(e){var t=r.get(e)-1,a=s.get(e)-1;r.set(e,t),s.set(e,a),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),a||e.removeAttribute(n)}),--i||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}},l=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),u(r,o,n,"aria-hidden")):function(){return null}}},8169:(e,t,n)=>{n.d(t,{$A:()=>i,YL:()=>u});var r=n(5155),o=n(2115);let a=(0,n(344).q6)("SearchContext",{enabled:!1,hotKey:[],setOpenSearch:()=>void 0});function i(){return a.use()}function c(){let[e,t]=(0,o.useState)("⌘");return(0,o.useEffect)(()=>{window.navigator.userAgent.includes("Windows")&&t("Ctrl")},[]),e}function u(e){let{SearchDialog:t,children:n,preload:i=!0,options:u,hotKey:l=[{key:e=>e.metaKey||e.ctrlKey,display:(0,r.jsx)(c,{})},{key:"k",display:"K"}],links:s}=e,[d,f]=(0,o.useState)(!i&&void 0);return(0,o.useEffect)(()=>{let e=e=>{l.every(t=>"string"==typeof t.key?e.key===t.key:t.key(e))&&(f(!0),e.preventDefault())};return window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e)}},[l]),(0,r.jsxs)(a.Provider,{value:(0,o.useMemo)(()=>({enabled:!0,hotKey:l,setOpenSearch:f}),[l]),children:[void 0!==d&&(0,r.jsx)(t,{open:d,onOpenChange:f,links:s,...u}),n]})}},9178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(2115),a=n(5185),i=n(3655),c=n(6101),u=n(9033),l=n(5155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:p,onPointerDownOutside:y,onFocusOutside:g,onInteractOutside:E,onDismiss:b,...w}=e,S=o.useContext(d),[C,k]=o.useState(null),L=null!=(f=null==C?void 0:C.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,T]=o.useState({}),A=(0,c.s)(t,e=>k(e)),x=Array.from(S.layers),[N]=[...S.layersWithOutsidePointerEventsDisabled].slice(-1),P=x.indexOf(N),O=C?x.indexOf(C):-1,M=S.layersWithOutsidePointerEventsDisabled.size>0,R=O>=P,D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){v("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...S.branches].some(e=>e.contains(t));R&&!n&&(null==y||y(e),null==E||E(e),e.defaultPrevented||null==b||b())},L),W=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&v("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![...S.branches].some(e=>e.contains(t))&&(null==g||g(e),null==E||E(e),e.defaultPrevented||null==b||b())},L);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{O===S.layers.size-1&&(null==p||p(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},L),o.useEffect(()=>{if(C)return h&&(0===S.layersWithOutsidePointerEventsDisabled.size&&(r=L.body.style.pointerEvents,L.body.style.pointerEvents="none"),S.layersWithOutsidePointerEventsDisabled.add(C)),S.layers.add(C),m(),()=>{h&&1===S.layersWithOutsidePointerEventsDisabled.size&&(L.body.style.pointerEvents=r)}},[C,L,h,S]),o.useEffect(()=>()=>{C&&(S.layers.delete(C),S.layersWithOutsidePointerEventsDisabled.delete(C),m())},[C,S]),o.useEffect(()=>{let e=()=>T({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,l.jsx)(i.sG.div,{...w,ref:A,style:{pointerEvents:M?R?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.mK)(e.onFocusCapture,W.onFocusCapture),onBlurCapture:(0,a.mK)(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:(0,a.mK)(e.onPointerDownCapture,D.onPointerDownCapture)})});function m(){let e=new CustomEvent(s);document.dispatchEvent(e)}function v(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,c=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,i.hO)(a,c):a.dispatchEvent(c)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),a=(0,c.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(i.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch"}}]);