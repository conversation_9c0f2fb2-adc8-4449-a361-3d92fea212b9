{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js"], "sourcesContent": ["// src/search/shared.ts\nfunction escapeRegExp(input) {\n  return input.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction buildRegexFromQuery(q) {\n  const trimmed = q.trim();\n  if (trimmed.length === 0) return null;\n  const terms = Array.from(\n    new Set(\n      trimmed.split(/\\s+/).map((t) => t.trim()).filter(Boolean)\n    )\n  );\n  if (terms.length === 0) return null;\n  const escaped = terms.map(escapeRegExp).join(\"|\");\n  return new RegExp(`(${escaped})`, \"gi\");\n}\nfunction createContentHighlighter(query) {\n  const regex = typeof query === \"string\" ? buildRegexFromQuery(query) : query;\n  return {\n    highlight(content) {\n      if (!regex) return [{ type: \"text\", content }];\n      const out = [];\n      let i = 0;\n      for (const match of content.matchAll(regex)) {\n        if (i < match.index) {\n          out.push({\n            type: \"text\",\n            content: content.substring(i, match.index)\n          });\n        }\n        out.push({\n          type: \"text\",\n          content: match[0],\n          styles: {\n            highlight: true\n          }\n        });\n        i = match.index + match[0].length;\n      }\n      if (i < content.length) {\n        out.push({\n          type: \"text\",\n          content: content.substring(i)\n        });\n      }\n      return out;\n    }\n  };\n}\n\nexport {\n  createContentHighlighter\n};\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AACvB,SAAS,aAAa,KAAK;IACzB,OAAO,MAAM,OAAO,CAAC,uBAAuB;AAC9C;AACA,SAAS,oBAAoB,CAAC;IAC5B,MAAM,UAAU,EAAE,IAAI;IACtB,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IACjC,MAAM,QAAQ,MAAM,IAAI,CACtB,IAAI,IACF,QAAQ,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,MAAM,CAAC;IAGrD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAC/B,MAAM,UAAU,MAAM,GAAG,CAAC,cAAc,IAAI,CAAC;IAC7C,OAAO,IAAI,OAAO,AAAC,IAAW,OAAR,SAAQ,MAAI;AACpC;AACA,SAAS,yBAAyB,KAAK;IACrC,MAAM,QAAQ,OAAO,UAAU,WAAW,oBAAoB,SAAS;IACvE,OAAO;QACL,WAAU,OAAO;YACf,IAAI,CAAC,OAAO,OAAO;gBAAC;oBAAE,MAAM;oBAAQ;gBAAQ;aAAE;YAC9C,MAAM,MAAM,EAAE;YACd,IAAI,IAAI;YACR,KAAK,MAAM,SAAS,QAAQ,QAAQ,CAAC,OAAQ;gBAC3C,IAAI,IAAI,MAAM,KAAK,EAAE;oBACnB,IAAI,IAAI,CAAC;wBACP,MAAM;wBACN,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,KAAK;oBAC3C;gBACF;gBACA,IAAI,IAAI,CAAC;oBACP,MAAM;oBACN,SAAS,KAAK,CAAC,EAAE;oBACjB,QAAQ;wBACN,WAAW;oBACb;gBACF;gBACA,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;YACnC;YACA,IAAI,IAAI,QAAQ,MAAM,EAAE;gBACtB,IAAI,IAAI,CAAC;oBACP,MAAM;oBACN,SAAS,QAAQ,SAAS,CAAC;gBAC7B;YACF;YACA,OAAO;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js"], "sourcesContent": ["import {\n  createContentHighlighter\n} from \"./chunk-CNWEGOUF.js\";\nimport \"./chunk-JSBRDJBE.js\";\n\n// src/search/client/algolia.ts\nfunction groupResults(hits) {\n  const grouped = [];\n  const scannedUrls = /* @__PURE__ */ new Set();\n  for (const hit of hits) {\n    if (!scannedUrls.has(hit.url)) {\n      scannedUrls.add(hit.url);\n      grouped.push({\n        id: hit.url,\n        type: \"page\",\n        url: hit.url,\n        content: hit.title\n      });\n    }\n    grouped.push({\n      id: hit.objectID,\n      type: hit.content === hit.section ? \"heading\" : \"text\",\n      url: hit.section_id ? `${hit.url}#${hit.section_id}` : hit.url,\n      content: hit.content\n    });\n  }\n  return grouped;\n}\nasync function searchDocs(query, { indexName, onSearch, client, locale, tag }) {\n  if (query.trim().length === 0) return [];\n  const result = onSearch ? await onSearch(query, tag, locale) : await client.searchForHits({\n    requests: [\n      {\n        type: \"default\",\n        indexName,\n        query,\n        distinct: 5,\n        hitsPerPage: 10,\n        filters: tag ? `tag:${tag}` : void 0\n      }\n    ]\n  });\n  const highlighter = createContentHighlighter(query);\n  return groupResults(result.results[0].hits).flatMap((hit) => {\n    if (hit.type === \"page\") {\n      return {\n        ...hit,\n        contentWithHighlights: hit.contentWithHighlights ?? highlighter.highlight(hit.content)\n      };\n    }\n    return [];\n  });\n}\nexport {\n  groupResults,\n  searchDocs\n};\n"], "names": [], "mappings": ";;;;AAAA;AAGA;;;AAEA,+BAA+B;AAC/B,SAAS,aAAa,IAAI;IACxB,MAAM,UAAU,EAAE;IAClB,MAAM,cAAc,aAAa,GAAG,IAAI;IACxC,KAAK,MAAM,OAAO,KAAM;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,GAAG,GAAG;YAC7B,YAAY,GAAG,CAAC,IAAI,GAAG;YACvB,QAAQ,IAAI,CAAC;gBACX,IAAI,IAAI,GAAG;gBACX,MAAM;gBACN,KAAK,IAAI,GAAG;gBACZ,SAAS,IAAI,KAAK;YACpB;QACF;QACA,QAAQ,IAAI,CAAC;YACX,IAAI,IAAI,QAAQ;YAChB,MAAM,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG,YAAY;YAChD,KAAK,IAAI,UAAU,GAAG,AAAC,GAAa,OAAX,IAAI,GAAG,EAAC,KAAkB,OAAf,IAAI,UAAU,IAAK,IAAI,GAAG;YAC9D,SAAS,IAAI,OAAO;QACtB;IACF;IACA,OAAO;AACT;AACA,eAAe,WAAW,KAAK,EAAE,KAA4C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAA5C;IAC/B,IAAI,MAAM,IAAI,GAAG,MAAM,KAAK,GAAG,OAAO,EAAE;IACxC,MAAM,SAAS,WAAW,MAAM,SAAS,OAAO,KAAK,UAAU,MAAM,OAAO,aAAa,CAAC;QACxF,UAAU;YACR;gBACE,MAAM;gBACN;gBACA;gBACA,UAAU;gBACV,aAAa;gBACb,SAAS,MAAM,AAAC,OAAU,OAAJ,OAAQ,KAAK;YACrC;SACD;IACH;IACA,MAAM,cAAc,CAAA,GAAA,gKAAA,CAAA,2BAAwB,AAAD,EAAE;IAC7C,OAAO,aAAa,OAAO,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACnD,IAAI,IAAI,IAAI,KAAK,QAAQ;gBAGE;YAFzB,OAAO;gBACL,GAAG,GAAG;gBACN,uBAAuB,CAAA,6BAAA,IAAI,qBAAqB,cAAzB,wCAAA,6BAA6B,YAAY,SAAS,CAAC,IAAI,OAAO;YACvF;QACF;QACA,OAAO,EAAE;IACX;AACF", "ignoreList": [0], "debugId": null}}]}