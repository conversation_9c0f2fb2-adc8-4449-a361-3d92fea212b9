{"version": 3, "sources": ["../../../src/server/dev/hot-middleware.ts"], "sourcesContent": ["// Based on https://github.com/webpack-contrib/webpack-hot-middleware/blob/9708d781ae0e46179cf8ea1a94719de4679aaf53/middleware.js\n// Included License below\n\n// Copyright JS Foundation and other contributors\n\n// Permission is hereby granted, free of charge, to any person obtaining\n// a copy of this software and associated documentation files (the\n// 'Software'), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to\n// permit persons to whom the Software is furnished to do so, subject to\n// the following conditions:\n\n// The above copyright notice and this permission notice shall be\n// included in all copies or substantial portions of the Software.\n\n// THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\n// EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n// IN NO EVENT SHALL THE AUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY\n// CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n// TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n// SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type ws from 'next/dist/compiled/ws'\nimport type { DevToolsConfig } from '../../next-devtools/dev-overlay/shared'\nimport { isMiddlewareFilename } from '../../build/utils'\nimport type { VersionInfo } from './parse-version-info'\nimport type { HMR_ACTION_TYPES } from './hot-reloader-types'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from './hot-reloader-types'\nimport { devIndicatorServerState } from './dev-indicator-server-state'\n\nfunction isMiddlewareStats(stats: webpack.Stats) {\n  for (const key of stats.compilation.entrypoints.keys()) {\n    if (isMiddlewareFilename(key)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction statsToJson(stats?: webpack.Stats | null) {\n  if (!stats) return {}\n  return stats.toJson({\n    all: false,\n    errors: true,\n    hash: true,\n    warnings: true,\n  })\n}\n\nfunction getStatsForSyncEvent(\n  clientStats: { ts: number; stats: webpack.Stats } | null,\n  serverStats: { ts: number; stats: webpack.Stats } | null\n) {\n  if (!clientStats) return serverStats?.stats\n  if (!serverStats) return clientStats?.stats\n\n  // Prefer the server compiler stats if it has errors.\n  // Otherwise we may end up in a state where the client compilation is the latest but without errors.\n  // This causes the error overlay to not display the build error.\n  if (serverStats.stats.hasErrors()) {\n    return serverStats.stats\n  }\n\n  // Return the latest stats\n  return serverStats.ts > clientStats.ts ? serverStats.stats : clientStats.stats\n}\n\nclass EventStream {\n  clients: Set<ws>\n  constructor() {\n    this.clients = new Set()\n  }\n\n  close() {\n    for (const wsClient of this.clients) {\n      // it's okay to not cleanly close these websocket connections, this is dev\n      wsClient.terminate()\n    }\n    this.clients.clear()\n  }\n\n  handler(client: ws) {\n    this.clients.add(client)\n    client.addEventListener('close', () => {\n      this.clients.delete(client)\n    })\n  }\n\n  publish(payload: any) {\n    for (const wsClient of this.clients) {\n      wsClient.send(JSON.stringify(payload))\n    }\n  }\n}\n\nexport class WebpackHotMiddleware {\n  eventStream: EventStream\n  clientLatestStats: { ts: number; stats: webpack.Stats } | null\n  middlewareLatestStats: { ts: number; stats: webpack.Stats } | null\n  serverLatestStats: { ts: number; stats: webpack.Stats } | null\n  closed: boolean\n  versionInfo: VersionInfo\n  devtoolsFrontendUrl: string | undefined\n  devToolsConfig: DevToolsConfig\n\n  constructor(\n    compilers: webpack.Compiler[],\n    versionInfo: VersionInfo,\n    devtoolsFrontendUrl: string | undefined,\n    devToolsConfig: DevToolsConfig\n  ) {\n    this.eventStream = new EventStream()\n    this.clientLatestStats = null\n    this.middlewareLatestStats = null\n    this.serverLatestStats = null\n    this.closed = false\n    this.versionInfo = versionInfo\n    this.devtoolsFrontendUrl = devtoolsFrontendUrl\n    this.devToolsConfig = devToolsConfig || ({} as DevToolsConfig)\n\n    compilers[0].hooks.invalid.tap(\n      'webpack-hot-middleware',\n      this.onClientInvalid\n    )\n    compilers[0].hooks.done.tap('webpack-hot-middleware', this.onClientDone)\n    compilers[1].hooks.invalid.tap(\n      'webpack-hot-middleware',\n      this.onServerInvalid\n    )\n    compilers[1].hooks.done.tap('webpack-hot-middleware', this.onServerDone)\n    compilers[2].hooks.done.tap('webpack-hot-middleware', this.onEdgeServerDone)\n    compilers[2].hooks.invalid.tap(\n      'webpack-hot-middleware',\n      this.onEdgeServerInvalid\n    )\n  }\n\n  onClientInvalid = () => {\n    if (this.closed || this.serverLatestStats?.stats.hasErrors()) return\n    this.publish({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING,\n    })\n  }\n\n  onClientDone = (statsResult: webpack.Stats) => {\n    this.clientLatestStats = { ts: Date.now(), stats: statsResult }\n    if (this.closed || this.serverLatestStats?.stats.hasErrors()) return\n    this.publishStats(statsResult)\n  }\n\n  onServerInvalid = () => {\n    if (!this.serverLatestStats?.stats.hasErrors()) return\n    this.serverLatestStats = null\n    if (this.clientLatestStats?.stats) {\n      this.publishStats(this.clientLatestStats.stats)\n    }\n  }\n\n  onServerDone = (statsResult: webpack.Stats) => {\n    if (this.closed) return\n    if (statsResult.hasErrors()) {\n      this.serverLatestStats = { ts: Date.now(), stats: statsResult }\n      this.publishStats(statsResult)\n    }\n  }\n\n  onEdgeServerInvalid = () => {\n    if (!this.middlewareLatestStats?.stats.hasErrors()) return\n    this.middlewareLatestStats = null\n    if (this.clientLatestStats?.stats) {\n      this.publishStats(this.clientLatestStats.stats)\n    }\n  }\n\n  onEdgeServerDone = (statsResult: webpack.Stats) => {\n    if (this.closed) return\n    if (!isMiddlewareStats(statsResult)) {\n      this.onServerInvalid()\n      this.onServerDone(statsResult)\n    }\n\n    if (statsResult.hasErrors()) {\n      this.middlewareLatestStats = { ts: Date.now(), stats: statsResult }\n      this.publishStats(statsResult)\n    }\n  }\n\n  public updateDevToolsConfig(newConfig: DevToolsConfig): void {\n    this.devToolsConfig = newConfig\n  }\n\n  /**\n   * To sync we use the most recent stats but also we append middleware\n   * errors. This is because it is possible that middleware fails to compile\n   * and we still want to show the client overlay with the error while\n   * the error page should be rendered just fine.\n   */\n  onHMR = (client: ws) => {\n    if (this.closed) return\n    this.eventStream.handler(client)\n\n    const syncStats = getStatsForSyncEvent(\n      this.clientLatestStats,\n      this.serverLatestStats\n    )\n\n    if (syncStats) {\n      const stats = statsToJson(syncStats)\n      const middlewareStats = statsToJson(this.middlewareLatestStats?.stats)\n\n      if (devIndicatorServerState.disabledUntil < Date.now()) {\n        devIndicatorServerState.disabledUntil = 0\n      }\n\n      this.publish({\n        action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC,\n        hash: stats.hash!,\n        errors: [...(stats.errors || []), ...(middlewareStats.errors || [])],\n        warnings: [\n          ...(stats.warnings || []),\n          ...(middlewareStats.warnings || []),\n        ],\n        versionInfo: this.versionInfo,\n        debug: {\n          devtoolsFrontendUrl: this.devtoolsFrontendUrl,\n        },\n        devIndicator: devIndicatorServerState,\n        devToolsConfig: this.devToolsConfig,\n      })\n    }\n  }\n\n  publishStats = (statsResult: webpack.Stats) => {\n    const stats = statsResult.toJson({\n      all: false,\n      hash: true,\n      warnings: true,\n      errors: true,\n      moduleTrace: true,\n    })\n\n    this.publish({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT,\n      hash: stats.hash!,\n      warnings: stats.warnings || [],\n      errors: stats.errors || [],\n    })\n  }\n\n  publish = (payload: HMR_ACTION_TYPES) => {\n    if (this.closed) return\n    this.eventStream.publish(payload)\n  }\n  close = () => {\n    if (this.closed) return\n    // Can't remove compiler plugins, so we just set a flag and noop if closed\n    // https://github.com/webpack/tapable/issues/32#issuecomment-350644466\n    this.closed = true\n    this.eventStream.close()\n  }\n}\n"], "names": ["WebpackHotMiddleware", "isMiddlewareStats", "stats", "key", "compilation", "entrypoints", "keys", "isMiddlewareFilename", "stats<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON>", "all", "errors", "hash", "warnings", "getStatsForSyncEvent", "clientStats", "serverStats", "hasErrors", "ts", "EventStream", "constructor", "clients", "Set", "close", "wsClient", "terminate", "clear", "handler", "client", "add", "addEventListener", "delete", "publish", "payload", "send", "JSON", "stringify", "compilers", "versionInfo", "devtoolsFrontendUrl", "devToolsConfig", "onClientInvalid", "closed", "serverLatestStats", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "BUILDING", "onClientDone", "statsResult", "clientLatestStats", "Date", "now", "publishStats", "onServerInvalid", "onServerDone", "onEdgeServerInvalid", "middlewareLatestStats", "onEdgeServerDone", "onHMR", "eventStream", "syncStats", "middlewareStats", "devIndicatorServerState", "disabledUntil", "SYNC", "debug", "devIndicator", "moduleTrace", "BUILT", "hooks", "invalid", "tap", "done", "updateDevToolsConfig", "newConfig"], "mappings": "AAAA,iIAAiI;AACjI,yBAAyB;AAEzB,iDAAiD;AAEjD,wEAAwE;AACxE,kEAAkE;AAClE,sEAAsE;AACtE,sEAAsE;AACtE,qEAAqE;AACrE,wEAAwE;AACxE,4BAA4B;AAE5B,iEAAiE;AACjE,kEAAkE;AAElE,kEAAkE;AAClE,qEAAqE;AACrE,yEAAyE;AACzE,uEAAuE;AACvE,uEAAuE;AACvE,oEAAoE;AACpE,yDAAyD;;;;;+BA4E5CA;;;eAAAA;;;uBAxEwB;kCAGO;yCACJ;AAExC,SAASC,kBAAkBC,KAAoB;IAC7C,KAAK,MAAMC,OAAOD,MAAME,WAAW,CAACC,WAAW,CAACC,IAAI,GAAI;QACtD,IAAIC,IAAAA,2BAAoB,EAACJ,MAAM;YAC7B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASK,YAAYN,KAA4B;IAC/C,IAAI,CAACA,OAAO,OAAO,CAAC;IACpB,OAAOA,MAAMO,MAAM,CAAC;QAClBC,KAAK;QACLC,QAAQ;QACRC,MAAM;QACNC,UAAU;IACZ;AACF;AAEA,SAASC,qBACPC,WAAwD,EACxDC,WAAwD;IAExD,IAAI,CAACD,aAAa,OAAOC,+BAAAA,YAAad,KAAK;IAC3C,IAAI,CAACc,aAAa,OAAOD,+BAAAA,YAAab,KAAK;IAE3C,qDAAqD;IACrD,oGAAoG;IACpG,gEAAgE;IAChE,IAAIc,YAAYd,KAAK,CAACe,SAAS,IAAI;QACjC,OAAOD,YAAYd,KAAK;IAC1B;IAEA,0BAA0B;IAC1B,OAAOc,YAAYE,EAAE,GAAGH,YAAYG,EAAE,GAAGF,YAAYd,KAAK,GAAGa,YAAYb,KAAK;AAChF;AAEA,MAAMiB;IAEJC,aAAc;QACZ,IAAI,CAACC,OAAO,GAAG,IAAIC;IACrB;IAEAC,QAAQ;QACN,KAAK,MAAMC,YAAY,IAAI,CAACH,OAAO,CAAE;YACnC,0EAA0E;YAC1EG,SAASC,SAAS;QACpB;QACA,IAAI,CAACJ,OAAO,CAACK,KAAK;IACpB;IAEAC,QAAQC,MAAU,EAAE;QAClB,IAAI,CAACP,OAAO,CAACQ,GAAG,CAACD;QACjBA,OAAOE,gBAAgB,CAAC,SAAS;YAC/B,IAAI,CAACT,OAAO,CAACU,MAAM,CAACH;QACtB;IACF;IAEAI,QAAQC,OAAY,EAAE;QACpB,KAAK,MAAMT,YAAY,IAAI,CAACH,OAAO,CAAE;YACnCG,SAASU,IAAI,CAACC,KAAKC,SAAS,CAACH;QAC/B;IACF;AACF;AAEO,MAAMjC;IAUXoB,YACEiB,SAA6B,EAC7BC,WAAwB,EACxBC,mBAAuC,EACvCC,cAA8B,CAC9B;aA2BFC,kBAAkB;gBACG;YAAnB,IAAI,IAAI,CAACC,MAAM,MAAI,0BAAA,IAAI,CAACC,iBAAiB,qBAAtB,wBAAwBzC,KAAK,CAACe,SAAS,KAAI;YAC9D,IAAI,CAACe,OAAO,CAAC;gBACXY,QAAQC,6CAA2B,CAACC,QAAQ;YAC9C;QACF;aAEAC,eAAe,CAACC;gBAEK;YADnB,IAAI,CAACC,iBAAiB,GAAG;gBAAE/B,IAAIgC,KAAKC,GAAG;gBAAIjD,OAAO8C;YAAY;YAC9D,IAAI,IAAI,CAACN,MAAM,MAAI,0BAAA,IAAI,CAACC,iBAAiB,qBAAtB,wBAAwBzC,KAAK,CAACe,SAAS,KAAI;YAC9D,IAAI,CAACmC,YAAY,CAACJ;QACpB;aAEAK,kBAAkB;gBACX,yBAED;YAFJ,IAAI,GAAC,0BAAA,IAAI,CAACV,iBAAiB,qBAAtB,wBAAwBzC,KAAK,CAACe,SAAS,KAAI;YAChD,IAAI,CAAC0B,iBAAiB,GAAG;YACzB,KAAI,0BAAA,IAAI,CAACM,iBAAiB,qBAAtB,wBAAwB/C,KAAK,EAAE;gBACjC,IAAI,CAACkD,YAAY,CAAC,IAAI,CAACH,iBAAiB,CAAC/C,KAAK;YAChD;QACF;aAEAoD,eAAe,CAACN;YACd,IAAI,IAAI,CAACN,MAAM,EAAE;YACjB,IAAIM,YAAY/B,SAAS,IAAI;gBAC3B,IAAI,CAAC0B,iBAAiB,GAAG;oBAAEzB,IAAIgC,KAAKC,GAAG;oBAAIjD,OAAO8C;gBAAY;gBAC9D,IAAI,CAACI,YAAY,CAACJ;YACpB;QACF;aAEAO,sBAAsB;gBACf,6BAED;YAFJ,IAAI,GAAC,8BAAA,IAAI,CAACC,qBAAqB,qBAA1B,4BAA4BtD,KAAK,CAACe,SAAS,KAAI;YACpD,IAAI,CAACuC,qBAAqB,GAAG;YAC7B,KAAI,0BAAA,IAAI,CAACP,iBAAiB,qBAAtB,wBAAwB/C,KAAK,EAAE;gBACjC,IAAI,CAACkD,YAAY,CAAC,IAAI,CAACH,iBAAiB,CAAC/C,KAAK;YAChD;QACF;aAEAuD,mBAAmB,CAACT;YAClB,IAAI,IAAI,CAACN,MAAM,EAAE;YACjB,IAAI,CAACzC,kBAAkB+C,cAAc;gBACnC,IAAI,CAACK,eAAe;gBACpB,IAAI,CAACC,YAAY,CAACN;YACpB;YAEA,IAAIA,YAAY/B,SAAS,IAAI;gBAC3B,IAAI,CAACuC,qBAAqB,GAAG;oBAAEtC,IAAIgC,KAAKC,GAAG;oBAAIjD,OAAO8C;gBAAY;gBAClE,IAAI,CAACI,YAAY,CAACJ;YACpB;QACF;QAMA;;;;;GAKC,QACDU,QAAQ,CAAC9B;YACP,IAAI,IAAI,CAACc,MAAM,EAAE;YACjB,IAAI,CAACiB,WAAW,CAAChC,OAAO,CAACC;YAEzB,MAAMgC,YAAY9C,qBAChB,IAAI,CAACmC,iBAAiB,EACtB,IAAI,CAACN,iBAAiB;YAGxB,IAAIiB,WAAW;oBAEuB;gBADpC,MAAM1D,QAAQM,YAAYoD;gBAC1B,MAAMC,kBAAkBrD,aAAY,8BAAA,IAAI,CAACgD,qBAAqB,qBAA1B,4BAA4BtD,KAAK;gBAErE,IAAI4D,gDAAuB,CAACC,aAAa,GAAGb,KAAKC,GAAG,IAAI;oBACtDW,gDAAuB,CAACC,aAAa,GAAG;gBAC1C;gBAEA,IAAI,CAAC/B,OAAO,CAAC;oBACXY,QAAQC,6CAA2B,CAACmB,IAAI;oBACxCpD,MAAMV,MAAMU,IAAI;oBAChBD,QAAQ;2BAAKT,MAAMS,MAAM,IAAI,EAAE;2BAAOkD,gBAAgBlD,MAAM,IAAI,EAAE;qBAAE;oBACpEE,UAAU;2BACJX,MAAMW,QAAQ,IAAI,EAAE;2BACpBgD,gBAAgBhD,QAAQ,IAAI,EAAE;qBACnC;oBACDyB,aAAa,IAAI,CAACA,WAAW;oBAC7B2B,OAAO;wBACL1B,qBAAqB,IAAI,CAACA,mBAAmB;oBAC/C;oBACA2B,cAAcJ,gDAAuB;oBACrCtB,gBAAgB,IAAI,CAACA,cAAc;gBACrC;YACF;QACF;aAEAY,eAAe,CAACJ;YACd,MAAM9C,QAAQ8C,YAAYvC,MAAM,CAAC;gBAC/BC,KAAK;gBACLE,MAAM;gBACNC,UAAU;gBACVF,QAAQ;gBACRwD,aAAa;YACf;YAEA,IAAI,CAACnC,OAAO,CAAC;gBACXY,QAAQC,6CAA2B,CAACuB,KAAK;gBACzCxD,MAAMV,MAAMU,IAAI;gBAChBC,UAAUX,MAAMW,QAAQ,IAAI,EAAE;gBAC9BF,QAAQT,MAAMS,MAAM,IAAI,EAAE;YAC5B;QACF;aAEAqB,UAAU,CAACC;YACT,IAAI,IAAI,CAACS,MAAM,EAAE;YACjB,IAAI,CAACiB,WAAW,CAAC3B,OAAO,CAACC;QAC3B;aACAV,QAAQ;YACN,IAAI,IAAI,CAACmB,MAAM,EAAE;YACjB,0EAA0E;YAC1E,sEAAsE;YACtE,IAAI,CAACA,MAAM,GAAG;YACd,IAAI,CAACiB,WAAW,CAACpC,KAAK;QACxB;QApJE,IAAI,CAACoC,WAAW,GAAG,IAAIxC;QACvB,IAAI,CAAC8B,iBAAiB,GAAG;QACzB,IAAI,CAACO,qBAAqB,GAAG;QAC7B,IAAI,CAACb,iBAAiB,GAAG;QACzB,IAAI,CAACD,MAAM,GAAG;QACd,IAAI,CAACJ,WAAW,GAAGA;QACnB,IAAI,CAACC,mBAAmB,GAAGA;QAC3B,IAAI,CAACC,cAAc,GAAGA,kBAAmB,CAAC;QAE1CH,SAAS,CAAC,EAAE,CAACgC,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAAC9B,eAAe;QAEtBJ,SAAS,CAAC,EAAE,CAACgC,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACxB,YAAY;QACvEV,SAAS,CAAC,EAAE,CAACgC,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAAClB,eAAe;QAEtBhB,SAAS,CAAC,EAAE,CAACgC,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACjB,YAAY;QACvEjB,SAAS,CAAC,EAAE,CAACgC,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,0BAA0B,IAAI,CAACd,gBAAgB;QAC3EpB,SAAS,CAAC,EAAE,CAACgC,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,0BACA,IAAI,CAAChB,mBAAmB;IAE5B;IAoDOkB,qBAAqBC,SAAyB,EAAQ;QAC3D,IAAI,CAAClC,cAAc,GAAGkC;IACxB;AAuEF", "ignoreList": [0]}