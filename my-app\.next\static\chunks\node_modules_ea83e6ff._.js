(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/fumadocs-ui/dist/components/dialog/search-default.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fumadocs-ui_dist_components_dialog_search-default_00052974.js",
  "static/chunks/node_modules_fumadocs-ui_dist_components_dialog_search-default_8fe920bb.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-ui/dist/components/dialog/search-default.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/mixedbread-AG5AAOKO.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_aab9bf03._.js",
  "static/chunks/node_modules_fumadocs-core_dist_mixedbread-AG5AAOKO_8fe920bb.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/mixedbread-AG5AAOKO.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/static-IWYDJ3C5.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fumadocs-core_dist_f94f7925._.js",
  "static/chunks/node_modules_fumadocs-core_dist_static-IWYDJ3C5_8fe920bb.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/static-IWYDJ3C5.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fumadocs-core_dist_8e8c6c70._.js",
  "static/chunks/node_modules_fumadocs-core_dist_orama-cloud-BYTAI6QU_8fe920bb.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fumadocs-core_dist_0bfe582d._.js",
  "static/chunks/node_modules_fumadocs-core_dist_algolia-KPRGMSJO_8fe920bb.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_fumadocs-core_dist_fetch-ITPHBPBE_536f4ce3.js",
  "static/chunks/node_modules_fumadocs-core_dist_fetch-ITPHBPBE_8fe920bb.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js [app-client] (ecmascript)");
    });
});
}),
}]);