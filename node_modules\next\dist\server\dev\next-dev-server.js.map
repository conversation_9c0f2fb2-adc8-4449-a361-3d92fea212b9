{"version": 3, "sources": ["../../../src/server/dev/next-dev-server.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON>ponentsR<PERSON>ult, NodeRequestHandler } from '../next-server'\nimport type { LoadComponentsReturnType } from '../load-components'\nimport type { Options as ServerOptions } from '../next-server'\nimport type { Params } from '../request/params'\nimport type { ParsedUrl } from '../../shared/lib/router/utils/parse-url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { MiddlewareRoutingItem } from '../base-server'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { RouteMatcherManager } from '../route-matcher-managers/route-matcher-manager'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  type NextParsedUrlQuery,\n  type NextUrlWithParsedQuery,\n} from '../request-meta'\nimport type { DevBundlerService } from '../lib/dev-bundler-service'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\nimport type { NodeNextResponse, NodeNextRequest } from '../base-http/node'\nimport type { RouteEnsurer } from '../route-matcher-managers/dev-route-matcher-manager'\nimport type { PagesManifest } from '../../build/webpack/plugins/pages-manifest-plugin'\n\nimport * as React from 'react'\nimport fs from 'fs'\nimport { Worker } from 'next/dist/compiled/jest-worker'\nimport { join as pathJoin } from 'path'\nimport { ampValidation } from '../../build/output'\nimport { PUBLIC_DIR_MIDDLEWARE_CONFLICT } from '../../lib/constants'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PAGES_MANIFEST,\n  APP_PATHS_MANIFEST,\n  COMPILER_NAMES,\n  PRERENDER_MANIFEST,\n} from '../../shared/lib/constants'\nimport Server, { WrappedBuildError } from '../next-server'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport { Telemetry } from '../../telemetry/storage'\nimport { type Span, setGlobal, trace } from '../../trace'\nimport { findPageFile } from '../lib/find-page-file'\nimport { getFormattedNodeOptionsWithoutInspect } from '../lib/utils'\nimport { withCoalescedInvoke } from '../../lib/coalesced-function'\nimport { loadDefaultErrorComponents } from '../load-default-error-components'\nimport { DecodeError, MiddlewareNotFoundError } from '../../shared/lib/utils'\nimport * as Log from '../../build/output/log'\nimport isError, { getProperError } from '../../lib/is-error'\nimport { isMiddlewareFile } from '../../build/utils'\nimport { formatServerError } from '../../lib/format-server-error'\nimport { DevRouteMatcherManager } from '../route-matcher-managers/dev-route-matcher-manager'\nimport { DevPagesRouteMatcherProvider } from '../route-matcher-providers/dev/dev-pages-route-matcher-provider'\nimport { DevPagesAPIRouteMatcherProvider } from '../route-matcher-providers/dev/dev-pages-api-route-matcher-provider'\nimport { DevAppPageRouteMatcherProvider } from '../route-matcher-providers/dev/dev-app-page-route-matcher-provider'\nimport { DevAppRouteRouteMatcherProvider } from '../route-matcher-providers/dev/dev-app-route-route-matcher-provider'\nimport { NodeManifestLoader } from '../route-matcher-providers/helpers/manifest-loaders/node-manifest-loader'\nimport { BatchedFileReader } from '../route-matcher-providers/dev/helpers/file-reader/batched-file-reader'\nimport { DefaultFileReader } from '../route-matcher-providers/dev/helpers/file-reader/default-file-reader'\nimport { LRUCache } from '../lib/lru-cache'\nimport { getMiddlewareRouteMatcher } from '../../shared/lib/router/utils/middleware-route-matcher'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { isPostpone } from '../lib/router-utils/is-postpone'\nimport { generateInterceptionRoutesRewrites } from '../../lib/generate-interception-routes-rewrites'\nimport { buildCustomRoute } from '../../lib/build-custom-route'\nimport { decorateServerError } from '../../shared/lib/error-source'\nimport type { ServerOnInstrumentationRequestError } from '../app-render/types'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport { logRequests } from './log-requests'\nimport { FallbackMode, fallbackModeToFallbackField } from '../../lib/fallback'\nimport type { PagesDevOverlayBridgeType } from '../../next-devtools/userspace/pages/pages-dev-overlay-setup'\nimport {\n  ensureInstrumentationRegistered,\n  getInstrumentationModule,\n} from '../lib/router-utils/instrumentation-globals.external'\nimport type { PrerenderManifest } from '../../build'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\nimport type { PrerenderedRoute } from '../../build/static-paths/types'\n\n// Load ReactDevOverlay only when needed\nlet PagesDevOverlayBridgeImpl: PagesDevOverlayBridgeType\nconst ReactDevOverlay: PagesDevOverlayBridgeType = (props) => {\n  if (PagesDevOverlayBridgeImpl === undefined) {\n    PagesDevOverlayBridgeImpl = (\n      require('../../next-devtools/userspace/pages/pages-dev-overlay-setup') as typeof import('../../next-devtools/userspace/pages/pages-dev-overlay-setup')\n    ).PagesDevOverlayBridge\n  }\n  return React.createElement(PagesDevOverlayBridgeImpl, props)\n}\n\nexport interface Options extends ServerOptions {\n  /**\n   * Tells of Next.js is running from the `next dev` command\n   */\n  isNextDevCommand?: boolean\n\n  /**\n   * Interface to the development bundler.\n   */\n  bundlerService: DevBundlerService\n\n  /**\n   * Trace span for server startup.\n   */\n  startServerSpan: Span\n}\n\nexport default class DevServer extends Server {\n  /**\n   * The promise that resolves when the server is ready. When this is unset\n   * the server is ready.\n   */\n  private ready? = new DetachedPromise<void>()\n  protected sortedRoutes?: string[]\n  private pagesDir?: string\n  private appDir?: string\n  private actualMiddlewareFile?: string\n  private actualInstrumentationHookFile?: string\n  private middleware?: MiddlewareRoutingItem\n  private readonly bundlerService: DevBundlerService\n  private staticPathsCache: LRUCache<\n    UnwrapPromise<ReturnType<DevServer['getStaticPaths']>>\n  >\n  private startServerSpan: Span\n  private readonly serverComponentsHmrCache:\n    | ServerComponentsHmrCache\n    | undefined\n\n  protected staticPathsWorker?: { [key: string]: any } & {\n    loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n  }\n\n  private getStaticPathsWorker(): { [key: string]: any } & {\n    loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n  } {\n    const worker = new Worker(require.resolve('./static-paths-worker'), {\n      maxRetries: 1,\n      // For dev server, it's not necessary to spin up too many workers as long as you are not doing a load test.\n      // This helps reusing the memory a lot.\n      numWorkers: 1,\n      enableWorkerThreads: this.nextConfig.experimental.workerThreads,\n      forkOptions: {\n        env: {\n          ...process.env,\n          // discard --inspect/--inspect-brk flags from process.env.NODE_OPTIONS. Otherwise multiple Node.js debuggers\n          // would be started if user launch Next.js in debugging mode. The number of debuggers is linked to\n          // the number of workers Next.js tries to launch. The only worker users are interested in debugging\n          // is the main Next.js one\n          NODE_OPTIONS: getFormattedNodeOptionsWithoutInspect(),\n        },\n      },\n    }) as Worker & {\n      loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n    }\n\n    worker.getStdout().pipe(process.stdout)\n    worker.getStderr().pipe(process.stderr)\n\n    return worker\n  }\n\n  constructor(options: Options) {\n    try {\n      // Increase the number of stack frames on the server\n      Error.stackTraceLimit = 50\n    } catch {}\n    super({ ...options, dev: true })\n    this.bundlerService = options.bundlerService\n    this.startServerSpan =\n      options.startServerSpan ?? trace('start-next-dev-server')\n    this.renderOpts.dev = true\n    this.renderOpts.ErrorDebug = ReactDevOverlay\n    this.staticPathsCache = new LRUCache(\n      // 5MB\n      5 * 1024 * 1024,\n      function length(value) {\n        return JSON.stringify(value.staticPaths)?.length ?? 0\n      }\n    )\n    this.renderOpts.ampSkipValidation =\n      this.nextConfig.experimental?.amp?.skipValidation ?? false\n    this.renderOpts.ampValidator = async (html: string, pathname: string) => {\n      const { getAmpValidatorInstance, getBundledAmpValidatorFilepath } =\n        require('../../export/helpers/get-amp-html-validator') as typeof import('../../export/helpers/get-amp-html-validator')\n\n      const validatorPath =\n        this.nextConfig.experimental?.amp?.validator ||\n        getBundledAmpValidatorFilepath()\n\n      const validator = await getAmpValidatorInstance(validatorPath)\n\n      const result = validator.validateString(html)\n      ampValidation(\n        pathname,\n        result.errors\n          .filter((error) => {\n            if (error.severity === 'ERROR') {\n              // Unclear yet if these actually prevent the page from being indexed by the AMP cache.\n              // These are coming from React so all we can do is ignore them for now.\n\n              // <link rel=\"expect\" blocking=\"render\" />\n              // https://github.com/ampproject/amphtml/issues/40279\n              if (\n                error.code === 'DISALLOWED_ATTR' &&\n                error.params[0] === 'blocking' &&\n                error.params[1] === 'link'\n              ) {\n                return false\n              }\n              // <template> without type\n              // https://github.com/ampproject/amphtml/issues/40280\n              if (\n                error.code === 'MANDATORY_ATTR_MISSING' &&\n                error.params[0] === 'type' &&\n                error.params[1] === 'template'\n              ) {\n                return false\n              }\n              // <template> without type\n              // https://github.com/ampproject/amphtml/issues/40280\n              if (\n                error.code === 'MISSING_REQUIRED_EXTENSION' &&\n                error.params[0] === 'template' &&\n                error.params[1] === 'amp-mustache'\n              ) {\n                return false\n              }\n              return true\n            }\n            return false\n          })\n          .filter((e) => this._filterAmpDevelopmentScript(html, e)),\n        result.errors.filter((e) => e.severity !== 'ERROR')\n      )\n    }\n\n    const { pagesDir, appDir } = findPagesDir(this.dir)\n    this.pagesDir = pagesDir\n    this.appDir = appDir\n\n    if (this.nextConfig.experimental.serverComponentsHmrCache) {\n      this.serverComponentsHmrCache = new LRUCache(\n        this.nextConfig.cacheMaxMemorySize,\n        function length(value) {\n          return JSON.stringify(value).length\n        }\n      )\n    }\n  }\n\n  protected override getServerComponentsHmrCache() {\n    return this.serverComponentsHmrCache\n  }\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    const { pagesDir, appDir } = findPagesDir(this.dir)\n\n    const ensurer: RouteEnsurer = {\n      ensure: async (match, pathname) => {\n        await this.ensurePage({\n          definition: match.definition,\n          page: match.definition.page,\n          clientOnly: false,\n          url: pathname,\n        })\n      },\n    }\n\n    const matchers = new DevRouteMatcherManager(\n      super.getRouteMatchers(),\n      ensurer,\n      this.dir\n    )\n    const extensions = this.nextConfig.pageExtensions\n    const extensionsExpression = new RegExp(`\\\\.(?:${extensions.join('|')})$`)\n\n    // If the pages directory is available, then configure those matchers.\n    if (pagesDir) {\n      const fileReader = new BatchedFileReader(\n        new DefaultFileReader({\n          // Only allow files that have the correct extensions.\n          pathnameFilter: (pathname) => extensionsExpression.test(pathname),\n        })\n      )\n\n      matchers.push(\n        new DevPagesRouteMatcherProvider(\n          pagesDir,\n          extensions,\n          fileReader,\n          this.localeNormalizer\n        )\n      )\n      matchers.push(\n        new DevPagesAPIRouteMatcherProvider(\n          pagesDir,\n          extensions,\n          fileReader,\n          this.localeNormalizer\n        )\n      )\n    }\n\n    if (appDir) {\n      // We create a new file reader for the app directory because we don't want\n      // to include any folders or files starting with an underscore. This will\n      // prevent the reader from wasting time reading files that we know we\n      // don't care about.\n      const fileReader = new BatchedFileReader(\n        new DefaultFileReader({\n          // Ignore any directory prefixed with an underscore.\n          ignorePartFilter: (part) => part.startsWith('_'),\n        })\n      )\n\n      // TODO: Improve passing of \"is running with Turbopack\"\n      const isTurbopack = !!process.env.TURBOPACK\n      matchers.push(\n        new DevAppPageRouteMatcherProvider(\n          appDir,\n          extensions,\n          fileReader,\n          isTurbopack\n        )\n      )\n      matchers.push(\n        new DevAppRouteRouteMatcherProvider(\n          appDir,\n          extensions,\n          fileReader,\n          isTurbopack\n        )\n      )\n    }\n\n    return matchers\n  }\n\n  protected getBuildId(): string {\n    return 'development'\n  }\n\n  protected async prepareImpl(): Promise<void> {\n    setGlobal('distDir', this.distDir)\n    setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n    const telemetry = new Telemetry({ distDir: this.distDir })\n\n    await super.prepareImpl()\n    await this.matchers.reload()\n\n    this.ready?.resolve()\n    this.ready = undefined\n\n    // In dev, this needs to be called after prepare because the build entries won't be known in the constructor\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // This is required by the tracing subsystem.\n    setGlobal('appDir', this.appDir)\n    setGlobal('pagesDir', this.pagesDir)\n    setGlobal('telemetry', telemetry)\n\n    process.on('unhandledRejection', (reason) => {\n      if (isPostpone(reason)) {\n        // React postpones that are unhandled might end up logged here but they're\n        // not really errors. They're just part of rendering.\n        return\n      }\n      this.logErrorWithOriginalStack(reason, 'unhandledRejection')\n    })\n    process.on('uncaughtException', (err) => {\n      this.logErrorWithOriginalStack(err, 'uncaughtException')\n    })\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    let normalizedPath: string\n    try {\n      normalizedPath = normalizePagePath(pathname)\n    } catch (err) {\n      console.error(err)\n      // if normalizing the page fails it means it isn't valid\n      // so it doesn't exist so don't throw and return false\n      // to ensure we return 404 instead of 500\n      return false\n    }\n\n    if (isMiddlewareFile(normalizedPath)) {\n      return findPageFile(\n        this.dir,\n        normalizedPath,\n        this.nextConfig.pageExtensions,\n        false\n      ).then(Boolean)\n    }\n\n    let appFile: string | null = null\n    let pagesFile: string | null = null\n\n    if (this.appDir) {\n      appFile = await findPageFile(\n        this.appDir,\n        normalizedPath + '/page',\n        this.nextConfig.pageExtensions,\n        true\n      )\n    }\n\n    if (this.pagesDir) {\n      pagesFile = await findPageFile(\n        this.pagesDir,\n        normalizedPath,\n        this.nextConfig.pageExtensions,\n        false\n      )\n    }\n    if (appFile && pagesFile) {\n      return false\n    }\n\n    return Boolean(appFile || pagesFile)\n  }\n\n  async runMiddleware(params: {\n    request: NodeNextRequest\n    response: NodeNextResponse\n    parsedUrl: ParsedUrl\n    parsed: UrlWithParsedQuery\n    middlewareList: MiddlewareRoutingItem[]\n  }) {\n    try {\n      const result = await super.runMiddleware({\n        ...params,\n        onWarning: (warn) => {\n          this.logErrorWithOriginalStack(warn, 'warning')\n        },\n      })\n\n      if ('finished' in result) {\n        return result\n      }\n\n      result.waitUntil.catch((error) => {\n        this.logErrorWithOriginalStack(error, 'unhandledRejection')\n      })\n      return result\n    } catch (error) {\n      if (error instanceof DecodeError) {\n        throw error\n      }\n\n      /**\n       * We only log the error when it is not a MiddlewareNotFound error as\n       * in that case we should be already displaying a compilation error\n       * which is what makes the module not found.\n       */\n      if (!(error instanceof MiddlewareNotFoundError)) {\n        this.logErrorWithOriginalStack(error)\n      }\n\n      const err = getProperError(error)\n      decorateServerError(err, COMPILER_NAMES.edgeServer)\n      const { request, response, parsedUrl } = params\n\n      /**\n       * When there is a failure for an internal Next.js request from\n       * middleware we bypass the error without finishing the request\n       * so we can serve the required chunks to render the error.\n       */\n      if (\n        request.url.includes('/_next/static') ||\n        request.url.includes('/__nextjs_original-stack-frame') ||\n        request.url.includes('/__nextjs_source-map') ||\n        request.url.includes('/__nextjs_error_feedback')\n      ) {\n        return { finished: false }\n      }\n\n      response.statusCode = 500\n      await this.renderError(err, request, response, parsedUrl.pathname)\n      return { finished: true }\n    }\n  }\n\n  async runEdgeFunction(params: {\n    req: NodeNextRequest\n    res: NodeNextResponse\n    query: ParsedUrlQuery\n    params: Params | undefined\n    page: string\n    appPaths: string[] | null\n    isAppPath: boolean\n  }) {\n    try {\n      return super.runEdgeFunction({\n        ...params,\n        onError: (err) => this.logErrorWithOriginalStack(err, 'app-dir'),\n        onWarning: (warn) => {\n          this.logErrorWithOriginalStack(warn, 'warning')\n        },\n      })\n    } catch (error) {\n      if (error instanceof DecodeError) {\n        throw error\n      }\n      this.logErrorWithOriginalStack(error, 'warning')\n      const err = getProperError(error)\n      const { req, res, page } = params\n\n      res.statusCode = 500\n      await this.renderError(err, req, res, page)\n      return null\n    }\n  }\n\n  public getRequestHandler(): NodeRequestHandler {\n    const handler = super.getRequestHandler()\n\n    return (req, res, parsedUrl) => {\n      const request = this.normalizeReq(req)\n      const response = this.normalizeRes(res)\n      const loggingConfig = this.nextConfig.logging\n\n      if (loggingConfig !== false) {\n        const start = Date.now()\n        const isMiddlewareRequest = getRequestMeta(req, 'middlewareInvoke')\n\n        if (!isMiddlewareRequest) {\n          response.originalResponse.once('close', () => {\n            // NOTE: The route match is only attached to the request's meta data\n            // after the request handler is created, so we need to check it in the\n            // close handler and not before.\n            const routeMatch = getRequestMeta(req).match\n\n            if (!routeMatch) {\n              return\n            }\n\n            logRequests({\n              request,\n              response,\n              loggingConfig,\n              requestDurationInMs: Date.now() - start,\n            })\n          })\n        }\n      }\n\n      return handler(request, response, parsedUrl)\n    }\n  }\n\n  public async handleRequest(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    const span = trace('handle-request', undefined, { url: req.url })\n    const result = await span.traceAsyncFn(async () => {\n      await this.ready?.promise\n      addRequestMeta(req, 'PagesErrorDebug', this.renderOpts.ErrorDebug)\n      return await super.handleRequest(req, res, parsedUrl)\n    })\n    const memoryUsage = process.memoryUsage()\n    span\n      .traceChild('memory-usage', {\n        url: req.url,\n        'memory.rss': String(memoryUsage.rss),\n        'memory.heapUsed': String(memoryUsage.heapUsed),\n        'memory.heapTotal': String(memoryUsage.heapTotal),\n      })\n      .stop()\n    return result\n  }\n\n  async run(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.ready?.promise\n\n    const { basePath } = this.nextConfig\n    let originalPathname: string | null = null\n\n    // TODO: see if we can remove this in the future\n    if (basePath && pathHasPrefix(parsedUrl.pathname || '/', basePath)) {\n      // strip basePath before handling dev bundles\n      // If replace ends up replacing the full url it'll be `undefined`, meaning we have to default it to `/`\n      originalPathname = parsedUrl.pathname\n      parsedUrl.pathname = removePathPrefix(parsedUrl.pathname || '/', basePath)\n    }\n\n    const { pathname } = parsedUrl\n\n    if (pathname!.startsWith('/_next')) {\n      if (fs.existsSync(pathJoin(this.publicDir, '_next'))) {\n        throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n      }\n    }\n\n    if (originalPathname) {\n      // restore the path before continuing so that custom-routes can accurately determine\n      // if they should match against the basePath or not\n      parsedUrl.pathname = originalPathname\n    }\n    try {\n      return await super.run(req, res, parsedUrl)\n    } catch (error) {\n      const err = getProperError(error)\n      formatServerError(err)\n      this.logErrorWithOriginalStack(err)\n      if (!res.sent) {\n        res.statusCode = 500\n        try {\n          return await this.renderError(err, req, res, pathname!, {\n            __NEXT_PAGE: (isError(err) && err.page) || pathname || '',\n          })\n        } catch (internalErr) {\n          console.error(internalErr)\n          res.body('Internal Server Error').send()\n        }\n      }\n    }\n  }\n\n  protected logErrorWithOriginalStack(\n    err?: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ): void {\n    this.bundlerService.logErrorWithOriginalStack(err, type)\n  }\n\n  protected getPagesManifest(): PagesManifest | undefined {\n    return (\n      NodeManifestLoader.require(\n        pathJoin(this.serverDistDir, PAGES_MANIFEST)\n      ) ?? undefined\n    )\n  }\n\n  protected getAppPathsManifest(): PagesManifest | undefined {\n    if (!this.enabledDirectories.app) return undefined\n\n    return (\n      NodeManifestLoader.require(\n        pathJoin(this.serverDistDir, APP_PATHS_MANIFEST)\n      ) ?? undefined\n    )\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    const rewrites = generateInterceptionRoutesRewrites(\n      Object.keys(this.appPathRoutes ?? {}),\n      this.nextConfig.basePath\n    ).map((route) => new RegExp(buildCustomRoute('rewrite', route).regex))\n\n    if (this.nextConfig.output === 'export' && rewrites.length > 0) {\n      Log.error(\n        'Intercepting routes are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features'\n      )\n\n      process.exit(1)\n    }\n\n    return rewrites ?? []\n  }\n\n  protected async getMiddleware() {\n    // We need to populate the match\n    // field as it isn't serializable\n    if (this.middleware?.match === null) {\n      this.middleware.match = getMiddlewareRouteMatcher(\n        this.middleware.matchers || []\n      )\n    }\n    return this.middleware\n  }\n\n  protected getNextFontManifest() {\n    return undefined\n  }\n\n  protected async hasMiddleware(): Promise<boolean> {\n    return this.hasPage(this.actualMiddlewareFile!)\n  }\n\n  protected async ensureMiddleware(url: string) {\n    return this.ensurePage({\n      page: this.actualMiddlewareFile!,\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  protected async loadInstrumentationModule(): Promise<any> {\n    let instrumentationModule: any\n    if (\n      this.actualInstrumentationHookFile &&\n      (await this.ensurePage({\n        page: this.actualInstrumentationHookFile!,\n        clientOnly: false,\n        definition: undefined,\n      })\n        .then(() => true)\n        .catch(() => false))\n    ) {\n      try {\n        instrumentationModule = await getInstrumentationModule(\n          this.dir,\n          this.nextConfig.distDir\n        )\n      } catch (err: any) {\n        err.message = `An error occurred while loading instrumentation hook: ${err.message}`\n        throw err\n      }\n    }\n    return instrumentationModule\n  }\n\n  protected async runInstrumentationHookIfAvailable() {\n    await ensureInstrumentationRegistered(this.dir, this.nextConfig.distDir)\n  }\n\n  protected async ensureEdgeFunction({\n    page,\n    appPaths,\n    url,\n  }: {\n    page: string\n    appPaths: string[] | null\n    url: string\n  }) {\n    return this.ensurePage({\n      page,\n      appPaths,\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  generateRoutes(_dev?: boolean) {\n    // In development we expose all compiled files for react-error-overlay's line show feature\n    // We use unshift so that we're sure the routes is defined before Next's default routes\n    // routes.unshift({\n    //   match: getPathMatch('/_next/development/:path*'),\n    //   type: 'route',\n    //   name: '_next/development catchall',\n    //   fn: async (req, res, params) => {\n    //     const p = pathJoin(this.distDir, ...(params.path || []))\n    //     await this.serveStatic(req, res, p)\n    //     return {\n    //       finished: true,\n    //     }\n    //   },\n    // })\n  }\n\n  _filterAmpDevelopmentScript(\n    html: string,\n    event: { line: number; col: number; code: string }\n  ): boolean {\n    if (event.code !== 'DISALLOWED_SCRIPT_TAG') {\n      return true\n    }\n\n    const snippetChunks = html.split('\\n')\n\n    let snippet\n    if (\n      !(snippet = html.split('\\n')[event.line - 1]) ||\n      !(snippet = snippet.substring(event.col))\n    ) {\n      return true\n    }\n\n    snippet = snippet + snippetChunks.slice(event.line).join('\\n')\n    snippet = snippet.substring(0, snippet.indexOf('</script>'))\n\n    return !snippet.includes('data-amp-development-mode-only')\n  }\n\n  protected async getStaticPaths({\n    pathname,\n    urlPathname,\n    requestHeaders,\n    page,\n    isAppPath,\n  }: {\n    pathname: string\n    urlPathname: string\n    requestHeaders: IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    prerenderedRoutes?: PrerenderedRoute[]\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // we lazy load the staticPaths to prevent the user\n    // from waiting on them for the page to load in dev mode\n\n    const __getStaticPaths = async () => {\n      const {\n        configFileName,\n        publicRuntimeConfig,\n        serverRuntimeConfig,\n        httpAgentOptions,\n      } = this.nextConfig\n      const { locales, defaultLocale } = this.nextConfig.i18n || {}\n      const staticPathsWorker = this.getStaticPathsWorker()\n\n      try {\n        const pathsResult = await staticPathsWorker.loadStaticPaths({\n          dir: this.dir,\n          distDir: this.distDir,\n          pathname,\n          config: {\n            pprConfig: this.nextConfig.experimental.ppr,\n            configFileName,\n            publicRuntimeConfig,\n            serverRuntimeConfig,\n            cacheComponents: Boolean(\n              this.nextConfig.experimental.cacheComponents\n            ),\n          },\n          httpAgentOptions,\n          locales,\n          defaultLocale,\n          page,\n          isAppPath,\n          requestHeaders,\n          cacheHandler: this.nextConfig.cacheHandler,\n          cacheHandlers: this.nextConfig.experimental.cacheHandlers,\n          cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n          fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n          isrFlushToDisk: this.nextConfig.experimental.isrFlushToDisk,\n          maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n          nextConfigOutput: this.nextConfig.output,\n          buildId: this.buildId,\n          authInterrupts: Boolean(this.nextConfig.experimental.authInterrupts),\n          sriEnabled: Boolean(this.nextConfig.experimental.sri?.algorithm),\n        })\n        return pathsResult\n      } finally {\n        // we don't re-use workers so destroy the used one\n        staticPathsWorker.end()\n      }\n    }\n    const result = this.staticPathsCache.get(pathname)\n\n    const nextInvoke = withCoalescedInvoke(__getStaticPaths)(\n      `staticPaths-${pathname}`,\n      []\n    )\n      .then(async (res) => {\n        const { prerenderedRoutes, fallbackMode: fallback } = res.value\n\n        if (isAppPath) {\n          if (this.nextConfig.output === 'export') {\n            if (!prerenderedRoutes) {\n              throw new Error(\n                `Page \"${page}\" is missing exported function \"generateStaticParams()\", which is required with \"output: export\" config.`\n              )\n            }\n\n            if (\n              !prerenderedRoutes.some((item) => item.pathname === urlPathname)\n            ) {\n              throw new Error(\n                `Page \"${page}\" is missing param \"${pathname}\" in \"generateStaticParams()\", which is required with \"output: export\" config.`\n              )\n            }\n          }\n        }\n\n        if (!isAppPath && this.nextConfig.output === 'export') {\n          if (fallback === FallbackMode.BLOCKING_STATIC_RENDER) {\n            throw new Error(\n              'getStaticPaths with \"fallback: blocking\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n          } else if (fallback === FallbackMode.PRERENDER) {\n            throw new Error(\n              'getStaticPaths with \"fallback: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n          }\n        }\n\n        const value: {\n          staticPaths: string[] | undefined\n          prerenderedRoutes: PrerenderedRoute[] | undefined\n          fallbackMode: FallbackMode | undefined\n        } = {\n          staticPaths: prerenderedRoutes?.map((route) => route.pathname),\n          prerenderedRoutes,\n          fallbackMode: fallback,\n        }\n\n        if (\n          res.value?.fallbackMode !== undefined &&\n          // This matches the hasGenerateStaticParams logic\n          // we do during build\n          (!isAppPath || (prerenderedRoutes && prerenderedRoutes.length > 0))\n        ) {\n          // we write the static paths to partial manifest for\n          // fallback handling inside of entry handler's\n          const rawExistingManifest = await fs.promises.readFile(\n            pathJoin(this.distDir, PRERENDER_MANIFEST),\n            'utf8'\n          )\n          const existingManifest: PrerenderManifest =\n            JSON.parse(rawExistingManifest)\n          for (const staticPath of value.staticPaths || []) {\n            existingManifest.routes[staticPath] = {} as any\n          }\n          existingManifest.dynamicRoutes[pathname] = {\n            dataRoute: null,\n            dataRouteRegex: null,\n            fallback: fallbackModeToFallbackField(res.value.fallbackMode, page),\n            fallbackRevalidate: false,\n            fallbackExpire: undefined,\n            fallbackHeaders: undefined,\n            fallbackStatus: undefined,\n            fallbackRootParams: undefined,\n            fallbackSourceRoute: pathname,\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            routeRegex: getRouteRegex(pathname).re.source,\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            allowHeader: [],\n          }\n\n          const updatedManifest = JSON.stringify(existingManifest)\n\n          if (updatedManifest !== rawExistingManifest) {\n            await fs.promises.writeFile(\n              pathJoin(this.distDir, PRERENDER_MANIFEST),\n              updatedManifest\n            )\n          }\n        }\n        this.staticPathsCache.set(pathname, value)\n        return value\n      })\n      .catch((err) => {\n        this.staticPathsCache.remove(pathname)\n        if (!result) throw err\n        Log.error(`Failed to generate static paths for ${pathname}:`)\n        console.error(err)\n      })\n\n    if (result) {\n      return result\n    }\n    return nextInvoke as NonNullable<typeof result>\n  }\n\n  protected async ensurePage(opts: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    definition: RouteDefinition | undefined\n    url?: string\n  }): Promise<void> {\n    await this.bundlerService.ensurePage(opts)\n  }\n\n  protected async findPageComponents({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    appPaths = null,\n    shouldEnsure,\n    url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    await this.ready?.promise\n\n    const compilationErr = await this.getCompilationError(page)\n    if (compilationErr) {\n      // Wrap build errors so that they don't get logged again\n      throw new WrappedBuildError(compilationErr)\n    }\n    if (shouldEnsure || this.serverOptions.customServer) {\n      await this.ensurePage({\n        page,\n        appPaths,\n        clientOnly: false,\n        definition: undefined,\n        url,\n      })\n    }\n\n    this.nextFontManifest = super.getNextFontManifest()\n\n    return await super.findPageComponents({\n      page,\n      query,\n      params,\n      locale,\n      isAppPath,\n      shouldEnsure,\n      url,\n    })\n  }\n\n  protected async getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    await this.bundlerService.getFallbackErrorComponents(url)\n    return await loadDefaultErrorComponents(this.distDir)\n  }\n\n  async getCompilationError(page: string): Promise<any> {\n    return await this.bundlerService.getCompilationError(page)\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n\n    const err = args[0]\n    this.logErrorWithOriginalStack(err, 'app-dir')\n  }\n}\n"], "names": ["DevServer", "PagesDevOverlayBridgeImpl", "ReactDevOverlay", "props", "undefined", "require", "PagesDevOverlayBridge", "React", "createElement", "Server", "getStaticPathsWorker", "worker", "Worker", "resolve", "maxRetries", "numWorkers", "enableWorkerThreads", "nextConfig", "experimental", "workerThreads", "forkOptions", "env", "process", "NODE_OPTIONS", "getFormattedNodeOptionsWithoutInspect", "getStdout", "pipe", "stdout", "getStderr", "stderr", "constructor", "options", "Error", "stackTraceLimit", "dev", "ready", "Detached<PERSON>romise", "bundlerService", "startServerSpan", "trace", "renderOpts", "ErrorDebug", "staticPathsCache", "L<PERSON><PERSON><PERSON>", "length", "value", "JSON", "stringify", "staticPaths", "ampSkipValidation", "amp", "skipValidation", "ampValidator", "html", "pathname", "getAmpValidatorInstance", "getBundledAmpValidatorFilepath", "validatorPath", "validator", "result", "validateString", "ampValidation", "errors", "filter", "error", "severity", "code", "params", "e", "_filterAmpDevelopmentScript", "pagesDir", "appDir", "findPagesDir", "dir", "serverComponentsHmrCache", "cacheMaxMemorySize", "getServerComponentsHmrCache", "getRouteMatchers", "ensurer", "ensure", "match", "ensurePage", "definition", "page", "clientOnly", "url", "matchers", "DevRouteMatcherManager", "extensions", "pageExtensions", "extensionsExpression", "RegExp", "join", "fileReader", "BatchedFileReader", "DefaultFileReader", "pathnameFilter", "test", "push", "DevPagesRouteMatcherProvider", "localeNormalizer", "DevPagesAPIRouteMatcherProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "startsWith", "isTurbopack", "TURBOPACK", "DevAppPageRouteMatcherProvider", "DevAppRouteRouteMatcherProvider", "getBuildId", "prepareImpl", "setGlobal", "distDir", "PHASE_DEVELOPMENT_SERVER", "telemetry", "Telemetry", "reload", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "on", "reason", "isPostpone", "logErrorWithOriginalStack", "err", "hasPage", "normalizedPath", "normalizePagePath", "console", "isMiddlewareFile", "findPageFile", "then", "Boolean", "appFile", "pagesFile", "runMiddleware", "onWarning", "warn", "waitUntil", "catch", "DecodeError", "MiddlewareNotFoundError", "getProperError", "decorateServerError", "COMPILER_NAMES", "edgeServer", "request", "response", "parsedUrl", "includes", "finished", "statusCode", "renderError", "runEdgeFunction", "onError", "req", "res", "getRequestHandler", "handler", "normalizeReq", "normalizeRes", "loggingConfig", "logging", "start", "Date", "now", "isMiddlewareRequest", "getRequestMeta", "originalResponse", "once", "routeMatch", "logRequests", "requestDurationInMs", "handleRequest", "span", "traceAsyncFn", "promise", "addRequestMeta", "memoryUsage", "<PERSON><PERSON><PERSON><PERSON>", "String", "rss", "heapUsed", "heapTotal", "stop", "run", "basePath", "originalPathname", "pathHasPrefix", "removePathPrefix", "fs", "existsSync", "pathJoin", "publicDir", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "formatServerError", "sent", "__NEXT_PAGE", "isError", "internalErr", "body", "send", "type", "getPagesManifest", "NodeManifestLoader", "serverDistDir", "PAGES_MANIFEST", "getAppPathsManifest", "enabledDirectories", "app", "APP_PATHS_MANIFEST", "rewrites", "generateInterceptionRoutesRewrites", "Object", "keys", "appPathRoutes", "map", "route", "buildCustomRoute", "regex", "output", "Log", "exit", "getMiddleware", "middleware", "getMiddlewareRouteMatcher", "getNextFontManifest", "hasMiddleware", "actualMiddlewareFile", "ensureMiddleware", "loadInstrumentationModule", "instrumentationModule", "actualInstrumentationHookFile", "getInstrumentationModule", "message", "runInstrumentationHookIfAvailable", "ensureInstrumentationRegistered", "ensureEdgeFunction", "appPaths", "generateRoutes", "_dev", "event", "snippetChunks", "split", "snippet", "line", "substring", "col", "slice", "indexOf", "getStaticPaths", "urlPathname", "requestHeaders", "isAppPath", "__getStaticPaths", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "httpAgentOptions", "locales", "defaultLocale", "i18n", "staticPathsWorker", "pathsResult", "loadStaticPaths", "config", "pprConfig", "ppr", "cacheComponents", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheLifeProfiles", "cacheLife", "fetchCacheKeyPrefix", "isrFlushToDisk", "maxMemoryCacheSize", "nextConfigOutput", "buildId", "authInterrupts", "sriEnabled", "sri", "algorithm", "end", "get", "nextInvoke", "withCoalescedInvoke", "prerenderedRoutes", "fallbackMode", "fallback", "some", "item", "FallbackMode", "BLOCKING_STATIC_RENDER", "PRERENDER", "rawExistingManifest", "promises", "readFile", "PRERENDER_MANIFEST", "existingManifest", "parse", "staticPath", "routes", "dynamicRoutes", "dataRoute", "dataRouteRegex", "fallbackModeToFallbackField", "fallbackRevalidate", "fallbackExpire", "fallbackHeaders", "fallback<PERSON><PERSON><PERSON>", "fallbackRootParams", "fallbackSourceRoute", "prefetchDataRoute", "prefetchDataRouteRegex", "routeRegex", "getRouteRegex", "re", "source", "experimentalPPR", "renderingMode", "allow<PERSON>eader", "updatedManifest", "writeFile", "set", "remove", "opts", "findPageComponents", "locale", "query", "shouldEnsure", "compilationErr", "getCompilationError", "WrappedBuildError", "serverOptions", "customServer", "nextFontManifest", "getFallbackErrorComponents", "loadDefaultErrorComponents", "instrumentationOnRequestError", "args"], "mappings": ";;;;+BA4GA;;;eAAqBA;;;6BA7Fd;+DAQgB;2DACR;4BACQ;sBACU;wBACH;2BACiB;8BAClB;4BAOtB;oEACmC;mCACR;+BACJ;kCACG;yBACP;uBACkB;8BACf;uBACyB;mCAClB;4CACO;wBACU;6DAChC;iEACmB;wBACP;mCACC;wCACK;8CACM;iDACG;gDACD;iDACC;oCACb;mCACD;mCACA;0BACT;wCACiB;iCACV;4BACL;oDACwB;kCAClB;6BACG;6BAGR;0BAC8B;gDAKnD;4BAEuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG9B,wCAAwC;AACxC,IAAIC;AACJ,MAAMC,kBAA6C,CAACC;IAClD,IAAIF,8BAA8BG,WAAW;QAC3CH,4BAA4B,AAC1BI,QAAQ,+DACRC,qBAAqB;IACzB;IACA,OAAOC,OAAMC,aAAa,CAACP,2BAA2BE;AACxD;AAmBe,MAAMH,kBAAkBS,mBAAM;IAyBnCC,uBAEN;QACA,MAAMC,SAAS,IAAIC,kBAAM,CAACP,QAAQQ,OAAO,CAAC,0BAA0B;YAClEC,YAAY;YACZ,2GAA2G;YAC3G,uCAAuC;YACvCC,YAAY;YACZC,qBAAqB,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,aAAa;YAC/DC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACd,4GAA4G;oBAC5G,kGAAkG;oBAClG,mGAAmG;oBACnG,0BAA0B;oBAC1BE,cAAcC,IAAAA,4CAAqC;gBACrD;YACF;QACF;QAIAb,OAAOc,SAAS,GAAGC,IAAI,CAACJ,QAAQK,MAAM;QACtChB,OAAOiB,SAAS,GAAGF,IAAI,CAACJ,QAAQO,MAAM;QAEtC,OAAOlB;IACT;IAEAmB,YAAYC,OAAgB,CAAE;YAmB1B,mCAAA;QAlBF,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,OAAM,CAAC;QACT,KAAK,CAAC;YAAE,GAAGF,OAAO;YAAEG,KAAK;QAAK,IA1DhC;;;GAGC,QACOC,QAAS,IAAIC,gCAAe;QAuDlC,IAAI,CAACC,cAAc,GAAGN,QAAQM,cAAc;QAC5C,IAAI,CAACC,eAAe,GAClBP,QAAQO,eAAe,IAAIC,IAAAA,YAAK,EAAC;QACnC,IAAI,CAACC,UAAU,CAACN,GAAG,GAAG;QACtB,IAAI,CAACM,UAAU,CAACC,UAAU,GAAGvC;QAC7B,IAAI,CAACwC,gBAAgB,GAAG,IAAIC,kBAAQ,CAClC,MAAM;QACN,IAAI,OAAO,MACX,SAASC,OAAOC,KAAK;gBACZC;YAAP,OAAOA,EAAAA,kBAAAA,KAAKC,SAAS,CAACF,MAAMG,WAAW,sBAAhCF,gBAAmCF,MAAM,KAAI;QACtD;QAEF,IAAI,CAACJ,UAAU,CAACS,iBAAiB,GAC/B,EAAA,gCAAA,IAAI,CAAChC,UAAU,CAACC,YAAY,sBAA5B,oCAAA,8BAA8BgC,GAAG,qBAAjC,kCAAmCC,cAAc,KAAI;QACvD,IAAI,CAACX,UAAU,CAACY,YAAY,GAAG,OAAOC,MAAcC;gBAKhD,mCAAA;YAJF,MAAM,EAAEC,uBAAuB,EAAEC,8BAA8B,EAAE,GAC/DnD,QAAQ;YAEV,MAAMoD,gBACJ,EAAA,gCAAA,IAAI,CAACxC,UAAU,CAACC,YAAY,sBAA5B,oCAAA,8BAA8BgC,GAAG,qBAAjC,kCAAmCQ,SAAS,KAC5CF;YAEF,MAAME,YAAY,MAAMH,wBAAwBE;YAEhD,MAAME,SAASD,UAAUE,cAAc,CAACP;YACxCQ,IAAAA,qBAAa,EACXP,UACAK,OAAOG,MAAM,CACVC,MAAM,CAAC,CAACC;gBACP,IAAIA,MAAMC,QAAQ,KAAK,SAAS;oBAC9B,sFAAsF;oBACtF,uEAAuE;oBAEvE,0CAA0C;oBAC1C,qDAAqD;oBACrD,IACED,MAAME,IAAI,KAAK,qBACfF,MAAMG,MAAM,CAAC,EAAE,KAAK,cACpBH,MAAMG,MAAM,CAAC,EAAE,KAAK,QACpB;wBACA,OAAO;oBACT;oBACA,0BAA0B;oBAC1B,qDAAqD;oBACrD,IACEH,MAAME,IAAI,KAAK,4BACfF,MAAMG,MAAM,CAAC,EAAE,KAAK,UACpBH,MAAMG,MAAM,CAAC,EAAE,KAAK,YACpB;wBACA,OAAO;oBACT;oBACA,0BAA0B;oBAC1B,qDAAqD;oBACrD,IACEH,MAAME,IAAI,KAAK,gCACfF,MAAMG,MAAM,CAAC,EAAE,KAAK,cACpBH,MAAMG,MAAM,CAAC,EAAE,KAAK,gBACpB;wBACA,OAAO;oBACT;oBACA,OAAO;gBACT;gBACA,OAAO;YACT,GACCJ,MAAM,CAAC,CAACK,IAAM,IAAI,CAACC,2BAA2B,CAAChB,MAAMe,KACxDT,OAAOG,MAAM,CAACC,MAAM,CAAC,CAACK,IAAMA,EAAEH,QAAQ,KAAK;QAE/C;QAEA,MAAM,EAAEK,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAAC,IAAI,CAACC,GAAG;QAClD,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACC,MAAM,GAAGA;QAEd,IAAI,IAAI,CAACtD,UAAU,CAACC,YAAY,CAACwD,wBAAwB,EAAE;YACzD,IAAI,CAACA,wBAAwB,GAAG,IAAI/B,kBAAQ,CAC1C,IAAI,CAAC1B,UAAU,CAAC0D,kBAAkB,EAClC,SAAS/B,OAAOC,KAAK;gBACnB,OAAOC,KAAKC,SAAS,CAACF,OAAOD,MAAM;YACrC;QAEJ;IACF;IAEmBgC,8BAA8B;QAC/C,OAAO,IAAI,CAACF,wBAAwB;IACtC;IAEUG,mBAAwC;QAChD,MAAM,EAAEP,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAAC,IAAI,CAACC,GAAG;QAElD,MAAMK,UAAwB;YAC5BC,QAAQ,OAAOC,OAAO1B;gBACpB,MAAM,IAAI,CAAC2B,UAAU,CAAC;oBACpBC,YAAYF,MAAME,UAAU;oBAC5BC,MAAMH,MAAME,UAAU,CAACC,IAAI;oBAC3BC,YAAY;oBACZC,KAAK/B;gBACP;YACF;QACF;QAEA,MAAMgC,WAAW,IAAIC,8CAAsB,CACzC,KAAK,CAACV,oBACNC,SACA,IAAI,CAACL,GAAG;QAEV,MAAMe,aAAa,IAAI,CAACvE,UAAU,CAACwE,cAAc;QACjD,MAAMC,uBAAuB,IAAIC,OAAO,CAAC,MAAM,EAAEH,WAAWI,IAAI,CAAC,KAAK,EAAE,CAAC;QAEzE,sEAAsE;QACtE,IAAItB,UAAU;YACZ,MAAMuB,aAAa,IAAIC,oCAAiB,CACtC,IAAIC,oCAAiB,CAAC;gBACpB,qDAAqD;gBACrDC,gBAAgB,CAAC1C,WAAaoC,qBAAqBO,IAAI,CAAC3C;YAC1D;YAGFgC,SAASY,IAAI,CACX,IAAIC,0DAA4B,CAC9B7B,UACAkB,YACAK,YACA,IAAI,CAACO,gBAAgB;YAGzBd,SAASY,IAAI,CACX,IAAIG,gEAA+B,CACjC/B,UACAkB,YACAK,YACA,IAAI,CAACO,gBAAgB;QAG3B;QAEA,IAAI7B,QAAQ;YACV,0EAA0E;YAC1E,yEAAyE;YACzE,qEAAqE;YACrE,oBAAoB;YACpB,MAAMsB,aAAa,IAAIC,oCAAiB,CACtC,IAAIC,oCAAiB,CAAC;gBACpB,oDAAoD;gBACpDO,kBAAkB,CAACC,OAASA,KAAKC,UAAU,CAAC;YAC9C;YAGF,uDAAuD;YACvD,MAAMC,cAAc,CAAC,CAACnF,QAAQD,GAAG,CAACqF,SAAS;YAC3CpB,SAASY,IAAI,CACX,IAAIS,8DAA8B,CAChCpC,QACAiB,YACAK,YACAY;YAGJnB,SAASY,IAAI,CACX,IAAIU,gEAA+B,CACjCrC,QACAiB,YACAK,YACAY;QAGN;QAEA,OAAOnB;IACT;IAEUuB,aAAqB;QAC7B,OAAO;IACT;IAEA,MAAgBC,cAA6B;YAS3C;QARAC,IAAAA,gBAAS,EAAC,WAAW,IAAI,CAACC,OAAO;QACjCD,IAAAA,gBAAS,EAAC,SAASE,oCAAwB;QAE3C,MAAMC,YAAY,IAAIC,kBAAS,CAAC;YAAEH,SAAS,IAAI,CAACA,OAAO;QAAC;QAExD,MAAM,KAAK,CAACF;QACZ,MAAM,IAAI,CAACxB,QAAQ,CAAC8B,MAAM;SAE1B,cAAA,IAAI,CAACjF,KAAK,qBAAV,YAAYtB,OAAO;QACnB,IAAI,CAACsB,KAAK,GAAG/B;QAEb,4GAA4G;QAC5G,IAAI,CAACiH,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,6CAA6C;QAC7CP,IAAAA,gBAAS,EAAC,UAAU,IAAI,CAACxC,MAAM;QAC/BwC,IAAAA,gBAAS,EAAC,YAAY,IAAI,CAACzC,QAAQ;QACnCyC,IAAAA,gBAAS,EAAC,aAAaG;QAEvB5F,QAAQiG,EAAE,CAAC,sBAAsB,CAACC;YAChC,IAAIC,IAAAA,sBAAU,EAACD,SAAS;gBACtB,0EAA0E;gBAC1E,qDAAqD;gBACrD;YACF;YACA,IAAI,CAACE,yBAAyB,CAACF,QAAQ;QACzC;QACAlG,QAAQiG,EAAE,CAAC,qBAAqB,CAACI;YAC/B,IAAI,CAACD,yBAAyB,CAACC,KAAK;QACtC;IACF;IAEA,MAAgBC,QAAQtE,QAAgB,EAAoB;QAC1D,IAAIuE;QACJ,IAAI;YACFA,iBAAiBC,IAAAA,oCAAiB,EAACxE;QACrC,EAAE,OAAOqE,KAAK;YACZI,QAAQ/D,KAAK,CAAC2D;YACd,wDAAwD;YACxD,sDAAsD;YACtD,yCAAyC;YACzC,OAAO;QACT;QAEA,IAAIK,IAAAA,wBAAgB,EAACH,iBAAiB;YACpC,OAAOI,IAAAA,0BAAY,EACjB,IAAI,CAACxD,GAAG,EACRoD,gBACA,IAAI,CAAC5G,UAAU,CAACwE,cAAc,EAC9B,OACAyC,IAAI,CAACC;QACT;QAEA,IAAIC,UAAyB;QAC7B,IAAIC,YAA2B;QAE/B,IAAI,IAAI,CAAC9D,MAAM,EAAE;YACf6D,UAAU,MAAMH,IAAAA,0BAAY,EAC1B,IAAI,CAAC1D,MAAM,EACXsD,iBAAiB,SACjB,IAAI,CAAC5G,UAAU,CAACwE,cAAc,EAC9B;QAEJ;QAEA,IAAI,IAAI,CAACnB,QAAQ,EAAE;YACjB+D,YAAY,MAAMJ,IAAAA,0BAAY,EAC5B,IAAI,CAAC3D,QAAQ,EACbuD,gBACA,IAAI,CAAC5G,UAAU,CAACwE,cAAc,EAC9B;QAEJ;QACA,IAAI2C,WAAWC,WAAW;YACxB,OAAO;QACT;QAEA,OAAOF,QAAQC,WAAWC;IAC5B;IAEA,MAAMC,cAAcnE,MAMnB,EAAE;QACD,IAAI;YACF,MAAMR,SAAS,MAAM,KAAK,CAAC2E,cAAc;gBACvC,GAAGnE,MAAM;gBACToE,WAAW,CAACC;oBACV,IAAI,CAACd,yBAAyB,CAACc,MAAM;gBACvC;YACF;YAEA,IAAI,cAAc7E,QAAQ;gBACxB,OAAOA;YACT;YAEAA,OAAO8E,SAAS,CAACC,KAAK,CAAC,CAAC1E;gBACtB,IAAI,CAAC0D,yBAAyB,CAAC1D,OAAO;YACxC;YACA,OAAOL;QACT,EAAE,OAAOK,OAAO;YACd,IAAIA,iBAAiB2E,mBAAW,EAAE;gBAChC,MAAM3E;YACR;YAEA;;;;OAIC,GACD,IAAI,CAAEA,CAAAA,iBAAiB4E,+BAAuB,AAAD,GAAI;gBAC/C,IAAI,CAAClB,yBAAyB,CAAC1D;YACjC;YAEA,MAAM2D,MAAMkB,IAAAA,uBAAc,EAAC7E;YAC3B8E,IAAAA,gCAAmB,EAACnB,KAAKoB,0BAAc,CAACC,UAAU;YAClD,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGhF;YAEzC;;;;OAIC,GACD,IACE8E,QAAQ5D,GAAG,CAAC+D,QAAQ,CAAC,oBACrBH,QAAQ5D,GAAG,CAAC+D,QAAQ,CAAC,qCACrBH,QAAQ5D,GAAG,CAAC+D,QAAQ,CAAC,2BACrBH,QAAQ5D,GAAG,CAAC+D,QAAQ,CAAC,6BACrB;gBACA,OAAO;oBAAEC,UAAU;gBAAM;YAC3B;YAEAH,SAASI,UAAU,GAAG;YACtB,MAAM,IAAI,CAACC,WAAW,CAAC5B,KAAKsB,SAASC,UAAUC,UAAU7F,QAAQ;YACjE,OAAO;gBAAE+F,UAAU;YAAK;QAC1B;IACF;IAEA,MAAMG,gBAAgBrF,MAQrB,EAAE;QACD,IAAI;YACF,OAAO,KAAK,CAACqF,gBAAgB;gBAC3B,GAAGrF,MAAM;gBACTsF,SAAS,CAAC9B,MAAQ,IAAI,CAACD,yBAAyB,CAACC,KAAK;gBACtDY,WAAW,CAACC;oBACV,IAAI,CAACd,yBAAyB,CAACc,MAAM;gBACvC;YACF;QACF,EAAE,OAAOxE,OAAO;YACd,IAAIA,iBAAiB2E,mBAAW,EAAE;gBAChC,MAAM3E;YACR;YACA,IAAI,CAAC0D,yBAAyB,CAAC1D,OAAO;YACtC,MAAM2D,MAAMkB,IAAAA,uBAAc,EAAC7E;YAC3B,MAAM,EAAE0F,GAAG,EAAEC,GAAG,EAAExE,IAAI,EAAE,GAAGhB;YAE3BwF,IAAIL,UAAU,GAAG;YACjB,MAAM,IAAI,CAACC,WAAW,CAAC5B,KAAK+B,KAAKC,KAAKxE;YACtC,OAAO;QACT;IACF;IAEOyE,oBAAwC;QAC7C,MAAMC,UAAU,KAAK,CAACD;QAEtB,OAAO,CAACF,KAAKC,KAAKR;YAChB,MAAMF,UAAU,IAAI,CAACa,YAAY,CAACJ;YAClC,MAAMR,WAAW,IAAI,CAACa,YAAY,CAACJ;YACnC,MAAMK,gBAAgB,IAAI,CAAC/I,UAAU,CAACgJ,OAAO;YAE7C,IAAID,kBAAkB,OAAO;gBAC3B,MAAME,QAAQC,KAAKC,GAAG;gBACtB,MAAMC,sBAAsBC,IAAAA,2BAAc,EAACZ,KAAK;gBAEhD,IAAI,CAACW,qBAAqB;oBACxBnB,SAASqB,gBAAgB,CAACC,IAAI,CAAC,SAAS;wBACtC,oEAAoE;wBACpE,sEAAsE;wBACtE,gCAAgC;wBAChC,MAAMC,aAAaH,IAAAA,2BAAc,EAACZ,KAAK1E,KAAK;wBAE5C,IAAI,CAACyF,YAAY;4BACf;wBACF;wBAEAC,IAAAA,wBAAW,EAAC;4BACVzB;4BACAC;4BACAc;4BACAW,qBAAqBR,KAAKC,GAAG,KAAKF;wBACpC;oBACF;gBACF;YACF;YAEA,OAAOL,QAAQZ,SAASC,UAAUC;QACpC;IACF;IAEA,MAAayB,cACXlB,GAAoB,EACpBC,GAAqB,EACrBR,SAAkC,EACnB;QACf,MAAM0B,OAAOtI,IAAAA,YAAK,EAAC,kBAAkBnC,WAAW;YAAEiF,KAAKqE,IAAIrE,GAAG;QAAC;QAC/D,MAAM1B,SAAS,MAAMkH,KAAKC,YAAY,CAAC;gBAC/B;YAAN,QAAM,cAAA,IAAI,CAAC3I,KAAK,qBAAV,YAAY4I,OAAO;YACzBC,IAAAA,2BAAc,EAACtB,KAAK,mBAAmB,IAAI,CAAClH,UAAU,CAACC,UAAU;YACjE,OAAO,MAAM,KAAK,CAACmI,cAAclB,KAAKC,KAAKR;QAC7C;QACA,MAAM8B,cAAc3J,QAAQ2J,WAAW;QACvCJ,KACGK,UAAU,CAAC,gBAAgB;YAC1B7F,KAAKqE,IAAIrE,GAAG;YACZ,cAAc8F,OAAOF,YAAYG,GAAG;YACpC,mBAAmBD,OAAOF,YAAYI,QAAQ;YAC9C,oBAAoBF,OAAOF,YAAYK,SAAS;QAClD,GACCC,IAAI;QACP,OAAO5H;IACT;IAEA,MAAM6H,IACJ9B,GAAoB,EACpBC,GAAqB,EACrBR,SAA6B,EACd;YACT;QAAN,QAAM,cAAA,IAAI,CAAChH,KAAK,qBAAV,YAAY4I,OAAO;QAEzB,MAAM,EAAEU,QAAQ,EAAE,GAAG,IAAI,CAACxK,UAAU;QACpC,IAAIyK,mBAAkC;QAEtC,gDAAgD;QAChD,IAAID,YAAYE,IAAAA,4BAAa,EAACxC,UAAU7F,QAAQ,IAAI,KAAKmI,WAAW;YAClE,6CAA6C;YAC7C,uGAAuG;YACvGC,mBAAmBvC,UAAU7F,QAAQ;YACrC6F,UAAU7F,QAAQ,GAAGsI,IAAAA,kCAAgB,EAACzC,UAAU7F,QAAQ,IAAI,KAAKmI;QACnE;QAEA,MAAM,EAAEnI,QAAQ,EAAE,GAAG6F;QAErB,IAAI7F,SAAUkD,UAAU,CAAC,WAAW;YAClC,IAAIqF,WAAE,CAACC,UAAU,CAACC,IAAAA,UAAQ,EAAC,IAAI,CAACC,SAAS,EAAE,WAAW;gBACpD,MAAM,qBAAyC,CAAzC,IAAIhK,MAAMiK,yCAA8B,GAAxC,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;QACF;QAEA,IAAIP,kBAAkB;YACpB,oFAAoF;YACpF,mDAAmD;YACnDvC,UAAU7F,QAAQ,GAAGoI;QACvB;QACA,IAAI;YACF,OAAO,MAAM,KAAK,CAACF,IAAI9B,KAAKC,KAAKR;QACnC,EAAE,OAAOnF,OAAO;YACd,MAAM2D,MAAMkB,IAAAA,uBAAc,EAAC7E;YAC3BkI,IAAAA,oCAAiB,EAACvE;YAClB,IAAI,CAACD,yBAAyB,CAACC;YAC/B,IAAI,CAACgC,IAAIwC,IAAI,EAAE;gBACbxC,IAAIL,UAAU,GAAG;gBACjB,IAAI;oBACF,OAAO,MAAM,IAAI,CAACC,WAAW,CAAC5B,KAAK+B,KAAKC,KAAKrG,UAAW;wBACtD8I,aAAa,AAACC,IAAAA,gBAAO,EAAC1E,QAAQA,IAAIxC,IAAI,IAAK7B,YAAY;oBACzD;gBACF,EAAE,OAAOgJ,aAAa;oBACpBvE,QAAQ/D,KAAK,CAACsI;oBACd3C,IAAI4C,IAAI,CAAC,yBAAyBC,IAAI;gBACxC;YACF;QACF;IACF;IAEU9E,0BACRC,GAAa,EACb8E,IAAyE,EACnE;QACN,IAAI,CAACpK,cAAc,CAACqF,yBAAyB,CAACC,KAAK8E;IACrD;IAEUC,mBAA8C;QACtD,OACEC,sCAAkB,CAACtM,OAAO,CACxB0L,IAAAA,UAAQ,EAAC,IAAI,CAACa,aAAa,EAAEC,0BAAc,MACxCzM;IAET;IAEU0M,sBAAiD;QACzD,IAAI,CAAC,IAAI,CAACC,kBAAkB,CAACC,GAAG,EAAE,OAAO5M;QAEzC,OACEuM,sCAAkB,CAACtM,OAAO,CACxB0L,IAAAA,UAAQ,EAAC,IAAI,CAACa,aAAa,EAAEK,8BAAkB,MAC5C7M;IAET;IAEUkH,+BAAyC;QACjD,MAAM4F,WAAWC,IAAAA,sEAAkC,EACjDC,OAAOC,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,CAAC,IACnC,IAAI,CAACrM,UAAU,CAACwK,QAAQ,EACxB8B,GAAG,CAAC,CAACC,QAAU,IAAI7H,OAAO8H,IAAAA,kCAAgB,EAAC,WAAWD,OAAOE,KAAK;QAEpE,IAAI,IAAI,CAACzM,UAAU,CAAC0M,MAAM,KAAK,YAAYT,SAAStK,MAAM,GAAG,GAAG;YAC9DgL,KAAI5J,KAAK,CACP;YAGF1C,QAAQuM,IAAI,CAAC;QACf;QAEA,OAAOX,YAAY,EAAE;IACvB;IAEA,MAAgBY,gBAAgB;YAG1B;QAFJ,gCAAgC;QAChC,iCAAiC;QACjC,IAAI,EAAA,mBAAA,IAAI,CAACC,UAAU,qBAAf,iBAAiB/I,KAAK,MAAK,MAAM;YACnC,IAAI,CAAC+I,UAAU,CAAC/I,KAAK,GAAGgJ,IAAAA,iDAAyB,EAC/C,IAAI,CAACD,UAAU,CAACzI,QAAQ,IAAI,EAAE;QAElC;QACA,OAAO,IAAI,CAACyI,UAAU;IACxB;IAEUE,sBAAsB;QAC9B,OAAO7N;IACT;IAEA,MAAgB8N,gBAAkC;QAChD,OAAO,IAAI,CAACtG,OAAO,CAAC,IAAI,CAACuG,oBAAoB;IAC/C;IAEA,MAAgBC,iBAAiB/I,GAAW,EAAE;QAC5C,OAAO,IAAI,CAACJ,UAAU,CAAC;YACrBE,MAAM,IAAI,CAACgJ,oBAAoB;YAC/B/I,YAAY;YACZF,YAAY9E;YACZiF;QACF;IACF;IAEA,MAAgBgJ,4BAA0C;QACxD,IAAIC;QACJ,IACE,IAAI,CAACC,6BAA6B,IACjC,MAAM,IAAI,CAACtJ,UAAU,CAAC;YACrBE,MAAM,IAAI,CAACoJ,6BAA6B;YACxCnJ,YAAY;YACZF,YAAY9E;QACd,GACG8H,IAAI,CAAC,IAAM,MACXQ,KAAK,CAAC,IAAM,QACf;YACA,IAAI;gBACF4F,wBAAwB,MAAME,IAAAA,wDAAwB,EACpD,IAAI,CAAC/J,GAAG,EACR,IAAI,CAACxD,UAAU,CAAC+F,OAAO;YAE3B,EAAE,OAAOW,KAAU;gBACjBA,IAAI8G,OAAO,GAAG,CAAC,sDAAsD,EAAE9G,IAAI8G,OAAO,EAAE;gBACpF,MAAM9G;YACR;QACF;QACA,OAAO2G;IACT;IAEA,MAAgBI,oCAAoC;QAClD,MAAMC,IAAAA,+DAA+B,EAAC,IAAI,CAAClK,GAAG,EAAE,IAAI,CAACxD,UAAU,CAAC+F,OAAO;IACzE;IAEA,MAAgB4H,mBAAmB,EACjCzJ,IAAI,EACJ0J,QAAQ,EACRxJ,GAAG,EAKJ,EAAE;QACD,OAAO,IAAI,CAACJ,UAAU,CAAC;YACrBE;YACA0J;YACAzJ,YAAY;YACZF,YAAY9E;YACZiF;QACF;IACF;IAEAyJ,eAAeC,IAAc,EAAE;IAC7B,0FAA0F;IAC1F,uFAAuF;IACvF,mBAAmB;IACnB,sDAAsD;IACtD,mBAAmB;IACnB,wCAAwC;IACxC,sCAAsC;IACtC,+DAA+D;IAC/D,0CAA0C;IAC1C,eAAe;IACf,wBAAwB;IACxB,QAAQ;IACR,OAAO;IACP,KAAK;IACP;IAEA1K,4BACEhB,IAAY,EACZ2L,KAAkD,EACzC;QACT,IAAIA,MAAM9K,IAAI,KAAK,yBAAyB;YAC1C,OAAO;QACT;QAEA,MAAM+K,gBAAgB5L,KAAK6L,KAAK,CAAC;QAEjC,IAAIC;QACJ,IACE,CAAEA,CAAAA,UAAU9L,KAAK6L,KAAK,CAAC,KAAK,CAACF,MAAMI,IAAI,GAAG,EAAE,AAAD,KAC3C,CAAED,CAAAA,UAAUA,QAAQE,SAAS,CAACL,MAAMM,GAAG,CAAA,GACvC;YACA,OAAO;QACT;QAEAH,UAAUA,UAAUF,cAAcM,KAAK,CAACP,MAAMI,IAAI,EAAExJ,IAAI,CAAC;QACzDuJ,UAAUA,QAAQE,SAAS,CAAC,GAAGF,QAAQK,OAAO,CAAC;QAE/C,OAAO,CAACL,QAAQ/F,QAAQ,CAAC;IAC3B;IAEA,MAAgBqG,eAAe,EAC7BnM,QAAQ,EACRoM,WAAW,EACXC,cAAc,EACdxK,IAAI,EACJyK,SAAS,EAOV,EAIE;QACD,mDAAmD;QACnD,wDAAwD;QAExD,MAAMC,mBAAmB;YACvB,MAAM,EACJC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EACjB,GAAG,IAAI,CAAChP,UAAU;YACnB,MAAM,EAAEiP,OAAO,EAAEC,aAAa,EAAE,GAAG,IAAI,CAAClP,UAAU,CAACmP,IAAI,IAAI,CAAC;YAC5D,MAAMC,oBAAoB,IAAI,CAAC3P,oBAAoB;YAEnD,IAAI;oBA6BoB;gBA5BtB,MAAM4P,cAAc,MAAMD,kBAAkBE,eAAe,CAAC;oBAC1D9L,KAAK,IAAI,CAACA,GAAG;oBACbuC,SAAS,IAAI,CAACA,OAAO;oBACrB1D;oBACAkN,QAAQ;wBACNC,WAAW,IAAI,CAACxP,UAAU,CAACC,YAAY,CAACwP,GAAG;wBAC3CZ;wBACAC;wBACAC;wBACAW,iBAAiBxI,QACf,IAAI,CAAClH,UAAU,CAACC,YAAY,CAACyP,eAAe;oBAEhD;oBACAV;oBACAC;oBACAC;oBACAhL;oBACAyK;oBACAD;oBACAiB,cAAc,IAAI,CAAC3P,UAAU,CAAC2P,YAAY;oBAC1CC,eAAe,IAAI,CAAC5P,UAAU,CAACC,YAAY,CAAC2P,aAAa;oBACzDC,mBAAmB,IAAI,CAAC7P,UAAU,CAACC,YAAY,CAAC6P,SAAS;oBACzDC,qBAAqB,IAAI,CAAC/P,UAAU,CAACC,YAAY,CAAC8P,mBAAmB;oBACrEC,gBAAgB,IAAI,CAAChQ,UAAU,CAACC,YAAY,CAAC+P,cAAc;oBAC3DC,oBAAoB,IAAI,CAACjQ,UAAU,CAAC0D,kBAAkB;oBACtDwM,kBAAkB,IAAI,CAAClQ,UAAU,CAAC0M,MAAM;oBACxCyD,SAAS,IAAI,CAACA,OAAO;oBACrBC,gBAAgBlJ,QAAQ,IAAI,CAAClH,UAAU,CAACC,YAAY,CAACmQ,cAAc;oBACnEC,YAAYnJ,SAAQ,oCAAA,IAAI,CAAClH,UAAU,CAACC,YAAY,CAACqQ,GAAG,qBAAhC,kCAAkCC,SAAS;gBACjE;gBACA,OAAOlB;YACT,SAAU;gBACR,kDAAkD;gBAClDD,kBAAkBoB,GAAG;YACvB;QACF;QACA,MAAM9N,SAAS,IAAI,CAACjB,gBAAgB,CAACgP,GAAG,CAACpO;QAEzC,MAAMqO,aAAaC,IAAAA,sCAAmB,EAAC/B,kBACrC,CAAC,YAAY,EAAEvM,UAAU,EACzB,EAAE,EAED4E,IAAI,CAAC,OAAOyB;gBA4CTA;YA3CF,MAAM,EAAEkI,iBAAiB,EAAEC,cAAcC,QAAQ,EAAE,GAAGpI,IAAI9G,KAAK;YAE/D,IAAI+M,WAAW;gBACb,IAAI,IAAI,CAAC3O,UAAU,CAAC0M,MAAM,KAAK,UAAU;oBACvC,IAAI,CAACkE,mBAAmB;wBACtB,MAAM,qBAEL,CAFK,IAAI7P,MACR,CAAC,MAAM,EAAEmD,KAAK,wGAAwG,CAAC,GADnH,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IACE,CAAC0M,kBAAkBG,IAAI,CAAC,CAACC,OAASA,KAAK3O,QAAQ,KAAKoM,cACpD;wBACA,MAAM,qBAEL,CAFK,IAAI1N,MACR,CAAC,MAAM,EAAEmD,KAAK,oBAAoB,EAAE7B,SAAS,8EAA8E,CAAC,GADxH,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;YACF;YAEA,IAAI,CAACsM,aAAa,IAAI,CAAC3O,UAAU,CAAC0M,MAAM,KAAK,UAAU;gBACrD,IAAIoE,aAAaG,sBAAY,CAACC,sBAAsB,EAAE;oBACpD,MAAM,qBAEL,CAFK,IAAInQ,MACR,oKADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO,IAAI+P,aAAaG,sBAAY,CAACE,SAAS,EAAE;oBAC9C,MAAM,qBAEL,CAFK,IAAIpQ,MACR,gKADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,MAAMa,QAIF;gBACFG,WAAW,EAAE6O,qCAAAA,kBAAmBtE,GAAG,CAAC,CAACC,QAAUA,MAAMlK,QAAQ;gBAC7DuO;gBACAC,cAAcC;YAChB;YAEA,IACEpI,EAAAA,aAAAA,IAAI9G,KAAK,qBAAT8G,WAAWmI,YAAY,MAAK1R,aAC5B,iDAAiD;YACjD,qBAAqB;YACpB,CAAA,CAACwP,aAAciC,qBAAqBA,kBAAkBjP,MAAM,GAAG,CAAC,GACjE;gBACA,oDAAoD;gBACpD,8CAA8C;gBAC9C,MAAMyP,sBAAsB,MAAMxG,WAAE,CAACyG,QAAQ,CAACC,QAAQ,CACpDxG,IAAAA,UAAQ,EAAC,IAAI,CAAC/E,OAAO,EAAEwL,8BAAkB,GACzC;gBAEF,MAAMC,mBACJ3P,KAAK4P,KAAK,CAACL;gBACb,KAAK,MAAMM,cAAc9P,MAAMG,WAAW,IAAI,EAAE,CAAE;oBAChDyP,iBAAiBG,MAAM,CAACD,WAAW,GAAG,CAAC;gBACzC;gBACAF,iBAAiBI,aAAa,CAACvP,SAAS,GAAG;oBACzCwP,WAAW;oBACXC,gBAAgB;oBAChBhB,UAAUiB,IAAAA,qCAA2B,EAACrJ,IAAI9G,KAAK,CAACiP,YAAY,EAAE3M;oBAC9D8N,oBAAoB;oBACpBC,gBAAgB9S;oBAChB+S,iBAAiB/S;oBACjBgT,gBAAgBhT;oBAChBiT,oBAAoBjT;oBACpBkT,qBAAqBhQ;oBACrBiQ,mBAAmBnT;oBACnBoT,wBAAwBpT;oBACxBqT,YAAYC,IAAAA,yBAAa,EAACpQ,UAAUqQ,EAAE,CAACC,MAAM;oBAC7CC,iBAAiBzT;oBACjB0T,eAAe1T;oBACf2T,aAAa,EAAE;gBACjB;gBAEA,MAAMC,kBAAkBlR,KAAKC,SAAS,CAAC0P;gBAEvC,IAAIuB,oBAAoB3B,qBAAqB;oBAC3C,MAAMxG,WAAE,CAACyG,QAAQ,CAAC2B,SAAS,CACzBlI,IAAAA,UAAQ,EAAC,IAAI,CAAC/E,OAAO,EAAEwL,8BAAkB,GACzCwB;gBAEJ;YACF;YACA,IAAI,CAACtR,gBAAgB,CAACwR,GAAG,CAAC5Q,UAAUT;YACpC,OAAOA;QACT,GACC6F,KAAK,CAAC,CAACf;YACN,IAAI,CAACjF,gBAAgB,CAACyR,MAAM,CAAC7Q;YAC7B,IAAI,CAACK,QAAQ,MAAMgE;YACnBiG,KAAI5J,KAAK,CAAC,CAAC,oCAAoC,EAAEV,SAAS,CAAC,CAAC;YAC5DyE,QAAQ/D,KAAK,CAAC2D;QAChB;QAEF,IAAIhE,QAAQ;YACV,OAAOA;QACT;QACA,OAAOgO;IACT;IAEA,MAAgB1M,WAAWmP,IAM1B,EAAiB;QAChB,MAAM,IAAI,CAAC/R,cAAc,CAAC4C,UAAU,CAACmP;IACvC;IAEA,MAAgBC,mBAAmB,EACjCC,MAAM,EACNnP,IAAI,EACJoP,KAAK,EACLpQ,MAAM,EACNyL,SAAS,EACTf,WAAW,IAAI,EACf2F,YAAY,EACZnP,GAAG,EAWJ,EAAwC;YACjC;QAAN,QAAM,cAAA,IAAI,CAAClD,KAAK,qBAAV,YAAY4I,OAAO;QAEzB,MAAM0J,iBAAiB,MAAM,IAAI,CAACC,mBAAmB,CAACvP;QACtD,IAAIsP,gBAAgB;YAClB,wDAAwD;YACxD,MAAM,IAAIE,6BAAiB,CAACF;QAC9B;QACA,IAAID,gBAAgB,IAAI,CAACI,aAAa,CAACC,YAAY,EAAE;YACnD,MAAM,IAAI,CAAC5P,UAAU,CAAC;gBACpBE;gBACA0J;gBACAzJ,YAAY;gBACZF,YAAY9E;gBACZiF;YACF;QACF;QAEA,IAAI,CAACyP,gBAAgB,GAAG,KAAK,CAAC7G;QAE9B,OAAO,MAAM,KAAK,CAACoG,mBAAmB;YACpClP;YACAoP;YACApQ;YACAmQ;YACA1E;YACA4E;YACAnP;QACF;IACF;IAEA,MAAgB0P,2BACd1P,GAAY,EAC8B;QAC1C,MAAM,IAAI,CAAChD,cAAc,CAAC0S,0BAA0B,CAAC1P;QACrD,OAAO,MAAM2P,IAAAA,sDAA0B,EAAC,IAAI,CAAChO,OAAO;IACtD;IAEA,MAAM0N,oBAAoBvP,IAAY,EAAgB;QACpD,OAAO,MAAM,IAAI,CAAC9C,cAAc,CAACqS,mBAAmB,CAACvP;IACvD;IAEA,MAAgB8P,8BACd,GAAGC,IAAqD,EACxD;QACA,MAAM,KAAK,CAACD,iCAAiCC;QAE7C,MAAMvN,MAAMuN,IAAI,CAAC,EAAE;QACnB,IAAI,CAACxN,yBAAyB,CAACC,KAAK;IACtC;AACF", "ignoreList": [0]}