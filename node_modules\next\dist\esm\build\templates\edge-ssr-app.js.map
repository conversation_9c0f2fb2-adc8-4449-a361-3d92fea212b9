{"version": 3, "sources": ["../../../src/build/templates/edge-ssr-app.ts"], "sourcesContent": ["import '../../server/web/globals'\nimport { adapter, type NextRequestHint } from '../../server/web/adapter'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\n\nimport * as pageMod from 'VAR_USERLAND'\n\nimport type { RequestData } from '../../server/web/types'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { initializeCacheHandlers } from '../../server/use-cache/handlers'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { WebNextRequest, WebNextResponse } from '../../server/base-http/web'\nimport type { NextFetchEvent } from '../../server/web/spec-extension/fetch-event'\nimport type {\n  AppPageRouteHandlerContext,\n  AppPageRouteModule,\n} from '../../server/route-modules/app-page/module.compiled'\nimport type { AppPageRenderResultMetadata } from '../../server/render-result'\nimport type RenderResult from '../../server/render-result'\nimport { getIsPossibleServerAction } from '../../server/lib/server-action-request-meta'\nimport { getBotType } from '../../shared/lib/router/utils/is-bot'\nimport { interopDefault } from '../../lib/interop-default'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { checkIsOnDemandRevalidate } from '../../server/api-utils'\nimport { CloseController } from '../../server/web/web-on-close'\n\ndeclare const incrementalCacheHandler: any\n// OPTIONAL_IMPORT:incrementalCacheHandler\n\n// Initialize the cache handlers interface.\ninitializeCacheHandlers()\n\n// injected by the loader afterwards.\ndeclare const nextConfig: NextConfigComplete\n// INJECT:nextConfig\n\nconst maybeJSONParse = (str?: string) => (str ? JSON.parse(str) : undefined)\n\nconst rscManifest = self.__RSC_MANIFEST?.['VAR_PAGE']\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST)\n\nif (rscManifest && rscServerManifest) {\n  setReferenceManifestsSingleton({\n    page: 'VAR_PAGE',\n    clientReferenceManifest: rscManifest,\n    serverActionsManifest: rscServerManifest,\n    serverModuleMap: createServerModuleMap({\n      serverActionsManifest: rscServerManifest,\n    }),\n  })\n}\n\nexport const ComponentMod = pageMod\n\nasync function requestHandler(\n  req: NextRequestHint,\n  event: NextFetchEvent\n): Promise<Response> {\n  let srcPage = 'VAR_PAGE'\n\n  const normalizedSrcPage = normalizeAppPath(srcPage)\n  const relativeUrl = `${req.nextUrl.pathname}${req.nextUrl.search}`\n  const baseReq = new WebNextRequest(req)\n  const baseRes = new WebNextResponse(undefined)\n\n  const pageRouteModule = pageMod.routeModule as AppPageRouteModule\n  const prepareResult = await pageRouteModule.prepare(baseReq, null, {\n    srcPage,\n    multiZoneDraftMode: false,\n  })\n\n  if (!prepareResult) {\n    return new Response('Bad Request', {\n      status: 400,\n    })\n  }\n  const {\n    query,\n    params,\n    buildId,\n    buildManifest,\n    prerenderManifest,\n    reactLoadableManifest,\n    clientReferenceManifest,\n    subresourceIntegrityManifest,\n    dynamicCssManifest,\n    nextFontManifest,\n    resolvedPathname,\n    serverActionsManifest,\n    interceptionRoutePatterns,\n    routerServerContext,\n  } = prepareResult\n\n  const isPossibleServerAction = getIsPossibleServerAction(req)\n  const botType = getBotType(req.headers.get('User-Agent') || '')\n  const { isOnDemandRevalidate } = checkIsOnDemandRevalidate(\n    req,\n    prerenderManifest.preview\n  )\n\n  const closeController = new CloseController()\n\n  const renderContext: AppPageRouteHandlerContext = {\n    page: normalizedSrcPage,\n    query,\n    params,\n\n    sharedContext: {\n      buildId,\n    },\n    fallbackRouteParams: null,\n\n    renderOpts: {\n      App: () => null,\n      Document: () => null,\n      pageConfig: {},\n      ComponentMod,\n      Component: interopDefault(ComponentMod),\n      routeModule: pageRouteModule,\n\n      params,\n      page: srcPage,\n      postponed: undefined,\n      shouldWaitOnAllReady: false,\n      serveStreamingMetadata: true,\n      supportsDynamicResponse: true,\n      buildManifest,\n      nextFontManifest,\n      reactLoadableManifest,\n      subresourceIntegrityManifest,\n      dynamicCssManifest,\n      serverActionsManifest,\n      clientReferenceManifest,\n      setIsrStatus: routerServerContext?.setIsrStatus,\n\n      dir: pageRouteModule.relativeProjectDir,\n      botType,\n      isDraftMode: false,\n      isRevalidate: false,\n      isOnDemandRevalidate,\n      isPossibleServerAction,\n      assetPrefix: nextConfig.assetPrefix,\n      nextConfigOutput: nextConfig.output,\n      crossOrigin: nextConfig.crossOrigin,\n      trailingSlash: nextConfig.trailingSlash,\n      previewProps: prerenderManifest.preview,\n      deploymentId: nextConfig.deploymentId,\n      enableTainting: nextConfig.experimental.taint,\n      htmlLimitedBots: nextConfig.htmlLimitedBots,\n      devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n      reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n\n      multiZoneDraftMode: false,\n      cacheLifeProfiles: nextConfig.experimental.cacheLife,\n      basePath: nextConfig.basePath,\n      serverActions: nextConfig.experimental.serverActions,\n\n      experimental: {\n        isRoutePPREnabled: false,\n        expireTime: nextConfig.expireTime,\n        staleTimes: nextConfig.experimental.staleTimes,\n        cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n        clientParamParsing: Boolean(nextConfig.experimental.clientParamParsing),\n        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n        clientTraceMetadata:\n          nextConfig.experimental.clientTraceMetadata || ([] as any),\n      },\n\n      incrementalCache: await pageRouteModule.getIncrementalCache(\n        baseReq,\n        nextConfig,\n        prerenderManifest\n      ),\n\n      waitUntil: event.waitUntil.bind(event),\n      onClose: (cb) => {\n        closeController.onClose(cb)\n      },\n      onAfterTaskError: () => {},\n\n      onInstrumentationRequestError: (error, _request, errorContext) =>\n        pageRouteModule.onRequestError(\n          baseReq,\n          error,\n          errorContext,\n          routerServerContext\n        ),\n      dev: pageRouteModule.isDev,\n    },\n  }\n  let finalStatus = 200\n\n  const renderResultToResponse = (\n    result: RenderResult<AppPageRenderResultMetadata>\n  ): Response => {\n    const varyHeader = pageRouteModule.getVaryHeader(\n      resolvedPathname,\n      interceptionRoutePatterns\n    )\n    // Handle null responses\n    if (result.isNull) {\n      finalStatus = 500\n      closeController.dispatchClose()\n      return new Response(null, { status: 500 })\n    }\n\n    // Extract metadata\n    const { metadata } = result\n    const headers = new Headers()\n    finalStatus = metadata.statusCode || baseRes.statusCode || 200\n    // Pull any fetch metrics from the render onto the request.\n    ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n    // Set content type\n    const contentType = result.contentType || 'text/html; charset=utf-8'\n    headers.set('Content-Type', contentType)\n    headers.set('x-edge-runtime', '1')\n\n    if (varyHeader) {\n      headers.set('Vary', varyHeader)\n    }\n\n    // Add existing headers\n    for (const [key, value] of Object.entries({\n      ...baseRes.getHeaders(),\n      ...metadata.headers,\n    })) {\n      if (value !== undefined) {\n        if (Array.isArray(value)) {\n          // Handle multiple header values\n          for (const v of value) {\n            headers.append(key, String(v))\n          }\n        } else {\n          headers.set(key, String(value))\n        }\n      }\n    }\n\n    // Handle static response\n    if (!result.isDynamic) {\n      const body = result.toUnchunkedString()\n      headers.set(\n        'Content-Length',\n        String(new TextEncoder().encode(body).length)\n      )\n      closeController.dispatchClose()\n      return new Response(body, {\n        status: finalStatus,\n        headers,\n      })\n    }\n\n    // Handle dynamic/streaming response\n    // For edge runtime, we need to create a readable stream that pipes from the result\n    const { readable, writable } = new TransformStream()\n\n    // Start piping the result to the writable stream\n    // This is done asynchronously to avoid blocking the response creation\n    result\n      .pipeTo(writable)\n      .catch((err: unknown) => {\n        console.error('Error piping RenderResult to response:', err)\n      })\n      .finally(() => closeController.dispatchClose())\n\n    return new Response(readable, {\n      status: finalStatus,\n      headers,\n    })\n  }\n\n  const invokeRender = async (span?: Span): Promise<Response> => {\n    try {\n      const result = await pageRouteModule\n        .render(baseReq, baseRes, renderContext)\n        .finally(() => {\n          if (!span) return\n\n          span.setAttributes({\n            'http.status_code': finalStatus,\n            'next.rsc': false,\n          })\n\n          const rootSpanAttributes = tracer.getRootSpanAttributes()\n          // We were unable to get attributes, probably OTEL is not enabled\n          if (!rootSpanAttributes) {\n            return\n          }\n\n          if (\n            rootSpanAttributes.get('next.span_type') !==\n            BaseServerSpan.handleRequest\n          ) {\n            console.warn(\n              `Unexpected root span type '${rootSpanAttributes.get(\n                'next.span_type'\n              )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n            )\n            return\n          }\n\n          const route = normalizedSrcPage\n          if (route) {\n            const name = `${req.method} ${route}`\n\n            span.setAttributes({\n              'next.route': route,\n              'http.route': route,\n              'next.span_name': name,\n            })\n            span.updateName(name)\n          } else {\n            span.updateName(`${req.method} ${relativeUrl}`)\n          }\n        })\n\n      return renderResultToResponse(result)\n    } catch (err) {\n      await pageRouteModule.onRequestError(baseReq, err, {\n        routerKind: 'App Router',\n        routePath: normalizedSrcPage,\n        routeType: 'render',\n        revalidateReason: undefined,\n      })\n      // rethrow so that we can handle serving error page\n      throw err\n    }\n  }\n\n  const tracer = getTracer()\n\n  return tracer.withPropagatedContext(req.headers, () =>\n    tracer.trace(\n      BaseServerSpan.handleRequest,\n      {\n        spanName: `${req.method} ${relativeUrl}`,\n        kind: SpanKind.SERVER,\n        attributes: {\n          'http.method': req.method,\n          'http.target': relativeUrl,\n          'http.route': normalizedSrcPage,\n        },\n      },\n      invokeRender\n    )\n  )\n}\n\nexport default function nHandler(opts: { page: string; request: RequestData }) {\n  return adapter({\n    ...opts,\n    IncrementalCache,\n    handler: requestHandler,\n    incrementalCacheHandler,\n  })\n}\n"], "names": ["self", "adapter", "IncrementalCache", "pageMod", "setReferenceManifestsSingleton", "createServerModuleMap", "initializeCacheHandlers", "BaseServerSpan", "getTracer", "SpanKind", "WebNextRequest", "WebNextResponse", "getIsPossibleServerAction", "getBotType", "interopDefault", "normalizeAppPath", "checkIsOnDemandRevalidate", "CloseController", "maybeJSONParse", "str", "JSON", "parse", "undefined", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "ComponentMod", "requestHandler", "req", "event", "srcPage", "normalizedSrcPage", "relativeUrl", "nextUrl", "pathname", "search", "baseReq", "baseRes", "pageRouteModule", "routeModule", "prepareResult", "prepare", "multiZoneDraftMode", "Response", "status", "query", "params", "buildId", "buildManifest", "prerenderManifest", "reactLoadableManifest", "subresourceIntegrityManifest", "dynamicCssManifest", "nextFontManifest", "resolvedPathname", "interceptionRoutePatterns", "routerServerContext", "isPossibleServerAction", "botType", "headers", "get", "isOnDemandRevalidate", "preview", "closeController", "renderContext", "sharedContext", "fallbackRouteParams", "renderOpts", "App", "Document", "pageConfig", "Component", "postponed", "shouldWaitOnAllReady", "serveStreamingMetadata", "supportsDynamicResponse", "setIsrStatus", "dir", "relativeProjectDir", "isDraftMode", "isRevalidate", "assetPrefix", "nextConfig", "nextConfigOutput", "output", "crossOrigin", "trailingSlash", "previewProps", "deploymentId", "enableTainting", "experimental", "taint", "htmlLimitedBots", "devtoolSegmentExplorer", "reactMaxHeadersLength", "cacheLifeProfiles", "cacheLife", "basePath", "serverActions", "isRoutePPREnabled", "expireTime", "staleTimes", "cacheComponents", "Boolean", "clientSegmentCache", "clientParamParsing", "dynamicOnHover", "inlineCss", "authInterrupts", "clientTraceMetadata", "incrementalCache", "getIncrementalCache", "waitUntil", "bind", "onClose", "cb", "onAfterTaskError", "onInstrumentationRequestError", "error", "_request", "errorContext", "onRequestError", "dev", "isDev", "finalStatus", "renderResultToResponse", "result", "<PERSON><PERSON><PERSON><PERSON>", "getVaryHeader", "isNull", "dispatchClose", "metadata", "Headers", "statusCode", "fetchMetrics", "contentType", "set", "key", "value", "Object", "entries", "getHeaders", "Array", "isArray", "v", "append", "String", "isDynamic", "body", "toUnchunkedString", "TextEncoder", "encode", "length", "readable", "writable", "TransformStream", "pipeTo", "catch", "err", "console", "finally", "invokeRender", "span", "render", "setAttributes", "rootSpanAttributes", "tracer", "getRootSpanAttributes", "handleRequest", "warn", "route", "name", "method", "updateName", "routerKind", "routePath", "routeType", "revalidateReason", "withPropagatedContext", "trace", "spanName", "kind", "SERVER", "attributes", "nH<PERSON><PERSON>", "opts", "handler", "incremental<PERSON>ache<PERSON><PERSON><PERSON>"], "mappings": "IAwCoBA;AAxCpB,OAAO,2BAA0B;AACjC,SAASC,OAAO,QAA8B,2BAA0B;AACxE,SAASC,gBAAgB,QAAQ,qCAAoC;AAErE,YAAYC,aAAa,eAAc;AAIvC,SAASC,8BAA8B,QAAQ,2CAA0C;AACzF,SAASC,qBAAqB,QAAQ,uCAAsC;AAC5E,SAASC,uBAAuB,QAAQ,kCAAiC;AACzE,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,SAAS,EAAEC,QAAQ,QAAmB,gCAA+B;AAC9E,SAASC,cAAc,EAAEC,eAAe,QAAQ,6BAA4B;AAQ5E,SAASC,yBAAyB,QAAQ,8CAA6C;AACvF,SAASC,UAAU,QAAQ,uCAAsC;AACjE,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,yBAAyB,QAAQ,yBAAwB;AAClE,SAASC,eAAe,QAAQ,gCAA+B;AAG/D,0CAA0C;AAE1C,2CAA2C;AAC3CX;AAIA,oBAAoB;AAEpB,MAAMY,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,eAAcvB,uBAAAA,KAAKwB,cAAc,qBAAnBxB,oBAAqB,CAAC,WAAW;AACrD,MAAMyB,oBAAoBP,eAAelB,KAAK0B,qBAAqB;AAEnE,IAAIH,eAAeE,mBAAmB;IACpCrB,+BAA+B;QAC7BuB,MAAM;QACNC,yBAAyBL;QACzBM,uBAAuBJ;QACvBK,iBAAiBzB,sBAAsB;YACrCwB,uBAAuBJ;QACzB;IACF;AACF;AAEA,OAAO,MAAMM,eAAe5B,QAAO;AAEnC,eAAe6B,eACbC,GAAoB,EACpBC,KAAqB;IAErB,IAAIC,UAAU;IAEd,MAAMC,oBAAoBrB,iBAAiBoB;IAC3C,MAAME,cAAc,GAAGJ,IAAIK,OAAO,CAACC,QAAQ,GAAGN,IAAIK,OAAO,CAACE,MAAM,EAAE;IAClE,MAAMC,UAAU,IAAI/B,eAAeuB;IACnC,MAAMS,UAAU,IAAI/B,gBAAgBW;IAEpC,MAAMqB,kBAAkBxC,QAAQyC,WAAW;IAC3C,MAAMC,gBAAgB,MAAMF,gBAAgBG,OAAO,CAACL,SAAS,MAAM;QACjEN;QACAY,oBAAoB;IACtB;IAEA,IAAI,CAACF,eAAe;QAClB,OAAO,IAAIG,SAAS,eAAe;YACjCC,QAAQ;QACV;IACF;IACA,MAAM,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,iBAAiB,EACjBC,qBAAqB,EACrB3B,uBAAuB,EACvB4B,4BAA4B,EAC5BC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EAChB9B,qBAAqB,EACrB+B,yBAAyB,EACzBC,mBAAmB,EACpB,GAAGhB;IAEJ,MAAMiB,yBAAyBlD,0BAA0BqB;IACzD,MAAM8B,UAAUlD,WAAWoB,IAAI+B,OAAO,CAACC,GAAG,CAAC,iBAAiB;IAC5D,MAAM,EAAEC,oBAAoB,EAAE,GAAGlD,0BAC/BiB,KACAqB,kBAAkBa,OAAO;IAG3B,MAAMC,kBAAkB,IAAInD;IAE5B,MAAMoD,gBAA4C;QAChD1C,MAAMS;QACNc;QACAC;QAEAmB,eAAe;YACblB;QACF;QACAmB,qBAAqB;QAErBC,YAAY;YACVC,KAAK,IAAM;YACXC,UAAU,IAAM;YAChBC,YAAY,CAAC;YACb5C;YACA6C,WAAW9D,eAAeiB;YAC1Ba,aAAaD;YAEbQ;YACAxB,MAAMQ;YACN0C,WAAWvD;YACXwD,sBAAsB;YACtBC,wBAAwB;YACxBC,yBAAyB;YACzB3B;YACAK;YACAH;YACAC;YACAC;YACA5B;YACAD;YACAqD,YAAY,EAAEpB,uCAAAA,oBAAqBoB,YAAY;YAE/CC,KAAKvC,gBAAgBwC,kBAAkB;YACvCpB;YACAqB,aAAa;YACbC,cAAc;YACdnB;YACAJ;YACAwB,aAAaC,WAAWD,WAAW;YACnCE,kBAAkBD,WAAWE,MAAM;YACnCC,aAAaH,WAAWG,WAAW;YACnCC,eAAeJ,WAAWI,aAAa;YACvCC,cAActC,kBAAkBa,OAAO;YACvC0B,cAAcN,WAAWM,YAAY;YACrCC,gBAAgBP,WAAWQ,YAAY,CAACC,KAAK;YAC7CC,iBAAiBV,WAAWU,eAAe;YAC3CC,wBAAwBX,WAAWQ,YAAY,CAACG,sBAAsB;YACtEC,uBAAuBZ,WAAWY,qBAAqB;YAEvDpD,oBAAoB;YACpBqD,mBAAmBb,WAAWQ,YAAY,CAACM,SAAS;YACpDC,UAAUf,WAAWe,QAAQ;YAC7BC,eAAehB,WAAWQ,YAAY,CAACQ,aAAa;YAEpDR,cAAc;gBACZS,mBAAmB;gBACnBC,YAAYlB,WAAWkB,UAAU;gBACjCC,YAAYnB,WAAWQ,YAAY,CAACW,UAAU;gBAC9CC,iBAAiBC,QAAQrB,WAAWQ,YAAY,CAACY,eAAe;gBAChEE,oBAAoBD,QAAQrB,WAAWQ,YAAY,CAACc,kBAAkB;gBACtEC,oBAAoBF,QAAQrB,WAAWQ,YAAY,CAACe,kBAAkB;gBACtEC,gBAAgBH,QAAQrB,WAAWQ,YAAY,CAACgB,cAAc;gBAC9DC,WAAWJ,QAAQrB,WAAWQ,YAAY,CAACiB,SAAS;gBACpDC,gBAAgBL,QAAQrB,WAAWQ,YAAY,CAACkB,cAAc;gBAC9DC,qBACE3B,WAAWQ,YAAY,CAACmB,mBAAmB,IAAK,EAAE;YACtD;YAEAC,kBAAkB,MAAMxE,gBAAgByE,mBAAmB,CACzD3E,SACA8C,YACAjC;YAGF+D,WAAWnF,MAAMmF,SAAS,CAACC,IAAI,CAACpF;YAChCqF,SAAS,CAACC;gBACRpD,gBAAgBmD,OAAO,CAACC;YAC1B;YACAC,kBAAkB,KAAO;YAEzBC,+BAA+B,CAACC,OAAOC,UAAUC,eAC/ClF,gBAAgBmF,cAAc,CAC5BrF,SACAkF,OACAE,cACAhE;YAEJkE,KAAKpF,gBAAgBqF,KAAK;QAC5B;IACF;IACA,IAAIC,cAAc;IAElB,MAAMC,yBAAyB,CAC7BC;QAEA,MAAMC,aAAazF,gBAAgB0F,aAAa,CAC9C1E,kBACAC;QAEF,wBAAwB;QACxB,IAAIuE,OAAOG,MAAM,EAAE;YACjBL,cAAc;YACd7D,gBAAgBmE,aAAa;YAC7B,OAAO,IAAIvF,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAC1C;QAEA,mBAAmB;QACnB,MAAM,EAAEuF,QAAQ,EAAE,GAAGL;QACrB,MAAMnE,UAAU,IAAIyE;QACpBR,cAAcO,SAASE,UAAU,IAAIhG,QAAQgG,UAAU,IAAI;QAEzDzG,IAAY0G,YAAY,GAAGH,SAASG,YAAY;QAElD,mBAAmB;QACnB,MAAMC,cAAcT,OAAOS,WAAW,IAAI;QAC1C5E,QAAQ6E,GAAG,CAAC,gBAAgBD;QAC5B5E,QAAQ6E,GAAG,CAAC,kBAAkB;QAE9B,IAAIT,YAAY;YACdpE,QAAQ6E,GAAG,CAAC,QAAQT;QACtB;QAEA,uBAAuB;QACvB,KAAK,MAAM,CAACU,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAC;YACxC,GAAGvG,QAAQwG,UAAU,EAAE;YACvB,GAAGV,SAASxE,OAAO;QACrB,GAAI;YACF,IAAI+E,UAAUzH,WAAW;gBACvB,IAAI6H,MAAMC,OAAO,CAACL,QAAQ;oBACxB,gCAAgC;oBAChC,KAAK,MAAMM,KAAKN,MAAO;wBACrB/E,QAAQsF,MAAM,CAACR,KAAKS,OAAOF;oBAC7B;gBACF,OAAO;oBACLrF,QAAQ6E,GAAG,CAACC,KAAKS,OAAOR;gBAC1B;YACF;QACF;QAEA,yBAAyB;QACzB,IAAI,CAACZ,OAAOqB,SAAS,EAAE;YACrB,MAAMC,OAAOtB,OAAOuB,iBAAiB;YACrC1F,QAAQ6E,GAAG,CACT,kBACAU,OAAO,IAAII,cAAcC,MAAM,CAACH,MAAMI,MAAM;YAE9CzF,gBAAgBmE,aAAa;YAC7B,OAAO,IAAIvF,SAASyG,MAAM;gBACxBxG,QAAQgF;gBACRjE;YACF;QACF;QAEA,oCAAoC;QACpC,mFAAmF;QACnF,MAAM,EAAE8F,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;QAEnC,iDAAiD;QACjD,sEAAsE;QACtE7B,OACG8B,MAAM,CAACF,UACPG,KAAK,CAAC,CAACC;YACNC,QAAQzC,KAAK,CAAC,0CAA0CwC;QAC1D,GACCE,OAAO,CAAC,IAAMjG,gBAAgBmE,aAAa;QAE9C,OAAO,IAAIvF,SAAS8G,UAAU;YAC5B7G,QAAQgF;YACRjE;QACF;IACF;IAEA,MAAMsG,eAAe,OAAOC;QAC1B,IAAI;YACF,MAAMpC,SAAS,MAAMxF,gBAClB6H,MAAM,CAAC/H,SAASC,SAAS2B,eACzBgG,OAAO,CAAC;gBACP,IAAI,CAACE,MAAM;gBAEXA,KAAKE,aAAa,CAAC;oBACjB,oBAAoBxC;oBACpB,YAAY;gBACd;gBAEA,MAAMyC,qBAAqBC,OAAOC,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACF,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBzG,GAAG,CAAC,sBACvB1D,eAAesK,aAAa,EAC5B;oBACAT,QAAQU,IAAI,CACV,CAAC,2BAA2B,EAAEJ,mBAAmBzG,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAM8G,QAAQ3I;gBACd,IAAI2I,OAAO;oBACT,MAAMC,OAAO,GAAG/I,IAAIgJ,MAAM,CAAC,CAAC,EAAEF,OAAO;oBAErCR,KAAKE,aAAa,CAAC;wBACjB,cAAcM;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACAT,KAAKW,UAAU,CAACF;gBAClB,OAAO;oBACLT,KAAKW,UAAU,CAAC,GAAGjJ,IAAIgJ,MAAM,CAAC,CAAC,EAAE5I,aAAa;gBAChD;YACF;YAEF,OAAO6F,uBAAuBC;QAChC,EAAE,OAAOgC,KAAK;YACZ,MAAMxH,gBAAgBmF,cAAc,CAACrF,SAAS0H,KAAK;gBACjDgB,YAAY;gBACZC,WAAWhJ;gBACXiJ,WAAW;gBACXC,kBAAkBhK;YACpB;YACA,mDAAmD;YACnD,MAAM6I;QACR;IACF;IAEA,MAAMQ,SAASnK;IAEf,OAAOmK,OAAOY,qBAAqB,CAACtJ,IAAI+B,OAAO,EAAE,IAC/C2G,OAAOa,KAAK,CACVjL,eAAesK,aAAa,EAC5B;YACEY,UAAU,GAAGxJ,IAAIgJ,MAAM,CAAC,CAAC,EAAE5I,aAAa;YACxCqJ,MAAMjL,SAASkL,MAAM;YACrBC,YAAY;gBACV,eAAe3J,IAAIgJ,MAAM;gBACzB,eAAe5I;gBACf,cAAcD;YAChB;QACF,GACAkI;AAGN;AAEA,eAAe,SAASuB,SAASC,IAA4C;IAC3E,OAAO7L,QAAQ;QACb,GAAG6L,IAAI;QACP5L;QACA6L,SAAS/J;QACTgK;IACF;AACF", "ignoreList": [0]}