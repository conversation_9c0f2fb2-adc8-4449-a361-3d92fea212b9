{"version": 3, "sources": ["../../../src/server/app-render/entry-base.ts"], "sourcesContent": ["// eslint-disable-next-line import/no-extraneous-dependencies\nexport {\n  createTemporaryReferenceSet,\n  renderToReadableStream,\n  decodeReply,\n  decodeAction,\n  decodeFormState,\n} from 'react-server-dom-webpack/server'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport { unstable_prerender as prerender } from 'react-server-dom-webpack/static'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport { captureOwnerStack } from 'react'\n\nexport { default as LayoutRouter } from '../../client/components/layout-router'\nexport { default as RenderFromTemplateContext } from '../../client/components/render-from-template-context'\nexport { workAsyncStorage } from '../app-render/work-async-storage.external'\nexport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nexport { actionAsyncStorage } from '../app-render/action-async-storage.external'\n\nexport { ClientPageRoot } from '../../client/components/client-page'\nexport { ClientSegmentRoot } from '../../client/components/client-segment'\nexport {\n  createServerSearchParamsForServerPage,\n  createPrerenderSearchParamsForClientPage,\n} from '../request/search-params'\nexport {\n  createServerParamsForServerSegment,\n  createPrerenderParamsForClientSegment,\n} from '../request/params'\nexport * as serverHooks from '../../client/components/hooks-server-context'\nexport { HTTPAccessFallbackBoundary } from '../../client/components/http-access-fallback/error-boundary'\nexport { createMetadataComponents } from '../../lib/metadata/metadata'\nexport {\n  MetadataBoundary,\n  ViewportBoundary,\n  OutletBoundary,\n  RootLayoutBoundary,\n} from '../../lib/framework/boundary-components'\n\nexport { preloadStyle, preloadFont, preconnect } from './rsc/preloads'\nexport { Postpone } from './rsc/postpone'\nexport { taintObjectReference } from './rsc/taint'\nexport { collectSegmentData } from './collect-segment-data'\n\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { patchFetch as _patchFetch } from '../lib/patch-fetch'\n\nlet SegmentViewNode: typeof import('../../next-devtools/userspace/app/segment-explorer-node').SegmentViewNode =\n  () => null\nlet SegmentViewStateNode: typeof import('../../next-devtools/userspace/app/segment-explorer-node').SegmentViewStateNode =\n  () => null\nif (process.env.NODE_ENV === 'development') {\n  const mod =\n    require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n  SegmentViewNode = mod.SegmentViewNode\n  SegmentViewStateNode = mod.SegmentViewStateNode\n}\n\n// For hot-reloader\ndeclare global {\n  var __next__clear_chunk_cache__: (() => void) | null | undefined\n  var __turbopack_clear_chunk_cache__: () => void | null | undefined\n}\n// hot-reloader modules are not bundled so we need to inject `__next__clear_chunk_cache__`\n// into globalThis from this file which is bundled.\nif (process.env.TURBOPACK) {\n  globalThis.__next__clear_chunk_cache__ = __turbopack_clear_chunk_cache__\n} else {\n  // Webpack does not have chunks on the server\n  globalThis.__next__clear_chunk_cache__ = null\n}\n\n// patchFetch makes use of APIs such as `React.unstable_postpone` which are only available\n// in the experimental channel of React, so export it from here so that it comes from the bundled runtime\nexport function patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\n// Development only\nexport { SegmentViewNode, SegmentViewStateNode }\n"], "names": ["ClientPageRoot", "ClientSegmentRoot", "HTTPAccessFallbackBoundary", "LayoutRouter", "MetadataBoundary", "OutletBoundary", "Postpone", "RenderFromTemplateContext", "RootLayoutBoundary", "SegmentViewNode", "SegmentViewStateNode", "ViewportBoundary", "actionAsyncStorage", "captureOwnerStack", "collectSegmentData", "createMetadataComponents", "createPrerenderParamsForClientSegment", "createPrerenderSearchParamsForClientPage", "createServerParamsForServerSegment", "createServerSearchParamsForServerPage", "createTemporaryReferenceSet", "decodeAction", "decodeFormState", "decodeReply", "patchFetch", "preconnect", "preloadFont", "preloadStyle", "prerender", "unstable_prerender", "renderToReadableStream", "serverHooks", "taintObjectReference", "workAsyncStorage", "workUnitAsyncStorage", "process", "env", "NODE_ENV", "mod", "require", "TURBOPACK", "globalThis", "__next__clear_chunk_cache__", "__turbopack_clear_chunk_cache__", "_patchFetch"], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqBpDA,cAAc;eAAdA,0BAAc;;IACdC,iBAAiB;eAAjBA,gCAAiB;;IAUjBC,0BAA0B;eAA1BA,yCAA0B;;IAjBfC,YAAY;eAAZA,qBAAY;;IAoB9BC,gBAAgB;eAAhBA,oCAAgB;;IAEhBC,cAAc;eAAdA,kCAAc;;IAKPC,QAAQ;eAARA,kBAAQ;;IA1BGC,yBAAyB;eAAzBA,kCAAyB;;IAsB3CC,kBAAkB;eAAlBA,sCAAkB;;IA+CXC,eAAe;eAAfA;;IAAiBC,oBAAoB;eAApBA;;IAjDxBC,gBAAgB;eAAhBA,oCAAgB;;IAjBTC,kBAAkB;eAAlBA,8CAAkB;;IANlBC,iBAAiB;eAAjBA,wBAAiB;;IA+BjBC,kBAAkB;eAAlBA,sCAAkB;;IAXlBC,wBAAwB;eAAxBA,kCAAwB;;IAJ/BC,qCAAqC;eAArCA,6CAAqC;;IAJrCC,wCAAwC;eAAxCA,sDAAwC;;IAGxCC,kCAAkC;eAAlCA,0CAAkC;;IAJlCC,qCAAqC;eAArCA,mDAAqC;;IAtBrCC,2BAA2B;eAA3BA,mCAA2B;;IAG3BC,YAAY;eAAZA,oBAAY;;IACZC,eAAe;eAAfA,uBAAe;;IAFfC,WAAW;eAAXA,mBAAW;;IAyEGC,UAAU;eAAVA;;IApCoBC,UAAU;eAAVA,oBAAU;;IAAvBC,WAAW;eAAXA,qBAAW;;IAAzBC,YAAY;eAAZA,sBAAY;;IA/BUC,SAAS;eAA/BC,0BAAkB;;IAPzBC,sBAAsB;eAAtBA,8BAAsB;;IA4BZC,WAAW;;;IAYdC,oBAAoB;eAApBA,2BAAoB;;IA1BpBC,gBAAgB;eAAhBA,0CAAgB;;IAChBC,oBAAoB;eAApBA,kDAAoB;;;wBAXtB;wBAGyC;uBAGd;qEAEM;kFACa;0CACpB;8CACI;4CACF;4BAEJ;+BACG;8BAI3B;wBAIA;4EACsB;+BACc;0BACF;oCAMlC;0BAE+C;0BAC7B;uBACY;oCACF;4BAIO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1C,IAAIzB,kBACF,IAAM;AACR,IAAIC,uBACF,IAAM;AACR,IAAIyB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,MACJC,QAAQ;IACV9B,kBAAkB6B,IAAI7B,eAAe;IACrCC,uBAAuB4B,IAAI5B,oBAAoB;AACjD;AAOA,0FAA0F;AAC1F,mDAAmD;AACnD,IAAIyB,QAAQC,GAAG,CAACI,SAAS,EAAE;IACzBC,WAAWC,2BAA2B,GAAGC;AAC3C,OAAO;IACL,6CAA6C;IAC7CF,WAAWC,2BAA2B,GAAG;AAC3C;AAIO,SAASlB;IACd,OAAOoB,IAAAA,sBAAW,EAAC;QACjBX,kBAAAA,0CAAgB;QAChBC,sBAAAA,kDAAoB;IACtB;AACF", "ignoreList": [0]}