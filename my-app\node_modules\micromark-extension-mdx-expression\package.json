{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/micromark/micromark-extension-mdx-expression/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/estree": "^1.0.0", "devlop": "^1.0.0", "micromark-factory-mdx-expression": "^2.0.0", "micromark-factory-space": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-events-to-acorn": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0"}, "description": "micromark extension to support MDX or MDX JS expressions", "exports": {"development": "./dev/index.js", "default": "./index.js"}, "files": ["dev/", "index.d.ts", "index.js", "lib/"], "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "keywords": ["ecmascript", "es", "expression", "javascript", "js", "markdown", "mdxjs", "mdx", "micromark-extension", "micromark", "unified"], "license": "MIT", "name": "micromark-extension-mdx-expression", "repository": "https://github.com/micromark/micromark-extension-mdx-expression/tree/main/packages/micromark-extension-mdx-expression", "scripts": {"build": "micromark-build"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "strict": true}, "type": "module", "version": "3.0.1", "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}], "prettier": true, "rules": {"logical-assignment-operators": "off", "unicorn/no-this-assignment": "off"}}}