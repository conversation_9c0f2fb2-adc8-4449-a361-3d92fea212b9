{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type {\n  <PERSON>R<PERSON>ult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  RenderOpts,\n  Segment,\n  CacheNodeSeedData,\n  PreloadCallbacks,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from './types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type {\n  PrerenderStoreModernRuntime,\n  RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\n\nimport React, { type ErrorInfo, type JSX } from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HASH_COOKIE,\n  NEXT_DID_POSTPONE_HEADER,\n} from '../../client/components/app-router-headers'\nimport { createMetadataContext } from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags, type ImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport { dynamicParamTypes } from './get-short-dynamic-param-type'\nimport { getSegmentParam } from './get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree, getRootParams } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport {\n  getServerModuleMap,\n  setReferenceManifestsSingleton,\n} from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  DynamicHTMLPreludeState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createRenderInBrowserAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  PreludeState,\n  consumeDynamicAccess,\n  type DynamicAccess,\n  logDisallowedDynamicError,\n  warnOnSyncDynamicError,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getIsPossibleServerAction } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../client/components/app-router-instance'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport {\n  prerenderAndAbortInSequentialTasksWithStages,\n  processPrelude,\n} from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport { scheduleInSequentialTasks } from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport { HTML_CONTENT_TYPE_HEADER, INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n  type PrerenderResumeDataCache,\n  type RenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\nimport isError from '../../lib/is-error'\nimport { createServerInsertedMetadata } from './metadata-insertion/create-server-inserted-metadata'\nimport { getPreviouslyRevalidatedTags } from '../server-utils'\nimport { executeRevalidates } from '../revalidation-utils'\nimport {\n  trackPendingChunkLoad,\n  trackPendingImport,\n  trackPendingModules,\n} from './module-loading/track-module-loading.external'\nimport { isReactLargeShellError } from './react-large-shell-error'\nimport type { GlobalErrorComponent } from '../../client/components/builtin/global-error'\nimport { normalizeConventionFilePath } from './segment-explorer-path'\nimport { getRequestMeta } from '../request-meta'\nimport { getDynamicParam } from '../../shared/lib/router/utils/get-dynamic-param'\nimport type { ExperimentalConfig } from '../config-shared'\nimport type { Params } from '../request/params'\nimport { createPromiseWithResolvers } from '../../shared/lib/promise-with-resolvers'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => DynamicParam | null\n\nexport type DynamicParam = {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n}\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppSharedContext = {\n  buildId: string\n}\n\nexport type AppRenderContext = {\n  sharedContext: AppSharedContext\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isPossibleServerAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n  /**\n   * For now, the implicit tags are common for the whole route. If we ever start\n   * rendering/revalidating segments independently, they need to move to the\n   * work unit store.\n   */\n  implicitTags: ImplicitTags\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isDevWarmup: undefined | boolean\n  readonly isRoutePPREnabled: boolean\n  readonly previewModeId: string | undefined\n}\n\nconst flightDataPathHeadKey = 'h'\nconst getFlightViewportKey = (requestId: string) => requestId + 'v'\nconst getFlightMetadataKey = (requestId: string) => requestId + 'm'\n\nconst filterStackFrame =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .filterStackFrameDEV\n    : undefined\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isRuntimePrefetchRequest: boolean\n  readonly isRouteTreePrefetchRequest: boolean\n  readonly isDevWarmupRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n  readonly previouslyRevalidatedTags: string[]\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  const isDevWarmupRequest = options.isDevWarmup === true\n\n  // dev warmup requests are treated as prefetch RSC requests\n  // runtime prefetch requests are *not* treated as prefetch requests\n  // (TODO: this is confusing, we should refactor this to express this better)\n  const isPrefetchRequest =\n    isDevWarmupRequest || headers[NEXT_ROUTER_PREFETCH_HEADER] === '1'\n\n  const isRuntimePrefetchRequest = headers[NEXT_ROUTER_PREFETCH_HEADER] === '2'\n\n  const isHmrRefresh = headers[NEXT_HMR_REFRESH_HEADER] !== undefined\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isRSCRequest = isDevWarmupRequest || headers[RSC_HEADER] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(headers[NEXT_ROUTER_STATE_TREE_HEADER])\n    : undefined\n\n  // Checks if this is a prefetch of the Route Tree by the Segment Cache\n  const isRouteTreePrefetchRequest =\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] === '/_tree'\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n    headers,\n    options.previewModeId\n  )\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isRuntimePrefetchRequest,\n    isRouteTreePrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    isDevWarmupRequest,\n    nonce,\n    previouslyRevalidatedTags,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  const components = loaderTree[2]\n  const hasGlobalNotFound = !!components['global-not-found']\n  return [\n    '',\n    {\n      children: [\n        PAGE_SEGMENT_KEY,\n        {},\n        {\n          page: components['global-not-found'] ?? components['not-found'],\n        },\n      ],\n    },\n    // When global-not-found is present, skip layout from components\n    hasGlobalNotFound ? components : {},\n  ]\n}\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  params: { [key: string]: any },\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n    const segmentKey = segmentParam.param\n    const dynamicParamType = dynamicParamTypes[segmentParam.type]\n    return getDynamicParam(\n      params,\n      segmentKey,\n      dynamicParamType,\n      pagePath,\n      fallbackRouteParams\n    )\n  }\n}\n\nfunction NonIndex({\n  pagePath,\n  statusCode,\n  isPossibleServerAction,\n}: {\n  pagePath: string\n  statusCode: number | undefined\n  isPossibleServerAction: boolean\n}) {\n  const is404Page = pagePath === '/404'\n  const isInvalidStatusCode = typeof statusCode === 'number' && statusCode > 400\n\n  // Only render noindex for page request, skip for server actions\n  // TODO: is this correct if `isPossibleServerAction` is a false positive?\n  if (!isPossibleServerAction && (is404Page || isInvalidStatusCode)) {\n    return <meta name=\"robots\" content=\"noindex\" />\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `next-router-state-tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      tree: loaderTree,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const {\n      ViewportTree,\n      MetadataTree,\n      getViewportReady,\n      getMetadataReady,\n      StreamingMetadataOutlet,\n    } = createMetadataComponents({\n      tree: loaderTree,\n      parsedQuery: query,\n      pathname: url.pathname,\n      metadataContext: createMetadataContext(ctx.renderOpts),\n      getDynamicParamFromSegment,\n      appUsingSizeAdjustment,\n      workStore,\n      MetadataBoundary,\n      ViewportBoundary,\n      serveStreamingMetadata,\n    })\n\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        // For flight, render metadata inside leaf page\n        rscHead: (\n          <React.Fragment key={flightDataPathHeadKey}>\n            {/* noindex needs to be blocking */}\n            <NonIndex\n              pagePath={ctx.pagePath}\n              statusCode={ctx.res.statusCode}\n              isPossibleServerAction={ctx.isPossibleServerAction}\n            />\n            {/* Adding requestId as react key to make metadata remount for each render */}\n            <ViewportTree key={getFlightViewportKey(requestId)} />\n            <MetadataTree key={getFlightMetadataKey(requestId)} />\n          </React.Fragment>\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        getViewportReady,\n        getMetadataReady,\n        preloadCallbacks,\n        StreamingMetadataOutlet,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.sharedContext.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.sharedContext.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    // TODO: is this correct if `isPossibleServerAction` is a false positive?\n    routeType: ctx.isPossibleServerAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    !!renderOpts.dev,\n    onFlightDataRenderError\n  )\n\n  const RSCPayload: RSCPayload & {\n    /** Only available during cacheComponents development builds. Used for logging errors. */\n    _validation?: Promise<React.ReactNode>\n  } = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    ctx.componentMod.renderToReadableStream,\n    RSCPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n      filterStackFrame,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n  })\n}\n\nasync function generateRuntimePrefetchResult(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  requestStore: RequestStore\n): Promise<RenderResult> {\n  const { workStore } = ctx\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      // TODO(runtime-ppr): should we use a different value?\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    false,\n    onFlightDataRenderError\n  )\n\n  const metadata: AppPageRenderResultMetadata = {}\n\n  const generatePayload = () => generateDynamicRSCPayload(ctx, undefined)\n\n  const {\n    componentMod: { tree },\n    getDynamicParamFromSegment,\n  } = ctx\n  const rootParams = getRootParams(tree, getDynamicParamFromSegment)\n\n  // We need to share caches between the prospective prerender and the final prerender,\n  // but we're not going to persist this anywhere.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  // We're not resuming an existing render.\n  const renderResumeDataCache = null\n\n  await prospectiveRuntimeServerPrerender(\n    ctx,\n    generatePayload,\n    prerenderResumeDataCache,\n    renderResumeDataCache,\n    rootParams,\n    requestStore.cookies,\n    requestStore.draftMode\n  )\n\n  const response = await finalRuntimeServerPrerender(\n    ctx,\n    generatePayload,\n    prerenderResumeDataCache,\n    renderResumeDataCache,\n    rootParams,\n    requestStore.cookies,\n    requestStore.draftMode,\n    onError\n  )\n\n  applyMetadataFromPrerenderResult(response, metadata, workStore)\n  metadata.fetchMetrics = ctx.workStore.fetchMetrics\n\n  if (response.isPartial) {\n    res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n  }\n\n  return new FlightRenderResult(response.result.prelude, metadata)\n}\n\nasync function prospectiveRuntimeServerPrerender(\n  ctx: AppRenderContext,\n  getPayload: () => any,\n  prerenderResumeDataCache: PrerenderResumeDataCache | null,\n  renderResumeDataCache: RenderResumeDataCache | null,\n  rootParams: Params,\n  cookies: PrerenderStoreModernRuntime['cookies'],\n  draftMode: PrerenderStoreModernRuntime['draftMode']\n) {\n  const { implicitTags, renderOpts, workStore } = ctx\n\n  const { clientReferenceManifest, ComponentMod } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  // The cacheSignal helps us track whether caches are still filling or we are ready\n  // to cut the render off.\n  const cacheSignal = new CacheSignal()\n\n  const initialServerPrerenderStore: PrerenderStoreModernRuntime = {\n    type: 'prerender-runtime',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    // We only need to track dynamic accesses during the final prerender.\n    dynamicTracking: null,\n    // Runtime prefetches are never cached server-side, only client-side,\n    // so we set `expire` and `revalidate` to their minimum values just in case.\n    revalidate: 1,\n    expire: 0,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    renderResumeDataCache,\n    prerenderResumeDataCache,\n    hmrRefreshHash: undefined,\n    captureOwnerStack: undefined,\n    // We only need task sequencing in the final prerender.\n    runtimeStagePromise: null,\n    // These are not present in regular prerenders, but allowed in a runtime prerender.\n    cookies,\n    draftMode,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const initialServerPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getPayload\n  )\n\n  const pendingInitialServerResult = workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    ComponentMod.prerender,\n    initialServerPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError: (err) => {\n        const digest = getDigestForWellKnownError(err)\n\n        if (digest) {\n          return digest\n        }\n\n        if (initialServerPrerenderController.signal.aborted) {\n          // The render aborted before this error was handled which indicates\n          // the error is caused by unfinished components within the render\n          return\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      },\n      // we don't care to track postpones during the prospective render because we need\n      // to always do a final render anyway\n      onPostpone: undefined,\n      // We don't want to stop rendering until the cacheSignal is complete so we pass\n      // a different signal to this render call than is used by dynamic APIs to signify\n      // transitioning out of the prerender environment\n      signal: initialServerRenderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We don't need to continue the prerender process if we already\n  // detected invalid dynamic usage in the initial prerender phase.\n  if (workStore.invalidDynamicUsageError) {\n    throw workStore.invalidDynamicUsageError\n  }\n\n  try {\n    return await createReactServerPrerenderResult(pendingInitialServerResult)\n  } catch (err) {\n    if (\n      initialServerRenderController.signal.aborted ||\n      initialServerPrerenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, workStore.route)\n    }\n    return null\n  }\n}\n\nasync function finalRuntimeServerPrerender(\n  ctx: AppRenderContext,\n  getPayload: () => any,\n  prerenderResumeDataCache: PrerenderResumeDataCache | null,\n  renderResumeDataCache: RenderResumeDataCache | null,\n  rootParams: Params,\n  cookies: PrerenderStoreModernRuntime['cookies'],\n  draftMode: PrerenderStoreModernRuntime['draftMode'],\n  onError: (err: unknown) => string | undefined\n) {\n  const { implicitTags, renderOpts } = ctx\n\n  const {\n    clientReferenceManifest,\n    ComponentMod,\n    experimental,\n    isDebugDynamicAccesses,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const selectStaleTime = createSelectStaleTime(experimental)\n\n  let serverIsDynamic = false\n  const finalServerController = new AbortController()\n\n  const serverDynamicTracking = createDynamicTrackingState(\n    isDebugDynamicAccesses\n  )\n\n  const { promise: runtimeStagePromise, resolve: resolveBlockedRuntimeAPIs } =\n    createPromiseWithResolvers<void>()\n\n  const finalServerPrerenderStore: PrerenderStoreModernRuntime = {\n    type: 'prerender-runtime',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    // Runtime prefetches are never cached server-side, only client-side,\n    // so we set `expire` and `revalidate` to their minimum values just in case.\n    revalidate: 1,\n    expire: 0,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache,\n    hmrRefreshHash: undefined,\n    captureOwnerStack: undefined,\n    // Used to separate the \"Static\" stage from the \"Runtime\" stage.\n    runtimeStagePromise,\n    // These are not present in regular prerenders, but allowed in a runtime prerender.\n    cookies,\n    draftMode,\n  }\n\n  const finalRSCPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getPayload\n  )\n\n  let prerenderIsPending = true\n  const result = await prerenderAndAbortInSequentialTasksWithStages(\n    async () => {\n      // Static stage\n      const prerenderResult = await workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        ComponentMod.prerender,\n        finalRSCPayload,\n        clientReferenceManifest.clientModules,\n        {\n          filterStackFrame,\n          onError,\n          signal: finalServerController.signal,\n        }\n      )\n      prerenderIsPending = false\n      return prerenderResult\n    },\n    () => {\n      // Advance to the runtime stage.\n      //\n      // We make runtime APIs hang during the first task (above), and unblock them in the following task (here).\n      // This makes sure that, at this point, we'll have finished all the static parts (what we'd prerender statically).\n      // We know that they don't contain any incorrect sync IO, because that'd have caused a build error.\n      // After we unblock Runtime APIs, if we encounter sync IO (e.g. `await cookies(); Date.now()`),\n      // we'll abort, but we'll produce at least as much output as a static prerender would.\n      resolveBlockedRuntimeAPIs()\n    },\n    () => {\n      // Abort.\n      if (finalServerController.signal.aborted) {\n        // If the server controller is already aborted we must have called something\n        // that required aborting the prerender synchronously such as with new Date()\n        serverIsDynamic = true\n        return\n      }\n\n      if (prerenderIsPending) {\n        // If prerenderIsPending then we have blocked for longer than a Task and we assume\n        // there is something unfinished.\n        serverIsDynamic = true\n      }\n      finalServerController.abort()\n    }\n  )\n\n  warnOnSyncDynamicError(serverDynamicTracking)\n\n  return {\n    result,\n    // TODO(runtime-ppr): do we need to produce a digest map here?\n    // digestErrorsMap: ...,\n    dynamicAccess: serverDynamicTracking,\n    isPartial: serverIsDynamic,\n    collectedRevalidate: finalServerPrerenderStore.revalidate,\n    collectedExpire: finalServerPrerenderStore.expire,\n    collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n    collectedTags: finalServerPrerenderStore.tags,\n  }\n}\n\n/**\n * Performs a \"warmup\" render of the RSC payload for a given route. This function is called by the server\n * prior to an actual render request in Dev mode only. It's purpose is to fill caches so the actual render\n * can accurately log activity in the right render context (Prerender vs Render).\n *\n * At the moment this implementation is mostly a fork of generateDynamicFlightRenderResult\n */\nasync function warmupDevRender(\n  req: BaseNextRequest,\n  ctx: AppRenderContext\n): Promise<RenderResult> {\n  const {\n    clientReferenceManifest,\n    componentMod: ComponentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const {\n    allowEmptyStaticShell = false,\n    dev,\n    onInstrumentationRequestError,\n  } = renderOpts\n\n  if (!dev) {\n    throw new InvariantError(\n      'generateDynamicFlightRenderResult should never be called in `next start` mode.'\n    )\n  }\n\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    true,\n    onFlightDataRenderError\n  )\n\n  // We're doing a dev warmup, so we should create a new resume data cache so\n  // we can fill it.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const renderController = new AbortController()\n  const prerenderController = new AbortController()\n  const reactController = new AbortController()\n  const cacheSignal = new CacheSignal()\n\n  const prerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: renderController.signal,\n    controller: prerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash: req.cookies[NEXT_HMR_REFRESH_HASH_COOKIE],\n    captureOwnerStack: ComponentMod.captureOwnerStack,\n    // warmup is a dev only feature and no fallback params are used in the\n    // primary render which is static. We only use a prerender store here to\n    // allow the warmup to halt on Request data APIs and fetches.\n    fallbackRouteParams: null,\n  }\n\n  const rscPayload = await workUnitAsyncStorage.run(\n    prerenderStore,\n    generateDynamicRSCPayload,\n    ctx\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  workUnitAsyncStorage.run(\n    prerenderStore,\n    ComponentMod.renderToReadableStream,\n    rscPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError,\n      signal: renderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  // We unset the cache so any late over-run renders aren't able to write into this cache\n  prerenderStore.prerenderResumeDataCache = null\n  // Abort the render\n  reactController.abort()\n  renderController.abort()\n\n  // We don't really want to return a result here but the stack of functions\n  // that calls into renderToHTML... expects a result. We should refactor this to\n  // lift the warmup pathway outside of renderToHTML... but for now this suffices\n  return new FlightRenderResult('', {\n    fetchMetrics: workStore.fetchMetrics,\n    renderResumeDataCache: createRenderResumeDataCache(\n      prerenderResumeDataCache\n    ),\n  })\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: React.ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const hasGlobalNotFound = !!tree[2]['global-not-found']\n\n  const {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  } = createMetadataComponents({\n    tree,\n    // When it's using global-not-found, metadata errorType is undefined, which will retrieve the\n    // metadata from the page.\n    // When it's using not-found, metadata errorType is 'not-found', which will retrieve the\n    // metadata from the not-found.js boundary.\n    // TODO: remove this condition and keep it undefined when global-not-found is stabilized.\n    errorType: is404 && !hasGlobalNotFound ? 'not-found' : undefined,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const seedData = await createComponentTree({\n    ctx,\n    loaderTree: tree,\n    parentParams: {},\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    getViewportReady,\n    getMetadataReady,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n    StreamingMetadataOutlet,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree />\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: <Preloads preloadCallbacks={preloadCallbacks} />,\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  ssrError: unknown,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const { MetadataTree, ViewportTree } = createMetadataComponents({\n    tree,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata: serveStreamingMetadata,\n  })\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree />\n      {process.env.NODE_ENV === 'development' && (\n        <meta name=\"next-error\" content=\"not-found\" />\n      )}\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  let err: Error | undefined = undefined\n  if (ssrError) {\n    err = isError(ssrError) ? ssrError : new Error(ssrError + '')\n  }\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const seedData: CacheNodeSeedData = [\n    initialTree[0],\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        {process.env.NODE_ENV !== 'production' && err ? (\n          <template\n            data-next-error-message={err.message}\n            data-next-error-digest={'digest' in err ? err.digest : ''}\n            data-next-error-stack={err.stack}\n          />\n        ) : null}\n      </body>\n    </html>,\n    {},\n    null,\n    false,\n  ]\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\nfunction assertClientReferenceManifest(\n  clientReferenceManifest: RenderOpts['clientReferenceManifest']\n): asserts clientReferenceManifest is NonNullable<\n  RenderOpts['clientReferenceManifest']\n> {\n  if (!clientReferenceManifest) {\n    throw new InvariantError('Expected clientReferenceManifest to be defined.')\n  }\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ServerInsertedHTMLProvider>\n        <AppRouter\n          actionQueue={actionQueue}\n          globalErrorState={response.G}\n          assetPrefix={response.p}\n        />\n      </ServerInsertedHTMLProvider>\n    </HeadManagerContext.Provider>\n  )\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction ErrorApp<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  return (\n    <ServerInsertedHTMLProvider>\n      <AppRouter\n        actionQueue={actionQueue}\n        globalErrorState={response.G}\n        assetPrefix={response.p}\n      />\n    </ServerInsertedHTMLProvider>\n  )\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  postponedState: PostponedState | null,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext,\n  fallbackRouteParams: FallbackRouteParams | null\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    clientReferenceManifest,\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we track calls to `loadChunk` and `require`. This allows us\n    // to treat chunk/module loading with similar semantics as cache reads to avoid\n    // module loading from causing a prerender to abort too early.\n\n    const shouldTrackModuleLoading = () => {\n      if (!renderOpts.experimental.cacheComponents) {\n        return false\n      }\n      if (renderOpts.dev) {\n        return true\n      }\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      if (!workUnitStore) {\n        return false\n      }\n\n      switch (workUnitStore.type) {\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-runtime':\n        case 'cache':\n        case 'private-cache':\n          return true\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n        case 'unstable-cache':\n          return false\n        default:\n          workUnitStore satisfies never\n      }\n    }\n\n    const __next_require__: typeof instrumented.require = (...args) => {\n      const exportsOrPromise = instrumented.require(...args)\n      if (shouldTrackModuleLoading()) {\n        // requiring an async module returns a promise.\n        trackPendingImport(exportsOrPromise)\n      }\n      return exportsOrPromise\n    }\n    // @ts-expect-error\n    globalThis.__next_require__ = __next_require__\n\n    const __next_chunk_load__: typeof instrumented.loadChunk = (...args) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      if (shouldTrackModuleLoading()) {\n        trackPendingChunkLoad(loadingChunk)\n      }\n      return loadingChunk\n    }\n    // @ts-expect-error\n    globalThis.__next_chunk_load__ = __next_chunk_load__\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // reset isr status at start of request\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setIsrStatus?.(pathname, null)\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    res.onClose(() => {\n      // We stop tracking fetch metrics when the response closes, since we\n      // report them at that time.\n      workStore.shouldTrackFetchMetrics = false\n    })\n\n    req.originalRequest.on('end', () => {\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {\n    statusCode: isNotFoundPath ? 404 : undefined,\n  }\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const { tree: loaderTree, taintObjectReference } = ComponentMod\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to Client Components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRuntimePrefetchRequest,\n    isRSCRequest,\n    isDevWarmupRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  const { isStaticGeneration } = workStore\n\n  /**\n   * The metadata items array created in next-app-loader with all relevant information\n   * that we need to resolve the final metadata.\n   */\n  let requestId: string\n\n  if (isStaticGeneration) {\n    requestId = Buffer.from(\n      await crypto.subtle.digest('SHA-1', Buffer.from(req.url))\n    ).toString('hex')\n  } else if (process.env.NEXT_RUNTIME === 'edge') {\n    requestId = crypto.randomUUID()\n  } else {\n    requestId = (\n      require('next/dist/compiled/nanoid') as typeof import('next/dist/compiled/nanoid')\n    ).nanoid()\n  }\n\n  /**\n   * Dynamic parameters. E.g. when you visit `/dashboard/vercel` which is rendered by `/dashboard/[slug]` the value will be {\"slug\": \"vercel\"}.\n   */\n  const params = renderOpts.params ?? {}\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    params,\n    pagePath,\n    fallbackRouteParams\n  )\n\n  const isPossibleActionRequest = getIsPossibleServerAction(req)\n\n  const implicitTags = await getImplicitTags(\n    workStore.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isPossibleServerAction: isPossibleActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n    sharedContext,\n    implicitTags,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      loaderTree,\n      fallbackRouteParams\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (workStore.invalidDynamicUsageError) {\n      logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError)\n      throw new StaticGenBailoutError()\n    }\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n      contentType: HTML_CONTENT_TYPE_HEADER,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    applyMetadataFromPrerenderResult(response, metadata, workStore)\n\n    if (response.renderResumeDataCache) {\n      metadata.renderResumeDataCache = response.renderResumeDataCache\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.renderResumeDataCache ?? postponedState?.renderResumeDataCache\n\n    const rootParams = getRootParams(loaderTree, ctx.getDynamicParamFromSegment)\n    const devValidatingFallbackParams =\n      getRequestMeta(req, 'devValidatingFallbackParams') || null\n    const requestStore = createRequestStoreForRender(\n      req,\n      res,\n      url,\n      rootParams,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache,\n      devValidatingFallbackParams\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setIsrStatus &&\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req) &&\n      !isDevWarmupRequest\n    ) {\n      const setIsrStatus = renderOpts.setIsrStatus\n      req.originalRequest.on('end', () => {\n        if (!requestStore.usedDynamic && !workStore.forceDynamic) {\n          // only node can be ISR so we only need to update the status here\n          const { pathname } = new URL(req.url || '/', 'http://n')\n          setIsrStatus(pathname, true)\n        }\n      })\n    }\n\n    if (isDevWarmupRequest) {\n      return warmupDevRender(req, ctx)\n    } else if (isRSCRequest) {\n      if (isRuntimePrefetchRequest) {\n        return generateRuntimePrefetchResult(req, res, ctx, requestStore)\n      } else {\n        return generateDynamicFlightRenderResult(req, ctx, requestStore)\n      }\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let formState: null | any = null\n    if (isPossibleActionRequest) {\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n        metadata,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          metadata.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            notFoundLoaderTree,\n            formState,\n            postponedState,\n            metadata,\n            devValidatingFallbackParams\n          )\n\n          return new RenderResult(stream, {\n            metadata,\n            contentType: HTML_CONTENT_TYPE_HEADER,\n          })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n      contentType: HTML_CONTENT_TYPE_HEADER,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      requestStore,\n      req,\n      res,\n      ctx,\n      loaderTree,\n      formState,\n      postponedState,\n      metadata,\n      devValidatingFallbackParams\n    )\n\n    // Invalid dynamic usages should only error the request in development.\n    // In production, it's better to produce a result.\n    // (the dynamic error will still be thrown inside the component tree, but it's catchable by error boundaries)\n    if (workStore.invalidDynamicUsageError && workStore.dev) {\n      throw workStore.invalidDynamicUsageError\n    }\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  isDevWarmup: boolean,\n  sharedContext: AppSharedContext\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  isDevWarmup,\n  sharedContext\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isDevWarmup,\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n    previewModeId: renderOpts.previewProps?.previewModeId,\n  })\n\n  const { isPrefetchRequest, previouslyRevalidatedTags } = parsedRequestHeaders\n\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      renderOpts.params\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.renderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    renderOpts,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n    buildId: sharedContext.buildId,\n    previouslyRevalidatedTags,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    postponedState,\n    serverComponentsHmrCache,\n    sharedContext,\n    fallbackRouteParams\n  )\n}\n\nfunction applyMetadataFromPrerenderResult(\n  response: Pick<\n    PrerenderToStreamResult,\n    | 'collectedExpire'\n    | 'collectedRevalidate'\n    | 'collectedStale'\n    | 'collectedTags'\n  >,\n  metadata: AppPageRenderResultMetadata,\n  workStore: WorkStore\n) {\n  if (response.collectedTags) {\n    metadata.fetchTags = response.collectedTags.join(',')\n  }\n\n  // Let the client router know how long to keep the cached entry around.\n  const staleHeader = String(response.collectedStale)\n  metadata.headers ??= {}\n  metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n  // If force static is specifically set to false, we should not revalidate\n  // the page.\n  if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n    metadata.cacheControl = { revalidate: 0, expire: undefined }\n  } else {\n    // Copy the cache control value onto the render result metadata.\n    metadata.cacheControl = {\n      revalidate:\n        response.collectedRevalidate >= INFINITE_CACHE\n          ? false\n          : response.collectedRevalidate,\n      expire:\n        response.collectedExpire >= INFINITE_CACHE\n          ? undefined\n          : response.collectedExpire,\n    }\n  }\n\n  // provide bailout info for debugging\n  if (metadata.cacheControl?.revalidate === 0) {\n    metadata.staticBailoutInfo = {\n      description: workStore.dynamicUsageDescription,\n      stack: workStore.dynamicUsageStack,\n    }\n  }\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null,\n  metadata: AppPageRenderResultMetadata,\n  devValidatingFallbackParams: FallbackRouteParams | null\n): Promise<ReadableStream<Uint8Array>> {\n  const { assetPrefix, nonce, pagePath, renderOpts } = ctx\n\n  const {\n    basePath,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod,\n    crossOrigin,\n    dev = false,\n    experimental,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    shouldWaitOnAllReady,\n    subresourceIntegrityManifest,\n    supportsDynamicResponse,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n\n  const setHeader = res.setHeader.bind(res)\n  const appendHeader = res.appendHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when running `next dev`\n      dev &&\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into cacheComponents\n      experimental.cacheComponents\n    ) {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: InitialRSCPayload & {\n        /** Only available during cacheComponents development builds. Used for logging errors. */\n        _validation?: Promise<React.ReactNode>\n      } = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      RSCPayload._validation = validationOutlet\n\n      const reactServerStream = await workUnitAsyncStorage.run(\n        requestStore,\n        scheduleInSequentialTasks,\n        () => {\n          requestStore.prerenderPhase = true\n          return ComponentMod.renderToReadableStream(\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n              environmentName: () =>\n                requestStore.prerenderPhase === true ? 'Prerender' : 'Server',\n              filterStackFrame,\n            }\n          )\n        },\n        () => {\n          requestStore.prerenderPhase = false\n        }\n      )\n\n      spawnDynamicValidationInDev(\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        requestStore,\n        devValidatingFallbackParams\n      )\n\n      reactServerResult = new ReactServerResult(reactServerStream)\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          ComponentMod.renderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: serverComponentsErrorHandler,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const { postponed, preludeState } =\n          getPostponedFromState(postponedState)\n        const resume = (\n          require('react-dom/server') as typeof import('react-dom/server')\n        ).resume\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n          />,\n          postponed,\n          { onError: htmlRendererErrorHandler, nonce }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          // If the prelude is empty (i.e. is no static shell), we should wait for initial HTML to be rendered\n          // to avoid injecting RSC data too early.\n          // If we have a non-empty-prelude (i.e. a static HTML shell), then it's already been sent separately,\n          // so we shouldn't wait for any HTML to be emitted from the resume before sending RSC data.\n          delayDataUntilFirstHtmlChunk:\n            preludeState === DynamicHTMLPreludeState.Empty,\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = (\n      require('react-dom/server') as typeof import('react-dom/server')\n    ).renderToReadableStream\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        nonce={nonce}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            appendHeader(key, value)\n          })\n        },\n        maxHeadersLength: reactMaxHeadersLength,\n        bootstrapScripts: [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n     *\n     *   3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n     *       resolve all suspenses and generate a full HTML. e.g. when it's a\n     *       html limited bot requests, we produce the full HTML content.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML =\n      supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n      buildId: ctx.workStore.buildId,\n      getServerInsertedHTML,\n      getServerInsertedMetadata,\n      validateRootLayout: dev,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? null : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={nonce}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n       *    3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n       *        resolve all suspenses and generate a full HTML. e.g. when it's a\n       *        html limited bot requests, we produce the full HTML content.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML =\n        supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n        buildId: ctx.workStore.buildId,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        getServerInsertedMetadata,\n        validateRootLayout: dev,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: React.ReactNode) => void\n  let outlet = new Promise<React.ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\n/**\n * This function is a fork of prerenderToStream cacheComponents branch.\n * While it doesn't return a stream we want it to have identical\n * prerender semantics to prerenderToStream and should update it\n * in conjunction with any changes to that function.\n */\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: React.ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  requestStore: RequestStore,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<void> {\n  const {\n    componentMod: ComponentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const { allowEmptyStaticShell = false } = renderOpts\n\n  // These values are placeholder values for this validating render\n  // that are provided during the actual prerenderToStream.\n  const preinitScripts = () => {}\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  const hmrRefreshHash = requestStore.cookies.get(\n    NEXT_HMR_REFRESH_HASH_COOKIE\n  )?.value\n\n  // The prerender controller represents the lifetime of the prerender. It will\n  // be aborted when a task is complete or a synchronously aborting API is\n  // called. Notably, during prospective prerenders, this does not actually\n  // terminate the prerender itself, which will continue until all caches are\n  // filled.\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller is used to abort the React prerender.\n  const initialServerReactController = new AbortController()\n\n  // This controller represents the lifetime of the React prerender. Its signal\n  // can be used for any I/O operation to abort the I/O and/or to reject, when\n  // prerendering aborts. This includes our own hanging promises for accessing\n  // request data, and for fetch calls. It might be replaced in the future by\n  // React.cacheSignal(). It's aborted after the React controller, so that no\n  // pending I/O can register abort listeners that are called before React's\n  // abort listener is called. This ensures that pending I/O is not rejected too\n  // early when aborting the prerender. Notably, during the prospective\n  // prerender, it is different from the prerender controller because we don't\n  // want to end the React prerender until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  // The cacheSignal helps us track whether caches are still filling or we are\n  // ready to cut the render off.\n  const cacheSignal = new CacheSignal()\n\n  const captureOwnerStackClient = React.captureOwnerStack\n  const captureOwnerStackServer = ComponentMod.captureOwnerStack\n\n  // The resume data cache here should use a fresh instance as it's\n  // performing a fresh prerender. If we get to implementing the\n  // prerendering of an already prerendered page, we should use the passed\n  // resume data cache instead.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPayloadPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n    // various request data APIs bind to this controller to reject after completion.\n    renderSignal: initialServerRenderController.signal,\n    // When we generate the RSC payload we might abort this controller due to sync IO\n    // but we don't actually care about sync IO in this phase so we use a throw away controller\n    // that isn't connected to anything\n    controller: new AbortController(),\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const initialServerPayload = await workUnitAsyncStorage.run(\n    initialServerPayloadPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const pendingInitialServerResult = workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    ComponentMod.prerender,\n    initialServerPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError: (err) => {\n        const digest = getDigestForWellKnownError(err)\n\n        if (digest) {\n          return digest\n        }\n\n        if (isReactLargeShellError(err)) {\n          // TODO: Aggregate\n          console.error(err)\n          return undefined\n        }\n\n        if (initialServerPrerenderController.signal.aborted) {\n          // The render aborted before this error was handled which indicates\n          // the error is caused by unfinished components within the render\n          return\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      },\n      // we don't care to track postpones during the prospective render because we need\n      // to always do a final render anyway\n      onPostpone: undefined,\n      // We don't want to stop rendering until the cacheSignal is complete so we pass\n      // a different signal to this render call than is used by dynamic APIs to signify\n      // transitioning out of the prerender environment\n      signal: initialServerReactController.signal,\n    }\n  )\n\n  // The listener to abort our own render controller must be added after React\n  // has added its listener, to ensure that pending I/O is not aborted/rejected\n  // too early.\n  initialServerReactController.signal.addEventListener(\n    'abort',\n    () => {\n      initialServerRenderController.abort()\n    },\n    { once: true }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  initialServerReactController.abort()\n\n  // We don't need to continue the prerender process if we already\n  // detected invalid dynamic usage in the initial prerender phase.\n  const { invalidDynamicUsageError } = workStore\n  if (invalidDynamicUsageError) {\n    resolveValidation(\n      <LogSafely\n        fn={() => {\n          console.error(invalidDynamicUsageError)\n        }}\n      />\n    )\n    return\n  }\n\n  let initialServerResult\n  try {\n    initialServerResult = await createReactServerPrerenderResult(\n      pendingInitialServerResult\n    )\n  } catch (err) {\n    if (\n      initialServerReactController.signal.aborted ||\n      initialServerPrerenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, workStore.route)\n    }\n  }\n\n  if (initialServerResult) {\n    const initialClientPrerenderController = new AbortController()\n    const initialClientReactController = new AbortController()\n    const initialClientRenderController = new AbortController()\n\n    const initialClientPrerenderStore: PrerenderStore = {\n      type: 'prerender-client',\n      phase: 'render',\n      rootParams,\n      fallbackRouteParams,\n      implicitTags,\n      renderSignal: initialClientRenderController.signal,\n      controller: initialClientPrerenderController,\n      // For HTML Generation the only cache tracked activity\n      // is module loading, which has it's own cache signal\n      cacheSignal: null,\n      dynamicTracking: null,\n      allowEmptyStaticShell,\n      revalidate: INFINITE_CACHE,\n      expire: INFINITE_CACHE,\n      stale: INFINITE_CACHE,\n      tags: [...implicitTags.tags],\n      prerenderResumeDataCache,\n      renderResumeDataCache: null,\n      hmrRefreshHash: undefined,\n      captureOwnerStack: captureOwnerStackClient,\n    }\n\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      <App\n        reactServerStream={initialServerResult.asUnclosingStream()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        nonce={nonce}\n      />,\n      {\n        signal: initialClientReactController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (isReactLargeShellError(err)) {\n            // TODO: Aggregate\n            console.error(err)\n            return undefined\n          }\n\n          if (initialClientReactController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        },\n        // We don't need bootstrap scripts in this prerender\n        // bootstrapScripts: [bootstrapScript],\n      }\n    )\n\n    // The listener to abort our own render controller must be added after React\n    // has added its listener, to ensure that pending I/O is not\n    // aborted/rejected too early.\n    initialClientReactController.signal.addEventListener(\n      'abort',\n      () => {\n        initialClientRenderController.abort()\n      },\n      { once: true }\n    )\n\n    pendingInitialClientResult.catch((err) => {\n      if (\n        initialClientReactController.signal.aborted ||\n        isPrerenderInterruptedError(err)\n      ) {\n        // These are expected errors that might error the prerender. we ignore them.\n      } else if (\n        process.env.NEXT_DEBUG_BUILD ||\n        process.env.__NEXT_VERBOSE_LOGGING\n      ) {\n        // We don't normally log these errors because we are going to retry anyway but\n        // it can be useful for debugging Next.js itself to get visibility here when needed\n        printDebugThrownValueForProspectiveRender(err, workStore.route)\n      }\n    })\n\n    // This is mostly needed for dynamic `import()`s in client components.\n    // Promises passed to client were already awaited above (assuming that they came from cached functions)\n    trackPendingModules(cacheSignal)\n    await cacheSignal.cacheReady()\n    initialClientReactController.abort()\n  }\n\n  const finalServerReactController = new AbortController()\n  const finalServerRenderController = new AbortController()\n\n  const finalServerPayloadPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n    // various request data APIs bind to this controller to reject after completion.\n    renderSignal: finalServerRenderController.signal,\n    // When we generate the RSC payload we might abort this controller due to sync IO\n    // but we don't actually care about sync IO in this phase so we use a throw away controller\n    // that isn't connected to anything\n    controller: new AbortController(),\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n    finalServerPayloadPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const serverDynamicTracking = createDynamicTrackingState(\n    false // isDebugDynamicAccesses\n  )\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    renderSignal: finalServerRenderController.signal,\n    controller: finalServerReactController,\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const reactServerResult = await createReactServerPrerenderResult(\n    prerenderAndAbortInSequentialTasks(\n      async () => {\n        const pendingPrerenderResult = workUnitAsyncStorage.run(\n          // The store to scope\n          finalServerPrerenderStore,\n          // The function to run\n          ComponentMod.prerender,\n          // ... the arguments for the function to run\n          finalAttemptRSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: (err: unknown) => {\n              if (\n                finalServerReactController.signal.aborted &&\n                isPrerenderInterruptedError(err)\n              ) {\n                return err.digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n            signal: finalServerReactController.signal,\n          }\n        )\n\n        // The listener to abort our own render controller must be added after\n        // React has added its listener, to ensure that pending I/O is not\n        // aborted/rejected too early.\n        finalServerReactController.signal.addEventListener(\n          'abort',\n          () => {\n            finalServerRenderController.abort()\n          },\n          { once: true }\n        )\n\n        return pendingPrerenderResult\n      },\n      () => {\n        finalServerReactController.abort()\n      }\n    )\n  )\n\n  const clientDynamicTracking = createDynamicTrackingState(\n    false //isDebugDynamicAccesses\n  )\n  const finalClientReactController = new AbortController()\n  const finalClientRenderController = new AbortController()\n\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender-client',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    renderSignal: finalClientRenderController.signal,\n    controller: finalClientReactController,\n    // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackClient,\n  }\n\n  let dynamicValidation = createDynamicValidationState()\n\n  try {\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    let { prelude: unprocessedPrelude } =\n      await prerenderAndAbortInSequentialTasks(\n        () => {\n          const pendingFinalClientResult = workUnitAsyncStorage.run(\n            finalClientPrerenderStore,\n            prerender,\n            <App\n              reactServerStream={reactServerResult.asUnclosingStream()}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={nonce}\n            />,\n            {\n              signal: finalClientReactController.signal,\n              onError: (err: unknown, errorInfo: ErrorInfo) => {\n                if (\n                  isPrerenderInterruptedError(err) ||\n                  finalClientReactController.signal.aborted\n                ) {\n                  const componentStack = errorInfo.componentStack\n                  if (typeof componentStack === 'string') {\n                    trackAllowedDynamicAccess(\n                      workStore,\n                      componentStack,\n                      dynamicValidation,\n                      clientDynamicTracking\n                    )\n                  }\n                  return\n                }\n\n                if (isReactLargeShellError(err)) {\n                  // TODO: Aggregate\n                  console.error(err)\n                  return undefined\n                }\n\n                return getDigestForWellKnownError(err)\n              },\n              // We don't need bootstrap scripts in this prerender\n              // bootstrapScripts: [bootstrapScript],\n            }\n          )\n\n          // The listener to abort our own render controller must be added after\n          // React has added its listener, to ensure that pending I/O is not\n          // aborted/rejected too early.\n          finalClientReactController.signal.addEventListener(\n            'abort',\n            () => {\n              finalClientRenderController.abort()\n            },\n            { once: true }\n          )\n\n          return pendingFinalClientResult\n        },\n        () => {\n          finalClientReactController.abort()\n        }\n      )\n\n    const { preludeIsEmpty } = await processPrelude(unprocessedPrelude)\n    resolveValidation(\n      <LogSafely\n        fn={throwIfDisallowedDynamic.bind(\n          null,\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        )}\n      />\n    )\n  } catch (thrownValue) {\n    // Even if the root errors we still want to report any cache components errors\n    // that were discovered before the root errored.\n\n    let loggingFunction = throwIfDisallowedDynamic.bind(\n      null,\n      workStore,\n      PreludeState.Errored,\n      dynamicValidation,\n      serverDynamicTracking\n    )\n\n    if (process.env.NEXT_DEBUG_BUILD || process.env.__NEXT_VERBOSE_LOGGING) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      const originalLoggingFunction = loggingFunction\n      loggingFunction = () => {\n        console.error(\n          'During dynamic validation the root of the page errored. The next logged error is the thrown value. It may be a duplicate of errors reported during the normal development mode render.'\n        )\n        console.error(thrownValue)\n        originalLoggingFunction()\n      }\n    }\n\n    resolveValidation(<LogSafely fn={loggingFunction} />)\n  }\n}\n\nasync function LogSafely({ fn }: { fn: () => unknown }) {\n  try {\n    await fn()\n  } catch {}\n  return null\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n  renderResumeDataCache?: RenderResumeDataCache\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  tree: LoaderTree,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n\n  const {\n    assetPrefix,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    pagePath,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const {\n    allowEmptyStaticShell = false,\n    basePath,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod,\n    crossOrigin,\n    dev = false,\n    experimental,\n    isDebugDynamicAccesses,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    subresourceIntegrityManifest,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const rootParams = getRootParams(tree, getDynamicParamFromSegment)\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult: null | ReactServerPrerenderResult = null\n  const setMetadataHeader = (name: string) => {\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n  }\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n    setMetadataHeader(name)\n    return res\n  }\n  const appendHeader = (name: string, value: string | string[]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => {\n        res.appendHeader(name, item)\n      })\n    } else {\n      res.appendHeader(name, value)\n    }\n    setMetadataHeader(name)\n  }\n\n  const selectStaleTime = createSelectStaleTime(experimental)\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (experimental.cacheComponents) {\n      /**\n       * cacheComponents with PPR\n       *\n       * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n       * Once we have settled all cache reads we restart the render and abort after a single Task.\n       *\n       * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n       * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n       * and a synchronous abort might prevent us from filling all caches.\n       *\n       * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n       * and the reactServerIsDynamic value to determine how to treat the resulting render\n       */\n\n      // The prerender controller represents the lifetime of the prerender. It\n      // will be aborted when a task is complete or a synchronously aborting API\n      // is called. Notably, during prospective prerenders, this does not\n      // actually terminate the prerender itself, which will continue until all\n      // caches are filled.\n      const initialServerPrerenderController = new AbortController()\n\n      // This controller is used to abort the React prerender.\n      const initialServerReactController = new AbortController()\n\n      // This controller represents the lifetime of the React prerender. Its\n      // signal can be used for any I/O operation to abort the I/O and/or to\n      // reject, when prerendering aborts. This includes our own hanging\n      // promises for accessing request data, and for fetch calls. It might be\n      // replaced in the future by React.cacheSignal(). It's aborted after the\n      // React controller, so that no pending I/O can register abort listeners\n      // that are called before React's abort listener is called. This ensures\n      // that pending I/O is not rejected too early when aborting the prerender.\n      // Notably, during the prospective prerender, it is different from the\n      // prerender controller because we don't want to end the React prerender\n      // until all caches are filled.\n      const initialServerRenderController = new AbortController()\n\n      // The cacheSignal helps us track whether caches are still filling or we are ready\n      // to cut the render off.\n      const cacheSignal = new CacheSignal()\n\n      let resumeDataCache: RenderResumeDataCache | PrerenderResumeDataCache\n      let renderResumeDataCache: RenderResumeDataCache | null = null\n      let prerenderResumeDataCache: PrerenderResumeDataCache | null = null\n\n      if (renderOpts.renderResumeDataCache) {\n        // If a prefilled immutable render resume data cache is provided, e.g.\n        // when prerendering an optional fallback shell after having prerendered\n        // pages with defined params, we use this instead of a prerender resume\n        // data cache.\n        resumeDataCache = renderResumeDataCache =\n          renderOpts.renderResumeDataCache\n      } else {\n        // Otherwise we create a new mutable prerender resume data cache.\n        resumeDataCache = prerenderResumeDataCache =\n          createPrerenderResumeDataCache()\n      }\n\n      const initialServerPayloadPrerenderStore: PrerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n        // various request data APIs bind to this controller to reject after completion.\n        renderSignal: initialServerRenderController.signal,\n        // When we generate the RSC payload we might abort this controller due to sync IO\n        // but we don't actually care about sync IO in this phase so we use a throw away controller\n        // that isn't connected to anything\n        controller: new AbortController(),\n        // During the initial prerender we need to track all cache reads to ensure\n        // we render long enough to fill every cache it is possible to visit during\n        // the final prerender.\n        cacheSignal,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      // We're not going to use the result of this render because the only time it could be used\n      // is if it completes in a microtask and that's likely very rare for any non-trivial app\n      const initialServerPayload = await workUnitAsyncStorage.run(\n        initialServerPayloadPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        renderSignal: initialServerRenderController.signal,\n        controller: initialServerPrerenderController,\n        // During the initial prerender we need to track all cache reads to ensure\n        // we render long enough to fill every cache it is possible to visit during\n        // the final prerender.\n        cacheSignal,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      const pendingInitialServerResult = workUnitAsyncStorage.run(\n        initialServerPrerenderStore,\n        ComponentMod.prerender,\n        initialServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          filterStackFrame,\n          onError: (err) => {\n            const digest = getDigestForWellKnownError(err)\n\n            if (digest) {\n              return digest\n            }\n\n            if (isReactLargeShellError(err)) {\n              // TODO: Aggregate\n              console.error(err)\n              return undefined\n            }\n\n            if (initialServerPrerenderController.signal.aborted) {\n              // The render aborted before this error was handled which indicates\n              // the error is caused by unfinished components within the render\n              return\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          },\n          // we don't care to track postpones during the prospective render because we need\n          // to always do a final render anyway\n          onPostpone: undefined,\n          // We don't want to stop rendering until the cacheSignal is complete so we pass\n          // a different signal to this render call than is used by dynamic APIs to signify\n          // transitioning out of the prerender environment\n          signal: initialServerReactController.signal,\n        }\n      )\n\n      // The listener to abort our own render controller must be added after\n      // React has added its listener, to ensure that pending I/O is not\n      // aborted/rejected too early.\n      initialServerReactController.signal.addEventListener(\n        'abort',\n        () => {\n          initialServerRenderController.abort()\n        },\n        { once: true }\n      )\n\n      // Wait for all caches to be finished filling and for async imports to resolve\n      trackPendingModules(cacheSignal)\n      await cacheSignal.cacheReady()\n\n      initialServerReactController.abort()\n\n      // We don't need to continue the prerender process if we already\n      // detected invalid dynamic usage in the initial prerender phase.\n      if (workStore.invalidDynamicUsageError) {\n        logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError)\n        throw new StaticGenBailoutError()\n      }\n\n      let initialServerResult\n      try {\n        initialServerResult = await createReactServerPrerenderResult(\n          pendingInitialServerResult\n        )\n      } catch (err) {\n        if (\n          initialServerReactController.signal.aborted ||\n          initialServerPrerenderController.signal.aborted\n        ) {\n          // These are expected errors that might error the prerender. we ignore them.\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          // We don't normally log these errors because we are going to retry anyway but\n          // it can be useful for debugging Next.js itself to get visibility here when needed\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      }\n\n      if (initialServerResult) {\n        const initialClientPrerenderController = new AbortController()\n        const initialClientReactController = new AbortController()\n        const initialClientRenderController = new AbortController()\n\n        const initialClientPrerenderStore: PrerenderStore = {\n          type: 'prerender-client',\n          phase: 'render',\n          rootParams,\n          fallbackRouteParams,\n          implicitTags,\n          renderSignal: initialClientRenderController.signal,\n          controller: initialClientPrerenderController,\n          // For HTML Generation the only cache tracked activity\n          // is module loading, which has it's own cache signal\n          cacheSignal: null,\n          dynamicTracking: null,\n          allowEmptyStaticShell,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          renderResumeDataCache,\n          hmrRefreshHash: undefined,\n          captureOwnerStack: undefined, // Not available in production.\n        }\n\n        const prerender = (\n          require('react-dom/static') as typeof import('react-dom/static')\n        ).prerender\n        const pendingInitialClientResult = workUnitAsyncStorage.run(\n          initialClientPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={initialServerResult.asUnclosingStream()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n          />,\n          {\n            signal: initialClientReactController.signal,\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              if (initialClientReactController.signal.aborted) {\n                // These are expected errors that might error the prerender. we ignore them.\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                // We don't normally log these errors because we are going to retry anyway but\n                // it can be useful for debugging Next.js itself to get visibility here when needed\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            bootstrapScripts: [bootstrapScript],\n          }\n        )\n\n        // The listener to abort our own render controller must be added after\n        // React has added its listener, to ensure that pending I/O is not\n        // aborted/rejected too early.\n        initialClientReactController.signal.addEventListener(\n          'abort',\n          () => {\n            initialClientRenderController.abort()\n          },\n          { once: true }\n        )\n\n        pendingInitialClientResult.catch((err) => {\n          if (\n            initialClientReactController.signal.aborted ||\n            isPrerenderInterruptedError(err)\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        })\n\n        // This is mostly needed for dynamic `import()`s in client components.\n        // Promises passed to client were already awaited above (assuming that they came from cached functions)\n        trackPendingModules(cacheSignal)\n        await cacheSignal.cacheReady()\n        initialClientReactController.abort()\n      }\n\n      const finalServerReactController = new AbortController()\n      const finalServerRenderController = new AbortController()\n\n      const finalServerPayloadPrerenderStore: PrerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n        // various request data APIs bind to this controller to reject after completion.\n        renderSignal: finalServerRenderController.signal,\n        // When we generate the RSC payload we might abort this controller due to sync IO\n        // but we don't actually care about sync IO in this phase so we use a throw away controller\n        // that isn't connected to anything\n        controller: new AbortController(),\n        // All caches we could read must already be filled so no tracking is necessary\n        cacheSignal: null,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n        finalServerPayloadPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const serverDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n      let serverIsDynamic = false\n\n      const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        renderSignal: finalServerRenderController.signal,\n        controller: finalServerReactController,\n        // All caches we could read must already be filled so no tracking is necessary\n        cacheSignal: null,\n        dynamicTracking: serverDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      let prerenderIsPending = true\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResult(\n          prerenderAndAbortInSequentialTasks(\n            async () => {\n              const pendingPrerenderResult = workUnitAsyncStorage.run(\n                // The store to scope\n                finalServerPrerenderStore,\n                // The function to run\n                ComponentMod.prerender,\n                // ... the arguments for the function to run\n                finalAttemptRSCPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  filterStackFrame,\n                  onError: (err: unknown) => {\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerReactController.signal,\n                }\n              )\n\n              // The listener to abort our own render controller must be added\n              // after React has added its listener, to ensure that pending I/O\n              // is not aborted/rejected too early.\n              finalServerReactController.signal.addEventListener(\n                'abort',\n                () => {\n                  finalServerRenderController.abort()\n                },\n                { once: true }\n              )\n\n              const prerenderResult = await pendingPrerenderResult\n              prerenderIsPending = false\n\n              return prerenderResult\n            },\n            () => {\n              if (finalServerReactController.signal.aborted) {\n                // If the server controller is already aborted we must have called something\n                // that required aborting the prerender synchronously such as with new Date()\n                serverIsDynamic = true\n                return\n              }\n\n              if (prerenderIsPending) {\n                // If prerenderIsPending then we have blocked for longer than a Task and we assume\n                // there is something unfinished.\n                serverIsDynamic = true\n              }\n\n              finalServerReactController.abort()\n            }\n          )\n        ))\n\n      const clientDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n\n      const finalClientReactController = new AbortController()\n      const finalClientRenderController = new AbortController()\n\n      const finalClientPrerenderStore: PrerenderStore = {\n        type: 'prerender-client',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        renderSignal: finalClientRenderController.signal,\n        controller: finalClientReactController,\n        // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n        cacheSignal: null,\n        dynamicTracking: clientDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      let dynamicValidation = createDynamicValidationState()\n\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      let { prelude: unprocessedPrelude, postponed } =\n        await prerenderAndAbortInSequentialTasks(\n          () => {\n            const pendingFinalClientResult = workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                nonce={nonce}\n              />,\n              {\n                signal: finalClientReactController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientReactController.signal.aborted\n                  ) {\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore,\n                        componentStack,\n                        dynamicValidation,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    appendHeader(key, value)\n                  })\n                },\n                maxHeadersLength: reactMaxHeadersLength,\n                bootstrapScripts: [bootstrapScript],\n              }\n            )\n\n            // The listener to abort our own render controller must be added\n            // after React has added its listener, to ensure that pending I/O is\n            // not aborted/rejected too early.\n            finalClientReactController.signal.addEventListener(\n              'abort',\n              () => {\n                finalClientRenderController.abort()\n              },\n              { once: true }\n            )\n\n            return pendingFinalClientResult\n          },\n          () => {\n            finalClientReactController.abort()\n          }\n        )\n\n      const { prelude, preludeIsEmpty } =\n        await processPrelude(unprocessedPrelude)\n\n      // If we've disabled throwing on empty static shell, then we don't need to\n      // track any dynamic access that occurs above the suspense boundary because\n      // we'll do so in the route shell.\n      if (!allowEmptyStaticShell) {\n        throwIfDisallowedDynamic(\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n      metadata.flightData = flightData\n      metadata.segmentData = await collectSegmentData(\n        flightData,\n        finalServerPrerenderStore,\n        ComponentMod,\n        renderOpts\n      )\n\n      // If there are fallback route params, the RSC data is inherently dynamic\n      // today because it's encoded into the flight router state. Until we can\n      // move the fallback route params out of the flight router state, we need\n      // to always perform a dynamic resume after the static prerender.\n      const hasFallbackRouteParams =\n        fallbackRouteParams && fallbackRouteParams.size > 0\n\n      if (serverIsDynamic || hasFallbackRouteParams) {\n        // Dynamic case\n        // We will always need to perform a \"resume\" render of some kind when this route is accessed\n        // because the RSC data itself is dynamic. We determine if there are any HTML holes or not\n        // but generally this is a \"partial\" prerender in that there will be a per-request compute\n        // concatenated to the static shell.\n        if (postponed != null) {\n          // Dynamic HTML case\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            preludeIsEmpty\n              ? DynamicHTMLPreludeState.Empty\n              : DynamicHTMLPreludeState.Full,\n            fallbackRouteParams,\n            resumeDataCache\n          )\n        } else {\n          // Dynamic Data case\n          metadata.postponed =\n            await getDynamicDataPostponedState(resumeDataCache)\n        }\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      } else {\n        // Static case\n        // We will not perform resumption per request. The result can be served statically to the requestor\n        // and if there was anything dynamic it will only be rendered in the browser.\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createRenderInBrowserAbortSignal(),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      }\n    } else if (experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(isDebugDynamicAccesses)\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      }\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      const { prelude: unprocessedPrelude, postponed } =\n        await workUnitAsyncStorage.run(\n          ssrPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={reactServerResult.asUnclosingStream()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n          />,\n          {\n            onError: htmlRendererErrorHandler,\n            onHeaders: (headers: Headers) => {\n              headers.forEach((value, key) => {\n                appendHeader(key, value)\n              })\n            },\n            maxHeadersLength: reactMaxHeadersLength,\n            bootstrapScripts: [bootstrapScript],\n          }\n        )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      const { prelude, preludeIsEmpty } =\n        await processPrelude(unprocessedPrelude)\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            preludeIsEmpty\n              ? DynamicHTMLPreludeState.Empty\n              : DynamicHTMLPreludeState.Full,\n            fallbackRouteParams,\n            prerenderResumeDataCache\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createRenderInBrowserAbortSignal(),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = (\n        require('react-dom/server') as typeof import('react-dom/server')\n      ).renderToReadableStream\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: selectStaleTime(prerenderLegacyStore.stale),\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      rootParams,\n      implicitTags: implicitTags,\n      revalidate:\n        typeof prerenderStore?.revalidate !== 'undefined'\n          ? prerenderStore.revalidate\n          : INFINITE_CACHE,\n      expire:\n        typeof prerenderStore?.expire !== 'undefined'\n          ? prerenderStore.expire\n          : INFINITE_CACHE,\n      stale:\n        typeof prerenderStore?.stale !== 'undefined'\n          ? prerenderStore.stale\n          : INFINITE_CACHE,\n      tags: [...(prerenderStore?.tags || implicitTags.tags)],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? undefined : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      // TODO we should use the same prerender semantics that we initially rendered\n      // with in this case too. The only reason why this is ok atm is because it's essentially\n      // an empty page and no user code runs.\n      const fizzStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={nonce}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream = reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          getServerInsertedMetadata,\n          validateRootLayout: dev,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale: selectStaleTime(\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE\n        ),\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<{\n  GlobalError: GlobalErrorComponent\n  styles: React.ReactNode | undefined\n}> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  const GlobalErrorComponent: GlobalErrorComponent =\n    ctx.componentMod.GlobalError\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n  if (ctx.renderOpts.dev) {\n    const dir =\n      (process.env.NEXT_RUNTIME === 'edge'\n        ? process.env.__NEXT_EDGE_PROJECT_DIR\n        : ctx.renderOpts.dir) || ''\n\n    const globalErrorModulePath = normalizeConventionFilePath(\n      dir,\n      globalErrorModule?.[1]\n    )\n    if (ctx.renderOpts.devtoolSegmentExplorer && globalErrorModulePath) {\n      const SegmentViewNode = ctx.componentMod.SegmentViewNode\n      globalErrorStyles = (\n        // This will be rendered next to GlobalError component under ErrorBoundary,\n        // it requires a key to avoid React warning about duplicate keys.\n        <SegmentViewNode\n          key=\"ge-svn\"\n          type=\"global-error\"\n          pagePath={globalErrorModulePath}\n        >\n          {globalErrorStyles}\n        </SegmentViewNode>\n      )\n    }\n  }\n\n  return {\n    GlobalError: GlobalErrorComponent,\n    styles: globalErrorStyles,\n  }\n}\n\nfunction createSelectStaleTime(experimental: ExperimentalConfig) {\n  return (stale: number) =>\n    stale === INFINITE_CACHE &&\n    typeof experimental.staleTimes?.static === 'number'\n      ? experimental.staleTimes.static\n      : stale\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (\n    !clientReferenceManifest ||\n    // Do not generate per-segment data unless the experimental Segment Cache\n    // flag is enabled.\n    //\n    // We also skip generating segment data if flag is set to \"client-only\",\n    // rather than true. (The \"client-only\" option only affects the behavior of\n    // the client-side implementation; per-segment prefetches are intentionally\n    // disabled in that configuration).\n    renderOpts.experimental.clientSegmentCache !== true\n  ) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: getServerModuleMap(),\n  }\n\n  const staleTime = prerenderStore.stale\n  return await ComponentMod.collectSegmentData(\n    renderOpts.experimental.clientParamParsing,\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest\n  )\n}\n"], "names": ["renderToHTMLOrFlight", "flightDataPathHeadKey", "getFlightViewportKey", "requestId", "getFlightMetadataKey", "filterStackFrame", "process", "env", "NODE_ENV", "require", "filterStackFrameDEV", "undefined", "parseRequestHeaders", "headers", "options", "isDevWarmupRequest", "isDevWarmup", "isPrefetchRequest", "NEXT_ROUTER_PREFETCH_HEADER", "isRuntimePrefetchRequest", "isHmrRefresh", "NEXT_HMR_REFRESH_HEADER", "isRSCRequest", "RSC_HEADER", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "parseAndValidateFlightRouterState", "NEXT_ROUTER_STATE_TREE_HEADER", "isRouteTreePrefetchRequest", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "csp", "nonce", "getScriptNonceFromHeader", "previouslyRevalidatedTags", "getPreviouslyRevalidatedTags", "previewModeId", "createNotFoundLoaderTree", "loaderTree", "components", "hasGlobalNotFound", "children", "PAGE_SEGMENT_KEY", "page", "makeGetDynamicParamFromSegment", "params", "pagePath", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "getSegmentParam", "segmentKey", "param", "dynamicParamType", "dynamicParamTypes", "type", "getDynamicParam", "NonIndex", "statusCode", "isPossibleServerAction", "is404Page", "isInvalidStatusCode", "meta", "name", "content", "generateDynamicRSCPayload", "ctx", "flightData", "componentMod", "tree", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "appUsingSizeAdjustment", "query", "workStore", "url", "serveStreamingMetadata", "renderOpts", "skipFlight", "preloadCallbacks", "ViewportTree", "MetadataTree", "getViewportReady", "getMetadataReady", "StreamingMetadataOutlet", "parsed<PERSON><PERSON><PERSON>", "pathname", "metadataContext", "createMetadataContext", "walkTreeWithFlightRouterState", "loaderTreeToFilter", "parentParams", "rscHead", "React", "Fragment", "res", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "map", "path", "slice", "actionResult", "a", "f", "b", "sharedContext", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "onFlightDataRenderError", "err", "onInstrumentationRequestError", "onError", "createFlightReactServerErrorHandler", "dev", "RSCPayload", "workUnitAsyncStorage", "run", "flightReadableStream", "renderToReadableStream", "clientReferenceManifest", "clientModules", "temporaryReferences", "FlightRenderResult", "fetchMetrics", "generateRuntimePrefetchResult", "metadata", "generatePayload", "rootParams", "getRootParams", "prerenderResumeDataCache", "createPrerenderResumeDataCache", "renderResumeDataCache", "prospectiveRuntimeServerPrerender", "cookies", "draftMode", "response", "finalRuntimeServerPrerender", "applyMetadataFromPrerenderResult", "isPartial", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_DID_POSTPONE_HEADER", "result", "prelude", "getPayload", "implicitTags", "ComponentMod", "assertClientReferenceManifest", "initialServerPrerenderController", "AbortController", "initialServerRenderController", "cacheSignal", "CacheSignal", "initialServerPrerenderStore", "phase", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "expire", "stale", "INFINITE_CACHE", "tags", "hmrRefreshHash", "captureOwnerStack", "runtimeStagePromise", "initialServerPayload", "pendingInitialServerResult", "prerender", "digest", "getDigestForWellKnownError", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "printDebugThrownValueForProspectiveRender", "route", "onPostpone", "trackPendingModules", "cacheReady", "abort", "invalidDynamicUsageError", "createReactServerPrerenderResult", "experimental", "isDebugDynamicAccesses", "selectStaleTime", "createSelectStaleTime", "serverIsDynamic", "finalServerController", "serverDynamicTracking", "createDynamicTrackingState", "promise", "resolve", "resolveBlockedRuntimeAPIs", "createPromiseWithResolvers", "finalServerPrerenderStore", "finalRSCPayload", "prerenderIsPending", "prerenderAndAbortInSequentialTasksWithStages", "prerenderResult", "warnOnSyncDynamicError", "dynamicAccess", "collectedRevalidate", "collectedExpire", "collectedStale", "collectedTags", "warmupDevRender", "allowEmptyStaticShell", "InvariantError", "renderController", "prerenderController", "reactController", "prerenderStore", "NEXT_HMR_REFRESH_HASH_COOKIE", "rscPayload", "createRenderResumeDataCache", "prepareInitialCanonicalUrl", "search", "split", "getRSCPayload", "is404", "missingSlots", "initialTree", "createFlightRouterStateFromLoaderTree", "errorType", "seedData", "createComponentTree", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "NEXT_URL", "initialHead", "GlobalError", "styles", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "p", "assetPrefix", "c", "i", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "ssrError", "isError", "Error", "html", "id", "head", "body", "template", "data-next-error-message", "message", "data-next-error-digest", "data-next-error-stack", "stack", "App", "reactServerStream", "preinitScripts", "ServerInsertedHTMLProvider", "use", "useFlightStream", "initialState", "createInitialRouterState", "navigatedAt", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "Map", "location", "prerendered", "actionQueue", "createMutableActionQueue", "HeadManagerContext", "Provider", "value", "appDir", "AppRouter", "globalErrorState", "ErrorApp", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "nextFontManifest", "serverActions", "enableTainting", "__next_app__", "instrumented", "wrapClientComponentLoader", "shouldTrackModuleLoading", "cacheComponents", "workUnitStore", "getStore", "__next_require__", "args", "exportsOrPromise", "trackPendingImport", "globalThis", "__next_chunk_load__", "loadingChunk", "loadChunk", "trackPendingChunkLoad", "URL", "setIsrStatus", "NEXT_RUNTIME", "isNodeNextRequest", "onClose", "shouldTrackFetchMetrics", "originalRequest", "on", "metrics", "getClientComponentLoaderMetrics", "reset", "getTracer", "startSpan", "NextNodeServerSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "appUsingSizeAdjust", "serverModuleMap", "createServerModuleMap", "setReferenceManifestsSingleton", "patchFetch", "taintObjectReference", "stripInternalQueries", "<PERSON><PERSON><PERSON>", "from", "crypto", "subtle", "toString", "randomUUID", "nanoid", "isPossibleActionRequest", "getIsPossibleServerAction", "getImplicitTags", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "AppRenderSpan", "getBodyResult", "spanName", "prerenderToStream", "accessedDynamicData", "warn", "access", "formatDynamicAPIAccesses", "logDisallowedDynamicError", "StaticGenBailoutError", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "length", "find", "isUserLandError", "contentType", "HTML_CONTENT_TYPE_HEADER", "pendingRevalidates", "pendingRevalidateWrites", "pendingRevalidatedTags", "pendingPromise", "executeRevalidates", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "waitUntil", "RenderResult", "streamToString", "stream", "devValidatingFallbackParams", "getRequestMeta", "createRequestStoreForRender", "onUpdateCookies", "previewProps", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "formState", "actionRequestResult", "handleAction", "generateFlight", "notFoundLoaderTree", "assignMetadata", "parseRelativeUrl", "parsePostponedState", "createWorkStore", "routeModule", "definition", "workAsyncStorage", "fetchTags", "join", "staleHeader", "String", "NEXT_ROUTER_STALE_TIME_HEADER", "forceStatic", "cacheControl", "staticBailoutInfo", "description", "dynamicUsageDescription", "dynamicUsageStack", "basePath", "buildManifest", "crossOrigin", "nextExport", "reactMaxHeadersLength", "shouldWaitOnAllReady", "subresourceIntegrityManifest", "supportsDynamicResponse", "renderServerInsertedHTML", "createServerInsertedHTML", "getServerInsertedMetadata", "createServerInsertedMetadata", "tracingMetadata", "getTracedMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "getAssetQueryString", "integrity", "noModule", "bootstrapScript", "getRequiredScripts", "reactServerErrorsByDigest", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "createHTMLReactServerErrorHandler", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "createHTMLErrorHandler", "reactServerResult", "bind", "append<PERSON><PERSON>er", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "scheduleInSequentialTasks", "prerenderPhase", "environmentName", "spawnDynamicValidationInDev", "ReactServerResult", "waitAtLeastOneReactRenderTask", "DynamicState", "DATA", "inlinedReactServerDataStream", "createInlinedDataReadableStream", "tee", "chainStreams", "createDocumentClosingStream", "preludeState", "getPostponedFromState", "resume", "htmlStream", "getServerInsertedHTML", "makeGetServerInsertedHTML", "serverCapturedErrors", "continueDynamicHTMLResume", "delayDataUntilFirstHtmlChunk", "DynamicHTMLPreludeState", "Empty", "inlinedDataStream", "consume", "onHeaders", "key", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bootstrapScripts", "generateStaticHTML", "continueFizzStream", "isBuildTimePrerendering", "validateRootLayout", "isStaticGenBailoutError", "shouldBailoutToCSR", "isBailoutToCSRError", "getStackWithoutErrorMessage", "error", "reason", "isHTTPAccessFallbackError", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isRedirectError", "getRedirectStatusCodeFromError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "Headers", "appendMutableCookies", "mutableCookies", "Array", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "has", "errorServerStream", "fizzStream", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "outlet", "Promise", "isNotFound", "get", "initialServerReactController", "captureOwnerStackClient", "captureOwnerStackServer", "initialServerPayloadPrerenderStore", "isReactLargeShellError", "addEventListener", "once", "LogSafely", "fn", "initialServerResult", "initialClientPrerenderController", "initialClientReactController", "initialClientRenderController", "initialClientPrerenderStore", "pendingInitialClientResult", "asUnclosingStream", "catch", "isPrerenderInterruptedError", "finalServerReactController", "finalServerRenderController", "finalServerPayloadPrerenderStore", "finalAttemptRSCPayload", "prerenderAndAbortInSequentialTasks", "pendingPrerenderResult", "clientDynamicTracking", "finalClientReactController", "finalClientRenderController", "finalClientPrerenderStore", "dynamicValidation", "createDynamicValidationState", "unprocessedPrelude", "pendingFinalClientResult", "errorInfo", "componentStack", "trackAllowedDynamicAccess", "preludeIsEmpty", "processPrelude", "throwIfDisallowedDynamic", "PreludeState", "Full", "thrownValue", "loggingFunction", "Errored", "originalLoggingFunction", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "setMetadataHeader", "isArray", "item", "resumeDataCache", "streamToBuffer", "asStream", "segmentData", "collectSegmentData", "hasFallbackRouteParams", "getDynamicHTMLPostponedState", "getDynamicDataPostponedState", "continueDynamicPrerender", "consumeDynamicAccess", "foreverStream", "ReadableStream", "resumeStream", "JSON", "parse", "stringify", "createRenderInBrowserAbortSignal", "continueStaticP<PERSON><PERSON>", "consumeAsStream", "reactServerPrerenderStore", "createReactServerPrerenderResultFromRender", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "isDynamicServerError", "flightStream", "modules", "globalErrorModule", "parseLoaderTree", "GlobalErrorComponent", "createComponentStylesAndScripts", "filePath", "getComponent", "dir", "__NEXT_EDGE_PROJECT_DIR", "globalErrorModulePath", "normalizeConventionFilePath", "devtoolSegmentExplorer", "SegmentViewNode", "staleTimes", "static", "fullPageDataBuffer", "clientSegmentCache", "isEdgeRuntime", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "getServerModuleMap", "staleTime", "clientParamParsing"], "mappings": ";;;;+BA85DaA;;;eAAAA;;;;0CA94DN;8DAgByC;qEAKzC;sCAWA;+BAC8B;kCAW9B;iCAC+B;8BACM;2BACZ;oCAKzB;0BAIA;+BACyB;8BACmB;2BACD;wBACxB;oCACS;oCAQ5B;0CAC2B;iCACF;0CACS;mDACS;uDACI;+BACzB;8BACO;qBACR;gCACS;oCACI;iCACN;+BACL;2CACY;+CACI;qCACK;qCACf;iCAI7B;gCAMA;oCAM8B;mCAI9B;yCAIA;mCACqC;kCAerC;+CAIA;6BAC+B;yBACJ;kCACD;kEACX;yCAGoB;0CACD;mCACA;uBACL;yBACH;yCAK1B;wCAQmD;sCAChB;2BACI;8CAIvC;6BACqB;wBACM;gCACH;4BAE0B;iDACT;iCAChB;iCAMzB;gEAEa;8CACyB;6BACA;mCACV;4CAK5B;sCACgC;qCAEK;6BACb;iCACC;sCAGW;;;;;;AAuD3C,MAAMC,wBAAwB;AAC9B,MAAMC,uBAAuB,CAACC,YAAsBA,YAAY;AAChE,MAAMC,uBAAuB,CAACD,YAAsBA,YAAY;AAEhE,MAAME,mBACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBC;AAoBN,SAASC,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,MAAMC,qBAAqBD,QAAQE,WAAW,KAAK;IAEnD,2DAA2D;IAC3D,mEAAmE;IACnE,4EAA4E;IAC5E,MAAMC,oBACJF,sBAAsBF,OAAO,CAACK,6CAA2B,CAAC,KAAK;IAEjE,MAAMC,2BAA2BN,OAAO,CAACK,6CAA2B,CAAC,KAAK;IAE1E,MAAME,eAAeP,OAAO,CAACQ,yCAAuB,CAAC,KAAKV;IAE1D,2DAA2D;IAC3D,MAAMW,eAAeP,sBAAsBF,OAAO,CAACU,4BAAU,CAAC,KAAKZ;IAEnE,MAAMa,iCACJF,gBAAiB,CAAA,CAACL,qBAAqB,CAACH,QAAQW,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBG,IAAAA,oEAAiC,EAACd,OAAO,CAACe,+CAA6B,CAAC,IACxEjB;IAEJ,sEAAsE;IACtE,MAAMkB,6BACJhB,OAAO,CAACiB,qDAAmC,CAAC,KAAK;IAEnD,MAAMC,MACJlB,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMmB,QACJ,OAAOD,QAAQ,WAAWE,IAAAA,kDAAwB,EAACF,OAAOpB;IAE5D,MAAMuB,4BAA4BC,IAAAA,yCAA4B,EAC5DtB,SACAC,QAAQsB,aAAa;IAGvB,OAAO;QACLV;QACAT;QACAE;QACAU;QACAT;QACAE;QACAP;QACAiB;QACAE;IACF;AACF;AAEA,SAASG,yBAAyBC,UAAsB;IACtD,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,MAAME,oBAAoB,CAAC,CAACD,UAAU,CAAC,mBAAmB;IAC1D,OAAO;QACL;QACA;YACEE,UAAU;gBACRC,yBAAgB;gBAChB,CAAC;gBACD;oBACEC,MAAMJ,UAAU,CAAC,mBAAmB,IAAIA,UAAU,CAAC,YAAY;gBACjE;aACD;QACH;QACA,gEAAgE;QAChEC,oBAAoBD,aAAa,CAAC;KACnC;AACH;AAEA;;CAEC,GACD,SAASK,+BACPC,MAA8B,EAC9BC,QAAgB,EAChBC,mBAA+C;IAE/C,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAeC,IAAAA,gCAAe,EAACF;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QACA,MAAME,aAAaF,aAAaG,KAAK;QACrC,MAAMC,mBAAmBC,2CAAiB,CAACL,aAAaM,IAAI,CAAC;QAC7D,OAAOC,IAAAA,gCAAe,EACpBZ,QACAO,YACAE,kBACAR,UACAC;IAEJ;AACF;AAEA,SAASW,SAAS,EAChBZ,QAAQ,EACRa,UAAU,EACVC,sBAAsB,EAKvB;IACC,MAAMC,YAAYf,aAAa;IAC/B,MAAMgB,sBAAsB,OAAOH,eAAe,YAAYA,aAAa;IAE3E,gEAAgE;IAChE,yEAAyE;IACzE,IAAI,CAACC,0BAA2BC,CAAAA,aAAaC,mBAAkB,GAAI;QACjE,qBAAO,qBAACC;YAAKC,MAAK;YAASC,SAAQ;;IACrC;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbC,GAAqB,EACrBrD,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAIsD,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,MAAMhC,UAAU,EAChBiC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDzB,0BAA0B,EAC1B0B,sBAAsB,EACtBC,KAAK,EACLxE,SAAS,EACTuB,iBAAiB,EACjBkD,SAAS,EACTC,GAAG,EACJ,GAAGV;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IAEtE,IAAI,EAAChE,2BAAAA,QAASkE,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAM,EACJC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;YAC3BD,MAAMhC;YACNiD,aAAaZ;YACba,UAAUX,IAAIW,QAAQ;YACtBC,iBAAiBC,IAAAA,sCAAqB,EAACvB,IAAIY,UAAU;YACrD/B;YACA0B;YACAE;YACAJ;YACAC;YACAK;QACF;QAEAV,aAAa,AACX,CAAA,MAAMuB,IAAAA,4DAA6B,EAAC;YAClCxB;YACAyB,oBAAoBtD;YACpBuD,cAAc,CAAC;YACfnE;YACA,+CAA+C;YAC/CoE,uBACE,sBAACC,cAAK,CAACC,QAAQ;;kCAEb,qBAACtC;wBACCZ,UAAUqB,IAAIrB,QAAQ;wBACtBa,YAAYQ,IAAI8B,GAAG,CAACtC,UAAU;wBAC9BC,wBAAwBO,IAAIP,sBAAsB;;kCAGpD,qBAACsB,kBAAkBhF,qBAAqBC;kCACxC,qBAACgF,kBAAkB/E,qBAAqBD;;eATrBF;YAYvBiG,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBlB;YACAC;YACAJ;YACAK;QACF,EAAC,EACDiB,GAAG,CAAC,CAACC,OAASA,KAAKC,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAI3F,2BAAAA,QAAS4F,YAAY,EAAE;QACzB,OAAO;YACLC,GAAG7F,QAAQ4F,YAAY;YACvBE,GAAGxC;YACHyC,GAAG1C,IAAI2C,aAAa,CAACC,OAAO;QAC9B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLF,GAAG1C,IAAI2C,aAAa,CAACC,OAAO;QAC5BH,GAAGxC;QACH4C,GAAGpC,UAAUqC,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACP/C,GAAqB,EACrBgD,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAWlD,IAAIrB,QAAQ;QACvB,yEAAyE;QACzEwE,WAAWnD,IAAIP,sBAAsB,GAAG,WAAW;QACnDuD;QACAI,kBAAkBC,IAAAA,0BAAmB,EAACrD,IAAIS,SAAS;IACrD;AACF;AAEA;;;CAGC,GACD,eAAe6C,kCACbC,GAAoB,EACpBvD,GAAqB,EACrBwD,YAA0B,EAC1B7G,OAMC;IAED,MAAMiE,aAAaZ,IAAIY,UAAU;IAEjC,SAAS6C,wBAAwBC,GAAkB;QACjD,OAAO9C,WAAW+C,6BAA6B,oBAAxC/C,WAAW+C,6BAA6B,MAAxC/C,YACL8C,KACAH,KACAR,mBAAmB/C,KAAK;IAE5B;IACA,MAAM4D,UAAUC,IAAAA,uDAAmC,EACjD,CAAC,CAACjD,WAAWkD,GAAG,EAChBL;IAGF,MAAMM,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACAzD,2BACAC,KACArD;IAGF,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMuH,uBAAuBF,kDAAoB,CAACC,GAAG,CACnDT,cACAxD,IAAIE,YAAY,CAACiE,sBAAsB,EACvCJ,YACA/D,IAAIoE,uBAAuB,CAACC,aAAa,EACzC;QACET;QACAU,mBAAmB,EAAE3H,2BAAAA,QAAS2H,mBAAmB;QACjDpI;IACF;IAGF,OAAO,IAAIqI,sCAAkB,CAACL,sBAAsB;QAClDM,cAAcxE,IAAIS,SAAS,CAAC+D,YAAY;IAC1C;AACF;AAEA,eAAeC,8BACblB,GAAoB,EACpBzB,GAAqB,EACrB9B,GAAqB,EACrBwD,YAA0B;IAE1B,MAAM,EAAE/C,SAAS,EAAE,GAAGT;IACtB,MAAMY,aAAaZ,IAAIY,UAAU;IAEjC,SAAS6C,wBAAwBC,GAAkB;QACjD,OAAO9C,WAAW+C,6BAA6B,oBAAxC/C,WAAW+C,6BAA6B,MAAxC/C,YACL8C,KACAH,KACA,sDAAsD;QACtDR,mBAAmB/C,KAAK;IAE5B;IACA,MAAM4D,UAAUC,IAAAA,uDAAmC,EACjD,OACAJ;IAGF,MAAMiB,WAAwC,CAAC;IAE/C,MAAMC,kBAAkB,IAAM5E,0BAA0BC,KAAKxD;IAE7D,MAAM,EACJ0D,cAAc,EAAEC,IAAI,EAAE,EACtBtB,0BAA0B,EAC3B,GAAGmB;IACJ,MAAM4E,aAAaC,IAAAA,kCAAa,EAAC1E,MAAMtB;IAEvC,qFAAqF;IACrF,gDAAgD;IAChD,MAAMiG,2BAA2BC,IAAAA,+CAA8B;IAC/D,yCAAyC;IACzC,MAAMC,wBAAwB;IAE9B,MAAMC,kCACJjF,KACA2E,iBACAG,0BACAE,uBACAJ,YACApB,aAAa0B,OAAO,EACpB1B,aAAa2B,SAAS;IAGxB,MAAMC,WAAW,MAAMC,4BACrBrF,KACA2E,iBACAG,0BACAE,uBACAJ,YACApB,aAAa0B,OAAO,EACpB1B,aAAa2B,SAAS,EACtBvB;IAGF0B,iCAAiCF,UAAUV,UAAUjE;IACrDiE,SAASF,YAAY,GAAGxE,IAAIS,SAAS,CAAC+D,YAAY;IAElD,IAAIY,SAASG,SAAS,EAAE;QACtBzD,IAAI0D,SAAS,CAACC,0CAAwB,EAAE;IAC1C;IAEA,OAAO,IAAIlB,sCAAkB,CAACa,SAASM,MAAM,CAACC,OAAO,EAAEjB;AACzD;AAEA,eAAeO,kCACbjF,GAAqB,EACrB4F,UAAqB,EACrBd,wBAAyD,EACzDE,qBAAmD,EACnDJ,UAAkB,EAClBM,OAA+C,EAC/CC,SAAmD;IAEnD,MAAM,EAAEU,YAAY,EAAEjF,UAAU,EAAEH,SAAS,EAAE,GAAGT;IAEhD,MAAM,EAAEoE,uBAAuB,EAAE0B,YAAY,EAAE,GAAGlF;IAElDmF,8BAA8B3B;IAE9B,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAM4B,mCAAmC,IAAIC;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAMC,gCAAgC,IAAID;IAE1C,kFAAkF;IAClF,yBAAyB;IACzB,MAAME,cAAc,IAAIC,wBAAW;IAEnC,MAAMC,8BAA2D;QAC/DhH,MAAM;QACNiH,OAAO;QACP1B;QACAiB;QACAU,cAAcL,8BAA8BM,MAAM;QAClDC,YAAYT;QACZ,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBG;QACA,qEAAqE;QACrEO,iBAAiB;QACjB,qEAAqE;QACrE,4EAA4E;QAC5EC,YAAY;QACZC,QAAQ;QACRC,OAAOC,0BAAc;QACrBC,MAAM;eAAIlB,aAAakB,IAAI;SAAC;QAC5B/B;QACAF;QACAkC,gBAAgBxK;QAChByK,mBAAmBzK;QACnB,uDAAuD;QACvD0K,qBAAqB;QACrB,mFAAmF;QACnFhC;QACAC;IACF;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAMgC,uBAAuB,MAAMnD,kDAAoB,CAACC,GAAG,CACzDoC,6BACAT;IAGF,MAAMwB,6BAA6BpD,kDAAoB,CAACC,GAAG,CACzDoC,6BACAP,aAAauB,SAAS,EACtBF,sBACA/C,wBAAwBC,aAAa,EACrC;QACEnI;QACA0H,SAAS,CAACF;YACR,MAAM4D,SAASC,IAAAA,8CAA0B,EAAC7D;YAE1C,IAAI4D,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAItB,iCAAiCQ,MAAM,CAACgB,OAAO,EAAE;gBACnD,mEAAmE;gBACnE,iEAAiE;gBACjE;YACF,OAAO,IACLrL,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;gBACAC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;YAChE;QACF;QACA,iFAAiF;QACjF,qCAAqC;QACrCC,YAAYrL;QACZ,+EAA+E;QAC/E,iFAAiF;QACjF,iDAAiD;QACjDgK,QAAQN,8BAA8BM,MAAM;IAC9C;IAGF,8EAA8E;IAC9EsB,IAAAA,+CAAmB,EAAC3B;IACpB,MAAMA,YAAY4B,UAAU;IAE5B7B,8BAA8B8B,KAAK;IACnChC,iCAAiCgC,KAAK;IAEtC,gEAAgE;IAChE,iEAAiE;IACjE,IAAIvH,UAAUwH,wBAAwB,EAAE;QACtC,MAAMxH,UAAUwH,wBAAwB;IAC1C;IAEA,IAAI;QACF,OAAO,MAAMC,IAAAA,yDAAgC,EAACd;IAChD,EAAE,OAAO1D,KAAK;QACZ,IACEwC,8BAA8BM,MAAM,CAACgB,OAAO,IAC5CxB,iCAAiCQ,MAAM,CAACgB,OAAO,EAC/C;QACA,4EAA4E;QAC9E,OAAO,IACLrL,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnFC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;QAChE;QACA,OAAO;IACT;AACF;AAEA,eAAevC,4BACbrF,GAAqB,EACrB4F,UAAqB,EACrBd,wBAAyD,EACzDE,qBAAmD,EACnDJ,UAAkB,EAClBM,OAA+C,EAC/CC,SAAmD,EACnDvB,OAA6C;IAE7C,MAAM,EAAEiC,YAAY,EAAEjF,UAAU,EAAE,GAAGZ;IAErC,MAAM,EACJoE,uBAAuB,EACvB0B,YAAY,EACZqC,YAAY,EACZC,sBAAsB,EACvB,GAAGxH;IAEJmF,8BAA8B3B;IAE9B,MAAMiE,kBAAkBC,sBAAsBH;IAE9C,IAAII,kBAAkB;IACtB,MAAMC,wBAAwB,IAAIvC;IAElC,MAAMwC,wBAAwBC,IAAAA,4CAA0B,EACtDN;IAGF,MAAM,EAAEO,SAASzB,mBAAmB,EAAE0B,SAASC,yBAAyB,EAAE,GACxEC,IAAAA,gDAA0B;IAE5B,MAAMC,4BAAyD;QAC7D1J,MAAM;QACNiH,OAAO;QACP1B;QACAiB;QACAU,cAAciC,sBAAsBhC,MAAM;QAC1CC,YAAY+B;QACZ,8EAA8E;QAC9ErC,aAAa;QACbO,iBAAiB+B;QACjB,qEAAqE;QACrE,4EAA4E;QAC5E9B,YAAY;QACZC,QAAQ;QACRC,OAAOC,0BAAc;QACrBC,MAAM;eAAIlB,aAAakB,IAAI;SAAC;QAC5BjC;QACAE;QACAgC,gBAAgBxK;QAChByK,mBAAmBzK;QACnB,gEAAgE;QAChE0K;QACA,mFAAmF;QACnFhC;QACAC;IACF;IAEA,MAAM6D,kBAAkB,MAAMhF,kDAAoB,CAACC,GAAG,CACpD8E,2BACAnD;IAGF,IAAIqD,qBAAqB;IACzB,MAAMvD,SAAS,MAAMwD,IAAAA,qEAA4C,EAC/D;QACE,eAAe;QACf,MAAMC,kBAAkB,MAAMnF,kDAAoB,CAACC,GAAG,CACpD8E,2BACAjD,aAAauB,SAAS,EACtB2B,iBACA5E,wBAAwBC,aAAa,EACrC;YACEnI;YACA0H;YACA4C,QAAQgC,sBAAsBhC,MAAM;QACtC;QAEFyC,qBAAqB;QACrB,OAAOE;IACT,GACA;QACE,gCAAgC;QAChC,EAAE;QACF,0GAA0G;QAC1G,kHAAkH;QAClH,mGAAmG;QACnG,+FAA+F;QAC/F,sFAAsF;QACtFN;IACF,GACA;QACE,SAAS;QACT,IAAIL,sBAAsBhC,MAAM,CAACgB,OAAO,EAAE;YACxC,4EAA4E;YAC5E,6EAA6E;YAC7Ee,kBAAkB;YAClB;QACF;QAEA,IAAIU,oBAAoB;YACtB,kFAAkF;YAClF,iCAAiC;YACjCV,kBAAkB;QACpB;QACAC,sBAAsBR,KAAK;IAC7B;IAGFoB,IAAAA,wCAAsB,EAACX;IAEvB,OAAO;QACL/C;QACA,8DAA8D;QAC9D,wBAAwB;QACxB2D,eAAeZ;QACflD,WAAWgD;QACXe,qBAAqBP,0BAA0BpC,UAAU;QACzD4C,iBAAiBR,0BAA0BnC,MAAM;QACjD4C,gBAAgBnB,gBAAgBU,0BAA0BlC,KAAK;QAC/D4C,eAAeV,0BAA0BhC,IAAI;IAC/C;AACF;AAEA;;;;;;CAMC,GACD,eAAe2C,gBACbnG,GAAoB,EACpBvD,GAAqB;IAErB,MAAM,EACJoE,uBAAuB,EACvBlE,cAAc4F,YAAY,EAC1BjH,0BAA0B,EAC1BgH,YAAY,EACZjF,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EACJ2J,wBAAwB,KAAK,EAC7B7F,GAAG,EACHH,6BAA6B,EAC9B,GAAG/C;IAEJ,IAAI,CAACkD,KAAK;QACR,MAAM,qBAEL,CAFK,IAAI8F,8BAAc,CACtB,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMhF,aAAaC,IAAAA,kCAAa,EAC9BiB,aAAa3F,IAAI,EACjBtB;IAGF,SAAS4E,wBAAwBC,GAAkB;QACjD,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB/C,KAAK;IAE5B;IACA,MAAM4D,UAAUC,IAAAA,uDAAmC,EACjD,MACAJ;IAGF,2EAA2E;IAC3E,kBAAkB;IAClB,MAAMqB,2BAA2BC,IAAAA,+CAA8B;IAE/D,MAAM8E,mBAAmB,IAAI5D;IAC7B,MAAM6D,sBAAsB,IAAI7D;IAChC,MAAM8D,kBAAkB,IAAI9D;IAC5B,MAAME,cAAc,IAAIC,wBAAW;IAEnC,MAAM4D,iBAAiC;QACrC3K,MAAM;QACNiH,OAAO;QACP1B;QACAiB;QACAU,cAAcsD,iBAAiBrD,MAAM;QACrCC,YAAYqD;QACZ3D;QACAO,iBAAiB;QACjBiD;QACAhD,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM,EAAE;QACRjC;QACAE,uBAAuB;QACvBgC,gBAAgBzD,IAAI2B,OAAO,CAAC+E,8CAA4B,CAAC;QACzDhD,mBAAmBnB,aAAamB,iBAAiB;QACjD,sEAAsE;QACtE,wEAAwE;QACxE,6DAA6D;QAC7DrI,qBAAqB;IACvB;IAEA,MAAMsL,aAAa,MAAMlG,kDAAoB,CAACC,GAAG,CAC/C+F,gBACAjK,2BACAC;IAGF,0FAA0F;IAC1F,mCAAmC;IACnCgE,kDAAoB,CAACC,GAAG,CACtB+F,gBACAlE,aAAa3B,sBAAsB,EACnC+F,YACA9F,wBAAwBC,aAAa,EACrC;QACEnI;QACA0H;QACA4C,QAAQqD,iBAAiBrD,MAAM;IACjC;IAGF,8EAA8E;IAC9EsB,IAAAA,+CAAmB,EAAC3B;IACpB,MAAMA,YAAY4B,UAAU;IAE5B,uFAAuF;IACvFiC,eAAelF,wBAAwB,GAAG;IAC1C,mBAAmB;IACnBiF,gBAAgB/B,KAAK;IACrB6B,iBAAiB7B,KAAK;IAEtB,0EAA0E;IAC1E,+EAA+E;IAC/E,+EAA+E;IAC/E,OAAO,IAAIzD,sCAAkB,CAAC,IAAI;QAChCC,cAAc/D,UAAU+D,YAAY;QACpCQ,uBAAuBmF,IAAAA,4CAA2B,EAChDrF;IAEJ;AACF;AAEA;;;;;CAKC,GACD,SAASsF,2BAA2B1J,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIW,QAAQ,GAAGX,IAAI2J,MAAM,AAAD,EAAGC,KAAK,CAAC;AAC3C;AAEA,wFAAwF;AACxF,eAAeC,cACbpK,IAAgB,EAChBH,GAAqB,EACrBwK,KAAc;IAEd,MAAMzI,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAIyI;IAEJ,sDAAsD;IACtD,IAAItO,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CoO,eAAe,IAAIzI;IACrB;IAEA,MAAM,EACJnD,0BAA0B,EAC1B2B,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZE,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAM0K,cAAcC,IAAAA,4EAAqC,EACvDxK,MACAtB,4BACA2B;IAEF,MAAMG,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAMtC,oBAAoB,CAAC,CAAC8B,IAAI,CAAC,EAAE,CAAC,mBAAmB;IAEvD,MAAM,EACJY,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;QAC3BD;QACA,6FAA6F;QAC7F,0BAA0B;QAC1B,wFAAwF;QACxF,2CAA2C;QAC3C,yFAAyF;QACzFyK,WAAWJ,SAAS,CAACnM,oBAAoB,cAAc7B;QACvD4E,aAAaZ;QACba,UAAUX,IAAIW,QAAQ;QACtBC,iBAAiBC,IAAAA,sCAAqB,EAACvB,IAAIY,UAAU;QACrD/B;QACA0B;QACAE;QACAJ;QACAC;QACAK;IACF;IAEA,MAAMG,mBAAqC,EAAE;IAE7C,MAAM+J,WAAW,MAAMC,IAAAA,wCAAmB,EAAC;QACzC9K;QACA7B,YAAYgC;QACZuB,cAAc,CAAC;QACfK;QACAE;QACAC;QACAC,oBAAoB;QACpBlB;QACAC;QACAuJ;QACA3J;QACAiK,gBAAgB/K,IAAIY,UAAU,CAACuH,YAAY,CAAC4C,cAAc;QAC1D5J;IACF;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAM6J,aAAahL,IAAI8B,GAAG,CAACmJ,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAACC,0BAAQ;IAEhE,MAAMC,4BACJ,sBAACzJ,cAAK,CAACC,QAAQ;;0BACb,qBAACtC;gBACCZ,UAAUqB,IAAIrB,QAAQ;gBACtBa,YAAYQ,IAAI8B,GAAG,CAACtC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,qBAACsB;0BACD,qBAACC;;OAPkBlF;IAWvB,MAAM,EAAEwP,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvDtL,MACAH;IAGF,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAM0L,wBACJjL,UAAUqC,kBAAkB,IAC5B9C,IAAIY,UAAU,CAACuH,YAAY,CAAC7K,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FqO,iBAAG,qBAACC;YAAS9K,kBAAkBA;;QAC/B4B,GAAG1C,IAAI2C,aAAa,CAACC,OAAO;QAC5BiJ,GAAG7L,IAAI8L,WAAW;QAClBC,GAAG3B,2BAA2B1J;QAC9BsL,GAAG,CAAC,CAACd;QACLzI,GAAG;YACD;gBACEiI;gBACAG;gBACAQ;gBACAK;aACD;SACF;QACDO,GAAGxB;QACHyB,GAAG;YAACZ;YAAaE;SAAkB;QACnCW,GAAG,OAAOnM,IAAIY,UAAU,CAACwL,SAAS,KAAK;QACvCvJ,GAAGpC,UAAUqC,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAAS8I,SAAS,EAAE9K,gBAAgB,EAAoC;IACtEA,iBAAiBuL,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACbpM,IAAgB,EAChBH,GAAqB,EACrBwM,QAAiB,EACjB5B,SAAqD;IAErD,MAAM,EACJ/L,0BAA0B,EAC1B2B,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZE,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAM,EAAEK,YAAY,EAAED,YAAY,EAAE,GAAGX,yBAAyB;QAC9DD;QACAiB,aAAaZ;QACba,UAAUX,IAAIW,QAAQ;QACtBC,iBAAiBC,IAAAA,sCAAqB,EAACvB,IAAIY,UAAU;QACrDgK;QACA/L;QACA0B;QACAE;QACAJ;QACAC;QACAK,wBAAwBA;IAC1B;IAEA,MAAM0K,4BACJ,sBAACzJ,cAAK,CAACC,QAAQ;;0BACb,qBAACtC;gBACCZ,UAAUqB,IAAIrB,QAAQ;gBACtBa,YAAYQ,IAAI8B,GAAG,CAACtC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,qBAACsB;YACA5E,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,qBAACuD;gBAAKC,MAAK;gBAAaC,SAAQ;;0BAElC,qBAACkB;;OAVkBlF;IAcvB,MAAM4O,cAAcC,IAAAA,4EAAqC,EACvDxK,MACAtB,4BACA2B;IAGF,IAAIkD,MAAyBlH;IAC7B,IAAIgQ,UAAU;QACZ9I,MAAM+I,IAAAA,gBAAO,EAACD,YAAYA,WAAW,qBAAwB,CAAxB,IAAIE,MAAMF,WAAW,KAArB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC9D;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAM3B,WAA8B;QAClCH,WAAW,CAAC,EAAE;sBACd,sBAACiC;YAAKC,IAAG;;8BACP,qBAACC;8BACD,qBAACC;8BACE3Q,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBqH,oBACxC,qBAACqJ;wBACCC,2BAAyBtJ,IAAIuJ,OAAO;wBACpCC,0BAAwB,YAAYxJ,MAAMA,IAAI4D,MAAM,GAAG;wBACvD6F,yBAAuBzJ,IAAI0J,KAAK;yBAEhC;;;;QAGR,CAAC;QACD;QACA;KACD;IAED,MAAM,EAAE9B,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvDtL,MACAH;IAGF,MAAM0L,wBACJjL,UAAUqC,kBAAkB,IAC5B9C,IAAIY,UAAU,CAACuH,YAAY,CAAC7K,iBAAiB,KAAK;IAEpD,OAAO;QACLoF,GAAG1C,IAAI2C,aAAa,CAACC,OAAO;QAC5BiJ,GAAG7L,IAAI8L,WAAW;QAClBC,GAAG3B,2BAA2B1J;QAC9BuL,GAAGzP;QACHwP,GAAG;QACHvJ,GAAG;YACD;gBACEiI;gBACAG;gBACAQ;gBACAK;aACD;SACF;QACDQ,GAAG;YAACZ;YAAaE;SAAkB;QACnCW,GAAG,OAAOnM,IAAIY,UAAU,CAACwL,SAAS,KAAK;QACvCvJ,GAAGpC,UAAUqC,kBAAkB;IACjC;AACF;AAEA,SAASiD,8BACP3B,uBAA8D;IAI9D,IAAI,CAACA,yBAAyB;QAC5B,MAAM,qBAAqE,CAArE,IAAIwF,8BAAc,CAAC,oDAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAoE;IAC5E;AACF;AAEA,mFAAmF;AACnF,SAASyD,IAAO,EACdC,iBAAiB,EACjBC,cAAc,EACdnJ,uBAAuB,EACvBoJ,0BAA0B,EAC1B3P,KAAK,EAON;IACC0P;IACA,MAAMnI,WAAWxD,cAAK,CAAC6L,GAAG,CACxBC,IAAAA,kCAAe,EACbJ,mBACAlJ,yBACAvG;IAIJ,MAAM8P,eAAeC,IAAAA,kDAAwB,EAAC;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBC,aAAa,CAAC;QACdC,mBAAmB1I,SAAS3C,CAAC;QAC7BsL,0BAA0B3I,SAAS2G,CAAC;QACpCiC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACVhD,oBAAoB9F,SAAS4G,CAAC;QAC9BI,WAAWhH,SAAS+G,CAAC;QACrBgC,aAAa/I,SAASvC,CAAC;IACzB;IAEA,MAAMuL,cAAcC,IAAAA,2CAAwB,EAACV,cAAc;IAE3D,MAAM,EAAEW,kBAAkB,EAAE,GAC1BhS,QAAQ;IAEV,qBACE,qBAACgS,mBAAmBC,QAAQ;QAC1BC,OAAO;YACLC,QAAQ;YACR5Q;QACF;kBAEA,cAAA,qBAAC2P;sBACC,cAAA,qBAACkB,kBAAS;gBACRN,aAAaA;gBACbO,kBAAkBvJ,SAAS8G,CAAC;gBAC5BJ,aAAa1G,SAASyG,CAAC;;;;AAKjC;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAAS+C,SAAY,EACnBtB,iBAAiB,EACjBC,cAAc,EACdnJ,uBAAuB,EACvBoJ,0BAA0B,EAC1B3P,KAAK,EAON;IACC0P;IACA,MAAMnI,WAAWxD,cAAK,CAAC6L,GAAG,CACxBC,IAAAA,kCAAe,EACbJ,mBACAlJ,yBACAvG;IAIJ,MAAM8P,eAAeC,IAAAA,kDAAwB,EAAC;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBC,aAAa,CAAC;QACdC,mBAAmB1I,SAAS3C,CAAC;QAC7BsL,0BAA0B3I,SAAS2G,CAAC;QACpCiC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACVhD,oBAAoB9F,SAAS4G,CAAC;QAC9BI,WAAWhH,SAAS+G,CAAC;QACrBgC,aAAa/I,SAASvC,CAAC;IACzB;IAEA,MAAMuL,cAAcC,IAAAA,2CAAwB,EAACV,cAAc;IAE3D,qBACE,qBAACH;kBACC,cAAA,qBAACkB,kBAAS;YACRN,aAAaA;YACbO,kBAAkBvJ,SAAS8G,CAAC;YAC5BJ,aAAa1G,SAASyG,CAAC;;;AAI/B;AASA,eAAegD,yBACbtL,GAAoB,EACpBzB,GAAqB,EACrBpB,GAAwC,EACxC/B,QAAgB,EAChB6B,KAAyB,EACzBI,UAAsB,EACtBH,SAAoB,EACpBqO,oBAA0C,EAC1CC,cAAqC,EACrCC,wBAA8D,EAC9DrM,aAA+B,EAC/B/D,mBAA+C;IAE/C,MAAMqQ,iBAAiBtQ,aAAa;IACpC,IAAIsQ,gBAAgB;QAClBnN,IAAItC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAM0P,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJhL,uBAAuB,EACvBiL,qBAAqB,EACrBvJ,YAAY,EACZwJ,gBAAgB,EAChBC,aAAa,EACbzD,cAAc,EAAE,EAChB0D,cAAc,EACf,GAAG5O;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAIkF,aAAa2J,YAAY,EAAE;QAC7B,MAAMC,eAAeC,IAAAA,wDAAyB,EAAC7J;QAE/C,kEAAkE;QAClE,0EAA0E;QAC1E,+EAA+E;QAC/E,8DAA8D;QAE9D,MAAM8J,2BAA2B;YAC/B,IAAI,CAAChP,WAAWuH,YAAY,CAAC0H,eAAe,EAAE;gBAC5C,OAAO;YACT;YACA,IAAIjP,WAAWkD,GAAG,EAAE;gBAClB,OAAO;YACT;YACA,MAAMgM,gBAAgB9L,kDAAoB,CAAC+L,QAAQ;YAEnD,IAAI,CAACD,eAAe;gBAClB,OAAO;YACT;YAEA,OAAQA,cAAczQ,IAAI;gBACxB,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,OAAO;gBACT,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,OAAO;gBACT;oBACEyQ;YACJ;QACF;QAEA,MAAME,mBAAgD,CAAC,GAAGC;YACxD,MAAMC,mBAAmBR,aAAapT,OAAO,IAAI2T;YACjD,IAAIL,4BAA4B;gBAC9B,+CAA+C;gBAC/CO,IAAAA,8CAAkB,EAACD;YACrB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBE,WAAWJ,gBAAgB,GAAGA;QAE9B,MAAMK,sBAAqD,CAAC,GAAGJ;YAC7D,MAAMK,eAAeZ,aAAaa,SAAS,IAAIN;YAC/C,IAAIL,4BAA4B;gBAC9BY,IAAAA,iDAAqB,EAACF;YACxB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBF,WAAWC,mBAAmB,GAAGA;IACnC;IAEA,IAAIlU,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,uCAAuC;QACvC,MAAM,EAAEgF,QAAQ,EAAE,GAAG,IAAIoP,IAAIlN,IAAI7C,GAAG,IAAI,KAAK;QAC7CE,WAAW8P,YAAY,oBAAvB9P,WAAW8P,YAAY,MAAvB9P,YAA0BS,UAAU;IACtC;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7DlF,QAAQC,GAAG,CAACuU,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAACrN,MAClB;QACAzB,IAAI+O,OAAO,CAAC;YACV,oEAAoE;YACpE,4BAA4B;YAC5BpQ,UAAUqQ,uBAAuB,GAAG;QACtC;QAEAvN,IAAIwN,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5B,IAAI,iBAAiBZ,YAAY;gBAC/B,MAAMa,UAAUC,IAAAA,8DAA+B,EAAC;oBAAEC,OAAO;gBAAK;gBAC9D,IAAIF,SAAS;oBACXG,IAAAA,iBAAS,IACNC,SAAS,CAACC,6BAAkB,CAACC,sBAAsB,EAAE;wBACpDC,WAAWP,QAAQQ,wBAAwB;wBAC3CC,YAAY;4BACV,iCACET,QAAQU,wBAAwB;4BAClC,kBAAkBL,6BAAkB,CAACC,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFX,QAAQQ,wBAAwB,GAC9BR,QAAQY,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAMnN,WAAwC;QAC5ClF,YAAYyP,iBAAiB,MAAMzS;IACrC;IAEA,MAAM+D,yBAAyB,CAAC,EAAC+O,oCAAAA,iBAAkBwC,kBAAkB;IAErE/L,8BAA8B3B;IAE9B,MAAM2N,kBAAkBC,IAAAA,kCAAqB,EAAC;QAAE3C;IAAsB;IAEtE4C,IAAAA,+CAA8B,EAAC;QAC7BzT,MAAMiC,UAAUjC,IAAI;QACpB4F;QACAiL;QACA0C;IACF;IAEAjM,aAAaoM,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EAAE/R,MAAMhC,UAAU,EAAEgU,oBAAoB,EAAE,GAAGrM;IACnD,IAAI0J,gBAAgB;QAClB2C,qBACE,kFACAhW,QAAQC,GAAG;IAEf;IAEAqE,UAAU+D,YAAY,GAAG,EAAE;IAC3BE,SAASF,YAAY,GAAG/D,UAAU+D,YAAY;IAE9C,qCAAqC;IACrChE,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnB4R,IAAAA,mCAAoB,EAAC5R;IAErB,MAAM,EACJjD,iBAAiB,EACjBT,iBAAiB,EACjBE,wBAAwB,EACxBG,YAAY,EACZP,kBAAkB,EAClBK,YAAY,EACZY,KAAK,EACN,GAAGiR;IAEJ,MAAM,EAAEhM,kBAAkB,EAAE,GAAGrC;IAE/B;;;GAGC,GACD,IAAIzE;IAEJ,IAAI8G,oBAAoB;QACtB9G,YAAYqW,OAAOC,IAAI,CACrB,MAAMC,OAAOC,MAAM,CAAClL,MAAM,CAAC,SAAS+K,OAAOC,IAAI,CAAC/O,IAAI7C,GAAG,IACvD+R,QAAQ,CAAC;IACb,OAAO,IAAItW,QAAQC,GAAG,CAACuU,YAAY,KAAK,QAAQ;QAC9C3U,YAAYuW,OAAOG,UAAU;IAC/B,OAAO;QACL1W,YAAY,AACVM,QAAQ,6BACRqW,MAAM;IACV;IAEA;;GAEC,GACD,MAAMjU,SAASkC,WAAWlC,MAAM,IAAI,CAAC;IAErC,MAAMG,6BAA6BJ,+BACjCC,QACAC,UACAC;IAGF,MAAMgU,0BAA0BC,IAAAA,kDAAyB,EAACtP;IAE1D,MAAMsC,eAAe,MAAMiN,IAAAA,6BAAe,EACxCrS,UAAUjC,IAAI,EACdkC,KACA9B;IAGF,MAAMoB,MAAwB;QAC5BE,cAAc4F;QACdpF;QACAE;QACAH;QACAqO;QACAjQ;QACA2B;QACAuS,YAAYjW;QACZ2C,wBAAwBmT;QACxB1D;QACA3O;QACAhD;QACAvB;QACA2C;QACAyF;QACA0H;QACAmD;QACApR;QACAiE;QACAa;QACAkD;IACF;IAEAuL,IAAAA,iBAAS,IAAG4B,oBAAoB,CAAC,cAAcrU;IAE/C,IAAImE,oBAAoB;QACtB,mEAAmE;QACnE,4CAA4C;QAC5C,MAAMmQ,+BAA+B7B,IAAAA,iBAAS,IAAG8B,IAAI,CACnDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAE1U,UAAU;YAC7C+S,YAAY;gBACV,cAAc/S;YAChB;QACF,GACA2U;QAGF,MAAMlO,WAAW,MAAM6N,6BACrB1P,KACAzB,KACA9B,KACA0E,UACAvG,YACAS;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACEwG,SAASiE,aAAa,IACtBkK,IAAAA,qCAAmB,EAACnO,SAASiE,aAAa,KAC1CzI,WAAWwH,sBAAsB,EACjC;YACAoL,IAAAA,SAAI,EAAC;YACL,KAAK,MAAMC,UAAUC,IAAAA,0CAAwB,EAACtO,SAASiE,aAAa,EAAG;gBACrEmK,IAAAA,SAAI,EAACC;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAIhT,UAAUwH,wBAAwB,EAAE;YACtC0L,IAAAA,2CAAyB,EAAClT,WAAWA,UAAUwH,wBAAwB;YACvE,MAAM,IAAI2L,8CAAqB;QACjC;QACA,IAAIxO,SAASyO,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoB3O,SAASyO,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAGzF,KAAK;YACxE,IAAIuF,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAI3O,SAAS8O,SAAS,CAACC,MAAM,EAAE;YAC7B,MAAMJ,oBAAoB3O,SAAS8O,SAAS,CAACE,IAAI,CAAC,CAAC1Q,MACjD2Q,IAAAA,mCAAe,EAAC3Q;YAElB,IAAIqQ,mBAAmB,MAAMA;QAC/B;QAEA,MAAMpX,UAA+B;YACnC+H;YACA4P,aAAaC,oCAAwB;QACvC;QACA,oEAAoE;QACpE,IACE9T,UAAU+T,kBAAkB,IAC5B/T,UAAUgU,uBAAuB,IACjChU,UAAUiU,sBAAsB,EAChC;YACA,MAAMC,iBAAiBC,IAAAA,qCAAkB,EAACnU,WAAWoU,OAAO,CAAC;gBAC3D,IAAI1Y,QAAQC,GAAG,CAAC0Y,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CtU;gBAC3D;YACF;YAEA,IAAIE,WAAWqU,SAAS,EAAE;gBACxBrU,WAAWqU,SAAS,CAACN;YACvB,OAAO;gBACLhY,QAAQsY,SAAS,GAAGN;YACtB;QACF;QAEArP,iCAAiCF,UAAUV,UAAUjE;QAErD,IAAI2E,SAASJ,qBAAqB,EAAE;YAClCN,SAASM,qBAAqB,GAAGI,SAASJ,qBAAqB;QACjE;QAEA,OAAO,IAAIkQ,qBAAY,CAAC,MAAMC,IAAAA,oCAAc,EAAC/P,SAASgQ,MAAM,GAAGzY;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAMqI,wBACJpE,WAAWoE,qBAAqB,KAAI+J,kCAAAA,eAAgB/J,qBAAqB;QAE3E,MAAMJ,aAAaC,IAAAA,kCAAa,EAAC1G,YAAY6B,IAAInB,0BAA0B;QAC3E,MAAMwW,8BACJC,IAAAA,2BAAc,EAAC/R,KAAK,kCAAkC;QACxD,MAAMC,eAAe+R,IAAAA,yCAA2B,EAC9ChS,KACAzB,KACApB,KACAkE,YACAiB,cACAjF,WAAW4U,eAAe,EAC1B5U,WAAW6U,YAAY,EACvBxY,cACA+R,0BACAhK,uBACAqQ;QAGF,IACElZ,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBuE,WAAW8P,YAAY,IACvB,qEAAqE;QACrE,6DAA6D;QAC7DvU,QAAQC,GAAG,CAACuU,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAACrN,QAClB,CAAC3G,oBACD;YACA,MAAM8T,eAAe9P,WAAW8P,YAAY;YAC5CnN,IAAIwN,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,IAAI,CAACxN,aAAakS,WAAW,IAAI,CAACjV,UAAUkV,YAAY,EAAE;oBACxD,iEAAiE;oBACjE,MAAM,EAAEtU,QAAQ,EAAE,GAAG,IAAIoP,IAAIlN,IAAI7C,GAAG,IAAI,KAAK;oBAC7CgQ,aAAarP,UAAU;gBACzB;YACF;QACF;QAEA,IAAIzE,oBAAoB;YACtB,OAAO8M,gBAAgBnG,KAAKvD;QAC9B,OAAO,IAAI7C,cAAc;YACvB,IAAIH,0BAA0B;gBAC5B,OAAOyH,8BAA8BlB,KAAKzB,KAAK9B,KAAKwD;YACtD,OAAO;gBACL,OAAOF,kCAAkCC,KAAKvD,KAAKwD;YACrD;QACF;QAEA,MAAMoS,4BAA4BxE,IAAAA,iBAAS,IAAG8B,IAAI,CAChDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAE1U,UAAU;YAC1C+S,YAAY;gBACV,cAAc/S;YAChB;QACF,GACAkX;QAGF,IAAIC,YAAwB;QAC5B,IAAIlD,yBAAyB;YAC3B,gFAAgF;YAChF,MAAMmD,sBAAsB,MAAMC,IAAAA,2BAAY,EAAC;gBAC7CzS;gBACAzB;gBACAgE;gBACAiM;gBACAkE,gBAAgB3S;gBAChB7C;gBACA+C;gBACA+L;gBACAvP;gBACA0E;YACF;YAEA,IAAIqR,qBAAqB;gBACvB,IAAIA,oBAAoB1W,IAAI,KAAK,aAAa;oBAC5C,MAAM6W,qBAAqBhY,yBAAyBC;oBACpD2D,IAAItC,UAAU,GAAG;oBACjBkF,SAASlF,UAAU,GAAG;oBACtB,MAAM4V,SAAS,MAAMQ,0BACnBpS,cACAD,KACAzB,KACA9B,KACAkW,oBACAJ,WACA/G,gBACArK,UACA2Q;oBAGF,OAAO,IAAIH,qBAAY,CAACE,QAAQ;wBAC9B1Q;wBACA4P,aAAaC,oCAAwB;oBACvC;gBACF,OAAO,IAAIwB,oBAAoB1W,IAAI,KAAK,QAAQ;oBAC9C,IAAI0W,oBAAoBrQ,MAAM,EAAE;wBAC9BqQ,oBAAoBrQ,MAAM,CAACyQ,cAAc,CAACzR;wBAC1C,OAAOqR,oBAAoBrQ,MAAM;oBACnC,OAAO,IAAIqQ,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;QACF;QAEA,MAAMnZ,UAA+B;YACnC+H;YACA4P,aAAaC,oCAAwB;QACvC;QAEA,MAAMa,SAAS,MAAMQ,0BACnBpS,cACAD,KACAzB,KACA9B,KACA7B,YACA2X,WACA/G,gBACArK,UACA2Q;QAGF,uEAAuE;QACvE,kDAAkD;QAClD,6GAA6G;QAC7G,IAAI5U,UAAUwH,wBAAwB,IAAIxH,UAAUqD,GAAG,EAAE;YACvD,MAAMrD,UAAUwH,wBAAwB;QAC1C;QAEA,oEAAoE;QACpE,IACExH,UAAU+T,kBAAkB,IAC5B/T,UAAUgU,uBAAuB,IACjChU,UAAUiU,sBAAsB,EAChC;YACA,MAAMC,iBAAiBC,IAAAA,qCAAkB,EAACnU,WAAWoU,OAAO,CAAC;gBAC3D,IAAI1Y,QAAQC,GAAG,CAAC0Y,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CtU;gBAC3D;YACF;YAEA,IAAIE,WAAWqU,SAAS,EAAE;gBACxBrU,WAAWqU,SAAS,CAACN;YACvB,OAAO;gBACLhY,QAAQsY,SAAS,GAAGN;YACtB;QACF;QAEA,iDAAiD;QACjD,OAAO,IAAIO,qBAAY,CAACE,QAAQzY;IAClC;AACF;AAcO,MAAMd,uBAAsC,CACjD0H,KACAzB,KACAnD,UACA6B,OACA5B,qBACAgC,YACAoO,0BACAnS,aACA8F;QAaiB/B;IAXjB,IAAI,CAAC2C,IAAI7C,GAAG,EAAE;QACZ,MAAM,qBAAwB,CAAxB,IAAIgM,MAAM,gBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B;IAEA,MAAMhM,MAAM0V,IAAAA,kCAAgB,EAAC7S,IAAI7C,GAAG,EAAElE,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAMsS,uBAAuBrS,oBAAoB8G,IAAI7G,OAAO,EAAE;QAC5DG;QACAS,mBAAmBsD,WAAWuH,YAAY,CAAC7K,iBAAiB,KAAK;QACjEW,aAAa,GAAE2C,2BAAAA,WAAW6U,YAAY,qBAAvB7U,yBAAyB3C,aAAa;IACvD;IAEA,MAAM,EAAEnB,iBAAiB,EAAEiB,yBAAyB,EAAE,GAAG+Q;IAEzD,IAAIC,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAOnO,WAAWwL,SAAS,KAAK,UAAU;QAC5C,IAAIxN,qBAAqB;YACvB,MAAM,qBAEL,CAFK,IAAIgL,8BAAc,CACtB,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAmF,iBAAiBsH,IAAAA,mCAAmB,EAClCzV,WAAWwL,SAAS,EACpBxL,WAAWlC,MAAM;IAErB;IAEA,IACEqQ,CAAAA,kCAAAA,eAAgB/J,qBAAqB,KACrCpE,WAAWoE,qBAAqB,EAChC;QACA,MAAM,qBAEL,CAFK,IAAI4E,8BAAc,CACtB,+FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMnJ,YAAY6V,IAAAA,0BAAe,EAAC;QAChC9X,MAAMoC,WAAW2V,WAAW,CAACC,UAAU,CAAChY,IAAI;QAC5CoC;QACA,8CAA8C;QAC9C9D;QACA8F,SAASD,cAAcC,OAAO;QAC9B7E;IACF;IAEA,OAAO0Y,0CAAgB,CAACxS,GAAG,CACzBxD,WACA,sBAAsB;IACtBoO,0BACA,mBAAmB;IACnBtL,KACAzB,KACApB,KACA/B,UACA6B,OACAI,YACAH,WACAqO,sBACAC,gBACAC,0BACArM,eACA/D;AAEJ;AAEA,SAAS0G,iCACPF,QAMC,EACDV,QAAqC,EACrCjE,SAAoB;QA8BhBiE;IA5BJ,IAAIU,SAASqE,aAAa,EAAE;QAC1B/E,SAASgS,SAAS,GAAGtR,SAASqE,aAAa,CAACkN,IAAI,CAAC;IACnD;IAEA,uEAAuE;IACvE,MAAMC,cAAcC,OAAOzR,SAASoE,cAAc;IAClD9E,SAAShI,OAAO,KAAK,CAAC;IACtBgI,SAAShI,OAAO,CAACoa,+CAA6B,CAAC,GAAGF;IAElD,yEAAyE;IACzE,YAAY;IACZ,IAAInW,UAAUsW,WAAW,KAAK,SAAS3R,SAASkE,mBAAmB,KAAK,GAAG;QACzE5E,SAASsS,YAAY,GAAG;YAAErQ,YAAY;YAAGC,QAAQpK;QAAU;IAC7D,OAAO;QACL,gEAAgE;QAChEkI,SAASsS,YAAY,GAAG;YACtBrQ,YACEvB,SAASkE,mBAAmB,IAAIxC,0BAAc,GAC1C,QACA1B,SAASkE,mBAAmB;YAClC1C,QACExB,SAASmE,eAAe,IAAIzC,0BAAc,GACtCtK,YACA4I,SAASmE,eAAe;QAChC;IACF;IAEA,qCAAqC;IACrC,IAAI7E,EAAAA,yBAAAA,SAASsS,YAAY,qBAArBtS,uBAAuBiC,UAAU,MAAK,GAAG;QAC3CjC,SAASuS,iBAAiB,GAAG;YAC3BC,aAAazW,UAAU0W,uBAAuB;YAC9C/J,OAAO3M,UAAU2W,iBAAiB;QACpC;IACF;AACF;AAEA,eAAevB,eACbrS,YAA0B,EAC1BD,GAAoB,EACpBzB,GAAqB,EACrB9B,GAAqB,EACrBG,IAAgB,EAChB2V,SAAc,EACd/G,cAAqC,EACrCrK,QAAqC,EACrC2Q,2BAAuD;IAEvD,MAAM,EAAEvJ,WAAW,EAAEjO,KAAK,EAAEc,QAAQ,EAAEiC,UAAU,EAAE,GAAGZ;IAErD,MAAM,EACJqX,QAAQ,EACRC,aAAa,EACblT,uBAAuB,EACvB0B,YAAY,EACZyR,WAAW,EACXzT,MAAM,KAAK,EACXqE,YAAY,EACZqP,aAAa,KAAK,EAClB7T,6BAA6B,EAC7BnF,IAAI,EACJiZ,qBAAqB,EACrBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,uBAAuB,EACxB,GAAGhX;IAEJmF,8BAA8B3B;IAE9B,MAAM,EAAEoJ,0BAA0B,EAAEqK,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAMC,4BAA4BC,IAAAA,0DAA4B,EAACna;IAE/D,MAAMoa,kBAAkBC,IAAAA,yBAAiB,EACvC9G,IAAAA,iBAAS,IAAG+G,uBAAuB,IACnChQ,aAAaiQ,mBAAmB;IAGlC,MAAMC,YACJf,cAAcgB,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDrW,GAAG,CAAC,CAACoW,WAAc,CAAA;YAClBE,KAAK,GAAG5M,YAAY,OAAO,EAAE0M,WAAWG,IAAAA,wCAAmB,EACzD3Y,KACA,QACC;YACH4Y,SAAS,EAAEjB,gDAAAA,4BAA8B,CAACa,SAAS;YACnDjB;YACAsB,UAAU;YACVhb;QACF,CAAA;IAEJ,MAAM,CAAC0P,gBAAgBuL,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1DzB,eACA,6CAA6C;IAC7C,8EAA8E;IAC9ExL,aACAyL,aACAI,8BACAgB,IAAAA,wCAAmB,EAAC3Y,KAAK,OACzBnC,OACAW;IAGF,MAAMwa,4BAAwD,IAAI/K;IAClE,MAAMgL,gBAAgB;IACtB,SAASC,qBAAqBxV,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB/C,KAAK;IAE5B;IACA,MAAMmZ,+BAA+BC,IAAAA,qDAAiC,EACpEtV,KACA0T,YACAwB,2BACAC,eACAC;IAGF,SAASG,qBAAqB3V,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB/C,KAAK;IAE5B;IAEA,MAAMsZ,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD1V,KACA0T,YACAwB,2BACAM,mBACAL,eACAI;IAGF,IAAII,oBAA8C;IAElD,MAAMjU,YAAY1D,IAAI0D,SAAS,CAACkU,IAAI,CAAC5X;IACrC,MAAM6X,eAAe7X,IAAI6X,YAAY,CAACD,IAAI,CAAC5X;IAE3C,IAAI;QACF,IACE,qDAAqD;QACrDgC,OACA,uEAAuE;QACvE3H,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,oGAAoG;QACpGF,QAAQC,GAAG,CAACuU,YAAY,KAAK,UAC7B,+EAA+E;QAC/ExI,aAAa0H,eAAe,EAC5B;YACA,wFAAwF;YACxF,MAAM9L,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACA+G,eACApK,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAErB,MAAM,CAACoa,mBAAmBC,iBAAiB,GAAGC;YAC9C/V,WAAWgW,WAAW,GAAGF;YAEzB,MAAMvM,oBAAoB,MAAMtJ,kDAAoB,CAACC,GAAG,CACtDT,cACAwW,+CAAyB,EACzB;gBACExW,aAAayW,cAAc,GAAG;gBAC9B,OAAOnU,aAAa3B,sBAAsB,CACxCJ,YACAK,wBAAwBC,aAAa,EACrC;oBACET,SAASuV;oBACTe,iBAAiB,IACf1W,aAAayW,cAAc,KAAK,OAAO,cAAc;oBACvD/d;gBACF;YAEJ,GACA;gBACEsH,aAAayW,cAAc,GAAG;YAChC;YAGFE,4BACEP,mBACAzZ,MACAH,KACA8B,IAAItC,UAAU,KAAK,KACnB4E,yBACAZ,cACA6R;YAGFoE,oBAAoB,IAAIW,0CAAiB,CAAC9M;QAC5C,OAAO;YACL,wFAAwF;YACxF,MAAMvJ,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/CT,cACA+G,eACApK,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAGrBia,oBAAoB,IAAIW,0CAAiB,CACvCpW,kDAAoB,CAACC,GAAG,CACtBT,cACAsC,aAAa3B,sBAAsB,EACnCJ,YACAK,wBAAwBC,aAAa,EACrC;gBACEnI;gBACA0H,SAASuV;YACX;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAMkB,IAAAA,wCAA6B;QAEnC,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAOzZ,WAAWwL,SAAS,KAAK,UAAU;YAC5C,IAAI2C,CAAAA,kCAAAA,eAAgB1P,IAAI,MAAKib,4BAAY,CAACC,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+BC,IAAAA,kDAA+B,EAClEhB,kBAAkBiB,GAAG,IACrB7c,OACAiY;gBAGF,OAAO6E,IAAAA,kCAAY,EACjBH,8BACAI,IAAAA,iDAA2B;YAE/B,OAAO,IAAI7L,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAM,EAAE3C,SAAS,EAAEyO,YAAY,EAAE,GAC/BC,IAAAA,qCAAqB,EAAC/L;gBACxB,MAAMgM,SAAS,AACbze,QAAQ,oBACRye,MAAM;gBAER,MAAMC,aAAa,MAAMhX,kDAAoB,CAACC,GAAG,CAC/CT,cACAuX,sBACA,qBAAC1N;oBACCC,mBAAmBmM,kBAAkBiB,GAAG;oBACxCnN,gBAAgBA;oBAChBnJ,yBAAyBA;oBACzBoJ,4BAA4BA;oBAC5B3P,OAAOA;oBAETuO,WACA;oBAAExI,SAAS2V;oBAA0B1b;gBAAM;gBAG7C,MAAMod,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD7C;oBACAR;oBACAsD,sBAAsB7B;oBACtBjC;oBACAY,iBAAiBA;gBACnB;gBACA,OAAO,MAAMmD,IAAAA,+CAAyB,EAACJ,YAAY;oBACjD,oGAAoG;oBACpG,yCAAyC;oBACzC,qGAAqG;oBACrG,2FAA2F;oBAC3FK,8BACER,iBAAiBS,uCAAuB,CAACC,KAAK;oBAChDC,mBAAmBf,IAAAA,kDAA+B,EAChDhB,kBAAkBgC,OAAO,IACzB5d,OACAiY;oBAEFmF;oBACAlD;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAM5T,yBAAyB,AAC7B7H,QAAQ,oBACR6H,sBAAsB;QAExB,MAAM6W,aAAa,MAAMhX,kDAAoB,CAACC,GAAG,CAC/CT,cACAW,sCACA,qBAACkJ;YACCC,mBAAmBmM,kBAAkBiB,GAAG;YACxCnN,gBAAgBA;YAChBnJ,yBAAyBA;YACzBoJ,4BAA4BA;YAC5B3P,OAAOA;YAET;YACE+F,SAAS2V;YACT1b;YACA6d,WAAW,CAAChf;gBACVA,QAAQ2P,OAAO,CAAC,CAACmC,OAAOmN;oBACtBhC,aAAagC,KAAKnN;gBACpB;YACF;YACAoN,kBAAkBnE;YAClBoE,kBAAkB;gBAAC/C;aAAgB;YACnChD;QACF;QAGF,MAAMmF,wBAAwBC,IAAAA,oDAAyB,EAAC;YACtD7C;YACAR;YACAsD,sBAAsB7B;YACtBjC;YACAY,iBAAiBA;QACnB;QACA;;;;;;;;;;;;;;;;KAgBC,GACD,MAAM6D,qBACJlE,4BAA4B,QAAQ,CAAC,CAACF;QAExC,OAAO,MAAMqE,IAAAA,wCAAkB,EAACf,YAAY;YAC1CQ,mBAAmBf,IAAAA,kDAA+B,EAChDhB,kBAAkBgC,OAAO,IACzB5d,OACAiY;YAEFhT,oBAAoBgZ;YACpBE,yBAAyBhc,IAAIS,SAAS,CAACub,uBAAuB,KAAK;YACnEpZ,SAAS5C,IAAIS,SAAS,CAACmC,OAAO;YAC9BqY;YACAlD;YACAkE,oBAAoBnY;QACtB;IACF,EAAE,OAAOJ,KAAK;QACZ,IACEwY,IAAAA,gDAAuB,EAACxY,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIuJ,OAAO,KAAK,YACvBvJ,IAAIuJ,OAAO,CAAC9B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMzH;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMyY,qBAAqBC,IAAAA,iCAAmB,EAAC1Y;QAC/C,IAAIyY,oBAAoB;YACtB,MAAM/O,QAAQiP,IAAAA,8CAA2B,EAAC3Y;YAC1C4Y,IAAAA,UAAK,EACH,GAAG5Y,IAAI6Y,MAAM,CAAC,mDAAmD,EAAE5d,SAAS,kFAAkF,EAAEyO,OAAO;YAGzK,MAAM1J;QACR;QAEA,IAAIkH;QAEJ,IAAI4R,IAAAA,6CAAyB,EAAC9Y,MAAM;YAClC5B,IAAItC,UAAU,GAAGid,IAAAA,+CAA2B,EAAC/Y;YAC7CgB,SAASlF,UAAU,GAAGsC,IAAItC,UAAU;YACpCoL,YAAY8R,IAAAA,sDAAkC,EAAC5a,IAAItC,UAAU;QAC/D,OAAO,IAAImd,IAAAA,8BAAe,EAACjZ,MAAM;YAC/BkH,YAAY;YACZ9I,IAAItC,UAAU,GAAGod,IAAAA,wCAA8B,EAAClZ;YAChDgB,SAASlF,UAAU,GAAGsC,IAAItC,UAAU;YAEpC,MAAMqd,cAAcC,IAAAA,4BAAa,EAACC,IAAAA,iCAAuB,EAACrZ,MAAM2T;YAEhE,gEAAgE;YAChE,YAAY;YACZ,MAAM3a,UAAU,IAAIsgB;YACpB,IAAIC,IAAAA,oCAAoB,EAACvgB,SAAS8G,aAAa0Z,cAAc,GAAG;gBAC9D1X,UAAU,cAAc2X,MAAM7K,IAAI,CAAC5V,QAAQsX,MAAM;YACnD;YAEAxO,UAAU,YAAYqX;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9Bra,IAAItC,UAAU,GAAG;YACjBkF,SAASlF,UAAU,GAAGsC,IAAItC,UAAU;QACtC;QAEA,MAAM,CAAC4d,qBAAqBC,qBAAqB,GAAGtE,IAAAA,mCAAkB,EACpEzB,eACAxL,aACAyL,aACAI,8BACAgB,IAAAA,wCAAmB,EAAC3Y,KAAK,QACzBnC,OACA;QAGF,MAAMyf,kBAAkB,MAAMtZ,kDAAoB,CAACC,GAAG,CACpDT,cACA+I,oBACApM,MACAH,KACAgZ,0BAA0BuE,GAAG,CAAC,AAAC7Z,IAAY4D,MAAM,IAAI,OAAO5D,KAC5DkH;QAGF,MAAM4S,oBAAoBxZ,kDAAoB,CAACC,GAAG,CAChDT,cACAsC,aAAa3B,sBAAsB,EACnCmZ,iBACAlZ,wBAAwBC,aAAa,EACrC;YACEnI;YACA0H,SAASuV;QACX;QAGF,IAAIM,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAM/V;QACR;QAEA,IAAI;YACF,MAAM+Z,aAAa,MAAMzZ,kDAAoB,CAACC,GAAG,CAC/CT,cACAka,+CAAyB,EACzB;gBACEC,gBACErhB,QAAQ;gBACVshB,uBACE,qBAAChP;oBACCtB,mBAAmBkQ;oBACnBhQ,4BAA4BA;oBAC5BD,gBAAgB6P;oBAChBhZ,yBAAyBA;oBACzBvG,OAAOA;;gBAGXggB,eAAe;oBACbhgB;oBACA,wCAAwC;oBACxCge,kBAAkB;wBAACwB;qBAAqB;oBACxCvH;gBACF;YACF;YAGF;;;;;;;;;;;;;;;OAeC,GACD,MAAMgG,qBACJlE,4BAA4B,QAAQ,CAAC,CAACF;YACxC,OAAO,MAAMqE,IAAAA,wCAAkB,EAAC0B,YAAY;gBAC1CjC,mBAAmBf,IAAAA,kDAA+B,EAChD,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACThB,kBAAkBgC,OAAO,IACzB5d,OACAiY;gBAEFhT,oBAAoBgZ;gBACpBE,yBAAyBhc,IAAIS,SAAS,CAACub,uBAAuB,KAAK;gBACnEpZ,SAAS5C,IAAIS,SAAS,CAACmC,OAAO;gBAC9BqY,uBAAuBC,IAAAA,oDAAyB,EAAC;oBAC/C7C;oBACAR;oBACAsD,sBAAsB,EAAE;oBACxB9D;oBACAY,iBAAiBA;gBACnB;gBACAF;gBACAkE,oBAAoBnY;YACtB;QACF,EAAE,OAAOga,UAAe;YACtB,IACE3hB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBmgB,IAAAA,6CAAyB,EAACsB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BzhB,QAAQ;gBACVyhB;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,SAAShE;IACP,IAAIF;IACJ,IAAIoE,SAAS,IAAIC,QAAyB,CAACrV;QACzCgR,oBAAoBhR;IACtB;IACA,OAAO;QAACgR;QAAoBoE;KAAO;AACrC;AAEA;;;;;CAKC,GACD,eAAe7D,4BACbP,iBAA+D,EAC/DzZ,IAAgB,EAChBH,GAAqB,EACrBke,UAAmB,EACnB9Z,uBAA2E,EAC3EZ,YAA0B,EAC1B5E,mBAA+C;QAuBxB4E;IArBvB,MAAM,EACJtD,cAAc4F,YAAY,EAC1BjH,0BAA0B,EAC1BgH,YAAY,EACZhI,KAAK,EACL+C,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EAAE2J,wBAAwB,KAAK,EAAE,GAAG/I;IAE1C,iEAAiE;IACjE,yDAAyD;IACzD,MAAM2M,iBAAiB,KAAO;IAC9B,MAAM,EAAEC,0BAA0B,EAAE,GAAGsK,IAAAA,4CAAwB;IAE/D,MAAMlT,aAAaC,IAAAA,kCAAa,EAC9BiB,aAAa3F,IAAI,EACjBtB;IAGF,MAAMmI,kBAAiBxD,4BAAAA,aAAa0B,OAAO,CAACiZ,GAAG,CAC7ClU,8CAA4B,sBADPzG,0BAEpBgL,KAAK;IAER,6EAA6E;IAC7E,wEAAwE;IACxE,yEAAyE;IACzE,2EAA2E;IAC3E,UAAU;IACV,MAAMxI,mCAAmC,IAAIC;IAE7C,wDAAwD;IACxD,MAAMmY,+BAA+B,IAAInY;IAEzC,6EAA6E;IAC7E,4EAA4E;IAC5E,4EAA4E;IAC5E,2EAA2E;IAC3E,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,qEAAqE;IACrE,4EAA4E;IAC5E,+DAA+D;IAC/D,MAAMC,gCAAgC,IAAID;IAE1C,4EAA4E;IAC5E,+BAA+B;IAC/B,MAAME,cAAc,IAAIC,wBAAW;IAEnC,MAAMiY,0BAA0Bzc,cAAK,CAACqF,iBAAiB;IACvD,MAAMqX,0BAA0BxY,aAAamB,iBAAiB;IAE9D,iEAAiE;IACjE,8DAA8D;IAC9D,wEAAwE;IACxE,6BAA6B;IAC7B,MAAMnC,2BAA2BC,IAAAA,+CAA8B;IAC/D,MAAMwZ,qCAAqD;QACzDlf,MAAM;QACNiH,OAAO;QACP1B;QACAhG;QACAiH;QACA,wGAAwG;QACxG,gFAAgF;QAChFU,cAAcL,8BAA8BM,MAAM;QAClD,iFAAiF;QACjF,2FAA2F;QAC3F,mCAAmC;QACnCC,YAAY,IAAIR;QAChB,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBE;QACAO,iBAAiB;QACjBiD;QACAhD,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIlB,aAAakB,IAAI;SAAC;QAC5BjC;QACAE,uBAAuB;QACvBgC;QACAC,mBAAmBqX;IACrB;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAMnX,uBAAuB,MAAMnD,kDAAoB,CAACC,GAAG,CACzDsa,oCACAhU,eACApK,MACAH,KACAke;IAGF,MAAM7X,8BAA8C;QAClDhH,MAAM;QACNiH,OAAO;QACP1B;QACAhG;QACAiH;QACAU,cAAcL,8BAA8BM,MAAM;QAClDC,YAAYT;QACZ,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBG;QACAO,iBAAiB;QACjBiD;QACAhD,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIlB,aAAakB,IAAI;SAAC;QAC5BjC;QACAE,uBAAuB;QACvBgC;QACAC,mBAAmBqX;IACrB;IAEA,MAAMlX,6BAA6BpD,kDAAoB,CAACC,GAAG,CACzDoC,6BACAP,aAAauB,SAAS,EACtBF,sBACA/C,wBAAwBC,aAAa,EACrC;QACEnI;QACA0H,SAAS,CAACF;YACR,MAAM4D,SAASC,IAAAA,8CAA0B,EAAC7D;YAE1C,IAAI4D,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAIkX,IAAAA,4CAAsB,EAAC9a,MAAM;gBAC/B,kBAAkB;gBAClBqR,QAAQuH,KAAK,CAAC5Y;gBACd,OAAOlH;YACT;YAEA,IAAIwJ,iCAAiCQ,MAAM,CAACgB,OAAO,EAAE;gBACnD,mEAAmE;gBACnE,iEAAiE;gBACjE;YACF,OAAO,IACLrL,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;gBACAC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;YAChE;QACF;QACA,iFAAiF;QACjF,qCAAqC;QACrCC,YAAYrL;QACZ,+EAA+E;QAC/E,iFAAiF;QACjF,iDAAiD;QACjDgK,QAAQ4X,6BAA6B5X,MAAM;IAC7C;IAGF,4EAA4E;IAC5E,6EAA6E;IAC7E,aAAa;IACb4X,6BAA6B5X,MAAM,CAACiY,gBAAgB,CAClD,SACA;QACEvY,8BAA8B8B,KAAK;IACrC,GACA;QAAE0W,MAAM;IAAK;IAGf,8EAA8E;IAC9E5W,IAAAA,+CAAmB,EAAC3B;IACpB,MAAMA,YAAY4B,UAAU;IAE5BqW,6BAA6BpW,KAAK;IAElC,gEAAgE;IAChE,iEAAiE;IACjE,MAAM,EAAEC,wBAAwB,EAAE,GAAGxH;IACrC,IAAIwH,0BAA0B;QAC5B2R,gCACE,qBAAC+E;YACCC,IAAI;gBACF7J,QAAQuH,KAAK,CAACrU;YAChB;;QAGJ;IACF;IAEA,IAAI4W;IACJ,IAAI;QACFA,sBAAsB,MAAM3W,IAAAA,yDAAgC,EAC1Dd;IAEJ,EAAE,OAAO1D,KAAK;QACZ,IACE0a,6BAA6B5X,MAAM,CAACgB,OAAO,IAC3CxB,iCAAiCQ,MAAM,CAACgB,OAAO,EAC/C;QACA,4EAA4E;QAC9E,OAAO,IACLrL,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnFC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;QAChE;IACF;IAEA,IAAIiX,qBAAqB;QACvB,MAAMC,mCAAmC,IAAI7Y;QAC7C,MAAM8Y,+BAA+B,IAAI9Y;QACzC,MAAM+Y,gCAAgC,IAAI/Y;QAE1C,MAAMgZ,8BAA8C;YAClD5f,MAAM;YACNiH,OAAO;YACP1B;YACAhG;YACAiH;YACAU,cAAcyY,8BAA8BxY,MAAM;YAClDC,YAAYqY;YACZ,sDAAsD;YACtD,qDAAqD;YACrD3Y,aAAa;YACbO,iBAAiB;YACjBiD;YACAhD,YAAYG,0BAAc;YAC1BF,QAAQE,0BAAc;YACtBD,OAAOC,0BAAc;YACrBC,MAAM;mBAAIlB,aAAakB,IAAI;aAAC;YAC5BjC;YACAE,uBAAuB;YACvBgC,gBAAgBxK;YAChByK,mBAAmBoX;QACrB;QAEA,MAAMhX,YAAY,AAChB/K,QAAQ,oBACR+K,SAAS;QACX,MAAM6X,6BAA6Blb,kDAAoB,CAACC,GAAG,CACzDgb,6BACA5X,yBACA,qBAACgG;YACCC,mBAAmBuR,oBAAoBM,iBAAiB;YACxD5R,gBAAgBA;YAChBnJ,yBAAyBA;YACzBoJ,4BAA4BA;YAC5B3P,OAAOA;YAET;YACE2I,QAAQuY,6BAA6BvY,MAAM;YAC3C5C,SAAS,CAACF;gBACR,MAAM4D,SAASC,IAAAA,8CAA0B,EAAC7D;gBAE1C,IAAI4D,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAIkX,IAAAA,4CAAsB,EAAC9a,MAAM;oBAC/B,kBAAkB;oBAClBqR,QAAQuH,KAAK,CAAC5Y;oBACd,OAAOlH;gBACT;gBAEA,IAAIuiB,6BAA6BvY,MAAM,CAACgB,OAAO,EAAE;gBAC/C,4EAA4E;gBAC9E,OAAO,IACLrL,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;gBAChE;YACF;QAGF;QAGF,4EAA4E;QAC5E,4DAA4D;QAC5D,8BAA8B;QAC9BmX,6BAA6BvY,MAAM,CAACiY,gBAAgB,CAClD,SACA;YACEO,8BAA8BhX,KAAK;QACrC,GACA;YAAE0W,MAAM;QAAK;QAGfQ,2BAA2BE,KAAK,CAAC,CAAC1b;YAChC,IACEqb,6BAA6BvY,MAAM,CAACgB,OAAO,IAC3C6X,IAAAA,6CAA2B,EAAC3b,MAC5B;YACA,4EAA4E;YAC9E,OAAO,IACLvH,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;gBACA,8EAA8E;gBAC9E,mFAAmF;gBACnFC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;YAChE;QACF;QAEA,sEAAsE;QACtE,uGAAuG;QACvGE,IAAAA,+CAAmB,EAAC3B;QACpB,MAAMA,YAAY4B,UAAU;QAC5BgX,6BAA6B/W,KAAK;IACpC;IAEA,MAAMsX,6BAA6B,IAAIrZ;IACvC,MAAMsZ,8BAA8B,IAAItZ;IAExC,MAAMuZ,mCAAmD;QACvDngB,MAAM;QACNiH,OAAO;QACP1B;QACAhG;QACAiH;QACA,wGAAwG;QACxG,gFAAgF;QAChFU,cAAcgZ,4BAA4B/Y,MAAM;QAChD,iFAAiF;QACjF,2FAA2F;QAC3F,mCAAmC;QACnCC,YAAY,IAAIR;QAChB,8EAA8E;QAC9EE,aAAa;QACbO,iBAAiB;QACjBiD;QACAhD,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIlB,aAAakB,IAAI;SAAC;QAC5BjC;QACAE,uBAAuB;QACvBgC;QACAC,mBAAmBqX;IACrB;IAEA,MAAMmB,yBAAyB,MAAMzb,kDAAoB,CAACC,GAAG,CAC3Dub,kCACAjV,eACApK,MACAH,KACAke;IAGF,MAAMzV,wBAAwBC,IAAAA,4CAA0B,EACtD,MAAM,yBAAyB;;IAGjC,MAAMK,4BAA4C;QAChD1J,MAAM;QACNiH,OAAO;QACP1B;QACAhG;QACAiH;QACAU,cAAcgZ,4BAA4B/Y,MAAM;QAChDC,YAAY6Y;QACZ,8EAA8E;QAC9EnZ,aAAa;QACbO,iBAAiB+B;QACjBkB;QACAhD,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIlB,aAAakB,IAAI;SAAC;QAC5BjC;QACAE,uBAAuB;QACvBgC;QACAC,mBAAmBqX;IACrB;IAEA,MAAM7E,oBAAoB,MAAMvR,IAAAA,yDAAgC,EAC9DwX,IAAAA,2DAAkC,EAChC;QACE,MAAMC,yBAAyB3b,kDAAoB,CAACC,GAAG,CACrD,qBAAqB;QACrB8E,2BACA,sBAAsB;QACtBjD,aAAauB,SAAS,EACtB,4CAA4C;QAC5CoY,wBACArb,wBAAwBC,aAAa,EACrC;YACEnI;YACA0H,SAAS,CAACF;gBACR,IACE4b,2BAA2B9Y,MAAM,CAACgB,OAAO,IACzC6X,IAAAA,6CAA2B,EAAC3b,MAC5B;oBACA,OAAOA,IAAI4D,MAAM;gBACnB;gBAEA,IAAIkX,IAAAA,4CAAsB,EAAC9a,MAAM;oBAC/B,kBAAkB;oBAClBqR,QAAQuH,KAAK,CAAC5Y;oBACd,OAAOlH;gBACT;gBAEA,OAAO+K,IAAAA,8CAA0B,EAAC7D;YACpC;YACA8C,QAAQ8Y,2BAA2B9Y,MAAM;QAC3C;QAGF,sEAAsE;QACtE,kEAAkE;QAClE,8BAA8B;QAC9B8Y,2BAA2B9Y,MAAM,CAACiY,gBAAgB,CAChD,SACA;YACEc,4BAA4BvX,KAAK;QACnC,GACA;YAAE0W,MAAM;QAAK;QAGf,OAAOiB;IACT,GACA;QACEL,2BAA2BtX,KAAK;IAClC;IAIJ,MAAM4X,wBAAwBlX,IAAAA,4CAA0B,EACtD,MAAM,wBAAwB;;IAEhC,MAAMmX,6BAA6B,IAAI5Z;IACvC,MAAM6Z,8BAA8B,IAAI7Z;IAExC,MAAM8Z,4BAA4C;QAChD1gB,MAAM;QACNiH,OAAO;QACP1B;QACAhG;QACAiH;QACAU,cAAcuZ,4BAA4BtZ,MAAM;QAChDC,YAAYoZ;QACZ,oFAAoF;QACpF1Z,aAAa;QACbO,iBAAiBkZ;QACjBjW;QACAhD,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIlB,aAAakB,IAAI;SAAC;QAC5BjC;QACAE,uBAAuB;QACvBgC;QACAC,mBAAmBoX;IACrB;IAEA,IAAI2B,oBAAoBC,IAAAA,8CAA4B;IAEpD,IAAI;QACF,MAAM5Y,YAAY,AAChB/K,QAAQ,oBACR+K,SAAS;QACX,IAAI,EAAE1B,SAASua,kBAAkB,EAAE,GACjC,MAAMR,IAAAA,2DAAkC,EACtC;YACE,MAAMS,2BAA2Bnc,kDAAoB,CAACC,GAAG,CACvD8b,2BACA1Y,yBACA,qBAACgG;gBACCC,mBAAmBmM,kBAAkB0F,iBAAiB;gBACtD5R,gBAAgBA;gBAChBnJ,yBAAyBA;gBACzBoJ,4BAA4BA;gBAC5B3P,OAAOA;gBAET;gBACE2I,QAAQqZ,2BAA2BrZ,MAAM;gBACzC5C,SAAS,CAACF,KAAc0c;oBACtB,IACEf,IAAAA,6CAA2B,EAAC3b,QAC5Bmc,2BAA2BrZ,MAAM,CAACgB,OAAO,EACzC;wBACA,MAAM6Y,iBAAiBD,UAAUC,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtCC,IAAAA,2CAAyB,EACvB7f,WACA4f,gBACAL,mBACAJ;wBAEJ;wBACA;oBACF;oBAEA,IAAIpB,IAAAA,4CAAsB,EAAC9a,MAAM;wBAC/B,kBAAkB;wBAClBqR,QAAQuH,KAAK,CAAC5Y;wBACd,OAAOlH;oBACT;oBAEA,OAAO+K,IAAAA,8CAA0B,EAAC7D;gBACpC;YAGF;YAGF,sEAAsE;YACtE,kEAAkE;YAClE,8BAA8B;YAC9Bmc,2BAA2BrZ,MAAM,CAACiY,gBAAgB,CAChD,SACA;gBACEqB,4BAA4B9X,KAAK;YACnC,GACA;gBAAE0W,MAAM;YAAK;YAGf,OAAOyB;QACT,GACA;YACEN,2BAA2B7X,KAAK;QAClC;QAGJ,MAAM,EAAEuY,cAAc,EAAE,GAAG,MAAMC,IAAAA,uCAAc,EAACN;QAChDtG,gCACE,qBAAC+E;YACCC,IAAI6B,0CAAwB,CAAC/G,IAAI,CAC/B,MACAjZ,WACA8f,iBAAiBG,8BAAY,CAACnF,KAAK,GAAGmF,8BAAY,CAACC,IAAI,EACvDX,mBACAvX;;IAIR,EAAE,OAAOmY,aAAa;QACpB,8EAA8E;QAC9E,gDAAgD;QAEhD,IAAIC,kBAAkBJ,0CAAwB,CAAC/G,IAAI,CACjD,MACAjZ,WACAigB,8BAAY,CAACI,OAAO,EACpBd,mBACAvX;QAGF,IAAItM,QAAQC,GAAG,CAACqL,gBAAgB,IAAItL,QAAQC,GAAG,CAACsL,sBAAsB,EAAE;YACtE,8EAA8E;YAC9E,mFAAmF;YACnF,MAAMqZ,0BAA0BF;YAChCA,kBAAkB;gBAChB9L,QAAQuH,KAAK,CACX;gBAEFvH,QAAQuH,KAAK,CAACsE;gBACdG;YACF;QACF;QAEAnH,gCAAkB,qBAAC+E;YAAUC,IAAIiC;;IACnC;AACF;AAEA,eAAelC,UAAU,EAAEC,EAAE,EAAyB;IACpD,IAAI;QACF,MAAMA;IACR,EAAE,OAAM,CAAC;IACT,OAAO;AACT;AAcA;;CAEC,GACD,SAASoC,+BAA+BvgB,SAAoB;IAC1D,MAAM,EAAEqC,kBAAkB,EAAE,GAAGrC;IAC/B,IAAI,CAACqC,oBAAoB,OAAO;IAEhC,OAAO;AACT;AAEA,eAAewQ,kBACb/P,GAAoB,EACpBzB,GAAqB,EACrB9B,GAAqB,EACrB0E,QAAqC,EACrCvE,IAAgB,EAChBvB,mBAA+C;IAE/C,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAMkX,YAAY;IAElB,MAAM,EACJhK,WAAW,EACXjN,0BAA0B,EAC1BgH,YAAY,EACZhI,KAAK,EACLc,QAAQ,EACRiC,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EACJ2J,wBAAwB,KAAK,EAC7B0N,QAAQ,EACRC,aAAa,EACblT,uBAAuB,EACvB0B,YAAY,EACZyR,WAAW,EACXzT,MAAM,KAAK,EACXqE,YAAY,EACZC,sBAAsB,EACtBoP,aAAa,KAAK,EAClB7T,6BAA6B,EAC7BnF,IAAI,EACJiZ,qBAAqB,EACrBE,4BAA4B,EAC7B,GAAG/W;IAEJmF,8BAA8B3B;IAE9B,MAAMQ,aAAaC,IAAAA,kCAAa,EAAC1E,MAAMtB;IAEvC,MAAM,EAAE2O,0BAA0B,EAAEqK,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAMC,4BAA4BC,IAAAA,0DAA4B,EAACna;IAE/D,MAAMoa,kBAAkBC,IAAAA,yBAAiB,EACvC9G,IAAAA,iBAAS,IAAG+G,uBAAuB,IACnChQ,aAAaiQ,mBAAmB;IAGlC,MAAMC,YACJf,cAAcgB,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDrW,GAAG,CAAC,CAACoW,WAAc,CAAA;YAClBE,KAAK,GAAG5M,YAAY,OAAO,EAAE0M,WAAWG,IAAAA,wCAAmB,EACzD3Y,KACA,QACC;YACH4Y,SAAS,EAAEjB,gDAAAA,4BAA8B,CAACa,SAAS;YACnDjB;YACAsB,UAAU;YACVhb;QACF,CAAA;IAEJ,MAAM,CAAC0P,gBAAgBuL,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1DzB,eACA,6CAA6C;IAC7C,8EAA8E;IAC9ExL,aACAyL,aACAI,8BACAgB,IAAAA,wCAAmB,EAAC3Y,KAAK,OACzBnC,OACAW;IAGF,MAAMwa,4BAAwD,IAAI/K;IAClE,+EAA+E;IAC/E,MAAMgL,gBAAgB,CAAC,CAAC9Q,aAAa7K,iBAAiB;IACtD,SAAS4b,qBAAqBxV,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB/C,KAAK;IAE5B;IACA,MAAMmZ,+BAA+BC,IAAAA,qDAAiC,EACpEtV,KACA0T,YACAwB,2BACAC,eACAC;IAGF,SAASG,qBAAqB3V,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAR,mBAAmB/C,KAAK;IAE5B;IACA,MAAMsZ,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD1V,KACA0T,YACAwB,2BACAM,mBACAL,eACAI;IAGF,IAAI4H,6BAAgE;IACpE,MAAMC,oBAAoB,CAACrhB;QACzB6E,SAAShI,OAAO,KAAK,CAAC;QACtBgI,SAAShI,OAAO,CAACmD,KAAK,GAAGiC,IAAImJ,SAAS,CAACpL;IACzC;IACA,MAAM2F,YAAY,CAAC3F,MAAc2O;QAC/B1M,IAAI0D,SAAS,CAAC3F,MAAM2O;QACpB0S,kBAAkBrhB;QAClB,OAAOiC;IACT;IACA,MAAM6X,eAAe,CAAC9Z,MAAc2O;QAClC,IAAI2O,MAAMgE,OAAO,CAAC3S,QAAQ;YACxBA,MAAMnC,OAAO,CAAC,CAAC+U;gBACbtf,IAAI6X,YAAY,CAAC9Z,MAAMuhB;YACzB;QACF,OAAO;YACLtf,IAAI6X,YAAY,CAAC9Z,MAAM2O;QACzB;QACA0S,kBAAkBrhB;IACpB;IAEA,MAAMwI,kBAAkBC,sBAAsBH;IAE9C,IAAI6B,iBAAwC;IAE5C,IAAI;QACF,IAAI7B,aAAa0H,eAAe,EAAE;YAChC;;;;;;;;;;;;OAYC,GAED,wEAAwE;YACxE,0EAA0E;YAC1E,mEAAmE;YACnE,yEAAyE;YACzE,qBAAqB;YACrB,MAAM7J,mCAAmC,IAAIC;YAE7C,wDAAwD;YACxD,MAAMmY,+BAA+B,IAAInY;YAEzC,sEAAsE;YACtE,sEAAsE;YACtE,kEAAkE;YAClE,wEAAwE;YACxE,wEAAwE;YACxE,wEAAwE;YACxE,wEAAwE;YACxE,0EAA0E;YAC1E,sEAAsE;YACtE,wEAAwE;YACxE,+BAA+B;YAC/B,MAAMC,gCAAgC,IAAID;YAE1C,kFAAkF;YAClF,yBAAyB;YACzB,MAAME,cAAc,IAAIC,wBAAW;YAEnC,IAAIib;YACJ,IAAIrc,wBAAsD;YAC1D,IAAIF,2BAA4D;YAEhE,IAAIlE,WAAWoE,qBAAqB,EAAE;gBACpC,sEAAsE;gBACtE,wEAAwE;gBACxE,uEAAuE;gBACvE,cAAc;gBACdqc,kBAAkBrc,wBAChBpE,WAAWoE,qBAAqB;YACpC,OAAO;gBACL,iEAAiE;gBACjEqc,kBAAkBvc,2BAChBC,IAAAA,+CAA8B;YAClC;YAEA,MAAMwZ,qCAAqD;gBACzDlf,MAAM;gBACNiH,OAAO;gBACP1B;gBACAhG;gBACAiH;gBACA,wGAAwG;gBACxG,gFAAgF;gBAChFU,cAAcL,8BAA8BM,MAAM;gBAClD,iFAAiF;gBACjF,2FAA2F;gBAC3F,mCAAmC;gBACnCC,YAAY,IAAIR;gBAChB,0EAA0E;gBAC1E,2EAA2E;gBAC3E,uBAAuB;gBACvBE;gBACAO,iBAAiB;gBACjBiD;gBACAhD,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIlB,aAAakB,IAAI;iBAAC;gBAC5BjC;gBACAE;gBACAgC,gBAAgBxK;gBAChByK,mBAAmBzK;YACrB;YAEA,0FAA0F;YAC1F,wFAAwF;YACxF,MAAM2K,uBAAuB,MAAMnD,kDAAoB,CAACC,GAAG,CACzDsa,oCACAhU,eACApK,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAGrB,MAAM6G,8BAA+C2D,iBAAiB;gBACpE3K,MAAM;gBACNiH,OAAO;gBACP1B;gBACAhG;gBACAiH;gBACAU,cAAcL,8BAA8BM,MAAM;gBAClDC,YAAYT;gBACZ,0EAA0E;gBAC1E,2EAA2E;gBAC3E,uBAAuB;gBACvBG;gBACAO,iBAAiB;gBACjBiD;gBACAhD,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIlB,aAAakB,IAAI;iBAAC;gBAC5BjC;gBACAE;gBACAgC,gBAAgBxK;gBAChByK,mBAAmBzK;YACrB;YAEA,MAAM4K,6BAA6BpD,kDAAoB,CAACC,GAAG,CACzDoC,6BACAP,aAAauB,SAAS,EACtBF,sBACA/C,wBAAwBC,aAAa,EACrC;gBACEnI;gBACA0H,SAAS,CAACF;oBACR,MAAM4D,SAASC,IAAAA,8CAA0B,EAAC7D;oBAE1C,IAAI4D,QAAQ;wBACV,OAAOA;oBACT;oBAEA,IAAIkX,IAAAA,4CAAsB,EAAC9a,MAAM;wBAC/B,kBAAkB;wBAClBqR,QAAQuH,KAAK,CAAC5Y;wBACd,OAAOlH;oBACT;oBAEA,IAAIwJ,iCAAiCQ,MAAM,CAACgB,OAAO,EAAE;wBACnD,mEAAmE;wBACnE,iEAAiE;wBACjE;oBACF,OAAO,IACLrL,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;wBACAC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;oBAChE;gBACF;gBACA,iFAAiF;gBACjF,qCAAqC;gBACrCC,YAAYrL;gBACZ,+EAA+E;gBAC/E,iFAAiF;gBACjF,iDAAiD;gBACjDgK,QAAQ4X,6BAA6B5X,MAAM;YAC7C;YAGF,sEAAsE;YACtE,kEAAkE;YAClE,8BAA8B;YAC9B4X,6BAA6B5X,MAAM,CAACiY,gBAAgB,CAClD,SACA;gBACEvY,8BAA8B8B,KAAK;YACrC,GACA;gBAAE0W,MAAM;YAAK;YAGf,8EAA8E;YAC9E5W,IAAAA,+CAAmB,EAAC3B;YACpB,MAAMA,YAAY4B,UAAU;YAE5BqW,6BAA6BpW,KAAK;YAElC,gEAAgE;YAChE,iEAAiE;YACjE,IAAIvH,UAAUwH,wBAAwB,EAAE;gBACtC0L,IAAAA,2CAAyB,EAAClT,WAAWA,UAAUwH,wBAAwB;gBACvE,MAAM,IAAI2L,8CAAqB;YACjC;YAEA,IAAIiL;YACJ,IAAI;gBACFA,sBAAsB,MAAM3W,IAAAA,yDAAgC,EAC1Dd;YAEJ,EAAE,OAAO1D,KAAK;gBACZ,IACE0a,6BAA6B5X,MAAM,CAACgB,OAAO,IAC3CxB,iCAAiCQ,MAAM,CAACgB,OAAO,EAC/C;gBACA,4EAA4E;gBAC9E,OAAO,IACLrL,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;gBAChE;YACF;YAEA,IAAIiX,qBAAqB;gBACvB,MAAMC,mCAAmC,IAAI7Y;gBAC7C,MAAM8Y,+BAA+B,IAAI9Y;gBACzC,MAAM+Y,gCAAgC,IAAI/Y;gBAE1C,MAAMgZ,8BAA8C;oBAClD5f,MAAM;oBACNiH,OAAO;oBACP1B;oBACAhG;oBACAiH;oBACAU,cAAcyY,8BAA8BxY,MAAM;oBAClDC,YAAYqY;oBACZ,sDAAsD;oBACtD,qDAAqD;oBACrD3Y,aAAa;oBACbO,iBAAiB;oBACjBiD;oBACAhD,YAAYG,0BAAc;oBAC1BF,QAAQE,0BAAc;oBACtBD,OAAOC,0BAAc;oBACrBC,MAAM;2BAAIlB,aAAakB,IAAI;qBAAC;oBAC5BjC;oBACAE;oBACAgC,gBAAgBxK;oBAChByK,mBAAmBzK;gBACrB;gBAEA,MAAM6K,YAAY,AAChB/K,QAAQ,oBACR+K,SAAS;gBACX,MAAM6X,6BAA6Blb,kDAAoB,CAACC,GAAG,CACzDgb,6BACA5X,yBACA,qBAACgG;oBACCC,mBAAmBuR,oBAAoBM,iBAAiB;oBACxD5R,gBAAgBA;oBAChBnJ,yBAAyBA;oBACzBoJ,4BAA4BA;oBAC5B3P,OAAOA;oBAET;oBACE2I,QAAQuY,6BAA6BvY,MAAM;oBAC3C5C,SAAS,CAACF;wBACR,MAAM4D,SAASC,IAAAA,8CAA0B,EAAC7D;wBAE1C,IAAI4D,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAIkX,IAAAA,4CAAsB,EAAC9a,MAAM;4BAC/B,kBAAkB;4BAClBqR,QAAQuH,KAAK,CAAC5Y;4BACd,OAAOlH;wBACT;wBAEA,IAAIuiB,6BAA6BvY,MAAM,CAACgB,OAAO,EAAE;wBAC/C,4EAA4E;wBAC9E,OAAO,IACLrL,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnFC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;wBAChE;oBACF;oBACAiU,kBAAkB;wBAAC/C;qBAAgB;gBACrC;gBAGF,sEAAsE;gBACtE,kEAAkE;gBAClE,8BAA8B;gBAC9BiG,6BAA6BvY,MAAM,CAACiY,gBAAgB,CAClD,SACA;oBACEO,8BAA8BhX,KAAK;gBACrC,GACA;oBAAE0W,MAAM;gBAAK;gBAGfQ,2BAA2BE,KAAK,CAAC,CAAC1b;oBAChC,IACEqb,6BAA6BvY,MAAM,CAACgB,OAAO,IAC3C6X,IAAAA,6CAA2B,EAAC3b,MAC5B;oBACA,4EAA4E;oBAC9E,OAAO,IACLvH,QAAQC,GAAG,CAACqL,gBAAgB,IAC5BtL,QAAQC,GAAG,CAACsL,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFC,IAAAA,iEAAyC,EAACjE,KAAKjD,UAAUmH,KAAK;oBAChE;gBACF;gBAEA,sEAAsE;gBACtE,uGAAuG;gBACvGE,IAAAA,+CAAmB,EAAC3B;gBACpB,MAAMA,YAAY4B,UAAU;gBAC5BgX,6BAA6B/W,KAAK;YACpC;YAEA,MAAMsX,6BAA6B,IAAIrZ;YACvC,MAAMsZ,8BAA8B,IAAItZ;YAExC,MAAMuZ,mCAAmD;gBACvDngB,MAAM;gBACNiH,OAAO;gBACP1B;gBACAhG;gBACAiH;gBACA,wGAAwG;gBACxG,gFAAgF;gBAChFU,cAAcgZ,4BAA4B/Y,MAAM;gBAChD,iFAAiF;gBACjF,2FAA2F;gBAC3F,mCAAmC;gBACnCC,YAAY,IAAIR;gBAChB,8EAA8E;gBAC9EE,aAAa;gBACbO,iBAAiB;gBACjBiD;gBACAhD,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIlB,aAAakB,IAAI;iBAAC;gBAC5BjC;gBACAE;gBACAgC,gBAAgBxK;gBAChByK,mBAAmBzK;YACrB;YAEA,MAAMijB,yBAAyB,MAAMzb,kDAAoB,CAACC,GAAG,CAC3Dub,kCACAjV,eACApK,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAGrB,MAAMiJ,wBAAwBC,IAAAA,4CAA0B,EACtDN;YAEF,IAAIG,kBAAkB;YAEtB,MAAMQ,4BAA6CiB,iBAAiB;gBAClE3K,MAAM;gBACNiH,OAAO;gBACP1B;gBACAhG;gBACAiH;gBACAU,cAAcgZ,4BAA4B/Y,MAAM;gBAChDC,YAAY6Y;gBACZ,8EAA8E;gBAC9EnZ,aAAa;gBACbO,iBAAiB+B;gBACjBkB;gBACAhD,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIlB,aAAakB,IAAI;iBAAC;gBAC5BjC;gBACAE;gBACAgC,gBAAgBxK;gBAChByK,mBAAmBzK;YACrB;YAEA,IAAIyM,qBAAqB;YACzB,MAAMwQ,oBAAqBwH,6BACzB,MAAM/Y,IAAAA,yDAAgC,EACpCwX,IAAAA,2DAAkC,EAChC;gBACE,MAAMC,yBAAyB3b,kDAAoB,CAACC,GAAG,CACrD,qBAAqB;gBACrB8E,2BACA,sBAAsB;gBACtBjD,aAAauB,SAAS,EACtB,4CAA4C;gBAC5CoY,wBACArb,wBAAwBC,aAAa,EACrC;oBACEnI;oBACA0H,SAAS,CAACF;wBACR,OAAOyV,6BAA6BzV;oBACtC;oBACA8C,QAAQ8Y,2BAA2B9Y,MAAM;gBAC3C;gBAGF,gEAAgE;gBAChE,iEAAiE;gBACjE,qCAAqC;gBACrC8Y,2BAA2B9Y,MAAM,CAACiY,gBAAgB,CAChD,SACA;oBACEc,4BAA4BvX,KAAK;gBACnC,GACA;oBAAE0W,MAAM;gBAAK;gBAGf,MAAMvV,kBAAkB,MAAMwW;gBAC9B1W,qBAAqB;gBAErB,OAAOE;YACT,GACA;gBACE,IAAImW,2BAA2B9Y,MAAM,CAACgB,OAAO,EAAE;oBAC7C,4EAA4E;oBAC5E,6EAA6E;oBAC7Ee,kBAAkB;oBAClB;gBACF;gBAEA,IAAIU,oBAAoB;oBACtB,kFAAkF;oBAClF,iCAAiC;oBACjCV,kBAAkB;gBACpB;gBAEA+W,2BAA2BtX,KAAK;YAClC;YAIN,MAAM4X,wBAAwBlX,IAAAA,4CAA0B,EACtDN;YAGF,MAAMyX,6BAA6B,IAAI5Z;YACvC,MAAM6Z,8BAA8B,IAAI7Z;YAExC,MAAM8Z,4BAA4C;gBAChD1gB,MAAM;gBACNiH,OAAO;gBACP1B;gBACAhG;gBACAiH;gBACAU,cAAcuZ,4BAA4BtZ,MAAM;gBAChDC,YAAYoZ;gBACZ,oFAAoF;gBACpF1Z,aAAa;gBACbO,iBAAiBkZ;gBACjBjW;gBACAhD,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIlB,aAAakB,IAAI;iBAAC;gBAC5BjC;gBACAE;gBACAgC,gBAAgBxK;gBAChByK,mBAAmBzK;YACrB;YAEA,IAAIwjB,oBAAoBC,IAAAA,8CAA4B;YAEpD,MAAM5Y,YAAY,AAChB/K,QAAQ,oBACR+K,SAAS;YACX,IAAI,EAAE1B,SAASua,kBAAkB,EAAE9T,SAAS,EAAE,GAC5C,MAAMsT,IAAAA,2DAAkC,EACtC;gBACE,MAAMS,2BAA2Bnc,kDAAoB,CAACC,GAAG,CACvD8b,2BACA1Y,yBACA,qBAACgG;oBACCC,mBAAmBmM,kBAAkB0F,iBAAiB;oBACtD5R,gBAAgBA;oBAChBnJ,yBAAyBA;oBACzBoJ,4BAA4BA;oBAC5B3P,OAAOA;oBAET;oBACE2I,QAAQqZ,2BAA2BrZ,MAAM;oBACzC5C,SAAS,CAACF,KAAc0c;wBACtB,IACEf,IAAAA,6CAA2B,EAAC3b,QAC5Bmc,2BAA2BrZ,MAAM,CAACgB,OAAO,EACzC;4BACA,MAAM6Y,iBAAqC,AACzCD,UACAC,cAAc;4BAChB,IAAI,OAAOA,mBAAmB,UAAU;gCACtCC,IAAAA,2CAAyB,EACvB7f,WACA4f,gBACAL,mBACAJ;4BAEJ;4BACA;wBACF;wBAEA,OAAOrG,yBAAyB7V,KAAK0c;oBACvC;oBACA1E,WAAW,CAAChf;wBACVA,QAAQ2P,OAAO,CAAC,CAACmC,OAAOmN;4BACtBhC,aAAagC,KAAKnN;wBACpB;oBACF;oBACAoN,kBAAkBnE;oBAClBoE,kBAAkB;wBAAC/C;qBAAgB;gBACrC;gBAGF,gEAAgE;gBAChE,oEAAoE;gBACpE,kCAAkC;gBAClC+G,2BAA2BrZ,MAAM,CAACiY,gBAAgB,CAChD,SACA;oBACEqB,4BAA4B9X,KAAK;gBACnC,GACA;oBAAE0W,MAAM;gBAAK;gBAGf,OAAOyB;YACT,GACA;gBACEN,2BAA2B7X,KAAK;YAClC;YAGJ,MAAM,EAAErC,OAAO,EAAE4a,cAAc,EAAE,GAC/B,MAAMC,IAAAA,uCAAc,EAACN;YAEvB,0EAA0E;YAC1E,2EAA2E;YAC3E,kCAAkC;YAClC,IAAI,CAACvW,uBAAuB;gBAC1B8W,IAAAA,0CAAwB,EACtBhgB,WACA8f,iBAAiBG,8BAAY,CAACnF,KAAK,GAAGmF,8BAAY,CAACC,IAAI,EACvDX,mBACAvX;YAEJ;YAEA,MAAMwS,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtD7C;gBACAR;gBACAsD,sBAAsB7B;gBACtBjC;gBACAY,iBAAiBA;YACnB;YAEA,MAAMhY,aAAa,MAAMqhB,IAAAA,oCAAc,EAAC7H,kBAAkB8H,QAAQ;YAClE7c,SAASzE,UAAU,GAAGA;YACtByE,SAAS8c,WAAW,GAAG,MAAMC,mBAC3BxhB,YACA8I,2BACAjD,cACAlF;YAGF,yEAAyE;YACzE,wEAAwE;YACxE,yEAAyE;YACzE,iEAAiE;YACjE,MAAM8gB,yBACJ9iB,uBAAuBA,oBAAoBkV,IAAI,GAAG;YAEpD,IAAIvL,mBAAmBmZ,wBAAwB;gBAC7C,eAAe;gBACf,4FAA4F;gBAC5F,0FAA0F;gBAC1F,0FAA0F;gBAC1F,oCAAoC;gBACpC,IAAItV,aAAa,MAAM;oBACrB,oBAAoB;oBACpB1H,SAAS0H,SAAS,GAAG,MAAMuV,IAAAA,4CAA4B,EACrDvV,WACAmU,iBACIjF,uCAAuB,CAACC,KAAK,GAC7BD,uCAAuB,CAACqF,IAAI,EAChC/hB,qBACAyiB;gBAEJ,OAAO;oBACL,oBAAoB;oBACpB3c,SAAS0H,SAAS,GAChB,MAAMwV,IAAAA,4CAA4B,EAACP;gBACvC;gBACA5H,kBAAkBgC,OAAO;gBACzB,OAAO;oBACL5H,iBAAiBmF;oBACjB9E,WAAWoF;oBACXlE,QAAQ,MAAMyM,IAAAA,8CAAwB,EAAClc,SAAS;wBAC9CsV;wBACAlD;oBACF;oBACA1O,eAAeyY,IAAAA,sCAAoB,EACjCrZ,uBACAmX;oBAEF,0CAA0C;oBAC1CtW,qBAAqBP,0BAA0BpC,UAAU;oBACzD4C,iBAAiBR,0BAA0BnC,MAAM;oBACjD4C,gBAAgBnB,gBAAgBU,0BAA0BlC,KAAK;oBAC/D4C,eAAeV,0BAA0BhC,IAAI;oBAC7C/B,uBAAuBmF,IAAAA,4CAA2B,EAACkX;gBACrD;YACF,OAAO;gBACL,cAAc;gBACd,mGAAmG;gBACnG,6EAA6E;gBAC7E,IAAI5gB,UAAUkV,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAI/B,8CAAqB,CAC7B,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIoH,aAAarV;gBACjB,IAAIyG,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAM2O,SAAS,AACbze,QAAQ,oBACRye,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMgH,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAMlH,qBACzB,qBAAC1N;wBACCC,mBAAmByU;wBACnBxU,gBAAgB,KAAO;wBACvBnJ,yBAAyBA;wBACzBoJ,4BAA4BA;wBAC5B3P,OAAOA;wBAETqkB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAAChW,aAC1B;wBACE5F,QAAQ6b,IAAAA,kDAAgC;wBACxCze,SAAS2V;wBACT1b;oBACF;oBAGF,wGAAwG;oBACxGmd,aAAaL,IAAAA,kCAAY,EAAChV,SAASsc;gBACrC;gBAEA,OAAO;oBACLpO,iBAAiBmF;oBACjB9E,WAAWoF;oBACXlE,QAAQ,MAAMkN,IAAAA,6CAAuB,EAACtH,YAAY;wBAChDQ,mBAAmBf,IAAAA,kDAA+B,EAChDhB,kBAAkB8I,eAAe,IACjC1kB,OACAiY;wBAEFmF;wBACAlD;wBACAiE,yBACEhc,IAAIS,SAAS,CAACub,uBAAuB,KAAK;wBAC5CpZ,SAAS5C,IAAIS,SAAS,CAACmC,OAAO;oBAChC;oBACAyG,eAAeyY,IAAAA,sCAAoB,EACjCrZ,uBACAmX;oBAEF,0CAA0C;oBAC1CtW,qBAAqBP,0BAA0BpC,UAAU;oBACzD4C,iBAAiBR,0BAA0BnC,MAAM;oBACjD4C,gBAAgBnB,gBAAgBU,0BAA0BlC,KAAK;oBAC/D4C,eAAeV,0BAA0BhC,IAAI;oBAC7C/B,uBAAuBmF,IAAAA,4CAA2B,EAACkX;gBACrD;YACF;QACF,OAAO,IAAIlZ,aAAa7K,iBAAiB,EAAE;YACzC,uEAAuE;YACvE,IAAIoJ,kBAAkBgC,IAAAA,4CAA0B,EAACN;YAEjD,MAAMtD,2BAA2BC,IAAAA,+CAA8B;YAC/D,MAAMyd,4BAA6CxY,iBAAiB;gBAClE3K,MAAM;gBACNiH,OAAO;gBACP1B;gBACAhG;gBACAiH;gBACAa;gBACAC,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIlB,aAAakB,IAAI;iBAAC;gBAC5BjC;YACF;YACA,MAAMf,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/Cue,2BACAjY,eACApK,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAErB,MAAMia,oBAAqBwH,6BACzB,MAAMwB,IAAAA,mEAA0C,EAC9Cze,kDAAoB,CAACC,GAAG,CACtBue,2BACA1c,aAAa3B,sBAAsB,EACnC,4CAA4C;YAC5CJ,YACAK,wBAAwBC,aAAa,EACrC;gBACEnI;gBACA0H,SAASuV;YACX;YAIN,MAAMuJ,oBAAoC;gBACxCrjB,MAAM;gBACNiH,OAAO;gBACP1B;gBACAhG;gBACAiH;gBACAa;gBACAC,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIlB,aAAakB,IAAI;iBAAC;gBAC5BjC;YACF;YACA,MAAMuC,YAAY,AAChB/K,QAAQ,oBACR+K,SAAS;YACX,MAAM,EAAE1B,SAASua,kBAAkB,EAAE9T,SAAS,EAAE,GAC9C,MAAMpI,kDAAoB,CAACC,GAAG,CAC5Bye,mBACArb,yBACA,qBAACgG;gBACCC,mBAAmBmM,kBAAkB0F,iBAAiB;gBACtD5R,gBAAgBA;gBAChBnJ,yBAAyBA;gBACzBoJ,4BAA4BA;gBAC5B3P,OAAOA;gBAET;gBACE+F,SAAS2V;gBACTmC,WAAW,CAAChf;oBACVA,QAAQ2P,OAAO,CAAC,CAACmC,OAAOmN;wBACtBhC,aAAagC,KAAKnN;oBACpB;gBACF;gBACAoN,kBAAkBnE;gBAClBoE,kBAAkB;oBAAC/C;iBAAgB;YACrC;YAEJ,MAAMmC,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtD7C;gBACAR;gBACAsD,sBAAsB7B;gBACtBjC;gBACAY,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAMhY,aAAa,MAAMqhB,IAAAA,oCAAc,EAAC7H,kBAAkB8H,QAAQ;YAElE,IAAIP,+BAA+BvgB,YAAY;gBAC7CiE,SAASzE,UAAU,GAAGA;gBACtByE,SAAS8c,WAAW,GAAG,MAAMC,mBAC3BxhB,YACAyiB,mBACA5c,cACAlF;YAEJ;YAEA,MAAM,EAAE+E,OAAO,EAAE4a,cAAc,EAAE,GAC/B,MAAMC,IAAAA,uCAAc,EAACN;YAEvB;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAI3M,IAAAA,qCAAmB,EAAC7M,gBAAgBic,eAAe,GAAG;gBACxD,IAAIvW,aAAa,MAAM;oBACrB,qBAAqB;oBACrB1H,SAAS0H,SAAS,GAAG,MAAMuV,IAAAA,4CAA4B,EACrDvV,WACAmU,iBACIjF,uCAAuB,CAACC,KAAK,GAC7BD,uCAAuB,CAACqF,IAAI,EAChC/hB,qBACAkG;gBAEJ,OAAO;oBACL,qBAAqB;oBACrBJ,SAAS0H,SAAS,GAAG,MAAMwV,IAAAA,4CAA4B,EACrD9c;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtD2U,kBAAkBgC,OAAO;gBACzB,OAAO;oBACL5H,iBAAiBmF;oBACjB9E,WAAWoF;oBACXlE,QAAQ,MAAMyM,IAAAA,8CAAwB,EAAClc,SAAS;wBAC9CsV;wBACAlD;oBACF;oBACA1O,eAAe3C,gBAAgBic,eAAe;oBAC9C,0CAA0C;oBAC1CrZ,qBAAqBkZ,0BAA0B7b,UAAU;oBACzD4C,iBAAiBiZ,0BAA0B5b,MAAM;oBACjD4C,gBAAgBnB,gBAAgBma,0BAA0B3b,KAAK;oBAC/D4C,eAAe+Y,0BAA0Bzb,IAAI;gBAC/C;YACF,OAAO,IAAInI,uBAAuBA,oBAAoBkV,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/BpP,SAAS0H,SAAS,GAAG,MAAMwV,IAAAA,4CAA4B,EACrD9c;gBAGF,OAAO;oBACL+O,iBAAiBmF;oBACjB9E,WAAWoF;oBACXlE,QAAQ,MAAMyM,IAAAA,8CAAwB,EAAClc,SAAS;wBAC9CsV;wBACAlD;oBACF;oBACA1O,eAAe3C,gBAAgBic,eAAe;oBAC9C,0CAA0C;oBAC1CrZ,qBAAqBkZ,0BAA0B7b,UAAU;oBACzD4C,iBAAiBiZ,0BAA0B5b,MAAM;oBACjD4C,gBAAgBnB,gBAAgBma,0BAA0B3b,KAAK;oBAC/D4C,eAAe+Y,0BAA0Bzb,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAItG,UAAUkV,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAI/B,8CAAqB,CAC7B,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIoH,aAAarV;gBACjB,IAAIyG,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAM2O,SAAS,AACbze,QAAQ,oBACRye,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMgH,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAMlH,qBACzB,qBAAC1N;wBACCC,mBAAmByU;wBACnBxU,gBAAgB,KAAO;wBACvBnJ,yBAAyBA;wBACzBoJ,4BAA4BA;wBAC5B3P,OAAOA;wBAETqkB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAAChW,aAC1B;wBACE5F,QAAQ6b,IAAAA,kDAAgC;wBACxCze,SAAS2V;wBACT1b;oBACF;oBAGF,wGAAwG;oBACxGmd,aAAaL,IAAAA,kCAAY,EAAChV,SAASsc;gBACrC;gBAEA,OAAO;oBACLpO,iBAAiBmF;oBACjB9E,WAAWoF;oBACXlE,QAAQ,MAAMkN,IAAAA,6CAAuB,EAACtH,YAAY;wBAChDQ,mBAAmBf,IAAAA,kDAA+B,EAChDhB,kBAAkB8I,eAAe,IACjC1kB,OACAiY;wBAEFmF;wBACAlD;wBACAiE,yBACEhc,IAAIS,SAAS,CAACub,uBAAuB,KAAK;wBAC5CpZ,SAAS5C,IAAIS,SAAS,CAACmC,OAAO;oBAChC;oBACAyG,eAAe3C,gBAAgBic,eAAe;oBAC9C,0CAA0C;oBAC1CrZ,qBAAqBkZ,0BAA0B7b,UAAU;oBACzD4C,iBAAiBiZ,0BAA0B5b,MAAM;oBACjD4C,gBAAgBnB,gBAAgBma,0BAA0B3b,KAAK;oBAC/D4C,eAAe+Y,0BAA0Bzb,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAM6b,uBAAwC5Y,iBAAiB;gBAC7D3K,MAAM;gBACNiH,OAAO;gBACP1B;gBACAiB;gBACAc,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIlB,aAAakB,IAAI;iBAAC;YAC9B;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAMhD,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/C2e,sBACArY,eACApK,MACAH,KACA8B,IAAItC,UAAU,KAAK;YAGrB,MAAMia,oBAAqBwH,6BACzB,MAAMwB,IAAAA,mEAA0C,EAC9Cze,kDAAoB,CAACC,GAAG,CACtB2e,sBACA9c,aAAa3B,sBAAsB,EACnCJ,YACAK,wBAAwBC,aAAa,EACrC;gBACEnI;gBACA0H,SAASuV;YACX;YAIN,MAAMhV,yBAAyB,AAC7B7H,QAAQ,oBACR6H,sBAAsB;YACxB,MAAM6W,aAAa,MAAMhX,kDAAoB,CAACC,GAAG,CAC/C2e,sBACAze,sCACA,qBAACkJ;gBACCC,mBAAmBmM,kBAAkB0F,iBAAiB;gBACtD5R,gBAAgBA;gBAChBnJ,yBAAyBA;gBACzBoJ,4BAA4BA;gBAC5B3P,OAAOA;gBAET;gBACE+F,SAAS2V;gBACT1b;gBACAge,kBAAkB;oBAAC/C;iBAAgB;YACrC;YAGF,IAAIkI,+BAA+BvgB,YAAY;gBAC7C,MAAMR,aAAa,MAAMqhB,IAAAA,oCAAc,EAAC7H,kBAAkB8H,QAAQ;gBAClE7c,SAASzE,UAAU,GAAGA;gBACtByE,SAAS8c,WAAW,GAAG,MAAMC,mBAC3BxhB,YACA2iB,sBACA9c,cACAlF;YAEJ;YAEA,MAAMqa,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtD7C;gBACAR;gBACAsD,sBAAsB7B;gBACtBjC;gBACAY,iBAAiBA;YACnB;YACA,OAAO;gBACLpE,iBAAiBmF;gBACjB9E,WAAWoF;gBACXlE,QAAQ,MAAM2G,IAAAA,wCAAkB,EAACf,YAAY;oBAC3CQ,mBAAmBf,IAAAA,kDAA+B,EAChDhB,kBAAkB8I,eAAe,IACjC1kB,OACAiY;oBAEFhT,oBAAoB;oBACpBkZ,yBACEhc,IAAIS,SAAS,CAACub,uBAAuB,KAAK;oBAC5CpZ,SAAS5C,IAAIS,SAAS,CAACmC,OAAO;oBAC9BqY;oBACAlD;gBACF;gBACA,0CAA0C;gBAC1CzO,qBAAqBsZ,qBAAqBjc,UAAU;gBACpD4C,iBAAiBqZ,qBAAqBhc,MAAM;gBAC5C4C,gBAAgBnB,gBAAgBua,qBAAqB/b,KAAK;gBAC1D4C,eAAemZ,qBAAqB7b,IAAI;YAC1C;QACF;IACF,EAAE,OAAOrD,KAAK;QACZ,IACEwY,IAAAA,gDAAuB,EAACxY,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIuJ,OAAO,KAAK,YACvBvJ,IAAIuJ,OAAO,CAAC9B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMzH;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAImf,IAAAA,wCAAoB,EAACnf,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMyY,qBAAqBC,IAAAA,iCAAmB,EAAC1Y;QAC/C,IAAIyY,oBAAoB;YACtB,MAAM/O,QAAQiP,IAAAA,8CAA2B,EAAC3Y;YAC1C4Y,IAAAA,UAAK,EACH,GAAG5Y,IAAI6Y,MAAM,CAAC,mDAAmD,EAAE5d,SAAS,kFAAkF,EAAEyO,OAAO;YAGzK,MAAM1J;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAIud,+BAA+B,MAAM;YACvC,MAAMvd;QACR;QAEA,IAAIkH;QAEJ,IAAI4R,IAAAA,6CAAyB,EAAC9Y,MAAM;YAClC5B,IAAItC,UAAU,GAAGid,IAAAA,+CAA2B,EAAC/Y;YAC7CgB,SAASlF,UAAU,GAAGsC,IAAItC,UAAU;YACpCoL,YAAY8R,IAAAA,sDAAkC,EAAC5a,IAAItC,UAAU;QAC/D,OAAO,IAAImd,IAAAA,8BAAe,EAACjZ,MAAM;YAC/BkH,YAAY;YACZ9I,IAAItC,UAAU,GAAGod,IAAAA,wCAA8B,EAAClZ;YAChDgB,SAASlF,UAAU,GAAGsC,IAAItC,UAAU;YAEpC,MAAMqd,cAAcC,IAAAA,4BAAa,EAACC,IAAAA,iCAAuB,EAACrZ,MAAM2T;YAEhE7R,UAAU,YAAYqX;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9Bra,IAAItC,UAAU,GAAG;YACjBkF,SAASlF,UAAU,GAAGsC,IAAItC,UAAU;QACtC;QAEA,MAAM,CAAC4d,qBAAqBC,qBAAqB,GAAGtE,IAAAA,mCAAkB,EACpEzB,eACAxL,aACAyL,aACAI,8BACAgB,IAAAA,wCAAmB,EAAC3Y,KAAK,QACzBnC,OACA;QAGF,MAAM+kB,uBAAwC5Y,iBAAiB;YAC7D3K,MAAM;YACNiH,OAAO;YACP1B;YACAiB,cAAcA;YACdc,YACE,QAAOqD,kCAAAA,eAAgBrD,UAAU,MAAK,cAClCqD,eAAerD,UAAU,GACzBG,0BAAc;YACpBF,QACE,QAAOoD,kCAAAA,eAAgBpD,MAAM,MAAK,cAC9BoD,eAAepD,MAAM,GACrBE,0BAAc;YACpBD,OACE,QAAOmD,kCAAAA,eAAgBnD,KAAK,MAAK,cAC7BmD,eAAenD,KAAK,GACpBC,0BAAc;YACpBC,MAAM;mBAAKiD,CAAAA,kCAAAA,eAAgBjD,IAAI,KAAIlB,aAAakB,IAAI;aAAE;QACxD;QACA,MAAMuW,kBAAkB,MAAMtZ,kDAAoB,CAACC,GAAG,CACpD2e,sBACArW,oBACApM,MACAH,KACAgZ,0BAA0BuE,GAAG,CAAC,AAAC7Z,IAAY4D,MAAM,IAAI9K,YAAYkH,KACjEkH;QAGF,MAAM4S,oBAAoBxZ,kDAAoB,CAACC,GAAG,CAChD2e,sBACA9c,aAAa3B,sBAAsB,EACnCmZ,iBACAlZ,wBAAwBC,aAAa,EACrC;YACEnI;YACA0H,SAASuV;QACX;QAGF,IAAI;YACF,6EAA6E;YAC7E,wFAAwF;YACxF,uCAAuC;YACvC,MAAMsE,aAAa,MAAMzZ,kDAAoB,CAACC,GAAG,CAC/C2e,sBACAlF,+CAAyB,EACzB;gBACEC,gBACErhB,QAAQ;gBACVshB,uBACE,qBAAChP;oBACCtB,mBAAmBkQ;oBACnBhQ,4BAA4BA;oBAC5BD,gBAAgB6P;oBAChBhZ,yBAAyBA;oBACzBvG,OAAOA;;gBAGXggB,eAAe;oBACbhgB;oBACA,wCAAwC;oBACxCge,kBAAkB;wBAACwB;qBAAqB;oBACxCvH;gBACF;YACF;YAGF,IAAIkL,+BAA+BvgB,YAAY;gBAC7C,MAAMR,aAAa,MAAMqhB,IAAAA,oCAAc,EACrCL,2BAA2BM,QAAQ;gBAErC7c,SAASzE,UAAU,GAAGA;gBACtByE,SAAS8c,WAAW,GAAG,MAAMC,mBAC3BxhB,YACA2iB,sBACA9c,cACAlF;YAEJ;YAEA,oEAAoE;YACpE,gEAAgE;YAChE,MAAMkiB,eAAe7B,2BAA2BsB,eAAe;YAE/D,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9B1O,iBAAiBmF;gBACjB9E,WAAWoF;gBACXlE,QAAQ,MAAM2G,IAAAA,wCAAkB,EAAC0B,YAAY;oBAC3CjC,mBAAmBf,IAAAA,kDAA+B,EAChDqI,cACAjlB,OACAiY;oBAEFhT,oBAAoB;oBACpBkZ,yBACEhc,IAAIS,SAAS,CAACub,uBAAuB,KAAK;oBAC5CpZ,SAAS5C,IAAIS,SAAS,CAACmC,OAAO;oBAC9BqY,uBAAuBC,IAAAA,oDAAyB,EAAC;wBAC/C7C;wBACAR;wBACAsD,sBAAsB,EAAE;wBACxB9D;wBACAY,iBAAiBA;oBACnB;oBACAF;oBACAkE,oBAAoBnY;gBACtB;gBACAuF,eAAe;gBACfC,qBACEU,mBAAmB,OAAOA,eAAerD,UAAU,GAAGG,0BAAc;gBACtEyC,iBACES,mBAAmB,OAAOA,eAAepD,MAAM,GAAGE,0BAAc;gBAClE0C,gBAAgBnB,gBACd2B,mBAAmB,OAAOA,eAAenD,KAAK,GAAGC,0BAAc;gBAEjE2C,eAAeO,mBAAmB,OAAOA,eAAejD,IAAI,GAAG;YACjE;QACF,EAAE,OAAO+W,UAAe;YACtB,IACE3hB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBmgB,IAAAA,6CAAyB,EAACsB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BzhB,QAAQ;gBACVyhB;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAMrS,uBAAuB,OAC3BtL,MACAH;IAKA,MAAM,EACJ+iB,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAGC,IAAAA,gCAAe,EAAC9iB;IAEpB,MAAM+iB,uBACJljB,IAAIE,YAAY,CAACoL,WAAW;IAC9B,IAAIE;IACJ,IAAIwX,mBAAmB;QACrB,MAAM,GAAGzX,OAAO,GAAG,MAAM4X,IAAAA,gEAA+B,EAAC;YACvDnjB;YACAojB,UAAUJ,iBAAiB,CAAC,EAAE;YAC9BK,cAAcL,iBAAiB,CAAC,EAAE;YAClCjhB,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAwJ,oBAAoBD;IACtB;IACA,IAAIvL,IAAIY,UAAU,CAACkD,GAAG,EAAE;QACtB,MAAMwf,MACJ,AAACnnB,CAAAA,QAAQC,GAAG,CAACuU,YAAY,KAAK,SAC1BxU,QAAQC,GAAG,CAACmnB,uBAAuB,GACnCvjB,IAAIY,UAAU,CAAC0iB,GAAG,AAAD,KAAM;QAE7B,MAAME,wBAAwBC,IAAAA,gDAA2B,EACvDH,KACAN,qCAAAA,iBAAmB,CAAC,EAAE;QAExB,IAAIhjB,IAAIY,UAAU,CAAC8iB,sBAAsB,IAAIF,uBAAuB;YAClE,MAAMG,kBAAkB3jB,IAAIE,YAAY,CAACyjB,eAAe;YACxDnY,oBACE,2EAA2E;YAC3E,iEAAiE;0BACjE,qBAACmY;gBAECtkB,MAAK;gBACLV,UAAU6kB;0BAEThY;eAJG;QAOV;IACF;IAEA,OAAO;QACLF,aAAa4X;QACb3X,QAAQC;IACV;AACF;AAEA,SAASlD,sBAAsBH,YAAgC;IAC7D,OAAO,CAACtB;YAECsB;eADPtB,UAAUC,0BAAc,IACxB,SAAOqB,2BAAAA,aAAayb,UAAU,qBAAvBzb,yBAAyB0b,MAAM,MAAK,WACvC1b,aAAayb,UAAU,CAACC,MAAM,GAC9Bhd;;AACR;AAEA,eAAe4a,mBACbqC,kBAA0B,EAC1B9Z,cAA8B,EAC9BlE,YAA2B,EAC3BlF,UAAsB;IAEtB,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAMwD,0BAA0BxD,WAAWwD,uBAAuB;IAClE,IACE,CAACA,2BACD,yEAAyE;IACzE,mBAAmB;IACnB,EAAE;IACF,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,mCAAmC;IACnCxD,WAAWuH,YAAY,CAAC4b,kBAAkB,KAAK,MAC/C;QACA;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,gBAAgB7nB,QAAQC,GAAG,CAACuU,YAAY,KAAK;IACnD,MAAMsT,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWH,gBACP5f,wBAAwBggB,oBAAoB,GAC5ChgB,wBAAwBigB,gBAAgB;QAC5CtS,iBAAiBuS,IAAAA,mCAAkB;IACrC;IAEA,MAAMC,YAAYva,eAAenD,KAAK;IACtC,OAAO,MAAMf,aAAa2b,kBAAkB,CAC1C7gB,WAAWuH,YAAY,CAACqc,kBAAkB,EAC1CV,oBACAS,WACAngB,wBAAwBC,aAAa,EACrC4f;AAEJ", "ignoreList": [0]}