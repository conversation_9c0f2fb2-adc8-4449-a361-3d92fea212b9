{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "sourcesContent": ["import type { RouteMetadata } from '../../../export/routes/types'\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheHandlerContext, CacheHandlerValue } from '.'\nimport type { CacheFs } from '../../../shared/lib/utils'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchValue,\n  type IncrementalCacheValue,\n  type SetIncrementalFetchCacheContext,\n  type SetIncrementalResponseCacheContext,\n} from '../../response-cache'\n\nimport type { LRUCache } from '../lru-cache'\nimport path from '../../../shared/lib/isomorphic/path'\nimport {\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_DATA_SUFFIX,\n  NEXT_META_SUFFIX,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n} from '../../../lib/constants'\nimport { isStale, tagsManifest } from './tags-manifest.external'\nimport { MultiFileWriter } from '../../../lib/multi-file-writer'\nimport { getMemoryCache } from './memory-cache.external'\n\ntype FileSystemCacheContext = Omit<\n  CacheHandlerContext,\n  'fs' | 'serverDistDir'\n> & {\n  fs: CacheFs\n  serverDistDir: string\n}\n\nexport default class FileSystemCache implements CacheHandler {\n  private fs: FileSystemCacheContext['fs']\n  private flushToDisk?: FileSystemCacheContext['flushToDisk']\n  private serverDistDir: FileSystemCacheContext['serverDistDir']\n  private revalidatedTags: string[]\n  private static debug: boolean = !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n  private static memoryCache: LRUCache<CacheHandlerValue> | undefined\n\n  constructor(ctx: FileSystemCacheContext) {\n    this.fs = ctx.fs\n    this.flushToDisk = ctx.flushToDisk\n    this.serverDistDir = ctx.serverDistDir\n    this.revalidatedTags = ctx.revalidatedTags\n\n    if (ctx.maxMemoryCacheSize) {\n      if (!FileSystemCache.memoryCache) {\n        if (FileSystemCache.debug) {\n          console.log('using memory store for fetch cache')\n        }\n\n        FileSystemCache.memoryCache = getMemoryCache(ctx.maxMemoryCacheSize)\n      } else if (FileSystemCache.debug) {\n        console.log('memory store already initialized')\n      }\n    } else if (FileSystemCache.debug) {\n      console.log('not using memory store for fetch cache')\n    }\n  }\n\n  public resetRequestCache(): void {}\n\n  public async revalidateTag(\n    ...args: Parameters<CacheHandler['revalidateTag']>\n  ) {\n    let [tags] = args\n    tags = typeof tags === 'string' ? [tags] : tags\n\n    if (FileSystemCache.debug) {\n      console.log('revalidateTag', tags)\n    }\n\n    if (tags.length === 0) {\n      return\n    }\n\n    for (const tag of tags) {\n      if (!tagsManifest.has(tag)) {\n        tagsManifest.set(tag, Date.now())\n      }\n    }\n  }\n\n  public async get(...args: Parameters<CacheHandler['get']>) {\n    const [key, ctx] = args\n    const { kind } = ctx\n\n    let data = FileSystemCache.memoryCache?.get(key)\n\n    if (FileSystemCache.debug) {\n      if (kind === IncrementalCacheKind.FETCH) {\n        console.log('get', key, ctx.tags, kind, !!data)\n      } else {\n        console.log('get', key, kind, !!data)\n      }\n    }\n\n    // let's check the disk for seed data\n    if (!data && process.env.NEXT_RUNTIME !== 'edge') {\n      try {\n        if (kind === IncrementalCacheKind.APP_ROUTE) {\n          const filePath = this.getFilePath(\n            `${key}.body`,\n            IncrementalCacheKind.APP_ROUTE\n          )\n          const fileData = await this.fs.readFile(filePath)\n          const { mtime } = await this.fs.stat(filePath)\n\n          const meta = JSON.parse(\n            await this.fs.readFile(\n              filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n              'utf8'\n            )\n          )\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_ROUTE,\n              body: fileData,\n              headers: meta.headers,\n              status: meta.status,\n            },\n          }\n        } else {\n          const filePath = this.getFilePath(\n            kind === IncrementalCacheKind.FETCH ? key : `${key}.html`,\n            kind\n          )\n\n          const fileData = await this.fs.readFile(filePath, 'utf8')\n          const { mtime } = await this.fs.stat(filePath)\n\n          if (kind === IncrementalCacheKind.FETCH) {\n            const { tags, fetchIdx, fetchUrl } = ctx\n\n            if (!this.flushToDisk) return null\n\n            const lastModified = mtime.getTime()\n            const parsedData: CachedFetchValue = JSON.parse(fileData)\n            data = {\n              lastModified,\n              value: parsedData,\n            }\n\n            if (data.value?.kind === CachedRouteKind.FETCH) {\n              const storedTags = data.value?.tags\n\n              // update stored tags if a new one is being added\n              // TODO: remove this when we can send the tags\n              // via header on GET same as SET\n              if (!tags?.every((tag) => storedTags?.includes(tag))) {\n                if (FileSystemCache.debug) {\n                  console.log('tags vs storedTags mismatch', tags, storedTags)\n                }\n                await this.set(key, data.value, {\n                  fetchCache: true,\n                  tags,\n                  fetchIdx,\n                  fetchUrl,\n                })\n              }\n            }\n          } else if (kind === IncrementalCacheKind.APP_PAGE) {\n            // We try to load the metadata file, but if it fails, we don't\n            // error. We also don't load it if this is a fallback.\n            let meta: RouteMetadata | undefined\n            try {\n              meta = JSON.parse(\n                await this.fs.readFile(\n                  filePath.replace(/\\.html$/, NEXT_META_SUFFIX),\n                  'utf8'\n                )\n              )\n            } catch {}\n\n            let maybeSegmentData: Map<string, Buffer> | undefined\n            if (meta?.segmentPaths) {\n              // Collect all the segment data for this page.\n              // TODO: To optimize file system reads, we should consider creating\n              // separate cache entries for each segment, rather than storing them\n              // all on the page's entry. Though the behavior is\n              // identical regardless.\n              const segmentData: Map<string, Buffer> = new Map()\n              maybeSegmentData = segmentData\n              const segmentsDir = key + RSC_SEGMENTS_DIR_SUFFIX\n              await Promise.all(\n                meta.segmentPaths.map(async (segmentPath: string) => {\n                  const segmentDataFilePath = this.getFilePath(\n                    segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX,\n                    IncrementalCacheKind.APP_PAGE\n                  )\n                  try {\n                    segmentData.set(\n                      segmentPath,\n                      await this.fs.readFile(segmentDataFilePath)\n                    )\n                  } catch {\n                    // This shouldn't happen, but if for some reason we fail to\n                    // load a segment from the filesystem, treat it the same as if\n                    // the segment is dynamic and does not have a prefetch.\n                  }\n                })\n              )\n            }\n\n            let rscData: Buffer | undefined\n            if (!ctx.isFallback) {\n              rscData = await this.fs.readFile(\n                this.getFilePath(\n                  `${key}${ctx.isRoutePPREnabled ? RSC_PREFETCH_SUFFIX : RSC_SUFFIX}`,\n                  IncrementalCacheKind.APP_PAGE\n                )\n              )\n            }\n\n            data = {\n              lastModified: mtime.getTime(),\n              value: {\n                kind: CachedRouteKind.APP_PAGE,\n                html: fileData,\n                rscData,\n                postponed: meta?.postponed,\n                headers: meta?.headers,\n                status: meta?.status,\n                segmentData: maybeSegmentData,\n              },\n            }\n          } else if (kind === IncrementalCacheKind.PAGES) {\n            let meta: RouteMetadata | undefined\n            let pageData: string | object = {}\n\n            if (!ctx.isFallback) {\n              pageData = JSON.parse(\n                await this.fs.readFile(\n                  this.getFilePath(\n                    `${key}${NEXT_DATA_SUFFIX}`,\n                    IncrementalCacheKind.PAGES\n                  ),\n                  'utf8'\n                )\n              )\n            }\n\n            data = {\n              lastModified: mtime.getTime(),\n              value: {\n                kind: CachedRouteKind.PAGES,\n                html: fileData,\n                pageData,\n                headers: meta?.headers,\n                status: meta?.status,\n              },\n            }\n          } else {\n            throw new Error(\n              `Invariant: Unexpected route kind ${kind} in file system cache.`\n            )\n          }\n        }\n\n        if (data) {\n          FileSystemCache.memoryCache?.set(key, data)\n        }\n      } catch {\n        return null\n      }\n    }\n\n    if (\n      data?.value?.kind === CachedRouteKind.APP_PAGE ||\n      data?.value?.kind === CachedRouteKind.APP_ROUTE ||\n      data?.value?.kind === CachedRouteKind.PAGES\n    ) {\n      let cacheTags: undefined | string[]\n      const tagsHeader = data.value.headers?.[NEXT_CACHE_TAGS_HEADER]\n\n      if (typeof tagsHeader === 'string') {\n        cacheTags = tagsHeader.split(',')\n      }\n\n      if (cacheTags?.length) {\n        // we trigger a blocking validation if an ISR page\n        // had a tag revalidated, if we want to be a background\n        // revalidation instead we return data.lastModified = -1\n        if (isStale(cacheTags, data?.lastModified || Date.now())) {\n          return null\n        }\n      }\n    } else if (data?.value?.kind === CachedRouteKind.FETCH) {\n      const combinedTags =\n        ctx.kind === IncrementalCacheKind.FETCH\n          ? [...(ctx.tags || []), ...(ctx.softTags || [])]\n          : []\n\n      const wasRevalidated = combinedTags.some((tag) => {\n        if (this.revalidatedTags.includes(tag)) {\n          return true\n        }\n\n        return isStale([tag], data?.lastModified || Date.now())\n      })\n      // When revalidate tag is called we don't return\n      // stale data so it's updated right away\n      if (wasRevalidated) {\n        data = undefined\n      }\n    }\n\n    return data ?? null\n  }\n\n  public async set(\n    key: string,\n    data: IncrementalCacheValue | null,\n    ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ) {\n    FileSystemCache.memoryCache?.set(key, {\n      value: data,\n      lastModified: Date.now(),\n    })\n\n    if (FileSystemCache.debug) {\n      console.log('set', key)\n    }\n\n    if (!this.flushToDisk || !data) return\n\n    // Create a new writer that will prepare to write all the files to disk\n    // after their containing directory is created.\n    const writer = new MultiFileWriter(this.fs)\n\n    if (data.kind === CachedRouteKind.APP_ROUTE) {\n      const filePath = this.getFilePath(\n        `${key}.body`,\n        IncrementalCacheKind.APP_ROUTE\n      )\n\n      writer.append(filePath, data.body)\n\n      const meta: RouteMetadata = {\n        headers: data.headers,\n        status: data.status,\n        postponed: undefined,\n        segmentPaths: undefined,\n      }\n\n      writer.append(\n        filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n        JSON.stringify(meta, null, 2)\n      )\n    } else if (\n      data.kind === CachedRouteKind.PAGES ||\n      data.kind === CachedRouteKind.APP_PAGE\n    ) {\n      const isAppPath = data.kind === CachedRouteKind.APP_PAGE\n      const htmlPath = this.getFilePath(\n        `${key}.html`,\n        isAppPath ? IncrementalCacheKind.APP_PAGE : IncrementalCacheKind.PAGES\n      )\n\n      writer.append(htmlPath, data.html)\n\n      // Fallbacks don't generate a data file.\n      if (!ctx.fetchCache && !ctx.isFallback) {\n        writer.append(\n          this.getFilePath(\n            `${key}${\n              isAppPath\n                ? ctx.isRoutePPREnabled\n                  ? RSC_PREFETCH_SUFFIX\n                  : RSC_SUFFIX\n                : NEXT_DATA_SUFFIX\n            }`,\n            isAppPath\n              ? IncrementalCacheKind.APP_PAGE\n              : IncrementalCacheKind.PAGES\n          ),\n          isAppPath ? data.rscData! : JSON.stringify(data.pageData)\n        )\n      }\n\n      if (data?.kind === CachedRouteKind.APP_PAGE) {\n        let segmentPaths: string[] | undefined\n        if (data.segmentData) {\n          segmentPaths = []\n          const segmentsDir = htmlPath.replace(\n            /\\.html$/,\n            RSC_SEGMENTS_DIR_SUFFIX\n          )\n\n          for (const [segmentPath, buffer] of data.segmentData) {\n            segmentPaths.push(segmentPath)\n            const segmentDataFilePath =\n              segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX\n            writer.append(segmentDataFilePath, buffer)\n          }\n        }\n\n        const meta: RouteMetadata = {\n          headers: data.headers,\n          status: data.status,\n          postponed: data.postponed,\n          segmentPaths,\n        }\n\n        writer.append(\n          htmlPath.replace(/\\.html$/, NEXT_META_SUFFIX),\n          JSON.stringify(meta)\n        )\n      }\n    } else if (data.kind === CachedRouteKind.FETCH) {\n      const filePath = this.getFilePath(key, IncrementalCacheKind.FETCH)\n      writer.append(\n        filePath,\n        JSON.stringify({\n          ...data,\n          tags: ctx.fetchCache ? ctx.tags : [],\n        })\n      )\n    }\n\n    // Wait for all FS operations to complete.\n    await writer.wait()\n  }\n\n  private getFilePath(pathname: string, kind: IncrementalCacheKind): string {\n    switch (kind) {\n      case IncrementalCacheKind.FETCH:\n        // we store in .next/cache/fetch-cache so it can be persisted\n        // across deploys\n        return path.join(\n          this.serverDistDir,\n          '..',\n          'cache',\n          'fetch-cache',\n          pathname\n        )\n      case IncrementalCacheKind.PAGES:\n        return path.join(this.serverDistDir, 'pages', pathname)\n      case IncrementalCacheKind.IMAGE:\n      case IncrementalCacheKind.APP_PAGE:\n      case IncrementalCacheKind.APP_ROUTE:\n        return path.join(this.serverDistDir, 'app', pathname)\n      default:\n        throw new Error(`Unexpected file path kind: ${kind}`)\n    }\n  }\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind", "path", "NEXT_CACHE_TAGS_HEADER", "NEXT_DATA_SUFFIX", "NEXT_META_SUFFIX", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SUFFIX", "isStale", "tagsManifest", "MultiFileWriter", "getMemoryCache", "FileSystemCache", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "revalidatedTags", "maxMemoryCacheSize", "memoryCache", "console", "log", "resetRequestCache", "revalidateTag", "args", "tags", "length", "tag", "has", "set", "Date", "now", "get", "data", "key", "kind", "FETCH", "NEXT_RUNTIME", "APP_ROUTE", "filePath", "getFilePath", "fileData", "readFile", "mtime", "stat", "meta", "JSON", "parse", "replace", "lastModified", "getTime", "value", "body", "headers", "status", "fetchIdx", "fetchUrl", "parsedData", "storedTags", "every", "includes", "fetchCache", "APP_PAGE", "maybeSegmentData", "segmentPaths", "segmentData", "Map", "segmentsDir", "Promise", "all", "map", "segmentPath", "segmentDataFilePath", "rscData", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "html", "postponed", "PAGES", "pageData", "Error", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "split", "combinedTags", "softTags", "wasRevalidated", "some", "undefined", "writer", "append", "stringify", "isAppPath", "htmlPath", "buffer", "push", "wait", "pathname", "join", "IMAGE"], "mappings": "AAGA,SACEA,eAAe,EACfC,oBAAoB,QAKf,uBAAsB;AAG7B,OAAOC,UAAU,sCAAqC;AACtD,SACEC,sBAAsB,EACtBC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,EACnBC,kBAAkB,EAClBC,uBAAuB,EACvBC,UAAU,QACL,yBAAwB;AAC/B,SAASC,OAAO,EAAEC,YAAY,QAAQ,2BAA0B;AAChE,SAASC,eAAe,QAAQ,iCAAgC;AAChE,SAASC,cAAc,QAAQ,0BAAyB;AAUxD,eAAe,MAAMC;qBAKJC,QAAiB,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;IAGtEC,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,eAAe,GAAGJ,IAAII,eAAe;QAE1C,IAAIJ,IAAIK,kBAAkB,EAAE;YAC1B,IAAI,CAACX,gBAAgBY,WAAW,EAAE;gBAChC,IAAIZ,gBAAgBC,KAAK,EAAE;oBACzBY,QAAQC,GAAG,CAAC;gBACd;gBAEAd,gBAAgBY,WAAW,GAAGb,eAAeO,IAAIK,kBAAkB;YACrE,OAAO,IAAIX,gBAAgBC,KAAK,EAAE;gBAChCY,QAAQC,GAAG,CAAC;YACd;QACF,OAAO,IAAId,gBAAgBC,KAAK,EAAE;YAChCY,QAAQC,GAAG,CAAC;QACd;IACF;IAEOC,oBAA0B,CAAC;IAElC,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAE3C,IAAIlB,gBAAgBC,KAAK,EAAE;YACzBY,QAAQC,GAAG,CAAC,iBAAiBI;QAC/B;QAEA,IAAIA,KAAKC,MAAM,KAAK,GAAG;YACrB;QACF;QAEA,KAAK,MAAMC,OAAOF,KAAM;YACtB,IAAI,CAACrB,aAAawB,GAAG,CAACD,MAAM;gBAC1BvB,aAAayB,GAAG,CAACF,KAAKG,KAAKC,GAAG;YAChC;QACF;IACF;IAEA,MAAaC,IAAI,GAAGR,IAAqC,EAAE;YAI9CjB,8BAuLT0B,aACAA,cACAA,cAiBSA;QA7MX,MAAM,CAACC,KAAKrB,IAAI,GAAGW;QACnB,MAAM,EAAEW,IAAI,EAAE,GAAGtB;QAEjB,IAAIoB,QAAO1B,+BAAAA,gBAAgBY,WAAW,qBAA3BZ,6BAA6ByB,GAAG,CAACE;QAE5C,IAAI3B,gBAAgBC,KAAK,EAAE;YACzB,IAAI2B,SAASzC,qBAAqB0C,KAAK,EAAE;gBACvChB,QAAQC,GAAG,CAAC,OAAOa,KAAKrB,IAAIY,IAAI,EAAEU,MAAM,CAAC,CAACF;YAC5C,OAAO;gBACLb,QAAQC,GAAG,CAAC,OAAOa,KAAKC,MAAM,CAAC,CAACF;YAClC;QACF;QAEA,qCAAqC;QACrC,IAAI,CAACA,QAAQxB,QAAQC,GAAG,CAAC2B,YAAY,KAAK,QAAQ;YAChD,IAAI;gBACF,IAAIF,SAASzC,qBAAqB4C,SAAS,EAAE;oBAC3C,MAAMC,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGN,IAAI,KAAK,CAAC,EACbxC,qBAAqB4C,SAAS;oBAEhC,MAAMG,WAAW,MAAM,IAAI,CAAC3B,EAAE,CAAC4B,QAAQ,CAACH;oBACxC,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC7B,EAAE,CAAC8B,IAAI,CAACL;oBAErC,MAAMM,OAAOC,KAAKC,KAAK,CACrB,MAAM,IAAI,CAACjC,EAAE,CAAC4B,QAAQ,CACpBH,SAASS,OAAO,CAAC,WAAWlD,mBAC5B;oBAIJmC,OAAO;wBACLgB,cAAcN,MAAMO,OAAO;wBAC3BC,OAAO;4BACLhB,MAAM1C,gBAAgB6C,SAAS;4BAC/Bc,MAAMX;4BACNY,SAASR,KAAKQ,OAAO;4BACrBC,QAAQT,KAAKS,MAAM;wBACrB;oBACF;gBACF,OAAO;oBACL,MAAMf,WAAW,IAAI,CAACC,WAAW,CAC/BL,SAASzC,qBAAqB0C,KAAK,GAAGF,MAAM,GAAGA,IAAI,KAAK,CAAC,EACzDC;oBAGF,MAAMM,WAAW,MAAM,IAAI,CAAC3B,EAAE,CAAC4B,QAAQ,CAACH,UAAU;oBAClD,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC7B,EAAE,CAAC8B,IAAI,CAACL;oBAErC,IAAIJ,SAASzC,qBAAqB0C,KAAK,EAAE;4BAYnCH;wBAXJ,MAAM,EAAER,IAAI,EAAE8B,QAAQ,EAAEC,QAAQ,EAAE,GAAG3C;wBAErC,IAAI,CAAC,IAAI,CAACE,WAAW,EAAE,OAAO;wBAE9B,MAAMkC,eAAeN,MAAMO,OAAO;wBAClC,MAAMO,aAA+BX,KAAKC,KAAK,CAACN;wBAChDR,OAAO;4BACLgB;4BACAE,OAAOM;wBACT;wBAEA,IAAIxB,EAAAA,eAAAA,KAAKkB,KAAK,qBAAVlB,aAAYE,IAAI,MAAK1C,gBAAgB2C,KAAK,EAAE;gCAC3BH;4BAAnB,MAAMyB,cAAazB,eAAAA,KAAKkB,KAAK,qBAAVlB,aAAYR,IAAI;4BAEnC,iDAAiD;4BACjD,8CAA8C;4BAC9C,gCAAgC;4BAChC,IAAI,EAACA,wBAAAA,KAAMkC,KAAK,CAAC,CAAChC,MAAQ+B,8BAAAA,WAAYE,QAAQ,CAACjC,QAAO;gCACpD,IAAIpB,gBAAgBC,KAAK,EAAE;oCACzBY,QAAQC,GAAG,CAAC,+BAA+BI,MAAMiC;gCACnD;gCACA,MAAM,IAAI,CAAC7B,GAAG,CAACK,KAAKD,KAAKkB,KAAK,EAAE;oCAC9BU,YAAY;oCACZpC;oCACA8B;oCACAC;gCACF;4BACF;wBACF;oBACF,OAAO,IAAIrB,SAASzC,qBAAqBoE,QAAQ,EAAE;wBACjD,8DAA8D;wBAC9D,sDAAsD;wBACtD,IAAIjB;wBACJ,IAAI;4BACFA,OAAOC,KAAKC,KAAK,CACf,MAAM,IAAI,CAACjC,EAAE,CAAC4B,QAAQ,CACpBH,SAASS,OAAO,CAAC,WAAWlD,mBAC5B;wBAGN,EAAE,OAAM,CAAC;wBAET,IAAIiE;wBACJ,IAAIlB,wBAAAA,KAAMmB,YAAY,EAAE;4BACtB,8CAA8C;4BAC9C,mEAAmE;4BACnE,oEAAoE;4BACpE,kDAAkD;4BAClD,wBAAwB;4BACxB,MAAMC,cAAmC,IAAIC;4BAC7CH,mBAAmBE;4BACnB,MAAME,cAAcjC,MAAMjC;4BAC1B,MAAMmE,QAAQC,GAAG,CACfxB,KAAKmB,YAAY,CAACM,GAAG,CAAC,OAAOC;gCAC3B,MAAMC,sBAAsB,IAAI,CAAChC,WAAW,CAC1C2B,cAAcI,cAAcvE,oBAC5BN,qBAAqBoE,QAAQ;gCAE/B,IAAI;oCACFG,YAAYpC,GAAG,CACb0C,aACA,MAAM,IAAI,CAACzD,EAAE,CAAC4B,QAAQ,CAAC8B;gCAE3B,EAAE,OAAM;gCACN,2DAA2D;gCAC3D,8DAA8D;gCAC9D,uDAAuD;gCACzD;4BACF;wBAEJ;wBAEA,IAAIC;wBACJ,IAAI,CAAC5D,IAAI6D,UAAU,EAAE;4BACnBD,UAAU,MAAM,IAAI,CAAC3D,EAAE,CAAC4B,QAAQ,CAC9B,IAAI,CAACF,WAAW,CACd,GAAGN,MAAMrB,IAAI8D,iBAAiB,GAAG5E,sBAAsBG,YAAY,EACnER,qBAAqBoE,QAAQ;wBAGnC;wBAEA7B,OAAO;4BACLgB,cAAcN,MAAMO,OAAO;4BAC3BC,OAAO;gCACLhB,MAAM1C,gBAAgBqE,QAAQ;gCAC9Bc,MAAMnC;gCACNgC;gCACAI,SAAS,EAAEhC,wBAAAA,KAAMgC,SAAS;gCAC1BxB,OAAO,EAAER,wBAAAA,KAAMQ,OAAO;gCACtBC,MAAM,EAAET,wBAAAA,KAAMS,MAAM;gCACpBW,aAAaF;4BACf;wBACF;oBACF,OAAO,IAAI5B,SAASzC,qBAAqBoF,KAAK,EAAE;wBAC9C,IAAIjC;wBACJ,IAAIkC,WAA4B,CAAC;wBAEjC,IAAI,CAAClE,IAAI6D,UAAU,EAAE;4BACnBK,WAAWjC,KAAKC,KAAK,CACnB,MAAM,IAAI,CAACjC,EAAE,CAAC4B,QAAQ,CACpB,IAAI,CAACF,WAAW,CACd,GAAGN,MAAMrC,kBAAkB,EAC3BH,qBAAqBoF,KAAK,GAE5B;wBAGN;wBAEA7C,OAAO;4BACLgB,cAAcN,MAAMO,OAAO;4BAC3BC,OAAO;gCACLhB,MAAM1C,gBAAgBqF,KAAK;gCAC3BF,MAAMnC;gCACNsC;gCACA1B,OAAO,EAAER,wBAAAA,KAAMQ,OAAO;gCACtBC,MAAM,EAAET,wBAAAA,KAAMS,MAAM;4BACtB;wBACF;oBACF,OAAO;wBACL,MAAM,qBAEL,CAFK,IAAI0B,MACR,CAAC,iCAAiC,EAAE7C,KAAK,sBAAsB,CAAC,GAD5D,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEA,IAAIF,MAAM;wBACR1B;qBAAAA,gCAAAA,gBAAgBY,WAAW,qBAA3BZ,8BAA6BsB,GAAG,CAACK,KAAKD;gBACxC;YACF,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,IACEA,CAAAA,yBAAAA,cAAAA,KAAMkB,KAAK,qBAAXlB,YAAaE,IAAI,MAAK1C,gBAAgBqE,QAAQ,IAC9C7B,CAAAA,yBAAAA,eAAAA,KAAMkB,KAAK,qBAAXlB,aAAaE,IAAI,MAAK1C,gBAAgB6C,SAAS,IAC/CL,CAAAA,yBAAAA,eAAAA,KAAMkB,KAAK,qBAAXlB,aAAaE,IAAI,MAAK1C,gBAAgBqF,KAAK,EAC3C;gBAEmB7C;YADnB,IAAIgD;YACJ,MAAMC,cAAajD,sBAAAA,KAAKkB,KAAK,CAACE,OAAO,qBAAlBpB,mBAAoB,CAACrC,uBAAuB;YAE/D,IAAI,OAAOsF,eAAe,UAAU;gBAClCD,YAAYC,WAAWC,KAAK,CAAC;YAC/B;YAEA,IAAIF,6BAAAA,UAAWvD,MAAM,EAAE;gBACrB,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAIvB,QAAQ8E,WAAWhD,CAAAA,wBAAAA,KAAMgB,YAAY,KAAInB,KAAKC,GAAG,KAAK;oBACxD,OAAO;gBACT;YACF;QACF,OAAO,IAAIE,CAAAA,yBAAAA,eAAAA,KAAMkB,KAAK,qBAAXlB,aAAaE,IAAI,MAAK1C,gBAAgB2C,KAAK,EAAE;YACtD,MAAMgD,eACJvE,IAAIsB,IAAI,KAAKzC,qBAAqB0C,KAAK,GACnC;mBAAKvB,IAAIY,IAAI,IAAI,EAAE;mBAAOZ,IAAIwE,QAAQ,IAAI,EAAE;aAAE,GAC9C,EAAE;YAER,MAAMC,iBAAiBF,aAAaG,IAAI,CAAC,CAAC5D;gBACxC,IAAI,IAAI,CAACV,eAAe,CAAC2C,QAAQ,CAACjC,MAAM;oBACtC,OAAO;gBACT;gBAEA,OAAOxB,QAAQ;oBAACwB;iBAAI,EAAEM,CAAAA,wBAAAA,KAAMgB,YAAY,KAAInB,KAAKC,GAAG;YACtD;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAIuD,gBAAgB;gBAClBrD,OAAOuD;YACT;QACF;QAEA,OAAOvD,QAAQ;IACjB;IAEA,MAAaJ,IACXK,GAAW,EACXD,IAAkC,EAClCpB,GAAyE,EACzE;YACAN;SAAAA,+BAAAA,gBAAgBY,WAAW,qBAA3BZ,6BAA6BsB,GAAG,CAACK,KAAK;YACpCiB,OAAOlB;YACPgB,cAAcnB,KAAKC,GAAG;QACxB;QAEA,IAAIxB,gBAAgBC,KAAK,EAAE;YACzBY,QAAQC,GAAG,CAAC,OAAOa;QACrB;QAEA,IAAI,CAAC,IAAI,CAACnB,WAAW,IAAI,CAACkB,MAAM;QAEhC,uEAAuE;QACvE,+CAA+C;QAC/C,MAAMwD,SAAS,IAAIpF,gBAAgB,IAAI,CAACS,EAAE;QAE1C,IAAImB,KAAKE,IAAI,KAAK1C,gBAAgB6C,SAAS,EAAE;YAC3C,MAAMC,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGN,IAAI,KAAK,CAAC,EACbxC,qBAAqB4C,SAAS;YAGhCmD,OAAOC,MAAM,CAACnD,UAAUN,KAAKmB,IAAI;YAEjC,MAAMP,OAAsB;gBAC1BQ,SAASpB,KAAKoB,OAAO;gBACrBC,QAAQrB,KAAKqB,MAAM;gBACnBuB,WAAWW;gBACXxB,cAAcwB;YAChB;YAEAC,OAAOC,MAAM,CACXnD,SAASS,OAAO,CAAC,WAAWlD,mBAC5BgD,KAAK6C,SAAS,CAAC9C,MAAM,MAAM;QAE/B,OAAO,IACLZ,KAAKE,IAAI,KAAK1C,gBAAgBqF,KAAK,IACnC7C,KAAKE,IAAI,KAAK1C,gBAAgBqE,QAAQ,EACtC;YACA,MAAM8B,YAAY3D,KAAKE,IAAI,KAAK1C,gBAAgBqE,QAAQ;YACxD,MAAM+B,WAAW,IAAI,CAACrD,WAAW,CAC/B,GAAGN,IAAI,KAAK,CAAC,EACb0D,YAAYlG,qBAAqBoE,QAAQ,GAAGpE,qBAAqBoF,KAAK;YAGxEW,OAAOC,MAAM,CAACG,UAAU5D,KAAK2C,IAAI;YAEjC,wCAAwC;YACxC,IAAI,CAAC/D,IAAIgD,UAAU,IAAI,CAAChD,IAAI6D,UAAU,EAAE;gBACtCe,OAAOC,MAAM,CACX,IAAI,CAAClD,WAAW,CACd,GAAGN,MACD0D,YACI/E,IAAI8D,iBAAiB,GACnB5E,sBACAG,aACFL,kBACJ,EACF+F,YACIlG,qBAAqBoE,QAAQ,GAC7BpE,qBAAqBoF,KAAK,GAEhCc,YAAY3D,KAAKwC,OAAO,GAAI3B,KAAK6C,SAAS,CAAC1D,KAAK8C,QAAQ;YAE5D;YAEA,IAAI9C,CAAAA,wBAAAA,KAAME,IAAI,MAAK1C,gBAAgBqE,QAAQ,EAAE;gBAC3C,IAAIE;gBACJ,IAAI/B,KAAKgC,WAAW,EAAE;oBACpBD,eAAe,EAAE;oBACjB,MAAMG,cAAc0B,SAAS7C,OAAO,CAClC,WACA/C;oBAGF,KAAK,MAAM,CAACsE,aAAauB,OAAO,IAAI7D,KAAKgC,WAAW,CAAE;wBACpDD,aAAa+B,IAAI,CAACxB;wBAClB,MAAMC,sBACJL,cAAcI,cAAcvE;wBAC9ByF,OAAOC,MAAM,CAAClB,qBAAqBsB;oBACrC;gBACF;gBAEA,MAAMjD,OAAsB;oBAC1BQ,SAASpB,KAAKoB,OAAO;oBACrBC,QAAQrB,KAAKqB,MAAM;oBACnBuB,WAAW5C,KAAK4C,SAAS;oBACzBb;gBACF;gBAEAyB,OAAOC,MAAM,CACXG,SAAS7C,OAAO,CAAC,WAAWlD,mBAC5BgD,KAAK6C,SAAS,CAAC9C;YAEnB;QACF,OAAO,IAAIZ,KAAKE,IAAI,KAAK1C,gBAAgB2C,KAAK,EAAE;YAC9C,MAAMG,WAAW,IAAI,CAACC,WAAW,CAACN,KAAKxC,qBAAqB0C,KAAK;YACjEqD,OAAOC,MAAM,CACXnD,UACAO,KAAK6C,SAAS,CAAC;gBACb,GAAG1D,IAAI;gBACPR,MAAMZ,IAAIgD,UAAU,GAAGhD,IAAIY,IAAI,GAAG,EAAE;YACtC;QAEJ;QAEA,0CAA0C;QAC1C,MAAMgE,OAAOO,IAAI;IACnB;IAEQxD,YAAYyD,QAAgB,EAAE9D,IAA0B,EAAU;QACxE,OAAQA;YACN,KAAKzC,qBAAqB0C,KAAK;gBAC7B,6DAA6D;gBAC7D,iBAAiB;gBACjB,OAAOzC,KAAKuG,IAAI,CACd,IAAI,CAAClF,aAAa,EAClB,MACA,SACA,eACAiF;YAEJ,KAAKvG,qBAAqBoF,KAAK;gBAC7B,OAAOnF,KAAKuG,IAAI,CAAC,IAAI,CAAClF,aAAa,EAAE,SAASiF;YAChD,KAAKvG,qBAAqByG,KAAK;YAC/B,KAAKzG,qBAAqBoE,QAAQ;YAClC,KAAKpE,qBAAqB4C,SAAS;gBACjC,OAAO3C,KAAKuG,IAAI,CAAC,IAAI,CAAClF,aAAa,EAAE,OAAOiF;YAC9C;gBACE,MAAM,qBAA+C,CAA/C,IAAIjB,MAAM,CAAC,2BAA2B,EAAE7C,MAAM,GAA9C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;QACxD;IACF;AACF", "ignoreList": [0]}