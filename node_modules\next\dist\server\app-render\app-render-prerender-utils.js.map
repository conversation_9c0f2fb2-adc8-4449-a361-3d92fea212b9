{"version": 3, "sources": ["../../../src/server/app-render/app-render-prerender-utils.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\n\n/**\n * This is a utility function to make scheduling sequential tasks that run back to back easier.\n * We schedule on the same queue (setImmediate) at the same time to ensure no other events can sneak in between.\n */\nexport function prerenderAndAbortInSequentialTasks<R>(\n  prerender: () => Promise<R>,\n  abort: () => void\n): Promise<R> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`prerenderAndAbortInSequentialTasks` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let pendingResult: Promise<R>\n      setImmediate(() => {\n        try {\n          pendingResult = prerender()\n          pendingResult.catch(() => {})\n        } catch (err) {\n          reject(err)\n        }\n      })\n      setImmediate(() => {\n        abort()\n        resolve(pendingResult)\n      })\n    })\n  }\n}\n\n/**\n * Like `prerenderAndAbortInSequentialTasks`, but with another task between `prerender` and `abort`,\n * which allows us to move a part of the render into a separate task.\n */\nexport function prerenderAndAbortInSequentialTasksWithStages<R>(\n  prerender: () => Promise<R>,\n  advanceStage: () => void,\n  abort: () => void\n): Promise<R> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`prerenderAndAbortInSequentialTasksWithStages` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let pendingResult: Promise<R>\n      setImmediate(() => {\n        try {\n          pendingResult = prerender()\n          pendingResult.catch(() => {})\n        } catch (err) {\n          reject(err)\n        }\n      })\n      setImmediate(() => {\n        advanceStage()\n      })\n      setImmediate(() => {\n        abort()\n        resolve(pendingResult)\n      })\n    })\n  }\n}\n\n// React's RSC prerender function will emit an incomplete flight stream when using `prerender`. If the connection\n// closes then whatever hanging chunks exist will be errored. This is because prerender (an experimental feature)\n// has not yet implemented a concept of resume. For now we will simulate a paused connection by wrapping the stream\n// in one that doesn't close even when the underlying is complete.\nexport class ReactServerResult {\n  private _stream: null | ReadableStream<Uint8Array>\n\n  constructor(stream: ReadableStream<Uint8Array>) {\n    this._stream = stream\n  }\n\n  tee() {\n    if (this._stream === null) {\n      throw new Error(\n        'Cannot tee a ReactServerResult that has already been consumed'\n      )\n    }\n    const tee = this._stream.tee()\n    this._stream = tee[0]\n    return tee[1]\n  }\n\n  consume() {\n    if (this._stream === null) {\n      throw new Error(\n        'Cannot consume a ReactServerResult that has already been consumed'\n      )\n    }\n    const stream = this._stream\n    this._stream = null\n    return stream\n  }\n}\n\nexport type ReactServerPrerenderResolveToType = {\n  prelude: ReadableStream<Uint8Array>\n}\n\nexport async function createReactServerPrerenderResult(\n  underlying: Promise<ReactServerPrerenderResolveToType>\n): Promise<ReactServerPrerenderResult> {\n  const chunks: Array<Uint8Array> = []\n  const { prelude } = await underlying\n  const reader = prelude.getReader()\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      return new ReactServerPrerenderResult(chunks)\n    } else {\n      chunks.push(value)\n    }\n  }\n}\n\nexport async function createReactServerPrerenderResultFromRender(\n  underlying: ReadableStream<Uint8Array>\n): Promise<ReactServerPrerenderResult> {\n  const chunks: Array<Uint8Array> = []\n  const reader = underlying.getReader()\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    } else {\n      chunks.push(value)\n    }\n  }\n  return new ReactServerPrerenderResult(chunks)\n}\nexport class ReactServerPrerenderResult {\n  private _chunks: null | Array<Uint8Array>\n\n  private assertChunks(expression: string): Array<Uint8Array> {\n    if (this._chunks === null) {\n      throw new InvariantError(\n        `Cannot \\`${expression}\\` on a ReactServerPrerenderResult that has already been consumed.`\n      )\n    }\n    return this._chunks\n  }\n\n  private consumeChunks(expression: string): Array<Uint8Array> {\n    const chunks = this.assertChunks(expression)\n    this.consume()\n    return chunks\n  }\n\n  consume(): void {\n    this._chunks = null\n  }\n\n  constructor(chunks: Array<Uint8Array>) {\n    this._chunks = chunks\n  }\n\n  asUnclosingStream(): ReadableStream<Uint8Array> {\n    const chunks = this.assertChunks('asUnclosingStream()')\n    return createUnclosingStream(chunks)\n  }\n\n  consumeAsUnclosingStream(): ReadableStream<Uint8Array> {\n    const chunks = this.consumeChunks('consumeAsUnclosingStream()')\n    return createUnclosingStream(chunks)\n  }\n\n  asStream(): ReadableStream<Uint8Array> {\n    const chunks = this.assertChunks('asStream()')\n    return createClosingStream(chunks)\n  }\n\n  consumeAsStream(): ReadableStream<Uint8Array> {\n    const chunks = this.consumeChunks('consumeAsStream()')\n    return createClosingStream(chunks)\n  }\n}\n\nfunction createUnclosingStream(\n  chunks: Array<Uint8Array>\n): ReadableStream<Uint8Array> {\n  let i = 0\n  return new ReadableStream({\n    async pull(controller) {\n      if (i < chunks.length) {\n        controller.enqueue(chunks[i++])\n      }\n      // we intentionally keep the stream open. The consumer will clear\n      // out chunks once finished and the remaining memory will be GC'd\n      // when this object goes out of scope\n    },\n  })\n}\n\nfunction createClosingStream(\n  chunks: Array<Uint8Array>\n): ReadableStream<Uint8Array> {\n  let i = 0\n  return new ReadableStream({\n    async pull(controller) {\n      if (i < chunks.length) {\n        controller.enqueue(chunks[i++])\n      } else {\n        controller.close()\n      }\n    },\n  })\n}\n\nexport async function processPrelude(\n  unprocessedPrelude: ReadableStream<Uint8Array>\n) {\n  const [prelude, peek] = unprocessedPrelude.tee()\n\n  const reader = peek.getReader()\n  const firstResult = await reader.read()\n  reader.cancel()\n\n  const preludeIsEmpty = firstResult.done === true\n\n  return { prelude, preludeIsEmpty }\n}\n"], "names": ["ReactServerPrerenderResult", "ReactServerResult", "createReactServerPrerenderResult", "createReactServerPrerenderResultFromRender", "prerenderAndAbortInSequentialTasks", "prerenderAndAbortInSequentialTasksWithStages", "processPrelude", "prerender", "abort", "process", "env", "NEXT_RUNTIME", "InvariantError", "Promise", "resolve", "reject", "pendingResult", "setImmediate", "catch", "err", "advanceStage", "constructor", "stream", "_stream", "tee", "Error", "consume", "underlying", "chunks", "prelude", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "push", "assertChunks", "expression", "_chunks", "consumeChunks", "asUnclosingStream", "createUnclosingStream", "consumeAsUnclosingStream", "asStream", "createClosingStream", "consumeAsStream", "i", "ReadableStream", "pull", "controller", "length", "enqueue", "close", "unprocessedPrelude", "peek", "firstResult", "cancel", "preludeIsEmpty"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAyIaA,0BAA0B;eAA1BA;;IAjEAC,iBAAiB;eAAjBA;;IAkCSC,gCAAgC;eAAhCA;;IAgBAC,0CAA0C;eAA1CA;;IApHNC,kCAAkC;eAAlCA;;IA+BAC,4CAA4C;eAA5CA;;IAkLMC,cAAc;eAAdA;;;gCAvNS;AAMxB,SAASF,mCACdG,SAA2B,EAC3BC,KAAiB;IAEjB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,+EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIC,QAAQ,CAACC,SAASC;YAC3B,IAAIC;YACJC,aAAa;gBACX,IAAI;oBACFD,gBAAgBT;oBAChBS,cAAcE,KAAK,CAAC,KAAO;gBAC7B,EAAE,OAAOC,KAAK;oBACZJ,OAAOI;gBACT;YACF;YACAF,aAAa;gBACXT;gBACAM,QAAQE;YACV;QACF;IACF;AACF;AAMO,SAASX,6CACdE,SAA2B,EAC3Ba,YAAwB,EACxBZ,KAAiB;IAEjB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,yFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIC,QAAQ,CAACC,SAASC;YAC3B,IAAIC;YACJC,aAAa;gBACX,IAAI;oBACFD,gBAAgBT;oBAChBS,cAAcE,KAAK,CAAC,KAAO;gBAC7B,EAAE,OAAOC,KAAK;oBACZJ,OAAOI;gBACT;YACF;YACAF,aAAa;gBACXG;YACF;YACAH,aAAa;gBACXT;gBACAM,QAAQE;YACV;QACF;IACF;AACF;AAMO,MAAMf;IAGXoB,YAAYC,MAAkC,CAAE;QAC9C,IAAI,CAACC,OAAO,GAAGD;IACjB;IAEAE,MAAM;QACJ,IAAI,IAAI,CAACD,OAAO,KAAK,MAAM;YACzB,MAAM,qBAEL,CAFK,IAAIE,MACR,kEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMD,MAAM,IAAI,CAACD,OAAO,CAACC,GAAG;QAC5B,IAAI,CAACD,OAAO,GAAGC,GAAG,CAAC,EAAE;QACrB,OAAOA,GAAG,CAAC,EAAE;IACf;IAEAE,UAAU;QACR,IAAI,IAAI,CAACH,OAAO,KAAK,MAAM;YACzB,MAAM,qBAEL,CAFK,IAAIE,MACR,sEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMH,SAAS,IAAI,CAACC,OAAO;QAC3B,IAAI,CAACA,OAAO,GAAG;QACf,OAAOD;IACT;AACF;AAMO,eAAepB,iCACpByB,UAAsD;IAEtD,MAAMC,SAA4B,EAAE;IACpC,MAAM,EAAEC,OAAO,EAAE,GAAG,MAAMF;IAC1B,MAAMG,SAASD,QAAQE,SAAS;IAChC,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMH,OAAOI,IAAI;QACzC,IAAIF,MAAM;YACR,OAAO,IAAIhC,2BAA2B4B;QACxC,OAAO;YACLA,OAAOO,IAAI,CAACF;QACd;IACF;AACF;AAEO,eAAe9B,2CACpBwB,UAAsC;IAEtC,MAAMC,SAA4B,EAAE;IACpC,MAAME,SAASH,WAAWI,SAAS;IACnC,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMH,OAAOI,IAAI;QACzC,IAAIF,MAAM;YACR;QACF,OAAO;YACLJ,OAAOO,IAAI,CAACF;QACd;IACF;IACA,OAAO,IAAIjC,2BAA2B4B;AACxC;AACO,MAAM5B;IAGHoC,aAAaC,UAAkB,EAAqB;QAC1D,IAAI,IAAI,CAACC,OAAO,KAAK,MAAM;YACzB,MAAM,qBAEL,CAFK,IAAI1B,8BAAc,CACtB,CAAC,SAAS,EAAEyB,WAAW,kEAAkE,CAAC,GADtF,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAO,IAAI,CAACC,OAAO;IACrB;IAEQC,cAAcF,UAAkB,EAAqB;QAC3D,MAAMT,SAAS,IAAI,CAACQ,YAAY,CAACC;QACjC,IAAI,CAACX,OAAO;QACZ,OAAOE;IACT;IAEAF,UAAgB;QACd,IAAI,CAACY,OAAO,GAAG;IACjB;IAEAjB,YAAYO,MAAyB,CAAE;QACrC,IAAI,CAACU,OAAO,GAAGV;IACjB;IAEAY,oBAAgD;QAC9C,MAAMZ,SAAS,IAAI,CAACQ,YAAY,CAAC;QACjC,OAAOK,sBAAsBb;IAC/B;IAEAc,2BAAuD;QACrD,MAAMd,SAAS,IAAI,CAACW,aAAa,CAAC;QAClC,OAAOE,sBAAsBb;IAC/B;IAEAe,WAAuC;QACrC,MAAMf,SAAS,IAAI,CAACQ,YAAY,CAAC;QACjC,OAAOQ,oBAAoBhB;IAC7B;IAEAiB,kBAA8C;QAC5C,MAAMjB,SAAS,IAAI,CAACW,aAAa,CAAC;QAClC,OAAOK,oBAAoBhB;IAC7B;AACF;AAEA,SAASa,sBACPb,MAAyB;IAEzB,IAAIkB,IAAI;IACR,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,IAAIH,IAAIlB,OAAOsB,MAAM,EAAE;gBACrBD,WAAWE,OAAO,CAACvB,MAAM,CAACkB,IAAI;YAChC;QACA,iEAAiE;QACjE,iEAAiE;QACjE,qCAAqC;QACvC;IACF;AACF;AAEA,SAASF,oBACPhB,MAAyB;IAEzB,IAAIkB,IAAI;IACR,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,IAAIH,IAAIlB,OAAOsB,MAAM,EAAE;gBACrBD,WAAWE,OAAO,CAACvB,MAAM,CAACkB,IAAI;YAChC,OAAO;gBACLG,WAAWG,KAAK;YAClB;QACF;IACF;AACF;AAEO,eAAe9C,eACpB+C,kBAA8C;IAE9C,MAAM,CAACxB,SAASyB,KAAK,GAAGD,mBAAmB7B,GAAG;IAE9C,MAAMM,SAASwB,KAAKvB,SAAS;IAC7B,MAAMwB,cAAc,MAAMzB,OAAOI,IAAI;IACrCJ,OAAO0B,MAAM;IAEb,MAAMC,iBAAiBF,YAAYvB,IAAI,KAAK;IAE5C,OAAO;QAAEH;QAAS4B;IAAe;AACnC", "ignoreList": [0]}