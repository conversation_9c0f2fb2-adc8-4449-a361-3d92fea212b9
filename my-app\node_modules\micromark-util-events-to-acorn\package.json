{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/micromark/micromark-extension-mdx-expression/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/estree": "^1.0.0", "@types/unist": "^3.0.0", "devlop": "^1.0.0", "estree-util-visit": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0", "vfile-message": "^4.0.0"}, "description": "micromark utility to try and parse events w/ acorn", "exports": {"development": "./dev/index.js", "default": "./index.js"}, "files": ["dev/", "index.d.ts", "index.js", "lib/"], "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "keywords": ["expression", "factory", "micromark", "mdx"], "license": "MIT", "name": "micromark-util-events-to-acorn", "repository": "https://github.com/micromark/micromark-extension-mdx-expression/tree/main/packages/micromark-util-events-to-acorn", "scripts": {"build": "micromark-build"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "strict": true}, "type": "module", "version": "2.0.3", "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}], "prettier": true, "rules": {"unicorn/prefer-at": "off", "unicorn/prefer-string-replace-all": "off"}}}