{"version": 3, "sources": ["../../../src/next-devtools/shared/deepmerge.ts"], "sourcesContent": ["export function deepMerge(target: any, source: any): any {\n  if (!source || typeof source !== 'object' || Array.isArray(source)) {\n    return source\n  }\n\n  if (!target || typeof target !== 'object' || Array.isArray(target)) {\n    return source\n  }\n\n  const result = { ...target }\n\n  for (const key in source) {\n    const sourceValue = source[key]\n    const targetValue = target[key]\n\n    if (sourceValue !== undefined) {\n      if (\n        sourceValue &&\n        typeof sourceValue === 'object' &&\n        !Array.isArray(sourceValue) &&\n        targetValue &&\n        typeof targetValue === 'object' &&\n        !Array.isArray(targetValue)\n      ) {\n        result[key] = deepMerge(targetValue, sourceValue)\n      } else {\n        result[key] = sourceValue\n      }\n    }\n  }\n\n  return result\n}\n"], "names": ["deepMerge", "target", "source", "Array", "isArray", "result", "key", "sourceValue", "targetValue", "undefined"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;AAAT,SAASA,UAAUC,MAAW,EAAEC,MAAW;IAChD,IAAI,CAACA,UAAU,OAAOA,WAAW,YAAYC,MAAMC,OAAO,CAACF,SAAS;QAClE,OAAOA;IACT;IAEA,IAAI,CAACD,UAAU,OAAOA,WAAW,YAAYE,MAAMC,OAAO,CAACH,SAAS;QAClE,OAAOC;IACT;IAEA,MAAMG,SAAS;QAAE,GAAGJ,MAAM;IAAC;IAE3B,IAAK,MAAMK,OAAOJ,OAAQ;QACxB,MAAMK,cAAcL,MAAM,CAACI,IAAI;QAC/B,MAAME,cAAcP,MAAM,CAACK,IAAI;QAE/B,IAAIC,gBAAgBE,WAAW;YAC7B,IACEF,eACA,OAAOA,gBAAgB,YACvB,CAACJ,MAAMC,OAAO,CAACG,gBACfC,eACA,OAAOA,gBAAgB,YACvB,CAACL,MAAMC,OAAO,CAACI,cACf;gBACAH,MAAM,CAACC,IAAI,GAAGN,UAAUQ,aAAaD;YACvC,OAAO;gBACLF,MAAM,CAACC,IAAI,GAAGC;YAChB;QACF;IACF;IAEA,OAAOF;AACT", "ignoreList": [0]}