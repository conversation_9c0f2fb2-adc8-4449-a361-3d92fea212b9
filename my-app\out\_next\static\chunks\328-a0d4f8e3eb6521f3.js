"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[328],{7328:(t,e,r)=>{function i(t,e,r){if(!e.has(t))throw TypeError("attempted to "+r+" private field on non-instance");return e.get(t)}function s(t,e){var r=i(t,e,"get");return r.get?r.get.call(t):r.value}function l(t,e,r){var s=i(t,e,"set");if(s.set)s.set.call(t,r);else{if(!s.writable)throw TypeError("attempted to set read only private field");s.value=r}return r}r.d(e,{N:()=>c});var n,h=r(2115),f=r(6081),o=r(6101),u=r(9708),a=r(5155);function c(t){let e=t+"CollectionProvider",[r,i]=(0,f.A)(e),[s,l]=r(e,{collectionRef:{current:null},itemMap:new Map}),n=t=>{let{scope:e,children:r}=t,i=h.useRef(null),l=h.useRef(new Map).current;return(0,a.jsx)(s,{scope:e,itemMap:l,collectionRef:i,children:r})};n.displayName=e;let c=t+"CollectionSlot",p=(0,u.TL)(c),d=h.forwardRef((t,e)=>{let{scope:r,children:i}=t,s=l(c,r),n=(0,o.s)(e,s.collectionRef);return(0,a.jsx)(p,{ref:n,children:i})});d.displayName=c;let y=t+"CollectionItemSlot",v="data-radix-collection-item",m=(0,u.TL)(y),w=h.forwardRef((t,e)=>{let{scope:r,children:i,...s}=t,n=h.useRef(null),f=(0,o.s)(e,n),u=l(y,r);return h.useEffect(()=>(u.itemMap.set(n,{ref:n,...s}),()=>void u.itemMap.delete(n))),(0,a.jsx)(m,{...{[v]:""},ref:f,children:i})});return w.displayName=y,[{Provider:n,Slot:d,ItemSlot:w},function(e){let r=l(t+"CollectionConsumer",e);return h.useCallback(()=>{let t=r.collectionRef.current;if(!t)return[];let e=Array.from(t.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((t,r)=>e.indexOf(t.ref.current)-e.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},i]}var p=new WeakMap;function d(t,e){if("at"in Array.prototype)return Array.prototype.at.call(t,e);let r=function(t,e){let r=t.length,i=y(e),s=i>=0?i:r+i;return s<0||s>=r?-1:s}(t,e);return -1===r?void 0:t[r]}function y(t){return t!=t||0===t?0:Math.trunc(t)}n=new WeakMap,class t extends Map{set(t,e){return p.get(this)&&(this.has(t)?s(this,n)[s(this,n).indexOf(t)]=t:s(this,n).push(t)),super.set(t,e),this}insert(t,e,r){let i,l=this.has(e),h=s(this,n).length,f=y(t),o=f>=0?f:h+f,u=o<0||o>=h?-1:o;if(u===this.size||l&&u===this.size-1||-1===u)return this.set(e,r),this;let a=this.size+ +!l;f<0&&o++;let c=[...s(this,n)],p=!1;for(let t=o;t<a;t++)if(o===t){let s=c[t];c[t]===e&&(s=c[t+1]),l&&this.delete(e),i=this.get(s),this.set(e,r)}else{p||c[t-1]!==e||(p=!0);let r=c[p?t:t-1],s=i;i=this.get(r),this.delete(r),this.set(r,s)}return this}with(e,r,i){let s=new t(this);return s.insert(e,r,i),s}before(t){let e=s(this,n).indexOf(t)-1;if(!(e<0))return this.entryAt(e)}setBefore(t,e,r){let i=s(this,n).indexOf(t);return -1===i?this:this.insert(i,e,r)}after(t){let e=s(this,n).indexOf(t);if(-1!==(e=-1===e||e===this.size-1?-1:e+1))return this.entryAt(e)}setAfter(t,e,r){let i=s(this,n).indexOf(t);return -1===i?this:this.insert(i+1,e,r)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return l(this,n,[]),super.clear()}delete(t){let e=super.delete(t);return e&&s(this,n).splice(s(this,n).indexOf(t),1),e}deleteAt(t){let e=this.keyAt(t);return void 0!==e&&this.delete(e)}at(t){let e=d(s(this,n),t);if(void 0!==e)return this.get(e)}entryAt(t){let e=d(s(this,n),t);if(void 0!==e)return[e,this.get(e)]}indexOf(t){return s(this,n).indexOf(t)}keyAt(t){return d(s(this,n),t)}from(t,e){let r=this.indexOf(t);if(-1===r)return;let i=r+e;return i<0&&(i=0),i>=this.size&&(i=this.size-1),this.at(i)}keyFrom(t,e){let r=this.indexOf(t);if(-1===r)return;let i=r+e;return i<0&&(i=0),i>=this.size&&(i=this.size-1),this.keyAt(i)}find(t,e){let r=0;for(let i of this){if(Reflect.apply(t,e,[i,r,this]))return i;r++}}findIndex(t,e){let r=0;for(let i of this){if(Reflect.apply(t,e,[i,r,this]))return r;r++}return -1}filter(e,r){let i=[],s=0;for(let t of this)Reflect.apply(e,r,[t,s,this])&&i.push(t),s++;return new t(i)}map(e,r){let i=[],s=0;for(let t of this)i.push([t[0],Reflect.apply(e,r,[t,s,this])]),s++;return new t(i)}reduce(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];let[i,s]=e,l=0,n=null!=s?s:this.at(0);for(let t of this)n=0===l&&1===e.length?t:Reflect.apply(i,this,[n,t,l,this]),l++;return n}reduceRight(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];let[i,s]=e,l=null!=s?s:this.at(-1);for(let t=this.size-1;t>=0;t--){let r=this.at(t);l=t===this.size-1&&1===e.length?r:Reflect.apply(i,this,[l,r,t,this])}return l}toSorted(e){return new t([...this.entries()].sort(e))}toReversed(){let e=new t;for(let t=this.size-1;t>=0;t--){let r=this.keyAt(t),i=this.get(r);e.set(r,i)}return e}toSpliced(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];let s=[...this.entries()];return s.splice(...r),new t(s)}slice(e,r){let i=new t,s=this.size-1;if(void 0===e)return i;e<0&&(e+=this.size),void 0!==r&&r>0&&(s=r-1);for(let t=e;t<=s;t++){let e=this.keyAt(t),r=this.get(e);i.set(e,r)}return i}every(t,e){let r=0;for(let i of this){if(!Reflect.apply(t,e,[i,r,this]))return!1;r++}return!0}some(t,e){let r=0;for(let i of this){if(Reflect.apply(t,e,[i,r,this]))return!0;r++}return!1}constructor(t){super(t),function(t,e,r){if(e.has(t))throw TypeError("Cannot initialize the same private elements twice on an object");e.set(t,r)}(this,n,{writable:!0,value:void 0}),l(this,n,[...super.keys()]),p.set(this,!0)}}}}]);