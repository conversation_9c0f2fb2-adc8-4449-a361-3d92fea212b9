{"version": 3, "sources": ["../../../../src/server/lib/router-utils/instrumentation-node-extensions.ts"], "sourcesContent": ["/**\n * This extension augments opentelemetry after registration if applicable.\n * This extension must only be loaded in Node environments.\n */\n\nimport type { Tracer } from '@opentelemetry/api'\nimport {\n  type WorkUnitStore,\n  workUnitAsyncStorage,\n} from '../../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../../shared/lib/invariant-error'\nimport { isUseCacheFunction } from '../../../lib/client-and-server-references'\n\nexport function afterRegistration(): void {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      'Node.js instrumentation extensions should not be loaded in the Edge runtime.'\n    )\n  }\n\n  extendTracerProviderForCacheComponents()\n}\n\n// In theory we only want to enable this extension when cacheComponents is enabled\n// however there are certain servers that might load instrumentation before nextConfig is available\n// and so gating it on the config might lead to skipping this extension even when it is necessary.\n// When cacheComponents is disabled this extension should be a no-op so we enable it universally.\n// Additionally, soon, cacheComponents will be enabled always so this just pulls the extension forward in time\nfunction extendTracerProviderForCacheComponents(): void {\n  let api: typeof import('next/dist/compiled/@opentelemetry/api')\n\n  // we want to allow users to use their own version of @opentelemetry/api if they\n  // want to, so we try to require it first, and if it fails we fall back to the\n  // version that is bundled with Next.js\n  // this is because @opentelemetry/api has to be synced with the version of\n  // @opentelemetry/tracing that is used, and we don't want to force users to use\n  // the version that is bundled with Next.js.\n  // the API is ~stable, so this should be fine\n  try {\n    api = require('@opentelemetry/api') as typeof import('@opentelemetry/api')\n  } catch (err) {\n    api =\n      require('next/dist/compiled/@opentelemetry/api') as typeof import('next/dist/compiled/@opentelemetry/api')\n  }\n\n  const provider = api.trace.getTracerProvider()\n\n  // When Cache Components is enabled we need to instrument the tracer\n  // to exit the workUnitAsyncStorage context when generating spans.\n  const originalGetTracer = provider.getTracer.bind(provider)\n  provider.getTracer = (...args) => {\n    const tracer = originalGetTracer.apply(provider, args)\n    if (WeakTracers.has(tracer)) {\n      return tracer\n    }\n    const originalStartSpan = tracer.startSpan\n    tracer.startSpan = (...startSpanArgs) => {\n      return workUnitAsyncStorage.exit(() =>\n        originalStartSpan.apply(tracer, startSpanArgs)\n      )\n    }\n\n    const originalStartActiveSpan = tracer.startActiveSpan\n    // @ts-ignore TS doesn't recognize the overloads correctly\n    tracer.startActiveSpan = (...startActiveSpanArgs: any[]) => {\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      if (!workUnitStore) {\n        // @ts-ignore TS doesn't recognize the overloads correctly\n        return originalStartActiveSpan.apply(tracer, startActiveSpanArgs)\n      }\n\n      let fnIdx: number = 0\n      if (\n        startActiveSpanArgs.length === 2 &&\n        typeof startActiveSpanArgs[1] === 'function'\n      ) {\n        fnIdx = 1\n      } else if (\n        startActiveSpanArgs.length === 3 &&\n        typeof startActiveSpanArgs[2] === 'function'\n      ) {\n        fnIdx = 2\n      } else if (\n        startActiveSpanArgs.length > 3 &&\n        typeof startActiveSpanArgs[3] === 'function'\n      ) {\n        fnIdx = 3\n      }\n\n      if (fnIdx) {\n        const originalFn = startActiveSpanArgs[fnIdx]\n        if (isUseCacheFunction(originalFn)) {\n          console.error(\n            'A Cache Function (`use cache`) was passed to startActiveSpan which means it will receive a Span argument with a possibly random ID on every invocation leading to cache misses. Provide a wrapping function around the Cache Function that does not forward the Span argument to avoid this issue.'\n          )\n        }\n        startActiveSpanArgs[fnIdx] = withWorkUnitContext(\n          workUnitStore,\n          originalFn\n        )\n      }\n\n      return workUnitAsyncStorage.exit(() => {\n        // @ts-ignore TS doesn't recognize the overloads correctly\n        return originalStartActiveSpan.apply(tracer, startActiveSpanArgs)\n      })\n    }\n\n    WeakTracers.add(tracer)\n    return tracer\n  }\n}\n\nconst WeakTracers = new WeakSet<Tracer>()\n\nfunction withWorkUnitContext(\n  workUnitStore: WorkUnitStore,\n  fn: (...args: any[]) => any\n) {\n  return (...args: any[]) =>\n    workUnitAsyncStorage.run(workUnitStore, fn, ...args)\n}\n"], "names": ["afterRegistration", "process", "env", "NEXT_RUNTIME", "InvariantError", "extendTracerProviderForCacheComponents", "api", "require", "err", "provider", "trace", "getTracer<PERSON>rovider", "originalGetTracer", "getTracer", "bind", "args", "tracer", "apply", "WeakTracers", "has", "originalStartSpan", "startSpan", "startSpanArgs", "workUnitAsyncStorage", "exit", "originalStartActiveSpan", "startActiveSpan", "startActiveSpanArgs", "workUnitStore", "getStore", "fnIdx", "length", "originalFn", "isUseCacheFunction", "console", "error", "withWorkUnitContext", "add", "WeakSet", "fn", "run"], "mappings": "AAAA;;;CAGC;;;;+BAUeA;;;eAAAA;;;8CAJT;gCACwB;2CACI;AAE5B,SAASA;IACd,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,iFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAC;AACF;AAEA,kFAAkF;AAClF,mGAAmG;AACnG,kGAAkG;AAClG,iGAAiG;AACjG,8GAA8G;AAC9G,SAASA;IACP,IAAIC;IAEJ,gFAAgF;IAChF,8EAA8E;IAC9E,uCAAuC;IACvC,0EAA0E;IAC1E,+EAA+E;IAC/E,4CAA4C;IAC5C,6CAA6C;IAC7C,IAAI;QACFA,MAAMC,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZF,MACEC,QAAQ;IACZ;IAEA,MAAME,WAAWH,IAAII,KAAK,CAACC,iBAAiB;IAE5C,oEAAoE;IACpE,kEAAkE;IAClE,MAAMC,oBAAoBH,SAASI,SAAS,CAACC,IAAI,CAACL;IAClDA,SAASI,SAAS,GAAG,CAAC,GAAGE;QACvB,MAAMC,SAASJ,kBAAkBK,KAAK,CAACR,UAAUM;QACjD,IAAIG,YAAYC,GAAG,CAACH,SAAS;YAC3B,OAAOA;QACT;QACA,MAAMI,oBAAoBJ,OAAOK,SAAS;QAC1CL,OAAOK,SAAS,GAAG,CAAC,GAAGC;YACrB,OAAOC,kDAAoB,CAACC,IAAI,CAAC,IAC/BJ,kBAAkBH,KAAK,CAACD,QAAQM;QAEpC;QAEA,MAAMG,0BAA0BT,OAAOU,eAAe;QACtD,0DAA0D;QAC1DV,OAAOU,eAAe,GAAG,CAAC,GAAGC;YAC3B,MAAMC,gBAAgBL,kDAAoB,CAACM,QAAQ;YACnD,IAAI,CAACD,eAAe;gBAClB,0DAA0D;gBAC1D,OAAOH,wBAAwBR,KAAK,CAACD,QAAQW;YAC/C;YAEA,IAAIG,QAAgB;YACpB,IACEH,oBAAoBI,MAAM,KAAK,KAC/B,OAAOJ,mBAAmB,CAAC,EAAE,KAAK,YAClC;gBACAG,QAAQ;YACV,OAAO,IACLH,oBAAoBI,MAAM,KAAK,KAC/B,OAAOJ,mBAAmB,CAAC,EAAE,KAAK,YAClC;gBACAG,QAAQ;YACV,OAAO,IACLH,oBAAoBI,MAAM,GAAG,KAC7B,OAAOJ,mBAAmB,CAAC,EAAE,KAAK,YAClC;gBACAG,QAAQ;YACV;YAEA,IAAIA,OAAO;gBACT,MAAME,aAAaL,mBAAmB,CAACG,MAAM;gBAC7C,IAAIG,IAAAA,6CAAkB,EAACD,aAAa;oBAClCE,QAAQC,KAAK,CACX;gBAEJ;gBACAR,mBAAmB,CAACG,MAAM,GAAGM,oBAC3BR,eACAI;YAEJ;YAEA,OAAOT,kDAAoB,CAACC,IAAI,CAAC;gBAC/B,0DAA0D;gBAC1D,OAAOC,wBAAwBR,KAAK,CAACD,QAAQW;YAC/C;QACF;QAEAT,YAAYmB,GAAG,CAACrB;QAChB,OAAOA;IACT;AACF;AAEA,MAAME,cAAc,IAAIoB;AAExB,SAASF,oBACPR,aAA4B,EAC5BW,EAA2B;IAE3B,OAAO,CAAC,GAAGxB,OACTQ,kDAAoB,CAACiB,GAAG,CAACZ,eAAeW,OAAOxB;AACnD", "ignoreList": [0]}