{"version": 3, "sources": ["../../../src/server/request/connection.ts"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport {\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  makeHangingPromise,\n  makeDevtoolsIOAwarePromise,\n} from '../dynamic-rendering-utils'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * This function allows you to indicate that you require an actual user Request before continuing.\n *\n * During prerendering it will never resolve and during rendering it resolves immediately.\n */\nexport function connection(): Promise<void> {\n  const callingExpression = 'connection'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"connection\" inside \"after(...)\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but \"after(...)\" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic, we override all other logic and always just\n      // return a resolving promise without tracking.\n      return Promise.resolve(undefined)\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`connection\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"connection\" inside \"use cache\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual request, but caches must be able to be produced before a request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, connection)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'private-cache': {\n          // It might not be intuitive to throw for private caches as well, but\n          // we don't consider runtime prefetches as \"actual requests\" (in the\n          // navigation sense), despite allowing them to read cookies.\n          const error = new Error(\n            `Route ${workStore.route} used \"connection\" inside \"use cache: private\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual navigation request, but caches must be able to be produced before a navigation request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, connection)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"connection\" inside a function cached with \"unstable_cache(...)\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-runtime':\n          // We return a promise that never resolves to allow the prerender to\n          // stall at this point.\n          return makeHangingPromise(\n            workUnitStore.renderSignal,\n            workStore.route,\n            '`connection()`'\n          )\n        case 'prerender-ppr':\n          // We use React's postpone API to interrupt rendering here to create a\n          // dynamic hole\n          return postponeWithTracking(\n            workStore.route,\n            'connection',\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          // We throw an error here to interrupt prerendering to mark the route\n          // as dynamic\n          return throwToInterruptStaticGeneration(\n            'connection',\n            workStore,\n            workUnitStore\n          )\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n          if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            return makeDevtoolsIOAwarePromise(undefined)\n          } else {\n            return Promise.resolve(undefined)\n          }\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n\n  // If we end up here, there was no work store or work unit store present.\n  throwForMissingRequestStore(callingExpression)\n}\n"], "names": ["workAsyncStorage", "throwForMissingRequestStore", "workUnitAsyncStorage", "postponeWithTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "StaticGenBailoutError", "makeHangingPromise", "makeDevtoolsIOAwarePromise", "isRequestAPICallableInsideAfter", "connection", "callingExpression", "workStore", "getStore", "workUnitStore", "phase", "Error", "route", "forceStatic", "Promise", "resolve", "undefined", "dynamicShouldError", "type", "error", "captureStackTrace", "invalidDynamicUsageError", "renderSignal", "dynamicTracking", "process", "env", "NODE_ENV"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,4CAA2C;AAC5E,SACEC,2BAA2B,EAC3BC,oBAAoB,QACf,iDAAgD;AACvD,SACEC,oBAAoB,EACpBC,gCAAgC,EAChCC,+BAA+B,QAC1B,kCAAiC;AACxC,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SACEC,kBAAkB,EAClBC,0BAA0B,QACrB,6BAA4B;AACnC,SAASC,+BAA+B,QAAQ,UAAS;AAEzD;;;;CAIC,GACD,OAAO,SAASC;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYZ,iBAAiBa,QAAQ;IAC3C,MAAMC,gBAAgBZ,qBAAqBW,QAAQ;IAEnD,IAAID,WAAW;QACb,IACEE,iBACAA,cAAcC,KAAK,KAAK,WACxB,CAACN,mCACD;YACA,MAAM,qBAEL,CAFK,IAAIO,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,6UAA6U,CAAC,GADnW,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,UAAUM,WAAW,EAAE;YACzB,sEAAsE;YACtE,+CAA+C;YAC/C,OAAOC,QAAQC,OAAO,CAACC;QACzB;QAEA,IAAIT,UAAUU,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIhB,sBACR,CAAC,MAAM,EAAEM,UAAUK,KAAK,CAAC,oNAAoN,CAAC,GAD1O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIH,eAAe;YACjB,OAAQA,cAAcS,IAAI;gBACxB,KAAK;oBAAS;wBACZ,MAAMC,QAAQ,qBAEb,CAFa,IAAIR,MAChB,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,kVAAkV,CAAC,GADhW,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAD,MAAMS,iBAAiB,CAACD,OAAOd;wBAC/BE,UAAUc,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBAAiB;wBACpB,qEAAqE;wBACrE,oEAAoE;wBACpE,4DAA4D;wBAC5D,MAAMA,QAAQ,qBAEb,CAFa,IAAIR,MAChB,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,iXAAiX,CAAC,GAD/X,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAD,MAAMS,iBAAiB,CAACD,OAAOd;wBAC/BE,UAAUc,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBACH,MAAM,qBAEL,CAFK,IAAIR,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,0XAA0X,CAAC,GADhZ,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,oEAAoE;oBACpE,uBAAuB;oBACvB,OAAOV,mBACLO,cAAca,YAAY,EAC1Bf,UAAUK,KAAK,EACf;gBAEJ,KAAK;oBACH,sEAAsE;oBACtE,eAAe;oBACf,OAAOd,qBACLS,UAAUK,KAAK,EACf,cACAH,cAAcc,eAAe;gBAEjC,KAAK;oBACH,qEAAqE;oBACrE,aAAa;oBACb,OAAOxB,iCACL,cACAQ,WACAE;gBAEJ,KAAK;oBACHT,gCAAgCS;oBAChC,IAAIe,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;wBAC1C,wEAAwE;wBACxE,8EAA8E;wBAC9E,4EAA4E;wBAC5E,OAAOvB,2BAA2Ba;oBACpC,OAAO;wBACL,OAAOF,QAAQC,OAAO,CAACC;oBACzB;gBACF;oBACEP;YACJ;QACF;IACF;IAEA,yEAAyE;IACzEb,4BAA4BU;AAC9B", "ignoreList": [0]}