{"version": 3, "sources": ["../../../../../../src/server/route-matcher-providers/dev/helpers/file-reader/default-file-reader.ts"], "sourcesContent": ["import type { <PERSON><PERSON>eader } from './file-reader'\nimport type { RecursiveReadDirOptions } from '../../../../../lib/recursive-readdir'\nimport { recursiveReadDir } from '../../../../../lib/recursive-readdir'\n\nexport type DefaultFileReaderOptions = Pick<\n  RecursiveReadDirOptions,\n  'pathnameFilter' | 'ignorePartFilter'\n>\n\n/**\n * Reads all the files in the directory and its subdirectories following any\n * symbolic links.\n */\nexport class DefaultFileReader implements FileReader {\n  /**\n   * Filter to ignore files with absolute pathnames. If undefined, no files are\n   * ignored.\n   */\n  private readonly options: Readonly<DefaultFileReaderOptions>\n\n  /**\n   * Creates a new file reader.\n   *\n   * @param pathnameFilter filter to ignore files with absolute pathnames, false to ignore\n   * @param ignoreFilter filter to ignore files and directories with absolute pathnames, false to ignore\n   * @param ignorePartFilter filter to ignore files and directories with the pathname part, false to ignore\n   */\n  constructor(options: Readonly<DefaultFileReaderOptions>) {\n    this.options = options\n  }\n\n  /**\n   * Reads all the files in the directory and its subdirectories following any\n   * symbolic links.\n   *\n   * @param dir the directory to read\n   * @returns a promise that resolves to the list of files\n   */\n  public async read(dir: string): Promise<ReadonlyArray<string>> {\n    return recursiveReadDir(dir, {\n      pathnameFilter: this.options.pathnameFilter,\n      ignorePartFilter: this.options.ignorePartFilter,\n\n      // We don't need to sort the results because we're not depending on the\n      // order of the results.\n      sortPathnames: false,\n\n      // We want absolute pathnames because we're going to be comparing them\n      // with other absolute pathnames.\n      relativePathnames: false,\n    })\n  }\n}\n"], "names": ["recursiveReadDir", "DefaultFileReader", "constructor", "options", "read", "dir", "pathnameFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sortPathnames", "relativePathnames"], "mappings": "AAEA,SAASA,gBAAgB,QAAQ,uCAAsC;AAOvE;;;CAGC,GACD,OAAO,MAAMC;IAOX;;;;;;GAMC,GACDC,YAAYC,OAA2C,CAAE;QACvD,IAAI,CAACA,OAAO,GAAGA;IACjB;IAEA;;;;;;GAMC,GACD,MAAaC,KAAKC,GAAW,EAAkC;QAC7D,OAAOL,iBAAiBK,KAAK;YAC3BC,gBAAgB,IAAI,CAACH,OAAO,CAACG,cAAc;YAC3CC,kBAAkB,IAAI,CAACJ,OAAO,CAACI,gBAAgB;YAE/C,uEAAuE;YACvE,wBAAwB;YACxBC,eAAe;YAEf,sEAAsE;YACtE,iCAAiC;YACjCC,mBAAmB;QACrB;IACF;AACF", "ignoreList": [0]}