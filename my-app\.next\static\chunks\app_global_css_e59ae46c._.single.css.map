{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/global.css"], "sourcesContent": ["/*! tailwindcss v4.1.12 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-sans: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\",\n      \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\",\n      \"Courier New\", monospace;\n    --color-black: #000;\n    --spacing: 0.25rem;\n    --breakpoint-sm: 40rem;\n    --container-sm: 24rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --radius-sm: 0.25rem;\n    --radius-md: 0.375rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --radius-2xl: 1rem;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-xs: 4px;\n    --blur-sm: 8px;\n    --blur-lg: 16px;\n    --blur-xl: 24px;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-sans);\n    --default-mono-font-family: var(--font-mono);\n    --color-fd-background: hsl(0, 0%, 96%);\n    --color-fd-foreground: hsl(0, 0%, 3.9%);\n    --color-fd-muted: hsl(0, 0%, 96.1%);\n    --color-fd-muted-foreground: hsl(0, 0%, 45.1%);\n    --color-fd-popover: hsl(0, 0%, 98%);\n    --color-fd-popover-foreground: hsl(0, 0%, 15.1%);\n    --color-fd-card: hsl(0, 0%, 94.7%);\n    --color-fd-card-foreground: hsl(0, 0%, 3.9%);\n    --color-fd-border: hsla(0, 0%, 62%, 20%);\n    --color-fd-primary: hsl(0, 0%, 9%);\n    --color-fd-primary-foreground: hsl(0, 0%, 98%);\n    --color-fd-secondary: hsl(0, 0%, 93.1%);\n    --color-fd-secondary-foreground: hsl(0, 0%, 9%);\n    --color-fd-accent: hsla(0, 0%, 82%, 50%);\n    --color-fd-accent-foreground: hsl(0, 0%, 9%);\n    --color-fd-info: oklch(62.3% 0.214 259.815);\n    --color-fd-warning: oklch(76.9% 0.188 70.08);\n    --color-fd-error: oklch(63.7% 0.237 25.331);\n    --color-fd-success: oklch(72.3% 0.219 149.579);\n    --fd-sidebar-mobile-offset: 100%;\n    --spacing-fd-container: 1400px;\n    --fd-page-width: 1200px;\n    --fd-sidebar-width: 0px;\n    --fd-toc-width: 0px;\n    --fd-layout-width: 100vw;\n    --fd-banner-height: 0px;\n    --fd-nav-height: 0px;\n    --fd-tocnav-height: 0px;\n    --color-fd-diff-remove: rgba(200, 10, 100, 0.12);\n    --color-fd-diff-remove-symbol: rgb(230, 10, 100);\n    --color-fd-diff-add: rgba(14, 180, 100, 0.1);\n    --color-fd-diff-add-symbol: rgb(10, 200, 100);\n    --animate-fd-fade-in: fd-fade-in 300ms ease;\n    --animate-fd-fade-out: fd-fade-out 300ms ease;\n    --animate-fd-dialog-in: fd-dialog-in 300ms cubic-bezier(0.16, 1, 0.3, 1);\n    --animate-fd-dialog-out: fd-dialog-out 300ms cubic-bezier(0.16, 1, 0.3, 1);\n    --animate-fd-popover-in: fd-popover-in 130ms ease;\n    --animate-fd-popover-out: fd-popover-out 130ms ease;\n    --animate-fd-collapsible-down: fd-collapsible-down 150ms\n    cubic-bezier(0.45, 0, 0.55, 1);\n    --animate-fd-collapsible-up: fd-collapsible-up 150ms\n    cubic-bezier(0.45, 0, 0.55, 1);\n    --animate-fd-accordion-down: fd-accordion-down 200ms ease-out;\n    --animate-fd-accordion-up: fd-accordion-up 200ms ease-out;\n    --animate-fd-nav-menu-in: fd-nav-menu-in 200ms ease;\n    --animate-fd-nav-menu-out: fd-nav-menu-out 200ms ease;\n    --animate-fd-enterFromLeft: fd-enterFromLeft 250ms ease;\n    --animate-fd-enterFromRight: fd-enterFromRight 250ms ease;\n    --animate-fd-sidebar-in: fd-sidebar-in 250ms ease;\n    --animate-fd-sidebar-out: fd-sidebar-out 250ms ease;\n    --animate-fd-exitToLeft: fd-exitToLeft 250ms ease;\n    --animate-fd-exitToRight: fd-exitToRight 250ms ease;\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  ::-webkit-calendar-picker-indicator {\n    line-height: 1;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .\\@container {\n    container-type: inline-size;\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .invisible {\n    visibility: hidden;\n  }\n  .fd-step {\n    &:before {\n      background-color: var(--color-fd-secondary);\n      color: var(--color-fd-secondary-foreground);\n      content: counter(step);\n      counter-increment: step;\n      justify-content: center;\n      align-items: center;\n      font-size: 0.875rem;\n      line-height: 1.25rem;\n      display: flex;\n      position: absolute;\n      inset-inline-start: calc(var(--spacing) * -4);\n      width: calc(var(--spacing) * 8);\n      height: calc(var(--spacing) * 8);\n      border-radius: calc(infinity * 1px);\n    }\n  }\n  .fd-steps {\n    counter-reset: step;\n    position: relative;\n    margin-left: calc(var(--spacing) * 2);\n    border-left-style: var(--tw-border-style);\n    border-left-width: 1px;\n    padding-left: calc(var(--spacing) * 6);\n    @media (width >= 40rem) {\n      margin-left: calc(var(--spacing) * 4);\n    }\n    @media (width >= 40rem) {\n      padding-left: calc(var(--spacing) * 7);\n    }\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .sticky {\n    position: sticky;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-x-0 {\n    inset-inline: calc(var(--spacing) * 0);\n  }\n  .inset-x-2 {\n    inset-inline: calc(var(--spacing) * 2);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .inset-y-1 {\n    inset-block: calc(var(--spacing) * 1);\n  }\n  .start-0 {\n    inset-inline-start: calc(var(--spacing) * 0);\n  }\n  .start-2\\.5 {\n    inset-inline-start: calc(var(--spacing) * 2.5);\n  }\n  .start-4\\.5 {\n    inset-inline-start: calc(var(--spacing) * 4.5);\n  }\n  .end-0 {\n    inset-inline-end: calc(var(--spacing) * 0);\n  }\n  .end-2 {\n    inset-inline-end: calc(var(--spacing) * 2);\n  }\n  .-top-1\\.5 {\n    top: calc(var(--spacing) * -1.5);\n  }\n  .top-\\(--fd-banner-height\\) {\n    top: var(--fd-banner-height);\n  }\n  .top-\\(--fd-sidebar-top\\) {\n    top: var(--fd-sidebar-top);\n  }\n  .top-\\(--fd-top\\) {\n    top: var(--fd-top);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1 {\n    top: calc(var(--spacing) * 1);\n  }\n  .top-1\\.5 {\n    top: calc(var(--spacing) * 1.5);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-1 {\n    right: calc(var(--spacing) * 1);\n  }\n  .bottom-\\(--fd-sidebar-margin\\) {\n    bottom: var(--fd-sidebar-margin);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-1\\.5 {\n    bottom: calc(var(--spacing) * 1.5);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .z-2 {\n    z-index: 2;\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-30 {\n    z-index: 30;\n  }\n  .z-40 {\n    z-index: 40;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-\\[-1\\] {\n    z-index: -1;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .container {\n    margin-inline: auto;\n    padding-inline: 1rem;\n    @media (width >= 96rem) {\n      max-width: 1400px;\n    }\n  }\n  .-mx-1 {\n    margin-inline: calc(var(--spacing) * -1);\n  }\n  .mx-0\\.5 {\n    margin-inline: calc(var(--spacing) * 0.5);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .\\!my-0 {\n    margin-block: calc(var(--spacing) * 0) !important;\n  }\n  .my-0 {\n    margin-block: calc(var(--spacing) * 0);\n  }\n  .my-4 {\n    margin-block: calc(var(--spacing) * 4);\n  }\n  .my-6 {\n    margin-block: calc(var(--spacing) * 6);\n  }\n  .my-auto {\n    margin-block: auto;\n  }\n  .-ms-1\\.5 {\n    margin-inline-start: calc(var(--spacing) * -1.5);\n  }\n  .ms-2 {\n    margin-inline-start: calc(var(--spacing) * 2);\n  }\n  .ms-auto {\n    margin-inline-start: auto;\n  }\n  .ms-px {\n    margin-inline-start: 1px;\n  }\n  .-me-0\\.5 {\n    margin-inline-end: calc(var(--spacing) * -0.5);\n  }\n  .-me-1\\.5 {\n    margin-inline-end: calc(var(--spacing) * -1.5);\n  }\n  .me-2 {\n    margin-inline-end: calc(var(--spacing) * 2);\n  }\n  .me-auto {\n    margin-inline-end: auto;\n  }\n  .prose {\n    color: var(--tw-prose-body);\n    max-width: none;\n    font-size: 1rem;\n    line-height: 1.75rem;\n    :where([class~=\"lead\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 1.25em;\n      line-height: 1.6;\n      margin-top: 1.2em;\n      margin-bottom: 1.2em;\n      color: var(--tw-prose-lead);\n    }\n    :where(ul):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 1rem;\n      list-style-type: disc;\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n    }\n    :where(li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5em;\n      margin-bottom: 0.5em;\n    }\n    :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0.375em;\n    }\n    :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0;\n    }\n    :where(.prose > ul > li p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.75em;\n      margin-bottom: 0.75em;\n    }\n    :where(.prose > ul > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n    }\n    :where(.prose > ul > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.25em;\n    }\n    :where(.prose > ol > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n    }\n    :where(.prose > ol > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.25em;\n    }\n    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.75em;\n      margin-bottom: 0.75em;\n    }\n    :where(dl):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n    }\n    :where(dt):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      margin-top: 1.25em;\n    }\n    :where(dd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5em;\n      padding-inline-start: 1.625em;\n    }\n    :where(hr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-color: var(--tw-prose-hr);\n      border-top-width: 1px;\n      margin-top: 3em;\n      margin-bottom: 3em;\n    }\n    :where(p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n    }\n    :where(strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-bold);\n      font-weight: 500;\n    }\n    :where(a strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(blockquote strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(thead th strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: decimal;\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n      padding-inline-start: 1.625em;\n    }\n    :where(ol[type=\"A\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-alpha;\n    }\n    :where(ol[type=\"a\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-alpha;\n    }\n    :where(ol[type=\"A\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-alpha;\n    }\n    :where(ol[type=\"a\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-alpha;\n    }\n    :where(ol[type=\"I\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-roman;\n    }\n    :where(ol[type=\"i\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-roman;\n    }\n    :where(ol[type=\"I\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-roman;\n    }\n    :where(ol[type=\"i\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-roman;\n    }\n    :where(ol[type=\"1\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: decimal;\n    }\n    :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n      font-weight: 400;\n      color: var(--tw-prose-counters);\n    }\n    :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n      color: var(--tw-prose-bullets);\n    }\n    :where(blockquote):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.6em;\n      margin-bottom: 1.6em;\n      padding-inline-start: 1em;\n      font-weight: 500;\n      font-style: italic;\n      color: var(--tw-prose-quotes);\n      border-inline-start-width: 0.25rem;\n      border-inline-start-color: var(--tw-prose-quote-borders);\n      quotes: \"\\201C\"\"\\201D\"\"\\2018\"\"\\2019\";\n    }\n    :where(blockquote p:first-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: open-quote;\n    }\n    :where(blockquote p:last-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: close-quote;\n    }\n    :where(h1):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 800;\n      font-size: var(--text-3xl);\n      margin-top: 0;\n      margin-bottom: 0.8888889em;\n      line-height: 1.1111111;\n    }\n    :where(h1 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 900;\n      color: inherit;\n    }\n    :where(h2):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-size: 1.5em;\n      margin-top: 2em;\n      margin-bottom: 1em;\n      line-height: 1.3333333;\n      font-weight: 600;\n    }\n    :where(h2 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 800;\n      color: inherit;\n    }\n    :where(h3):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      font-size: 1.25em;\n      margin-top: 1.6em;\n      margin-bottom: 0.6em;\n      line-height: 1.6;\n    }\n    :where(h3 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 700;\n      color: inherit;\n    }\n    :where(h4):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      margin-top: 1.5em;\n      margin-bottom: 0.5em;\n      line-height: 1.5;\n    }\n    :where(h4 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 700;\n      color: inherit;\n    }\n    :where(hr + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h2 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h3 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h4 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(picture):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      display: block;\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(picture > img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(video):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(kbd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 0.875em;\n      border-radius: 0.3125rem;\n      padding-top: 0.1875em;\n      padding-inline-end: 0.375em;\n      padding-bottom: 0.1875em;\n      padding-inline-start: 0.375em;\n      font-weight: 500;\n      font-family: inherit;\n      color: var(--tw-prose-kbd);\n      box-shadow: 0 0 0 1px var(--tw-prose-kbd-shadows),0 3px 0 var(--tw-prose-kbd-shadows);\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding: 3px;\n      border: solid 1px;\n      font-size: 13px;\n      border-color: var(--color-fd-border);\n      border-radius: 5px;\n      font-weight: 400;\n      background: var(--color-fd-muted);\n      color: var(--tw-prose-code);\n    }\n    :where(a code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(h1 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n      font-size: var(--text-2xl);\n    }\n    :where(h2 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n      font-size: 0.875em;\n    }\n    :where(h3 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n      font-size: 0.9em;\n    }\n    :where(h4 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(blockquote code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(thead th code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(table):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-size: 0.875em;\n      line-height: 1.7142857;\n      width: 100%;\n      table-layout: auto;\n      margin-top: 2em;\n      margin-bottom: 2em;\n      border-collapse: separate;\n      border-spacing: 0;\n      background: var(--color-fd-card);\n      border-radius: var(--radius-lg);\n      border: 1px solid var(--color-fd-border);\n      overflow: hidden;\n    }\n    :where(thead th):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n    }\n    :where(figure):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(figure > *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(figcaption):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-captions);\n      font-size: 0.875em;\n      line-height: 1.4285714;\n      margin-top: 0.8571429em;\n    }\n    :where(a:not([data-card])):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-links);\n      transition: opacity .2s;\n      font-weight: 500;\n      text-decoration: underline;\n      text-underline-offset: 3.5px;\n      text-decoration-color: var(--color-fd-primary);\n      text-decoration-thickness: 1.5px;\n    }\n    :where(a:not([data-card]):hover):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      opacity: 80%;\n    }\n    --tw-prose-body: color-mix(in srgb, hsl(0, 0%, 3.9%) 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-prose-body: color-mix(in oklab, var(--color-fd-foreground) 90%, transparent);\n    }\n    --tw-prose-headings: var(--color-fd-foreground);\n    --tw-prose-lead: var(--color-fd-foreground);\n    --tw-prose-links: var(--color-fd-foreground);\n    --tw-prose-bold: var(--color-fd-foreground);\n    --tw-prose-counters: var(--color-fd-muted-foreground);\n    --tw-prose-bullets: var(--color-fd-muted-foreground);\n    --tw-prose-hr: var(--color-fd-border);\n    --tw-prose-quotes: var(--color-fd-foreground);\n    --tw-prose-quote-borders: var(--color-fd-border);\n    --tw-prose-captions: var(--color-fd-foreground);\n    --tw-prose-code: var(--color-fd-foreground);\n    --tw-prose-th-borders: var(--color-fd-border);\n    --tw-prose-td-borders: var(--color-fd-border);\n    --tw-prose-kbd: var(--color-fd-foreground);\n    --tw-prose-kbd-shadows: color-mix(in srgb, hsl(0, 0%, 9%) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-prose-kbd-shadows: color-mix(in oklab, var(--color-fd-primary) 50%, transparent);\n    }\n    :where(.prose > :first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(.prose > :last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 0;\n    }\n    :where(th):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      text-align: start;\n      padding: calc(var(--spacing) * 2.5);\n      border-inline-start: 1px solid var(--color-fd-border);\n      background: var(--color-fd-muted);\n    }\n    :where(th:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-inline-start: none;\n    }\n    :where(th:not(tr:last-child *), td:not(tr:last-child *)):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom: 1px solid var(--color-fd-border);\n    }\n    :where(td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      text-align: start;\n      border-inline-start: 1px solid var(--color-fd-border);\n      padding: calc(var(--spacing) * 2.5);\n    }\n    :where(td:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-inline-start: none;\n    }\n    :where(tfoot th, tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-top-width: 1px;\n      border-top-color: var(--tw-prose-th-borders);\n    }\n    :where(thead th, thead td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 1px;\n      border-bottom-color: var(--tw-prose-th-borders);\n    }\n  }\n  .prose-no-margin {\n    & > :first-child {\n      margin-top: 0;\n    }\n    & > :last-child {\n      margin-bottom: 0;\n    }\n  }\n  .mt-\\(--fd-top\\) {\n    margin-top: var(--fd-top);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-px {\n    margin-top: 1px;\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-1\\.5 {\n    margin-bottom: calc(var(--spacing) * 1.5);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-auto {\n    margin-bottom: auto;\n  }\n  .fd-scroll-container {\n    &::-webkit-scrollbar {\n      width: 5px;\n      height: 5px;\n    }\n    &::-webkit-scrollbar-thumb {\n      border-radius: 5px;\n      background: var(--color-fd-border);\n    }\n    &::-webkit-scrollbar-track {\n      background: transparent;\n    }\n    &::-webkit-scrollbar-corner {\n      display: none;\n    }\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .table {\n    display: table;\n  }\n  .\\!size-5\\.5 {\n    width: calc(var(--spacing) * 5.5) !important;\n    height: calc(var(--spacing) * 5.5) !important;\n  }\n  .size-3 {\n    width: calc(var(--spacing) * 3);\n    height: calc(var(--spacing) * 3);\n  }\n  .size-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n    height: calc(var(--spacing) * 3.5);\n  }\n  .size-4 {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n  .size-4\\.5 {\n    width: calc(var(--spacing) * 4.5);\n    height: calc(var(--spacing) * 4.5);\n  }\n  .size-5 {\n    width: calc(var(--spacing) * 5);\n    height: calc(var(--spacing) * 5);\n  }\n  .size-6 {\n    width: calc(var(--spacing) * 6);\n    height: calc(var(--spacing) * 6);\n  }\n  .size-6\\.5 {\n    width: calc(var(--spacing) * 6.5);\n    height: calc(var(--spacing) * 6.5);\n  }\n  .size-9 {\n    width: calc(var(--spacing) * 9);\n    height: calc(var(--spacing) * 9);\n  }\n  .size-full {\n    width: 100%;\n    height: 100%;\n  }\n  .h-\\(--fd-animated-height\\) {\n    height: var(--fd-animated-height);\n  }\n  .h-\\(--fd-height\\) {\n    height: var(--fd-height);\n  }\n  .h-\\(--fd-nav-height\\) {\n    height: var(--fd-nav-height);\n  }\n  .h-\\(--fd-tocnav-height\\) {\n    height: var(--fd-tocnav-height);\n  }\n  .h-\\(--radix-navigation-menu-viewport-height\\) {\n    height: var(--radix-navigation-menu-viewport-height);\n  }\n  .h-1\\.5 {\n    height: calc(var(--spacing) * 1.5);\n  }\n  .h-9\\.5 {\n    height: calc(var(--spacing) * 9.5);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-14 {\n    height: calc(var(--spacing) * 14);\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-px {\n    height: 1px;\n  }\n  .max-h-\\[50vh\\] {\n    max-height: 50vh;\n  }\n  .max-h-\\[80svh\\] {\n    max-height: 80svh;\n  }\n  .max-h-\\[400px\\] {\n    max-height: 400px;\n  }\n  .max-h-\\[460px\\] {\n    max-height: 460px;\n  }\n  .max-h-\\[600px\\] {\n    max-height: 600px;\n  }\n  .min-h-0 {\n    min-height: calc(var(--spacing) * 0);\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .w-\\(--fd-toc-width\\) {\n    width: var(--fd-toc-width);\n  }\n  .w-\\(--radix-popover-trigger-width\\) {\n    width: var(--radix-popover-trigger-width);\n  }\n  .w-0 {\n    width: calc(var(--spacing) * 0);\n  }\n  .w-0\\.5 {\n    width: calc(var(--spacing) * 0.5);\n  }\n  .w-1\\.5 {\n    width: calc(var(--spacing) * 1.5);\n  }\n  .w-1\\/4 {\n    width: calc(1/4 * 100%);\n  }\n  .w-\\[30\\%\\] {\n    width: 30%;\n  }\n  .w-\\[45\\%\\] {\n    width: 45%;\n  }\n  .w-\\[85\\%\\] {\n    width: 85%;\n  }\n  .w-\\[calc\\(100\\%-1rem\\)\\] {\n    width: calc(100% - 1rem);\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .w-max {\n    width: max-content;\n  }\n  .w-px {\n    width: 1px;\n  }\n  .max-w-\\(--fd-page-width\\) {\n    max-width: var(--fd-page-width);\n  }\n  .max-w-\\[98vw\\] {\n    max-width: 98vw;\n  }\n  .max-w-\\[240px\\] {\n    max-width: 240px;\n  }\n  .max-w-\\[380px\\] {\n    max-width: 380px;\n  }\n  .max-w-\\[400px\\] {\n    max-width: 400px;\n  }\n  .max-w-full {\n    max-width: 100%;\n  }\n  .max-w-screen-sm {\n    max-width: var(--breakpoint-sm);\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-\\[220px\\] {\n    min-width: 220px;\n  }\n  .min-w-\\[240px\\] {\n    min-width: 240px;\n  }\n  .min-w-full {\n    min-width: 100%;\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .origin-\\(--radix-popover-content-transform-origin\\) {\n    transform-origin: var(--radix-popover-content-transform-origin);\n  }\n  .origin-\\[top_center\\] {\n    transform-origin: top center;\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-\\(--fd-sidebar-offset\\) {\n    --tw-translate-x: var(--fd-sidebar-offset);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-full {\n    --tw-translate-y: -100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-full {\n    --tw-translate-y: 100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-rotate-90 {\n    rotate: calc(90deg * -1);\n  }\n  .rotate-180 {\n    rotate: 180deg;\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .scroll-m-24 {\n    scroll-margin: calc(var(--spacing) * 24);\n  }\n  .scroll-m-28 {\n    scroll-margin: calc(var(--spacing) * 28);\n  }\n  .list-none {\n    list-style-type: none;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-row {\n    flex-direction: row;\n  }\n  .flex-row-reverse {\n    flex-direction: row-reverse;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .gap-0\\.5 {\n    gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-2\\.5 {\n    gap: calc(var(--spacing) * 2.5);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-3\\.5 {\n    gap: calc(var(--spacing) * 3.5);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .divide-y {\n    :where(& > :not(:last-child)) {\n      --tw-divide-y-reverse: 0;\n      border-bottom-style: var(--tw-border-style);\n      border-top-style: var(--tw-border-style);\n      border-top-width: calc(1px * var(--tw-divide-y-reverse));\n      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n    }\n  }\n  .divide-fd-border {\n    :where(& > :not(:last-child)) {\n      border-color: var(--color-fd-border);\n    }\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-auto {\n    overflow: auto;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .rounded-2xl {\n    border-radius: var(--radius-2xl);\n  }\n  .rounded-\\[inherit\\] {\n    border-radius: inherit;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-md {\n    border-radius: var(--radius-md);\n  }\n  .rounded-sm {\n    border-radius: var(--radius-sm);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .rounded-bl-lg {\n    border-bottom-left-radius: var(--radius-lg);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-s {\n    border-inline-start-style: var(--tw-border-style);\n    border-inline-start-width: 1px;\n  }\n  .border-e {\n    border-inline-end-style: var(--tw-border-style);\n    border-inline-end-width: 1px;\n  }\n  .border-e-0 {\n    border-inline-end-style: var(--tw-border-style);\n    border-inline-end-width: 0px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-l {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 1px;\n  }\n  .border-fd-foreground\\/10 {\n    border-color: color-mix(in srgb, hsl(0, 0%, 3.9%) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-fd-foreground) 10%, transparent);\n    }\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .bg-\\(--callout-color\\)\\/50 {\n    background-color: var(--callout-color);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--callout-color) 50%, transparent);\n    }\n  }\n  .bg-\\(--shiki-light-bg\\) {\n    background-color: var(--shiki-light-bg);\n  }\n  .bg-fd-accent {\n    background-color: var(--color-fd-accent);\n  }\n  .bg-fd-background {\n    background-color: var(--color-fd-background);\n  }\n  .bg-fd-background\\/80 {\n    background-color: color-mix(in srgb, hsl(0, 0%, 96%) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-fd-background) 80%, transparent);\n    }\n  }\n  .bg-fd-border {\n    background-color: var(--color-fd-border);\n  }\n  .bg-fd-card {\n    background-color: var(--color-fd-card);\n  }\n  .bg-fd-foreground\\/10 {\n    background-color: color-mix(in srgb, hsl(0, 0%, 3.9%) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-fd-foreground) 10%, transparent);\n    }\n  }\n  .bg-fd-muted {\n    background-color: var(--color-fd-muted);\n  }\n  .bg-fd-popover\\/60 {\n    background-color: color-mix(in srgb, hsl(0, 0%, 98%) 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-fd-popover) 60%, transparent);\n    }\n  }\n  .bg-fd-popover\\/80 {\n    background-color: color-mix(in srgb, hsl(0, 0%, 98%) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-fd-popover) 80%, transparent);\n    }\n  }\n  .bg-fd-primary {\n    background-color: var(--color-fd-primary);\n  }\n  .bg-fd-primary\\/10 {\n    background-color: color-mix(in srgb, hsl(0, 0%, 9%) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-fd-primary) 10%, transparent);\n    }\n  }\n  .bg-fd-secondary {\n    background-color: var(--color-fd-secondary);\n  }\n  .bg-fd-secondary\\/50 {\n    background-color: color-mix(in srgb, hsl(0, 0%, 93.1%) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-fd-secondary) 50%, transparent);\n    }\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .\\[mask-image\\:linear-gradient\\(to_bottom\\,transparent\\,white_16px\\,white_calc\\(100\\%-16px\\)\\,transparent\\)\\] {\n    mask-image: linear-gradient(to bottom,transparent,white 16px,white calc(100% - 16px),transparent);\n  }\n  .fill-\\(--callout-color\\) {\n    fill: var(--callout-color);\n  }\n  .stroke-current\\/25 {\n    stroke: currentcolor;\n    @supports (color: color-mix(in lab, red, red)) {\n      stroke: color-mix(in oklab, currentcolor 25%, transparent);\n    }\n  }\n  .stroke-fd-foreground\\/10 {\n    stroke: color-mix(in srgb, hsl(0, 0%, 3.9%) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      stroke: color-mix(in oklab, var(--color-fd-foreground) 10%, transparent);\n    }\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-0\\.5 {\n    padding: calc(var(--spacing) * 0.5);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-1\\.5 {\n    padding: calc(var(--spacing) * 1.5);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .px-\\(--fd-layout-offset\\) {\n    padding-inline: var(--fd-layout-offset);\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-1\\.5 {\n    padding-inline: calc(var(--spacing) * 1.5);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-2\\.5 {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-2\\.5 {\n    padding-block: calc(var(--spacing) * 2.5);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-3\\.5 {\n    padding-block: calc(var(--spacing) * 3.5);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .ps-\\(--sidebar-item-offset\\) {\n    padding-inline-start: var(--sidebar-item-offset);\n  }\n  .ps-1 {\n    padding-inline-start: calc(var(--spacing) * 1);\n  }\n  .ps-2 {\n    padding-inline-start: calc(var(--spacing) * 2);\n  }\n  .ps-2\\.5 {\n    padding-inline-start: calc(var(--spacing) * 2.5);\n  }\n  .ps-3 {\n    padding-inline-start: calc(var(--spacing) * 3);\n  }\n  .ps-4 {\n    padding-inline-start: calc(var(--spacing) * 4);\n  }\n  .ps-6 {\n    padding-inline-start: calc(var(--spacing) * 6);\n  }\n  .ps-8 {\n    padding-inline-start: calc(var(--spacing) * 8);\n  }\n  .ps-\\[calc\\(var\\(--fd-layout-offset\\)\\+var\\(--fd-sidebar-width\\)\\)\\] {\n    padding-inline-start: calc(var(--fd-layout-offset) + var(--fd-sidebar-width));\n  }\n  .pe-2\\.5 {\n    padding-inline-end: calc(var(--spacing) * 2.5);\n  }\n  .pe-4 {\n    padding-inline-end: calc(var(--spacing) * 4);\n  }\n  .pt-\\(--fd-nav-height\\) {\n    padding-top: var(--fd-nav-height);\n  }\n  .pt-\\(--fd-tocnav-height\\) {\n    padding-top: var(--fd-tocnav-height);\n  }\n  .pt-0 {\n    padding-top: calc(var(--spacing) * 0);\n  }\n  .pt-8 {\n    padding-top: calc(var(--spacing) * 8);\n  }\n  .pt-12 {\n    padding-top: calc(var(--spacing) * 12);\n  }\n  .pt-14 {\n    padding-top: calc(var(--spacing) * 14);\n  }\n  .pb-2 {\n    padding-bottom: calc(var(--spacing) * 2);\n  }\n  .pb-6 {\n    padding-bottom: calc(var(--spacing) * 6);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-end {\n    text-align: end;\n  }\n  .text-start {\n    text-align: start;\n  }\n  .font-mono {\n    font-family: var(--font-mono);\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .text-\\[13px\\] {\n    font-size: 13px;\n  }\n  .text-\\[15px\\] {\n    font-size: 15px;\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .text-nowrap {\n    text-wrap: nowrap;\n  }\n  .\\[overflow-wrap\\:anywhere\\] {\n    overflow-wrap: anywhere;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .text-fd-accent-foreground {\n    color: var(--color-fd-accent-foreground);\n  }\n  .text-fd-card {\n    color: var(--color-fd-card);\n  }\n  .text-fd-card-foreground {\n    color: var(--color-fd-card-foreground);\n  }\n  .text-fd-foreground {\n    color: var(--color-fd-foreground);\n  }\n  .text-fd-foreground\\/80 {\n    color: color-mix(in srgb, hsl(0, 0%, 3.9%) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-fd-foreground) 80%, transparent);\n    }\n  }\n  .text-fd-muted-foreground {\n    color: var(--color-fd-muted-foreground);\n  }\n  .text-fd-muted-foreground\\/50 {\n    color: color-mix(in srgb, hsl(0, 0%, 45.1%) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-fd-muted-foreground) 50%, transparent);\n    }\n  }\n  .text-fd-popover-foreground {\n    color: var(--color-fd-popover-foreground);\n  }\n  .text-fd-popover-foreground\\/80 {\n    color: color-mix(in srgb, hsl(0, 0%, 15.1%) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-fd-popover-foreground) 80%, transparent);\n    }\n  }\n  .text-fd-primary {\n    color: var(--color-fd-primary);\n  }\n  .text-fd-primary-foreground {\n    color: var(--color-fd-primary-foreground);\n  }\n  .text-fd-primary\\/50 {\n    color: color-mix(in srgb, hsl(0, 0%, 9%) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-fd-primary) 50%, transparent);\n    }\n  }\n  .text-fd-secondary-foreground {\n    color: var(--color-fd-secondary-foreground);\n  }\n  .italic {\n    font-style: italic;\n  }\n  .line-through {\n    text-decoration-line: line-through;\n  }\n  .underline {\n    text-decoration-line: underline;\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .shadow-2xl {\n    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-black\\/50 {\n    --tw-shadow-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-black) 50%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur-lg {\n    --tw-backdrop-blur: blur(var(--blur-lg));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-xl {\n    --tw-backdrop-blur: blur(var(--blur-xl));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-xs {\n    --tw-backdrop-blur: blur(var(--blur-xs));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[height\\] {\n    transition-property: height;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[padding\\] {\n    transition-property: padding;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\,height\\] {\n    transition-property: width,height;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-100 {\n    --tw-duration: 100ms;\n    transition-duration: 100ms;\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .duration-400 {\n    --tw-duration: 400ms;\n    transition-duration: 400ms;\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .\\[--fd-nav-height\\:0px\\] {\n    --fd-nav-height: 0px;\n  }\n  .\\[--fd-nav-height\\:56px\\] {\n    --fd-nav-height: 56px;\n  }\n  .\\[scrollbar-width\\:none\\] {\n    scrollbar-width: none;\n  }\n  .\\*\\:col-start-1 {\n    :is(& > *) {\n      grid-column-start: 1;\n    }\n  }\n  .\\*\\:row-start-1 {\n    :is(& > *) {\n      grid-row-start: 1;\n    }\n  }\n  .\\*\\:mx-auto {\n    :is(& > *) {\n      margin-inline: auto;\n    }\n  }\n  .\\*\\:my-auto {\n    :is(& > *) {\n      margin-block: auto;\n    }\n  }\n  .\\*\\:flex {\n    :is(& > *) {\n      display: flex;\n    }\n  }\n  .\\*\\:w-\\(--fd-sidebar-width\\) {\n    :is(& > *) {\n      width: var(--fd-sidebar-width);\n    }\n  }\n  .\\*\\:max-w-fd-container {\n    :is(& > *) {\n      max-width: var(--spacing-fd-container);\n    }\n  }\n  .\\*\\:flex-col {\n    :is(& > *) {\n      flex-direction: column;\n    }\n  }\n  .\\*\\:border-b {\n    :is(& > *) {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .group-data-\\[state\\=active\\]\\:bg-fd-primary {\n    &:is(:where(.group)[data-state=\"active\"] *) {\n      background-color: var(--color-fd-primary);\n    }\n  }\n  .group-data-\\[state\\=open\\]\\:rotate-90 {\n    &:is(:where(.group)[data-state=\"open\"] *) {\n      rotate: 90deg;\n    }\n  }\n  .group-data-\\[state\\=open\\]\\:rotate-180 {\n    &:is(:where(.group)[data-state=\"open\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .peer-hover\\:opacity-100 {\n    &:is(:where(.peer):hover ~ *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .placeholder\\:text-fd-muted-foreground {\n    &::placeholder {\n      color: var(--color-fd-muted-foreground);\n    }\n  }\n  .first\\:ms-1 {\n    &:first-child {\n      margin-inline-start: calc(var(--spacing) * 1);\n    }\n  }\n  .first\\:pt-0 {\n    &:first-child {\n      padding-top: calc(var(--spacing) * 0);\n    }\n  }\n  .last\\:pb-0 {\n    &:last-child {\n      padding-bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .\\*\\:last\\:border-b-0 {\n    :is(& > *) {\n      &:last-child {\n        border-bottom-style: var(--tw-border-style);\n        border-bottom-width: 0px;\n      }\n    }\n  }\n  .empty\\:mb-0 {\n    &:empty {\n      margin-bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .empty\\:hidden {\n    &:empty {\n      display: none;\n    }\n  }\n  .hover\\:bg-fd-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-fd-accent);\n      }\n    }\n  }\n  .hover\\:bg-fd-accent\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, hsla(0, 0%, 82%, 50%) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-fd-accent) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-fd-accent\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, hsla(0, 0%, 82%, 50%) 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-fd-accent) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-fd-primary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, hsl(0, 0%, 9%) 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-fd-primary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:text-fd-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-fd-accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-fd-accent-foreground\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        color: color-mix(in srgb, hsl(0, 0%, 9%) 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--color-fd-accent-foreground) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:text-fd-popover-foreground\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        color: color-mix(in srgb, hsl(0, 0%, 15.1%) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--color-fd-popover-foreground) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:opacity-80 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 80%;\n      }\n    }\n  }\n  .hover\\:transition-none {\n    &:hover {\n      @media (hover: hover) {\n        transition-property: none;\n      }\n    }\n  }\n  .focus-visible\\:outline-none {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .has-focus-visible\\:bg-fd-accent {\n    &:has(*:focus-visible) {\n      background-color: var(--color-fd-accent);\n    }\n  }\n  .\\*\\:has-\\[\\+\\:last-child\\[data-empty\\=true\\]\\]\\:border-b-0 {\n    :is(& > *) {\n      &:has(+:last-child[data-empty=true]) {\n        border-bottom-style: var(--tw-border-style);\n        border-bottom-width: 0px;\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:font-medium {\n    &[data-active=\"true\"] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-fd-primary {\n    &[data-active=\"true\"] {\n      color: var(--color-fd-primary);\n    }\n  }\n  .\\*\\*\\:data-\\[active\\=true\\]\\:before\\:absolute {\n    :is(& *) {\n      &[data-active=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          position: absolute;\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[active\\=true\\]\\:before\\:inset-y-2\\.5 {\n    :is(& *) {\n      &[data-active=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          inset-block: calc(var(--spacing) * 2.5);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[active\\=true\\]\\:before\\:start-2\\.5 {\n    :is(& *) {\n      &[data-active=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          inset-inline-start: calc(var(--spacing) * 2.5);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[active\\=true\\]\\:before\\:w-px {\n    :is(& *) {\n      &[data-active=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          width: 1px;\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[active\\=true\\]\\:before\\:bg-fd-primary {\n    :is(& *) {\n      &[data-active=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          background-color: var(--color-fd-primary);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[active\\=true\\]\\:before\\:content-\\[\\'\\'\\] {\n    :is(& *) {\n      &[data-active=\"true\"] {\n        &::before {\n          content: var(--tw-content);\n          --tw-content: '';\n          content: var(--tw-content);\n        }\n      }\n    }\n  }\n  .data-\\[collapsed\\=false\\]\\:hidden {\n    &[data-collapsed=\"false\"] {\n      display: none;\n    }\n  }\n  .\\*\\:data-\\[empty\\=true\\]\\:border-b-0 {\n    :is(& > *) {\n      &[data-empty=\"true\"] {\n        border-bottom-style: var(--tw-border-style);\n        border-bottom-width: 0px;\n      }\n    }\n  }\n  .data-\\[motion\\=from-end\\]\\:animate-fd-enterFromRight {\n    &[data-motion=\"from-end\"] {\n      animation: var(--animate-fd-enterFromRight);\n    }\n  }\n  .data-\\[motion\\=from-start\\]\\:animate-fd-enterFromLeft {\n    &[data-motion=\"from-start\"] {\n      animation: var(--animate-fd-enterFromLeft);\n    }\n  }\n  .data-\\[motion\\=to-end\\]\\:animate-fd-exitToRight {\n    &[data-motion=\"to-end\"] {\n      animation: var(--animate-fd-exitToRight);\n    }\n  }\n  .data-\\[motion\\=to-start\\]\\:animate-fd-exitToLeft {\n    &[data-motion=\"to-start\"] {\n      animation: var(--animate-fd-exitToLeft);\n    }\n  }\n  .data-\\[state\\=active\\]\\:border-fd-primary {\n    &[data-state=\"active\"] {\n      border-color: var(--color-fd-primary);\n    }\n  }\n  .data-\\[state\\=active\\]\\:text-fd-primary {\n    &[data-state=\"active\"] {\n      color: var(--color-fd-primary);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-fd-accordion-up {\n    &[data-state=\"closed\"] {\n      animation: var(--animate-fd-accordion-up);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-fd-collapsible-up {\n    &[data-state=\"closed\"] {\n      animation: var(--animate-fd-collapsible-up);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-fd-dialog-out {\n    &[data-state=\"closed\"] {\n      animation: var(--animate-fd-dialog-out);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-fd-fade-out {\n    &[data-state=\"closed\"] {\n      animation: var(--animate-fd-fade-out);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-fd-nav-menu-out {\n    &[data-state=\"closed\"] {\n      animation: var(--animate-fd-nav-menu-out);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-fd-popover-out {\n    &[data-state=\"closed\"] {\n      animation: var(--animate-fd-popover-out);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-fd-sidebar-out {\n    &[data-state=\"closed\"] {\n      animation: var(--animate-fd-sidebar-out);\n    }\n  }\n  .data-\\[state\\=hidden\\]\\:animate-fd-fade-out {\n    &[data-state=\"hidden\"] {\n      animation: var(--animate-fd-fade-out);\n    }\n  }\n  .data-\\[state\\=inactive\\]\\:hidden {\n    &[data-state=\"inactive\"] {\n      display: none;\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-fd-accordion-down {\n    &[data-state=\"open\"] {\n      animation: var(--animate-fd-accordion-down);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-fd-collapsible-down {\n    &[data-state=\"open\"] {\n      animation: var(--animate-fd-collapsible-down);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-fd-dialog-in {\n    &[data-state=\"open\"] {\n      animation: var(--animate-fd-dialog-in);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-fd-fade-in {\n    &[data-state=\"open\"] {\n      animation: var(--animate-fd-fade-in);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-fd-nav-menu-in {\n    &[data-state=\"open\"] {\n      animation: var(--animate-fd-nav-menu-in);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-fd-popover-in {\n    &[data-state=\"open\"] {\n      animation: var(--animate-fd-popover-in);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-fd-sidebar-in {\n    &[data-state=\"open\"] {\n      animation: var(--animate-fd-sidebar-in);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-fd-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--color-fd-accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-fd-accent\\/50 {\n    &[data-state=\"open\"] {\n      background-color: color-mix(in srgb, hsla(0, 0%, 82%, 50%) 50%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-fd-accent) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-fd-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--color-fd-accent-foreground);\n    }\n  }\n  .max-xl\\:end-4 {\n    @media (width < 80rem) {\n      inset-inline-end: calc(var(--spacing) * 4);\n    }\n  }\n  .max-xl\\:hidden {\n    @media (width < 80rem) {\n      display: none;\n    }\n  }\n  .max-lg\\:hidden {\n    @media (width < 64rem) {\n      display: none;\n    }\n  }\n  .max-lg\\:rounded-b-2xl {\n    @media (width < 64rem) {\n      border-bottom-right-radius: var(--radius-2xl);\n      border-bottom-left-radius: var(--radius-2xl);\n    }\n  }\n  .max-lg\\:shadow-lg {\n    @media (width < 64rem) {\n      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .max-md\\:hidden {\n    @media (width < 48rem) {\n      display: none;\n    }\n  }\n  .max-md\\:rounded-md {\n    @media (width < 48rem) {\n      border-radius: var(--radius-md);\n    }\n  }\n  .max-md\\:border {\n    @media (width < 48rem) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .max-md\\:bg-fd-secondary {\n    @media (width < 48rem) {\n      background-color: var(--color-fd-secondary);\n    }\n  }\n  .max-md\\:p-1\\.5 {\n    @media (width < 48rem) {\n      padding: calc(var(--spacing) * 1.5);\n    }\n  }\n  .max-md\\:backdrop-blur-xs {\n    @media (width < 48rem) {\n      --tw-backdrop-blur: blur(var(--blur-xs));\n      -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n      backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    }\n  }\n  .max-sm\\:mt-2 {\n    @media (width < 40rem) {\n      margin-top: calc(var(--spacing) * 2);\n    }\n  }\n  .max-sm\\:hidden {\n    @media (width < 40rem) {\n      display: none;\n    }\n  }\n  .sm\\:hidden {\n    @media (width >= 40rem) {\n      display: none;\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:items-center {\n    @media (width >= 40rem) {\n      align-items: center;\n    }\n  }\n  .sm\\:justify-end {\n    @media (width >= 40rem) {\n      justify-content: flex-end;\n    }\n  }\n  .md\\:top-\\[calc\\(50\\%-250px\\)\\] {\n    @media (width >= 48rem) {\n      top: calc(50% - 250px);\n    }\n  }\n  .md\\:mx-auto {\n    @media (width >= 48rem) {\n      margin-inline: auto;\n    }\n  }\n  .md\\:mt-1 {\n    @media (width >= 48rem) {\n      margin-top: calc(var(--spacing) * 1);\n    }\n  }\n  .md\\:mb-auto {\n    @media (width >= 48rem) {\n      margin-bottom: auto;\n    }\n  }\n  .md\\:hidden {\n    @media (width >= 48rem) {\n      display: none;\n    }\n  }\n  .md\\:size-5 {\n    @media (width >= 48rem) {\n      width: calc(var(--spacing) * 5);\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:gap-2 {\n    @media (width >= 48rem) {\n      gap: calc(var(--spacing) * 2);\n    }\n  }\n  .md\\:px-4 {\n    @media (width >= 48rem) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .md\\:px-6 {\n    @media (width >= 48rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:\\[--fd-nav-height\\:0px\\] {\n    @media (width >= 48rem) {\n      --fd-nav-height: 0px;\n    }\n  }\n  .md\\:\\[--fd-nav-height\\:64px\\] {\n    @media (width >= 48rem) {\n      --fd-nav-height: 64px;\n    }\n  }\n  .md\\:\\[--fd-sidebar-width\\:268px\\] {\n    @media (width >= 48rem) {\n      --fd-sidebar-width: 268px;\n    }\n  }\n  .md\\:\\[--fd-sidebar-width\\:286px\\] {\n    @media (width >= 48rem) {\n      --fd-sidebar-width: 286px;\n    }\n  }\n  .lg\\:hidden {\n    @media (width >= 64rem) {\n      display: none;\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:flex-row {\n    @media (width >= 64rem) {\n      flex-direction: row;\n    }\n  }\n  .lg\\:items-center {\n    @media (width >= 64rem) {\n      align-items: center;\n    }\n  }\n  .lg\\:\\[--fd-nav-height\\:104px\\] {\n    @media (width >= 64rem) {\n      --fd-nav-height: 104px;\n    }\n  }\n  .lg\\:\\[--fd-sidebar-width\\:286px\\] {\n    @media (width >= 64rem) {\n      --fd-sidebar-width: 286px;\n    }\n  }\n  .xl\\:start-4 {\n    @media (width >= 80rem) {\n      inset-inline-start: calc(var(--spacing) * 4);\n    }\n  }\n  .xl\\:hidden {\n    @media (width >= 80rem) {\n      display: none;\n    }\n  }\n  .xl\\:px-12 {\n    @media (width >= 80rem) {\n      padding-inline: calc(var(--spacing) * 12);\n    }\n  }\n  .xl\\:pt-12 {\n    @media (width >= 80rem) {\n      padding-top: calc(var(--spacing) * 12);\n    }\n  }\n  .xl\\:\\[--fd-toc-width\\:286px\\] {\n    @media (width >= 80rem) {\n      --fd-toc-width: 286px;\n    }\n  }\n  .\\@max-lg\\:col-span-full {\n    @container (width < 32rem) {\n      grid-column: 1 / -1;\n    }\n  }\n  .rtl\\:-translate-x-\\(--fd-sidebar-offset\\) {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      --tw-translate-x: calc(var(--fd-sidebar-offset) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .rtl\\:-scale-x-100 {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      --tw-scale-x: calc(100% * -1);\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .rtl\\:rotate-180 {\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .dark\\:bg-\\(--shiki-dark-bg\\) {\n    &:where(.dark, .dark *) {\n      background-color: var(--shiki-dark-bg);\n    }\n  }\n  .\\[\\&_svg\\]\\:size-3\\.5 {\n    & svg {\n      width: calc(var(--spacing) * 3.5);\n      height: calc(var(--spacing) * 3.5);\n    }\n  }\n  .\\[\\&_svg\\]\\:size-4 {\n    & svg {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\]\\:size-4\\.5 {\n    & svg {\n      width: calc(var(--spacing) * 4.5);\n      height: calc(var(--spacing) * 4.5);\n    }\n  }\n  .\\[\\&_svg\\]\\:size-5 {\n    & svg {\n      width: calc(var(--spacing) * 5);\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_svg\\]\\:size-full {\n    & svg {\n      width: 100%;\n      height: 100%;\n    }\n  }\n  .\\[\\&_svg\\]\\:shrink-0 {\n    & svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&\\>figure\\:only-child\\]\\:-m-4 {\n    &>figure:only-child {\n      margin: calc(var(--spacing) * -4);\n    }\n  }\n  .\\[\\&\\>figure\\:only-child\\]\\:border-none {\n    &>figure:only-child {\n      --tw-border-style: none;\n      border-style: none;\n    }\n  }\n}\n.dark {\n  --color-fd-background: hsl(0, 0%, 7.04%);\n  --color-fd-foreground: hsl(0, 0%, 92%);\n  --color-fd-muted: hsl(0, 0%, 12.9%);\n  --color-fd-muted-foreground: hsla(0, 0%, 70%, 0.8);\n  --color-fd-popover: hsl(0, 0%, 11.6%);\n  --color-fd-popover-foreground: hsl(0, 0%, 86.9%);\n  --color-fd-card: hsl(0, 0%, 9.8%);\n  --color-fd-card-foreground: hsl(0, 0%, 98%);\n  --color-fd-border: hsla(0, 0%, 40%, 20%);\n  --color-fd-primary: hsl(0, 0%, 98%);\n  --color-fd-primary-foreground: hsl(0, 0%, 9%);\n  --color-fd-secondary: hsl(0, 0%, 12.9%);\n  --color-fd-secondary-foreground: hsl(0, 0%, 92%);\n  --color-fd-accent: hsla(0, 0%, 40.9%, 30%);\n  --color-fd-accent-foreground: hsl(0, 0%, 90%);\n  --color-fd-ring: hsl(0, 0%, 54.9%);\n}\n.dark #nd-sidebar {\n  --color-fd-muted: hsl(0, 0%, 16%);\n  --color-fd-secondary: hsl(0, 0%, 18%);\n  --color-fd-muted-foreground: hsl(0, 0%, 72%);\n}\n.shiki:not(.not-fumadocs-codeblock *) {\n  --padding-left: calc(var(--spacing) * 3);\n  --padding-right: calc(var(--spacing) * 3);\n  code span {\n    color: var(--shiki-light);\n  }\n  code .line {\n    position: relative;\n    min-height: 1lh;\n    padding-left: var(--padding-left);\n    padding-right: var(--padding-right);\n  }\n  &.has-focused code .line:not(.focused) {\n    filter: blur(2px);\n    transition: filter 200ms;\n  }\n  &.has-focused:hover code .line:not(.focused) {\n    filter: blur(0);\n  }\n  &[data-line-numbers] code .twoslash-meta-line {\n    padding-left: calc(var(--padding-left) + 7 * var(--spacing));\n  }\n  &[data-line-numbers] code .line {\n    counter-increment: line;\n    padding-left: calc(var(--padding-left) + 7 * var(--spacing));\n    &::after {\n      position: absolute;\n      content: counter(line);\n      color: var(--fd-counter-color, hsl(0, 0%, 45.1%));\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix( in oklab, var(--fd-counter-color, var(--color-fd-muted-foreground)) 60%, transparent );\n      }\n      top: calc(var(--spacing) * 0);\n      left: calc(var(--spacing) * 4);\n    }\n  }\n  code .diff::before {\n    position: absolute;\n    left: var(--spacing);\n  }\n  code .diff.remove {\n    opacity: 0.7;\n    --fd-counter-color: var(--color-fd-diff-remove-symbol);\n    background-color: var(--color-fd-diff-remove);\n  }\n  code .diff.remove::before {\n    content: '-';\n    color: var(--color-fd-diff-remove-symbol);\n  }\n  code .diff.add {\n    --fd-counter-color: var(--color-fd-diff-add-symbol);\n    background-color: var(--color-fd-diff-add);\n  }\n  code .diff.add::before {\n    content: '+';\n    color: var(--color-fd-diff-add-symbol);\n  }\n  code .highlighted {\n    --fd-counter-color: var(--color-fd-primary);\n    background-color: color-mix(in srgb, hsl(0, 0%, 9%) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-fd-primary) 10%, transparent);\n    }\n  }\n  code .highlighted-word {\n    padding: 1px;\n    margin-block: -1px;\n    border-radius: var(--radius-md);\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n    border-color: color-mix(in srgb, hsl(0, 0%, 9%) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-fd-primary) 30%, transparent);\n    }\n    background-color: color-mix(in srgb, hsl(0, 0%, 9%) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-fd-primary) 10%, transparent);\n    }\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n}\n.dark .shiki:not(.not-fumadocs-codeblock *) {\n  code span {\n    color: var(--shiki-dark);\n  }\n}\n[dir='rtl'] {\n  --fd-sidebar-mobile-offset: -100%;\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    border-color: var(--color-fd-border, currentColor);\n  }\n  body {\n    background-color: var(--color-fd-background);\n    color: var(--color-fd-foreground);\n  }\n  [data-rmiz-modal-overlay='visible'] {\n    background-color: var(--color-fd-background);\n  }\n  :root, #nd-docs-layout {\n    --fd-layout-offset: max(calc(50vw - var(--fd-layout-width) / 2), 0px);\n  }\n}\n@media (width < 80rem) {\n  #nd-docs-layout:has([data-toc-popover]) {\n    --fd-tocnav-height: calc(var(--spacing) * 10);\n  }\n}\n@property --radix-collapsible-content-height {\n  syntax: '<length>';\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-divide-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-content {\n  syntax: \"*\";\n  initial-value: \"\";\n  inherits: false;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@keyframes fd-sidebar-in {\n  from {\n    transform: translateX(var(--fd-sidebar-mobile-offset));\n  }\n}\n@keyframes fd-sidebar-out {\n  to {\n    transform: translateX(var(--fd-sidebar-mobile-offset));\n  }\n}\n@keyframes fd-collapsible-down {\n  from {\n    height: 0;\n    opacity: 0;\n  }\n  to {\n    height: var(--radix-collapsible-content-height);\n  }\n}\n@keyframes fd-collapsible-up {\n  from {\n    height: var(--radix-collapsible-content-height);\n  }\n  to {\n    height: 0;\n    opacity: 0;\n  }\n}\n@keyframes fd-accordion-down {\n  from {\n    height: 0;\n    opacity: 0.5;\n  }\n  to {\n    height: var(--radix-accordion-content-height);\n  }\n}\n@keyframes fd-accordion-up {\n  from {\n    height: var(--radix-accordion-content-height);\n  }\n  to {\n    height: 0;\n    opacity: 0.5;\n  }\n}\n@keyframes fd-dialog-in {\n  from {\n    transform: scale(1.06);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n  }\n}\n@keyframes fd-dialog-out {\n  from {\n    transform: scale(1);\n  }\n  to {\n    transform: scale(1.04);\n    opacity: 0;\n  }\n}\n@keyframes fd-popover-in {\n  from {\n    opacity: 0;\n    transform: scale(0.7);\n  }\n}\n@keyframes fd-popover-out {\n  to {\n    opacity: 0;\n    transform: scale(0.7);\n  }\n}\n@keyframes fd-fade-in {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n@keyframes fd-fade-out {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n@keyframes fd-enterFromRight {\n  from {\n    opacity: 0;\n    transform: translateX(200px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n@keyframes fd-enterFromLeft {\n  from {\n    opacity: 0;\n    transform: translateX(-200px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n@keyframes fd-exitToRight {\n  from {\n    opacity: 1;\n    transform: translateX(0);\n  }\n  to {\n    opacity: 0;\n    transform: translateX(200px);\n  }\n}\n@keyframes fd-exitToLeft {\n  from {\n    opacity: 1;\n    transform: translateX(0);\n  }\n  to {\n    opacity: 0;\n    transform: translateX(-200px);\n  }\n}\n@keyframes fd-nav-menu-in {\n  from {\n    opacity: 0;\n    height: 0px;\n  }\n  to {\n    opacity: 1;\n    height: var(--radix-navigation-menu-viewport-height);\n  }\n}\n@keyframes fd-nav-menu-out {\n  from {\n    opacity: 1;\n    height: var(--radix-navigation-menu-viewport-height);\n  }\n  to {\n    opacity: 0;\n    height: 0px;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-border-style: solid;\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-divide-y-reverse: 0;\n      --tw-font-weight: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-content: \"\";\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --radix-collapsible-content-height: 0px;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EAk6FE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAl6FJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;EAAA;IAAA;;;;;;;;;AAFF;EA6FE;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAMA;;;;;;;;;;;EASA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAAA;;;;;;;;;;;EAAA;;;;;;;;;;;EAUA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAGA;;;;EAAA;;;;EAGA;;;;EAuxEA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;AA7gFF;;AAAA;EAiPE;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;;;;;;;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAiBF;;;;;;;;;EAOE;IAAyB;;;;IAGA;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;;;;EAGE;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;;;;EAKE;;;;;;;;EAOA;;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAMA;;;;;EAIA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAIA;;;;;;;EAMA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;EAOA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAYA;;;;;;;;;;;EAUA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;;EAcA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;;;;;EASA;;;;EAGgB;;;;EAChB;IAAgD;;;;;EAG5B;;;;;;;;;;;;;;;;;;EAepB;IAAgD;;;;;EAGhD;;;;EAGA;;;;EAGA;;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAMA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAGA;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAKA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;;EAIA;;;;;EAMA;;;;EAGA;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;;;EASA;;;;EAIF;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAIA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAIA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;;EAGA;;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAIE;;;;EAKA;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAOF;;;;EAKA;;;;EAME;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAMzB;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAOF;;;;;EAMA;;;;EAOI;;;;;EAUA;;;;;EAUA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAUA;;;;;EAUA;;;;;EAUA;;;;;;EASJ;;;;EAME;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;IAAwB;;;EAKxB;IAAwB;;;;;EAKxB;IAAwB;;;;;EAKxB;IAAwB;;;;;;EAMxB;IAAwB;;;;;;EAMxB;IAAwB;;;;;EAKxB;IAAwB;;;;;EAKxB;IAAwB;;;;;;EAMxB;IAAwB;;;;;EAKxB;IAAwB;;;;;EAKxB;IAAwB;;;;;;;EAOxB;IAAwB;;;;;EAKxB;IAAwB;;;;;EAKxB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAA4B;;;;;EAK5B;;;;;EAAA;;;;;EAAA;;;;;EAMA;;;;;EAAA;;;;;EAAA;;;;;EAMA;;;;EAAA;;;;EAAA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;;AAMJ;;;;;;;;;;;;;;;;;;;AAkBA;;;;;;AAKA;;;;;AAGE;;;;AAGA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAGE;;;;;;AAIE;EAAgD;;;;;AAG5C;;;;;AAIR;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAGE;EAAgD;;;;;AAIlD;;;;;;;;;;AAOE;EAAgD;;;;;AAG/B;;;;AACjB;EAAgD;;;;;AAG/B;;;;;AAKnB;;;;AAIF;;;;AAkBA;EACE;;;;;AAIF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;;;AASA;;;;;;;;;;;AASA;;;;;;;;;;;AASA;;;;;;;;;;;AASA;;;;;;;;;;;AASA;;;;;;;;;;;AASA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA"}}]}