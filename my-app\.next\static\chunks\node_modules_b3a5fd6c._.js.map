{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/toc.js"], "sourcesContent": ["\"use client\";\nimport {\n  useOnChange\n} from \"./chunk-EMWGTXSW.js\";\nimport \"./chunk-JSBRDJBE.js\";\n\n// src/toc.tsx\nimport { createContext, forwardRef, useContext, useMemo, useRef } from \"react\";\nimport scrollIntoView from \"scroll-into-view-if-needed\";\n\n// src/utils/merge-refs.ts\nfunction mergeRefs(...refs) {\n  return (value) => {\n    refs.forEach((ref) => {\n      if (typeof ref === \"function\") {\n        ref(value);\n      } else if (ref !== null) {\n        ref.current = value;\n      }\n    });\n  };\n}\n\n// src/utils/use-anchor-observer.ts\nimport { useEffect, useState } from \"react\";\nfunction useAnchorObserver(watch, single) {\n  const [activeAnchor, setActiveAnchor] = useState([]);\n  useEffect(() => {\n    let visible = [];\n    const observer = new IntersectionObserver(\n      (entries) => {\n        for (const entry of entries) {\n          if (entry.isIntersecting && !visible.includes(entry.target.id)) {\n            visible = [...visible, entry.target.id];\n          } else if (!entry.isIntersecting && visible.includes(entry.target.id)) {\n            visible = visible.filter((v) => v !== entry.target.id);\n          }\n        }\n        if (visible.length > 0) setActiveAnchor(visible);\n      },\n      {\n        rootMargin: single ? \"-80px 0% -70% 0%\" : `-20px 0% -40% 0%`,\n        threshold: 1\n      }\n    );\n    function onScroll() {\n      const element = document.scrollingElement;\n      if (!element) return;\n      const top = element.scrollTop;\n      if (top <= 0 && single) setActiveAnchor(watch.slice(0, 1));\n      else if (top + element.clientHeight >= element.scrollHeight - 6) {\n        setActiveAnchor((active) => {\n          return active.length > 0 && !single ? watch.slice(watch.indexOf(active[0])) : watch.slice(-1);\n        });\n      }\n    }\n    for (const heading of watch) {\n      const element = document.getElementById(heading);\n      if (element) observer.observe(element);\n    }\n    onScroll();\n    window.addEventListener(\"scroll\", onScroll);\n    return () => {\n      window.removeEventListener(\"scroll\", onScroll);\n      observer.disconnect();\n    };\n  }, [single, watch]);\n  return single ? activeAnchor.slice(0, 1) : activeAnchor;\n}\n\n// src/toc.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ActiveAnchorContext = createContext([]);\nvar ScrollContext = createContext({\n  current: null\n});\nfunction useActiveAnchor() {\n  return useContext(ActiveAnchorContext).at(-1);\n}\nfunction useActiveAnchors() {\n  return useContext(ActiveAnchorContext);\n}\nfunction ScrollProvider({\n  containerRef,\n  children\n}) {\n  return /* @__PURE__ */ jsx(ScrollContext.Provider, { value: containerRef, children });\n}\nfunction AnchorProvider({\n  toc,\n  single = true,\n  children\n}) {\n  const headings = useMemo(() => {\n    return toc.map((item) => item.url.split(\"#\")[1]);\n  }, [toc]);\n  return /* @__PURE__ */ jsx(ActiveAnchorContext.Provider, { value: useAnchorObserver(headings, single), children });\n}\nvar TOCItem = forwardRef(\n  ({ onActiveChange, ...props }, ref) => {\n    const containerRef = useContext(ScrollContext);\n    const anchors = useActiveAnchors();\n    const anchorRef = useRef(null);\n    const mergedRef = mergeRefs(anchorRef, ref);\n    const isActive = anchors.includes(props.href.slice(1));\n    useOnChange(isActive, (v) => {\n      const element = anchorRef.current;\n      if (!element) return;\n      if (v && containerRef.current) {\n        scrollIntoView(element, {\n          behavior: \"smooth\",\n          block: \"center\",\n          inline: \"center\",\n          scrollMode: \"always\",\n          boundary: containerRef.current\n        });\n      }\n      onActiveChange?.(v);\n    });\n    return /* @__PURE__ */ jsx(\"a\", { ref: mergedRef, \"data-active\": isActive, ...props, children: props.children });\n  }\n);\nTOCItem.displayName = \"TOCItem\";\nexport {\n  AnchorProvider,\n  ScrollProvider,\n  TOCItem,\n  useActiveAnchor,\n  useActiveAnchors\n};\n"], "names": [], "mappings": ";;;;;;;AACA;AAGA;AAEA,cAAc;AACd;AACA;AA8DA,cAAc;AACd;AAvEA;;;;;AAUA,0BAA0B;AAC1B,SAAS;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,KAAH,QAAA,SAAA,CAAA,KAAO;;IACxB,OAAO,CAAC;QACN,KAAK,OAAO,CAAC,CAAC;YACZ,IAAI,OAAO,QAAQ,YAAY;gBAC7B,IAAI;YACN,OAAO,IAAI,QAAQ,MAAM;gBACvB,IAAI,OAAO,GAAG;YAChB;QACF;IACF;AACF;;AAIA,SAAS,kBAAkB,KAAK,EAAE,MAAM;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,UAAU,EAAE;YAChB,MAAM,WAAW,IAAI;+CACnB,CAAC;oBACC,KAAK,MAAM,SAAS,QAAS;wBAC3B,IAAI,MAAM,cAAc,IAAI,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,CAAC,EAAE,GAAG;4BAC9D,UAAU;mCAAI;gCAAS,MAAM,MAAM,CAAC,EAAE;6BAAC;wBACzC,OAAO,IAAI,CAAC,MAAM,cAAc,IAAI,QAAQ,QAAQ,CAAC,MAAM,MAAM,CAAC,EAAE,GAAG;4BACrE,UAAU,QAAQ,MAAM;+DAAC,CAAC,IAAM,MAAM,MAAM,MAAM,CAAC,EAAE;;wBACvD;oBACF;oBACA,IAAI,QAAQ,MAAM,GAAG,GAAG,gBAAgB;gBAC1C;8CACA;gBACE,YAAY,SAAS,qBAAsB;gBAC3C,WAAW;YACb;YAEF,SAAS;gBACP,MAAM,UAAU,SAAS,gBAAgB;gBACzC,IAAI,CAAC,SAAS;gBACd,MAAM,MAAM,QAAQ,SAAS;gBAC7B,IAAI,OAAO,KAAK,QAAQ,gBAAgB,MAAM,KAAK,CAAC,GAAG;qBAClD,IAAI,MAAM,QAAQ,YAAY,IAAI,QAAQ,YAAY,GAAG,GAAG;oBAC/D;gEAAgB,CAAC;4BACf,OAAO,OAAO,MAAM,GAAG,KAAK,CAAC,SAAS,MAAM,KAAK,CAAC,MAAM,OAAO,CAAC,MAAM,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC,CAAC;wBAC7F;;gBACF;YACF;YACA,KAAK,MAAM,WAAW,MAAO;gBAC3B,MAAM,UAAU,SAAS,cAAc,CAAC;gBACxC,IAAI,SAAS,SAAS,OAAO,CAAC;YAChC;YACA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;+CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,SAAS,UAAU;gBACrB;;QACF;sCAAG;QAAC;QAAQ;KAAM;IAClB,OAAO,SAAS,aAAa,KAAK,CAAC,GAAG,KAAK;AAC7C;;AAIA,IAAI,sBAAsB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE,EAAE;AAC1C,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;IAChC,SAAS;AACX;AACA,SAAS;IACP,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,qBAAqB,EAAE,CAAC,CAAC;AAC7C;AACA,SAAS;IACP,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AACA,SAAS,eAAe,KAGvB;QAHuB,EACtB,YAAY,EACZ,QAAQ,EACT,GAHuB;IAItB,OAAO,aAAa,GAAG,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,cAAc,QAAQ,EAAE;QAAE,OAAO;QAAc;IAAS;AACrF;AACA,SAAS,eAAe,KAIvB;QAJuB,EACtB,GAAG,EACH,SAAS,IAAI,EACb,QAAQ,EACT,GAJuB;IAKtB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACvB,OAAO,IAAI,GAAG;oDAAC,CAAC,OAAS,KAAK,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;QACjD;2CAAG;QAAC;KAAI;IACR,OAAO,aAAa,GAAG,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,oBAAoB,QAAQ,EAAE;QAAE,OAAO,kBAAkB,UAAU;QAAS;IAAS;AAClH;AACA,IAAI,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACrB,QAA+B;QAA9B,EAAE,cAAc,EAAE,GAAG,OAAO;IAC3B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAChC,MAAM,UAAU;IAChB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,YAAY,UAAU,WAAW;IACvC,MAAM,WAAW,QAAQ,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC;IACnD,CAAA,GAAA,gKAAA,CAAA,cAAW,AAAD,EAAE;+BAAU,CAAC;YACrB,MAAM,UAAU,UAAU,OAAO;YACjC,IAAI,CAAC,SAAS;YACd,IAAI,KAAK,aAAa,OAAO,EAAE;gBAC7B,CAAA,GAAA,0KAAA,CAAA,UAAc,AAAD,EAAE,SAAS;oBACtB,UAAU;oBACV,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,UAAU,aAAa,OAAO;gBAChC;YACF;YACA,2BAAA,qCAAA,eAAiB;QACnB;;IACA,OAAO,aAAa,GAAG,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,KAAK;QAAE,KAAK;QAAW,eAAe;QAAU,GAAG,KAAK;QAAE,UAAU,MAAM,QAAQ;IAAC;AAChH;AAEF,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-ui/dist/components/layout/toc-thumb.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useEffect, useRef } from 'react';\nimport * as Primitive from 'fumadocs-core/toc';\nimport { useOnChange } from 'fumadocs-core/utils/use-on-change';\nimport { useEffectEvent } from 'fumadocs-core/utils/use-effect-event';\nfunction calc(container, active) {\n    if (active.length === 0 || container.clientHeight === 0) {\n        return [0, 0];\n    }\n    let upper = Number.MAX_VALUE, lower = 0;\n    for (const item of active) {\n        const element = container.querySelector(`a[href=\"#${item}\"]`);\n        if (!element)\n            continue;\n        const styles = getComputedStyle(element);\n        upper = Math.min(upper, element.offsetTop + parseFloat(styles.paddingTop));\n        lower = Math.max(lower, element.offsetTop +\n            element.clientHeight -\n            parseFloat(styles.paddingBottom));\n    }\n    return [upper, lower - upper];\n}\nfunction update(element, info) {\n    element.style.setProperty('--fd-top', `${info[0]}px`);\n    element.style.setProperty('--fd-height', `${info[1]}px`);\n}\nexport function TocThumb({ containerRef, ...props }) {\n    const active = Primitive.useActiveAnchors();\n    const thumbRef = useRef(null);\n    const onResize = useEffectEvent(() => {\n        if (!containerRef.current || !thumbRef.current)\n            return;\n        update(thumbRef.current, calc(containerRef.current, active));\n    });\n    useEffect(() => {\n        if (!containerRef.current)\n            return;\n        const container = containerRef.current;\n        onResize();\n        const observer = new ResizeObserver(onResize);\n        observer.observe(container);\n        return () => {\n            observer.disconnect();\n        };\n    }, [containerRef, onResize]);\n    useOnChange(active, () => {\n        if (!containerRef.current || !thumbRef.current)\n            return;\n        update(thumbRef.current, calc(containerRef.current, active));\n    });\n    return _jsx(\"div\", { ref: thumbRef, role: \"none\", ...props });\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;AACA;;;;;;AACA,SAAS,KAAK,SAAS,EAAE,MAAM;IAC3B,IAAI,OAAO,MAAM,KAAK,KAAK,UAAU,YAAY,KAAK,GAAG;QACrD,OAAO;YAAC;YAAG;SAAE;IACjB;IACA,IAAI,QAAQ,OAAO,SAAS,EAAE,QAAQ;IACtC,KAAK,MAAM,QAAQ,OAAQ;QACvB,MAAM,UAAU,UAAU,aAAa,CAAC,AAAC,YAAgB,OAAL,MAAK;QACzD,IAAI,CAAC,SACD;QACJ,MAAM,SAAS,iBAAiB;QAChC,QAAQ,KAAK,GAAG,CAAC,OAAO,QAAQ,SAAS,GAAG,WAAW,OAAO,UAAU;QACxE,QAAQ,KAAK,GAAG,CAAC,OAAO,QAAQ,SAAS,GACrC,QAAQ,YAAY,GACpB,WAAW,OAAO,aAAa;IACvC;IACA,OAAO;QAAC;QAAO,QAAQ;KAAM;AACjC;AACA,SAAS,OAAO,OAAO,EAAE,IAAI;IACzB,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY,AAAC,GAAU,OAAR,IAAI,CAAC,EAAE,EAAC;IACjD,QAAQ,KAAK,CAAC,WAAW,CAAC,eAAe,AAAC,GAAU,OAAR,IAAI,CAAC,EAAE,EAAC;AACxD;AACO,SAAS,SAAS,KAA0B;QAA1B,EAAE,YAAY,EAAE,GAAG,OAAO,GAA1B;IACrB,MAAM,SAAS,kJAAA,CAAA,mBAA0B;IACzC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,WAAW,CAAA,GAAA,8KAAA,CAAA,iBAAc,AAAD;6CAAE;YAC5B,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,SAAS,OAAO,EAC1C;YACJ,OAAO,SAAS,OAAO,EAAE,KAAK,aAAa,OAAO,EAAE;QACxD;;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACN,IAAI,CAAC,aAAa,OAAO,EACrB;YACJ,MAAM,YAAY,aAAa,OAAO;YACtC;YACA,MAAM,WAAW,IAAI,eAAe;YACpC,SAAS,OAAO,CAAC;YACjB;sCAAO;oBACH,SAAS,UAAU;gBACvB;;QACJ;6BAAG;QAAC;QAAc;KAAS;IAC3B,CAAA,GAAA,gKAAA,CAAA,cAAW,AAAD,EAAE;gCAAQ;YAChB,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,SAAS,OAAO,EAC1C;YACJ,OAAO,SAAS,OAAO,EAAE,KAAK,aAAa,OAAO,EAAE;QACxD;;IACA,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,KAAK;QAAU,MAAM;QAAQ,GAAG,KAAK;IAAC;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-ui/dist/utils/merge-refs.js"], "sourcesContent": ["export function mergeRefs(...refs) {\n    return (value) => {\n        refs.forEach((ref) => {\n            if (typeof ref === 'function') {\n                ref(value);\n            }\n            else if (ref) {\n                ref.current = value;\n            }\n        });\n    };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,KAAH,QAAA,SAAA,CAAA,KAAO;;IAC7B,OAAO,CAAC;QACJ,KAAK,OAAO,CAAC,CAAC;YACV,IAAI,OAAO,QAAQ,YAAY;gBAC3B,IAAI;YACR,OACK,IAAI,KAAK;gBACV,IAAI,OAAO,GAAG;YAClB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-ui/dist/components/layout/toc.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport * as Primitive from 'fumadocs-core/toc';\nimport { createContext, useContext, useRef } from 'react';\nimport { cn } from '../../utils/cn.js';\nimport { useI18n } from '../../contexts/i18n.js';\nimport { TocThumb } from '../../components/layout/toc-thumb.js';\nimport { mergeRefs } from '../../utils/merge-refs.js';\nconst TOCContext = createContext([]);\nexport function useTOCItems() {\n    return useContext(TOCContext);\n}\nexport function TOCProvider({ toc, children, ...props }) {\n    return (_jsx(TOCContext, { value: toc, children: _jsx(Primitive.AnchorProvider, { toc: toc, ...props, children: children }) }));\n}\nexport function TOCScrollArea({ ref, className, ...props }) {\n    const viewRef = useRef(null);\n    return (_jsx(\"div\", { ref: mergeRefs(viewRef, ref), className: cn('relative min-h-0 text-sm ms-px overflow-auto [scrollbar-width:none] [mask-image:linear-gradient(to_bottom,transparent,white_16px,white_calc(100%-16px),transparent)] py-3', className), ...props, children: _jsx(Primitive.ScrollProvider, { containerRef: viewRef, children: props.children }) }));\n}\nexport function TOCItems({ ref, className, ...props }) {\n    const containerRef = useRef(null);\n    const items = useTOCItems();\n    const { text } = useI18n();\n    if (items.length === 0)\n        return (_jsx(\"div\", { className: \"rounded-lg border bg-fd-card p-3 text-xs text-fd-muted-foreground\", children: text.tocNoHeadings }));\n    return (_jsxs(_Fragment, { children: [_jsx(TocThumb, { containerRef: containerRef, className: \"absolute top-(--fd-top) h-(--fd-height) w-px bg-fd-primary transition-all\" }), _jsx(\"div\", { ref: mergeRefs(ref, containerRef), className: cn('flex flex-col border-s border-fd-foreground/10', className), ...props, children: items.map((item) => (_jsx(TOCItem, { item: item }, item.url))) })] }));\n}\nfunction TOCItem({ item }) {\n    return (_jsx(Primitive.TOCItem, { href: item.url, className: cn('prose py-1.5 text-sm text-fd-muted-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary', item.depth <= 2 && 'ps-3', item.depth === 3 && 'ps-6', item.depth >= 4 && 'ps-8'), children: item.title }));\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAQA,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE,EAAE;AAC5B,SAAS;IACZ,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACtB;AACO,SAAS,YAAY,KAA2B;QAA3B,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO,GAA3B;IACxB,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,YAAY;QAAE,OAAO;QAAK,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,kJAAA,CAAA,iBAAwB,EAAE;YAAE,KAAK;YAAK,GAAG,KAAK;YAAE,UAAU;QAAS;IAAG;AAChI;AACO,SAAS,cAAc,KAA4B;QAA5B,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,OAAO,GAA5B;IAC1B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,KAAK,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QAAM,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,6KAA6K;QAAY,GAAG,KAAK;QAAE,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,kJAAA,CAAA,iBAAwB,EAAE;YAAE,cAAc;YAAS,UAAU,MAAM,QAAQ;QAAC;IAAG;AACvW;AACO,SAAS,SAAS,KAA4B;QAA5B,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,OAAO,GAA5B;IACrB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,QAAQ;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;IACvB,IAAI,MAAM,MAAM,KAAK,GACjB,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,WAAW;QAAqE,UAAU,KAAK,aAAa;IAAC;IACvI,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,sKAAA,CAAA,WAAS,EAAE;QAAE,UAAU;YAAC,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,iLAAA,CAAA,WAAQ,EAAE;gBAAE,cAAc;gBAAc,WAAW;YAA4E;YAAI,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBAAE,KAAK,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,KAAK;gBAAe,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;gBAAY,GAAG,KAAK;gBAAE,UAAU,MAAM,GAAG,CAAC,CAAC,OAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,SAAS;wBAAE,MAAM;oBAAK,GAAG,KAAK,GAAG;YAAI;SAAG;IAAC;AACtY;AACA,SAAS,QAAQ,KAAQ;QAAR,EAAE,IAAI,EAAE,GAAR;IACb,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,kJAAA,CAAA,UAAiB,EAAE;QAAE,MAAM,KAAK,GAAG;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,oJAAoJ,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,KAAK;QAAS,UAAU,KAAK,KAAK;IAAC;AAChU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-ui/dist/layouts/docs/page-client.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment, useEffect, useMemo, useRef, useState, } from 'react';\nimport { ChevronDown, ChevronLeft, ChevronRight } from '../../icons.js';\nimport Link from 'fumadocs-core/link';\nimport { cn } from '../../utils/cn.js';\nimport { useI18n } from '../../contexts/i18n.js';\nimport { useTreeContext, useTreePath } from '../../contexts/tree.js';\nimport { createContext, usePathname } from 'fumadocs-core/framework';\nimport { getBreadcrumbItemsFromPath, } from 'fumadocs-core/breadcrumb';\nimport { useNav } from '../../contexts/layout.js';\nimport { isActive } from '../../utils/is-active.js';\nimport { useEffectEvent } from 'fumadocs-core/utils/use-effect-event';\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger, } from '../../components/ui/collapsible.js';\nimport { useSidebar } from '../../contexts/sidebar.js';\nimport { useTOCItems } from '../../components/layout/toc.js';\nimport { useActiveAnchor } from 'fumadocs-core/toc';\nconst TocPopoverContext = createContext('TocPopoverContext');\nexport function PageTOCPopoverTrigger(props) {\n    const { text } = useI18n();\n    const { open } = TocPopoverContext.use();\n    const items = useTOCItems();\n    const active = useActiveAnchor();\n    const selected = useMemo(() => items.findIndex((item) => active === item.url.slice(1)), [items, active]);\n    const path = useTreePath().at(-1);\n    const showItem = selected !== -1 && !open;\n    return (_jsxs(CollapsibleTrigger, { ...props, className: cn('flex w-full h-(--fd-tocnav-height) items-center text-sm text-fd-muted-foreground gap-2.5 px-4 py-2.5 text-start focus-visible:outline-none [&_svg]:size-4 md:px-6', props.className), children: [_jsx(ProgressCircle, { value: (selected + 1) / Math.max(1, items.length), max: 1, className: cn('shrink-0', open && 'text-fd-primary') }), _jsxs(\"span\", { className: \"grid flex-1 *:my-auto *:row-start-1 *:col-start-1\", children: [_jsx(\"span\", { className: cn('truncate transition-all', open && 'text-fd-foreground', showItem && 'opacity-0 -translate-y-full pointer-events-none'), children: path?.name ?? text.toc }), _jsx(\"span\", { className: cn('truncate transition-all', !showItem && 'opacity-0 translate-y-full pointer-events-none'), children: items[selected]?.title })] }), _jsx(ChevronDown, { className: cn('shrink-0 transition-transform mx-0.5', open && 'rotate-180') })] }));\n}\nfunction clamp(input, min, max) {\n    if (input < min)\n        return min;\n    if (input > max)\n        return max;\n    return input;\n}\nfunction ProgressCircle({ value, strokeWidth = 2, size = 24, min = 0, max = 100, ...restSvgProps }) {\n    const normalizedValue = clamp(value, min, max);\n    const radius = (size - strokeWidth) / 2;\n    const circumference = 2 * Math.PI * radius;\n    const progress = (normalizedValue / max) * circumference;\n    const circleProps = {\n        cx: size / 2,\n        cy: size / 2,\n        r: radius,\n        fill: 'none',\n        strokeWidth,\n    };\n    return (_jsxs(\"svg\", { role: \"progressbar\", viewBox: `0 0 ${size} ${size}`, \"aria-valuenow\": normalizedValue, \"aria-valuemin\": min, \"aria-valuemax\": max, ...restSvgProps, children: [_jsx(\"circle\", { ...circleProps, className: \"stroke-current/25\" }), _jsx(\"circle\", { ...circleProps, stroke: \"currentColor\", strokeDasharray: circumference, strokeDashoffset: circumference - progress, strokeLinecap: \"round\", transform: `rotate(-90 ${size / 2} ${size / 2})`, className: \"transition-all\" })] }));\n}\nexport function PageTOCPopoverContent(props) {\n    return (_jsx(CollapsibleContent, { \"data-toc-popover\": \"\", ...props, className: cn('flex flex-col px-4 max-h-[50vh] md:px-6', props.className), children: props.children }));\n}\nexport function PageTOCPopover(props) {\n    const ref = useRef(null);\n    const [open, setOpen] = useState(false);\n    const { collapsed } = useSidebar();\n    const { isTransparent } = useNav();\n    const onClick = useEffectEvent((e) => {\n        if (!open)\n            return;\n        if (ref.current && !ref.current.contains(e.target))\n            setOpen(false);\n    });\n    useEffect(() => {\n        window.addEventListener('click', onClick);\n        return () => {\n            window.removeEventListener('click', onClick);\n        };\n    }, [onClick]);\n    return (_jsx(TocPopoverContext.Provider, { value: useMemo(() => ({\n            open,\n            setOpen,\n        }), [setOpen, open]), children: _jsx(Collapsible, { open: open, onOpenChange: setOpen, asChild: true, children: _jsx(\"header\", { ref: ref, id: \"nd-tocnav\", ...props, className: cn('fixed inset-x-0 z-10 border-b backdrop-blur-sm transition-colors xl:hidden', (!isTransparent || open) && 'bg-fd-background/80', open && 'shadow-lg', props.className), style: {\n                    ...props.style,\n                    top: 'calc(var(--fd-banner-height) + var(--fd-nav-height))',\n                    insetInlineStart: collapsed\n                        ? '0px'\n                        : 'calc(var(--fd-sidebar-width) + var(--fd-layout-offset))',\n                }, children: props.children }) }) }));\n}\nexport function PageLastUpdate({ date: value, ...props }) {\n    const { text } = useI18n();\n    const [date, setDate] = useState('');\n    useEffect(() => {\n        // to the timezone of client\n        setDate(new Date(value).toLocaleDateString());\n    }, [value]);\n    return (_jsxs(\"p\", { ...props, className: cn('text-sm text-fd-muted-foreground', props.className), children: [text.lastUpdate, \" \", date] }));\n}\nfunction scanNavigationList(tree) {\n    const list = [];\n    tree.forEach((node) => {\n        if (node.type === 'folder') {\n            if (node.index) {\n                list.push(node.index);\n            }\n            list.push(...scanNavigationList(node.children));\n            return;\n        }\n        if (node.type === 'page' && !node.external) {\n            list.push(node);\n        }\n    });\n    return list;\n}\nconst listCache = new Map();\nexport function PageFooter({ items, ...props }) {\n    const { root } = useTreeContext();\n    const pathname = usePathname();\n    const { previous, next } = useMemo(() => {\n        if (items)\n            return items;\n        const cached = listCache.get(root.$id);\n        const list = cached ?? scanNavigationList(root.children);\n        listCache.set(root.$id, list);\n        const idx = list.findIndex((item) => isActive(item.url, pathname, false));\n        if (idx === -1)\n            return {};\n        return {\n            previous: list[idx - 1],\n            next: list[idx + 1],\n        };\n    }, [items, pathname, root]);\n    return (_jsxs(\"div\", { ...props, className: cn('@container grid gap-4 pb-6', previous && next ? 'grid-cols-2' : 'grid-cols-1', props.className), children: [previous ? _jsx(FooterItem, { item: previous, index: 0 }) : null, next ? _jsx(FooterItem, { item: next, index: 1 }) : null] }));\n}\nfunction FooterItem({ item, index }) {\n    const { text } = useI18n();\n    const Icon = index === 0 ? ChevronLeft : ChevronRight;\n    return (_jsxs(Link, { href: item.url, className: cn('flex flex-col gap-2 rounded-lg border p-4 text-sm transition-colors hover:bg-fd-accent/80 hover:text-fd-accent-foreground @max-lg:col-span-full', index === 1 && 'text-end'), children: [_jsxs(\"div\", { className: cn('inline-flex items-center gap-1.5 font-medium', index === 1 && 'flex-row-reverse'), children: [_jsx(Icon, { className: \"-mx-1 size-4 shrink-0 rtl:rotate-180\" }), _jsx(\"p\", { children: item.name })] }), _jsx(\"p\", { className: \"text-fd-muted-foreground truncate\", children: item.description ?? (index === 0 ? text.previousPage : text.nextPage) })] }));\n}\nexport function PageBreadcrumb({ includeRoot = false, includeSeparator, includePage = false, ...props }) {\n    const path = useTreePath();\n    const { root } = useTreeContext();\n    const items = useMemo(() => {\n        return getBreadcrumbItemsFromPath(root, path, {\n            includePage,\n            includeSeparator,\n            includeRoot,\n        });\n    }, [includePage, includeRoot, includeSeparator, path, root]);\n    if (items.length === 0)\n        return null;\n    return (_jsx(\"div\", { ...props, className: cn('flex items-center gap-1.5 text-sm text-fd-muted-foreground', props.className), children: items.map((item, i) => {\n            const className = cn('truncate', i === items.length - 1 && 'text-fd-primary font-medium');\n            return (_jsxs(Fragment, { children: [i !== 0 && _jsx(ChevronRight, { className: \"size-3.5 shrink-0\" }), item.url ? (_jsx(Link, { href: item.url, className: cn(className, 'transition-opacity hover:opacity-80'), children: item.name })) : (_jsx(\"span\", { className: className, children: item.name }))] }, i));\n        }) }));\n}\nexport function PageTOC(props) {\n    return (_jsx(\"div\", { id: \"nd-toc\", ...props, className: cn('sticky pb-2 pt-12 max-xl:hidden', props.className), style: {\n            ...props.style,\n            top: 'calc(var(--fd-banner-height) + var(--fd-nav-height))',\n            height: 'calc(100dvh - var(--fd-banner-height) - var(--fd-nav-height))',\n        }, children: _jsx(\"div\", { className: \"flex h-full w-(--fd-toc-width) max-w-full flex-col pe-4\", children: props.children }) }));\n}\n"], "names": [], "mappings": ";;;;;;;;;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;;AAiBA,MAAM,oBAAoB,CAAA,GAAA,gKAAA,CAAA,gBAAa,AAAD,EAAE;AACjC,SAAS,sBAAsB,KAAK;QAQ0vB;IAPjyB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB,GAAG;IACtC,MAAM,QAAQ,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAC7B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM,MAAM,SAAS;2DAAC,CAAC,OAAS,WAAW,KAAK,GAAG,CAAC,KAAK,CAAC;;kDAAK;QAAC;QAAO;KAAO;IACvG,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,IAAI,EAAE,CAAC,CAAC;IAC/B,MAAM,WAAW,aAAa,CAAC,KAAK,CAAC;QAC+lB;IAApoB,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,4KAAA,CAAA,qBAAkB,EAAE;QAAE,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,qKAAqK,MAAM,SAAS;QAAG,UAAU;YAAC,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,gBAAgB;gBAAE,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM;gBAAG,KAAK;gBAAG,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,YAAY,QAAQ;YAAmB;YAAI,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,QAAQ;gBAAE,WAAW;gBAAqD,UAAU;oBAAC,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;wBAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B,QAAQ,sBAAsB,YAAY;wBAAoD,UAAU,CAAA,aAAA,iBAAA,2BAAA,KAAM,IAAI,cAAV,wBAAA,aAAc,KAAK,GAAG;oBAAC;oBAAI,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;wBAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B,CAAC,YAAY;wBAAmD,QAAQ,GAAE,kBAAA,KAAK,CAAC,SAAS,cAAf,sCAAA,gBAAiB,KAAK;oBAAC;iBAAG;YAAC;YAAI,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,kJAAA,CAAA,cAAW,EAAE;gBAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC,QAAQ;YAAc;SAAG;IAAC;AACx6B;AACA,SAAS,MAAM,KAAK,EAAE,GAAG,EAAE,GAAG;IAC1B,IAAI,QAAQ,KACR,OAAO;IACX,IAAI,QAAQ,KACR,OAAO;IACX,OAAO;AACX;AACA,SAAS,eAAe,KAA0E;QAA1E,EAAE,KAAK,EAAE,cAAc,CAAC,EAAE,OAAO,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,GAAG,cAAc,GAA1E;IACpB,MAAM,kBAAkB,MAAM,OAAO,KAAK;IAC1C,MAAM,SAAS,CAAC,OAAO,WAAW,IAAI;IACtC,MAAM,gBAAgB,IAAI,KAAK,EAAE,GAAG;IACpC,MAAM,WAAW,AAAC,kBAAkB,MAAO;IAC3C,MAAM,cAAc;QAChB,IAAI,OAAO;QACX,IAAI,OAAO;QACX,GAAG;QACH,MAAM;QACN;IACJ;IACA,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAAE,MAAM;QAAe,SAAS,AAAC,OAAc,OAAR,MAAK,KAAQ,OAAL;QAAQ,iBAAiB;QAAiB,iBAAiB;QAAK,iBAAiB;QAAK,GAAG,YAAY;QAAE,UAAU;YAAC,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,UAAU;gBAAE,GAAG,WAAW;gBAAE,WAAW;YAAoB;YAAI,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,UAAU;gBAAE,GAAG,WAAW;gBAAE,QAAQ;gBAAgB,iBAAiB;gBAAe,kBAAkB,gBAAgB;gBAAU,eAAe;gBAAS,WAAW,AAAC,cAAyB,OAAZ,OAAO,GAAE,KAAY,OAAT,OAAO,GAAE;gBAAI,WAAW;YAAiB;SAAG;IAAC;AAC7e;AACO,SAAS,sBAAsB,KAAK;IACvC,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,4KAAA,CAAA,qBAAkB,EAAE;QAAE,oBAAoB;QAAI,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C,MAAM,SAAS;QAAG,UAAU,MAAM,QAAQ;IAAC;AAC7K;AACO,SAAS,eAAe,KAAK;IAChC,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,aAAU,AAAD;IAC/B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD;IAC/B,MAAM,UAAU,CAAA,GAAA,8KAAA,CAAA,iBAAc,AAAD;kDAAE,CAAC;YAC5B,IAAI,CAAC,MACD;YACJ,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,GAC7C,QAAQ;QAChB;;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,OAAO,gBAAgB,CAAC,SAAS;YACjC;4CAAO;oBACH,OAAO,mBAAmB,CAAC,SAAS;gBACxC;;QACJ;mCAAG;QAAC;KAAQ;IACZ,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,kBAAkB,QAAQ,EAAE;QAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCAAE,IAAM,CAAC;oBACzD;oBACA;gBACJ,CAAC;qCAAG;YAAC;YAAS;SAAK;QAAG,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,4KAAA,CAAA,cAAW,EAAE;YAAE,MAAM;YAAM,cAAc;YAAS,SAAS;YAAM,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,UAAU;gBAAE,KAAK;gBAAK,IAAI;gBAAa,GAAG,KAAK;gBAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,8EAA8E,CAAC,CAAC,iBAAiB,IAAI,KAAK,uBAAuB,QAAQ,aAAa,MAAM,SAAS;gBAAG,OAAO;oBACvV,GAAG,MAAM,KAAK;oBACd,KAAK;oBACL,kBAAkB,YACZ,QACA;gBACV;gBAAG,UAAU,MAAM,QAAQ;YAAC;QAAG;IAAG;AAClD;AACO,SAAS,eAAe,KAAyB;QAAzB,EAAE,MAAM,KAAK,EAAE,GAAG,OAAO,GAAzB;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,4BAA4B;YAC5B,QAAQ,IAAI,KAAK,OAAO,kBAAkB;QAC9C;mCAAG;QAAC;KAAM;IACV,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,KAAK;QAAE,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC,MAAM,SAAS;QAAG,UAAU;YAAC,KAAK,UAAU;YAAE;YAAK;SAAK;IAAC;AAC9I;AACA,SAAS,mBAAmB,IAAI;IAC5B,MAAM,OAAO,EAAE;IACf,KAAK,OAAO,CAAC,CAAC;QACV,IAAI,KAAK,IAAI,KAAK,UAAU;YACxB,IAAI,KAAK,KAAK,EAAE;gBACZ,KAAK,IAAI,CAAC,KAAK,KAAK;YACxB;YACA,KAAK,IAAI,IAAI,mBAAmB,KAAK,QAAQ;YAC7C;QACJ;QACA,IAAI,KAAK,IAAI,KAAK,UAAU,CAAC,KAAK,QAAQ,EAAE;YACxC,KAAK,IAAI,CAAC;QACd;IACJ;IACA,OAAO;AACX;AACA,MAAM,YAAY,IAAI;AACf,SAAS,WAAW,KAAmB;QAAnB,EAAE,KAAK,EAAE,GAAG,OAAO,GAAnB;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,WAAW,CAAA,GAAA,gKAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8BAAE;YAC/B,IAAI,OACA,OAAO;YACX,MAAM,SAAS,UAAU,GAAG,CAAC,KAAK,GAAG;YACrC,MAAM,OAAO,mBAAA,oBAAA,SAAU,mBAAmB,KAAK,QAAQ;YACvD,UAAU,GAAG,CAAC,KAAK,GAAG,EAAE;YACxB,MAAM,MAAM,KAAK,SAAS;0CAAC,CAAC,OAAS,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG,EAAE,UAAU;;YAClE,IAAI,QAAQ,CAAC,GACT,OAAO,CAAC;YACZ,OAAO;gBACH,UAAU,IAAI,CAAC,MAAM,EAAE;gBACvB,MAAM,IAAI,CAAC,MAAM,EAAE;YACvB;QACJ;6BAAG;QAAC;QAAO;QAAU;KAAK;IAC1B,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAAE,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,YAAY,OAAO,gBAAgB,eAAe,MAAM,SAAS;QAAG,UAAU;YAAC,WAAW,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,YAAY;gBAAE,MAAM;gBAAU,OAAO;YAAE,KAAK;YAAM,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,YAAY;gBAAE,MAAM;gBAAM,OAAO;YAAE,KAAK;SAAK;IAAC;AAC5R;AACA,SAAS,WAAW,KAAe;QAAf,EAAE,IAAI,EAAE,KAAK,EAAE,GAAf;IAChB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;IACvB,MAAM,OAAO,UAAU,IAAI,kJAAA,CAAA,cAAW,GAAG,kJAAA,CAAA,eAAY;QACse;IAA3hB,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,mMAAA,CAAA,UAAI,EAAE;QAAE,MAAM,KAAK,GAAG;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,mJAAmJ,UAAU,KAAK;QAAa,UAAU;YAAC,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD,UAAU,KAAK;gBAAqB,UAAU;oBAAC,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,MAAM;wBAAE,WAAW;oBAAuC;oBAAI,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAAE,UAAU,KAAK,IAAI;oBAAC;iBAAG;YAAC;YAAI,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gBAAE,WAAW;gBAAqC,UAAU,CAAA,oBAAA,KAAK,WAAW,cAAhB,+BAAA,oBAAqB,UAAU,IAAI,KAAK,YAAY,GAAG,KAAK,QAAQ;YAAE;SAAG;IAAC;AACzmB;AACO,SAAS,eAAe,KAAwE;QAAxE,EAAE,cAAc,KAAK,EAAE,gBAAgB,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,GAAxE;IAC3B,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE;YAClB,OAAO,CAAA,GAAA,yJAAA,CAAA,6BAA0B,AAAD,EAAE,MAAM,MAAM;gBAC1C;gBACA;gBACA;YACJ;QACJ;wCAAG;QAAC;QAAa;QAAa;QAAkB;QAAM;KAAK;IAC3D,IAAI,MAAM,MAAM,KAAK,GACjB,OAAO;IACX,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D,MAAM,SAAS;QAAG,UAAU,MAAM,GAAG,CAAC,CAAC,MAAM;YACjJ,MAAM,YAAY,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,YAAY,MAAM,MAAM,MAAM,GAAG,KAAK;YAC3D,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,6JAAA,CAAA,WAAQ,EAAE;gBAAE,UAAU;oBAAC,MAAM,KAAK,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,kJAAA,CAAA,eAAY,EAAE;wBAAE,WAAW;oBAAoB;oBAAI,KAAK,GAAG,GAAI,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,mMAAA,CAAA,UAAI,EAAE;wBAAE,MAAM,KAAK,GAAG;wBAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,WAAW;wBAAwC,UAAU,KAAK,IAAI;oBAAC,KAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;wBAAE,WAAW;wBAAW,UAAU,KAAK,IAAI;oBAAC;iBAAI;YAAC,GAAG;QAClT;IAAG;AACX;AACO,SAAS,QAAQ,KAAK;IACzB,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,IAAI;QAAU,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC,MAAM,SAAS;QAAG,OAAO;YAChH,GAAG,MAAM,KAAK;YACd,KAAK;YACL,QAAQ;QACZ;QAAG,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YAAE,WAAW;YAA2D,UAAU,MAAM,QAAQ;QAAC;IAAG;AACrI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 718, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport * as Primitive from 'fumadocs-core/toc';\nimport { useEffect, useRef, useState } from 'react';\nimport { cn } from '../../utils/cn.js';\nimport { TocThumb } from '../../components/layout/toc-thumb.js';\nimport { useTOCItems } from '../../components/layout/toc.js';\nimport { mergeRefs } from '../../utils/merge-refs.js';\nimport { useI18n } from '../../contexts/i18n.js';\nexport default function ClerkTOCItems({ ref, className, ...props }) {\n    const containerRef = useRef(null);\n    const items = useTOCItems();\n    const { text } = useI18n();\n    const [svg, setSvg] = useState();\n    useEffect(() => {\n        if (!containerRef.current)\n            return;\n        const container = containerRef.current;\n        function onResize() {\n            if (container.clientHeight === 0)\n                return;\n            let w = 0, h = 0;\n            const d = [];\n            for (let i = 0; i < items.length; i++) {\n                const element = container.querySelector(`a[href=\"#${items[i].url.slice(1)}\"]`);\n                if (!element)\n                    continue;\n                const styles = getComputedStyle(element);\n                const offset = getLineOffset(items[i].depth) + 1, top = element.offsetTop + parseFloat(styles.paddingTop), bottom = element.offsetTop +\n                    element.clientHeight -\n                    parseFloat(styles.paddingBottom);\n                w = Math.max(offset, w);\n                h = Math.max(h, bottom);\n                d.push(`${i === 0 ? 'M' : 'L'}${offset} ${top}`);\n                d.push(`L${offset} ${bottom}`);\n            }\n            setSvg({\n                path: d.join(' '),\n                width: w + 1,\n                height: h,\n            });\n        }\n        const observer = new ResizeObserver(onResize);\n        onResize();\n        observer.observe(container);\n        return () => {\n            observer.disconnect();\n        };\n    }, [items]);\n    if (items.length === 0)\n        return (_jsx(\"div\", { className: \"rounded-lg border bg-fd-card p-3 text-xs text-fd-muted-foreground\", children: text.tocNoHeadings }));\n    return (_jsxs(_Fragment, { children: [svg ? (_jsx(\"div\", { className: \"absolute start-0 top-0 rtl:-scale-x-100\", style: {\n                    width: svg.width,\n                    height: svg.height,\n                    maskImage: `url(\"data:image/svg+xml,${\n                    // Inline SVG\n                    encodeURIComponent(`<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 ${svg.width} ${svg.height}\"><path d=\"${svg.path}\" stroke=\"black\" stroke-width=\"1\" fill=\"none\" /></svg>`)}\")`,\n                }, children: _jsx(TocThumb, { containerRef: containerRef, className: \"mt-(--fd-top) h-(--fd-height) bg-fd-primary transition-all\" }) })) : null, _jsx(\"div\", { ref: mergeRefs(containerRef, ref), className: cn('flex flex-col', className), ...props, children: items.map((item, i) => (_jsx(TOCItem, { item: item, upper: items[i - 1]?.depth, lower: items[i + 1]?.depth }, item.url))) })] }));\n}\nfunction getItemOffset(depth) {\n    if (depth <= 2)\n        return 14;\n    if (depth === 3)\n        return 26;\n    return 36;\n}\nfunction getLineOffset(depth) {\n    return depth >= 3 ? 10 : 0;\n}\nfunction TOCItem({ item, upper = item.depth, lower = item.depth, }) {\n    const offset = getLineOffset(item.depth), upperOffset = getLineOffset(upper), lowerOffset = getLineOffset(lower);\n    return (_jsxs(Primitive.TOCItem, { href: item.url, style: {\n            paddingInlineStart: getItemOffset(item.depth),\n        }, className: \"prose relative py-1.5 text-sm text-fd-muted-foreground hover:text-fd-accent-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary\", children: [offset !== upperOffset ? (_jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 16 16\", className: \"absolute -top-1.5 start-0 size-4 rtl:-scale-x-100\", children: _jsx(\"line\", { x1: upperOffset, y1: \"0\", x2: offset, y2: \"12\", className: \"stroke-fd-foreground/10\", strokeWidth: \"1\" }) })) : null, _jsx(\"div\", { className: cn('absolute inset-y-0 w-px bg-fd-foreground/10', offset !== upperOffset && 'top-1.5', offset !== lowerOffset && 'bottom-1.5'), style: {\n                    insetInlineStart: offset,\n                } }), item.title] }));\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AASe,SAAS,cAAc,KAA4B;QAA5B,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,OAAO,GAA5B;IAClC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,QAAQ,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,CAAC,aAAa,OAAO,EACrB;YACJ,MAAM,YAAY,aAAa,OAAO;YACtC,SAAS;gBACL,IAAI,UAAU,YAAY,KAAK,GAC3B;gBACJ,IAAI,IAAI,GAAG,IAAI;gBACf,MAAM,IAAI,EAAE;gBACZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACnC,MAAM,UAAU,UAAU,aAAa,CAAC,AAAC,YAAiC,OAAtB,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAG;oBAC1E,IAAI,CAAC,SACD;oBACJ,MAAM,SAAS,iBAAiB;oBAChC,MAAM,SAAS,cAAc,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,MAAM,QAAQ,SAAS,GAAG,WAAW,OAAO,UAAU,GAAG,SAAS,QAAQ,SAAS,GACjI,QAAQ,YAAY,GACpB,WAAW,OAAO,aAAa;oBACnC,IAAI,KAAK,GAAG,CAAC,QAAQ;oBACrB,IAAI,KAAK,GAAG,CAAC,GAAG;oBAChB,EAAE,IAAI,CAAC,AAAC,GAAwB,OAAtB,MAAM,IAAI,MAAM,KAAgB,OAAV,QAAO,KAAO,OAAJ;oBAC1C,EAAE,IAAI,CAAC,AAAC,IAAa,OAAV,QAAO,KAAU,OAAP;gBACzB;gBACA,OAAO;oBACH,MAAM,EAAE,IAAI,CAAC;oBACb,OAAO,IAAI;oBACX,QAAQ;gBACZ;YACJ;YACA,MAAM,WAAW,IAAI,eAAe;YACpC;YACA,SAAS,OAAO,CAAC;YACjB;2CAAO;oBACH,SAAS,UAAU;gBACvB;;QACJ;kCAAG;QAAC;KAAM;IACV,IAAI,MAAM,MAAM,KAAK,GACjB,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,WAAW;QAAqE,UAAU,KAAK,aAAa;IAAC;IACvI,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,sKAAA,CAAA,WAAS,EAAE;QAAE,UAAU;YAAC,MAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBAAE,WAAW;gBAA2C,OAAO;oBACxG,OAAO,IAAI,KAAK;oBAChB,QAAQ,IAAI,MAAM;oBAClB,WAAW,AAAC,2BAEsK,OADlL,aAAa;oBACb,mBAAmB,AAAC,wDAAoE,OAAb,IAAI,KAAK,EAAC,KAA2B,OAAxB,IAAI,MAAM,EAAC,eAAsB,OAAT,IAAI,IAAI,EAAC,4DAAyD;gBACtL;gBAAG,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,iLAAA,CAAA,WAAQ,EAAE;oBAAE,cAAc;oBAAc,WAAW;gBAA6D;YAAG,KAAM;YAAM,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBAAE,KAAK,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;gBAAM,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;gBAAY,GAAG,KAAK;gBAAE,UAAU,MAAM,GAAG,CAAC,CAAC,MAAM;wBAA0C,SAA4B;2BAA/D,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,SAAS;wBAAE,MAAM;wBAAM,KAAK,GAAE,UAAA,KAAK,CAAC,IAAI,EAAE,cAAZ,8BAAA,QAAc,KAAK;wBAAE,KAAK,GAAE,WAAA,KAAK,CAAC,IAAI,EAAE,cAAZ,+BAAA,SAAc,KAAK;oBAAC,GAAG,KAAK,GAAG;;YAAI;SAAG;IAAC;AAC/Y;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,SAAS,GACT,OAAO;IACX,IAAI,UAAU,GACV,OAAO;IACX,OAAO;AACX;AACA,SAAS,cAAc,KAAK;IACxB,OAAO,SAAS,IAAI,KAAK;AAC7B;AACA,SAAS,QAAQ,KAAiD;QAAjD,EAAE,IAAI,EAAE,QAAQ,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK,EAAG,GAAjD;IACb,MAAM,SAAS,cAAc,KAAK,KAAK,GAAG,cAAc,cAAc,QAAQ,cAAc,cAAc;IAC1G,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,kJAAA,CAAA,UAAiB,EAAE;QAAE,MAAM,KAAK,GAAG;QAAE,OAAO;YAClD,oBAAoB,cAAc,KAAK,KAAK;QAChD;QAAG,WAAW;QAA6L,UAAU;YAAC,WAAW,cAAe,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBAAE,OAAO;gBAA8B,SAAS;gBAAa,WAAW;gBAAqD,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;oBAAE,IAAI;oBAAa,IAAI;oBAAK,IAAI;oBAAQ,IAAI;oBAAM,WAAW;oBAA2B,aAAa;gBAAI;YAAG,KAAM;YAAM,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C,WAAW,eAAe,WAAW,WAAW,eAAe;gBAAe,OAAO;oBAC5pB,kBAAkB;gBACtB;YAAE;YAAI,KAAK,KAAK;SAAC;IAAC;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-ui/dist/utils/use-copy-button.js"], "sourcesContent": ["'use client';\nimport { useEffect, useRef, useState } from 'react';\nimport { useEffectEvent } from 'fumadocs-core/utils/use-effect-event';\nexport function useCopyButton(onCopy) {\n    const [checked, setChecked] = useState(false);\n    const timeoutRef = useRef(null);\n    const onClick = useEffectEvent(() => {\n        if (timeoutRef.current)\n            window.clearTimeout(timeoutRef.current);\n        const res = Promise.resolve(onCopy());\n        void res.then(() => {\n            setChecked(true);\n            timeoutRef.current = window.setTimeout(() => {\n                setChecked(false);\n            }, 1500);\n        });\n    });\n    // Avoid updates after being unmounted\n    useEffect(() => {\n        return () => {\n            if (timeoutRef.current)\n                window.clearTimeout(timeoutRef.current);\n        };\n    }, []);\n    return [checked, onClick];\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;AAGO,SAAS,cAAc,MAAM;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,8KAAA,CAAA,iBAAc,AAAD;iDAAE;YAC3B,IAAI,WAAW,OAAO,EAClB,OAAO,YAAY,CAAC,WAAW,OAAO;YAC1C,MAAM,MAAM,QAAQ,OAAO,CAAC;YAC5B,KAAK,IAAI,IAAI;yDAAC;oBACV,WAAW;oBACX,WAAW,OAAO,GAAG,OAAO,UAAU;iEAAC;4BACnC,WAAW;wBACf;gEAAG;gBACP;;QACJ;;IACA,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN;2CAAO;oBACH,IAAI,WAAW,OAAO,EAClB,OAAO,YAAY,CAAC,WAAW,OAAO;gBAC9C;;QACJ;kCAAG,EAAE;IACL,OAAO;QAAC;QAAS;KAAQ;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40swc/helpers/esm/_class_apply_descriptor_get.js"], "sourcesContent": ["function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU;IACrD,IAAI,WAAW,GAAG,EAAE,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC;IAE/C,OAAO,WAAW,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40swc/helpers/esm/_class_extract_field_descriptor.js"], "sourcesContent": ["function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gCAAgC,QAAQ,EAAE,UAAU,EAAE,MAAM;IACjE,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU,kBAAkB,SAAS;IAE9E,OAAO,WAAW,GAAG,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40swc/helpers/esm/_class_private_field_get.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_get(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"get\");\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_private_field_get as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU;IAClD,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,OAAO,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40swc/helpers/esm/_check_private_redeclaration.js"], "sourcesContent": ["function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,6BAA6B,GAAG,EAAE,iBAAiB;IACxD,IAAI,kBAAkB,GAAG,CAAC,MAAM;QAC5B,MAAM,IAAI,UAAU;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40swc/helpers/esm/_class_private_field_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,0BAA0B,GAAG,EAAE,UAAU,EAAE,KAAK;IACrD,CAAA,GAAA,0KAAA,CAAA,IAA4B,AAAD,EAAE,KAAK;IAClC,WAAW,GAAG,CAAC,KAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40swc/helpers/esm/_class_apply_descriptor_set.js"], "sourcesContent": ["function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU,EAAE,KAAK;IAC5D,IAAI,WAAW,GAAG,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;SAC7C;QACD,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QACA,WAAW,KAAK,GAAG;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40swc/helpers/esm/_class_private_field_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU,EAAE,KAAK;IACzD,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU,YAAY;IAClD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40radix-ui/react-collection/src/collection-legacy.tsx", "file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40radix-ui/react-collection/src/collection.tsx", "file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40radix-ui/react-collection/src/ordered-dictionary.ts"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement | null>;\n    itemMap: Map<\n      React.RefObject<ItemElement | null>,\n      { ref: React.RefObject<ItemElement | null> } & ItemData\n    >;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\nimport type { EntryOf } from './ordered-dictionary';\nimport { OrderedDict } from './ordered-dictionary';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\ninterface BaseItemData {\n  id?: string;\n}\n\ntype ItemDataWithElement<\n  ItemData extends BaseItemData,\n  ItemElement extends HTMLElement,\n> = ItemData & {\n  element: ItemElement;\n};\n\ntype ItemMap<ItemElement extends HTMLElement, ItemData extends BaseItemData> = OrderedDict<\n  ItemElement,\n  ItemDataWithElement<ItemData, ItemElement>\n>;\n\nfunction createCollection<\n  ItemElement extends HTMLElement,\n  ItemData extends BaseItemData = BaseItemData,\n>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionElement: CollectionElement | null;\n    collectionRef: React.Ref<CollectionElement | null>;\n    collectionRefObject: React.RefObject<CollectionElement | null>;\n    itemMap: ItemMap<ItemElement, ItemData>;\n    setItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>;\n  };\n\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0,\n    }\n  );\n\n  type CollectionState = [\n    ItemMap: ItemMap<ItemElement, ItemData>,\n    SetItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>,\n  ];\n\n  const CollectionProvider: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state?: CollectionState;\n  }> = ({ state, ...props }) => {\n    return state ? (\n      <CollectionProviderImpl {...props} state={state} />\n    ) : (\n      <CollectionInit {...props} />\n    );\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  const CollectionInit: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n  }> = (props) => {\n    const state = useInitCollection();\n    return <CollectionProviderImpl {...props} state={state} />;\n  };\n  CollectionInit.displayName = PROVIDER_NAME + 'Init';\n\n  const CollectionProviderImpl: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state: CollectionState;\n  }> = (props) => {\n    const { scope, children, state } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const [collectionElement, setCollectionElement] = React.useState<CollectionElement | null>(\n      null\n    );\n    const composeRefs = useComposedRefs(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n\n    React.useEffect(() => {\n      if (!collectionElement) return;\n\n      const observer = getChildListObserver(() => {\n        // setItemMap((map) => {\n        //   const copy = new OrderedDict(map).toSorted(([, a], [, b]) =>\n        //     !a.element || !b.element ? 0 : isElementPreceding(a.element, b.element) ? -1 : 1\n        //   );\n        //   // check if the order has changed\n        //   let index = -1;\n        //   for (const entry of copy) {\n        //     index++;\n        //     const key = map.keyAt(index)!;\n        //     const [copyKey] = entry;\n        //     if (key !== copyKey) {\n        //       // order has changed!\n        //       return copy;\n        //     }\n        //   }\n        //   return map;\n        // });\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true,\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n\n    return (\n      <CollectionContextProvider\n        scope={scope}\n        itemMap={itemMap}\n        setItemMap={setItemMap}\n        collectionRef={composeRefs}\n        collectionRefObject={ref}\n        collectionElement={collectionElement}\n      >\n        {children}\n      </CollectionContextProvider>\n    );\n  };\n\n  CollectionProviderImpl.displayName = PROVIDER_NAME + 'Impl';\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const [element, setElement] = React.useState<ItemElement | null>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      const { setItemMap } = context;\n\n      const itemDataRef = React.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n\n      React.useEffect(() => {\n        const itemData = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n\n          if (!map.has(element)) {\n            map.set(element, { ...(itemData as unknown as ItemData), element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n\n          return map\n            .set(element, { ...(itemData as unknown as ItemData), element })\n            .toSorted(sortByDocumentPosition);\n        });\n\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs as any}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useInitCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useInitCollection() {\n    return React.useState<ItemMap<ItemElement, ItemData>>(new OrderedDict());\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const { itemMap } = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    return itemMap;\n  }\n\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection,\n  };\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n\nfunction shallowEqual(a: any, b: any) {\n  if (a === b) return true;\n  if (typeof a !== 'object' || typeof b !== 'object') return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\n\nfunction isElementPreceding(a: Element, b: Element) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\n\nfunction sortByDocumentPosition<E extends HTMLElement, T extends BaseItemData>(\n  a: EntryOf<ItemMap<E, T>>,\n  b: EntryOf<ItemMap<E, T>>\n) {\n  return !a[1].element || !b[1].element\n    ? 0\n    : isElementPreceding(a[1].element, b[1].element)\n      ? -1\n      : 1;\n}\n\nfunction getChildListObserver(callback: () => void) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === 'childList') {\n        callback();\n        return;\n      }\n    }\n  });\n\n  return observer;\n}\n", "// Not a real member because it shouldn't be accessible, but the super class\n// calls `set` which needs to read the instanciation state, so it can't be a\n// private member.\nconst __instanciated = new WeakMap<OrderedDict<any, any>, boolean>();\nexport class OrderedDict<K, V> extends Map<K, V> {\n  #keys: K[];\n\n  constructor(iterable?: Iterable<readonly [K, V]> | null | undefined);\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n\n  set(key: K, value: V) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n\n  insert(index: number, key: K, value: V) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n\n    if (safeIndex === this.size || (has && safeIndex === this.size - 1) || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n\n    const size = this.size + (has ? 0 : 1);\n\n    // If you insert at, say, -2, without this bit you'd replace the\n    // second-to-last item and push the rest up one, which means the new item is\n    // 3rd to last. This isn't very intuitive; inserting at -2 is more like\n    // saying \"make this item the second to last\".\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n\n    const keys = [...this.#keys];\n    let nextValue: V | undefined;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i]!;\n        if (keys[i] === key) {\n          nextKey = keys[i + 1]!;\n        }\n        if (has) {\n          // delete first to ensure that the item is moved to the end\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1]!;\n        const currentValue = nextValue!;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n\n  with(index: number, key: K, value: V) {\n    const copy = new OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n\n  before(key: K) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n\n  after(key: K) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n\n  first() {\n    return this.entryAt(0);\n  }\n\n  last() {\n    return this.entryAt(-1);\n  }\n\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n\n  delete(key: K) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n\n  deleteAt(index: number) {\n    const key = this.keyAt(index);\n    if (key !== undefined) {\n      return this.delete(key);\n    }\n    return false;\n  }\n\n  at(index: number) {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return this.get(key);\n    }\n  }\n\n  entryAt(index: number): [K, V] | undefined {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return [key, this.get(key)!];\n    }\n  }\n\n  indexOf(key: K) {\n    return this.#keys.indexOf(key);\n  }\n\n  keyAt(index: number) {\n    return at(this.#keys, index);\n  }\n\n  from(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n\n  keyFrom(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n\n  find(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return undefined;\n  }\n\n  findIndex(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n\n  filter<KK extends K, VV extends V>(\n    predicate: (entry: [K, V], index: number, dict: OrderedDict<K, V>) => entry is [KK, VV],\n    thisArg?: any\n  ): OrderedDict<KK, VV>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ): OrderedDict<K, V>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    const entries: Array<[K, V]> = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  map<U>(\n    callbackfn: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => U,\n    thisArg?: any\n  ): OrderedDict<K, U> {\n    const entries: [K, U][] = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduce<U>(\n    callbackfn: (\n      previousValue: U,\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduce<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0)!;\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduceRight<U>(\n    callbackfn: (\n      previousValue: [K, V],\n      currentValue: U,\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduceRight<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1)!;\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index)!;\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n\n  toSorted(compareFn?: (a: [K, V], b: [K, V]) => number): OrderedDict<K, V> {\n    const entries = [...this.entries()].sort(compareFn);\n    return new OrderedDict(entries);\n  }\n\n  toReversed(): OrderedDict<K, V> {\n    const reversed = new OrderedDict<K, V>();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n\n  toSpliced(start: number, deleteCount?: number): OrderedDict<K, V>;\n  toSpliced(start: number, deleteCount: number, ...items: [K, V][]): OrderedDict<K, V>;\n\n  toSpliced(...args: [start: number, deleteCount: number, ...items: [K, V][]]) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new OrderedDict(entries);\n  }\n\n  slice(start?: number, end?: number) {\n    const result = new OrderedDict<K, V>();\n    let stop = this.size - 1;\n\n    if (start === undefined) {\n      return result;\n    }\n\n    if (start < 0) {\n      start = start + this.size;\n    }\n\n    if (end !== undefined && end > 0) {\n      stop = end - 1;\n    }\n\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      result.set(key, element);\n    }\n    return result;\n  }\n\n  every(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n\n  some(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n}\n\nexport type KeyOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<infer K, any> ? K : never;\nexport type ValueOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<any, infer V> ? V : never;\nexport type EntryOf<D extends OrderedDict<any, any>> = [KeyOf<D>, ValueOf<D>];\nexport type KeyFrom<E extends EntryOf<any>> = E[0];\nexport type ValueFrom<E extends EntryOf<any>> = E[1];\n\nfunction at<T>(array: ArrayLike<T>, index: number): T | undefined {\n  if ('at' in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? undefined : array[actualIndex];\n}\n\nfunction toSafeIndex(array: ArrayLike<any>, index: number) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\n\nfunction toSafeInteger(number: number) {\n  // eslint-disable-next-line no-self-compare\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n"], "names": ["React", "createContextScope", "useComposedRefs", "createSlot", "jsx", "createCollection", "createContextScope", "React", "useComposedRefs", "createSlot", "itemData"], "mappings": ";;;;;;;;AAAA,OAAO,WAAW;AAClB,SAAS,0BAA0B;AACnC,SAAS,uBAAuB;AAChC,SAAS,kBAA6B;AAuChC;;;;;;;;;;;AA1BN,SAAS,iBAAiE,IAAA,EAAc;IAKtF,MAAM,gBAAgB,OAAO;IAC7B,MAAM,CAAC,yBAAyB,qBAAqB,CAAA,8KAAI,qBAAA,EAAmB,aAAa;IAUzF,MAAM,CAAC,wBAAwB,oBAAoB,CAAA,GAAI,wBACrD,eACA;QAAE,eAAe;YAAE,SAAS;QAAK;QAAG,SAAS,aAAA,GAAA,IAAI,IAAI;IAAE;IAGzD,MAAM,qBAA2E,CAAC,UAAU;QAC1F,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,oKAAM,UAAA,CAAM,MAAA,CAA0B,IAAI;QAChD,MAAM,wKAAU,UAAA,CAAM,MAAA,CAAgC,aAAA,GAAA,IAAI,IAAI,CAAC,EAAE,OAAA;QACjE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,wBAAA;YAAuB;YAAc;YAAkB,eAAe;YACpE;QAAA,CACH;IAEJ;IAEA,mBAAmB,WAAA,GAAc;IAMjC,MAAM,uBAAuB,OAAO;IAEpC,MAAM,6LAAqB,aAAA,EAAW,oBAAoB;IAC1D,MAAM,+KAAiB,UAAA,CAAM,UAAA,CAC3B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,UAAU,qBAAqB,sBAAsB,KAAK;QAChE,MAAM,kMAAe,kBAAA,EAAgB,cAAc,QAAQ,aAAa;QACxE,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;YAAmB,KAAK;YAAe;QAAA,CAAS;IAC1D;IAGF,eAAe,WAAA,GAAc;IAM7B,MAAM,iBAAiB,OAAO;IAC9B,MAAM,iBAAiB;IAOvB,MAAM,iMAAyB,aAAA,EAAW,cAAc;IACxD,MAAM,mLAAqB,UAAA,CAAM,UAAA,CAC/B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,SAAS,CAAA,GAAI;QACzC,MAAM,oKAAM,UAAA,CAAM,MAAA,CAAoB,IAAI;QAC1C,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;QACtD,MAAM,UAAU,qBAAqB,gBAAgB,KAAK;QAE1D,6JAAA,CAAA,UAAA,CAAM,SAAA;6DAAU,MAAM;gBACpB,QAAQ,OAAA,CAAQ,GAAA,CAAI,KAAK;oBAAE;oBAAK,GAAI,QAAA;gBAAiC,CAAC;gBACtE;qEAAO,IAAM,KAAK,QAAQ,OAAA,CAAQ,MAAA,CAAO,GAAG;;YAC9C,CAAC;;QAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,wBAAA;YAAwB,GAAG;gBAAE,CAAC,cAAc,CAAA,EAAG;YAAG,CAAA;YAAG,KAAK;YACxD;QAAA,CACH;IAEJ;IAGF,mBAAmB,WAAA,GAAc;IAMjC,SAAS,cAAc,KAAA,EAAY;QACjC,MAAM,UAAU,qBAAqB,OAAO,sBAAsB,KAAK;QAEvE,MAAM,yKAAW,UAAA,CAAM,WAAA;oEAAY,MAAM;gBACvC,MAAM,iBAAiB,QAAQ,aAAA,CAAc,OAAA;gBAC7C,IAAI,CAAC,eAAgB,CAAA,OAAO,CAAC,CAAA;gBAC7B,MAAM,eAAe,MAAM,IAAA,CAAK,eAAe,gBAAA,CAAiB,IAAkB,OAAd,cAAc,EAAA,EAAG,CAAC;gBACtF,MAAM,QAAQ,MAAM,IAAA,CAAK,QAAQ,OAAA,CAAQ,MAAA,CAAO,CAAC;gBACjD,MAAM,eAAe,MAAM,IAAA;yFACzB,CAAC,GAAG,IAAM,aAAa,OAAA,CAAQ,EAAE,GAAA,CAAI,OAAQ,IAAI,aAAa,OAAA,CAAQ,EAAE,GAAA,CAAI,OAAQ;;gBAEtF,OAAO;YACT;mEAAG;YAAC,QAAQ,aAAA;YAAe,QAAQ,OAAO;SAAC;QAE3C,OAAO;IACT;IAEA,OAAO;QACL;YAAE,UAAU;YAAoB,MAAM;YAAgB,UAAU;QAAmB;QACnF;QACA;KACF;AACF;;;;;;AE9HA,IAAM,iBAAiB,aAAA,GAAA,IAAI,QAAwC;AAC5D,IAAM,oDAAN,MAAM,qBAA0B,IAAU;IAU/C,IAAI,GAAA,EAAQ,KAAA,EAAU;QACpB,IAAI,eAAe,GAAA,CAAI,IAAI,GAAG;YAC5B,IAAI,IAAA,CAAK,GAAA,CAAI,GAAG,GAAG;gBACjB,iLAAA,IAAA,EAAK,MAAA,kLAAM,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG,CAAC,CAAA,GAAI;YACxC,OAAO;gBACL,iLAAA,IAAA,EAAK,OAAM,IAAA,CAAK,GAAG;YACrB;QACF;QACA,KAAA,CAAM,IAAI,KAAK,KAAK;QACpB,OAAO,IAAA;IACT;IAEA,OAAO,KAAA,EAAe,GAAA,EAAQ,KAAA,EAAU;QACtC,MAAM,MAAM,IAAA,CAAK,GAAA,CAAI,GAAG;QACxB,MAAM,0LAAS,IAAA,EAAK,OAAM,MAAA;QAC1B,MAAM,gBAAgB,cAAc,KAAK;QACzC,IAAI,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;QAChE,MAAM,YAAY,cAAc,KAAK,eAAe,SAAS,CAAA,IAAK;QAElE,IAAI,cAAc,IAAA,CAAK,IAAA,IAAS,OAAO,cAAc,IAAA,CAAK,IAAA,GAAO,KAAM,cAAc,CAAA,GAAI;YACvF,IAAA,CAAK,GAAA,CAAI,KAAK,KAAK;YACnB,OAAO,IAAA;QACT;QAEA,MAAM,OAAO,IAAA,CAAK,IAAA,GAAA,CAAQ,MAAM,IAAI,CAAA;QAMpC,IAAI,gBAAgB,GAAG;YACrB;QACF;QAEA,MAAM,OAAO,CAAC;gMAAG,IAAA,EAAK,KAAK;SAAA;QAC3B,IAAI;QACJ,IAAI,aAAa;QACjB,IAAA,IAAS,IAAI,aAAa,IAAI,MAAM,IAAK;YACvC,IAAI,gBAAgB,GAAG;gBACrB,IAAI,UAAU,IAAA,CAAK,CAAC,CAAA;gBACpB,IAAI,IAAA,CAAK,CAAC,CAAA,KAAM,KAAK;oBACnB,UAAU,IAAA,CAAK,IAAI,CAAC,CAAA;gBACtB;gBACA,IAAI,KAAK;oBAEP,IAAA,CAAK,MAAA,CAAO,GAAG;gBACjB;gBACA,YAAY,IAAA,CAAK,GAAA,CAAI,OAAO;gBAC5B,IAAA,CAAK,GAAA,CAAI,KAAK,KAAK;YACrB,OAAO;gBACL,IAAI,CAAC,cAAc,IAAA,CAAK,IAAI,CAAC,CAAA,KAAM,KAAK;oBACtC,aAAa;gBACf;gBACA,MAAM,aAAa,IAAA,CAAK,aAAa,IAAI,IAAI,CAAC,CAAA;gBAC9C,MAAM,eAAe;gBACrB,YAAY,IAAA,CAAK,GAAA,CAAI,UAAU;gBAC/B,IAAA,CAAK,MAAA,CAAO,UAAU;gBACtB,IAAA,CAAK,GAAA,CAAI,YAAY,YAAY;YACnC;QACF;QACA,OAAO,IAAA;IACT;IAEA,KAAK,KAAA,EAAe,GAAA,EAAQ,KAAA,EAAU;QACpC,MAAM,OAAO,IAAI,aAAY,IAAI;QACjC,KAAK,MAAA,CAAO,OAAO,KAAK,KAAK;QAC7B,OAAO;IACT;IAEA,OAAO,GAAA,EAAQ;QACb,MAAM,yLAAQ,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG,IAAI;QACxC,IAAI,QAAQ,GAAG;YACb,OAAO,KAAA;QACT;QACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAK;IAC3B;IAAA;;GAAA,GAKA,UAAU,GAAA,EAAQ,MAAA,EAAW,KAAA,EAAU;QACrC,MAAM,yLAAQ,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG;QACpC,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,IAAA;QACT;QACA,OAAO,IAAA,CAAK,MAAA,CAAO,OAAO,QAAQ,KAAK;IACzC;IAEA,MAAM,GAAA,EAAQ;QACZ,IAAI,yLAAQ,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG;QAClC,QAAQ,UAAU,CAAA,KAAM,UAAU,IAAA,CAAK,IAAA,GAAO,IAAI,CAAA,IAAK,QAAQ;QAC/D,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAK;IAC3B;IAAA;;GAAA,GAKA,SAAS,GAAA,EAAQ,MAAA,EAAW,KAAA,EAAU;QACpC,MAAM,yLAAQ,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG;QACpC,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,IAAA;QACT;QACA,OAAO,IAAA,CAAK,MAAA,CAAO,QAAQ,GAAG,QAAQ,KAAK;IAC7C;IAEA,QAAQ;QACN,OAAO,IAAA,CAAK,OAAA,CAAQ,CAAC;IACvB;IAEA,OAAO;QACL,OAAO,IAAA,CAAK,OAAA,CAAQ,CAAA,CAAE;IACxB;IAEA,QAAQ;+LACD,OAAQ,CAAC,CAAA;QACd,OAAO,KAAA,CAAM,MAAM;IACrB;IAEA,OAAO,GAAA,EAAQ;QACb,MAAM,UAAU,KAAA,CAAM,OAAO,GAAG;QAChC,IAAI,SAAS;YACX,iLAAA,IAAA,EAAK,OAAM,MAAA,kLAAO,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG,GAAG,CAAC;QAC9C;QACA,OAAO;IACT;IAEA,SAAS,KAAA,EAAe;QACtB,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;QAC5B,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,GAAG;QACxB;QACA,OAAO;IACT;IAEA,GAAG,KAAA,EAAe;QAChB,MAAM,MAAM,oLAAG,IAAA,EAAK,QAAO,KAAK;QAChC,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO,IAAA,CAAK,GAAA,CAAI,GAAG;QACrB;IACF;IAEA,QAAQ,KAAA,EAAmC;QACzC,MAAM,MAAM,oLAAG,IAAA,EAAK,QAAO,KAAK;QAChC,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO;gBAAC;gBAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAE;aAAA;QAC7B;IACF;IAEA,QAAQ,GAAA,EAAQ;QACd,wLAAO,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG;IAC/B;IAEA,MAAM,KAAA,EAAe;QACnB,OAAO,oLAAG,IAAA,EAAK,QAAO,KAAK;IAC7B;IAEA,KAAK,GAAA,EAAQ,MAAA,EAAgB;QAC3B,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,GAAG;QAC9B,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,OAAO,EAAG,CAAA,OAAO;QACrB,IAAI,QAAQ,IAAA,CAAK,IAAA,CAAM,CAAA,OAAO,IAAA,CAAK,IAAA,GAAO;QAC1C,OAAO,IAAA,CAAK,EAAA,CAAG,IAAI;IACrB;IAEA,QAAQ,GAAA,EAAQ,MAAA,EAAgB;QAC9B,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,GAAG;QAC9B,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,OAAO,EAAG,CAAA,OAAO;QACrB,IAAI,QAAQ,IAAA,CAAK,IAAA,CAAM,CAAA,OAAO,IAAA,CAAK,IAAA,GAAO;QAC1C,OAAO,IAAA,CAAK,KAAA,CAAM,IAAI;IACxB;IAEA,KACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO,KAAA;IACT;IAEA,UACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO,CAAA;IACT;IAYA,OACE,SAAA,EACA,OAAA,EACA;QACA,MAAM,UAAyB,CAAC,CAAA;QAChC,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,QAAQ,IAAA,CAAK,KAAK;YACpB;YACA;QACF;QACA,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,IACE,UAAA,EACA,OAAA,EACmB;QACnB,MAAM,UAAoB,CAAC,CAAA;QAC3B,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,QAAQ,IAAA,CAAK;gBAAC,KAAA,CAAM,CAAC,CAAA;gBAAG,QAAQ,KAAA,CAAM,YAAY,SAAS;oBAAC;oBAAO;oBAAO,IAAI;iBAAC,CAAC;aAAC;YACjF;QACF;QACA,OAAO,IAAI,aAAY,OAAO;IAChC;IA6BA,SAUE;QAVF,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;YACK,KADL,QAAA,SAAA,CAAA,KACK;;QAUH,MAAM,CAAC,YAAY,YAAY,CAAA,GAAI;QACnC,IAAI,QAAQ;QACZ,IAAI,cAAc,kEAAgB,IAAA,CAAK,EAAA,CAAG,CAAC;QAC3C,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,UAAU,KAAK,KAAK,MAAA,KAAW,GAAG;gBACpC,cAAc;YAChB,OAAO;gBACL,cAAc,QAAQ,KAAA,CAAM,YAAY,IAAA,EAAM;oBAAC;oBAAa;oBAAO;oBAAO,IAAI;iBAAC;YACjF;YACA;QACF;QACA,OAAO;IACT;IA6BA,cAUE;QAVF,IAAA,IAAA,OAAA,UAAA,QAAA,AACK,OADL,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;iBAAA,QAAA,SAAA,CAAA,KACK;;QAUH,MAAM,CAAC,YAAY,YAAY,CAAA,GAAI;QACnC,IAAI,iEAAc,eAAgB,IAAA,CAAK,EAAA,CAAG,CAAA,CAAE;QAC5C,IAAA,IAAS,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,SAAS,GAAG,QAAS;YACnD,MAAM,QAAQ,IAAA,CAAK,EAAA,CAAG,KAAK;YAC3B,IAAI,UAAU,IAAA,CAAK,IAAA,GAAO,KAAK,KAAK,MAAA,KAAW,GAAG;gBAChD,cAAc;YAChB,OAAO;gBACL,cAAc,QAAQ,KAAA,CAAM,YAAY,IAAA,EAAM;oBAAC;oBAAa;oBAAO;oBAAO,IAAI;iBAAC;YACjF;QACF;QACA,OAAO;IACT;IAEA,SAAS,SAAA,EAAiE;QACxE,MAAM,UAAU,CAAC;eAAG,IAAA,CAAK,OAAA,CAAQ,CAAC;SAAA,CAAE,IAAA,CAAK,SAAS;QAClD,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,aAAgC;QAC9B,MAAM,WAAW,IAAI,aAAkB;QACvC,IAAA,IAAS,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,SAAS,GAAG,QAAS;YACnD,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;YAC5B,MAAM,UAAU,IAAA,CAAK,GAAA,CAAI,GAAG;YAC5B,SAAS,GAAA,CAAI,KAAK,OAAO;QAC3B;QACA,OAAO;IACT;IAKA,YAA6E;QAA7E,IAAA,IAAA,OAAA,UAAA,QAAa,AAAb,OAAA,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;iBAAA,QAAA,SAAA,CAAA,KAAa;;QACX,MAAM,UAAU,CAAC;eAAG,IAAA,CAAK,OAAA,CAAQ,CAAC;SAAA;QAClC,QAAQ,MAAA,CAAO,GAAG,IAAI;QACtB,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,MAAM,KAAA,EAAgB,GAAA,EAAc;QAClC,MAAM,SAAS,IAAI,aAAkB;QACrC,IAAI,OAAO,IAAA,CAAK,IAAA,GAAO;QAEvB,IAAI,UAAU,KAAA,GAAW;YACvB,OAAO;QACT;QAEA,IAAI,QAAQ,GAAG;YACb,QAAQ,QAAQ,IAAA,CAAK,IAAA;QACvB;QAEA,IAAI,QAAQ,KAAA,KAAa,MAAM,GAAG;YAChC,OAAO,MAAM;QACf;QAEA,IAAA,IAAS,QAAQ,OAAO,SAAS,MAAM,QAAS;YAC9C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;YAC5B,MAAM,UAAU,IAAA,CAAK,GAAA,CAAI,GAAG;YAC5B,OAAO,GAAA,CAAI,KAAK,OAAO;QACzB;QACA,OAAO;IACT;IAEA,MACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,CAAC,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC5D,OAAO;YACT;YACA;QACF;QACA,OAAO;IACT;IAEA,KACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO;IACT;IA9aA,YAAY,OAAA,CAA+C;QACzD,KAAA,CAAM,OAAO;;wBAJf;;+LAKO,OAAQ,CAAC;eAAG,KAAA,CAAM,KAAK,CAAC;SAAA;QAC7B,eAAe,GAAA,CAAI,IAAA,EAAM,IAAI;IAC/B;AA2aF;AAUA,SAAS,GAAM,KAAA,EAAqB,KAAA,EAA8B;IAChE,IAAI,QAAQ,MAAM,SAAA,EAAW;QAC3B,OAAO,MAAM,SAAA,CAAU,EAAA,CAAG,IAAA,CAAK,OAAO,KAAK;IAC7C;IACA,MAAM,cAAc,YAAY,OAAO,KAAK;IAC5C,OAAO,gBAAgB,CAAA,IAAK,KAAA,IAAY,KAAA,CAAM,WAAW,CAAA;AAC3D;AAEA,SAAS,YAAY,KAAA,EAAuB,KAAA,EAAe;IACzD,MAAM,SAAS,MAAM,MAAA;IACrB,MAAM,gBAAgB,cAAc,KAAK;IACzC,MAAM,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;IAClE,OAAO,cAAc,KAAK,eAAe,SAAS,CAAA,IAAK;AACzD;AAEA,SAAS,cAAc,MAAA,EAAgB;IAErC,OAAO,WAAW,UAAU,WAAW,IAAI,IAAI,KAAK,KAAA,CAAM,MAAM;AAClE;;ADtbA,SAASK,kBAGP,IAAA,EAAc;IAKd,MAAM,gBAAgB,OAAO;IAC7B,MAAM,CAAC,yBAAyB,qBAAqB,CAAA,8KAAIC,qBAAAA,EAAmB,aAAa;IAUzF,MAAM,CAAC,2BAA2B,oBAAoB,CAAA,GAAI,wBACxD,eACA;QACE,mBAAmB;QACnB,eAAe;YAAE,SAAS;QAAK;QAC/B,qBAAqB;YAAE,SAAS;QAAK;QACrC,SAAS,IAAI,YAAY;QACzB,YAAY,IAAM,KAAA;IACpB;IAQF,MAAM,qBAID;YAAC,EAAE,KAAA,EAAO,GAAG,MAAM,CAAA,KAAM;QAC5B,OAAO,QACL,aAAA,8KAAAF,MAAAA,EAAC,wBAAA;YAAwB,GAAG,KAAA;YAAO;QAAA,CAAc,IAEjD,aAAA,8KAAAA,MAAAA,EAAC,gBAAA;YAAgB,GAAG,KAAA;QAAA,CAAO;IAE/B;IACA,mBAAmB,WAAA,GAAc;IAEjC,MAAM,iBAGD,CAAC,UAAU;QACd,MAAM,QAAQ,kBAAkB;QAChC,OAAO,aAAA,GAAAA,iLAAAA,EAAC,wBAAA;YAAwB,GAAG,KAAA;YAAO;QAAA,CAAc;IAC1D;IACA,eAAe,WAAA,GAAc,gBAAgB;IAE7C,MAAM,yBAID,CAAC,UAAU;QACd,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI;QACnC,MAAM,oKAAMG,UAAAA,CAAM,MAAA,CAA0B,IAAI;QAChD,MAAM,CAAC,mBAAmB,oBAAoB,CAAA,iKAAIA,UAAAA,CAAM,QAAA,CACtD;QAEF,MAAM,iMAAcC,kBAAAA,EAAgB,KAAK,oBAAoB;QAC7D,MAAM,CAAC,SAAS,UAAU,CAAA,GAAI;QAE9BD,wKAAAA,CAAM,SAAA;kEAAU,MAAM;gBACpB,IAAI,CAAC,kBAAmB,CAAA;gBAExB,MAAM,WAAW;mFAAqB,KAkBtC,CAlB4C,AAkB3C;;gBACD,SAAS,OAAA,CAAQ,mBAAmB;oBAClC,WAAW;oBACX,SAAS;gBACX,CAAC;gBACD;0EAAO,MAAM;wBACX,SAAS,UAAA,CAAW;oBACtB;;YACF;iEAAG;YAAC,iBAAiB;SAAC;QAEtB,OACE,aAAA,8KAAAH,MAAAA,EAAC,2BAAA;YACC;YACA;YACA;YACA,eAAe;YACf,qBAAqB;YACrB;YAEC;QAAA;IAGP;IAEA,uBAAuB,WAAA,GAAc,gBAAgB;IAMrD,MAAM,uBAAuB,OAAO;IAEpC,MAAM,6LAAqBK,aAAAA,EAAW,oBAAoB;IAC1D,MAAM,+KAAiBF,UAAAA,CAAM,UAAA,CAC3B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,UAAU,qBAAqB,sBAAsB,KAAK;QAChE,MAAM,kMAAeC,kBAAAA,EAAgB,cAAc,QAAQ,aAAa;QACxE,OAAO,aAAA,GAAAJ,iLAAAA,EAAC,oBAAA;YAAmB,KAAK;YAAe;QAAA,CAAS;IAC1D;IAGF,eAAe,WAAA,GAAc;IAM7B,MAAM,iBAAiB,OAAO;IAC9B,MAAM,iBAAiB;IAOvB,MAAM,iMAAyBK,aAAAA,EAAW,cAAc;IACxD,MAAM,mLAAqBF,UAAAA,CAAM,UAAA,CAC/B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,SAAS,CAAA,GAAI;QACzC,MAAM,oKAAMA,UAAAA,CAAM,MAAA,CAAoB,IAAI;QAC1C,MAAM,CAAC,SAAS,UAAU,CAAA,iKAAIA,UAAAA,CAAM,QAAA,CAA6B,IAAI;QACrE,MAAM,mBAAeC,iMAAAA,EAAgB,cAAc,KAAK,UAAU;QAClE,MAAM,UAAU,qBAAqB,gBAAgB,KAAK;QAE1D,MAAM,EAAE,UAAA,CAAW,CAAA,GAAI;QAEvB,MAAM,4KAAcD,UAAAA,CAAM,MAAA,CAAO,QAAQ;QACzC,IAAI,CAAC,aAAa,YAAY,OAAA,EAAS,QAAQ,GAAG;YAChD,YAAY,OAAA,GAAU;QACxB;QACA,MAAM,mBAAmB,YAAY,OAAA;sKAErCA,UAAAA,CAAM,SAAA;8DAAU,MAAM;gBACpB,MAAMG,YAAW;gBACjB;sEAAW,CAAC,QAAQ;wBAClB,IAAI,CAAC,SAAS;4BACZ,OAAO;wBACT;wBAEA,IAAI,CAAC,IAAI,GAAA,CAAI,OAAO,GAAG;4BACrB,IAAI,GAAA,CAAI,SAAS;gCAAE,GAAIA,SAAAA;gCAAkC;4BAAQ,CAAC;4BAClE,OAAO,IAAI,QAAA,CAAS,sBAAsB;wBAC5C;wBAEA,OAAO,IACJ,GAAA,CAAI,SAAS;4BAAE,GAAIA,SAAAA;4BAAkC;wBAAQ,CAAC,EAC9D,QAAA,CAAS,sBAAsB;oBACpC,CAAC;;gBAED;sEAAO,MAAM;wBACX;8EAAW,CAAC,QAAQ;gCAClB,IAAI,CAAC,WAAW,CAAC,IAAI,GAAA,CAAI,OAAO,GAAG;oCACjC,OAAO;gCACT;gCACA,IAAI,MAAA,CAAO,OAAO;gCAClB,OAAO,IAAI,YAAY,GAAG;4BAC5B,CAAC;;oBACH;;YACF;6DAAG;YAAC;YAAS;YAAkB,UAAU;SAAC;QAE1C,OACE,aAAA,8KAAAN,MAAAA,EAAC,wBAAA;YAAwB,GAAG;gBAAE,CAAC,cAAc,CAAA,EAAG;YAAG,CAAA;YAAG,KAAK;YACxD;QAAA,CACH;IAEJ;IAGF,mBAAmB,WAAA,GAAc;IAMjC,SAAS,oBAAoB;QAC3B,qKAAOG,UAAAA,CAAM,QAAA,CAAyC,IAAI,YAAY,CAAC;IACzE;IAMA,SAAS,cAAc,KAAA,EAAY;QACjC,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,qBAAqB,OAAO,sBAAsB,KAAK;QAE3E,OAAO;IACT;IAEA,MAAM,YAAY;QAChB;QACA;QACA;IACF;IAEA,OAAO;QACL;YAAE,UAAU;YAAoB,MAAM;YAAgB,UAAU;QAAmB;QACnF;KACF;AACF;AAKA,SAAS,aAAa,CAAA,EAAQ,CAAA,EAAQ;IACpC,IAAI,MAAM,EAAG,CAAA,OAAO;IACpB,IAAI,OAAO,MAAM,YAAY,OAAO,MAAM,SAAU,CAAA,OAAO;IAC3D,IAAI,KAAK,QAAQ,KAAK,KAAM,CAAA,OAAO;IACnC,MAAM,QAAQ,OAAO,IAAA,CAAK,CAAC;IAC3B,MAAM,QAAQ,OAAO,IAAA,CAAK,CAAC;IAC3B,IAAI,MAAM,MAAA,KAAW,MAAM,MAAA,CAAQ,CAAA,OAAO;IAC1C,KAAA,MAAW,OAAO,MAAO;QACvB,IAAI,CAAC,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,GAAG,GAAG,EAAG,CAAA,OAAO;QAC1D,IAAI,CAAA,CAAE,GAAG,CAAA,KAAM,CAAA,CAAE,GAAG,CAAA,CAAG,CAAA,OAAO;IAChC;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAY,CAAA,EAAY;IAClD,OAAO,CAAC,CAAA,CAAE,EAAE,uBAAA,CAAwB,CAAC,IAAI,KAAK,2BAAA;AAChD;AAEA,SAAS,uBACP,CAAA,EACA,CAAA,EACA;IACA,OAAO,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,IAAW,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,GAC1B,IACA,mBAAmB,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA,CAAE,CAAC,CAAA,CAAE,OAAO,IAC3C,CAAA,IACA;AACR;AAEA,SAAS,qBAAqB,QAAA,EAAsB;IAClD,MAAM,WAAW,IAAI,iBAAiB,CAAC,kBAAkB;QACvD,KAAA,MAAW,YAAY,cAAe;YACpC,IAAI,SAAS,IAAA,KAAS,aAAa;gBACjC,SAAS;gBACT;YACF;QACF;IACF,CAAC;IAED,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40radix-ui/react-roving-focus/src/roving-focus-group.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,aAAa;AACtB,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,4BAA4B;AACrC,SAAS,oBAAoB;AAgEnB;;;;;;;;;;;;;AA5DV,IAAM,cAAc;AACpB,IAAM,gBAAgB;IAAE,SAAS;IAAO,YAAY;AAAK;AAMzD,IAAM,aAAa;AAGnB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,iLAAI,mBAAA,EAGzD,UAAU;AAGZ,IAAM,CAAC,+BAA+B,2BAA2B,CAAA,8KAAI,qBAAA,EACnE,YACA;IAAC,qBAAqB;CAAA;AA+BxB,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,8BAAkD,UAAU;AAK9D,IAAM,iLAAyB,aAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,uBAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;YAAgB,OAAO,MAAM,uBAAA;YAC5B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;gBAAsB,GAAG,KAAA;gBAAO,KAAK;YAAA,CAAc;QAAA,CACtD;IAAA,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAgB/B,IAAM,qLAA6B,aAAA,CAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EACJ,uBAAA,EACA,WAAA,EACA,OAAO,KAAA,EACP,GAAA,EACA,kBAAkB,oBAAA,EAClB,uBAAA,EACA,wBAAA,EACA,YAAA,EACA,4BAA4B,KAAA,EAC5B,GAAG,YACL,GAAI;IACJ,MAAM,mKAAY,UAAA,CAAoC,IAAI;IAC1D,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,yLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,mMAAI,uBAAA,EAAqB;QACnE,MAAM;QACN,sFAAa,0BAA2B;QACxC,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,iKAAU,WAAA,CAAS,KAAK;IACpE,MAAM,mBAAmB,2MAAA,EAAe,YAAY;IACpD,MAAM,WAAW,cAAc,uBAAuB;IACtD,MAAM,gLAAwB,SAAA,CAAO,KAAK;IAC1C,MAAM,CAAC,qBAAqB,sBAAsB,CAAA,iKAAU,WAAA,CAAS,CAAC;kKAEhE,YAAA;0CAAU,MAAM;YACpB,MAAM,OAAO,IAAI,OAAA;YACjB,IAAI,MAAM;gBACR,KAAK,gBAAA,CAAiB,aAAa,gBAAgB;gBACnD;sDAAO,IAAM,KAAK,mBAAA,CAAoB,aAAa,gBAAgB;;YACrE;QACF;yCAAG;QAAC,gBAAgB;KAAC;IAErB,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,KAAK;QACL;QACA;QACA,2KAAmB,cAAA;gDACjB,CAAC,YAAc,oBAAoB,SAAS;+CAC5C;YAAC,mBAAmB;SAAA;QAEtB,8KAAsB,cAAA;gDAAY,IAAM,oBAAoB,IAAI;+CAAG,CAAC,CAAC;QACrE,oBAA0B,4KAAA;gDACxB,IAAM;wDAAuB,CAAC,YAAc,YAAY,CAAC;;+CACzD,CAAC,CAAA;QAEH,qLAA6B,cAAA;gDAC3B,IAAM;wDAAuB,CAAC,YAAc,YAAY,CAAC;;+CACzD,CAAC,CAAA;QAGH,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,UAAU,oBAAoB,wBAAwB,IAAI,CAAA,IAAK;YAC/D,oBAAkB;YACjB,GAAG,UAAA;YACJ,KAAK;YACL,OAAO;gBAAE,SAAS;gBAAQ,GAAG,MAAM,KAAA;YAAM;YACzC,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,MAAM;gBACzD,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;gBAKtD,MAAM,kBAAkB,CAAC,gBAAgB,OAAA;gBAEzC,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,IAAiB,mBAAmB,CAAC,kBAAkB;oBAChF,MAAM,kBAAkB,IAAI,YAAY,aAAa,aAAa;oBAClE,MAAM,aAAA,CAAc,aAAA,CAAc,eAAe;oBAEjD,IAAI,CAAC,gBAAgB,gBAAA,EAAkB;wBACrC,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;wBACxD,MAAM,aAAa,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,MAAM;wBACnD,MAAM,cAAc,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,EAAA,KAAO,gBAAgB;wBACrE,MAAM,iBAAiB;4BAAC;4BAAY,aAAa;+BAAG,KAAK;yBAAA,CAAE,MAAA,CACzD;wBAEF,MAAM,iBAAiB,eAAe,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;wBACrE,WAAW,gBAAgB,yBAAyB;oBACtD;gBACF;gBAEA,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,SAAQ,0LAAA,EAAqB,MAAM,MAAA,EAAQ,IAAM,oBAAoB,KAAK,CAAC;QAAA;IAC7E;AAGN,CAAC;AAMD,IAAM,YAAY;AAalB,IAAM,qLAA6B,aAAA,CACjC,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EACJ,uBAAA,EACA,YAAY,IAAA,EACZ,SAAS,KAAA,EACT,SAAA,EACA,QAAA,EACA,GAAG,WACL,GAAI;IACJ,MAAM,+KAAS,QAAA,CAAM;IACrB,MAAM,KAAK,aAAa;IACxB,MAAM,UAAU,sBAAsB,WAAW,uBAAuB;IACxE,MAAM,mBAAmB,QAAQ,gBAAA,KAAqB;IACtD,MAAM,WAAW,cAAc,uBAAuB;IAEtD,MAAM,EAAE,kBAAA,EAAoB,qBAAA,EAAuB,gBAAA,CAAiB,CAAA,GAAI;kKAElE,YAAA;0CAAU,MAAM;YACpB,IAAI,WAAW;gBACb,mBAAmB;gBACnB;sDAAO,IAAM,sBAAsB;;YACrC;QACF;yCAAG;QAAC;QAAW;QAAoB,qBAAqB;KAAC;IAEzD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QACC,OAAO;QACP;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;YACC,UAAU,mBAAmB,IAAI,CAAA;YACjC,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;YACL,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,UAAW,CAAA,MAAM,cAAA,CAAe;qBAEhC,QAAQ,WAAA,CAAY,EAAE;YAC7B,CAAC;YACD,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,QAAQ,WAAA,CAAY,EAAE,CAAC;YAC1E,WAAW,2LAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI,MAAM,GAAA,KAAQ,SAAS,MAAM,QAAA,EAAU;oBACzC,QAAQ,cAAA,CAAe;oBACvB;gBACF;gBAEA,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,CAAe,CAAA;gBAE1C,MAAM,cAAc,eAAe,OAAO,QAAQ,WAAA,EAAa,QAAQ,GAAG;gBAE1E,IAAI,gBAAgB,KAAA,GAAW;oBAC7B,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,QAAA,CAAU,CAAA;oBACtE,MAAM,cAAA,CAAe;oBACrB,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;oBACxD,IAAI,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;oBAE1D,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;yBAAA,IAC1C,gBAAgB,UAAU,gBAAgB,QAAQ;wBACzD,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;wBACnD,MAAM,eAAe,eAAe,OAAA,CAAQ,MAAM,aAAa;wBAC/D,iBAAiB,QAAQ,IAAA,GACrB,UAAU,gBAAgB,eAAe,CAAC,IAC1C,eAAe,KAAA,CAAM,eAAe,CAAC;oBAC3C;oBAMA,WAAW,IAAM,WAAW,cAAc,CAAC;gBAC7C;YACF,CAAC;YAEA,UAAA,OAAO,aAAa,aACjB,SAAS;gBAAE;gBAAkB,YAAY,oBAAoB;YAAK,CAAC,IACnE;QAAA;IACN;AAGN;AAGF,qBAAqB,WAAA,GAAc;AAKnC,IAAM,0BAAuD;IAC3D,WAAW;IAAQ,SAAS;IAC5B,YAAY;IAAQ,WAAW;IAC/B,QAAQ;IAAS,MAAM;IACvB,UAAU;IAAQ,KAAK;AACzB;AAEA,SAAS,qBAAqB,GAAA,EAAa,GAAA,EAAiB;IAC1D,IAAI,QAAQ,MAAO,CAAA,OAAO;IAC1B,OAAO,QAAQ,cAAc,eAAe,QAAQ,eAAe,cAAc;AACnF;AAIA,SAAS,eAAe,KAAA,EAA4B,WAAA,EAA2B,GAAA,EAAiB;IAC9F,MAAM,MAAM,qBAAqB,MAAM,GAAA,EAAK,GAAG;IAC/C,IAAI,gBAAgB,cAAc;QAAC;QAAa,YAAY;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACpF,IAAI,gBAAgB,gBAAgB;QAAC;QAAW,WAAW;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACnF,OAAO,uBAAA,CAAwB,GAAG,CAAA;AACpC;AAEA,SAAS,WAAW,UAAA;QAA2B,iFAAgB,OAAO;IACpE,MAAM,6BAA6B,SAAS,aAAA;IAC5C,KAAA,MAAW,aAAa,WAAY;QAElC,IAAI,cAAc,2BAA4B,CAAA;QAC9C,UAAU,KAAA,CAAM;YAAE;QAAc,CAAC;QACjC,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;IAC7D;AACF;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAM,OAAO;AACb,IAAM,OAAO", "debugId": null}}, {"offset": {"line": 1980, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/%40radix-ui/react-tabs/src/tabs.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: TABS_NAME,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n"], "names": ["Root"], "mappings": ";;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,0BAA0B;AACnC,SAAS,mCAAmC;AAC5C,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAE1B,SAAS,oBAAoB;AAC7B,SAAS,4BAA4B;AACrC,SAAS,aAAa;AAoFd;;;;;;;;;;;;;AA5ER,IAAM,YAAY;AAGlB,IAAM,CAAC,mBAAmB,eAAe,CAAA,8KAAI,qBAAA,EAAmB,WAAW;mLACzE,8BAAA;CACD;AACD,IAAM,8MAA2B,8BAAA,CAA4B;AAW7D,IAAM,CAAC,cAAc,cAAc,CAAA,GAAI,kBAAoC,SAAS;AA6BpF,IAAM,qKAAa,aAAA,CACjB,CAAC,OAA+B,iBAAiB;IAC/C,MAAM,EACJ,WAAA,EACA,OAAO,SAAA,EACP,aAAA,EACA,YAAA,EACA,cAAc,YAAA,EACd,GAAA,EACA,iBAAiB,WAAA,EACjB,GAAG,WACL,GAAI;IACJ,MAAM,yLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAI,uNAAA,EAAqB;QAC7C,MAAM;QACN,UAAU;QACV,gEAAa,eAAgB;QAC7B,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,cAAA;QACC,OAAO;QACP,8KAAQ,QAAA,CAAM;QACd;QACA,eAAe;QACf;QACA,KAAK;QACL;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,KAAK;YACL,oBAAkB;YACjB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,KAAK,WAAA,GAAc;AAMnB,IAAM,gBAAgB;AAOtB,IAAM,yKAAiB,aAAA,CACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EAAE,WAAA,EAAa,OAAO,IAAA,EAAM,GAAG,UAAU,CAAA,GAAI;IACnD,MAAM,UAAU,eAAe,eAAe,WAAW;IACzD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAkB,sLAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,aAAa,QAAQ,WAAA;QACrB,KAAK,QAAQ,GAAA;QACb;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,MAAK;YACL,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,eAAe;AAQrB,IAAM,4KAAoB,aAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IAClE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,iLAAkB,OAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,WAAW,CAAC;QACZ,QAAQ;QAER,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,MAAK;YACL,iBAAe;YACf,iBAAe;YACf,cAAY,aAAa,WAAW;YACpC,iBAAe,WAAW,KAAK,KAAA;YAC/B;YACA,IAAI;YACH,GAAG,YAAA;YACJ,KAAK;YACL,cAAa,0LAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,YAAY,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,OAAO;oBAC9D,QAAQ,aAAA,CAAc,KAAK;gBAC7B,OAAO;oBAEL,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;YACD,+KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI;oBAAC;oBAAK,OAAO;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,QAAQ,aAAA,CAAc,KAAK;YACrE,CAAC;YACD,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,MAAM;gBAGjD,MAAM,wBAAwB,QAAQ,cAAA,KAAmB;gBACzD,IAAI,CAAC,cAAc,CAAC,YAAY,uBAAuB;oBACrD,QAAQ,aAAA,CAAc,KAAK;gBAC7B;YACF,CAAC;QAAA;IACH;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,eAAe;AAarB,IAAM,4KAAoB,aAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,UAAA,EAAY,QAAA,EAAU,GAAG,aAAa,CAAA,GAAI;IACtE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,MAAM,6LAAqC,SAAA,CAAO,UAAU;kKAEtD,YAAA;iCAAU,MAAM;YACpB,MAAM,MAAM;6CAAsB,IAAO,6BAA6B,OAAA,GAAU,KAAM;;YACtF;yCAAO,IAAM,qBAAqB,GAAG;;QACvC;gCAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc;QAC9B,UAAA;gBAAC,EAAE,OAAA,CAAQ,CAAA;mBACV,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;gBACC,cAAY,aAAa,WAAW;gBACpC,oBAAkB,QAAQ,WAAA;gBAC1B,MAAK;gBACL,mBAAiB;gBACjB,QAAQ,CAAC;gBACT,IAAI;gBACJ,UAAU;gBACT,GAAG,YAAA;gBACJ,KAAK;gBACL,OAAO;oBACL,GAAG,MAAM,KAAA;oBACT,mBAAmB,6BAA6B,OAAA,GAAU,OAAO,KAAA;gBACnE;gBAEC,UAAA,WAAW;YAAA;;IACd,CAEJ;AAEJ;AAGF,YAAY,WAAA,GAAc;AAI1B,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,UAAG,MAAM,EAAA,aAAiB,OAAL,KAAK;AACnC;AAEA,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,UAAG,MAAM,EAAA,aAAiB,OAAL,KAAK;AACnC;AAEA,IAAMA,QAAO;AACb,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU", "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-ui/dist/components/tabs.unstyled.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { createContext, useContext, useLayoutEffect, useMemo, useRef, useState, } from 'react';\nimport * as Primitive from '@radix-ui/react-tabs';\nimport { mergeRefs } from '../utils/merge-refs.js';\nimport { useEffectEvent } from 'fumadocs-core/utils/use-effect-event';\nconst listeners = new Map();\nfunction addChangeListener(id, listener) {\n    const list = listeners.get(id) ?? [];\n    list.push(listener);\n    listeners.set(id, list);\n}\nfunction removeChangeListener(id, listener) {\n    const list = listeners.get(id) ?? [];\n    listeners.set(id, list.filter((item) => item !== listener));\n}\nconst TabsContext = createContext(null);\nfunction useTabContext() {\n    const ctx = useContext(TabsContext);\n    if (!ctx)\n        throw new Error('You must wrap your component in <Tabs>');\n    return ctx;\n}\nexport const TabsList = Primitive.TabsList;\nexport const TabsTrigger = Primitive.TabsTrigger;\n/**\n * @internal You better not use it\n */\nexport function Tabs({ ref, groupId, persist = false, updateAnchor = false, defaultValue, value: _value, onValueChange: _onValueChange, ...props }) {\n    const tabsRef = useRef(null);\n    const [value, setValue] = _value === undefined\n        ? // eslint-disable-next-line react-hooks/rules-of-hooks -- not supposed to change controlled/uncontrolled\n            useState(defaultValue)\n        : [_value, _onValueChange ?? (() => undefined)];\n    const onChange = useEffectEvent((v) => setValue(v));\n    const valueToIdMap = useMemo(() => new Map(), []);\n    useLayoutEffect(() => {\n        if (!groupId)\n            return;\n        const previous = persist\n            ? localStorage.getItem(groupId)\n            : sessionStorage.getItem(groupId);\n        if (previous)\n            onChange(previous);\n        addChangeListener(groupId, onChange);\n        return () => {\n            removeChangeListener(groupId, onChange);\n        };\n    }, [groupId, onChange, persist]);\n    useLayoutEffect(() => {\n        const hash = window.location.hash.slice(1);\n        if (!hash)\n            return;\n        for (const [value, id] of valueToIdMap.entries()) {\n            if (id === hash) {\n                onChange(value);\n                tabsRef.current?.scrollIntoView();\n                break;\n            }\n        }\n    }, [onChange, valueToIdMap]);\n    return (_jsx(Primitive.Tabs, { ref: mergeRefs(ref, tabsRef), value: value, onValueChange: (v) => {\n            if (updateAnchor) {\n                const id = valueToIdMap.get(v);\n                if (id) {\n                    window.history.replaceState(null, '', `#${id}`);\n                }\n            }\n            if (groupId) {\n                listeners.get(groupId)?.forEach((item) => {\n                    item(v);\n                });\n                if (persist)\n                    localStorage.setItem(groupId, v);\n                else\n                    sessionStorage.setItem(groupId, v);\n            }\n            else {\n                setValue(v);\n            }\n        }, ...props, children: _jsx(TabsContext.Provider, { value: useMemo(() => ({ valueToIdMap }), [valueToIdMap]), children: props.children }) }));\n}\nexport function TabsContent({ value, ...props }) {\n    const { valueToIdMap } = useTabContext();\n    if (props.id) {\n        valueToIdMap.set(value, props.id);\n    }\n    return (_jsx(Primitive.TabsContent, { value: value, ...props, children: props.children }));\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;AAMA,MAAM,YAAY,IAAI;AACtB,SAAS,kBAAkB,EAAE,EAAE,QAAQ;QACtB;IAAb,MAAM,OAAO,CAAA,iBAAA,UAAU,GAAG,CAAC,iBAAd,4BAAA,iBAAqB,EAAE;IACpC,KAAK,IAAI,CAAC;IACV,UAAU,GAAG,CAAC,IAAI;AACtB;AACA,SAAS,qBAAqB,EAAE,EAAE,QAAQ;QACzB;IAAb,MAAM,OAAO,CAAA,iBAAA,UAAU,GAAG,CAAC,iBAAd,4BAAA,iBAAqB,EAAE;IACpC,UAAU,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,OAAS,SAAS;AACrD;AACA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;AAClC,SAAS;IACL,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IACvB,IAAI,CAAC,KACD,MAAM,IAAI,MAAM;IACpB,OAAO;AACX;AACO,MAAM,WAAW,mKAAA,CAAA,WAAkB;AACnC,MAAM,cAAc,mKAAA,CAAA,cAAqB;AAIzC,SAAS,KAAK,KAA6H;QAA7H,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,eAAe,KAAK,EAAE,YAAY,EAAE,OAAO,MAAM,EAAE,eAAe,cAAc,EAAE,GAAG,OAAO,GAA7H;IACjB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,WAAW,YAE7B,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,gBACX;QAAC;QAAQ,2BAAA,4BAAA,iBAAmB,IAAM;KAAW;IACnD,MAAM,WAAW,CAAA,GAAA,8KAAA,CAAA,iBAAc,AAAD;yCAAE,CAAC,IAAM,SAAS;;IAChD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCAAE,IAAM,IAAI;qCAAO,EAAE;IAChD,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;gCAAE;YACZ,IAAI,CAAC,SACD;YACJ,MAAM,WAAW,UACX,aAAa,OAAO,CAAC,WACrB,eAAe,OAAO,CAAC;YAC7B,IAAI,UACA,SAAS;YACb,kBAAkB,SAAS;YAC3B;wCAAO;oBACH,qBAAqB,SAAS;gBAClC;;QACJ;+BAAG;QAAC;QAAS;QAAU;KAAQ;IAC/B,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;gCAAE;YACZ,MAAM,OAAO,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;YACxC,IAAI,CAAC,MACD;YACJ,KAAK,MAAM,CAAC,OAAO,GAAG,IAAI,aAAa,OAAO,GAAI;gBAC9C,IAAI,OAAO,MAAM;wBAEb;oBADA,SAAS;qBACT,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,cAAc;oBAC/B;gBACJ;YACJ;QACJ;+BAAG;QAAC;QAAU;KAAa;IAC3B,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,mKAAA,CAAA,OAAc,EAAE;QAAE,KAAK,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAU,OAAO;QAAO,eAAe,CAAC;YACnF,IAAI,cAAc;gBACd,MAAM,KAAK,aAAa,GAAG,CAAC;gBAC5B,IAAI,IAAI;oBACJ,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,AAAC,IAAM,OAAH;gBAC9C;YACJ;YACA,IAAI,SAAS;oBACT;iBAAA,iBAAA,UAAU,GAAG,CAAC,sBAAd,qCAAA,eAAwB,OAAO,CAAC,CAAC;oBAC7B,KAAK;gBACT;gBACA,IAAI,SACA,aAAa,OAAO,CAAC,SAAS;qBAE9B,eAAe,OAAO,CAAC,SAAS;YACxC,OACK;gBACD,SAAS;YACb;QACJ;QAAG,GAAG,KAAK;QAAE,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,YAAY,QAAQ,EAAE;YAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gCAAE,IAAM,CAAC;wBAAE;oBAAa,CAAC;+BAAG;gBAAC;aAAa;YAAG,UAAU,MAAM,QAAQ;QAAC;IAAG;AAClJ;AACO,SAAS,YAAY,KAAmB;QAAnB,EAAE,KAAK,EAAE,GAAG,OAAO,GAAnB;IACxB,MAAM,EAAE,YAAY,EAAE,GAAG;IACzB,IAAI,MAAM,EAAE,EAAE;QACV,aAAa,GAAG,CAAC,OAAO,MAAM,EAAE;IACpC;IACA,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,mKAAA,CAAA,cAAqB,EAAE;QAAE,OAAO;QAAO,GAAG,KAAK;QAAE,UAAU,MAAM,QAAQ;IAAC;AAC3F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-ui/dist/components/codeblock.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Check, Copy } from '../icons.js';\nimport { createContext, useContext, useMemo, useRef, } from 'react';\nimport { cn } from '../utils/cn.js';\nimport { useCopyButton } from '../utils/use-copy-button.js';\nimport { buttonVariants } from '../components/ui/button.js';\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger, } from '../components/tabs.unstyled.js';\nimport { mergeRefs } from '../utils/merge-refs.js';\nconst TabsContext = createContext(null);\nexport function Pre(props) {\n    return (_jsx(\"pre\", { ...props, className: cn('min-w-full w-max *:flex *:flex-col', props.className), children: props.children }));\n}\nexport function CodeBlock({ ref, title, allowCopy, keepBackground = false, icon, viewportProps = {}, children, Actions = (props) => (_jsx(\"div\", { ...props, className: cn('empty:hidden', props.className) })), ...props }) {\n    const isTab = useContext(TabsContext) !== null;\n    const areaRef = useRef(null);\n    allowCopy ?? (allowCopy = !isTab);\n    const bg = cn('bg-fd-secondary', keepBackground && 'bg-(--shiki-light-bg) dark:bg-(--shiki-dark-bg)');\n    return (_jsxs(\"figure\", { ref: ref, dir: \"ltr\", ...props, className: cn(isTab ? [bg, 'rounded-lg shadow-sm'] : 'my-4 rounded-xl bg-fd-card p-1', 'shiki relative border outline-none not-prose overflow-hidden text-sm', props.className), children: [title ? (_jsxs(\"div\", { className: cn('flex text-fd-muted-foreground items-center gap-2 ps-3 h-9.5', isTab && 'border-b'), children: [typeof icon === 'string' ? (_jsx(\"div\", { className: \"[&_svg]:size-3.5\", dangerouslySetInnerHTML: {\n                            __html: icon,\n                        } })) : (icon), _jsx(\"figcaption\", { className: \"flex-1 truncate\", children: title }), Actions({\n                        children: allowCopy && _jsx(CopyButton, { containerRef: areaRef }),\n                    })] })) : (Actions({\n                className: 'absolute top-1 right-1 z-2 bg-fd-card rounded-bl-lg border-l border-b text-fd-muted-foreground',\n                children: allowCopy && _jsx(CopyButton, { containerRef: areaRef }),\n            })), _jsx(\"div\", { ref: areaRef, ...viewportProps, className: cn(!isTab && [bg, 'rounded-lg border'], 'text-[13px] py-3.5 overflow-auto max-h-[600px] fd-scroll-container', viewportProps.className), style: {\n                    // space for toolbar\n                    '--padding-right': !title ? 'calc(var(--spacing) * 8)' : undefined,\n                    counterSet: props['data-line-numbers']\n                        ? `line ${Number(props['data-line-numbers-start'] ?? 1) - 1}`\n                        : undefined,\n                    ...viewportProps.style,\n                }, children: children })] }));\n}\nfunction CopyButton({ className, containerRef, ...props }) {\n    const [checked, onClick] = useCopyButton(() => {\n        const pre = containerRef.current?.getElementsByTagName('pre').item(0);\n        if (!pre)\n            return;\n        const clone = pre.cloneNode(true);\n        clone.querySelectorAll('.nd-copy-ignore').forEach((node) => {\n            node.replaceWith('\\n');\n        });\n        void navigator.clipboard.writeText(clone.textContent ?? '');\n    });\n    return (_jsx(\"button\", { type: \"button\", className: cn(buttonVariants({\n            color: 'ghost',\n            className: '[&_svg]:size-3.5',\n        }), className), \"aria-label\": checked ? 'Copied Text' : 'Copy Text', onClick: onClick, ...props, children: checked ? _jsx(Check, {}) : _jsx(Copy, {}) }));\n}\nexport function CodeBlockTabs({ ref, ...props }) {\n    const containerRef = useRef(null);\n    const nested = useContext(TabsContext) !== null;\n    return (_jsx(Tabs, { ref: mergeRefs(containerRef, ref), ...props, className: cn('bg-fd-card p-1 rounded-xl border overflow-hidden', !nested && 'my-4', props.className), children: _jsx(TabsContext.Provider, { value: useMemo(() => ({\n                containerRef,\n                nested,\n            }), [nested]), children: props.children }) }));\n}\nexport function CodeBlockTabsList(props) {\n    const { containerRef, nested } = useContext(TabsContext);\n    return (_jsxs(TabsList, { ...props, className: cn('flex flex-row overflow-x-auto px-1 -mx-1 text-fd-muted-foreground', props.className), children: [props.children, !nested && (_jsx(CopyButton, { className: \"sticky ms-auto right-0 bg-fd-card backdrop-blur-sm\", containerRef: containerRef }))] }));\n}\nexport function CodeBlockTabsTrigger({ children, ...props }) {\n    return (_jsxs(TabsTrigger, { ...props, className: cn('relative group inline-flex text-sm font-medium text-nowrap items-center gap-2 px-2 first:ms-1 py-1.5 hover:text-fd-accent-foreground data-[state=active]:text-fd-primary [&_svg]:size-3.5', props.className), children: [_jsx(\"div\", { className: \"absolute inset-x-2 bottom-0 h-px group-data-[state=active]:bg-fd-primary\" }), children] }));\n}\n// TODO: currently Vite RSC plugin has problem with adding `asChild` here, maybe revisit this in future\nexport const CodeBlockTab = TabsContent;\n"], "names": [], "mappings": ";;;;;;;;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AASA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;AAC3B,SAAS,IAAI,KAAK;IACrB,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC,MAAM,SAAS;QAAG,UAAU,MAAM,QAAQ;IAAC;AACnI;AACO,SAAS,UAAU,KAAiM;QAAjM,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,iBAAiB,KAAK,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,QAAW,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YAAE,GAAG,KAAK;YAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,MAAM,SAAS;QAAE,EAAG,EAAE,GAAG,OAAO,GAAjM;IACtB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB;IAC1C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,sBAAA,uBAAA,YAAc,YAAY,CAAC;IAC3B,MAAM,KAAK,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB,kBAAkB;QAYd;IAXrC,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,UAAU;QAAE,KAAK;QAAK,KAAK;QAAO,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;YAAC;YAAI;SAAuB,GAAG,kCAAkC,wEAAwE,MAAM,SAAS;QAAG,UAAU;YAAC,QAAS,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,+DAA+D,SAAS;gBAAa,UAAU;oBAAC,OAAO,SAAS,WAAY,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBAAE,WAAW;wBAAoB,yBAAyB;4BACtc,QAAQ;wBACZ;oBAAE,KAAO;oBAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,cAAc;wBAAE,WAAW;wBAAmB,UAAU;oBAAM;oBAAI,QAAQ;wBAC/F,UAAU,aAAa,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,YAAY;4BAAE,cAAc;wBAAQ;oBACpE;iBAAG;YAAC,KAAO,QAAQ;gBACvB,WAAW;gBACX,UAAU,aAAa,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,YAAY;oBAAE,cAAc;gBAAQ;YACpE;YAAK,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBAAE,KAAK;gBAAS,GAAG,aAAa;gBAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,CAAC,SAAS;oBAAC;oBAAI;iBAAoB,EAAE,sEAAsE,cAAc,SAAS;gBAAG,OAAO;oBACrM,oBAAoB;oBACpB,mBAAmB,CAAC,QAAQ,6BAA6B;oBACzD,YAAY,KAAK,CAAC,oBAAoB,GAChC,AAAC,QAAyD,OAAlD,OAAO,CAAA,8BAAA,KAAK,CAAC,0BAA0B,cAAhC,yCAAA,8BAAoC,KAAK,KACxD;oBACN,GAAG,cAAc,KAAK;gBAC1B;gBAAG,UAAU;YAAS;SAAG;IAAC;AAC1C;AACA,SAAS,WAAW,KAAqC;QAArC,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,OAAO,GAArC;IAChB,MAAM,CAAC,SAAS,QAAQ,GAAG,CAAA,GAAA,2KAAA,CAAA,gBAAa,AAAD;oCAAE;gBACzB;YAAZ,MAAM,OAAM,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,oBAAoB,CAAC,OAAO,IAAI,CAAC;YACnE,IAAI,CAAC,KACD;YACJ,MAAM,QAAQ,IAAI,SAAS,CAAC;YAC5B,MAAM,gBAAgB,CAAC,mBAAmB,OAAO;4CAAC,CAAC;oBAC/C,KAAK,WAAW,CAAC;gBACrB;;gBACmC;YAAnC,KAAK,UAAU,SAAS,CAAC,SAAS,CAAC,CAAA,qBAAA,MAAM,WAAW,cAAjB,gCAAA,qBAAqB;QAC5D;;IACA,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,UAAU;QAAE,MAAM;QAAU,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,uKAAA,CAAA,iBAAc,AAAD,EAAE;YAC9D,OAAO;YACP,WAAW;QACf,IAAI;QAAY,cAAc,UAAU,gBAAgB;QAAa,SAAS;QAAS,GAAG,KAAK;QAAE,UAAU,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,kJAAA,CAAA,QAAK,EAAE,CAAC,KAAK,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,kJAAA,CAAA,OAAI,EAAE,CAAC;IAAG;AAC9J;AACO,SAAS,cAAc,KAAiB;QAAjB,EAAE,GAAG,EAAE,GAAG,OAAO,GAAjB;IAC1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB;IAC3C,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,2KAAA,CAAA,OAAI,EAAE;QAAE,KAAK,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAM,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD,CAAC,UAAU,QAAQ,MAAM,SAAS;QAAG,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,YAAY,QAAQ,EAAE;YAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE,IAAM,CAAC;wBAC1N;wBACA;oBACJ,CAAC;wCAAG;gBAAC;aAAO;YAAG,UAAU,MAAM,QAAQ;QAAC;IAAG;AACvD;AACO,SAAS,kBAAkB,KAAK;IACnC,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC5C,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,2KAAA,CAAA,WAAQ,EAAE;QAAE,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,qEAAqE,MAAM,SAAS;QAAG,UAAU;YAAC,MAAM,QAAQ;YAAE,CAAC,UAAW,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,YAAY;gBAAE,WAAW;gBAAsD,cAAc;YAAa;SAAI;IAAC;AACxS;AACO,SAAS,qBAAqB,KAAsB;QAAtB,EAAE,QAAQ,EAAE,GAAG,OAAO,GAAtB;IACjC,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,2KAAA,CAAA,cAAW,EAAE;QAAE,GAAG,KAAK;QAAE,WAAW,CAAA,GAAA,+LAAA,CAAA,KAAE,AAAD,EAAE,6LAA6L,MAAM,SAAS;QAAG,UAAU;YAAC,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBAAE,WAAW;YAA2E;YAAI;SAAS;IAAC;AACrY;AAEO,MAAM,eAAe,2KAAA,CAAA,cAAW", "ignoreList": [0], "debugId": null}}]}