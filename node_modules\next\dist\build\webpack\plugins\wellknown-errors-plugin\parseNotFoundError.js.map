{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "sourcesContent": ["import { bold, cyan, green, red, yellow } from '../../../../lib/picocolors'\nimport { SimpleWebpackError } from './simpleWebpackError'\nimport {\n  createOriginalStackFrame,\n  getIgnoredSources,\n} from '../../../../server/dev/middleware-webpack'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { RawSourceMap } from 'next/dist/compiled/source-map08'\n\n// Based on https://github.com/webpack/webpack/blob/fcdd04a833943394bbb0a9eeb54a962a24cc7e41/lib/stats/DefaultStatsFactoryPlugin.js#L422-L431\n/*\nCopyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\nfunction getModuleTrace(input: any, compilation: any) {\n  const visitedModules = new Set()\n  const moduleTrace = []\n  let current = input.module\n  while (current) {\n    if (visitedModules.has(current)) break // circular (technically impossible, but who knows)\n    visitedModules.add(current)\n    const origin = compilation.moduleGraph.getIssuer(current)\n    if (!origin) break\n    moduleTrace.push({ origin, module: current })\n    current = origin\n  }\n\n  return moduleTrace\n}\n\nfunction sourceMapIgnoreListsEverything(\n  sourceMap: RawSourceMap & { ignoreList?: number[] }\n): boolean {\n  return sourceMap.sources.length === sourceMap.ignoreList?.length\n}\n\nasync function getSourceFrame(\n  input: any,\n  fileName: any,\n  compilation: any\n): Promise<{ frame: string; line1: string; column1: string }> {\n  try {\n    const loc =\n      input.loc || input.dependencies.map((d: any) => d.loc).filter(Boolean)[0]\n    const module = input.module as webpack.Module\n    const originalSource = module.originalSource()\n    const sourceMap = originalSource?.map() ?? undefined\n\n    if (sourceMap) {\n      const moduleId = compilation.chunkGraph.getModuleId(module)\n\n      const result = await createOriginalStackFrame({\n        ignoredByDefault: sourceMapIgnoreListsEverything(sourceMap),\n        source: {\n          type: 'bundle',\n          sourceMap,\n          ignoredSources: getIgnoredSources(sourceMap),\n          compilation,\n          moduleId,\n          moduleURL: fileName,\n        },\n        rootDirectory: compilation.options.context!,\n        frame: {\n          arguments: [],\n          file: fileName,\n          methodName: '',\n          line1: loc.start.line,\n          // loc is 0-based but columns in stack frames are 1-based.\n          column1: (loc.start.column ?? 0) + 1,\n        },\n      })\n\n      return {\n        frame: result?.originalCodeFrame ?? '',\n        line1: result?.originalStackFrame?.line1?.toString() ?? '',\n        column1: result?.originalStackFrame?.column1?.toString() ?? '',\n      }\n    }\n  } catch {}\n\n  return { frame: '', line1: '', column1: '' }\n}\n\nfunction getFormattedFileName(\n  fileName: string,\n  module: any,\n  lineNumber?: string,\n  column?: string\n): string {\n  if (\n    module.loaders?.find((loader: any) =>\n      /next-font-loader[/\\\\]index.js/.test(loader.loader)\n    )\n  ) {\n    // Parse the query and get the path of the file where the font function was called.\n    // provided by next-swc next-transform-font\n    return JSON.parse(module.resourceResolveData.query.slice(1)).path\n  } else {\n    let formattedFileName: string = cyan(fileName)\n    if (lineNumber && column) {\n      formattedFileName += `:${yellow(lineNumber)}:${yellow(column)}`\n    }\n\n    return formattedFileName\n  }\n}\n\nexport async function getNotFoundError(\n  compilation: webpack.Compilation,\n  input: any,\n  fileName: string,\n  module: any\n) {\n  if (\n    input.name !== 'ModuleNotFoundError' &&\n    !(\n      input.name === 'ModuleBuildError' &&\n      /Error: Can't resolve '.+' in /.test(input.message)\n    )\n  ) {\n    return false\n  }\n\n  try {\n    const { frame, line1, column1 } = await getSourceFrame(\n      input,\n      fileName,\n      compilation\n    )\n\n    const errorMessage = input.error.message\n      .replace(/ in '.*?'/, '')\n      .replace(/Can't resolve '(.*)'/, `Can't resolve '${green('$1')}'`)\n\n    const importTrace = () => {\n      const moduleTrace = getModuleTrace(input, compilation)\n        .map(({ origin }) =>\n          origin.readableIdentifier(compilation.requestShortener)\n        )\n        .filter(\n          (name) =>\n            name &&\n            !/next-(app|middleware|client-pages|route|flight-(client|server|client-entry))-loader/.test(\n              name\n            ) &&\n            !/css-loader.+\\.js/.test(name)\n        )\n      if (moduleTrace.length === 0) return ''\n\n      return `\\nImport trace for requested module:\\n${moduleTrace.join('\\n')}`\n    }\n\n    let message =\n      red(bold('Module not found')) +\n      `: ${errorMessage}` +\n      '\\n' +\n      frame +\n      (frame !== '' ? '\\n' : '') +\n      '\\nhttps://nextjs.org/docs/messages/module-not-found\\n' +\n      importTrace()\n\n    const formattedFileName = getFormattedFileName(\n      fileName,\n      module,\n      line1,\n      column1\n    )\n\n    return new SimpleWebpackError(formattedFileName, message)\n  } catch (err) {\n    // Don't fail on failure to resolve sourcemaps\n    return input\n  }\n}\n\nexport async function getImageError(\n  compilation: any,\n  input: any,\n  err: Error\n): Promise<SimpleWebpackError | false> {\n  if (err.name !== 'InvalidImageFormatError') {\n    return false\n  }\n\n  const moduleTrace = getModuleTrace(input, compilation)\n  const { origin, module } = moduleTrace[0] || {}\n  if (!origin || !module) {\n    return false\n  }\n  const page = origin.rawRequest.replace(/^private-next-pages/, './pages')\n  const importedFile = module.rawRequest\n  const source = origin.originalSource().buffer().toString('utf8') as string\n  let lineNumber = -1\n  source.split('\\n').some((line) => {\n    lineNumber++\n    return line.includes(importedFile)\n  })\n  return new SimpleWebpackError(\n    `${cyan(page)}:${yellow(lineNumber.toString())}`,\n    red(bold('Error')).concat(\n      `: Image import \"${importedFile}\" is not a valid image file. The image may be corrupted or an unsupported format.`\n    )\n  )\n}\n"], "names": ["getImageError", "getNotFoundError", "getModuleTrace", "input", "compilation", "visitedModules", "Set", "moduleTrace", "current", "module", "has", "add", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>", "push", "sourceMapIgnoreListsEverything", "sourceMap", "sources", "length", "ignoreList", "getSourceFrame", "fileName", "loc", "dependencies", "map", "d", "filter", "Boolean", "originalSource", "undefined", "result", "moduleId", "chunkGraph", "getModuleId", "createOriginalStackFrame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "source", "type", "ignoredSources", "getIgnoredSources", "moduleURL", "rootDirectory", "options", "context", "frame", "arguments", "file", "methodName", "line1", "start", "line", "column1", "column", "originalCodeFrame", "originalStackFrame", "toString", "getFormattedFileName", "lineNumber", "loaders", "find", "loader", "test", "JSON", "parse", "resourceResolveData", "query", "slice", "path", "formattedFileName", "cyan", "yellow", "name", "message", "errorMessage", "error", "replace", "green", "importTrace", "readableIdentifier", "requestShortener", "join", "red", "bold", "SimpleWebpackError", "err", "page", "rawRequest", "importedFile", "buffer", "split", "some", "includes", "concat"], "mappings": ";;;;;;;;;;;;;;;IAkMsBA,aAAa;eAAbA;;IApEAC,gBAAgB;eAAhBA;;;4BA9HyB;oCACZ;mCAI5B;AAIP,6IAA6I;AAC7I;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,SAASC,eAAeC,KAAU,EAAEC,WAAgB;IAClD,MAAMC,iBAAiB,IAAIC;IAC3B,MAAMC,cAAc,EAAE;IACtB,IAAIC,UAAUL,MAAMM,MAAM;IAC1B,MAAOD,QAAS;QACd,IAAIH,eAAeK,GAAG,CAACF,UAAU,OAAM,mDAAmD;QAC1FH,eAAeM,GAAG,CAACH;QACnB,MAAMI,SAASR,YAAYS,WAAW,CAACC,SAAS,CAACN;QACjD,IAAI,CAACI,QAAQ;QACbL,YAAYQ,IAAI,CAAC;YAAEH;YAAQH,QAAQD;QAAQ;QAC3CA,UAAUI;IACZ;IAEA,OAAOL;AACT;AAEA,SAASS,+BACPC,SAAmD;QAEfA;IAApC,OAAOA,UAAUC,OAAO,CAACC,MAAM,OAAKF,wBAAAA,UAAUG,UAAU,qBAApBH,sBAAsBE,MAAM;AAClE;AAEA,eAAeE,eACblB,KAAU,EACVmB,QAAa,EACblB,WAAgB;IAEhB,IAAI;QACF,MAAMmB,MACJpB,MAAMoB,GAAG,IAAIpB,MAAMqB,YAAY,CAACC,GAAG,CAAC,CAACC,IAAWA,EAAEH,GAAG,EAAEI,MAAM,CAACC,QAAQ,CAAC,EAAE;QAC3E,MAAMnB,UAASN,MAAMM,MAAM;QAC3B,MAAMoB,iBAAiBpB,QAAOoB,cAAc;QAC5C,MAAMZ,YAAYY,CAAAA,kCAAAA,eAAgBJ,GAAG,OAAMK;QAE3C,IAAIb,WAAW;gBA0BJc,kCAAAA,4BACEA,oCAAAA;YA1BX,MAAMC,WAAW5B,YAAY6B,UAAU,CAACC,WAAW,CAACzB;YAEpD,MAAMsB,SAAS,MAAMI,IAAAA,2CAAwB,EAAC;gBAC5CC,kBAAkBpB,+BAA+BC;gBACjDoB,QAAQ;oBACNC,MAAM;oBACNrB;oBACAsB,gBAAgBC,IAAAA,oCAAiB,EAACvB;oBAClCb;oBACA4B;oBACAS,WAAWnB;gBACb;gBACAoB,eAAetC,YAAYuC,OAAO,CAACC,OAAO;gBAC1CC,OAAO;oBACLC,WAAW,EAAE;oBACbC,MAAMzB;oBACN0B,YAAY;oBACZC,OAAO1B,IAAI2B,KAAK,CAACC,IAAI;oBACrB,0DAA0D;oBAC1DC,SAAS,AAAC7B,CAAAA,IAAI2B,KAAK,CAACG,MAAM,IAAI,CAAA,IAAK;gBACrC;YACF;YAEA,OAAO;gBACLR,OAAOd,CAAAA,0BAAAA,OAAQuB,iBAAiB,KAAI;gBACpCL,OAAOlB,CAAAA,2BAAAA,6BAAAA,OAAQwB,kBAAkB,sBAA1BxB,mCAAAA,2BAA4BkB,KAAK,qBAAjClB,iCAAmCyB,QAAQ,OAAM;gBACxDJ,SAASrB,CAAAA,2BAAAA,8BAAAA,OAAQwB,kBAAkB,sBAA1BxB,qCAAAA,4BAA4BqB,OAAO,qBAAnCrB,mCAAqCyB,QAAQ,OAAM;YAC9D;QACF;IACF,EAAE,OAAM,CAAC;IAET,OAAO;QAAEX,OAAO;QAAII,OAAO;QAAIG,SAAS;IAAG;AAC7C;AAEA,SAASK,qBACPnC,QAAgB,EAChBb,OAAW,EACXiD,UAAmB,EACnBL,MAAe;QAGb5C;IADF,KACEA,kBAAAA,QAAOkD,OAAO,qBAAdlD,gBAAgBmD,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;QACA,mFAAmF;QACnF,2CAA2C;QAC3C,OAAOE,KAAKC,KAAK,CAACvD,QAAOwD,mBAAmB,CAACC,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;IACnE,OAAO;QACL,IAAIC,oBAA4BC,IAAAA,gBAAI,EAAChD;QACrC,IAAIoC,cAAcL,QAAQ;YACxBgB,qBAAqB,CAAC,CAAC,EAAEE,IAAAA,kBAAM,EAACb,YAAY,CAAC,EAAEa,IAAAA,kBAAM,EAAClB,SAAS;QACjE;QAEA,OAAOgB;IACT;AACF;AAEO,eAAepE,iBACpBG,WAAgC,EAChCD,KAAU,EACVmB,QAAgB,EAChBb,OAAW;IAEX,IACEN,MAAMqE,IAAI,KAAK,yBACf,CACErE,CAAAA,MAAMqE,IAAI,KAAK,sBACf,gCAAgCV,IAAI,CAAC3D,MAAMsE,OAAO,CAAA,GAEpD;QACA,OAAO;IACT;IAEA,IAAI;QACF,MAAM,EAAE5B,KAAK,EAAEI,KAAK,EAAEG,OAAO,EAAE,GAAG,MAAM/B,eACtClB,OACAmB,UACAlB;QAGF,MAAMsE,eAAevE,MAAMwE,KAAK,CAACF,OAAO,CACrCG,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,wBAAwB,CAAC,eAAe,EAAEC,IAAAA,iBAAK,EAAC,MAAM,CAAC,CAAC;QAEnE,MAAMC,cAAc;YAClB,MAAMvE,cAAcL,eAAeC,OAAOC,aACvCqB,GAAG,CAAC,CAAC,EAAEb,MAAM,EAAE,GACdA,OAAOmE,kBAAkB,CAAC3E,YAAY4E,gBAAgB,GAEvDrD,MAAM,CACL,CAAC6C,OACCA,QACA,CAAC,sFAAsFV,IAAI,CACzFU,SAEF,CAAC,mBAAmBV,IAAI,CAACU;YAE/B,IAAIjE,YAAYY,MAAM,KAAK,GAAG,OAAO;YAErC,OAAO,CAAC,sCAAsC,EAAEZ,YAAY0E,IAAI,CAAC,OAAO;QAC1E;QAEA,IAAIR,UACFS,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,uBACT,CAAC,EAAE,EAAET,cAAc,GACnB,OACA7B,QACCA,CAAAA,UAAU,KAAK,OAAO,EAAC,IACxB,0DACAiC;QAEF,MAAMT,oBAAoBZ,qBACxBnC,UACAb,SACAwC,OACAG;QAGF,OAAO,IAAIgC,sCAAkB,CAACf,mBAAmBI;IACnD,EAAE,OAAOY,KAAK;QACZ,8CAA8C;QAC9C,OAAOlF;IACT;AACF;AAEO,eAAeH,cACpBI,WAAgB,EAChBD,KAAU,EACVkF,GAAU;IAEV,IAAIA,IAAIb,IAAI,KAAK,2BAA2B;QAC1C,OAAO;IACT;IAEA,MAAMjE,cAAcL,eAAeC,OAAOC;IAC1C,MAAM,EAAEQ,MAAM,EAAEH,QAAAA,OAAM,EAAE,GAAGF,WAAW,CAAC,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACK,UAAU,CAACH,SAAQ;QACtB,OAAO;IACT;IACA,MAAM6E,OAAO1E,OAAO2E,UAAU,CAACX,OAAO,CAAC,uBAAuB;IAC9D,MAAMY,eAAe/E,QAAO8E,UAAU;IACtC,MAAMlD,SAASzB,OAAOiB,cAAc,GAAG4D,MAAM,GAAGjC,QAAQ,CAAC;IACzD,IAAIE,aAAa,CAAC;IAClBrB,OAAOqD,KAAK,CAAC,MAAMC,IAAI,CAAC,CAACxC;QACvBO;QACA,OAAOP,KAAKyC,QAAQ,CAACJ;IACvB;IACA,OAAO,IAAIJ,sCAAkB,CAC3B,GAAGd,IAAAA,gBAAI,EAACgB,MAAM,CAAC,EAAEf,IAAAA,kBAAM,EAACb,WAAWF,QAAQ,KAAK,EAChD0B,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,UAAUU,MAAM,CACvB,CAAC,gBAAgB,EAAEL,aAAa,iFAAiF,CAAC;AAGxH", "ignoreList": [0]}