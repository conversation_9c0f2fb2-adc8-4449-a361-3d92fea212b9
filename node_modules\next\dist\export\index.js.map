{"version": 3, "sources": ["../../src/export/index.ts"], "sourcesContent": ["import type {\n  ExportAppResult,\n  ExportAppOptions,\n  WorkerRenderOptsPartial,\n  ExportPagesResult,\n  ExportPathEntry,\n} from './types'\nimport {\n  createStaticWorker,\n  type PrerenderManifest,\n  type StaticWorker,\n} from '../build'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\n\nimport { bold, yellow } from '../lib/picocolors'\nimport findUp from 'next/dist/compiled/find-up'\nimport { existsSync, promises as fs } from 'fs'\n\nimport '../server/require-hook'\n\nimport { dirname, join, resolve, sep, relative } from 'path'\nimport { formatAmpMessages } from '../build/output/index'\nimport type { AmpPageStatus } from '../build/output/index'\nimport * as Log from '../build/output/log'\nimport {\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n  SSG_FALLBACK_EXPORT_ERROR,\n} from '../lib/constants'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport {\n  BUILD_ID_FILE,\n  CLIENT_PUBLIC_FILES_PATH,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  NEXT_FONT_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_EXPORT,\n  PRERENDER_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_REFERENCE_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  ROUTES_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n} from '../shared/lib/constants'\nimport loadConfig from '../server/config'\nimport type { ExportPathMap } from '../server/config-shared'\nimport { eventCliSession } from '../telemetry/events'\nimport { hasNextSupport } from '../server/ci-info'\nimport { Telemetry } from '../telemetry/storage'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { loadEnvConfig } from '@next/env'\nimport { isAPIRoute } from '../lib/is-api-route'\nimport { getPagePath } from '../server/require'\nimport type { Span } from '../trace'\nimport type { MiddlewareManifest } from '../build/webpack/plugins/middleware-plugin'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { isAppPageRoute } from '../lib/is-app-page-route'\nimport isError from '../lib/is-error'\nimport { formatManifest } from '../build/manifests/formatter/format-manifest'\nimport { TurborepoAccessTraceResult } from '../build/turborepo-access-trace'\nimport { createProgress } from '../build/progress'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport type { ActionManifest } from '../build/webpack/plugins/flight-client-entry-plugin'\nimport { extractInfoFromServerReferenceId } from '../shared/lib/server-reference-info'\nimport { convertSegmentPathToStaticExportFilename } from '../shared/lib/segment-cache/segment-value-encoding'\nimport { getNextBuildDebuggerPortOffset } from '../lib/worker'\n\nexport class ExportError extends Error {\n  code = 'NEXT_EXPORT_ERROR'\n}\n\nasync function exportAppImpl(\n  dir: string,\n  options: Readonly<ExportAppOptions>,\n  span: Span\n): Promise<ExportAppResult | null> {\n  dir = resolve(dir)\n\n  // attempt to load global env values so they are available in next.config.js\n  span.traceChild('load-dotenv').traceFn(() => loadEnvConfig(dir, false, Log))\n\n  const { enabledDirectories } = options\n\n  const nextConfig =\n    options.nextConfig ||\n    (await span.traceChild('load-next-config').traceAsyncFn(() =>\n      loadConfig(PHASE_EXPORT, dir, {\n        debugPrerender: options.debugPrerender,\n      })\n    ))\n\n  const distDir = join(dir, nextConfig.distDir)\n  const telemetry = options.buildExport ? null : new Telemetry({ distDir })\n\n  if (telemetry) {\n    telemetry.record(\n      eventCliSession(distDir, nextConfig, {\n        webpackVersion: null,\n        cliCommand: 'export',\n        isSrcDir: null,\n        hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n        isCustomServer: null,\n        turboFlag: false,\n        pagesDir: null,\n        appDir: null,\n      })\n    )\n  }\n\n  const subFolders = nextConfig.trailingSlash && !options.buildExport\n\n  if (!options.silent && !options.buildExport) {\n    Log.info(`using build directory: ${distDir}`)\n  }\n\n  const buildIdFile = join(distDir, BUILD_ID_FILE)\n\n  if (!existsSync(buildIdFile)) {\n    throw new ExportError(\n      `Could not find a production build in the '${distDir}' directory. Try building your app with 'next build' before starting the static export. https://nextjs.org/docs/messages/next-export-no-build-id`\n    )\n  }\n\n  const customRoutes = ['rewrites', 'redirects', 'headers'].filter(\n    (config) => typeof nextConfig[config] === 'function'\n  )\n\n  if (!hasNextSupport && !options.buildExport && customRoutes.length > 0) {\n    Log.warn(\n      `rewrites, redirects, and headers are not applied when exporting your application, detected (${customRoutes.join(\n        ', '\n      )}). See more info here: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  const buildId = await fs.readFile(buildIdFile, 'utf8')\n\n  const pagesManifest =\n    !options.pages &&\n    (require(join(distDir, SERVER_DIRECTORY, PAGES_MANIFEST)) as PagesManifest)\n\n  let prerenderManifest: DeepReadonly<PrerenderManifest> | undefined\n  try {\n    prerenderManifest = require(join(distDir, PRERENDER_MANIFEST))\n  } catch {}\n\n  let appRoutePathManifest: Record<string, string> | undefined\n  try {\n    appRoutePathManifest = require(join(distDir, APP_PATH_ROUTES_MANIFEST))\n  } catch (err) {\n    if (\n      isError(err) &&\n      (err.code === 'ENOENT' || err.code === 'MODULE_NOT_FOUND')\n    ) {\n      // the manifest doesn't exist which will happen when using\n      // \"pages\" dir instead of \"app\" dir.\n      appRoutePathManifest = undefined\n    } else {\n      // the manifest is malformed (invalid json)\n      throw err\n    }\n  }\n\n  const excludedPrerenderRoutes = new Set<string>()\n  const pages = options.pages || Object.keys(pagesManifest)\n  const defaultPathMap: ExportPathMap = {}\n\n  let hasApiRoutes = false\n  for (const page of pages) {\n    // _document and _app are not real pages\n    // _error is exported as 404.html later on\n    // API Routes are Node.js functions\n\n    if (isAPIRoute(page)) {\n      hasApiRoutes = true\n      continue\n    }\n\n    if (page === '/_document' || page === '/_app' || page === '/_error') {\n      continue\n    }\n\n    // iSSG pages that are dynamic should not export templated version by\n    // default. In most cases, this would never work. There is no server that\n    // could run `getStaticProps`. If users make their page work lazily, they\n    // can manually add it to the `exportPathMap`.\n    if (prerenderManifest?.dynamicRoutes[page]) {\n      excludedPrerenderRoutes.add(page)\n      continue\n    }\n\n    defaultPathMap[page] = { page }\n  }\n\n  const mapAppRouteToPage = new Map<string, string>()\n  if (!options.buildExport && appRoutePathManifest) {\n    for (const [pageName, routePath] of Object.entries(appRoutePathManifest)) {\n      mapAppRouteToPage.set(routePath, pageName)\n      if (\n        isAppPageRoute(pageName) &&\n        !prerenderManifest?.routes[routePath] &&\n        !prerenderManifest?.dynamicRoutes[routePath]\n      ) {\n        defaultPathMap[routePath] = {\n          page: pageName,\n          _isAppDir: true,\n        }\n      }\n    }\n  }\n\n  // Initialize the output directory\n  const outDir = options.outdir\n\n  if (outDir === join(dir, 'public')) {\n    throw new ExportError(\n      `The 'public' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-public`\n    )\n  }\n\n  if (outDir === join(dir, 'static')) {\n    throw new ExportError(\n      `The 'static' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-static`\n    )\n  }\n\n  await fs.rm(outDir, { recursive: true, force: true })\n  await fs.mkdir(join(outDir, '_next', buildId), { recursive: true })\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: false,\n    }),\n    'utf8'\n  )\n\n  // Copy static directory\n  if (!options.buildExport && existsSync(join(dir, 'static'))) {\n    if (!options.silent) {\n      Log.info('Copying \"static\" directory')\n    }\n    await span\n      .traceChild('copy-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(join(dir, 'static'), join(outDir, 'static'))\n      )\n  }\n\n  // Copy .next/static directory\n  if (\n    !options.buildExport &&\n    existsSync(join(distDir, CLIENT_STATIC_FILES_PATH))\n  ) {\n    if (!options.silent) {\n      Log.info('Copying \"static build\" directory')\n    }\n    await span\n      .traceChild('copy-next-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(\n          join(distDir, CLIENT_STATIC_FILES_PATH),\n          join(outDir, '_next', CLIENT_STATIC_FILES_PATH)\n        )\n      )\n  }\n\n  // Get the exportPathMap from the config file\n  if (typeof nextConfig.exportPathMap !== 'function') {\n    nextConfig.exportPathMap = async (defaultMap) => {\n      return defaultMap\n    }\n  }\n\n  const {\n    i18n,\n    images: { loader = 'default', unoptimized },\n  } = nextConfig\n\n  if (i18n && !options.buildExport) {\n    throw new ExportError(\n      `i18n support is not compatible with next export. See here for more info on deploying: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  if (!options.buildExport) {\n    const { isNextImageImported } = await span\n      .traceChild('is-next-image-imported')\n      .traceAsyncFn(() =>\n        fs\n          .readFile(join(distDir, EXPORT_MARKER), 'utf8')\n          .then((text) => JSON.parse(text))\n          .catch(() => ({}))\n      )\n\n    if (\n      isNextImageImported &&\n      loader === 'default' &&\n      !unoptimized &&\n      !hasNextSupport\n    ) {\n      throw new ExportError(\n        `Image Optimization using the default loader is not compatible with export.\n  Possible solutions:\n    - Use \\`next start\\` to run a server, which includes the Image Optimization API.\n    - Configure \\`images.unoptimized = true\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n  }\n\n  let serverActionsManifest: ActionManifest | undefined\n  if (enabledDirectories.app) {\n    serverActionsManifest = require(\n      join(distDir, SERVER_DIRECTORY, SERVER_REFERENCE_MANIFEST + '.json')\n    ) as ActionManifest\n\n    if (nextConfig.output === 'export') {\n      const routesManifest = require(join(distDir, ROUTES_MANIFEST))\n\n      // We already prevent rewrites earlier in the process, however Next.js will insert rewrites\n      // for interception routes so we need to check for that here.\n      if (routesManifest?.rewrites?.beforeFiles?.length > 0) {\n        const hasInterceptionRouteRewrite =\n          routesManifest.rewrites.beforeFiles.some(isInterceptionRouteRewrite)\n\n        if (hasInterceptionRouteRewrite) {\n          throw new ExportError(\n            `Intercepting routes are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features`\n          )\n        }\n      }\n\n      const actionIds = [\n        ...Object.keys(serverActionsManifest.node),\n        ...Object.keys(serverActionsManifest.edge),\n      ]\n\n      if (\n        actionIds.some(\n          (actionId) =>\n            extractInfoFromServerReferenceId(actionId).type === 'server-action'\n        )\n      ) {\n        throw new ExportError(\n          `Server Actions are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features`\n        )\n      }\n    }\n  }\n\n  // Start the rendering process\n  const renderOpts: WorkerRenderOptsPartial = {\n    previewProps: prerenderManifest?.preview,\n    nextExport: true,\n    assetPrefix: nextConfig.assetPrefix.replace(/\\/$/, ''),\n    distDir,\n    dev: false,\n    basePath: nextConfig.basePath,\n    trailingSlash: nextConfig.trailingSlash,\n    canonicalBase: nextConfig.amp?.canonicalBase || '',\n    ampSkipValidation: nextConfig.experimental.amp?.skipValidation || false,\n    ampOptimizerConfig: nextConfig.experimental.amp?.optimizer || undefined,\n    locales: i18n?.locales,\n    locale: i18n?.defaultLocale,\n    defaultLocale: i18n?.defaultLocale,\n    domainLocales: i18n?.domains,\n    disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n    // Exported pages do not currently support dynamic HTML.\n    supportsDynamicResponse: false,\n    crossOrigin: nextConfig.crossOrigin,\n    optimizeCss: nextConfig.experimental.optimizeCss,\n    nextConfigOutput: nextConfig.output,\n    nextScriptWorkers: nextConfig.experimental.nextScriptWorkers,\n    largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n    serverActions: nextConfig.experimental.serverActions,\n    serverComponents: enabledDirectories.app,\n    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n    nextFontManifest: require(\n      join(distDir, 'server', `${NEXT_FONT_MANIFEST}.json`)\n    ),\n    images: nextConfig.images,\n    ...(enabledDirectories.app\n      ? {\n          serverActionsManifest,\n        }\n      : {}),\n    deploymentId: nextConfig.deploymentId,\n    htmlLimitedBots: nextConfig.htmlLimitedBots.source,\n    experimental: {\n      clientTraceMetadata: nextConfig.experimental.clientTraceMetadata,\n      expireTime: nextConfig.expireTime,\n      staleTimes: nextConfig.experimental.staleTimes,\n      cacheComponents: nextConfig.experimental.cacheComponents ?? false,\n      clientSegmentCache:\n        nextConfig.experimental.clientSegmentCache === 'client-only'\n          ? 'client-only'\n          : Boolean(nextConfig.experimental.clientSegmentCache),\n      clientParamParsing: nextConfig.experimental.clientParamParsing ?? false,\n      dynamicOnHover: nextConfig.experimental.dynamicOnHover ?? false,\n      inlineCss: nextConfig.experimental.inlineCss ?? false,\n      authInterrupts: !!nextConfig.experimental.authInterrupts,\n    },\n    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n    hasReadableErrorStacks:\n      nextConfig.experimental.serverSourceMaps === true &&\n      // TODO(NDX-531): Checking (and setting) the minify flags should be\n      // unnecessary once name mapping is fixed.\n      (process.env.TURBOPACK\n        ? nextConfig.experimental.turbopackMinify === false\n        : nextConfig.experimental.serverMinification === false) &&\n      nextConfig.experimental.enablePrerenderSourceMaps === true,\n  }\n\n  const { publicRuntimeConfig } = nextConfig\n\n  if (Object.keys(publicRuntimeConfig).length > 0) {\n    renderOpts.runtimeConfig = publicRuntimeConfig\n  }\n\n  // We need this for server rendering the Link component.\n  ;(globalThis as any).__NEXT_DATA__ = {\n    nextExport: true,\n  }\n\n  const exportPathMap = await span\n    .traceChild('run-export-path-map')\n    .traceAsyncFn(async () => {\n      const exportMap = await nextConfig.exportPathMap(defaultPathMap, {\n        dev: false,\n        dir,\n        outDir,\n        distDir,\n        buildId,\n      })\n      return exportMap\n    })\n\n  // only add missing 404 page when `buildExport` is false\n  if (!options.buildExport) {\n    // only add missing /404 if not specified in `exportPathMap`\n    if (!exportPathMap['/404']) {\n      exportPathMap['/404'] = { page: '/_error' }\n    }\n\n    /**\n     * exports 404.html for backwards compat\n     * E.g. GitHub Pages, GitLab Pages, Cloudflare Pages, Netlify\n     */\n    if (!exportPathMap['/404.html']) {\n      // alias /404.html to /404 to be compatible with custom 404 / _error page\n      exportPathMap['/404.html'] = exportPathMap['/404']\n    }\n  }\n\n  const allExportPaths: ExportPathEntry[] = []\n  const seenExportPaths = new Set<string>()\n  const fallbackEnabledPages = new Set<string>()\n\n  for (const [path, entry] of Object.entries(exportPathMap)) {\n    // make sure to prevent duplicates\n    const normalizedPath = denormalizePagePath(normalizePagePath(path))\n\n    if (seenExportPaths.has(normalizedPath)) {\n      continue\n    }\n\n    seenExportPaths.add(normalizedPath)\n\n    if (!entry._isAppDir && isAPIRoute(entry.page)) {\n      hasApiRoutes = true\n      continue\n    }\n\n    allExportPaths.push({ ...entry, path: normalizedPath })\n\n    if (prerenderManifest && !options.buildExport) {\n      const prerenderInfo = prerenderManifest.dynamicRoutes[entry.page]\n\n      if (prerenderInfo && prerenderInfo.fallback !== false) {\n        fallbackEnabledPages.add(entry.page)\n      }\n    }\n  }\n\n  if (allExportPaths.length === 0) {\n    return null\n  }\n\n  if (fallbackEnabledPages.size > 0) {\n    throw new ExportError(\n      `Found pages with \\`fallback\\` enabled:\\n${[...fallbackEnabledPages].join(\n        '\\n'\n      )}\\n${SSG_FALLBACK_EXPORT_ERROR}\\n`\n    )\n  }\n\n  let hasMiddleware = false\n\n  if (!options.buildExport) {\n    try {\n      const middlewareManifest = require(\n        join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      ) as MiddlewareManifest\n\n      const functionsConfigManifest = require(\n        join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST)\n      )\n\n      hasMiddleware =\n        Object.keys(middlewareManifest.middleware).length > 0 ||\n        Boolean(functionsConfigManifest.functions?.['/_middleware'])\n    } catch {}\n\n    // Warn if the user defines a path for an API page\n    if (hasApiRoutes || hasMiddleware) {\n      if (nextConfig.output === 'export') {\n        Log.warn(\n          yellow(\n            `Statically exporting a Next.js application via \\`next export\\` disables API routes and middleware.`\n          ) +\n            `\\n` +\n            yellow(\n              `This command is meant for static-only hosts, and is` +\n                ' ' +\n                bold(`not necessary to make your application static.`)\n            ) +\n            `\\n` +\n            yellow(\n              `Pages in your application without server-side data dependencies will be automatically statically exported by \\`next build\\`, including pages powered by \\`getStaticProps\\`.`\n            ) +\n            `\\n` +\n            yellow(\n              `Learn more: https://nextjs.org/docs/messages/api-routes-static-export`\n            )\n        )\n      }\n    }\n  }\n\n  const pagesDataDir = options.buildExport\n    ? outDir\n    : join(outDir, '_next/data', buildId)\n\n  const ampValidations: AmpPageStatus = {}\n\n  const publicDir = join(dir, CLIENT_PUBLIC_FILES_PATH)\n  // Copy public directory\n  if (!options.buildExport && existsSync(publicDir)) {\n    if (!options.silent) {\n      Log.info('Copying \"public\" directory')\n    }\n    await span.traceChild('copy-public-directory').traceAsyncFn(() =>\n      recursiveCopy(publicDir, outDir, {\n        filter(path) {\n          // Exclude paths used by pages\n          return !exportPathMap[path]\n        },\n      })\n    )\n  }\n\n  const exportPagesInBatches = async (\n    worker: StaticWorker,\n    exportPaths: ExportPathEntry[],\n    renderResumeDataCachesByPage?: Record<string, string>\n  ): Promise<ExportPagesResult> => {\n    // Batch filtered pages into smaller batches, and call the export worker on\n    // each batch. We've set a default minimum of 25 pages per batch to ensure\n    // that even setups with only a few static pages can leverage a shared\n    // incremental cache, however this value can be configured.\n    const minPageCountPerBatch =\n      nextConfig.experimental.staticGenerationMinPagesPerWorker ?? 25\n\n    // Calculate the number of workers needed to ensure each batch has at least\n    // minPageCountPerBatch pages.\n    const numWorkers = Math.min(\n      options.numWorkers,\n      Math.ceil(exportPaths.length / minPageCountPerBatch)\n    )\n\n    // Calculate the page count per batch based on the number of workers.\n    const pageCountPerBatch = Math.ceil(exportPaths.length / numWorkers)\n\n    const batches = Array.from({ length: numWorkers }, (_, i) =>\n      exportPaths.slice(i * pageCountPerBatch, (i + 1) * pageCountPerBatch)\n    )\n\n    // Distribute remaining pages.\n    const remainingPages = exportPaths.slice(numWorkers * pageCountPerBatch)\n    remainingPages.forEach((page, index) => {\n      batches[index % batches.length].push(page)\n    })\n\n    return (\n      await Promise.all(\n        batches.map(async (batch) =>\n          worker.exportPages({\n            buildId,\n            exportPaths: batch,\n            parentSpanId: span.getId(),\n            pagesDataDir,\n            renderOpts,\n            options,\n            dir,\n            distDir,\n            outDir,\n            nextConfig,\n            cacheHandler: nextConfig.cacheHandler,\n            cacheMaxMemorySize: nextConfig.cacheMaxMemorySize,\n            fetchCache: true,\n            fetchCacheKeyPrefix: nextConfig.experimental.fetchCacheKeyPrefix,\n            renderResumeDataCachesByPage,\n          })\n        )\n      )\n    ).flat()\n  }\n\n  let initialPhaseExportPaths: ExportPathEntry[] = []\n  const finalPhaseExportPaths: ExportPathEntry[] = []\n\n  if (renderOpts.experimental.cacheComponents) {\n    for (const exportPath of allExportPaths) {\n      if (exportPath._allowEmptyStaticShell) {\n        finalPhaseExportPaths.push(exportPath)\n      } else {\n        initialPhaseExportPaths.push(exportPath)\n      }\n    }\n  } else {\n    initialPhaseExportPaths = allExportPaths\n  }\n\n  const progress = createProgress(\n    initialPhaseExportPaths.length + finalPhaseExportPaths.length,\n    options.statusMessage || 'Exporting'\n  )\n\n  const worker = createStaticWorker(nextConfig, {\n    debuggerPortOffset: getNextBuildDebuggerPortOffset({ kind: 'export-page' }),\n    progress,\n  })\n\n  const results = await exportPagesInBatches(worker, initialPhaseExportPaths)\n\n  if (finalPhaseExportPaths.length > 0) {\n    const renderResumeDataCachesByPage: Record<string, string> = {}\n\n    for (const { page, result } of results) {\n      if (!result) {\n        continue\n      }\n\n      if ('renderResumeDataCache' in result && result.renderResumeDataCache) {\n        // The last RDC for each page is used. We only need one. It should have\n        // all the entries that the fallback shell also needs. We don't need to\n        // merge them per page.\n        renderResumeDataCachesByPage[page] = result.renderResumeDataCache\n        // Remove the RDC string from the result so that it can be garbage\n        // collected, when there are more results for the same page.\n        result.renderResumeDataCache = undefined\n      }\n    }\n\n    const finalPhaseResults = await exportPagesInBatches(\n      worker,\n      finalPhaseExportPaths,\n      renderResumeDataCachesByPage\n    )\n\n    results.push(...finalPhaseResults)\n  }\n\n  let hadValidationError = false\n\n  const collector: ExportAppResult = {\n    byPath: new Map(),\n    byPage: new Map(),\n    ssgNotFoundPaths: new Set(),\n    turborepoAccessTraceResults: new Map(),\n  }\n\n  const failedExportAttemptsByPage: Map<string, boolean> = new Map()\n\n  for (const { result, path, page, pageKey } of results) {\n    if (!result) continue\n    if ('error' in result) {\n      failedExportAttemptsByPage.set(pageKey, true)\n      continue\n    }\n\n    if (result.turborepoAccessTraceResult) {\n      collector.turborepoAccessTraceResults?.set(\n        path,\n        TurborepoAccessTraceResult.fromSerialized(\n          result.turborepoAccessTraceResult\n        )\n      )\n    }\n\n    // Capture any amp validations.\n    if (result.ampValidations) {\n      for (const validation of result.ampValidations) {\n        ampValidations[validation.page] = validation.result\n        hadValidationError ||= validation.result.errors.length > 0\n      }\n    }\n\n    if (options.buildExport) {\n      // Update path info by path.\n      const info = collector.byPath.get(path) ?? {}\n      if (result.cacheControl) {\n        info.cacheControl = result.cacheControl\n      }\n      if (typeof result.metadata !== 'undefined') {\n        info.metadata = result.metadata\n      }\n\n      if (typeof result.hasEmptyStaticShell !== 'undefined') {\n        info.hasEmptyStaticShell = result.hasEmptyStaticShell\n      }\n\n      if (typeof result.hasPostponed !== 'undefined') {\n        info.hasPostponed = result.hasPostponed\n      }\n\n      if (typeof result.fetchMetrics !== 'undefined') {\n        info.fetchMetrics = result.fetchMetrics\n      }\n\n      collector.byPath.set(path, info)\n\n      // Update not found.\n      if (result.ssgNotFound === true) {\n        collector.ssgNotFoundPaths.add(path)\n      }\n\n      // Update durations.\n      const durations = collector.byPage.get(page) ?? {\n        durationsByPath: new Map<string, number>(),\n      }\n      durations.durationsByPath.set(path, result.duration)\n      collector.byPage.set(page, durations)\n    }\n  }\n\n  // Export mode provide static outputs that are not compatible with PPR mode.\n  if (!options.buildExport && nextConfig.experimental.ppr) {\n    // TODO: add message\n    throw new Error('Invariant: PPR cannot be enabled in export mode')\n  }\n\n  // copy prerendered routes to outDir\n  if (!options.buildExport && prerenderManifest) {\n    await Promise.all(\n      Object.keys(prerenderManifest.routes).map(async (unnormalizedRoute) => {\n        // Skip handling /_not-found route, it will copy the 404.html file later\n        if (unnormalizedRoute === '/_not-found') {\n          return\n        }\n        const { srcRoute } = prerenderManifest!.routes[unnormalizedRoute]\n        const appPageName = mapAppRouteToPage.get(srcRoute || '')\n        const pageName = appPageName || srcRoute || unnormalizedRoute\n        const isAppPath = Boolean(appPageName)\n        const isAppRouteHandler = appPageName && isAppRouteRoute(appPageName)\n\n        // returning notFound: true from getStaticProps will not\n        // output html/json files during the build\n        if (prerenderManifest!.notFoundRoutes.includes(unnormalizedRoute)) {\n          return\n        }\n        // TODO: This rewrites /index/foo to /index/index/foo. Investigate and\n        // fix. I presume this was because normalizePagePath was designed for\n        // some other use case and then reused here for static exports without\n        // realizing the implications.\n        const route = normalizePagePath(unnormalizedRoute)\n\n        const pagePath = getPagePath(pageName, distDir, undefined, isAppPath)\n        const distPagesDir = join(\n          pagePath,\n          // strip leading / and then recurse number of nested dirs\n          // to place from base folder\n          pageName\n            .slice(1)\n            .split('/')\n            .map(() => '..')\n            .join('/')\n        )\n\n        const orig = join(distPagesDir, route)\n        const handlerSrc = `${orig}.body`\n        const handlerDest = join(outDir, route)\n\n        if (isAppRouteHandler && existsSync(handlerSrc)) {\n          await fs.mkdir(dirname(handlerDest), { recursive: true })\n          await fs.copyFile(handlerSrc, handlerDest)\n          return\n        }\n\n        const htmlDest = join(\n          outDir,\n          `${route}${\n            subFolders && route !== '/index' ? `${sep}index` : ''\n          }.html`\n        )\n        const ampHtmlDest = join(\n          outDir,\n          `${route}.amp${subFolders ? `${sep}index` : ''}.html`\n        )\n        const jsonDest = isAppPath\n          ? join(\n              outDir,\n              `${route}${\n                subFolders && route !== '/index' ? `${sep}index` : ''\n              }.txt`\n            )\n          : join(pagesDataDir, `${route}.json`)\n\n        await fs.mkdir(dirname(htmlDest), { recursive: true })\n        await fs.mkdir(dirname(jsonDest), { recursive: true })\n\n        const htmlSrc = `${orig}.html`\n        const jsonSrc = `${orig}${isAppPath ? RSC_SUFFIX : '.json'}`\n\n        await fs.copyFile(htmlSrc, htmlDest)\n        await fs.copyFile(jsonSrc, jsonDest)\n\n        if (existsSync(`${orig}.amp.html`)) {\n          await fs.mkdir(dirname(ampHtmlDest), { recursive: true })\n          await fs.copyFile(`${orig}.amp.html`, ampHtmlDest)\n        }\n\n        const segmentsDir = `${orig}${RSC_SEGMENTS_DIR_SUFFIX}`\n        if (isAppPath && existsSync(segmentsDir)) {\n          // Output a data file for each of this page's segments\n          //\n          // These files are requested by the client router's internal\n          // prefetcher, not the user directly. So we don't need to account for\n          // things like trailing slash handling.\n          //\n          // To keep the protocol simple, we can use the non-normalized route\n          // path instead of the normalized one (which, among other things,\n          // rewrites `/` to `/index`).\n          const segmentsDirDest = join(outDir, unnormalizedRoute)\n          const segmentPaths = await collectSegmentPaths(segmentsDir)\n          await Promise.all(\n            segmentPaths.map(async (segmentFileSrc) => {\n              const segmentPath =\n                '/' + segmentFileSrc.slice(0, -RSC_SEGMENT_SUFFIX.length)\n              const segmentFilename =\n                convertSegmentPathToStaticExportFilename(segmentPath)\n              const segmentFileDest = join(segmentsDirDest, segmentFilename)\n              await fs.mkdir(dirname(segmentFileDest), { recursive: true })\n              await fs.copyFile(\n                join(segmentsDir, segmentFileSrc),\n                segmentFileDest\n              )\n            })\n          )\n        }\n      })\n    )\n  }\n\n  if (Object.keys(ampValidations).length) {\n    console.log(formatAmpMessages(ampValidations))\n  }\n  if (hadValidationError) {\n    throw new ExportError(\n      `AMP Validation caused the export to fail. https://nextjs.org/docs/messages/amp-export-validation`\n    )\n  }\n\n  if (failedExportAttemptsByPage.size > 0) {\n    const failedPages = Array.from(failedExportAttemptsByPage.keys())\n    throw new ExportError(\n      `Export encountered errors on following paths:\\n\\t${failedPages\n        .sort()\n        .join('\\n\\t')}`\n    )\n  }\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: true,\n    }),\n    'utf8'\n  )\n\n  if (telemetry) {\n    await telemetry.flush()\n  }\n\n  await worker.end()\n\n  return collector\n}\n\nasync function collectSegmentPaths(segmentsDirectory: string) {\n  const results: Array<string> = []\n  await collectSegmentPathsImpl(segmentsDirectory, segmentsDirectory, results)\n  return results\n}\n\nasync function collectSegmentPathsImpl(\n  segmentsDirectory: string,\n  directory: string,\n  results: Array<string>\n) {\n  const segmentFiles = await fs.readdir(directory, {\n    withFileTypes: true,\n  })\n  await Promise.all(\n    segmentFiles.map(async (segmentFile) => {\n      if (segmentFile.isDirectory()) {\n        await collectSegmentPathsImpl(\n          segmentsDirectory,\n          join(directory, segmentFile.name),\n          results\n        )\n        return\n      }\n      if (!segmentFile.name.endsWith(RSC_SEGMENT_SUFFIX)) {\n        return\n      }\n      results.push(\n        relative(segmentsDirectory, join(directory, segmentFile.name))\n      )\n    })\n  )\n}\n\nexport default async function exportApp(\n  dir: string,\n  options: ExportAppOptions,\n  span: Span\n): Promise<ExportAppResult | null> {\n  const nextExportSpan = span.traceChild('next-export')\n\n  return nextExportSpan.traceAsyncFn(async () => {\n    return await exportAppImpl(dir, options, nextExportSpan)\n  })\n}\n"], "names": ["ExportError", "exportApp", "Error", "code", "exportAppImpl", "dir", "options", "span", "nextConfig", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "loadEnvConfig", "Log", "enabledDirectories", "traceAsyncFn", "loadConfig", "PHASE_EXPORT", "debugPrerender", "distDir", "join", "telemetry", "buildExport", "Telemetry", "record", "eventCliSession", "webpackVersion", "cliCommand", "isSrcDir", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "isCustomServer", "turboFlag", "pagesDir", "appDir", "subFolders", "trailingSlash", "silent", "info", "buildIdFile", "BUILD_ID_FILE", "existsSync", "customRoutes", "filter", "config", "hasNextSupport", "length", "warn", "buildId", "fs", "readFile", "pagesManifest", "pages", "require", "SERVER_DIRECTORY", "PAGES_MANIFEST", "prerenderManifest", "PRERENDER_MANIFEST", "appRoutePathManifest", "APP_PATH_ROUTES_MANIFEST", "err", "isError", "undefined", "excludedPrerenderRoutes", "Set", "Object", "keys", "defaultPathMap", "hasApiRoutes", "page", "isAPIRoute", "dynamicRoutes", "add", "mapAppRouteToPage", "Map", "pageName", "routePath", "entries", "set", "isAppPageRoute", "routes", "_isAppDir", "outDir", "outdir", "rm", "recursive", "force", "mkdir", "writeFile", "EXPORT_DETAIL", "formatManifest", "version", "outDirectory", "success", "recursiveCopy", "CLIENT_STATIC_FILES_PATH", "exportPathMap", "defaultMap", "i18n", "images", "loader", "unoptimized", "isNextImageImported", "EXPORT_MARKER", "then", "text", "JSON", "parse", "catch", "serverActionsManifest", "app", "SERVER_REFERENCE_MANIFEST", "output", "routesManifest", "ROUTES_MANIFEST", "rewrites", "beforeFiles", "hasInterceptionRouteRewrite", "some", "isInterceptionRouteRewrite", "actionIds", "node", "edge", "actionId", "extractInfoFromServerReferenceId", "type", "renderOpts", "previewProps", "preview", "nextExport", "assetPrefix", "replace", "dev", "basePath", "canonicalBase", "amp", "ampSkipValidation", "experimental", "skipValidation", "ampOptimizerConfig", "optimizer", "locales", "locale", "defaultLocale", "domainLocales", "domains", "disableOptimizedLoading", "supportsDynamicResponse", "crossOrigin", "optimizeCss", "nextConfigOutput", "nextScriptWorkers", "largePageDataBytes", "serverActions", "serverComponents", "cacheLifeProfiles", "cacheLife", "nextFontManifest", "NEXT_FONT_MANIFEST", "deploymentId", "htmlLimitedBots", "source", "clientTraceMetadata", "expireTime", "staleTimes", "cacheComponents", "clientSegmentCache", "Boolean", "clientParamParsing", "dynamicOnHover", "inlineCss", "authInterrupts", "reactMaxHeadersLength", "hasReadableErrorStacks", "serverSourceMaps", "process", "env", "TURBOPACK", "turbopackMinify", "serverMinification", "enablePrerenderSourceMaps", "publicRuntimeConfig", "runtimeConfig", "globalThis", "__NEXT_DATA__", "exportMap", "allExportPaths", "seenExportPaths", "fallbackEnabledPages", "path", "entry", "normalizedPath", "denormalizePagePath", "normalizePagePath", "has", "push", "prerenderInfo", "fallback", "size", "SSG_FALLBACK_EXPORT_ERROR", "hasMiddleware", "functionsConfigManifest", "middlewareManifest", "MIDDLEWARE_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "middleware", "functions", "yellow", "bold", "pagesDataDir", "ampValidations", "publicDir", "CLIENT_PUBLIC_FILES_PATH", "exportPagesInBatches", "worker", "exportPaths", "renderResumeDataCachesByPage", "minPageCountPerBatch", "staticGenerationMinPagesPerWorker", "numWorkers", "Math", "min", "ceil", "pageCountPerBatch", "batches", "Array", "from", "_", "i", "slice", "remainingPages", "for<PERSON>ach", "index", "Promise", "all", "map", "batch", "exportPages", "parentSpanId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCache", "fetchCacheKeyPrefix", "flat", "initialPhaseExportPaths", "finalPhaseExportPaths", "exportPath", "_allowEmptyStaticShell", "progress", "createProgress", "statusMessage", "createStaticWorker", "debuggerPortOffset", "getNextBuildDebuggerPortOffset", "kind", "results", "result", "renderResumeDataCache", "finalPhaseResults", "hadValidationError", "collector", "by<PERSON><PERSON>", "byPage", "ssgNotFoundPaths", "turborepoAccessTraceResults", "failedExportAttemptsByPage", "page<PERSON><PERSON>", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "fromSerialized", "validation", "errors", "get", "cacheControl", "metadata", "hasEmptyStaticShell", "hasPostponed", "fetchMetrics", "ssgNotFound", "durations", "durationsByPath", "duration", "ppr", "unnormalizedRoute", "srcRoute", "appPageName", "isAppPath", "isAppRouteHandler", "isAppRouteRoute", "notFoundRoutes", "includes", "route", "pagePath", "getPagePath", "distPagesDir", "split", "orig", "handlerSrc", "handlerDest", "dirname", "copyFile", "htmlDest", "sep", "ampHtmlDest", "jsonDest", "htmlSrc", "jsonSrc", "RSC_SUFFIX", "segmentsDir", "RSC_SEGMENTS_DIR_SUFFIX", "segmentsDirDest", "segmentPaths", "collectSegmentPaths", "segmentFileSrc", "segmentPath", "RSC_SEGMENT_SUFFIX", "segmentFilename", "convertSegmentPathToStaticExportFilename", "segmentFileDest", "console", "log", "formatAmpMessages", "failedPages", "sort", "flush", "end", "segmentsDirectory", "collectSegmentPathsImpl", "directory", "segmentFiles", "readdir", "withFileTypes", "segmentFile", "isDirectory", "name", "endsWith", "relative", "nextExportSpan"], "mappings": ";;;;;;;;;;;;;;;IAyEaA,WAAW;eAAXA;;IAu2Bb,OAUC;eAV6BC;;;uBAr6BvB;4BAGsB;+DACV;oBACwB;QAEpC;sBAE+C;uBACpB;6DAEb;2BAMd;+BACuB;4BAiBvB;+DACgB;wBAES;wBACD;yBACL;mCACQ;qCACE;qBACN;4BACH;yBACC;iCAGI;gCACD;gEACX;gCACW;sCACY;0BACZ;oDAEY;qCAEM;sCACQ;wBACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExC,MAAMD,oBAAoBE;;QAA1B,qBACLC,OAAO;;AACT;AAEA,eAAeC,cACbC,GAAW,EACXC,OAAmC,EACnCC,IAAU;QAgSOC,iBACIA,8BACCA;IAhStBH,MAAMI,IAAAA,aAAO,EAACJ;IAEd,4EAA4E;IAC5EE,KAAKG,UAAU,CAAC,eAAeC,OAAO,CAAC,IAAMC,IAAAA,kBAAa,EAACP,KAAK,OAAOQ;IAEvE,MAAM,EAAEC,kBAAkB,EAAE,GAAGR;IAE/B,MAAME,aACJF,QAAQE,UAAU,IACjB,MAAMD,KAAKG,UAAU,CAAC,oBAAoBK,YAAY,CAAC,IACtDC,IAAAA,eAAU,EAACC,wBAAY,EAAEZ,KAAK;YAC5Ba,gBAAgBZ,QAAQY,cAAc;QACxC;IAGJ,MAAMC,UAAUC,IAAAA,UAAI,EAACf,KAAKG,WAAWW,OAAO;IAC5C,MAAME,YAAYf,QAAQgB,WAAW,GAAG,OAAO,IAAIC,kBAAS,CAAC;QAAEJ;IAAQ;IAEvE,IAAIE,WAAW;QACbA,UAAUG,MAAM,CACdC,IAAAA,uBAAe,EAACN,SAASX,YAAY;YACnCkB,gBAAgB;YAChBC,YAAY;YACZC,UAAU;YACVC,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;gBAAEC,KAAK1B;YAAI;YACnD2B,gBAAgB;YAChBC,WAAW;YACXC,UAAU;YACVC,QAAQ;QACV;IAEJ;IAEA,MAAMC,aAAa5B,WAAW6B,aAAa,IAAI,CAAC/B,QAAQgB,WAAW;IAEnE,IAAI,CAAChB,QAAQgC,MAAM,IAAI,CAAChC,QAAQgB,WAAW,EAAE;QAC3CT,KAAI0B,IAAI,CAAC,CAAC,uBAAuB,EAAEpB,SAAS;IAC9C;IAEA,MAAMqB,cAAcpB,IAAAA,UAAI,EAACD,SAASsB,yBAAa;IAE/C,IAAI,CAACC,IAAAA,cAAU,EAACF,cAAc;QAC5B,MAAM,qBAEL,CAFK,IAAIxC,YACR,CAAC,0CAA0C,EAAEmB,QAAQ,gJAAgJ,CAAC,GADlM,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMwB,eAAe;QAAC;QAAY;QAAa;KAAU,CAACC,MAAM,CAC9D,CAACC,SAAW,OAAOrC,UAAU,CAACqC,OAAO,KAAK;IAG5C,IAAI,CAACC,sBAAc,IAAI,CAACxC,QAAQgB,WAAW,IAAIqB,aAAaI,MAAM,GAAG,GAAG;QACtElC,KAAImC,IAAI,CACN,CAAC,4FAA4F,EAAEL,aAAavB,IAAI,CAC9G,MACA,+EAA+E,CAAC;IAEtF;IAEA,MAAM6B,UAAU,MAAMC,YAAE,CAACC,QAAQ,CAACX,aAAa;IAE/C,MAAMY,gBACJ,CAAC9C,QAAQ+C,KAAK,IACbC,QAAQlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAEC,0BAAc;IAEzD,IAAIC;IACJ,IAAI;QACFA,oBAAoBH,QAAQlC,IAAAA,UAAI,EAACD,SAASuC,8BAAkB;IAC9D,EAAE,OAAM,CAAC;IAET,IAAIC;IACJ,IAAI;QACFA,uBAAuBL,QAAQlC,IAAAA,UAAI,EAACD,SAASyC,oCAAwB;IACvE,EAAE,OAAOC,KAAK;QACZ,IACEC,IAAAA,gBAAO,EAACD,QACPA,CAAAA,IAAI1D,IAAI,KAAK,YAAY0D,IAAI1D,IAAI,KAAK,kBAAiB,GACxD;YACA,0DAA0D;YAC1D,oCAAoC;YACpCwD,uBAAuBI;QACzB,OAAO;YACL,2CAA2C;YAC3C,MAAMF;QACR;IACF;IAEA,MAAMG,0BAA0B,IAAIC;IACpC,MAAMZ,QAAQ/C,QAAQ+C,KAAK,IAAIa,OAAOC,IAAI,CAACf;IAC3C,MAAMgB,iBAAgC,CAAC;IAEvC,IAAIC,eAAe;IACnB,KAAK,MAAMC,QAAQjB,MAAO;QACxB,wCAAwC;QACxC,0CAA0C;QAC1C,mCAAmC;QAEnC,IAAIkB,IAAAA,sBAAU,EAACD,OAAO;YACpBD,eAAe;YACf;QACF;QAEA,IAAIC,SAAS,gBAAgBA,SAAS,WAAWA,SAAS,WAAW;YACnE;QACF;QAEA,qEAAqE;QACrE,yEAAyE;QACzE,yEAAyE;QACzE,8CAA8C;QAC9C,IAAIb,qCAAAA,kBAAmBe,aAAa,CAACF,KAAK,EAAE;YAC1CN,wBAAwBS,GAAG,CAACH;YAC5B;QACF;QAEAF,cAAc,CAACE,KAAK,GAAG;YAAEA;QAAK;IAChC;IAEA,MAAMI,oBAAoB,IAAIC;IAC9B,IAAI,CAACrE,QAAQgB,WAAW,IAAIqC,sBAAsB;QAChD,KAAK,MAAM,CAACiB,UAAUC,UAAU,IAAIX,OAAOY,OAAO,CAACnB,sBAAuB;YACxEe,kBAAkBK,GAAG,CAACF,WAAWD;YACjC,IACEI,IAAAA,8BAAc,EAACJ,aACf,EAACnB,qCAAAA,kBAAmBwB,MAAM,CAACJ,UAAU,KACrC,EAACpB,qCAAAA,kBAAmBe,aAAa,CAACK,UAAU,GAC5C;gBACAT,cAAc,CAACS,UAAU,GAAG;oBAC1BP,MAAMM;oBACNM,WAAW;gBACb;YACF;QACF;IACF;IAEA,kCAAkC;IAClC,MAAMC,SAAS7E,QAAQ8E,MAAM;IAE7B,IAAID,WAAW/D,IAAAA,UAAI,EAACf,KAAK,WAAW;QAClC,MAAM,qBAEL,CAFK,IAAIL,YACR,CAAC,wJAAwJ,CAAC,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAImF,WAAW/D,IAAAA,UAAI,EAACf,KAAK,WAAW;QAClC,MAAM,qBAEL,CAFK,IAAIL,YACR,CAAC,wJAAwJ,CAAC,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMkD,YAAE,CAACmC,EAAE,CAACF,QAAQ;QAAEG,WAAW;QAAMC,OAAO;IAAK;IACnD,MAAMrC,YAAE,CAACsC,KAAK,CAACpE,IAAAA,UAAI,EAAC+D,QAAQ,SAASlC,UAAU;QAAEqC,WAAW;IAAK;IAEjE,MAAMpC,YAAE,CAACuC,SAAS,CAChBrE,IAAAA,UAAI,EAACD,SAASuE,yBAAa,GAC3BC,IAAAA,8BAAc,EAAC;QACbC,SAAS;QACTC,cAAcV;QACdW,SAAS;IACX,IACA;IAGF,wBAAwB;IACxB,IAAI,CAACxF,QAAQgB,WAAW,IAAIoB,IAAAA,cAAU,EAACtB,IAAAA,UAAI,EAACf,KAAK,YAAY;QAC3D,IAAI,CAACC,QAAQgC,MAAM,EAAE;YACnBzB,KAAI0B,IAAI,CAAC;QACX;QACA,MAAMhC,KACHG,UAAU,CAAC,yBACXK,YAAY,CAAC,IACZgF,IAAAA,4BAAa,EAAC3E,IAAAA,UAAI,EAACf,KAAK,WAAWe,IAAAA,UAAI,EAAC+D,QAAQ;IAEtD;IAEA,8BAA8B;IAC9B,IACE,CAAC7E,QAAQgB,WAAW,IACpBoB,IAAAA,cAAU,EAACtB,IAAAA,UAAI,EAACD,SAAS6E,oCAAwB,IACjD;QACA,IAAI,CAAC1F,QAAQgC,MAAM,EAAE;YACnBzB,KAAI0B,IAAI,CAAC;QACX;QACA,MAAMhC,KACHG,UAAU,CAAC,8BACXK,YAAY,CAAC,IACZgF,IAAAA,4BAAa,EACX3E,IAAAA,UAAI,EAACD,SAAS6E,oCAAwB,GACtC5E,IAAAA,UAAI,EAAC+D,QAAQ,SAASa,oCAAwB;IAGtD;IAEA,6CAA6C;IAC7C,IAAI,OAAOxF,WAAWyF,aAAa,KAAK,YAAY;QAClDzF,WAAWyF,aAAa,GAAG,OAAOC;YAChC,OAAOA;QACT;IACF;IAEA,MAAM,EACJC,IAAI,EACJC,QAAQ,EAAEC,SAAS,SAAS,EAAEC,WAAW,EAAE,EAC5C,GAAG9F;IAEJ,IAAI2F,QAAQ,CAAC7F,QAAQgB,WAAW,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAItB,YACR,CAAC,8IAA8I,CAAC,GAD5I,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,CAACM,QAAQgB,WAAW,EAAE;QACxB,MAAM,EAAEiF,mBAAmB,EAAE,GAAG,MAAMhG,KACnCG,UAAU,CAAC,0BACXK,YAAY,CAAC,IACZmC,YAAE,CACCC,QAAQ,CAAC/B,IAAAA,UAAI,EAACD,SAASqF,yBAAa,GAAG,QACvCC,IAAI,CAAC,CAACC,OAASC,KAAKC,KAAK,CAACF,OAC1BG,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA;QAGrB,IACEN,uBACAF,WAAW,aACX,CAACC,eACD,CAACxD,sBAAc,EACf;YACA,MAAM,qBAML,CANK,IAAI9C,YACR,CAAC;;;;8DAIqD,CAAC,GALnD,qBAAA;uBAAA;4BAAA;8BAAA;YAMN;QACF;IACF;IAEA,IAAI8G;IACJ,IAAIhG,mBAAmBiG,GAAG,EAAE;QAC1BD,wBAAwBxD,QACtBlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAEyD,qCAAyB,GAAG;QAG9D,IAAIxG,WAAWyG,MAAM,KAAK,UAAU;gBAK9BC,sCAAAA;YAJJ,MAAMA,iBAAiB5D,QAAQlC,IAAAA,UAAI,EAACD,SAASgG,2BAAe;YAE5D,2FAA2F;YAC3F,6DAA6D;YAC7D,IAAID,CAAAA,mCAAAA,2BAAAA,eAAgBE,QAAQ,sBAAxBF,uCAAAA,yBAA0BG,WAAW,qBAArCH,qCAAuCnE,MAAM,IAAG,GAAG;gBACrD,MAAMuE,8BACJJ,eAAeE,QAAQ,CAACC,WAAW,CAACE,IAAI,CAACC,8DAA0B;gBAErE,IAAIF,6BAA6B;oBAC/B,MAAM,qBAEL,CAFK,IAAItH,YACR,CAAC,yKAAyK,CAAC,GADvK,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,MAAMyH,YAAY;mBACbvD,OAAOC,IAAI,CAAC2C,sBAAsBY,IAAI;mBACtCxD,OAAOC,IAAI,CAAC2C,sBAAsBa,IAAI;aAC1C;YAED,IACEF,UAAUF,IAAI,CACZ,CAACK,WACCC,IAAAA,qDAAgC,EAACD,UAAUE,IAAI,KAAK,kBAExD;gBACA,MAAM,qBAEL,CAFK,IAAI9H,YACR,CAAC,oKAAoK,CAAC,GADlK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM+H,aAAsC;QAC1CC,YAAY,EAAEvE,qCAAAA,kBAAmBwE,OAAO;QACxCC,YAAY;QACZC,aAAa3H,WAAW2H,WAAW,CAACC,OAAO,CAAC,OAAO;QACnDjH;QACAkH,KAAK;QACLC,UAAU9H,WAAW8H,QAAQ;QAC7BjG,eAAe7B,WAAW6B,aAAa;QACvCkG,eAAe/H,EAAAA,kBAAAA,WAAWgI,GAAG,qBAAdhI,gBAAgB+H,aAAa,KAAI;QAChDE,mBAAmBjI,EAAAA,+BAAAA,WAAWkI,YAAY,CAACF,GAAG,qBAA3BhI,6BAA6BmI,cAAc,KAAI;QAClEC,oBAAoBpI,EAAAA,gCAAAA,WAAWkI,YAAY,CAACF,GAAG,qBAA3BhI,8BAA6BqI,SAAS,KAAI9E;QAC9D+E,OAAO,EAAE3C,wBAAAA,KAAM2C,OAAO;QACtBC,MAAM,EAAE5C,wBAAAA,KAAM6C,aAAa;QAC3BA,aAAa,EAAE7C,wBAAAA,KAAM6C,aAAa;QAClCC,aAAa,EAAE9C,wBAAAA,KAAM+C,OAAO;QAC5BC,yBAAyB3I,WAAWkI,YAAY,CAACS,uBAAuB;QACxE,wDAAwD;QACxDC,yBAAyB;QACzBC,aAAa7I,WAAW6I,WAAW;QACnCC,aAAa9I,WAAWkI,YAAY,CAACY,WAAW;QAChDC,kBAAkB/I,WAAWyG,MAAM;QACnCuC,mBAAmBhJ,WAAWkI,YAAY,CAACc,iBAAiB;QAC5DC,oBAAoBjJ,WAAWkI,YAAY,CAACe,kBAAkB;QAC9DC,eAAelJ,WAAWkI,YAAY,CAACgB,aAAa;QACpDC,kBAAkB7I,mBAAmBiG,GAAG;QACxC6C,mBAAmBpJ,WAAWkI,YAAY,CAACmB,SAAS;QACpDC,kBAAkBxG,QAChBlC,IAAAA,UAAI,EAACD,SAAS,UAAU,GAAG4I,8BAAkB,CAAC,KAAK,CAAC;QAEtD3D,QAAQ5F,WAAW4F,MAAM;QACzB,GAAItF,mBAAmBiG,GAAG,GACtB;YACED;QACF,IACA,CAAC,CAAC;QACNkD,cAAcxJ,WAAWwJ,YAAY;QACrCC,iBAAiBzJ,WAAWyJ,eAAe,CAACC,MAAM;QAClDxB,cAAc;YACZyB,qBAAqB3J,WAAWkI,YAAY,CAACyB,mBAAmB;YAChEC,YAAY5J,WAAW4J,UAAU;YACjCC,YAAY7J,WAAWkI,YAAY,CAAC2B,UAAU;YAC9CC,iBAAiB9J,WAAWkI,YAAY,CAAC4B,eAAe,IAAI;YAC5DC,oBACE/J,WAAWkI,YAAY,CAAC6B,kBAAkB,KAAK,gBAC3C,gBACAC,QAAQhK,WAAWkI,YAAY,CAAC6B,kBAAkB;YACxDE,oBAAoBjK,WAAWkI,YAAY,CAAC+B,kBAAkB,IAAI;YAClEC,gBAAgBlK,WAAWkI,YAAY,CAACgC,cAAc,IAAI;YAC1DC,WAAWnK,WAAWkI,YAAY,CAACiC,SAAS,IAAI;YAChDC,gBAAgB,CAAC,CAACpK,WAAWkI,YAAY,CAACkC,cAAc;QAC1D;QACAC,uBAAuBrK,WAAWqK,qBAAqB;QACvDC,wBACEtK,WAAWkI,YAAY,CAACqC,gBAAgB,KAAK,QAC7C,mEAAmE;QACnE,0CAA0C;QACzCC,CAAAA,QAAQC,GAAG,CAACC,SAAS,GAClB1K,WAAWkI,YAAY,CAACyC,eAAe,KAAK,QAC5C3K,WAAWkI,YAAY,CAAC0C,kBAAkB,KAAK,KAAI,KACvD5K,WAAWkI,YAAY,CAAC2C,yBAAyB,KAAK;IAC1D;IAEA,MAAM,EAAEC,mBAAmB,EAAE,GAAG9K;IAEhC,IAAI0D,OAAOC,IAAI,CAACmH,qBAAqBvI,MAAM,GAAG,GAAG;QAC/CgF,WAAWwD,aAAa,GAAGD;IAC7B;IAEA,wDAAwD;;IACtDE,WAAmBC,aAAa,GAAG;QACnCvD,YAAY;IACd;IAEA,MAAMjC,gBAAgB,MAAM1F,KACzBG,UAAU,CAAC,uBACXK,YAAY,CAAC;QACZ,MAAM2K,YAAY,MAAMlL,WAAWyF,aAAa,CAAC7B,gBAAgB;YAC/DiE,KAAK;YACLhI;YACA8E;YACAhE;YACA8B;QACF;QACA,OAAOyI;IACT;IAEF,wDAAwD;IACxD,IAAI,CAACpL,QAAQgB,WAAW,EAAE;QACxB,4DAA4D;QAC5D,IAAI,CAAC2E,aAAa,CAAC,OAAO,EAAE;YAC1BA,aAAa,CAAC,OAAO,GAAG;gBAAE3B,MAAM;YAAU;QAC5C;QAEA;;;KAGC,GACD,IAAI,CAAC2B,aAAa,CAAC,YAAY,EAAE;YAC/B,yEAAyE;YACzEA,aAAa,CAAC,YAAY,GAAGA,aAAa,CAAC,OAAO;QACpD;IACF;IAEA,MAAM0F,iBAAoC,EAAE;IAC5C,MAAMC,kBAAkB,IAAI3H;IAC5B,MAAM4H,uBAAuB,IAAI5H;IAEjC,KAAK,MAAM,CAAC6H,MAAMC,MAAM,IAAI7H,OAAOY,OAAO,CAACmB,eAAgB;QACzD,kCAAkC;QAClC,MAAM+F,iBAAiBC,IAAAA,wCAAmB,EAACC,IAAAA,oCAAiB,EAACJ;QAE7D,IAAIF,gBAAgBO,GAAG,CAACH,iBAAiB;YACvC;QACF;QAEAJ,gBAAgBnH,GAAG,CAACuH;QAEpB,IAAI,CAACD,MAAM7G,SAAS,IAAIX,IAAAA,sBAAU,EAACwH,MAAMzH,IAAI,GAAG;YAC9CD,eAAe;YACf;QACF;QAEAsH,eAAeS,IAAI,CAAC;YAAE,GAAGL,KAAK;YAAED,MAAME;QAAe;QAErD,IAAIvI,qBAAqB,CAACnD,QAAQgB,WAAW,EAAE;YAC7C,MAAM+K,gBAAgB5I,kBAAkBe,aAAa,CAACuH,MAAMzH,IAAI,CAAC;YAEjE,IAAI+H,iBAAiBA,cAAcC,QAAQ,KAAK,OAAO;gBACrDT,qBAAqBpH,GAAG,CAACsH,MAAMzH,IAAI;YACrC;QACF;IACF;IAEA,IAAIqH,eAAe5I,MAAM,KAAK,GAAG;QAC/B,OAAO;IACT;IAEA,IAAI8I,qBAAqBU,IAAI,GAAG,GAAG;QACjC,MAAM,qBAIL,CAJK,IAAIvM,YACR,CAAC,wCAAwC,EAAE;eAAI6L;SAAqB,CAACzK,IAAI,CACvE,MACA,EAAE,EAAEoL,oCAAyB,CAAC,EAAE,CAAC,GAH/B,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,IAAIC,gBAAgB;IAEpB,IAAI,CAACnM,QAAQgB,WAAW,EAAE;QACxB,IAAI;gBAWQoL;YAVV,MAAMC,qBAAqBrJ,QACzBlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAEqJ,+BAAmB;YAGrD,MAAMF,0BAA0BpJ,QAC9BlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAEsJ,qCAAyB;YAG3DJ,gBACEvI,OAAOC,IAAI,CAACwI,mBAAmBG,UAAU,EAAE/J,MAAM,GAAG,KACpDyH,SAAQkC,qCAAAA,wBAAwBK,SAAS,qBAAjCL,kCAAmC,CAAC,eAAe;QAC/D,EAAE,OAAM,CAAC;QAET,kDAAkD;QAClD,IAAIrI,gBAAgBoI,eAAe;YACjC,IAAIjM,WAAWyG,MAAM,KAAK,UAAU;gBAClCpG,KAAImC,IAAI,CACNgK,IAAAA,kBAAM,EACJ,CAAC,kGAAkG,CAAC,IAEpG,CAAC,EAAE,CAAC,GACJA,IAAAA,kBAAM,EACJ,CAAC,mDAAmD,CAAC,GACnD,MACAC,IAAAA,gBAAI,EAAC,CAAC,8CAA8C,CAAC,KAEzD,CAAC,EAAE,CAAC,GACJD,IAAAA,kBAAM,EACJ,CAAC,2KAA2K,CAAC,IAE/K,CAAC,EAAE,CAAC,GACJA,IAAAA,kBAAM,EACJ,CAAC,qEAAqE,CAAC;YAG/E;QACF;IACF;IAEA,MAAME,eAAe5M,QAAQgB,WAAW,GACpC6D,SACA/D,IAAAA,UAAI,EAAC+D,QAAQ,cAAclC;IAE/B,MAAMkK,iBAAgC,CAAC;IAEvC,MAAMC,YAAYhM,IAAAA,UAAI,EAACf,KAAKgN,oCAAwB;IACpD,wBAAwB;IACxB,IAAI,CAAC/M,QAAQgB,WAAW,IAAIoB,IAAAA,cAAU,EAAC0K,YAAY;QACjD,IAAI,CAAC9M,QAAQgC,MAAM,EAAE;YACnBzB,KAAI0B,IAAI,CAAC;QACX;QACA,MAAMhC,KAAKG,UAAU,CAAC,yBAAyBK,YAAY,CAAC,IAC1DgF,IAAAA,4BAAa,EAACqH,WAAWjI,QAAQ;gBAC/BvC,QAAOkJ,IAAI;oBACT,8BAA8B;oBAC9B,OAAO,CAAC7F,aAAa,CAAC6F,KAAK;gBAC7B;YACF;IAEJ;IAEA,MAAMwB,uBAAuB,OAC3BC,QACAC,aACAC;QAEA,2EAA2E;QAC3E,0EAA0E;QAC1E,sEAAsE;QACtE,2DAA2D;QAC3D,MAAMC,uBACJlN,WAAWkI,YAAY,CAACiF,iCAAiC,IAAI;QAE/D,2EAA2E;QAC3E,8BAA8B;QAC9B,MAAMC,aAAaC,KAAKC,GAAG,CACzBxN,QAAQsN,UAAU,EAClBC,KAAKE,IAAI,CAACP,YAAYzK,MAAM,GAAG2K;QAGjC,qEAAqE;QACrE,MAAMM,oBAAoBH,KAAKE,IAAI,CAACP,YAAYzK,MAAM,GAAG6K;QAEzD,MAAMK,UAAUC,MAAMC,IAAI,CAAC;YAAEpL,QAAQ6K;QAAW,GAAG,CAACQ,GAAGC,IACrDb,YAAYc,KAAK,CAACD,IAAIL,mBAAmB,AAACK,CAAAA,IAAI,CAAA,IAAKL;QAGrD,8BAA8B;QAC9B,MAAMO,iBAAiBf,YAAYc,KAAK,CAACV,aAAaI;QACtDO,eAAeC,OAAO,CAAC,CAAClK,MAAMmK;YAC5BR,OAAO,CAACQ,QAAQR,QAAQlL,MAAM,CAAC,CAACqJ,IAAI,CAAC9H;QACvC;QAEA,OAAO,AACL,CAAA,MAAMoK,QAAQC,GAAG,CACfV,QAAQW,GAAG,CAAC,OAAOC,QACjBtB,OAAOuB,WAAW,CAAC;gBACjB7L;gBACAuK,aAAaqB;gBACbE,cAAcxO,KAAKyO,KAAK;gBACxB9B;gBACAnF;gBACAzH;gBACAD;gBACAc;gBACAgE;gBACA3E;gBACAyO,cAAczO,WAAWyO,YAAY;gBACrCC,oBAAoB1O,WAAW0O,kBAAkB;gBACjDC,YAAY;gBACZC,qBAAqB5O,WAAWkI,YAAY,CAAC0G,mBAAmB;gBAChE3B;YACF,IAEJ,EACA4B,IAAI;IACR;IAEA,IAAIC,0BAA6C,EAAE;IACnD,MAAMC,wBAA2C,EAAE;IAEnD,IAAIxH,WAAWW,YAAY,CAAC4B,eAAe,EAAE;QAC3C,KAAK,MAAMkF,cAAc7D,eAAgB;YACvC,IAAI6D,WAAWC,sBAAsB,EAAE;gBACrCF,sBAAsBnD,IAAI,CAACoD;YAC7B,OAAO;gBACLF,wBAAwBlD,IAAI,CAACoD;YAC/B;QACF;IACF,OAAO;QACLF,0BAA0B3D;IAC5B;IAEA,MAAM+D,WAAWC,IAAAA,wBAAc,EAC7BL,wBAAwBvM,MAAM,GAAGwM,sBAAsBxM,MAAM,EAC7DzC,QAAQsP,aAAa,IAAI;IAG3B,MAAMrC,SAASsC,IAAAA,yBAAkB,EAACrP,YAAY;QAC5CsP,oBAAoBC,IAAAA,sCAA8B,EAAC;YAAEC,MAAM;QAAc;QACzEN;IACF;IAEA,MAAMO,UAAU,MAAM3C,qBAAqBC,QAAQ+B;IAEnD,IAAIC,sBAAsBxM,MAAM,GAAG,GAAG;QACpC,MAAM0K,+BAAuD,CAAC;QAE9D,KAAK,MAAM,EAAEnJ,IAAI,EAAE4L,MAAM,EAAE,IAAID,QAAS;YACtC,IAAI,CAACC,QAAQ;gBACX;YACF;YAEA,IAAI,2BAA2BA,UAAUA,OAAOC,qBAAqB,EAAE;gBACrE,uEAAuE;gBACvE,uEAAuE;gBACvE,uBAAuB;gBACvB1C,4BAA4B,CAACnJ,KAAK,GAAG4L,OAAOC,qBAAqB;gBACjE,kEAAkE;gBAClE,4DAA4D;gBAC5DD,OAAOC,qBAAqB,GAAGpM;YACjC;QACF;QAEA,MAAMqM,oBAAoB,MAAM9C,qBAC9BC,QACAgC,uBACA9B;QAGFwC,QAAQ7D,IAAI,IAAIgE;IAClB;IAEA,IAAIC,qBAAqB;IAEzB,MAAMC,YAA6B;QACjCC,QAAQ,IAAI5L;QACZ6L,QAAQ,IAAI7L;QACZ8L,kBAAkB,IAAIxM;QACtByM,6BAA6B,IAAI/L;IACnC;IAEA,MAAMgM,6BAAmD,IAAIhM;IAE7D,KAAK,MAAM,EAAEuL,MAAM,EAAEpE,IAAI,EAAExH,IAAI,EAAEsM,OAAO,EAAE,IAAIX,QAAS;QACrD,IAAI,CAACC,QAAQ;QACb,IAAI,WAAWA,QAAQ;YACrBS,2BAA2B5L,GAAG,CAAC6L,SAAS;YACxC;QACF;QAEA,IAAIV,OAAOW,0BAA0B,EAAE;gBACrCP;aAAAA,yCAAAA,UAAUI,2BAA2B,qBAArCJ,uCAAuCvL,GAAG,CACxC+G,MACAgF,gDAA0B,CAACC,cAAc,CACvCb,OAAOW,0BAA0B;QAGvC;QAEA,+BAA+B;QAC/B,IAAIX,OAAO/C,cAAc,EAAE;YACzB,KAAK,MAAM6D,cAAcd,OAAO/C,cAAc,CAAE;gBAC9CA,cAAc,CAAC6D,WAAW1M,IAAI,CAAC,GAAG0M,WAAWd,MAAM;gBACnDG,uBAAuBW,WAAWd,MAAM,CAACe,MAAM,CAAClO,MAAM,GAAG;YAC3D;QACF;QAEA,IAAIzC,QAAQgB,WAAW,EAAE;YACvB,4BAA4B;YAC5B,MAAMiB,OAAO+N,UAAUC,MAAM,CAACW,GAAG,CAACpF,SAAS,CAAC;YAC5C,IAAIoE,OAAOiB,YAAY,EAAE;gBACvB5O,KAAK4O,YAAY,GAAGjB,OAAOiB,YAAY;YACzC;YACA,IAAI,OAAOjB,OAAOkB,QAAQ,KAAK,aAAa;gBAC1C7O,KAAK6O,QAAQ,GAAGlB,OAAOkB,QAAQ;YACjC;YAEA,IAAI,OAAOlB,OAAOmB,mBAAmB,KAAK,aAAa;gBACrD9O,KAAK8O,mBAAmB,GAAGnB,OAAOmB,mBAAmB;YACvD;YAEA,IAAI,OAAOnB,OAAOoB,YAAY,KAAK,aAAa;gBAC9C/O,KAAK+O,YAAY,GAAGpB,OAAOoB,YAAY;YACzC;YAEA,IAAI,OAAOpB,OAAOqB,YAAY,KAAK,aAAa;gBAC9ChP,KAAKgP,YAAY,GAAGrB,OAAOqB,YAAY;YACzC;YAEAjB,UAAUC,MAAM,CAACxL,GAAG,CAAC+G,MAAMvJ;YAE3B,oBAAoB;YACpB,IAAI2N,OAAOsB,WAAW,KAAK,MAAM;gBAC/BlB,UAAUG,gBAAgB,CAAChM,GAAG,CAACqH;YACjC;YAEA,oBAAoB;YACpB,MAAM2F,YAAYnB,UAAUE,MAAM,CAACU,GAAG,CAAC5M,SAAS;gBAC9CoN,iBAAiB,IAAI/M;YACvB;YACA8M,UAAUC,eAAe,CAAC3M,GAAG,CAAC+G,MAAMoE,OAAOyB,QAAQ;YACnDrB,UAAUE,MAAM,CAACzL,GAAG,CAACT,MAAMmN;QAC7B;IACF;IAEA,4EAA4E;IAC5E,IAAI,CAACnR,QAAQgB,WAAW,IAAId,WAAWkI,YAAY,CAACkJ,GAAG,EAAE;QACvD,oBAAoB;QACpB,MAAM,qBAA4D,CAA5D,IAAI1R,MAAM,oDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA2D;IACnE;IAEA,oCAAoC;IACpC,IAAI,CAACI,QAAQgB,WAAW,IAAImC,mBAAmB;QAC7C,MAAMiL,QAAQC,GAAG,CACfzK,OAAOC,IAAI,CAACV,kBAAkBwB,MAAM,EAAE2J,GAAG,CAAC,OAAOiD;YAC/C,wEAAwE;YACxE,IAAIA,sBAAsB,eAAe;gBACvC;YACF;YACA,MAAM,EAAEC,QAAQ,EAAE,GAAGrO,kBAAmBwB,MAAM,CAAC4M,kBAAkB;YACjE,MAAME,cAAcrN,kBAAkBwM,GAAG,CAACY,YAAY;YACtD,MAAMlN,WAAWmN,eAAeD,YAAYD;YAC5C,MAAMG,YAAYxH,QAAQuH;YAC1B,MAAME,oBAAoBF,eAAeG,IAAAA,gCAAe,EAACH;YAEzD,wDAAwD;YACxD,0CAA0C;YAC1C,IAAItO,kBAAmB0O,cAAc,CAACC,QAAQ,CAACP,oBAAoB;gBACjE;YACF;YACA,sEAAsE;YACtE,qEAAqE;YACrE,sEAAsE;YACtE,8BAA8B;YAC9B,MAAMQ,QAAQnG,IAAAA,oCAAiB,EAAC2F;YAEhC,MAAMS,WAAWC,IAAAA,oBAAW,EAAC3N,UAAUzD,SAAS4C,WAAWiO;YAC3D,MAAMQ,eAAepR,IAAAA,UAAI,EACvBkR,UACA,yDAAyD;YACzD,4BAA4B;YAC5B1N,SACG0J,KAAK,CAAC,GACNmE,KAAK,CAAC,KACN7D,GAAG,CAAC,IAAM,MACVxN,IAAI,CAAC;YAGV,MAAMsR,OAAOtR,IAAAA,UAAI,EAACoR,cAAcH;YAChC,MAAMM,aAAa,GAAGD,KAAK,KAAK,CAAC;YACjC,MAAME,cAAcxR,IAAAA,UAAI,EAAC+D,QAAQkN;YAEjC,IAAIJ,qBAAqBvP,IAAAA,cAAU,EAACiQ,aAAa;gBAC/C,MAAMzP,YAAE,CAACsC,KAAK,CAACqN,IAAAA,aAAO,EAACD,cAAc;oBAAEtN,WAAW;gBAAK;gBACvD,MAAMpC,YAAE,CAAC4P,QAAQ,CAACH,YAAYC;gBAC9B;YACF;YAEA,MAAMG,WAAW3R,IAAAA,UAAI,EACnB+D,QACA,GAAGkN,QACDjQ,cAAciQ,UAAU,WAAW,GAAGW,SAAG,CAAC,KAAK,CAAC,GAAG,GACpD,KAAK,CAAC;YAET,MAAMC,cAAc7R,IAAAA,UAAI,EACtB+D,QACA,GAAGkN,MAAM,IAAI,EAAEjQ,aAAa,GAAG4Q,SAAG,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;YAEvD,MAAME,WAAWlB,YACb5Q,IAAAA,UAAI,EACF+D,QACA,GAAGkN,QACDjQ,cAAciQ,UAAU,WAAW,GAAGW,SAAG,CAAC,KAAK,CAAC,GAAG,GACpD,IAAI,CAAC,IAER5R,IAAAA,UAAI,EAAC8L,cAAc,GAAGmF,MAAM,KAAK,CAAC;YAEtC,MAAMnP,YAAE,CAACsC,KAAK,CAACqN,IAAAA,aAAO,EAACE,WAAW;gBAAEzN,WAAW;YAAK;YACpD,MAAMpC,YAAE,CAACsC,KAAK,CAACqN,IAAAA,aAAO,EAACK,WAAW;gBAAE5N,WAAW;YAAK;YAEpD,MAAM6N,UAAU,GAAGT,KAAK,KAAK,CAAC;YAC9B,MAAMU,UAAU,GAAGV,OAAOV,YAAYqB,qBAAU,GAAG,SAAS;YAE5D,MAAMnQ,YAAE,CAAC4P,QAAQ,CAACK,SAASJ;YAC3B,MAAM7P,YAAE,CAAC4P,QAAQ,CAACM,SAASF;YAE3B,IAAIxQ,IAAAA,cAAU,EAAC,GAAGgQ,KAAK,SAAS,CAAC,GAAG;gBAClC,MAAMxP,YAAE,CAACsC,KAAK,CAACqN,IAAAA,aAAO,EAACI,cAAc;oBAAE3N,WAAW;gBAAK;gBACvD,MAAMpC,YAAE,CAAC4P,QAAQ,CAAC,GAAGJ,KAAK,SAAS,CAAC,EAAEO;YACxC;YAEA,MAAMK,cAAc,GAAGZ,OAAOa,kCAAuB,EAAE;YACvD,IAAIvB,aAAatP,IAAAA,cAAU,EAAC4Q,cAAc;gBACxC,sDAAsD;gBACtD,EAAE;gBACF,4DAA4D;gBAC5D,qEAAqE;gBACrE,uCAAuC;gBACvC,EAAE;gBACF,mEAAmE;gBACnE,iEAAiE;gBACjE,6BAA6B;gBAC7B,MAAME,kBAAkBpS,IAAAA,UAAI,EAAC+D,QAAQ0M;gBACrC,MAAM4B,eAAe,MAAMC,oBAAoBJ;gBAC/C,MAAM5E,QAAQC,GAAG,CACf8E,aAAa7E,GAAG,CAAC,OAAO+E;oBACtB,MAAMC,cACJ,MAAMD,eAAerF,KAAK,CAAC,GAAG,CAACuF,6BAAkB,CAAC9Q,MAAM;oBAC1D,MAAM+Q,kBACJC,IAAAA,8DAAwC,EAACH;oBAC3C,MAAMI,kBAAkB5S,IAAAA,UAAI,EAACoS,iBAAiBM;oBAC9C,MAAM5Q,YAAE,CAACsC,KAAK,CAACqN,IAAAA,aAAO,EAACmB,kBAAkB;wBAAE1O,WAAW;oBAAK;oBAC3D,MAAMpC,YAAE,CAAC4P,QAAQ,CACf1R,IAAAA,UAAI,EAACkS,aAAaK,iBAClBK;gBAEJ;YAEJ;QACF;IAEJ;IAEA,IAAI9P,OAAOC,IAAI,CAACgJ,gBAAgBpK,MAAM,EAAE;QACtCkR,QAAQC,GAAG,CAACC,IAAAA,wBAAiB,EAAChH;IAChC;IACA,IAAIkD,oBAAoB;QACtB,MAAM,qBAEL,CAFK,IAAIrQ,YACR,CAAC,gGAAgG,CAAC,GAD9F,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI2Q,2BAA2BpE,IAAI,GAAG,GAAG;QACvC,MAAM6H,cAAclG,MAAMC,IAAI,CAACwC,2BAA2BxM,IAAI;QAC9D,MAAM,qBAIL,CAJK,IAAInE,YACR,CAAC,iDAAiD,EAAEoU,YACjDC,IAAI,GACJjT,IAAI,CAAC,SAAS,GAHb,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,MAAM8B,YAAE,CAACuC,SAAS,CAChBrE,IAAAA,UAAI,EAACD,SAASuE,yBAAa,GAC3BC,IAAAA,8BAAc,EAAC;QACbC,SAAS;QACTC,cAAcV;QACdW,SAAS;IACX,IACA;IAGF,IAAIzE,WAAW;QACb,MAAMA,UAAUiT,KAAK;IACvB;IAEA,MAAM/G,OAAOgH,GAAG;IAEhB,OAAOjE;AACT;AAEA,eAAeoD,oBAAoBc,iBAAyB;IAC1D,MAAMvE,UAAyB,EAAE;IACjC,MAAMwE,wBAAwBD,mBAAmBA,mBAAmBvE;IACpE,OAAOA;AACT;AAEA,eAAewE,wBACbD,iBAAyB,EACzBE,SAAiB,EACjBzE,OAAsB;IAEtB,MAAM0E,eAAe,MAAMzR,YAAE,CAAC0R,OAAO,CAACF,WAAW;QAC/CG,eAAe;IACjB;IACA,MAAMnG,QAAQC,GAAG,CACfgG,aAAa/F,GAAG,CAAC,OAAOkG;QACtB,IAAIA,YAAYC,WAAW,IAAI;YAC7B,MAAMN,wBACJD,mBACApT,IAAAA,UAAI,EAACsT,WAAWI,YAAYE,IAAI,GAChC/E;YAEF;QACF;QACA,IAAI,CAAC6E,YAAYE,IAAI,CAACC,QAAQ,CAACpB,6BAAkB,GAAG;YAClD;QACF;QACA5D,QAAQ7D,IAAI,CACV8I,IAAAA,cAAQ,EAACV,mBAAmBpT,IAAAA,UAAI,EAACsT,WAAWI,YAAYE,IAAI;IAEhE;AAEJ;AAEe,eAAe/U,UAC5BI,GAAW,EACXC,OAAyB,EACzBC,IAAU;IAEV,MAAM4U,iBAAiB5U,KAAKG,UAAU,CAAC;IAEvC,OAAOyU,eAAepU,YAAY,CAAC;QACjC,OAAO,MAAMX,cAAcC,KAAKC,SAAS6U;IAC3C;AACF", "ignoreList": [0]}