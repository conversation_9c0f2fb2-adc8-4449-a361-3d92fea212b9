{"version": 3, "sources": ["../../../src/server/app-render/segment-explorer-path.ts"], "sourcesContent": ["import type { LoaderTree } from '../lib/app-dir-module'\n\nexport const BUILTIN_PREFIX = '__next_builtin__'\n\nconst nextInternalPrefixRegex =\n  /^(.*[\\\\/])?next[\\\\/]dist[\\\\/]client[\\\\/]components[\\\\/]builtin[\\\\/]/\n\nexport function normalizeConventionFilePath(\n  projectDir: string,\n  conventionPath: string | undefined\n) {\n  // Turbopack project path is formed as: \"<project root>/<cwd>\".\n  // When project root is not the working directory, we can extract the relative project root path.\n  // This is mostly used for running Next.js inside a monorepo.\n  const cwd = process.env.NEXT_RUNTIME === 'edge' ? '' : process.cwd()\n  const relativeProjectRoot = projectDir.replace(cwd, '')\n\n  let relativePath = (conventionPath || '')\n    // remove turbopack [project] prefix\n    .replace(/^\\[project\\]/, '')\n    // remove turbopack relative project path, everything after [project] and before the working directory.\n    .replace(relativeProjectRoot, '')\n    // remove the project root from the path\n    .replace(projectDir, '')\n    // remove cwd prefix\n    .replace(cwd, '')\n    // remove /(src/)?app/ dir prefix\n    .replace(/^([\\\\/])*(src[\\\\/])?app[\\\\/]/, '')\n\n  // If it's internal file only keep the filename, strip nextjs internal prefix\n  if (nextInternalPrefixRegex.test(relativePath)) {\n    relativePath = relativePath.replace(nextInternalPrefixRegex, '')\n    // Add a special prefix to let segment explorer know it's a built-in component\n    relativePath = `${BUILTIN_PREFIX}${relativePath}`\n  }\n\n  return relativePath.replace(/\\\\/g, '/')\n}\n\n// if a filepath is a builtin file. e.g.\n// .../project/node_modules/next/dist/client/components/builtin/global-error.js -> true\n// .../project/app/global-error.js -> false\nexport const isNextjsBuiltinFilePath = (filePath: string) => {\n  return nextInternalPrefixRegex.test(filePath)\n}\n\nexport const BOUNDARY_SUFFIX = '@boundary'\nexport function normalizeBoundaryFilename(filename: string) {\n  return filename\n    .replace(new RegExp(`^${BUILTIN_PREFIX}`), '')\n    .replace(new RegExp(`${BOUNDARY_SUFFIX}$`), '')\n}\n\nexport const BOUNDARY_PREFIX = 'boundary:'\nexport function isBoundaryFile(fileType: string) {\n  return fileType.startsWith(BOUNDARY_PREFIX)\n}\n\n// if a filename is a builtin file.\n// __next_builtin__global-error.js -> true\n// page.js -> false\nexport function isBuiltinBoundaryFile(fileType: string) {\n  return fileType.startsWith(BUILTIN_PREFIX)\n}\n\nexport function getBoundaryOriginFileType(fileType: string) {\n  return fileType.replace(BOUNDARY_PREFIX, '')\n}\n\nexport function getConventionPathByType(\n  tree: LoaderTree,\n  dir: string,\n  conventionType:\n    | 'layout'\n    | 'template'\n    | 'page'\n    | 'not-found'\n    | 'error'\n    | 'loading'\n    | 'forbidden'\n    | 'unauthorized'\n    | 'defaultPage'\n    | 'global-error'\n) {\n  const modules = tree[2]\n  const conventionPath = modules[conventionType]\n    ? modules[conventionType][1]\n    : undefined\n  if (conventionPath) {\n    return normalizeConventionFilePath(dir, conventionPath)\n  }\n  return undefined\n}\n"], "names": ["BOUNDARY_PREFIX", "BOUNDARY_SUFFIX", "BUILTIN_PREFIX", "getBoundaryOriginFileType", "getConventionPathByType", "isBoundaryFile", "isBuiltinBoundaryFile", "isNextjsBuiltinFilePath", "normalizeBoundaryFilename", "normalizeConventionFilePath", "nextInternalPrefixRegex", "projectDir", "conventionPath", "cwd", "process", "env", "NEXT_RUNTIME", "relativeProjectRoot", "replace", "relativePath", "test", "filePath", "filename", "RegExp", "fileType", "startsWith", "tree", "dir", "conventionType", "modules", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAqDaA,eAAe;eAAfA;;IAPAC,eAAe;eAAfA;;IA5CAC,cAAc;eAAdA;;IA+DGC,yBAAyB;eAAzBA;;IAIAC,uBAAuB;eAAvBA;;IAfAC,cAAc;eAAdA;;IAOAC,qBAAqB;eAArBA;;IAnBHC,uBAAuB;eAAvBA;;IAKGC,yBAAyB;eAAzBA;;IAxCAC,2BAA2B;eAA3BA;;;AALT,MAAMP,iBAAiB;AAE9B,MAAMQ,0BACJ;AAEK,SAASD,4BACdE,UAAkB,EAClBC,cAAkC;IAElC,+DAA+D;IAC/D,iGAAiG;IACjG,6DAA6D;IAC7D,MAAMC,MAAMC,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,KAAKF,QAAQD,GAAG;IAClE,MAAMI,sBAAsBN,WAAWO,OAAO,CAACL,KAAK;IAEpD,IAAIM,eAAe,AAACP,CAAAA,kBAAkB,EAAC,CACrC,oCAAoC;KACnCM,OAAO,CAAC,gBAAgB,GACzB,uGAAuG;KACtGA,OAAO,CAACD,qBAAqB,GAC9B,wCAAwC;KACvCC,OAAO,CAACP,YAAY,GACrB,oBAAoB;KACnBO,OAAO,CAACL,KAAK,GACd,iCAAiC;KAChCK,OAAO,CAAC,gCAAgC;IAE3C,6EAA6E;IAC7E,IAAIR,wBAAwBU,IAAI,CAACD,eAAe;QAC9CA,eAAeA,aAAaD,OAAO,CAACR,yBAAyB;QAC7D,8EAA8E;QAC9ES,eAAe,GAAGjB,iBAAiBiB,cAAc;IACnD;IAEA,OAAOA,aAAaD,OAAO,CAAC,OAAO;AACrC;AAKO,MAAMX,0BAA0B,CAACc;IACtC,OAAOX,wBAAwBU,IAAI,CAACC;AACtC;AAEO,MAAMpB,kBAAkB;AACxB,SAASO,0BAA0Bc,QAAgB;IACxD,OAAOA,SACJJ,OAAO,CAAC,IAAIK,OAAO,CAAC,CAAC,EAAErB,gBAAgB,GAAG,IAC1CgB,OAAO,CAAC,IAAIK,OAAO,GAAGtB,gBAAgB,CAAC,CAAC,GAAG;AAChD;AAEO,MAAMD,kBAAkB;AACxB,SAASK,eAAemB,QAAgB;IAC7C,OAAOA,SAASC,UAAU,CAACzB;AAC7B;AAKO,SAASM,sBAAsBkB,QAAgB;IACpD,OAAOA,SAASC,UAAU,CAACvB;AAC7B;AAEO,SAASC,0BAA0BqB,QAAgB;IACxD,OAAOA,SAASN,OAAO,CAAClB,iBAAiB;AAC3C;AAEO,SAASI,wBACdsB,IAAgB,EAChBC,GAAW,EACXC,cAUkB;IAElB,MAAMC,UAAUH,IAAI,CAAC,EAAE;IACvB,MAAMd,iBAAiBiB,OAAO,CAACD,eAAe,GAC1CC,OAAO,CAACD,eAAe,CAAC,EAAE,GAC1BE;IACJ,IAAIlB,gBAAgB;QAClB,OAAOH,4BAA4BkB,KAAKf;IAC1C;IACA,OAAOkB;AACT", "ignoreList": [0]}