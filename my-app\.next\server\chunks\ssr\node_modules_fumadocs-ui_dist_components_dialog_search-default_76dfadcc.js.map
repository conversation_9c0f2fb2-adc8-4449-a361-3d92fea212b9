{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-ui/dist/components/dialog/search-default.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useDocsSearch } from 'fumadocs-core/search/client';\nimport { useMemo, useState } from 'react';\nimport { useOnChange } from 'fumadocs-core/utils/use-on-change';\nimport { useI18n } from '../../contexts/i18n.js';\nimport { SearchDialog, SearchDialogClose, SearchDialogContent, SearchDialogFooter, SearchDialogHeader, SearchDialogIcon, SearchDialogInput, SearchDialogList, SearchDialogOverlay, TagsList, TagsListItem, } from './search.js';\nexport default function DefaultSearchDialog({ defaultTag, tags = [], api, delayMs, type = 'fetch', allowClear = false, links = [], footer, ...props }) {\n    const { locale } = useI18n();\n    const [tag, setTag] = useState(defaultTag);\n    const { search, setSearch, query } = useDocsSearch(type === 'fetch'\n        ? {\n            type: 'fetch',\n            api,\n            locale,\n            tag,\n            delayMs,\n        }\n        : {\n            type: 'static',\n            from: api,\n            locale,\n            tag,\n            delayMs,\n        });\n    const defaultItems = useMemo(() => {\n        if (links.length === 0)\n            return null;\n        return links.map(([name, link]) => ({\n            type: 'page',\n            id: name,\n            content: name,\n            url: link,\n        }));\n    }, [links]);\n    useOnChange(defaultTag, (v) => {\n        setTag(v);\n    });\n    return (_jsxs(SearchDialog, { search: search, onSearchChange: setSearch, isLoading: query.isLoading, ...props, children: [_jsx(SearchDialogOverlay, {}), _jsxs(SearchDialogContent, { children: [_jsxs(SearchDialogHeader, { children: [_jsx(SearchDialogIcon, {}), _jsx(SearchDialogInput, {}), _jsx(SearchDialogClose, {})] }), _jsx(SearchDialogList, { items: query.data !== 'empty' ? query.data : defaultItems })] }), _jsxs(SearchDialogFooter, { children: [tags.length > 0 && (_jsx(TagsList, { tag: tag, onTagChange: setTag, allowClear: allowClear, children: tags.map((tag) => (_jsx(TagsListItem, { value: tag.value, children: tag.name }, tag.value))) })), footer] })] }));\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAOe,SAAS,oBAAoB,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,OAAO,EAAE,aAAa,KAAK,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,OAAO;IACjJ,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD;IACzB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,UACtD;QACE,MAAM;QACN;QACA;QACA;QACA;IACJ,IACE;QACE,MAAM;QACN,MAAM;QACN;QACA;QACA;IACJ;IACJ,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,IAAI,MAAM,MAAM,KAAK,GACjB,OAAO;QACX,OAAO,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;gBAChC,MAAM;gBACN,IAAI;gBACJ,SAAS;gBACT,KAAK;YACT,CAAC;IACL,GAAG;QAAC;KAAM;IACV,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,YAAY,CAAC;QACrB,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,wKAAA,CAAA,eAAY,EAAE;QAAE,QAAQ;QAAQ,gBAAgB;QAAW,WAAW,MAAM,SAAS;QAAE,GAAG,KAAK;QAAE,UAAU;YAAC,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wKAAA,CAAA,sBAAmB,EAAE,CAAC;YAAI,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,wKAAA,CAAA,sBAAmB,EAAE;gBAAE,UAAU;oBAAC,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,wKAAA,CAAA,qBAAkB,EAAE;wBAAE,UAAU;4BAAC,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wKAAA,CAAA,mBAAgB,EAAE,CAAC;4BAAI,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wKAAA,CAAA,oBAAiB,EAAE,CAAC;4BAAI,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wKAAA,CAAA,oBAAiB,EAAE,CAAC;yBAAG;oBAAC;oBAAI,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wKAAA,CAAA,mBAAgB,EAAE;wBAAE,OAAO,MAAM,IAAI,KAAK,UAAU,MAAM,IAAI,GAAG;oBAAa;iBAAG;YAAC;YAAI,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,wKAAA,CAAA,qBAAkB,EAAE;gBAAE,UAAU;oBAAC,KAAK,MAAM,GAAG,KAAM,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wKAAA,CAAA,WAAQ,EAAE;wBAAE,KAAK;wBAAK,aAAa;wBAAQ,YAAY;wBAAY,UAAU,KAAK,GAAG,CAAC,CAAC,MAAS,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wKAAA,CAAA,eAAY,EAAE;gCAAE,OAAO,IAAI,KAAK;gCAAE,UAAU,IAAI,IAAI;4BAAC,GAAG,IAAI,KAAK;oBAAI;oBAAK;iBAAO;YAAC;SAAG;IAAC;AAC5pB", "ignoreList": [0], "debugId": null}}]}