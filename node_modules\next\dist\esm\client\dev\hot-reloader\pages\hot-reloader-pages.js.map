{"version": 3, "sources": ["../../../../../src/client/dev/hot-reloader/pages/hot-reloader-pages.ts"], "sourcesContent": ["// TODO: Remove use of `any` type. Fix no-use-before-define violations.\n/* eslint-disable @typescript-eslint/no-use-before-define */\n/**\n * MIT License\n *\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n/// <reference types=\"webpack/module.d.ts\" />\n\n// This file is a modified version of the Create React App HMR dev client that\n// can be found here:\n// https://github.com/facebook/create-react-app/blob/v3.4.1/packages/react-dev-utils/webpackHotDevClient.js\n\n/// <reference types=\"webpack/module.d.ts\" />\n\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { register } from '../../../../next-devtools/userspace/pages/pages-dev-overlay-setup'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { addMessageListener, sendMessage } from './websocket'\nimport formatWebpackMessages from '../../../../shared/lib/format-webpack-messages'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport {\n  REACT_REFRESH_FULL_RELOAD,\n  REACT_REFRESH_FULL_RELOAD_FROM_ERROR,\n  reportInvalidHmrMessage,\n} from '../shared'\nimport { RuntimeErrorHandler } from '../../runtime-error-handler'\nimport reportHmrLatency from '../../report-hmr-latency'\nimport { TurbopackHmr } from '../turbopack-hot-reloader-common'\n\n// This alternative WebpackDevServer combines the functionality of:\n// https://github.com/webpack/webpack-dev-server/blob/webpack-1/client/index.js\n// https://github.com/webpack/webpack/blob/webpack-1/hot/dev-server.js\n\n// It only supports their simplest configuration (hot updates on same server).\n// It makes some opinionated choices on top, like adding a syntax error overlay\n// that looks similar to our console output. The error overlay is inspired by:\n// https://github.com/glenjamin/webpack-hot-middleware\n\ndeclare global {\n  interface Window {\n    __nextDevClientId: number\n  }\n}\n\nwindow.__nextDevClientId = Math.round(Math.random() * 100 + Date.now())\n\nlet customHmrEventHandler: any\nlet turbopackMessageListeners: ((msg: TurbopackMsgToBrowser) => void)[] = []\nexport default function connect() {\n  register()\n\n  addMessageListener((payload) => {\n    if (!('action' in payload)) {\n      return\n    }\n\n    try {\n      processMessage(payload)\n    } catch (err: unknown) {\n      reportInvalidHmrMessage(payload, err)\n    }\n  })\n\n  return {\n    subscribeToHmrEvent(handler: any) {\n      customHmrEventHandler = handler\n    },\n    onUnrecoverableError() {\n      RuntimeErrorHandler.hadRuntimeError = true\n    },\n    addTurbopackMessageListener(cb: (msg: TurbopackMsgToBrowser) => void) {\n      turbopackMessageListeners.push(cb)\n    },\n    sendTurbopackMessage(msg: string) {\n      sendMessage(msg)\n    },\n    handleUpdateError(err: unknown) {\n      performFullReload(err)\n    },\n  }\n}\n\n// Remember some state related to hot module replacement.\nvar isFirstCompilation = true\nvar mostRecentCompilationHash: string | null = null\nvar hasCompileErrors = false\n\nfunction clearOutdatedErrors() {\n  // Clean up outdated compile errors, if any.\n  if (typeof console !== 'undefined' && typeof console.clear === 'function') {\n    if (hasCompileErrors) {\n      console.clear()\n    }\n  }\n}\n\n// Successful compilation.\nfunction handleSuccess() {\n  clearOutdatedErrors()\n  hasCompileErrors = false\n\n  if (process.env.TURBOPACK) {\n    const hmrUpdate = turbopackHmr!.onBuilt()\n    if (hmrUpdate != null) {\n      reportHmrLatency(\n        sendMessage,\n        [...hmrUpdate.updatedModules],\n        hmrUpdate.startMsSinceEpoch,\n        hmrUpdate.endMsSinceEpoch,\n        hmrUpdate.hasUpdates\n      )\n    }\n    dispatcher.onBuildOk()\n  } else {\n    const isHotUpdate =\n      !isFirstCompilation ||\n      (window.__NEXT_DATA__.page !== '/_error' && isUpdateAvailable())\n\n    // Attempt to apply hot updates or reload.\n    if (isHotUpdate) {\n      tryApplyUpdatesWebpack()\n    }\n  }\n\n  isFirstCompilation = false\n}\n\n// Compilation with warnings (e.g. ESLint).\nfunction handleWarnings(warnings: any) {\n  clearOutdatedErrors()\n\n  const isHotUpdate = !isFirstCompilation\n  isFirstCompilation = false\n  hasCompileErrors = false\n\n  function printWarnings() {\n    // Print warnings to the console.\n    const formatted = formatWebpackMessages({\n      warnings: warnings,\n      errors: [],\n    })\n\n    if (typeof console !== 'undefined' && typeof console.warn === 'function') {\n      for (let i = 0; i < formatted.warnings?.length; i++) {\n        if (i === 5) {\n          console.warn(\n            'There were more warnings in other files.\\n' +\n              'You can find a complete log in the terminal.'\n          )\n          break\n        }\n        console.warn(stripAnsi(formatted.warnings[i]))\n      }\n    }\n  }\n\n  printWarnings()\n\n  // Attempt to apply hot updates or reload.\n  if (isHotUpdate) {\n    tryApplyUpdatesWebpack()\n  }\n}\n\n// Compilation with errors (e.g. syntax error or missing modules).\nfunction handleErrors(errors: any) {\n  clearOutdatedErrors()\n\n  isFirstCompilation = false\n  hasCompileErrors = true\n\n  // \"Massage\" webpack messages.\n  var formatted = formatWebpackMessages({\n    errors: errors,\n    warnings: [],\n  })\n\n  // Only show the first error.\n\n  dispatcher.onBuildError(formatted.errors[0])\n\n  // Also log them to the console.\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    for (var i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n  }\n\n  // Do not attempt to reload now.\n  // We will reload on next success instead.\n  if (process.env.__NEXT_TEST_MODE) {\n    if (self.__NEXT_HMR_CB) {\n      self.__NEXT_HMR_CB(formatted.errors[0])\n      self.__NEXT_HMR_CB = null\n    }\n  }\n}\n\nlet webpackStartMsSinceEpoch: number | null = null\nconst turbopackHmr: TurbopackHmr | null = process.env.TURBOPACK\n  ? new TurbopackHmr()\n  : null\nlet isrManifest: Record<string, boolean> = {}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\nexport function handleStaticIndicator() {\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    const routeInfo = window.next.router.components[window.next.router.pathname]\n    const pageComponent = routeInfo?.Component\n    const appComponent = window.next.router.components['/_app']?.Component\n    const isDynamicPage =\n      Boolean(pageComponent?.getInitialProps) || Boolean(routeInfo?.__N_SSP)\n    const hasAppGetInitialProps =\n      Boolean(appComponent?.getInitialProps) &&\n      appComponent?.getInitialProps !== appComponent?.origGetInitialProps\n\n    const isPageStatic =\n      window.location.pathname in isrManifest ||\n      (!isDynamicPage && !hasAppGetInitialProps)\n\n    dispatcher.onStaticIndicator(isPageStatic)\n  }\n}\n\n/** Handles messages from the server for the Pages Router. */\nfunction processMessage(obj: HMR_ACTION_TYPES) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      isrManifest = obj.data\n      handleStaticIndicator()\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      dispatcher.buildingIndicatorShow()\n\n      if (process.env.TURBOPACK) {\n        turbopackHmr!.onBuilding()\n      } else {\n        webpackStartMsSinceEpoch = Date.now()\n        console.log('[Fast Refresh] rebuilding')\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      dispatcher.buildingIndicatorHide()\n\n      if (obj.hash) handleAvailableHash(obj.hash)\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator)\n      if ('devToolsConfig' in obj)\n        dispatcher.onDevToolsConfig(obj.devToolsConfig)\n\n      const hasErrors = Boolean(errors && errors.length)\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleErrors(errors)\n      }\n\n      // NOTE: Turbopack does not currently send warnings\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleWarnings(warnings)\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: window.__nextDevClientId,\n        })\n      )\n      return handleSuccess()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      turbopackHmr?.onServerComponentChanges()\n      if (hasCompileErrors || RuntimeErrorHandler.hadRuntimeError) {\n        window.location.reload()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n          data: obj.data,\n        })\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      turbopackHmr!.onTurbopackMessage(obj)\n      dispatcher.onBeforeRefresh()\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n          data: obj.data,\n        })\n      }\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null)\n      }\n      dispatcher.onRefresh()\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE:\n      if (customHmrEventHandler) {\n        customHmrEventHandler(obj)\n      }\n      break\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEVTOOLS_CONFIG:\n      dispatcher.onDevToolsConfig(obj.data)\n      break\n    default:\n      obj satisfies never\n  }\n}\n\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: () => void) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: string) {\n      if (status === 'idle') {\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    module.hot.addStatusHandler(handler)\n  }\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdatesWebpack() {\n  if (!module.hot) {\n    // HotModuleReplacementPlugin is not in Webpack configuration.\n    console.error('HotModuleReplacementPlugin is not in Webpack configuration.')\n    // window.location.reload();\n    return\n  }\n\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    dispatcher.onBuildOk()\n    return\n  }\n\n  function handleApplyUpdates(\n    err: any,\n    updatedModules: (string | number)[] | null\n  ) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || updatedModules == null) {\n      if (err) {\n        console.warn(REACT_REFRESH_FULL_RELOAD)\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err)\n      return\n    }\n\n    dispatcher.onBuildOk()\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdatesWebpack()\n      return\n    }\n\n    dispatcher.onRefresh()\n    reportHmrLatency(\n      sendMessage,\n      updatedModules,\n      webpackStartMsSinceEpoch!,\n      Date.now()\n    )\n\n    if (process.env.__NEXT_TEST_MODE) {\n      afterApplyUpdates(() => {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      })\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: (string | number)[] | null) => {\n      if (updatedModules == null) {\n        return null\n      }\n\n      // We should always handle an update, even if updatedModules is empty (but\n      // non-null) for any reason. That's what webpack would normally do:\n      // https://github.com/webpack/webpack/blob/3aa6b6bc3a64/lib/hmr/HotModuleReplacement.runtime.js#L296-L298\n      dispatcher.onBeforeRefresh()\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: (string | number)[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\nexport function performFullReload(err: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  window.location.reload()\n}\n"], "names": ["dispatcher", "register", "stripAnsi", "addMessageListener", "sendMessage", "formatWebpackMessages", "HMR_ACTIONS_SENT_TO_BROWSER", "REACT_REFRESH_FULL_RELOAD", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "reportInvalidHmrMessage", "RuntimeError<PERSON>andler", "reportHmrLatency", "TurbopackHmr", "window", "__nextDevClientId", "Math", "round", "random", "Date", "now", "customHmrEventHandler", "turbopackMessageListeners", "connect", "payload", "processMessage", "err", "subscribeToHmrEvent", "handler", "onUnrecoverableError", "hadRuntimeError", "addTurbopackMessageListener", "cb", "push", "sendTurbopackMessage", "msg", "handleUpdateError", "performFullReload", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "console", "clear", "handleSuccess", "process", "env", "TURBOPACK", "hmrUpdate", "turbopackHmr", "onBuilt", "updatedModules", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdates", "onBuildOk", "isHotUpdate", "__NEXT_DATA__", "page", "isUpdateAvailable", "tryApplyUpdatesWebpack", "handleWarnings", "warnings", "printWarnings", "formatted", "errors", "warn", "i", "length", "handleErrors", "onBuildError", "error", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "webpackStartMsSinceEpoch", "isrManifest", "handleAvailableHash", "hash", "handleStaticIndicator", "__NEXT_DEV_INDICATOR", "routeInfo", "next", "router", "components", "pathname", "pageComponent", "Component", "appComponent", "isDynamicPage", "Boolean", "getInitialProps", "__N_SSP", "hasAppGetInitialProps", "origGetInitialProps", "isPageStatic", "location", "onStaticIndicator", "obj", "action", "ISR_MANIFEST", "data", "BUILDING", "buildingIndicatorShow", "onBuilding", "log", "BUILT", "SYNC", "buildingIndicatorHide", "onVersionInfo", "versionInfo", "onDevIndicator", "devIndicator", "onDevToolsConfig", "devToolsConfig", "hasErrors", "JSON", "stringify", "event", "errorCount", "clientId", "hasWarnings", "warningCount", "SERVER_COMPONENT_CHANGES", "onServerComponentChanges", "reload", "SERVER_ERROR", "errorJSON", "message", "stack", "parse", "Error", "TURBOPACK_CONNECTED", "listener", "type", "TURBOPACK_MESSAGE", "onTurbopackMessage", "onBeforeRefresh", "onRefresh", "ADDED_PAGE", "REMOVED_PAGE", "RELOAD_PAGE", "DEV_PAGES_MANIFEST_UPDATE", "DEVTOOLS_CONFIG", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "handleApplyUpdates", "check", "then", "apply", "stackTrace", "split", "slice", "join", "dependency<PERSON><PERSON>n", "undefined"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,6CAA6C;AAE7C,8EAA8E;AAC9E,qBAAqB;AACrB,2GAA2G;AAE3G,6CAA6C;AAE7C,SAASA,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,QAAQ,QAAQ,oEAAmE;AAC5F,OAAOC,eAAe,gCAA+B;AACrD,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,cAAa;AAC7D,OAAOC,2BAA2B,iDAAgD;AAClF,SAASC,2BAA2B,QAAQ,4CAA2C;AAKvF,SACEC,yBAAyB,EACzBC,oCAAoC,EACpCC,uBAAuB,QAClB,YAAW;AAClB,SAASC,mBAAmB,QAAQ,8BAA6B;AACjE,OAAOC,sBAAsB,2BAA0B;AACvD,SAASC,YAAY,QAAQ,mCAAkC;AAiB/DC,OAAOC,iBAAiB,GAAGC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEpE,IAAIC;AACJ,IAAIC,4BAAsE,EAAE;AAC5E,eAAe,SAASC;IACtBrB;IAEAE,mBAAmB,CAACoB;QAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;YAC1B;QACF;QAEA,IAAI;YACFC,eAAeD;QACjB,EAAE,OAAOE,KAAc;YACrBhB,wBAAwBc,SAASE;QACnC;IACF;IAEA,OAAO;QACLC,qBAAoBC,OAAY;YAC9BP,wBAAwBO;QAC1B;QACAC;YACElB,oBAAoBmB,eAAe,GAAG;QACxC;QACAC,6BAA4BC,EAAwC;YAClEV,0BAA0BW,IAAI,CAACD;QACjC;QACAE,sBAAqBC,GAAW;YAC9B9B,YAAY8B;QACd;QACAC,mBAAkBV,GAAY;YAC5BW,kBAAkBX;QACpB;IACF;AACF;AAEA,yDAAyD;AACzD,IAAIY,qBAAqB;AACzB,IAAIC,4BAA2C;AAC/C,IAAIC,mBAAmB;AAEvB,SAASC;IACP,4CAA4C;IAC5C,IAAI,OAAOC,YAAY,eAAe,OAAOA,QAAQC,KAAK,KAAK,YAAY;QACzE,IAAIH,kBAAkB;YACpBE,QAAQC,KAAK;QACf;IACF;AACF;AAEA,0BAA0B;AAC1B,SAASC;IACPH;IACAD,mBAAmB;IAEnB,IAAIK,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,MAAMC,YAAYC,aAAcC,OAAO;QACvC,IAAIF,aAAa,MAAM;YACrBpC,iBACEP,aACA;mBAAI2C,UAAUG,cAAc;aAAC,EAC7BH,UAAUI,iBAAiB,EAC3BJ,UAAUK,eAAe,EACzBL,UAAUM,UAAU;QAExB;QACArD,WAAWsD,SAAS;IACtB,OAAO;QACL,MAAMC,cACJ,CAAClB,sBACAxB,OAAO2C,aAAa,CAACC,IAAI,KAAK,aAAaC;QAE9C,0CAA0C;QAC1C,IAAIH,aAAa;YACfI;QACF;IACF;IAEAtB,qBAAqB;AACvB;AAEA,2CAA2C;AAC3C,SAASuB,eAAeC,QAAa;IACnCrB;IAEA,MAAMe,cAAc,CAAClB;IACrBA,qBAAqB;IACrBE,mBAAmB;IAEnB,SAASuB;QACP,iCAAiC;QACjC,MAAMC,YAAY1D,sBAAsB;YACtCwD,UAAUA;YACVG,QAAQ,EAAE;QACZ;QAEA,IAAI,OAAOvB,YAAY,eAAe,OAAOA,QAAQwB,IAAI,KAAK,YAAY;gBACpDF;YAApB,IAAK,IAAIG,IAAI,GAAGA,MAAIH,sBAAAA,UAAUF,QAAQ,qBAAlBE,oBAAoBI,MAAM,GAAED,IAAK;gBACnD,IAAIA,MAAM,GAAG;oBACXzB,QAAQwB,IAAI,CACV,+CACE;oBAEJ;gBACF;gBACAxB,QAAQwB,IAAI,CAAC/D,UAAU6D,UAAUF,QAAQ,CAACK,EAAE;YAC9C;QACF;IACF;IAEAJ;IAEA,0CAA0C;IAC1C,IAAIP,aAAa;QACfI;IACF;AACF;AAEA,kEAAkE;AAClE,SAASS,aAAaJ,MAAW;IAC/BxB;IAEAH,qBAAqB;IACrBE,mBAAmB;IAEnB,8BAA8B;IAC9B,IAAIwB,YAAY1D,sBAAsB;QACpC2D,QAAQA;QACRH,UAAU,EAAE;IACd;IAEA,6BAA6B;IAE7B7D,WAAWqE,YAAY,CAACN,UAAUC,MAAM,CAAC,EAAE;IAE3C,gCAAgC;IAChC,IAAI,OAAOvB,YAAY,eAAe,OAAOA,QAAQ6B,KAAK,KAAK,YAAY;QACzE,IAAK,IAAIJ,IAAI,GAAGA,IAAIH,UAAUC,MAAM,CAACG,MAAM,EAAED,IAAK;YAChDzB,QAAQ6B,KAAK,CAACpE,UAAU6D,UAAUC,MAAM,CAACE,EAAE;QAC7C;IACF;IAEA,gCAAgC;IAChC,0CAA0C;IAC1C,IAAItB,QAAQC,GAAG,CAAC0B,gBAAgB,EAAE;QAChC,IAAIC,KAAKC,aAAa,EAAE;YACtBD,KAAKC,aAAa,CAACV,UAAUC,MAAM,CAAC,EAAE;YACtCQ,KAAKC,aAAa,GAAG;QACvB;IACF;AACF;AAEA,IAAIC,2BAA0C;AAC9C,MAAM1B,eAAoCJ,QAAQC,GAAG,CAACC,SAAS,GAC3D,IAAIlC,iBACJ;AACJ,IAAI+D,cAAuC,CAAC;AAE5C,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCvC,4BAA4BuC;AAC9B;AAEA,OAAO,SAASC;IACd,IAAIlC,QAAQC,GAAG,CAACkC,oBAAoB,EAAE;YAGflE;QAFrB,MAAMmE,YAAYnE,OAAOoE,IAAI,CAACC,MAAM,CAACC,UAAU,CAACtE,OAAOoE,IAAI,CAACC,MAAM,CAACE,QAAQ,CAAC;QAC5E,MAAMC,gBAAgBL,6BAAAA,UAAWM,SAAS;QAC1C,MAAMC,gBAAe1E,sCAAAA,OAAOoE,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC,QAAQ,qBAAtCtE,oCAAwCyE,SAAS;QACtE,MAAME,gBACJC,QAAQJ,iCAAAA,cAAeK,eAAe,KAAKD,QAAQT,6BAAAA,UAAWW,OAAO;QACvE,MAAMC,wBACJH,QAAQF,gCAAAA,aAAcG,eAAe,KACrCH,CAAAA,gCAAAA,aAAcG,eAAe,OAAKH,gCAAAA,aAAcM,mBAAmB;QAErE,MAAMC,eACJjF,OAAOkF,QAAQ,CAACX,QAAQ,IAAIT,eAC3B,CAACa,iBAAiB,CAACI;QAEtB5F,WAAWgG,iBAAiB,CAACF;IAC/B;AACF;AAEA,2DAA2D,GAC3D,SAAStE,eAAeyE,GAAqB;IAC3C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;QACtB;IACF;IAEA,OAAQA,IAAIC,MAAM;QAChB,KAAK5F,4BAA4B6F,YAAY;YAAE;gBAC7CxB,cAAcsB,IAAIG,IAAI;gBACtBtB;gBACA;YACF;QACA,KAAKxE,4BAA4B+F,QAAQ;YAAE;gBACzCrG,WAAWsG,qBAAqB;gBAEhC,IAAI1D,QAAQC,GAAG,CAACC,SAAS,EAAE;oBACzBE,aAAcuD,UAAU;gBAC1B,OAAO;oBACL7B,2BAA2BxD,KAAKC,GAAG;oBACnCsB,QAAQ+D,GAAG,CAAC;gBACd;gBACA;YACF;QACA,KAAKlG,4BAA4BmG,KAAK;QACtC,KAAKnG,4BAA4BoG,IAAI;YAAE;gBACrC1G,WAAW2G,qBAAqB;gBAEhC,IAAIV,IAAIpB,IAAI,EAAED,oBAAoBqB,IAAIpB,IAAI;gBAE1C,MAAM,EAAEb,MAAM,EAAEH,QAAQ,EAAE,GAAGoC;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKjG,WAAW4G,aAAa,CAACX,IAAIY,WAAW;gBAClE,IAAI,kBAAkBZ,KAAKjG,WAAW8G,cAAc,CAACb,IAAIc,YAAY;gBACrE,IAAI,oBAAoBd,KACtBjG,WAAWgH,gBAAgB,CAACf,IAAIgB,cAAc;gBAEhD,MAAMC,YAAYzB,QAAQzB,UAAUA,OAAOG,MAAM;gBACjD,IAAI+C,WAAW;oBACb9G,YACE+G,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,YAAYtD,OAAOG,MAAM;wBACzBoD,UAAU1G,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOsD,aAAaJ;gBACtB;gBAEA,mDAAmD;gBACnD,MAAMwD,cAAc/B,QAAQ5B,YAAYA,SAASM,MAAM;gBACvD,IAAIqD,aAAa;oBACfpH,YACE+G,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPI,cAAc5D,SAASM,MAAM;wBAC7BoD,UAAU1G,OAAOC,iBAAiB;oBACpC;oBAEF,OAAO8C,eAAeC;gBACxB;gBAEAzD,YACE+G,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPE,UAAU1G,OAAOC,iBAAiB;gBACpC;gBAEF,OAAO6B;YACT;QACA,KAAKrC,4BAA4BoH,wBAAwB;YAAE;gBACzD1E,gCAAAA,aAAc2E,wBAAwB;gBACtC,IAAIpF,oBAAoB7B,oBAAoBmB,eAAe,EAAE;oBAC3DhB,OAAOkF,QAAQ,CAAC6B,MAAM;gBACxB;gBACA;YACF;QACA,KAAKtH,4BAA4BuH,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAG7B;gBACtB,IAAI6B,WAAW;oBACb,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE,GAAGb,KAAKc,KAAK,CAACH;oBACtC,MAAMxD,QAAQ,qBAAkB,CAAlB,IAAI4D,MAAMH,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/BzD,MAAM0D,KAAK,GAAGA;oBACd5D,aAAa;wBAACE;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKhE,4BAA4B6H,mBAAmB;YAAE;gBACpD,KAAK,MAAMC,YAAY/G,0BAA2B;oBAChD+G,SAAS;wBACPC,MAAM/H,4BAA4B6H,mBAAmB;wBACrD/B,MAAMH,IAAIG,IAAI;oBAChB;gBACF;gBACA;YACF;QACA,KAAK9F,4BAA4BgI,iBAAiB;YAAE;gBAClDtF,aAAcuF,kBAAkB,CAACtC;gBACjCjG,WAAWwI,eAAe;gBAC1B,KAAK,MAAMJ,YAAY/G,0BAA2B;oBAChD+G,SAAS;wBACPC,MAAM/H,4BAA4BgI,iBAAiB;wBACnDlC,MAAMH,IAAIG,IAAI;oBAChB;gBACF;gBACA,IAAI1F,oBAAoBmB,eAAe,EAAE;oBACvCY,QAAQwB,IAAI,CAACzD;oBACb4B,kBAAkB;gBACpB;gBACApC,WAAWyI,SAAS;gBACpB;YACF;QACA,KAAKnI,4BAA4BoI,UAAU;QAC3C,KAAKpI,4BAA4BqI,YAAY;QAC7C,KAAKrI,4BAA4BsI,WAAW;QAC5C,KAAKtI,4BAA4BuI,yBAAyB;YACxD,IAAIzH,uBAAuB;gBACzBA,sBAAsB6E;YACxB;YACA;QACF,KAAK3F,4BAA4BwI,eAAe;YAC9C9I,WAAWgH,gBAAgB,CAACf,IAAIG,IAAI;YACpC;QACF;YACEH;IACJ;AACF;AAEA,mDAAmD;AACnD,SAASvC;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOpB,8BAA8ByG;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAc;IACvC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAAS1H,QAAQwH,MAAc;YAC7B,IAAIA,WAAW,QAAQ;gBACrBF,OAAOC,GAAG,CAACI,mBAAmB,CAAC3H;gBAC/B0H;YACF;QACF;QACAJ,OAAOC,GAAG,CAACK,gBAAgB,CAAC5H;IAC9B;AACF;AAEA,iEAAiE;AACjE,SAASgC;IACP,IAAI,CAACsF,OAAOC,GAAG,EAAE;QACf,8DAA8D;QAC9DzG,QAAQ6B,KAAK,CAAC;QACd,4BAA4B;QAC5B;IACF;IAEA,IAAI,CAACZ,uBAAuB,CAACsF,mBAAmB;QAC9ChJ,WAAWsD,SAAS;QACpB;IACF;IAEA,SAASkG,mBACP/H,GAAQ,EACRyB,cAA0C;QAE1C,IAAIzB,OAAOf,oBAAoBmB,eAAe,IAAIqB,kBAAkB,MAAM;YACxE,IAAIzB,KAAK;gBACPgB,QAAQwB,IAAI,CAAC1D;YACf,OAAO,IAAIG,oBAAoBmB,eAAe,EAAE;gBAC9CY,QAAQwB,IAAI,CAACzD;YACf;YACA4B,kBAAkBX;YAClB;QACF;QAEAzB,WAAWsD,SAAS;QAEpB,IAAII,qBAAqB;YACvB,+DAA+D;YAC/DC;YACA;QACF;QAEA3D,WAAWyI,SAAS;QACpB9H,iBACEP,aACA8C,gBACAwB,0BACAxD,KAAKC,GAAG;QAGV,IAAIyB,QAAQC,GAAG,CAAC0B,gBAAgB,EAAE;YAChC6E,kBAAkB;gBAChB,IAAI5E,KAAKC,aAAa,EAAE;oBACtBD,KAAKC,aAAa;oBAClBD,KAAKC,aAAa,GAAG;gBACvB;YACF;QACF;IACF;IAEA,2DAA2D;IAC3DwE,OAAOC,GAAG,CACPO,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACxG;QACL,IAAIA,kBAAkB,MAAM;YAC1B,OAAO;QACT;QAEA,0EAA0E;QAC1E,mEAAmE;QACnE,yGAAyG;QACzGlD,WAAWwI,eAAe;QAC1B,2DAA2D;QAC3D,OAAOS,OAAOC,GAAG,CAACS,KAAK;IACzB,GACCD,IAAI,CACH,CAACxG;QACCsG,mBAAmB,MAAMtG;IAC3B,GACA,CAACzB;QACC+H,mBAAmB/H,KAAK;IAC1B;AAEN;AAEA,OAAO,SAASW,kBAAkBX,GAAQ;IACxC,MAAMmI,aACJnI,OACC,CAAA,AAACA,IAAIuG,KAAK,IAAIvG,IAAIuG,KAAK,CAAC6B,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDtI,IAAIsG,OAAO,IACXtG,MAAM,EAAC;IAEXrB,YACE+G,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPuC;QACA/H,iBAAiB,CAAC,CAACnB,oBAAoBmB,eAAe;QACtDmI,iBAAiBvI,MAAMA,IAAIuI,eAAe,GAAGC;IAC/C;IAGFpJ,OAAOkF,QAAQ,CAAC6B,MAAM;AACxB", "ignoreList": [0]}