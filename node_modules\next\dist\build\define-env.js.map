{"version": 3, "sources": ["../../src/build/define-env.ts"], "sourcesContent": ["import type {\n  I18NConfig,\n  I18NDomains,\n  NextConfigComplete,\n} from '../server/config-shared'\nimport type { MiddlewareMatcher } from './analysis/get-page-static-info'\nimport type { Rewrite } from '../lib/load-custom-routes'\nimport path from 'node:path'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport { checkIsAppPPREnabled } from '../server/lib/experimental/ppr'\nimport {\n  getNextConfigEnv,\n  getNextPublicEnvironmentVariables,\n} from '../lib/static-env'\n\ntype BloomFilter = ReturnType<\n  import('../shared/lib/bloom-filter').BloomFilter['export']\n>\n\nexport interface DefineEnvOptions {\n  isTurbopack: boolean\n  clientRouterFilters?: {\n    staticFilter: BloomFilter\n    dynamicFilter: BloomFilter\n  }\n  config: NextConfigComplete\n  dev: boolean\n  distDir: string\n  projectPath: string\n  fetchCacheKeyPrefix: string | undefined\n  hasRewrites: boolean\n  isClient: boolean\n  isEdgeServer: boolean\n  isNodeServer: boolean\n  middlewareMatchers: MiddlewareMatcher[] | undefined\n  omitNonDeterministic?: boolean\n  rewrites: {\n    beforeFiles: Rewrite[]\n    afterFiles: Rewrite[]\n    fallback: Rewrite[]\n  }\n}\n\ninterface DefineEnv {\n  [key: string]:\n    | string\n    | string[]\n    | boolean\n    | MiddlewareMatcher[]\n    | BloomFilter\n    | Partial<NextConfigComplete['images']>\n    | I18NDomains\n    | I18NConfig\n}\n\ninterface SerializedDefineEnv {\n  [key: string]: string\n}\n\n/**\n * Serializes the DefineEnv config so that it can be inserted into the code by Webpack/Turbopack, JSON stringifies each value.\n */\nfunction serializeDefineEnv(defineEnv: DefineEnv): SerializedDefineEnv {\n  const defineEnvStringified: SerializedDefineEnv = {}\n  for (const key in defineEnv) {\n    const value = defineEnv[key]\n    defineEnvStringified[key] = JSON.stringify(value)\n  }\n\n  return defineEnvStringified\n}\n\nfunction getImageConfig(\n  config: NextConfigComplete,\n  dev: boolean\n): { 'process.env.__NEXT_IMAGE_OPTS': Partial<NextConfigComplete['images']> } {\n  return {\n    'process.env.__NEXT_IMAGE_OPTS': {\n      deviceSizes: config.images.deviceSizes,\n      imageSizes: config.images.imageSizes,\n      qualities: config.images.qualities,\n      path: config.images.path,\n      loader: config.images.loader,\n      dangerouslyAllowSVG: config.images.dangerouslyAllowSVG,\n      unoptimized: config?.images?.unoptimized,\n      ...(dev\n        ? {\n            // additional config in dev to allow validating on the client\n            domains: config.images.domains,\n            remotePatterns: config.images?.remotePatterns,\n            localPatterns: config.images?.localPatterns,\n            output: config.output,\n          }\n        : {}),\n    },\n  }\n}\n\nexport function getDefineEnv({\n  isTurbopack,\n  clientRouterFilters,\n  config,\n  dev,\n  distDir,\n  projectPath,\n  fetchCacheKeyPrefix,\n  hasRewrites,\n  isClient,\n  isEdgeServer,\n  isNodeServer,\n  middlewareMatchers,\n  omitNonDeterministic,\n  rewrites,\n}: DefineEnvOptions): SerializedDefineEnv {\n  const nextPublicEnv = getNextPublicEnvironmentVariables()\n  const nextConfigEnv = getNextConfigEnv(config)\n\n  const isPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n  const isCacheComponentsEnabled = !!config.experimental.cacheComponents\n  const isUseCacheEnabled = !!config.experimental.useCache\n\n  const defineEnv: DefineEnv = {\n    // internal field to identify the plugin config\n    __NEXT_DEFINE_ENV: true,\n\n    ...nextPublicEnv,\n    ...nextConfigEnv,\n    ...(!isEdgeServer\n      ? {}\n      : {\n          EdgeRuntime:\n            /**\n             * Cloud providers can set this environment variable to allow users\n             * and library authors to have different implementations based on\n             * the runtime they are running with, if it's not using `edge-runtime`\n             */\n            process.env.NEXT_EDGE_RUNTIME_PROVIDER ?? 'edge-runtime',\n\n          // process should be only { env: {...} } for edge runtime.\n          // For ignore avoid warn on `process.emit` usage but directly omit it.\n          'process.emit': false,\n        }),\n    'process.turbopack': isTurbopack,\n    'process.env.TURBOPACK': isTurbopack,\n    'process.env.__NEXT_BUNDLER': isTurbopack\n      ? 'Turbopack'\n      : process.env.NEXT_RSPACK\n        ? 'Rspack'\n        : 'Webpack',\n    // TODO: enforce `NODE_ENV` on `process.env`, and add a test:\n    'process.env.NODE_ENV':\n      dev || config.experimental.allowDevelopmentBuild\n        ? 'development'\n        : 'production',\n    'process.env.NEXT_RUNTIME': isEdgeServer\n      ? 'edge'\n      : isNodeServer\n        ? 'nodejs'\n        : '',\n    'process.env.NEXT_MINIMAL': '',\n    'process.env.__NEXT_APP_NAV_FAIL_HANDLING': Boolean(\n      config.experimental.appNavFailHandling\n    ),\n    'process.env.__NEXT_PPR': isPPREnabled,\n    'process.env.__NEXT_CACHE_COMPONENTS': isCacheComponentsEnabled,\n    'process.env.__NEXT_USE_CACHE': isUseCacheEnabled,\n\n    'process.env.NEXT_DEPLOYMENT_ID': config.experimental?.useSkewCookie\n      ? false\n      : config.deploymentId || false,\n    // Propagates the `__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING` environment\n    // variable to the client.\n    'process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING':\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING || false,\n    'process.env.__NEXT_FETCH_CACHE_KEY_PREFIX': fetchCacheKeyPrefix ?? '',\n    ...(isTurbopack\n      ? {}\n      : {\n          'process.env.__NEXT_MIDDLEWARE_MATCHERS': middlewareMatchers ?? [],\n        }),\n    'process.env.__NEXT_MANUAL_CLIENT_BASE_PATH':\n      config.experimental.manualClientBasePath ?? false,\n    'process.env.__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME': JSON.stringify(\n      isNaN(Number(config.experimental.staleTimes?.dynamic))\n        ? 0\n        : config.experimental.staleTimes?.dynamic\n    ),\n    'process.env.__NEXT_CLIENT_ROUTER_STATIC_STALETIME': JSON.stringify(\n      isNaN(Number(config.experimental.staleTimes?.static))\n        ? 5 * 60 // 5 minutes\n        : config.experimental.staleTimes?.static\n    ),\n    'process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED':\n      config.experimental.clientRouterFilter ?? true,\n    'process.env.__NEXT_CLIENT_ROUTER_S_FILTER':\n      clientRouterFilters?.staticFilter ?? false,\n    'process.env.__NEXT_CLIENT_ROUTER_D_FILTER':\n      clientRouterFilters?.dynamicFilter ?? false,\n    'process.env.__NEXT_CLIENT_SEGMENT_CACHE': Boolean(\n      config.experimental.clientSegmentCache\n    ),\n    'process.env.__NEXT_CLIENT_PARAM_PARSING': Boolean(\n      config.experimental.clientParamParsing\n    ),\n    'process.env.__NEXT_CLIENT_VALIDATE_RSC_REQUEST_HEADERS': Boolean(\n      config.experimental.validateRSCRequestHeaders\n    ),\n    'process.env.__NEXT_DYNAMIC_ON_HOVER': Boolean(\n      config.experimental.dynamicOnHover\n    ),\n    'process.env.__NEXT_ROUTER_BF_CACHE': Boolean(\n      config.experimental.routerBFCache\n    ),\n    'process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE':\n      config.experimental.optimisticClientCache ?? true,\n    'process.env.__NEXT_MIDDLEWARE_PREFETCH':\n      config.experimental.middlewarePrefetch ?? 'flexible',\n    'process.env.__NEXT_CROSS_ORIGIN': config.crossOrigin,\n    'process.browser': isClient,\n    'process.env.__NEXT_TEST_MODE': process.env.__NEXT_TEST_MODE ?? false,\n    // This is used in client/dev-error-overlay/hot-dev-client.js to replace the dist directory\n    ...(dev && (isClient ?? isEdgeServer)\n      ? {\n          'process.env.__NEXT_DIST_DIR': distDir,\n        }\n      : {}),\n    // This is used in devtools to strip the project path in edge runtime,\n    // as there's only a dummy `dir` value (`.`) as edge runtime doesn't have concept of file system.\n    ...(dev && isEdgeServer\n      ? {\n          'process.env.__NEXT_EDGE_PROJECT_DIR': isTurbopack\n            ? path.relative(process.cwd(), projectPath)\n            : projectPath,\n        }\n      : {}),\n    'process.env.__NEXT_BASE_PATH': config.basePath,\n    'process.env.__NEXT_CASE_SENSITIVE_ROUTES': Boolean(\n      config.experimental.caseSensitiveRoutes\n    ),\n    'process.env.__NEXT_REWRITES': rewrites as any,\n    'process.env.__NEXT_TRAILING_SLASH': config.trailingSlash,\n    'process.env.__NEXT_DEV_INDICATOR': config.devIndicators !== false,\n    'process.env.__NEXT_DEV_INDICATOR_POSITION':\n      config.devIndicators === false\n        ? 'bottom-left' // This will not be used as the indicator is disabled.\n        : config.devIndicators.position ?? 'bottom-left',\n    'process.env.__NEXT_STRICT_MODE':\n      config.reactStrictMode === null ? false : config.reactStrictMode,\n    'process.env.__NEXT_STRICT_MODE_APP':\n      // When next.config.js does not have reactStrictMode it's enabled by default.\n      config.reactStrictMode === null ? true : config.reactStrictMode,\n    'process.env.__NEXT_OPTIMIZE_CSS':\n      (config.experimental.optimizeCss && !dev) ?? false,\n    'process.env.__NEXT_SCRIPT_WORKERS':\n      (config.experimental.nextScriptWorkers && !dev) ?? false,\n    'process.env.__NEXT_SCROLL_RESTORATION':\n      config.experimental.scrollRestoration ?? false,\n    ...getImageConfig(config, dev),\n    'process.env.__NEXT_ROUTER_BASEPATH': config.basePath,\n    'process.env.__NEXT_HAS_REWRITES': hasRewrites,\n    'process.env.__NEXT_CONFIG_OUTPUT': config.output,\n    'process.env.__NEXT_I18N_SUPPORT': !!config.i18n,\n    'process.env.__NEXT_I18N_DOMAINS': config.i18n?.domains ?? false,\n    'process.env.__NEXT_I18N_CONFIG': config.i18n || '',\n    'process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE':\n      config.skipMiddlewareUrlNormalize,\n    'process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE':\n      config.experimental.externalMiddlewareRewritesResolve ?? false,\n    'process.env.__NEXT_MANUAL_TRAILING_SLASH':\n      config.skipTrailingSlashRedirect,\n    'process.env.__NEXT_HAS_WEB_VITALS_ATTRIBUTION':\n      (config.experimental.webVitalsAttribution &&\n        config.experimental.webVitalsAttribution.length > 0) ??\n      false,\n    'process.env.__NEXT_WEB_VITALS_ATTRIBUTION':\n      config.experimental.webVitalsAttribution ?? false,\n    'process.env.__NEXT_LINK_NO_TOUCH_START':\n      config.experimental.linkNoTouchStart ?? false,\n    'process.env.__NEXT_ASSET_PREFIX': config.assetPrefix,\n    'process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS':\n      !!config.experimental.authInterrupts,\n    'process.env.__NEXT_TELEMETRY_DISABLED': Boolean(\n      process.env.NEXT_TELEMETRY_DISABLED\n    ),\n    ...(isNodeServer || isEdgeServer\n      ? {\n          // Fix bad-actors in the npm ecosystem (e.g. `node-formidable`)\n          // This is typically found in unmaintained modules from the\n          // pre-webpack era (common in server-side code)\n          'global.GENTLY': false,\n        }\n      : undefined),\n    ...(isNodeServer || isEdgeServer\n      ? {\n          'process.env.__NEXT_EXPERIMENTAL_REACT':\n            needsExperimentalReact(config),\n        }\n      : undefined),\n\n    'process.env.__NEXT_MULTI_ZONE_DRAFT_MODE':\n      config.experimental.multiZoneDraftMode ?? false,\n    'process.env.__NEXT_TRUST_HOST_HEADER':\n      config.experimental.trustHostHeader ?? false,\n    'process.env.__NEXT_ALLOWED_REVALIDATE_HEADERS':\n      config.experimental.allowedRevalidateHeaderKeys ?? [],\n    ...(isNodeServer\n      ? {\n          'process.env.__NEXT_RELATIVE_DIST_DIR': config.distDir,\n          'process.env.__NEXT_RELATIVE_PROJECT_DIR': path.relative(\n            process.cwd(),\n            projectPath\n          ),\n        }\n      : {}),\n    'process.env.__NEXT_DEVTOOL_SEGMENT_EXPLORER':\n      !!config.experimental.devtoolSegmentExplorer,\n\n    'process.env.__NEXT_BROWSER_DEBUG_INFO_IN_TERMINAL': JSON.stringify(\n      config.experimental.browserDebugInfoInTerminal || false\n    ),\n\n    // The devtools need to know whether or not to show an option to clear the\n    // bundler cache. This option may be removed later once Turbopack's\n    // persistent cache feature is more stable.\n    //\n    // This environment value is currently best-effort:\n    // - It's possible to disable the webpack filesystem cache, but it's\n    //   unlikely for a user to do that.\n    // - Rspack's persistent cache is unstable and requires a different\n    //   configuration than webpack to enable (which we don't do).\n    //\n    // In the worst case we'll show an option to clear the cache, but it'll be a\n    // no-op that just restarts the development server.\n    'process.env.__NEXT_BUNDLER_HAS_PERSISTENT_CACHE':\n      !isTurbopack || (config.experimental.turbopackPersistentCaching ?? false),\n    'process.env.__NEXT_OPTIMIZE_ROUTER_SCROLL':\n      config.experimental.optimizeRouterScrolling ?? false,\n  }\n\n  const userDefines = config.compiler?.define ?? {}\n  for (const key in userDefines) {\n    if (defineEnv.hasOwnProperty(key)) {\n      throw new Error(\n        `The \\`compiler.define\\` option is configured to replace the \\`${key}\\` variable. This variable is either part of a Next.js built-in or is already configured.`\n      )\n    }\n    defineEnv[key] = userDefines[key]\n  }\n\n  if (isNodeServer || isEdgeServer) {\n    const userDefinesServer = config.compiler?.defineServer ?? {}\n    for (const key in userDefinesServer) {\n      if (defineEnv.hasOwnProperty(key)) {\n        throw new Error(\n          `The \\`compiler.defineServer\\` option is configured to replace the \\`${key}\\` variable. This variable is either part of a Next.js built-in or is already configured.`\n        )\n      }\n      defineEnv[key] = userDefinesServer[key]\n    }\n  }\n\n  const serializedDefineEnv = serializeDefineEnv(defineEnv)\n\n  // we delay inlining these values until after the build\n  // with flying shuttle enabled so we can update them\n  // without invalidating entries\n  if (!dev && omitNonDeterministic) {\n    // client uses window. instead of leaving process.env\n    // in case process isn't polyfilled on client already\n    // since by this point it won't be added by webpack\n    const safeKey = (key: string) =>\n      isClient ? `window.${key.split('.').pop()}` : key\n\n    for (const key in nextPublicEnv) {\n      serializedDefineEnv[key] = safeKey(key)\n    }\n    for (const key in nextConfigEnv) {\n      serializedDefineEnv[key] = safeKey(key)\n    }\n    for (const key of ['process.env.NEXT_DEPLOYMENT_ID']) {\n      serializedDefineEnv[key] = safeKey(key)\n    }\n  }\n\n  return serializedDefineEnv\n}\n"], "names": ["getDefineEnv", "serializeDefineEnv", "defineEnv", "defineEnvStringified", "key", "value", "JSON", "stringify", "getImageConfig", "config", "dev", "deviceSizes", "images", "imageSizes", "qualities", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "localPatterns", "output", "isTurbopack", "clientRouterFilters", "distDir", "projectPath", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeServer", "middlewareMatchers", "omitNonDeterministic", "rewrites", "nextPublicEnv", "getNextPublicEnvironmentVariables", "nextConfigEnv", "getNextConfigEnv", "isPPREnabled", "checkIsAppPPREnabled", "experimental", "ppr", "isCacheComponentsEnabled", "cacheComponents", "isUseCacheEnabled", "useCache", "__NEXT_DEFINE_ENV", "EdgeRuntime", "process", "env", "NEXT_EDGE_RUNTIME_PROVIDER", "NEXT_RSPACK", "allowDevelopmentBuild", "Boolean", "appNavFailHandling", "useSkewCookie", "deploymentId", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "manualClientBasePath", "isNaN", "Number", "staleTimes", "dynamic", "static", "clientRouterFilter", "staticFilter", "dynamicFilter", "clientSegmentCache", "clientParamParsing", "validateRSCRequestHeaders", "dynamicOnHover", "routerBFCache", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "relative", "cwd", "basePath", "caseSensitiveRoutes", "trailingSlash", "devIndicators", "position", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "i18n", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "linkNoTouchStart", "assetPrefix", "authInterrupts", "NEXT_TELEMETRY_DISABLED", "undefined", "needsExperimentalReact", "multiZoneDraftMode", "trustHostHeader", "allowedRevalidateHeaderKeys", "devtoolSegmentExplorer", "browserDebugInfoInTerminal", "turbopackPersistentCaching", "optimizeRouterScrolling", "userDefines", "compiler", "define", "hasOwnProperty", "Error", "userDefinesServer", "defineServer", "serializedDefineEnv", "<PERSON><PERSON><PERSON>", "split", "pop"], "mappings": ";;;;+BAkGgBA;;;eAAAA;;;iEA3FC;wCACsB;qBACF;2BAI9B;;;;;;AA8CP;;CAEC,GACD,SAASC,mBAAmBC,SAAoB;IAC9C,MAAMC,uBAA4C,CAAC;IACnD,IAAK,MAAMC,OAAOF,UAAW;QAC3B,MAAMG,QAAQH,SAAS,CAACE,IAAI;QAC5BD,oBAAoB,CAACC,IAAI,GAAGE,KAAKC,SAAS,CAACF;IAC7C;IAEA,OAAOF;AACT;AAEA,SAASK,eACPC,MAA0B,EAC1BC,GAAY;QAUKD,gBAKSA,iBACDA;IAdzB,OAAO;QACL,iCAAiC;YAC/BE,aAAaF,OAAOG,MAAM,CAACD,WAAW;YACtCE,YAAYJ,OAAOG,MAAM,CAACC,UAAU;YACpCC,WAAWL,OAAOG,MAAM,CAACE,SAAS;YAClCC,MAAMN,OAAOG,MAAM,CAACG,IAAI;YACxBC,QAAQP,OAAOG,MAAM,CAACI,MAAM;YAC5BC,qBAAqBR,OAAOG,MAAM,CAACK,mBAAmB;YACtDC,WAAW,EAAET,2BAAAA,iBAAAA,OAAQG,MAAM,qBAAdH,eAAgBS,WAAW;YACxC,GAAIR,MACA;gBACE,6DAA6D;gBAC7DS,SAASV,OAAOG,MAAM,CAACO,OAAO;gBAC9BC,cAAc,GAAEX,kBAAAA,OAAOG,MAAM,qBAAbH,gBAAeW,cAAc;gBAC7CC,aAAa,GAAEZ,kBAAAA,OAAOG,MAAM,qBAAbH,gBAAeY,aAAa;gBAC3CC,QAAQb,OAAOa,MAAM;YACvB,IACA,CAAC,CAAC;QACR;IACF;AACF;AAEO,SAAStB,aAAa,EAC3BuB,WAAW,EACXC,mBAAmB,EACnBf,MAAM,EACNC,GAAG,EACHe,OAAO,EACPC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,kBAAkB,EAClBC,oBAAoB,EACpBC,QAAQ,EACS;QAsDmBzB,sBAgBnBA,iCAETA,kCAGSA,kCAETA,kCAwE6BA,cA6EjBA;IAjOpB,MAAM0B,gBAAgBC,IAAAA,4CAAiC;IACvD,MAAMC,gBAAgBC,IAAAA,2BAAgB,EAAC7B;IAEvC,MAAM8B,eAAeC,IAAAA,yBAAoB,EAAC/B,OAAOgC,YAAY,CAACC,GAAG;IACjE,MAAMC,2BAA2B,CAAC,CAAClC,OAAOgC,YAAY,CAACG,eAAe;IACtE,MAAMC,oBAAoB,CAAC,CAACpC,OAAOgC,YAAY,CAACK,QAAQ;IAExD,MAAM5C,YAAuB;QAC3B,+CAA+C;QAC/C6C,mBAAmB;QAEnB,GAAGZ,aAAa;QAChB,GAAGE,aAAa;QAChB,GAAI,CAACP,eACD,CAAC,IACD;YACEkB,aACE;;;;aAIC,GACDC,QAAQC,GAAG,CAACC,0BAA0B,IAAI;YAE5C,0DAA0D;YAC1D,sEAAsE;YACtE,gBAAgB;QAClB,CAAC;QACL,qBAAqB5B;QACrB,yBAAyBA;QACzB,8BAA8BA,cAC1B,cACA0B,QAAQC,GAAG,CAACE,WAAW,GACrB,WACA;QACN,6DAA6D;QAC7D,wBACE1C,OAAOD,OAAOgC,YAAY,CAACY,qBAAqB,GAC5C,gBACA;QACN,4BAA4BvB,eACxB,SACAC,eACE,WACA;QACN,4BAA4B;QAC5B,4CAA4CuB,QAC1C7C,OAAOgC,YAAY,CAACc,kBAAkB;QAExC,0BAA0BhB;QAC1B,uCAAuCI;QACvC,gCAAgCE;QAEhC,kCAAkCpC,EAAAA,uBAAAA,OAAOgC,YAAY,qBAAnBhC,qBAAqB+C,aAAa,IAChE,QACA/C,OAAOgD,YAAY,IAAI;QAC3B,0EAA0E;QAC1E,0BAA0B;QAC1B,0DACER,QAAQC,GAAG,CAACQ,0CAA0C,IAAI;QAC5D,6CAA6C/B,uBAAuB;QACpE,GAAIJ,cACA,CAAC,IACD;YACE,0CAA0CS,sBAAsB,EAAE;QACpE,CAAC;QACL,8CACEvB,OAAOgC,YAAY,CAACkB,oBAAoB,IAAI;QAC9C,sDAAsDrD,KAAKC,SAAS,CAClEqD,MAAMC,QAAOpD,kCAAAA,OAAOgC,YAAY,CAACqB,UAAU,qBAA9BrD,gCAAgCsD,OAAO,KAChD,KACAtD,mCAAAA,OAAOgC,YAAY,CAACqB,UAAU,qBAA9BrD,iCAAgCsD,OAAO;QAE7C,qDAAqDzD,KAAKC,SAAS,CACjEqD,MAAMC,QAAOpD,mCAAAA,OAAOgC,YAAY,CAACqB,UAAU,qBAA9BrD,iCAAgCuD,MAAM,KAC/C,IAAI,GAAG,YAAY;YACnBvD,mCAAAA,OAAOgC,YAAY,CAACqB,UAAU,qBAA9BrD,iCAAgCuD,MAAM;QAE5C,mDACEvD,OAAOgC,YAAY,CAACwB,kBAAkB,IAAI;QAC5C,6CACEzC,CAAAA,uCAAAA,oBAAqB0C,YAAY,KAAI;QACvC,6CACE1C,CAAAA,uCAAAA,oBAAqB2C,aAAa,KAAI;QACxC,2CAA2Cb,QACzC7C,OAAOgC,YAAY,CAAC2B,kBAAkB;QAExC,2CAA2Cd,QACzC7C,OAAOgC,YAAY,CAAC4B,kBAAkB;QAExC,0DAA0Df,QACxD7C,OAAOgC,YAAY,CAAC6B,yBAAyB;QAE/C,uCAAuChB,QACrC7C,OAAOgC,YAAY,CAAC8B,cAAc;QAEpC,sCAAsCjB,QACpC7C,OAAOgC,YAAY,CAAC+B,aAAa;QAEnC,8CACE/D,OAAOgC,YAAY,CAACgC,qBAAqB,IAAI;QAC/C,0CACEhE,OAAOgC,YAAY,CAACiC,kBAAkB,IAAI;QAC5C,mCAAmCjE,OAAOkE,WAAW;QACrD,mBAAmB9C;QACnB,gCAAgCoB,QAAQC,GAAG,CAAC0B,gBAAgB,IAAI;QAChE,2FAA2F;QAC3F,GAAIlE,OAAQmB,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+BL;QACjC,IACA,CAAC,CAAC;QACN,sEAAsE;QACtE,iGAAiG;QACjG,GAAIf,OAAOoB,eACP;YACE,uCAAuCP,cACnCR,iBAAI,CAAC8D,QAAQ,CAAC5B,QAAQ6B,GAAG,IAAIpD,eAC7BA;QACN,IACA,CAAC,CAAC;QACN,gCAAgCjB,OAAOsE,QAAQ;QAC/C,4CAA4CzB,QAC1C7C,OAAOgC,YAAY,CAACuC,mBAAmB;QAEzC,+BAA+B9C;QAC/B,qCAAqCzB,OAAOwE,aAAa;QACzD,oCAAoCxE,OAAOyE,aAAa,KAAK;QAC7D,6CACEzE,OAAOyE,aAAa,KAAK,QACrB,cAAc,sDAAsD;WACpEzE,OAAOyE,aAAa,CAACC,QAAQ,IAAI;QACvC,kCACE1E,OAAO2E,eAAe,KAAK,OAAO,QAAQ3E,OAAO2E,eAAe;QAClE,sCACE,6EAA6E;QAC7E3E,OAAO2E,eAAe,KAAK,OAAO,OAAO3E,OAAO2E,eAAe;QACjE,mCACE,AAAC3E,CAAAA,OAAOgC,YAAY,CAAC4C,WAAW,IAAI,CAAC3E,GAAE,KAAM;QAC/C,qCACE,AAACD,CAAAA,OAAOgC,YAAY,CAAC6C,iBAAiB,IAAI,CAAC5E,GAAE,KAAM;QACrD,yCACED,OAAOgC,YAAY,CAAC8C,iBAAiB,IAAI;QAC3C,GAAG/E,eAAeC,QAAQC,IAAI;QAC9B,sCAAsCD,OAAOsE,QAAQ;QACrD,mCAAmCnD;QACnC,oCAAoCnB,OAAOa,MAAM;QACjD,mCAAmC,CAAC,CAACb,OAAO+E,IAAI;QAChD,mCAAmC/E,EAAAA,eAAAA,OAAO+E,IAAI,qBAAX/E,aAAaU,OAAO,KAAI;QAC3D,kCAAkCV,OAAO+E,IAAI,IAAI;QACjD,kDACE/E,OAAOgF,0BAA0B;QACnC,0DACEhF,OAAOgC,YAAY,CAACiD,iCAAiC,IAAI;QAC3D,4CACEjF,OAAOkF,yBAAyB;QAClC,iDACE,AAAClF,CAAAA,OAAOgC,YAAY,CAACmD,oBAAoB,IACvCnF,OAAOgC,YAAY,CAACmD,oBAAoB,CAACC,MAAM,GAAG,CAAA,KACpD;QACF,6CACEpF,OAAOgC,YAAY,CAACmD,oBAAoB,IAAI;QAC9C,0CACEnF,OAAOgC,YAAY,CAACqD,gBAAgB,IAAI;QAC1C,mCAAmCrF,OAAOsF,WAAW;QACrD,mDACE,CAAC,CAACtF,OAAOgC,YAAY,CAACuD,cAAc;QACtC,yCAAyC1C,QACvCL,QAAQC,GAAG,CAAC+C,uBAAuB;QAErC,GAAIlE,gBAAgBD,eAChB;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiB;QACnB,IACAoE,SAAS;QACb,GAAInE,gBAAgBD,eAChB;YACE,yCACEqE,IAAAA,8CAAsB,EAAC1F;QAC3B,IACAyF,SAAS;QAEb,4CACEzF,OAAOgC,YAAY,CAAC2D,kBAAkB,IAAI;QAC5C,wCACE3F,OAAOgC,YAAY,CAAC4D,eAAe,IAAI;QACzC,iDACE5F,OAAOgC,YAAY,CAAC6D,2BAA2B,IAAI,EAAE;QACvD,GAAIvE,eACA;YACE,wCAAwCtB,OAAOgB,OAAO;YACtD,2CAA2CV,iBAAI,CAAC8D,QAAQ,CACtD5B,QAAQ6B,GAAG,IACXpD;QAEJ,IACA,CAAC,CAAC;QACN,+CACE,CAAC,CAACjB,OAAOgC,YAAY,CAAC8D,sBAAsB;QAE9C,qDAAqDjG,KAAKC,SAAS,CACjEE,OAAOgC,YAAY,CAAC+D,0BAA0B,IAAI;QAGpD,0EAA0E;QAC1E,mEAAmE;QACnE,2CAA2C;QAC3C,EAAE;QACF,mDAAmD;QACnD,oEAAoE;QACpE,oCAAoC;QACpC,mEAAmE;QACnE,8DAA8D;QAC9D,EAAE;QACF,4EAA4E;QAC5E,mDAAmD;QACnD,mDACE,CAACjF,eAAgBd,CAAAA,OAAOgC,YAAY,CAACgE,0BAA0B,IAAI,KAAI;QACzE,6CACEhG,OAAOgC,YAAY,CAACiE,uBAAuB,IAAI;IACnD;IAEA,MAAMC,cAAclG,EAAAA,mBAAAA,OAAOmG,QAAQ,qBAAfnG,iBAAiBoG,MAAM,KAAI,CAAC;IAChD,IAAK,MAAMzG,OAAOuG,YAAa;QAC7B,IAAIzG,UAAU4G,cAAc,CAAC1G,MAAM;YACjC,MAAM,qBAEL,CAFK,IAAI2G,MACR,CAAC,8DAA8D,EAAE3G,IAAI,yFAAyF,CAAC,GAD3J,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACAF,SAAS,CAACE,IAAI,GAAGuG,WAAW,CAACvG,IAAI;IACnC;IAEA,IAAI2B,gBAAgBD,cAAc;YACNrB;QAA1B,MAAMuG,oBAAoBvG,EAAAA,oBAAAA,OAAOmG,QAAQ,qBAAfnG,kBAAiBwG,YAAY,KAAI,CAAC;QAC5D,IAAK,MAAM7G,OAAO4G,kBAAmB;YACnC,IAAI9G,UAAU4G,cAAc,CAAC1G,MAAM;gBACjC,MAAM,qBAEL,CAFK,IAAI2G,MACR,CAAC,oEAAoE,EAAE3G,IAAI,yFAAyF,CAAC,GADjK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAF,SAAS,CAACE,IAAI,GAAG4G,iBAAiB,CAAC5G,IAAI;QACzC;IACF;IAEA,MAAM8G,sBAAsBjH,mBAAmBC;IAE/C,uDAAuD;IACvD,oDAAoD;IACpD,+BAA+B;IAC/B,IAAI,CAACQ,OAAOuB,sBAAsB;QAChC,qDAAqD;QACrD,qDAAqD;QACrD,mDAAmD;QACnD,MAAMkF,UAAU,CAAC/G,MACfyB,WAAW,CAAC,OAAO,EAAEzB,IAAIgH,KAAK,CAAC,KAAKC,GAAG,IAAI,GAAGjH;QAEhD,IAAK,MAAMA,OAAO+B,cAAe;YAC/B+E,mBAAmB,CAAC9G,IAAI,GAAG+G,QAAQ/G;QACrC;QACA,IAAK,MAAMA,OAAOiC,cAAe;YAC/B6E,mBAAmB,CAAC9G,IAAI,GAAG+G,QAAQ/G;QACrC;QACA,KAAK,MAAMA,OAAO;YAAC;SAAiC,CAAE;YACpD8G,mBAAmB,CAAC9G,IAAI,GAAG+G,QAAQ/G;QACrC;IACF;IAEA,OAAO8G;AACT", "ignoreList": [0]}