{"version": 3, "sources": ["../../../src/server/dev/next-dev-server.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON>ponentsR<PERSON>ult, NodeRequestHandler } from '../next-server'\nimport type { LoadComponentsReturnType } from '../load-components'\nimport type { Options as ServerOptions } from '../next-server'\nimport type { Params } from '../request/params'\nimport type { ParsedUrl } from '../../shared/lib/router/utils/parse-url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { MiddlewareRoutingItem } from '../base-server'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { RouteMatcherManager } from '../route-matcher-managers/route-matcher-manager'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  type NextParsedUrlQuery,\n  type NextUrlWithParsedQuery,\n} from '../request-meta'\nimport type { DevBundlerService } from '../lib/dev-bundler-service'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\nimport type { NodeNextResponse, NodeNextRequest } from '../base-http/node'\nimport type { RouteEnsurer } from '../route-matcher-managers/dev-route-matcher-manager'\nimport type { PagesManifest } from '../../build/webpack/plugins/pages-manifest-plugin'\n\nimport * as React from 'react'\nimport fs from 'fs'\nimport { Worker } from 'next/dist/compiled/jest-worker'\nimport { join as pathJoin } from 'path'\nimport { ampValidation } from '../../build/output'\nimport { PUBLIC_DIR_MIDDLEWARE_CONFLICT } from '../../lib/constants'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PAGES_MANIFEST,\n  APP_PATHS_MANIFEST,\n  COMPILER_NAMES,\n  PRERENDER_MANIFEST,\n} from '../../shared/lib/constants'\nimport Server, { WrappedBuildError } from '../next-server'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport { Telemetry } from '../../telemetry/storage'\nimport { type Span, setGlobal, trace } from '../../trace'\nimport { findPageFile } from '../lib/find-page-file'\nimport { getFormattedNodeOptionsWithoutInspect } from '../lib/utils'\nimport { withCoalescedInvoke } from '../../lib/coalesced-function'\nimport { loadDefaultErrorComponents } from '../load-default-error-components'\nimport { DecodeError, MiddlewareNotFoundError } from '../../shared/lib/utils'\nimport * as Log from '../../build/output/log'\nimport isError, { getProperError } from '../../lib/is-error'\nimport { isMiddlewareFile } from '../../build/utils'\nimport { formatServerError } from '../../lib/format-server-error'\nimport { DevRouteMatcherManager } from '../route-matcher-managers/dev-route-matcher-manager'\nimport { DevPagesRouteMatcherProvider } from '../route-matcher-providers/dev/dev-pages-route-matcher-provider'\nimport { DevPagesAPIRouteMatcherProvider } from '../route-matcher-providers/dev/dev-pages-api-route-matcher-provider'\nimport { DevAppPageRouteMatcherProvider } from '../route-matcher-providers/dev/dev-app-page-route-matcher-provider'\nimport { DevAppRouteRouteMatcherProvider } from '../route-matcher-providers/dev/dev-app-route-route-matcher-provider'\nimport { NodeManifestLoader } from '../route-matcher-providers/helpers/manifest-loaders/node-manifest-loader'\nimport { BatchedFileReader } from '../route-matcher-providers/dev/helpers/file-reader/batched-file-reader'\nimport { DefaultFileReader } from '../route-matcher-providers/dev/helpers/file-reader/default-file-reader'\nimport { LRUCache } from '../lib/lru-cache'\nimport { getMiddlewareRouteMatcher } from '../../shared/lib/router/utils/middleware-route-matcher'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { isPostpone } from '../lib/router-utils/is-postpone'\nimport { generateInterceptionRoutesRewrites } from '../../lib/generate-interception-routes-rewrites'\nimport { buildCustomRoute } from '../../lib/build-custom-route'\nimport { decorateServerError } from '../../shared/lib/error-source'\nimport type { ServerOnInstrumentationRequestError } from '../app-render/types'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport { logRequests } from './log-requests'\nimport { FallbackMode, fallbackModeToFallbackField } from '../../lib/fallback'\nimport type { PagesDevOverlayBridgeType } from '../../next-devtools/userspace/pages/pages-dev-overlay-setup'\nimport {\n  ensureInstrumentationRegistered,\n  getInstrumentationModule,\n} from '../lib/router-utils/instrumentation-globals.external'\nimport type { PrerenderManifest } from '../../build'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\nimport type { PrerenderedRoute } from '../../build/static-paths/types'\n\n// Load ReactDevOverlay only when needed\nlet PagesDevOverlayBridgeImpl: PagesDevOverlayBridgeType\nconst ReactDevOverlay: PagesDevOverlayBridgeType = (props) => {\n  if (PagesDevOverlayBridgeImpl === undefined) {\n    PagesDevOverlayBridgeImpl = (\n      require('../../next-devtools/userspace/pages/pages-dev-overlay-setup') as typeof import('../../next-devtools/userspace/pages/pages-dev-overlay-setup')\n    ).PagesDevOverlayBridge\n  }\n  return React.createElement(PagesDevOverlayBridgeImpl, props)\n}\n\nexport interface Options extends ServerOptions {\n  /**\n   * Tells of Next.js is running from the `next dev` command\n   */\n  isNextDevCommand?: boolean\n\n  /**\n   * Interface to the development bundler.\n   */\n  bundlerService: DevBundlerService\n\n  /**\n   * Trace span for server startup.\n   */\n  startServerSpan: Span\n}\n\nexport default class DevServer extends Server {\n  /**\n   * The promise that resolves when the server is ready. When this is unset\n   * the server is ready.\n   */\n  private ready? = new DetachedPromise<void>()\n  protected sortedRoutes?: string[]\n  private pagesDir?: string\n  private appDir?: string\n  private actualMiddlewareFile?: string\n  private actualInstrumentationHookFile?: string\n  private middleware?: MiddlewareRoutingItem\n  private readonly bundlerService: DevBundlerService\n  private staticPathsCache: LRUCache<\n    UnwrapPromise<ReturnType<DevServer['getStaticPaths']>>\n  >\n  private startServerSpan: Span\n  private readonly serverComponentsHmrCache:\n    | ServerComponentsHmrCache\n    | undefined\n\n  protected staticPathsWorker?: { [key: string]: any } & {\n    loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n  }\n\n  private getStaticPathsWorker(): { [key: string]: any } & {\n    loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n  } {\n    const worker = new Worker(require.resolve('./static-paths-worker'), {\n      maxRetries: 1,\n      // For dev server, it's not necessary to spin up too many workers as long as you are not doing a load test.\n      // This helps reusing the memory a lot.\n      numWorkers: 1,\n      enableWorkerThreads: this.nextConfig.experimental.workerThreads,\n      forkOptions: {\n        env: {\n          ...process.env,\n          // discard --inspect/--inspect-brk flags from process.env.NODE_OPTIONS. Otherwise multiple Node.js debuggers\n          // would be started if user launch Next.js in debugging mode. The number of debuggers is linked to\n          // the number of workers Next.js tries to launch. The only worker users are interested in debugging\n          // is the main Next.js one\n          NODE_OPTIONS: getFormattedNodeOptionsWithoutInspect(),\n        },\n      },\n    }) as Worker & {\n      loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n    }\n\n    worker.getStdout().pipe(process.stdout)\n    worker.getStderr().pipe(process.stderr)\n\n    return worker\n  }\n\n  constructor(options: Options) {\n    try {\n      // Increase the number of stack frames on the server\n      Error.stackTraceLimit = 50\n    } catch {}\n    super({ ...options, dev: true })\n    this.bundlerService = options.bundlerService\n    this.startServerSpan =\n      options.startServerSpan ?? trace('start-next-dev-server')\n    this.renderOpts.dev = true\n    this.renderOpts.ErrorDebug = ReactDevOverlay\n    this.staticPathsCache = new LRUCache(\n      // 5MB\n      5 * 1024 * 1024,\n      function length(value) {\n        return JSON.stringify(value.staticPaths)?.length ?? 0\n      }\n    )\n    this.renderOpts.ampSkipValidation =\n      this.nextConfig.experimental?.amp?.skipValidation ?? false\n    this.renderOpts.ampValidator = async (html: string, pathname: string) => {\n      const { getAmpValidatorInstance, getBundledAmpValidatorFilepath } =\n        require('../../export/helpers/get-amp-html-validator') as typeof import('../../export/helpers/get-amp-html-validator')\n\n      const validatorPath =\n        this.nextConfig.experimental?.amp?.validator ||\n        getBundledAmpValidatorFilepath()\n\n      const validator = await getAmpValidatorInstance(validatorPath)\n\n      const result = validator.validateString(html)\n      ampValidation(\n        pathname,\n        result.errors\n          .filter((error) => {\n            if (error.severity === 'ERROR') {\n              // Unclear yet if these actually prevent the page from being indexed by the AMP cache.\n              // These are coming from React so all we can do is ignore them for now.\n\n              // <link rel=\"expect\" blocking=\"render\" />\n              // https://github.com/ampproject/amphtml/issues/40279\n              if (\n                error.code === 'DISALLOWED_ATTR' &&\n                error.params[0] === 'blocking' &&\n                error.params[1] === 'link'\n              ) {\n                return false\n              }\n              // <template> without type\n              // https://github.com/ampproject/amphtml/issues/40280\n              if (\n                error.code === 'MANDATORY_ATTR_MISSING' &&\n                error.params[0] === 'type' &&\n                error.params[1] === 'template'\n              ) {\n                return false\n              }\n              // <template> without type\n              // https://github.com/ampproject/amphtml/issues/40280\n              if (\n                error.code === 'MISSING_REQUIRED_EXTENSION' &&\n                error.params[0] === 'template' &&\n                error.params[1] === 'amp-mustache'\n              ) {\n                return false\n              }\n              return true\n            }\n            return false\n          })\n          .filter((e) => this._filterAmpDevelopmentScript(html, e)),\n        result.errors.filter((e) => e.severity !== 'ERROR')\n      )\n    }\n\n    const { pagesDir, appDir } = findPagesDir(this.dir)\n    this.pagesDir = pagesDir\n    this.appDir = appDir\n\n    if (this.nextConfig.experimental.serverComponentsHmrCache) {\n      this.serverComponentsHmrCache = new LRUCache(\n        this.nextConfig.cacheMaxMemorySize,\n        function length(value) {\n          return JSON.stringify(value).length\n        }\n      )\n    }\n  }\n\n  protected override getServerComponentsHmrCache() {\n    return this.serverComponentsHmrCache\n  }\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    const { pagesDir, appDir } = findPagesDir(this.dir)\n\n    const ensurer: RouteEnsurer = {\n      ensure: async (match, pathname) => {\n        await this.ensurePage({\n          definition: match.definition,\n          page: match.definition.page,\n          clientOnly: false,\n          url: pathname,\n        })\n      },\n    }\n\n    const matchers = new DevRouteMatcherManager(\n      super.getRouteMatchers(),\n      ensurer,\n      this.dir\n    )\n    const extensions = this.nextConfig.pageExtensions\n    const extensionsExpression = new RegExp(`\\\\.(?:${extensions.join('|')})$`)\n\n    // If the pages directory is available, then configure those matchers.\n    if (pagesDir) {\n      const fileReader = new BatchedFileReader(\n        new DefaultFileReader({\n          // Only allow files that have the correct extensions.\n          pathnameFilter: (pathname) => extensionsExpression.test(pathname),\n        })\n      )\n\n      matchers.push(\n        new DevPagesRouteMatcherProvider(\n          pagesDir,\n          extensions,\n          fileReader,\n          this.localeNormalizer\n        )\n      )\n      matchers.push(\n        new DevPagesAPIRouteMatcherProvider(\n          pagesDir,\n          extensions,\n          fileReader,\n          this.localeNormalizer\n        )\n      )\n    }\n\n    if (appDir) {\n      // We create a new file reader for the app directory because we don't want\n      // to include any folders or files starting with an underscore. This will\n      // prevent the reader from wasting time reading files that we know we\n      // don't care about.\n      const fileReader = new BatchedFileReader(\n        new DefaultFileReader({\n          // Ignore any directory prefixed with an underscore.\n          ignorePartFilter: (part) => part.startsWith('_'),\n        })\n      )\n\n      // TODO: Improve passing of \"is running with Turbopack\"\n      const isTurbopack = !!process.env.TURBOPACK\n      matchers.push(\n        new DevAppPageRouteMatcherProvider(\n          appDir,\n          extensions,\n          fileReader,\n          isTurbopack\n        )\n      )\n      matchers.push(\n        new DevAppRouteRouteMatcherProvider(\n          appDir,\n          extensions,\n          fileReader,\n          isTurbopack\n        )\n      )\n    }\n\n    return matchers\n  }\n\n  protected getBuildId(): string {\n    return 'development'\n  }\n\n  protected async prepareImpl(): Promise<void> {\n    setGlobal('distDir', this.distDir)\n    setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n    const telemetry = new Telemetry({ distDir: this.distDir })\n\n    await super.prepareImpl()\n    await this.matchers.reload()\n\n    this.ready?.resolve()\n    this.ready = undefined\n\n    // In dev, this needs to be called after prepare because the build entries won't be known in the constructor\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // This is required by the tracing subsystem.\n    setGlobal('appDir', this.appDir)\n    setGlobal('pagesDir', this.pagesDir)\n    setGlobal('telemetry', telemetry)\n\n    process.on('unhandledRejection', (reason) => {\n      if (isPostpone(reason)) {\n        // React postpones that are unhandled might end up logged here but they're\n        // not really errors. They're just part of rendering.\n        return\n      }\n      this.logErrorWithOriginalStack(reason, 'unhandledRejection')\n    })\n    process.on('uncaughtException', (err) => {\n      this.logErrorWithOriginalStack(err, 'uncaughtException')\n    })\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    let normalizedPath: string\n    try {\n      normalizedPath = normalizePagePath(pathname)\n    } catch (err) {\n      console.error(err)\n      // if normalizing the page fails it means it isn't valid\n      // so it doesn't exist so don't throw and return false\n      // to ensure we return 404 instead of 500\n      return false\n    }\n\n    if (isMiddlewareFile(normalizedPath)) {\n      return findPageFile(\n        this.dir,\n        normalizedPath,\n        this.nextConfig.pageExtensions,\n        false\n      ).then(Boolean)\n    }\n\n    let appFile: string | null = null\n    let pagesFile: string | null = null\n\n    if (this.appDir) {\n      appFile = await findPageFile(\n        this.appDir,\n        normalizedPath + '/page',\n        this.nextConfig.pageExtensions,\n        true\n      )\n    }\n\n    if (this.pagesDir) {\n      pagesFile = await findPageFile(\n        this.pagesDir,\n        normalizedPath,\n        this.nextConfig.pageExtensions,\n        false\n      )\n    }\n    if (appFile && pagesFile) {\n      return false\n    }\n\n    return Boolean(appFile || pagesFile)\n  }\n\n  async runMiddleware(params: {\n    request: NodeNextRequest\n    response: NodeNextResponse\n    parsedUrl: ParsedUrl\n    parsed: UrlWithParsedQuery\n    middlewareList: MiddlewareRoutingItem[]\n  }) {\n    try {\n      const result = await super.runMiddleware({\n        ...params,\n        onWarning: (warn) => {\n          this.logErrorWithOriginalStack(warn, 'warning')\n        },\n      })\n\n      if ('finished' in result) {\n        return result\n      }\n\n      result.waitUntil.catch((error) => {\n        this.logErrorWithOriginalStack(error, 'unhandledRejection')\n      })\n      return result\n    } catch (error) {\n      if (error instanceof DecodeError) {\n        throw error\n      }\n\n      /**\n       * We only log the error when it is not a MiddlewareNotFound error as\n       * in that case we should be already displaying a compilation error\n       * which is what makes the module not found.\n       */\n      if (!(error instanceof MiddlewareNotFoundError)) {\n        this.logErrorWithOriginalStack(error)\n      }\n\n      const err = getProperError(error)\n      decorateServerError(err, COMPILER_NAMES.edgeServer)\n      const { request, response, parsedUrl } = params\n\n      /**\n       * When there is a failure for an internal Next.js request from\n       * middleware we bypass the error without finishing the request\n       * so we can serve the required chunks to render the error.\n       */\n      if (\n        request.url.includes('/_next/static') ||\n        request.url.includes('/__nextjs_original-stack-frame') ||\n        request.url.includes('/__nextjs_source-map') ||\n        request.url.includes('/__nextjs_error_feedback')\n      ) {\n        return { finished: false }\n      }\n\n      response.statusCode = 500\n      await this.renderError(err, request, response, parsedUrl.pathname)\n      return { finished: true }\n    }\n  }\n\n  async runEdgeFunction(params: {\n    req: NodeNextRequest\n    res: NodeNextResponse\n    query: ParsedUrlQuery\n    params: Params | undefined\n    page: string\n    appPaths: string[] | null\n    isAppPath: boolean\n  }) {\n    try {\n      return super.runEdgeFunction({\n        ...params,\n        onError: (err) => this.logErrorWithOriginalStack(err, 'app-dir'),\n        onWarning: (warn) => {\n          this.logErrorWithOriginalStack(warn, 'warning')\n        },\n      })\n    } catch (error) {\n      if (error instanceof DecodeError) {\n        throw error\n      }\n      this.logErrorWithOriginalStack(error, 'warning')\n      const err = getProperError(error)\n      const { req, res, page } = params\n\n      res.statusCode = 500\n      await this.renderError(err, req, res, page)\n      return null\n    }\n  }\n\n  public getRequestHandler(): NodeRequestHandler {\n    const handler = super.getRequestHandler()\n\n    return (req, res, parsedUrl) => {\n      const request = this.normalizeReq(req)\n      const response = this.normalizeRes(res)\n      const loggingConfig = this.nextConfig.logging\n\n      if (loggingConfig !== false) {\n        const start = Date.now()\n        const isMiddlewareRequest = getRequestMeta(req, 'middlewareInvoke')\n\n        if (!isMiddlewareRequest) {\n          response.originalResponse.once('close', () => {\n            // NOTE: The route match is only attached to the request's meta data\n            // after the request handler is created, so we need to check it in the\n            // close handler and not before.\n            const routeMatch = getRequestMeta(req).match\n\n            if (!routeMatch) {\n              return\n            }\n\n            logRequests({\n              request,\n              response,\n              loggingConfig,\n              requestDurationInMs: Date.now() - start,\n            })\n          })\n        }\n      }\n\n      return handler(request, response, parsedUrl)\n    }\n  }\n\n  public async handleRequest(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    const span = trace('handle-request', undefined, { url: req.url })\n    const result = await span.traceAsyncFn(async () => {\n      await this.ready?.promise\n      addRequestMeta(req, 'PagesErrorDebug', this.renderOpts.ErrorDebug)\n      return await super.handleRequest(req, res, parsedUrl)\n    })\n    const memoryUsage = process.memoryUsage()\n    span\n      .traceChild('memory-usage', {\n        url: req.url,\n        'memory.rss': String(memoryUsage.rss),\n        'memory.heapUsed': String(memoryUsage.heapUsed),\n        'memory.heapTotal': String(memoryUsage.heapTotal),\n      })\n      .stop()\n    return result\n  }\n\n  async run(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.ready?.promise\n\n    const { basePath } = this.nextConfig\n    let originalPathname: string | null = null\n\n    // TODO: see if we can remove this in the future\n    if (basePath && pathHasPrefix(parsedUrl.pathname || '/', basePath)) {\n      // strip basePath before handling dev bundles\n      // If replace ends up replacing the full url it'll be `undefined`, meaning we have to default it to `/`\n      originalPathname = parsedUrl.pathname\n      parsedUrl.pathname = removePathPrefix(parsedUrl.pathname || '/', basePath)\n    }\n\n    const { pathname } = parsedUrl\n\n    if (pathname!.startsWith('/_next')) {\n      if (fs.existsSync(pathJoin(this.publicDir, '_next'))) {\n        throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n      }\n    }\n\n    if (originalPathname) {\n      // restore the path before continuing so that custom-routes can accurately determine\n      // if they should match against the basePath or not\n      parsedUrl.pathname = originalPathname\n    }\n    try {\n      return await super.run(req, res, parsedUrl)\n    } catch (error) {\n      const err = getProperError(error)\n      formatServerError(err)\n      this.logErrorWithOriginalStack(err)\n      if (!res.sent) {\n        res.statusCode = 500\n        try {\n          return await this.renderError(err, req, res, pathname!, {\n            __NEXT_PAGE: (isError(err) && err.page) || pathname || '',\n          })\n        } catch (internalErr) {\n          console.error(internalErr)\n          res.body('Internal Server Error').send()\n        }\n      }\n    }\n  }\n\n  protected logErrorWithOriginalStack(\n    err?: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ): void {\n    this.bundlerService.logErrorWithOriginalStack(err, type)\n  }\n\n  protected getPagesManifest(): PagesManifest | undefined {\n    return (\n      NodeManifestLoader.require(\n        pathJoin(this.serverDistDir, PAGES_MANIFEST)\n      ) ?? undefined\n    )\n  }\n\n  protected getAppPathsManifest(): PagesManifest | undefined {\n    if (!this.enabledDirectories.app) return undefined\n\n    return (\n      NodeManifestLoader.require(\n        pathJoin(this.serverDistDir, APP_PATHS_MANIFEST)\n      ) ?? undefined\n    )\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    const rewrites = generateInterceptionRoutesRewrites(\n      Object.keys(this.appPathRoutes ?? {}),\n      this.nextConfig.basePath\n    ).map((route) => new RegExp(buildCustomRoute('rewrite', route).regex))\n\n    if (this.nextConfig.output === 'export' && rewrites.length > 0) {\n      Log.error(\n        'Intercepting routes are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features'\n      )\n\n      process.exit(1)\n    }\n\n    return rewrites ?? []\n  }\n\n  protected async getMiddleware() {\n    // We need to populate the match\n    // field as it isn't serializable\n    if (this.middleware?.match === null) {\n      this.middleware.match = getMiddlewareRouteMatcher(\n        this.middleware.matchers || []\n      )\n    }\n    return this.middleware\n  }\n\n  protected getNextFontManifest() {\n    return undefined\n  }\n\n  protected async hasMiddleware(): Promise<boolean> {\n    return this.hasPage(this.actualMiddlewareFile!)\n  }\n\n  protected async ensureMiddleware(url: string) {\n    return this.ensurePage({\n      page: this.actualMiddlewareFile!,\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  protected async loadInstrumentationModule(): Promise<any> {\n    let instrumentationModule: any\n    if (\n      this.actualInstrumentationHookFile &&\n      (await this.ensurePage({\n        page: this.actualInstrumentationHookFile!,\n        clientOnly: false,\n        definition: undefined,\n      })\n        .then(() => true)\n        .catch(() => false))\n    ) {\n      try {\n        instrumentationModule = await getInstrumentationModule(\n          this.dir,\n          this.nextConfig.distDir\n        )\n      } catch (err: any) {\n        err.message = `An error occurred while loading instrumentation hook: ${err.message}`\n        throw err\n      }\n    }\n    return instrumentationModule\n  }\n\n  protected async runInstrumentationHookIfAvailable() {\n    await ensureInstrumentationRegistered(this.dir, this.nextConfig.distDir)\n  }\n\n  protected async ensureEdgeFunction({\n    page,\n    appPaths,\n    url,\n  }: {\n    page: string\n    appPaths: string[] | null\n    url: string\n  }) {\n    return this.ensurePage({\n      page,\n      appPaths,\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  generateRoutes(_dev?: boolean) {\n    // In development we expose all compiled files for react-error-overlay's line show feature\n    // We use unshift so that we're sure the routes is defined before Next's default routes\n    // routes.unshift({\n    //   match: getPathMatch('/_next/development/:path*'),\n    //   type: 'route',\n    //   name: '_next/development catchall',\n    //   fn: async (req, res, params) => {\n    //     const p = pathJoin(this.distDir, ...(params.path || []))\n    //     await this.serveStatic(req, res, p)\n    //     return {\n    //       finished: true,\n    //     }\n    //   },\n    // })\n  }\n\n  _filterAmpDevelopmentScript(\n    html: string,\n    event: { line: number; col: number; code: string }\n  ): boolean {\n    if (event.code !== 'DISALLOWED_SCRIPT_TAG') {\n      return true\n    }\n\n    const snippetChunks = html.split('\\n')\n\n    let snippet\n    if (\n      !(snippet = html.split('\\n')[event.line - 1]) ||\n      !(snippet = snippet.substring(event.col))\n    ) {\n      return true\n    }\n\n    snippet = snippet + snippetChunks.slice(event.line).join('\\n')\n    snippet = snippet.substring(0, snippet.indexOf('</script>'))\n\n    return !snippet.includes('data-amp-development-mode-only')\n  }\n\n  protected async getStaticPaths({\n    pathname,\n    urlPathname,\n    requestHeaders,\n    page,\n    isAppPath,\n  }: {\n    pathname: string\n    urlPathname: string\n    requestHeaders: IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    prerenderedRoutes?: PrerenderedRoute[]\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // we lazy load the staticPaths to prevent the user\n    // from waiting on them for the page to load in dev mode\n\n    const __getStaticPaths = async () => {\n      const {\n        configFileName,\n        publicRuntimeConfig,\n        serverRuntimeConfig,\n        httpAgentOptions,\n      } = this.nextConfig\n      const { locales, defaultLocale } = this.nextConfig.i18n || {}\n      const staticPathsWorker = this.getStaticPathsWorker()\n\n      try {\n        const pathsResult = await staticPathsWorker.loadStaticPaths({\n          dir: this.dir,\n          distDir: this.distDir,\n          pathname,\n          config: {\n            pprConfig: this.nextConfig.experimental.ppr,\n            configFileName,\n            publicRuntimeConfig,\n            serverRuntimeConfig,\n            cacheComponents: Boolean(\n              this.nextConfig.experimental.cacheComponents\n            ),\n          },\n          httpAgentOptions,\n          locales,\n          defaultLocale,\n          page,\n          isAppPath,\n          requestHeaders,\n          cacheHandler: this.nextConfig.cacheHandler,\n          cacheHandlers: this.nextConfig.experimental.cacheHandlers,\n          cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n          fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n          isrFlushToDisk: this.nextConfig.experimental.isrFlushToDisk,\n          maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n          nextConfigOutput: this.nextConfig.output,\n          buildId: this.buildId,\n          authInterrupts: Boolean(this.nextConfig.experimental.authInterrupts),\n          sriEnabled: Boolean(this.nextConfig.experimental.sri?.algorithm),\n        })\n        return pathsResult\n      } finally {\n        // we don't re-use workers so destroy the used one\n        staticPathsWorker.end()\n      }\n    }\n    const result = this.staticPathsCache.get(pathname)\n\n    const nextInvoke = withCoalescedInvoke(__getStaticPaths)(\n      `staticPaths-${pathname}`,\n      []\n    )\n      .then(async (res) => {\n        const { prerenderedRoutes, fallbackMode: fallback } = res.value\n\n        if (isAppPath) {\n          if (this.nextConfig.output === 'export') {\n            if (!prerenderedRoutes) {\n              throw new Error(\n                `Page \"${page}\" is missing exported function \"generateStaticParams()\", which is required with \"output: export\" config.`\n              )\n            }\n\n            if (\n              !prerenderedRoutes.some((item) => item.pathname === urlPathname)\n            ) {\n              throw new Error(\n                `Page \"${page}\" is missing param \"${pathname}\" in \"generateStaticParams()\", which is required with \"output: export\" config.`\n              )\n            }\n          }\n        }\n\n        if (!isAppPath && this.nextConfig.output === 'export') {\n          if (fallback === FallbackMode.BLOCKING_STATIC_RENDER) {\n            throw new Error(\n              'getStaticPaths with \"fallback: blocking\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n          } else if (fallback === FallbackMode.PRERENDER) {\n            throw new Error(\n              'getStaticPaths with \"fallback: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n          }\n        }\n\n        const value: {\n          staticPaths: string[] | undefined\n          prerenderedRoutes: PrerenderedRoute[] | undefined\n          fallbackMode: FallbackMode | undefined\n        } = {\n          staticPaths: prerenderedRoutes?.map((route) => route.pathname),\n          prerenderedRoutes,\n          fallbackMode: fallback,\n        }\n\n        if (\n          res.value?.fallbackMode !== undefined &&\n          // This matches the hasGenerateStaticParams logic\n          // we do during build\n          (!isAppPath || (prerenderedRoutes && prerenderedRoutes.length > 0))\n        ) {\n          // we write the static paths to partial manifest for\n          // fallback handling inside of entry handler's\n          const rawExistingManifest = await fs.promises.readFile(\n            pathJoin(this.distDir, PRERENDER_MANIFEST),\n            'utf8'\n          )\n          const existingManifest: PrerenderManifest =\n            JSON.parse(rawExistingManifest)\n          for (const staticPath of value.staticPaths || []) {\n            existingManifest.routes[staticPath] = {} as any\n          }\n          existingManifest.dynamicRoutes[pathname] = {\n            dataRoute: null,\n            dataRouteRegex: null,\n            fallback: fallbackModeToFallbackField(res.value.fallbackMode, page),\n            fallbackRevalidate: false,\n            fallbackExpire: undefined,\n            fallbackHeaders: undefined,\n            fallbackStatus: undefined,\n            fallbackRootParams: undefined,\n            fallbackSourceRoute: pathname,\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            routeRegex: getRouteRegex(pathname).re.source,\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            allowHeader: [],\n          }\n\n          const updatedManifest = JSON.stringify(existingManifest)\n\n          if (updatedManifest !== rawExistingManifest) {\n            await fs.promises.writeFile(\n              pathJoin(this.distDir, PRERENDER_MANIFEST),\n              updatedManifest\n            )\n          }\n        }\n        this.staticPathsCache.set(pathname, value)\n        return value\n      })\n      .catch((err) => {\n        this.staticPathsCache.remove(pathname)\n        if (!result) throw err\n        Log.error(`Failed to generate static paths for ${pathname}:`)\n        console.error(err)\n      })\n\n    if (result) {\n      return result\n    }\n    return nextInvoke as NonNullable<typeof result>\n  }\n\n  protected async ensurePage(opts: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    definition: RouteDefinition | undefined\n    url?: string\n  }): Promise<void> {\n    await this.bundlerService.ensurePage(opts)\n  }\n\n  protected async findPageComponents({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    appPaths = null,\n    shouldEnsure,\n    url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    await this.ready?.promise\n\n    const compilationErr = await this.getCompilationError(page)\n    if (compilationErr) {\n      // Wrap build errors so that they don't get logged again\n      throw new WrappedBuildError(compilationErr)\n    }\n    if (shouldEnsure || this.serverOptions.customServer) {\n      await this.ensurePage({\n        page,\n        appPaths,\n        clientOnly: false,\n        definition: undefined,\n        url,\n      })\n    }\n\n    this.nextFontManifest = super.getNextFontManifest()\n\n    return await super.findPageComponents({\n      page,\n      query,\n      params,\n      locale,\n      isAppPath,\n      shouldEnsure,\n      url,\n    })\n  }\n\n  protected async getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    await this.bundlerService.getFallbackErrorComponents(url)\n    return await loadDefaultErrorComponents(this.distDir)\n  }\n\n  async getCompilationError(page: string): Promise<any> {\n    return await this.bundlerService.getCompilationError(page)\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n\n    const err = args[0]\n    this.logErrorWithOriginalStack(err, 'app-dir')\n  }\n}\n"], "names": ["addRequestMeta", "getRequestMeta", "React", "fs", "Worker", "join", "pathJoin", "ampValidation", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "findPagesDir", "PHASE_DEVELOPMENT_SERVER", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "COMPILER_NAMES", "PRERENDER_MANIFEST", "Server", "WrappedBuildError", "normalizePagePath", "pathHasPrefix", "removePathPrefix", "Telemetry", "setGlobal", "trace", "findPageFile", "getFormattedNodeOptionsWithoutInspect", "withCoalescedInvoke", "loadDefaultErrorComponents", "DecodeError", "MiddlewareNotFoundError", "Log", "isError", "getProperError", "isMiddlewareFile", "formatServerError", "DevRouteMatcherManager", "DevPagesRouteMatcherProvider", "DevPagesAPIRouteMatcherProvider", "DevAppPageRouteMatcherProvider", "DevAppRouteRouteMatcherProvider", "NodeManifestLoader", "BatchedFileReader", "DefaultFileReader", "L<PERSON><PERSON><PERSON>", "getMiddlewareRouteMatcher", "Detached<PERSON>romise", "isPostpone", "generateInterceptionRoutesRewrites", "buildCustomRoute", "decorateServerError", "logRequests", "FallbackMode", "fallbackModeToFallbackField", "ensureInstrumentationRegistered", "getInstrumentationModule", "getRouteRegex", "PagesDevOverlayBridgeImpl", "ReactDevOverlay", "props", "undefined", "require", "PagesDevOverlayBridge", "createElement", "DevServer", "getStaticPathsWorker", "worker", "resolve", "maxRetries", "numWorkers", "enableWorkerThreads", "nextConfig", "experimental", "workerThreads", "forkOptions", "env", "process", "NODE_OPTIONS", "getStdout", "pipe", "stdout", "getStderr", "stderr", "constructor", "options", "Error", "stackTraceLimit", "dev", "ready", "bundlerService", "startServerSpan", "renderOpts", "ErrorDebug", "staticPathsCache", "length", "value", "JSON", "stringify", "staticPaths", "ampSkipValidation", "amp", "skipValidation", "ampValidator", "html", "pathname", "getAmpValidatorInstance", "getBundledAmpValidatorFilepath", "validatorPath", "validator", "result", "validateString", "errors", "filter", "error", "severity", "code", "params", "e", "_filterAmpDevelopmentScript", "pagesDir", "appDir", "dir", "serverComponentsHmrCache", "cacheMaxMemorySize", "getServerComponentsHmrCache", "getRouteMatchers", "ensurer", "ensure", "match", "ensurePage", "definition", "page", "clientOnly", "url", "matchers", "extensions", "pageExtensions", "extensionsExpression", "RegExp", "fileReader", "pathnameFilter", "test", "push", "localeNormalizer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "startsWith", "isTurbopack", "TURBOPACK", "getBuildId", "prepareImpl", "distDir", "telemetry", "reload", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "on", "reason", "logErrorWithOriginalStack", "err", "hasPage", "normalizedPath", "console", "then", "Boolean", "appFile", "pagesFile", "runMiddleware", "onWarning", "warn", "waitUntil", "catch", "edgeServer", "request", "response", "parsedUrl", "includes", "finished", "statusCode", "renderError", "runEdgeFunction", "onError", "req", "res", "getRequestHandler", "handler", "normalizeReq", "normalizeRes", "loggingConfig", "logging", "start", "Date", "now", "isMiddlewareRequest", "originalResponse", "once", "routeMatch", "requestDurationInMs", "handleRequest", "span", "traceAsyncFn", "promise", "memoryUsage", "<PERSON><PERSON><PERSON><PERSON>", "String", "rss", "heapUsed", "heapTotal", "stop", "run", "basePath", "originalPathname", "existsSync", "publicDir", "sent", "__NEXT_PAGE", "internalErr", "body", "send", "type", "getPagesManifest", "serverDistDir", "getAppPathsManifest", "enabledDirectories", "app", "rewrites", "Object", "keys", "appPathRoutes", "map", "route", "regex", "output", "exit", "getMiddleware", "middleware", "getNextFontManifest", "hasMiddleware", "actualMiddlewareFile", "ensureMiddleware", "loadInstrumentationModule", "instrumentationModule", "actualInstrumentationHookFile", "message", "runInstrumentationHookIfAvailable", "ensureEdgeFunction", "appPaths", "generateRoutes", "_dev", "event", "snippetChunks", "split", "snippet", "line", "substring", "col", "slice", "indexOf", "getStaticPaths", "urlPathname", "requestHeaders", "isAppPath", "__getStaticPaths", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "httpAgentOptions", "locales", "defaultLocale", "i18n", "staticPathsWorker", "pathsResult", "loadStaticPaths", "config", "pprConfig", "ppr", "cacheComponents", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheLifeProfiles", "cacheLife", "fetchCacheKeyPrefix", "isrFlushToDisk", "maxMemoryCacheSize", "nextConfigOutput", "buildId", "authInterrupts", "sriEnabled", "sri", "algorithm", "end", "get", "nextInvoke", "prerenderedRoutes", "fallbackMode", "fallback", "some", "item", "BLOCKING_STATIC_RENDER", "PRERENDER", "rawExistingManifest", "promises", "readFile", "existingManifest", "parse", "staticPath", "routes", "dynamicRoutes", "dataRoute", "dataRouteRegex", "fallbackRevalidate", "fallbackExpire", "fallbackHeaders", "fallback<PERSON><PERSON><PERSON>", "fallbackRootParams", "fallbackSourceRoute", "prefetchDataRoute", "prefetchDataRouteRegex", "routeRegex", "re", "source", "experimentalPPR", "renderingMode", "allow<PERSON>eader", "updatedManifest", "writeFile", "set", "remove", "opts", "findPageComponents", "locale", "query", "shouldEnsure", "compilationErr", "getCompilationError", "serverOptions", "customServer", "nextFontManifest", "getFallbackErrorComponents", "instrumentationOnRequestError", "args"], "mappings": "AAUA,SACEA,cAAc,EACdC,cAAc,QAGT,kBAAiB;AAQxB,YAAYC,WAAW,QAAO;AAC9B,OAAOC,QAAQ,KAAI;AACnB,SAASC,MAAM,QAAQ,iCAAgC;AACvD,SAASC,QAAQC,QAAQ,QAAQ,OAAM;AACvC,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,8BAA8B,QAAQ,sBAAqB;AACpE,SAASC,YAAY,QAAQ,2BAA0B;AACvD,SACEC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,cAAc,EACdC,kBAAkB,QACb,6BAA4B;AACnC,OAAOC,UAAUC,iBAAiB,QAAQ,iBAAgB;AAC1D,SAASC,iBAAiB,QAAQ,iDAAgD;AAClF,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAAoBC,SAAS,EAAEC,KAAK,QAAQ,cAAa;AACzD,SAASC,YAAY,QAAQ,wBAAuB;AACpD,SAASC,qCAAqC,QAAQ,eAAc;AACpE,SAASC,mBAAmB,QAAQ,+BAA8B;AAClE,SAASC,0BAA0B,QAAQ,mCAAkC;AAC7E,SAASC,WAAW,EAAEC,uBAAuB,QAAQ,yBAAwB;AAC7E,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,WAAWC,cAAc,QAAQ,qBAAoB;AAC5D,SAASC,gBAAgB,QAAQ,oBAAmB;AACpD,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,SAASC,sBAAsB,QAAQ,sDAAqD;AAC5F,SAASC,4BAA4B,QAAQ,kEAAiE;AAC9G,SAASC,+BAA+B,QAAQ,sEAAqE;AACrH,SAASC,8BAA8B,QAAQ,qEAAoE;AACnH,SAASC,+BAA+B,QAAQ,sEAAqE;AACrH,SAASC,kBAAkB,QAAQ,2EAA0E;AAC7G,SAASC,iBAAiB,QAAQ,yEAAwE;AAC1G,SAASC,iBAAiB,QAAQ,yEAAwE;AAC1G,SAASC,QAAQ,QAAQ,mBAAkB;AAC3C,SAASC,yBAAyB,QAAQ,yDAAwD;AAClG,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,UAAU,QAAQ,kCAAiC;AAC5D,SAASC,kCAAkC,QAAQ,kDAAiD;AACpG,SAASC,gBAAgB,QAAQ,+BAA8B;AAC/D,SAASC,mBAAmB,QAAQ,gCAA+B;AAGnE,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,YAAY,EAAEC,2BAA2B,QAAQ,qBAAoB;AAE9E,SACEC,+BAA+B,EAC/BC,wBAAwB,QACnB,uDAAsD;AAE7D,SAASC,aAAa,QAAQ,4CAA2C;AAGzE,wCAAwC;AACxC,IAAIC;AACJ,MAAMC,kBAA6C,CAACC;IAClD,IAAIF,8BAA8BG,WAAW;QAC3CH,4BAA4B,AAC1BI,QAAQ,+DACRC,qBAAqB;IACzB;IACA,OAAO1D,MAAM2D,aAAa,CAACN,2BAA2BE;AACxD;AAmBA,eAAe,MAAMK,kBAAkB/C;IAyB7BgD,uBAEN;QACA,MAAMC,SAAS,IAAI5D,OAAOuD,QAAQM,OAAO,CAAC,0BAA0B;YAClEC,YAAY;YACZ,2GAA2G;YAC3G,uCAAuC;YACvCC,YAAY;YACZC,qBAAqB,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,aAAa;YAC/DC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACd,4GAA4G;oBAC5G,kGAAkG;oBAClG,mGAAmG;oBACnG,0BAA0B;oBAC1BE,cAAcnD;gBAChB;YACF;QACF;QAIAwC,OAAOY,SAAS,GAAGC,IAAI,CAACH,QAAQI,MAAM;QACtCd,OAAOe,SAAS,GAAGF,IAAI,CAACH,QAAQM,MAAM;QAEtC,OAAOhB;IACT;IAEAiB,YAAYC,OAAgB,CAAE;YAmB1B,mCAAA;QAlBF,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,OAAM,CAAC;QACT,KAAK,CAAC;YAAE,GAAGF,OAAO;YAAEG,KAAK;QAAK,IA1DhC;;;GAGC,QACOC,QAAS,IAAI1C;QAuDnB,IAAI,CAAC2C,cAAc,GAAGL,QAAQK,cAAc;QAC5C,IAAI,CAACC,eAAe,GAClBN,QAAQM,eAAe,IAAIlE,MAAM;QACnC,IAAI,CAACmE,UAAU,CAACJ,GAAG,GAAG;QACtB,IAAI,CAACI,UAAU,CAACC,UAAU,GAAGlC;QAC7B,IAAI,CAACmC,gBAAgB,GAAG,IAAIjD,SAC1B,MAAM;QACN,IAAI,OAAO,MACX,SAASkD,OAAOC,KAAK;gBACZC;YAAP,OAAOA,EAAAA,kBAAAA,KAAKC,SAAS,CAACF,MAAMG,WAAW,sBAAhCF,gBAAmCF,MAAM,KAAI;QACtD;QAEF,IAAI,CAACH,UAAU,CAACQ,iBAAiB,GAC/B,EAAA,gCAAA,IAAI,CAAC5B,UAAU,CAACC,YAAY,sBAA5B,oCAAA,8BAA8B4B,GAAG,qBAAjC,kCAAmCC,cAAc,KAAI;QACvD,IAAI,CAACV,UAAU,CAACW,YAAY,GAAG,OAAOC,MAAcC;gBAKhD,mCAAA;YAJF,MAAM,EAAEC,uBAAuB,EAAEC,8BAA8B,EAAE,GAC/D7C,QAAQ;YAEV,MAAM8C,gBACJ,EAAA,gCAAA,IAAI,CAACpC,UAAU,CAACC,YAAY,sBAA5B,oCAAA,8BAA8B4B,GAAG,qBAAjC,kCAAmCQ,SAAS,KAC5CF;YAEF,MAAME,YAAY,MAAMH,wBAAwBE;YAEhD,MAAME,SAASD,UAAUE,cAAc,CAACP;YACxC9F,cACE+F,UACAK,OAAOE,MAAM,CACVC,MAAM,CAAC,CAACC;gBACP,IAAIA,MAAMC,QAAQ,KAAK,SAAS;oBAC9B,sFAAsF;oBACtF,uEAAuE;oBAEvE,0CAA0C;oBAC1C,qDAAqD;oBACrD,IACED,MAAME,IAAI,KAAK,qBACfF,MAAMG,MAAM,CAAC,EAAE,KAAK,cACpBH,MAAMG,MAAM,CAAC,EAAE,KAAK,QACpB;wBACA,OAAO;oBACT;oBACA,0BAA0B;oBAC1B,qDAAqD;oBACrD,IACEH,MAAME,IAAI,KAAK,4BACfF,MAAMG,MAAM,CAAC,EAAE,KAAK,UACpBH,MAAMG,MAAM,CAAC,EAAE,KAAK,YACpB;wBACA,OAAO;oBACT;oBACA,0BAA0B;oBAC1B,qDAAqD;oBACrD,IACEH,MAAME,IAAI,KAAK,gCACfF,MAAMG,MAAM,CAAC,EAAE,KAAK,cACpBH,MAAMG,MAAM,CAAC,EAAE,KAAK,gBACpB;wBACA,OAAO;oBACT;oBACA,OAAO;gBACT;gBACA,OAAO;YACT,GACCJ,MAAM,CAAC,CAACK,IAAM,IAAI,CAACC,2BAA2B,CAACf,MAAMc,KACxDR,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACK,IAAMA,EAAEH,QAAQ,KAAK;QAE/C;QAEA,MAAM,EAAEK,QAAQ,EAAEC,MAAM,EAAE,GAAG7G,aAAa,IAAI,CAAC8G,GAAG;QAClD,IAAI,CAACF,QAAQ,GAAGA;QAChB,IAAI,CAACC,MAAM,GAAGA;QAEd,IAAI,IAAI,CAACjD,UAAU,CAACC,YAAY,CAACkD,wBAAwB,EAAE;YACzD,IAAI,CAACA,wBAAwB,GAAG,IAAI9E,SAClC,IAAI,CAAC2B,UAAU,CAACoD,kBAAkB,EAClC,SAAS7B,OAAOC,KAAK;gBACnB,OAAOC,KAAKC,SAAS,CAACF,OAAOD,MAAM;YACrC;QAEJ;IACF;IAEmB8B,8BAA8B;QAC/C,OAAO,IAAI,CAACF,wBAAwB;IACtC;IAEUG,mBAAwC;QAChD,MAAM,EAAEN,QAAQ,EAAEC,MAAM,EAAE,GAAG7G,aAAa,IAAI,CAAC8G,GAAG;QAElD,MAAMK,UAAwB;YAC5BC,QAAQ,OAAOC,OAAOxB;gBACpB,MAAM,IAAI,CAACyB,UAAU,CAAC;oBACpBC,YAAYF,MAAME,UAAU;oBAC5BC,MAAMH,MAAME,UAAU,CAACC,IAAI;oBAC3BC,YAAY;oBACZC,KAAK7B;gBACP;YACF;QACF;QAEA,MAAM8B,WAAW,IAAIlG,uBACnB,KAAK,CAACyF,oBACNC,SACA,IAAI,CAACL,GAAG;QAEV,MAAMc,aAAa,IAAI,CAAChE,UAAU,CAACiE,cAAc;QACjD,MAAMC,uBAAuB,IAAIC,OAAO,CAAC,MAAM,EAAEH,WAAWhI,IAAI,CAAC,KAAK,EAAE,CAAC;QAEzE,sEAAsE;QACtE,IAAIgH,UAAU;YACZ,MAAMoB,aAAa,IAAIjG,kBACrB,IAAIC,kBAAkB;gBACpB,qDAAqD;gBACrDiG,gBAAgB,CAACpC,WAAaiC,qBAAqBI,IAAI,CAACrC;YAC1D;YAGF8B,SAASQ,IAAI,CACX,IAAIzG,6BACFkF,UACAgB,YACAI,YACA,IAAI,CAACI,gBAAgB;YAGzBT,SAASQ,IAAI,CACX,IAAIxG,gCACFiF,UACAgB,YACAI,YACA,IAAI,CAACI,gBAAgB;QAG3B;QAEA,IAAIvB,QAAQ;YACV,0EAA0E;YAC1E,yEAAyE;YACzE,qEAAqE;YACrE,oBAAoB;YACpB,MAAMmB,aAAa,IAAIjG,kBACrB,IAAIC,kBAAkB;gBACpB,oDAAoD;gBACpDqG,kBAAkB,CAACC,OAASA,KAAKC,UAAU,CAAC;YAC9C;YAGF,uDAAuD;YACvD,MAAMC,cAAc,CAAC,CAACvE,QAAQD,GAAG,CAACyE,SAAS;YAC3Cd,SAASQ,IAAI,CACX,IAAIvG,+BACFiF,QACAe,YACAI,YACAQ;YAGJb,SAASQ,IAAI,CACX,IAAItG,gCACFgF,QACAe,YACAI,YACAQ;QAGN;QAEA,OAAOb;IACT;IAEUe,aAAqB;QAC7B,OAAO;IACT;IAEA,MAAgBC,cAA6B;YAS3C;QARA/H,UAAU,WAAW,IAAI,CAACgI,OAAO;QACjChI,UAAU,SAASX;QAEnB,MAAM4I,YAAY,IAAIlI,UAAU;YAAEiI,SAAS,IAAI,CAACA,OAAO;QAAC;QAExD,MAAM,KAAK,CAACD;QACZ,MAAM,IAAI,CAAChB,QAAQ,CAACmB,MAAM;SAE1B,cAAA,IAAI,CAACjE,KAAK,qBAAV,YAAYrB,OAAO;QACnB,IAAI,CAACqB,KAAK,GAAG5B;QAEb,4GAA4G;QAC5G,IAAI,CAAC8F,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,6CAA6C;QAC7CpI,UAAU,UAAU,IAAI,CAACiG,MAAM;QAC/BjG,UAAU,YAAY,IAAI,CAACgG,QAAQ;QACnChG,UAAU,aAAaiI;QAEvB5E,QAAQgF,EAAE,CAAC,sBAAsB,CAACC;YAChC,IAAI9G,WAAW8G,SAAS;gBACtB,0EAA0E;gBAC1E,qDAAqD;gBACrD;YACF;YACA,IAAI,CAACC,yBAAyB,CAACD,QAAQ;QACzC;QACAjF,QAAQgF,EAAE,CAAC,qBAAqB,CAACG;YAC/B,IAAI,CAACD,yBAAyB,CAACC,KAAK;QACtC;IACF;IAEA,MAAgBC,QAAQxD,QAAgB,EAAoB;QAC1D,IAAIyD;QACJ,IAAI;YACFA,iBAAiB9I,kBAAkBqF;QACrC,EAAE,OAAOuD,KAAK;YACZG,QAAQjD,KAAK,CAAC8C;YACd,wDAAwD;YACxD,sDAAsD;YACtD,yCAAyC;YACzC,OAAO;QACT;QAEA,IAAI7H,iBAAiB+H,iBAAiB;YACpC,OAAOxI,aACL,IAAI,CAACgG,GAAG,EACRwC,gBACA,IAAI,CAAC1F,UAAU,CAACiE,cAAc,EAC9B,OACA2B,IAAI,CAACC;QACT;QAEA,IAAIC,UAAyB;QAC7B,IAAIC,YAA2B;QAE/B,IAAI,IAAI,CAAC9C,MAAM,EAAE;YACf6C,UAAU,MAAM5I,aACd,IAAI,CAAC+F,MAAM,EACXyC,iBAAiB,SACjB,IAAI,CAAC1F,UAAU,CAACiE,cAAc,EAC9B;QAEJ;QAEA,IAAI,IAAI,CAACjB,QAAQ,EAAE;YACjB+C,YAAY,MAAM7I,aAChB,IAAI,CAAC8F,QAAQ,EACb0C,gBACA,IAAI,CAAC1F,UAAU,CAACiE,cAAc,EAC9B;QAEJ;QACA,IAAI6B,WAAWC,WAAW;YACxB,OAAO;QACT;QAEA,OAAOF,QAAQC,WAAWC;IAC5B;IAEA,MAAMC,cAAcnD,MAMnB,EAAE;QACD,IAAI;YACF,MAAMP,SAAS,MAAM,KAAK,CAAC0D,cAAc;gBACvC,GAAGnD,MAAM;gBACToD,WAAW,CAACC;oBACV,IAAI,CAACX,yBAAyB,CAACW,MAAM;gBACvC;YACF;YAEA,IAAI,cAAc5D,QAAQ;gBACxB,OAAOA;YACT;YAEAA,OAAO6D,SAAS,CAACC,KAAK,CAAC,CAAC1D;gBACtB,IAAI,CAAC6C,yBAAyB,CAAC7C,OAAO;YACxC;YACA,OAAOJ;QACT,EAAE,OAAOI,OAAO;YACd,IAAIA,iBAAiBpF,aAAa;gBAChC,MAAMoF;YACR;YAEA;;;;OAIC,GACD,IAAI,CAAEA,CAAAA,iBAAiBnF,uBAAsB,GAAI;gBAC/C,IAAI,CAACgI,yBAAyB,CAAC7C;YACjC;YAEA,MAAM8C,MAAM9H,eAAegF;YAC3B/D,oBAAoB6G,KAAKhJ,eAAe6J,UAAU;YAClD,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAG3D;YAEzC;;;;OAIC,GACD,IACEyD,QAAQxC,GAAG,CAAC2C,QAAQ,CAAC,oBACrBH,QAAQxC,GAAG,CAAC2C,QAAQ,CAAC,qCACrBH,QAAQxC,GAAG,CAAC2C,QAAQ,CAAC,2BACrBH,QAAQxC,GAAG,CAAC2C,QAAQ,CAAC,6BACrB;gBACA,OAAO;oBAAEC,UAAU;gBAAM;YAC3B;YAEAH,SAASI,UAAU,GAAG;YACtB,MAAM,IAAI,CAACC,WAAW,CAACpB,KAAKc,SAASC,UAAUC,UAAUvE,QAAQ;YACjE,OAAO;gBAAEyE,UAAU;YAAK;QAC1B;IACF;IAEA,MAAMG,gBAAgBhE,MAQrB,EAAE;QACD,IAAI;YACF,OAAO,KAAK,CAACgE,gBAAgB;gBAC3B,GAAGhE,MAAM;gBACTiE,SAAS,CAACtB,MAAQ,IAAI,CAACD,yBAAyB,CAACC,KAAK;gBACtDS,WAAW,CAACC;oBACV,IAAI,CAACX,yBAAyB,CAACW,MAAM;gBACvC;YACF;QACF,EAAE,OAAOxD,OAAO;YACd,IAAIA,iBAAiBpF,aAAa;gBAChC,MAAMoF;YACR;YACA,IAAI,CAAC6C,yBAAyB,CAAC7C,OAAO;YACtC,MAAM8C,MAAM9H,eAAegF;YAC3B,MAAM,EAAEqE,GAAG,EAAEC,GAAG,EAAEpD,IAAI,EAAE,GAAGf;YAE3BmE,IAAIL,UAAU,GAAG;YACjB,MAAM,IAAI,CAACC,WAAW,CAACpB,KAAKuB,KAAKC,KAAKpD;YACtC,OAAO;QACT;IACF;IAEOqD,oBAAwC;QAC7C,MAAMC,UAAU,KAAK,CAACD;QAEtB,OAAO,CAACF,KAAKC,KAAKR;YAChB,MAAMF,UAAU,IAAI,CAACa,YAAY,CAACJ;YAClC,MAAMR,WAAW,IAAI,CAACa,YAAY,CAACJ;YACnC,MAAMK,gBAAgB,IAAI,CAACrH,UAAU,CAACsH,OAAO;YAE7C,IAAID,kBAAkB,OAAO;gBAC3B,MAAME,QAAQC,KAAKC,GAAG;gBACtB,MAAMC,sBAAsB9L,eAAemL,KAAK;gBAEhD,IAAI,CAACW,qBAAqB;oBACxBnB,SAASoB,gBAAgB,CAACC,IAAI,CAAC,SAAS;wBACtC,oEAAoE;wBACpE,sEAAsE;wBACtE,gCAAgC;wBAChC,MAAMC,aAAajM,eAAemL,KAAKtD,KAAK;wBAE5C,IAAI,CAACoE,YAAY;4BACf;wBACF;wBAEAjJ,YAAY;4BACV0H;4BACAC;4BACAc;4BACAS,qBAAqBN,KAAKC,GAAG,KAAKF;wBACpC;oBACF;gBACF;YACF;YAEA,OAAOL,QAAQZ,SAASC,UAAUC;QACpC;IACF;IAEA,MAAauB,cACXhB,GAAoB,EACpBC,GAAqB,EACrBR,SAAkC,EACnB;QACf,MAAMwB,OAAO/K,MAAM,kBAAkBoC,WAAW;YAAEyE,KAAKiD,IAAIjD,GAAG;QAAC;QAC/D,MAAMxB,SAAS,MAAM0F,KAAKC,YAAY,CAAC;gBAC/B;YAAN,QAAM,cAAA,IAAI,CAAChH,KAAK,qBAAV,YAAYiH,OAAO;YACzBvM,eAAeoL,KAAK,mBAAmB,IAAI,CAAC3F,UAAU,CAACC,UAAU;YACjE,OAAO,MAAM,KAAK,CAAC0G,cAAchB,KAAKC,KAAKR;QAC7C;QACA,MAAM2B,cAAc9H,QAAQ8H,WAAW;QACvCH,KACGI,UAAU,CAAC,gBAAgB;YAC1BtE,KAAKiD,IAAIjD,GAAG;YACZ,cAAcuE,OAAOF,YAAYG,GAAG;YACpC,mBAAmBD,OAAOF,YAAYI,QAAQ;YAC9C,oBAAoBF,OAAOF,YAAYK,SAAS;QAClD,GACCC,IAAI;QACP,OAAOnG;IACT;IAEA,MAAMoG,IACJ3B,GAAoB,EACpBC,GAAqB,EACrBR,SAA6B,EACd;YACT;QAAN,QAAM,cAAA,IAAI,CAACvF,KAAK,qBAAV,YAAYiH,OAAO;QAEzB,MAAM,EAAES,QAAQ,EAAE,GAAG,IAAI,CAAC3I,UAAU;QACpC,IAAI4I,mBAAkC;QAEtC,gDAAgD;QAChD,IAAID,YAAY9L,cAAc2J,UAAUvE,QAAQ,IAAI,KAAK0G,WAAW;YAClE,6CAA6C;YAC7C,uGAAuG;YACvGC,mBAAmBpC,UAAUvE,QAAQ;YACrCuE,UAAUvE,QAAQ,GAAGnF,iBAAiB0J,UAAUvE,QAAQ,IAAI,KAAK0G;QACnE;QAEA,MAAM,EAAE1G,QAAQ,EAAE,GAAGuE;QAErB,IAAIvE,SAAU0C,UAAU,CAAC,WAAW;YAClC,IAAI7I,GAAG+M,UAAU,CAAC5M,SAAS,IAAI,CAAC6M,SAAS,EAAE,WAAW;gBACpD,MAAM,qBAAyC,CAAzC,IAAIhI,MAAM3E,iCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;QACF;QAEA,IAAIyM,kBAAkB;YACpB,oFAAoF;YACpF,mDAAmD;YACnDpC,UAAUvE,QAAQ,GAAG2G;QACvB;QACA,IAAI;YACF,OAAO,MAAM,KAAK,CAACF,IAAI3B,KAAKC,KAAKR;QACnC,EAAE,OAAO9D,OAAO;YACd,MAAM8C,MAAM9H,eAAegF;YAC3B9E,kBAAkB4H;YAClB,IAAI,CAACD,yBAAyB,CAACC;YAC/B,IAAI,CAACwB,IAAI+B,IAAI,EAAE;gBACb/B,IAAIL,UAAU,GAAG;gBACjB,IAAI;oBACF,OAAO,MAAM,IAAI,CAACC,WAAW,CAACpB,KAAKuB,KAAKC,KAAK/E,UAAW;wBACtD+G,aAAa,AAACvL,QAAQ+H,QAAQA,IAAI5B,IAAI,IAAK3B,YAAY;oBACzD;gBACF,EAAE,OAAOgH,aAAa;oBACpBtD,QAAQjD,KAAK,CAACuG;oBACdjC,IAAIkC,IAAI,CAAC,yBAAyBC,IAAI;gBACxC;YACF;QACF;IACF;IAEU5D,0BACRC,GAAa,EACb4D,IAAyE,EACnE;QACN,IAAI,CAAClI,cAAc,CAACqE,yBAAyB,CAACC,KAAK4D;IACrD;IAEUC,mBAA8C;QACtD,OACEnL,mBAAmBoB,OAAO,CACxBrD,SAAS,IAAI,CAACqN,aAAa,EAAEhN,oBAC1B+C;IAET;IAEUkK,sBAAiD;QACzD,IAAI,CAAC,IAAI,CAACC,kBAAkB,CAACC,GAAG,EAAE,OAAOpK;QAEzC,OACEnB,mBAAmBoB,OAAO,CACxBrD,SAAS,IAAI,CAACqN,aAAa,EAAE/M,wBAC1B8C;IAET;IAEU+F,+BAAyC;QACjD,MAAMsE,WAAWjL,mCACfkL,OAAOC,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,CAAC,IACnC,IAAI,CAAC7J,UAAU,CAAC2I,QAAQ,EACxBmB,GAAG,CAAC,CAACC,QAAU,IAAI5F,OAAOzF,iBAAiB,WAAWqL,OAAOC,KAAK;QAEpE,IAAI,IAAI,CAAChK,UAAU,CAACiK,MAAM,KAAK,YAAYP,SAASnI,MAAM,GAAG,GAAG;YAC9D/D,IAAIkF,KAAK,CACP;YAGFrC,QAAQ6J,IAAI,CAAC;QACf;QAEA,OAAOR,YAAY,EAAE;IACvB;IAEA,MAAgBS,gBAAgB;YAG1B;QAFJ,gCAAgC;QAChC,iCAAiC;QACjC,IAAI,EAAA,mBAAA,IAAI,CAACC,UAAU,qBAAf,iBAAiB3G,KAAK,MAAK,MAAM;YACnC,IAAI,CAAC2G,UAAU,CAAC3G,KAAK,GAAGnF,0BACtB,IAAI,CAAC8L,UAAU,CAACrG,QAAQ,IAAI,EAAE;QAElC;QACA,OAAO,IAAI,CAACqG,UAAU;IACxB;IAEUC,sBAAsB;QAC9B,OAAOhL;IACT;IAEA,MAAgBiL,gBAAkC;QAChD,OAAO,IAAI,CAAC7E,OAAO,CAAC,IAAI,CAAC8E,oBAAoB;IAC/C;IAEA,MAAgBC,iBAAiB1G,GAAW,EAAE;QAC5C,OAAO,IAAI,CAACJ,UAAU,CAAC;YACrBE,MAAM,IAAI,CAAC2G,oBAAoB;YAC/B1G,YAAY;YACZF,YAAYtE;YACZyE;QACF;IACF;IAEA,MAAgB2G,4BAA0C;QACxD,IAAIC;QACJ,IACE,IAAI,CAACC,6BAA6B,IACjC,MAAM,IAAI,CAACjH,UAAU,CAAC;YACrBE,MAAM,IAAI,CAAC+G,6BAA6B;YACxC9G,YAAY;YACZF,YAAYtE;QACd,GACGuG,IAAI,CAAC,IAAM,MACXQ,KAAK,CAAC,IAAM,QACf;YACA,IAAI;gBACFsE,wBAAwB,MAAM1L,yBAC5B,IAAI,CAACkE,GAAG,EACR,IAAI,CAAClD,UAAU,CAACgF,OAAO;YAE3B,EAAE,OAAOQ,KAAU;gBACjBA,IAAIoF,OAAO,GAAG,CAAC,sDAAsD,EAAEpF,IAAIoF,OAAO,EAAE;gBACpF,MAAMpF;YACR;QACF;QACA,OAAOkF;IACT;IAEA,MAAgBG,oCAAoC;QAClD,MAAM9L,gCAAgC,IAAI,CAACmE,GAAG,EAAE,IAAI,CAAClD,UAAU,CAACgF,OAAO;IACzE;IAEA,MAAgB8F,mBAAmB,EACjClH,IAAI,EACJmH,QAAQ,EACRjH,GAAG,EAKJ,EAAE;QACD,OAAO,IAAI,CAACJ,UAAU,CAAC;YACrBE;YACAmH;YACAlH,YAAY;YACZF,YAAYtE;YACZyE;QACF;IACF;IAEAkH,eAAeC,IAAc,EAAE;IAC7B,0FAA0F;IAC1F,uFAAuF;IACvF,mBAAmB;IACnB,sDAAsD;IACtD,mBAAmB;IACnB,wCAAwC;IACxC,sCAAsC;IACtC,+DAA+D;IAC/D,0CAA0C;IAC1C,eAAe;IACf,wBAAwB;IACxB,QAAQ;IACR,OAAO;IACP,KAAK;IACP;IAEAlI,4BACEf,IAAY,EACZkJ,KAAkD,EACzC;QACT,IAAIA,MAAMtI,IAAI,KAAK,yBAAyB;YAC1C,OAAO;QACT;QAEA,MAAMuI,gBAAgBnJ,KAAKoJ,KAAK,CAAC;QAEjC,IAAIC;QACJ,IACE,CAAEA,CAAAA,UAAUrJ,KAAKoJ,KAAK,CAAC,KAAK,CAACF,MAAMI,IAAI,GAAG,EAAE,AAAD,KAC3C,CAAED,CAAAA,UAAUA,QAAQE,SAAS,CAACL,MAAMM,GAAG,CAAA,GACvC;YACA,OAAO;QACT;QAEAH,UAAUA,UAAUF,cAAcM,KAAK,CAACP,MAAMI,IAAI,EAAEtP,IAAI,CAAC;QACzDqP,UAAUA,QAAQE,SAAS,CAAC,GAAGF,QAAQK,OAAO,CAAC;QAE/C,OAAO,CAACL,QAAQ5E,QAAQ,CAAC;IAC3B;IAEA,MAAgBkF,eAAe,EAC7B1J,QAAQ,EACR2J,WAAW,EACXC,cAAc,EACdjI,IAAI,EACJkI,SAAS,EAOV,EAIE;QACD,mDAAmD;QACnD,wDAAwD;QAExD,MAAMC,mBAAmB;YACvB,MAAM,EACJC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EACjB,GAAG,IAAI,CAACnM,UAAU;YACnB,MAAM,EAAEoM,OAAO,EAAEC,aAAa,EAAE,GAAG,IAAI,CAACrM,UAAU,CAACsM,IAAI,IAAI,CAAC;YAC5D,MAAMC,oBAAoB,IAAI,CAAC7M,oBAAoB;YAEnD,IAAI;oBA6BoB;gBA5BtB,MAAM8M,cAAc,MAAMD,kBAAkBE,eAAe,CAAC;oBAC1DvJ,KAAK,IAAI,CAACA,GAAG;oBACb8B,SAAS,IAAI,CAACA,OAAO;oBACrB/C;oBACAyK,QAAQ;wBACNC,WAAW,IAAI,CAAC3M,UAAU,CAACC,YAAY,CAAC2M,GAAG;wBAC3CZ;wBACAC;wBACAC;wBACAW,iBAAiBhH,QACf,IAAI,CAAC7F,UAAU,CAACC,YAAY,CAAC4M,eAAe;oBAEhD;oBACAV;oBACAC;oBACAC;oBACAzI;oBACAkI;oBACAD;oBACAiB,cAAc,IAAI,CAAC9M,UAAU,CAAC8M,YAAY;oBAC1CC,eAAe,IAAI,CAAC/M,UAAU,CAACC,YAAY,CAAC8M,aAAa;oBACzDC,mBAAmB,IAAI,CAAChN,UAAU,CAACC,YAAY,CAACgN,SAAS;oBACzDC,qBAAqB,IAAI,CAAClN,UAAU,CAACC,YAAY,CAACiN,mBAAmB;oBACrEC,gBAAgB,IAAI,CAACnN,UAAU,CAACC,YAAY,CAACkN,cAAc;oBAC3DC,oBAAoB,IAAI,CAACpN,UAAU,CAACoD,kBAAkB;oBACtDiK,kBAAkB,IAAI,CAACrN,UAAU,CAACiK,MAAM;oBACxCqD,SAAS,IAAI,CAACA,OAAO;oBACrBC,gBAAgB1H,QAAQ,IAAI,CAAC7F,UAAU,CAACC,YAAY,CAACsN,cAAc;oBACnEC,YAAY3H,SAAQ,oCAAA,IAAI,CAAC7F,UAAU,CAACC,YAAY,CAACwN,GAAG,qBAAhC,kCAAkCC,SAAS;gBACjE;gBACA,OAAOlB;YACT,SAAU;gBACR,kDAAkD;gBAClDD,kBAAkBoB,GAAG;YACvB;QACF;QACA,MAAMrL,SAAS,IAAI,CAAChB,gBAAgB,CAACsM,GAAG,CAAC3L;QAEzC,MAAM4L,aAAazQ,oBAAoB2O,kBACrC,CAAC,YAAY,EAAE9J,UAAU,EACzB,EAAE,EAED2D,IAAI,CAAC,OAAOoB;gBA4CTA;YA3CF,MAAM,EAAE8G,iBAAiB,EAAEC,cAAcC,QAAQ,EAAE,GAAGhH,IAAIxF,KAAK;YAE/D,IAAIsK,WAAW;gBACb,IAAI,IAAI,CAAC9L,UAAU,CAACiK,MAAM,KAAK,UAAU;oBACvC,IAAI,CAAC6D,mBAAmB;wBACtB,MAAM,qBAEL,CAFK,IAAIhN,MACR,CAAC,MAAM,EAAE8C,KAAK,wGAAwG,CAAC,GADnH,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IACE,CAACkK,kBAAkBG,IAAI,CAAC,CAACC,OAASA,KAAKjM,QAAQ,KAAK2J,cACpD;wBACA,MAAM,qBAEL,CAFK,IAAI9K,MACR,CAAC,MAAM,EAAE8C,KAAK,oBAAoB,EAAE3B,SAAS,8EAA8E,CAAC,GADxH,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;YACF;YAEA,IAAI,CAAC6J,aAAa,IAAI,CAAC9L,UAAU,CAACiK,MAAM,KAAK,UAAU;gBACrD,IAAI+D,aAAanP,aAAasP,sBAAsB,EAAE;oBACpD,MAAM,qBAEL,CAFK,IAAIrN,MACR,oKADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO,IAAIkN,aAAanP,aAAauP,SAAS,EAAE;oBAC9C,MAAM,qBAEL,CAFK,IAAItN,MACR,gKADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,MAAMU,QAIF;gBACFG,WAAW,EAAEmM,qCAAAA,kBAAmBhE,GAAG,CAAC,CAACC,QAAUA,MAAM9H,QAAQ;gBAC7D6L;gBACAC,cAAcC;YAChB;YAEA,IACEhH,EAAAA,aAAAA,IAAIxF,KAAK,qBAATwF,WAAW+G,YAAY,MAAK1O,aAC5B,iDAAiD;YACjD,qBAAqB;YACpB,CAAA,CAACyM,aAAcgC,qBAAqBA,kBAAkBvM,MAAM,GAAG,CAAC,GACjE;gBACA,oDAAoD;gBACpD,8CAA8C;gBAC9C,MAAM8M,sBAAsB,MAAMvS,GAAGwS,QAAQ,CAACC,QAAQ,CACpDtS,SAAS,IAAI,CAAC+I,OAAO,EAAEvI,qBACvB;gBAEF,MAAM+R,mBACJ/M,KAAKgN,KAAK,CAACJ;gBACb,KAAK,MAAMK,cAAclN,MAAMG,WAAW,IAAI,EAAE,CAAE;oBAChD6M,iBAAiBG,MAAM,CAACD,WAAW,GAAG,CAAC;gBACzC;gBACAF,iBAAiBI,aAAa,CAAC3M,SAAS,GAAG;oBACzC4M,WAAW;oBACXC,gBAAgB;oBAChBd,UAAUlP,4BAA4BkI,IAAIxF,KAAK,CAACuM,YAAY,EAAEnK;oBAC9DmL,oBAAoB;oBACpBC,gBAAgB3P;oBAChB4P,iBAAiB5P;oBACjB6P,gBAAgB7P;oBAChB8P,oBAAoB9P;oBACpB+P,qBAAqBnN;oBACrBoN,mBAAmBhQ;oBACnBiQ,wBAAwBjQ;oBACxBkQ,YAAYtQ,cAAcgD,UAAUuN,EAAE,CAACC,MAAM;oBAC7CC,iBAAiBrQ;oBACjBsQ,eAAetQ;oBACfuQ,aAAa,EAAE;gBACjB;gBAEA,MAAMC,kBAAkBpO,KAAKC,SAAS,CAAC8M;gBAEvC,IAAIqB,oBAAoBxB,qBAAqB;oBAC3C,MAAMvS,GAAGwS,QAAQ,CAACwB,SAAS,CACzB7T,SAAS,IAAI,CAAC+I,OAAO,EAAEvI,qBACvBoT;gBAEJ;YACF;YACA,IAAI,CAACvO,gBAAgB,CAACyO,GAAG,CAAC9N,UAAUT;YACpC,OAAOA;QACT,GACC4E,KAAK,CAAC,CAACZ;YACN,IAAI,CAAClE,gBAAgB,CAAC0O,MAAM,CAAC/N;YAC7B,IAAI,CAACK,QAAQ,MAAMkD;YACnBhI,IAAIkF,KAAK,CAAC,CAAC,oCAAoC,EAAET,SAAS,CAAC,CAAC;YAC5D0D,QAAQjD,KAAK,CAAC8C;QAChB;QAEF,IAAIlD,QAAQ;YACV,OAAOA;QACT;QACA,OAAOuL;IACT;IAEA,MAAgBnK,WAAWuM,IAM1B,EAAiB;QAChB,MAAM,IAAI,CAAC/O,cAAc,CAACwC,UAAU,CAACuM;IACvC;IAEA,MAAgBC,mBAAmB,EACjCC,MAAM,EACNvM,IAAI,EACJwM,KAAK,EACLvN,MAAM,EACNiJ,SAAS,EACTf,WAAW,IAAI,EACfsF,YAAY,EACZvM,GAAG,EAWJ,EAAwC;YACjC;QAAN,QAAM,cAAA,IAAI,CAAC7C,KAAK,qBAAV,YAAYiH,OAAO;QAEzB,MAAMoI,iBAAiB,MAAM,IAAI,CAACC,mBAAmB,CAAC3M;QACtD,IAAI0M,gBAAgB;YAClB,wDAAwD;YACxD,MAAM,IAAI3T,kBAAkB2T;QAC9B;QACA,IAAID,gBAAgB,IAAI,CAACG,aAAa,CAACC,YAAY,EAAE;YACnD,MAAM,IAAI,CAAC/M,UAAU,CAAC;gBACpBE;gBACAmH;gBACAlH,YAAY;gBACZF,YAAYtE;gBACZyE;YACF;QACF;QAEA,IAAI,CAAC4M,gBAAgB,GAAG,KAAK,CAACrG;QAE9B,OAAO,MAAM,KAAK,CAAC6F,mBAAmB;YACpCtM;YACAwM;YACAvN;YACAsN;YACArE;YACAuE;YACAvM;QACF;IACF;IAEA,MAAgB6M,2BACd7M,GAAY,EAC8B;QAC1C,MAAM,IAAI,CAAC5C,cAAc,CAACyP,0BAA0B,CAAC7M;QACrD,OAAO,MAAMzG,2BAA2B,IAAI,CAAC2H,OAAO;IACtD;IAEA,MAAMuL,oBAAoB3M,IAAY,EAAgB;QACpD,OAAO,MAAM,IAAI,CAAC1C,cAAc,CAACqP,mBAAmB,CAAC3M;IACvD;IAEA,MAAgBgN,8BACd,GAAGC,IAAqD,EACxD;QACA,MAAM,KAAK,CAACD,iCAAiCC;QAE7C,MAAMrL,MAAMqL,IAAI,CAAC,EAAE;QACnB,IAAI,CAACtL,yBAAyB,CAACC,KAAK;IACtC;AACF", "ignoreList": [0]}