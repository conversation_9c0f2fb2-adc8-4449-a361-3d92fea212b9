{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-invalid-import-error-loader.ts"], "sourcesContent": ["import type webpack from 'webpack'\n\nexport type InvalidImportLoaderOpts = { message: string }\n\nconst nextInvalidImportErrorLoader: webpack.LoaderDefinitionFunction<InvalidImportLoaderOpts> =\n  function () {\n    const { message } = this.getOptions()\n    throw new Error(message)\n  }\n\nexport default nextInvalidImportErrorLoader\n"], "names": ["nextInvalidImportErrorLoader", "message", "getOptions", "Error"], "mappings": "AAIA,MAAMA,+BACJ;IACE,MAAM,EAAEC,OAAO,EAAE,GAAG,IAAI,CAACC,UAAU;IACnC,MAAM,qBAAkB,CAAlB,IAAIC,MAAMF,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;AACzB;AAEF,eAAeD,6BAA4B", "ignoreList": [0]}