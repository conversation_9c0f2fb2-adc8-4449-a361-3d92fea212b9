{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/content/docs/test.mdx.js%3Fcollection%3Ddocs%26hash%3D1755743334004"], "sourcesContent": ["import {Fragment as _Fragment, jsx as _jsx, jsxs as _jsxs} from \"react/jsx-runtime\";\nexport let lastModified = undefined;\nexport let frontmatter = {\n  \"title\": \"Components\",\n  \"description\": \"Components\"\n};\nexport let structuredData = {\n  \"contents\": [],\n  \"headings\": [{\n    \"id\": \"code-block\",\n    \"content\": \"Code Block\"\n  }, {\n    \"id\": \"cards\",\n    \"content\": \"Cards\"\n  }]\n};\nexport const toc = [{\n  depth: 2,\n  url: \"#code-block\",\n  title: _jsx(_Fragment, {\n    children: \"Code Block\"\n  })\n}, {\n  depth: 2,\n  url: \"#cards\",\n  title: _jsx(_Fragment, {\n    children: \"Cards\"\n  })\n}];\nfunction _createMdxContent(props) {\n  const _components = {\n    code: \"code\",\n    h2: \"h2\",\n    pre: \"pre\",\n    span: \"span\",\n    ...props.components\n  }, {Card, Cards} = _components;\n  if (!Card) _missingMdxReference(\"Card\", true);\n  if (!Cards) _missingMdxReference(\"Cards\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.h2, {\n      id: \"code-block\",\n      children: \"Code Block\"\n    }), \"\\n\", _jsx(_Fragment, {\n      children: _jsx(_components.pre, {\n        className: \"shiki shiki-themes github-light github-dark\",\n        style: {\n          \"--shiki-light\": \"#24292e\",\n          \"--shiki-dark\": \"#e1e4e8\",\n          \"--shiki-light-bg\": \"#fff\",\n          \"--shiki-dark-bg\": \"#24292e\"\n        },\n        tabIndex: \"0\",\n        icon: \"<svg viewBox=\\\"0 0 24 24\\\"><path d=\\\"M0 0h24v24H0V0zm22.034 18.276c-.175-1.095-.888-2.015-3.003-2.873-.736-.345-1.554-.585-1.797-1.14-.091-.33-.105-.51-.046-.705.15-.646.915-.84 1.515-.66.39.12.75.42.976.9 1.034-.676 1.034-.676 1.755-1.125-.27-.42-.404-.601-.586-.78-.63-.705-1.469-1.065-2.834-1.034l-.705.089c-.676.165-1.32.525-1.71 1.005-1.14 1.291-.811 3.541.569 4.471 1.365 1.02 3.361 1.244 3.616 2.205.24 1.17-.87 1.545-1.966 1.41-.811-.18-1.26-.586-1.755-1.336l-1.83 1.051c.21.48.45.689.81 1.109 1.74 1.756 6.09 1.666 6.871-1.004.029-.09.24-.705.074-1.65l.046.067zm-8.983-7.245h-2.248c0 1.938-.009 3.864-.009 5.805 0 1.232.063 2.363-.138 2.711-.33.689-1.18.601-1.566.48-.396-.196-.597-.466-.83-.855-.063-.105-.11-.196-.127-.196l-1.825 1.125c.305.63.75 1.172 1.324 1.517.855.51 2.004.675 3.207.405.783-.226 1.458-.691 1.811-1.411.51-.93.402-2.07.397-3.346.012-2.054 0-4.109 0-6.179l.004-.056z\\\" fill=\\\"currentColor\\\" /></svg>\",\n        children: _jsx(_components.code, {\n          children: _jsxs(_components.span, {\n            className: \"line\",\n            children: [_jsx(_components.span, {\n              style: {\n                \"--shiki-light\": \"#24292E\",\n                \"--shiki-dark\": \"#E1E4E8\"\n              },\n              children: \"console.\"\n            }), _jsx(_components.span, {\n              style: {\n                \"--shiki-light\": \"#6F42C1\",\n                \"--shiki-dark\": \"#B392F0\"\n              },\n              children: \"log\"\n            }), _jsx(_components.span, {\n              style: {\n                \"--shiki-light\": \"#24292E\",\n                \"--shiki-dark\": \"#E1E4E8\"\n              },\n              children: \"(\"\n            }), _jsx(_components.span, {\n              style: {\n                \"--shiki-light\": \"#032F62\",\n                \"--shiki-dark\": \"#9ECBFF\"\n              },\n              children: \"'Hello World'\"\n            }), _jsx(_components.span, {\n              style: {\n                \"--shiki-light\": \"#24292E\",\n                \"--shiki-dark\": \"#E1E4E8\"\n              },\n              children: \");\"\n            })]\n          })\n        })\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"cards\",\n      children: \"Cards\"\n    }), \"\\n\", _jsxs(Cards, {\n      children: [_jsx(Card, {\n        title: \"Learn more about Next.js\",\n        href: \"https://nextjs.org/docs\"\n      }), _jsx(Card, {\n        title: \"Learn more about Fumadocs\",\n        href: \"https://fumadocs.vercel.app\"\n      })]\n    })]\n  });\n}\nexport default function MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = props.components || ({});\n  return MDXLayout ? _jsx(MDXLayout, {\n    ...props,\n    children: _jsx(_createMdxContent, {\n      ...props\n    })\n  }) : _createMdxContent(props);\n}\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,IAAI,eAAe;AACnB,IAAI,cAAc;IACvB,SAAS;IACT,eAAe;AACjB;AACO,IAAI,iBAAiB;IAC1B,YAAY,EAAE;IACd,YAAY;QAAC;YACX,MAAM;YACN,WAAW;QACb;QAAG;YACD,MAAM;YACN,WAAW;QACb;KAAE;AACJ;AACO,MAAM,MAAM;IAAC;QAClB,OAAO;QACP,KAAK;QACL,OAAO,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,yNAAA,CAAA,WAAS,EAAE;YACrB,UAAU;QACZ;IACF;IAAG;QACD,OAAO;QACP,KAAK;QACL,OAAO,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,yNAAA,CAAA,WAAS,EAAE;YACrB,UAAU;QACZ;IACF;CAAE;AACF,SAAS,kBAAkB,KAAK;IAC9B,MAAM,cAAc;QAClB,MAAM;QACN,IAAI;QACJ,KAAK;QACL,MAAM;QACN,GAAG,MAAM,UAAU;IACrB,GAAG,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG;IACnB,IAAI,CAAC,MAAM,qBAAqB,QAAQ;IACxC,IAAI,CAAC,OAAO,qBAAqB,SAAS;IAC1C,OAAO,CAAA,GAAA,yNAAA,CAAA,OAAK,AAAD,EAAE,yNAAA,CAAA,WAAS,EAAE;QACtB,UAAU;YAAC,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,EAAE,EAAE;gBAC9B,IAAI;gBACJ,UAAU;YACZ;YAAI;YAAM,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,yNAAA,CAAA,WAAS,EAAE;gBACxB,UAAU,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,GAAG,EAAE;oBAC9B,WAAW;oBACX,OAAO;wBACL,iBAAiB;wBACjB,gBAAgB;wBAChB,oBAAoB;wBACpB,mBAAmB;oBACrB;oBACA,UAAU;oBACV,MAAM;oBACN,UAAU,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,IAAI,EAAE;wBAC/B,UAAU,CAAA,GAAA,yNAAA,CAAA,OAAK,AAAD,EAAE,YAAY,IAAI,EAAE;4BAChC,WAAW;4BACX,UAAU;gCAAC,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,IAAI,EAAE;oCAChC,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;oCAClB;oCACA,UAAU;gCACZ;gCAAI,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,IAAI,EAAE;oCACzB,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;oCAClB;oCACA,UAAU;gCACZ;gCAAI,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,IAAI,EAAE;oCACzB,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;oCAClB;oCACA,UAAU;gCACZ;gCAAI,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,IAAI,EAAE;oCACzB,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;oCAClB;oCACA,UAAU;gCACZ;gCAAI,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,IAAI,EAAE;oCACzB,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB;oCAClB;oCACA,UAAU;gCACZ;6BAAG;wBACL;oBACF;gBACF;YACF;YAAI;YAAM,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,EAAE,EAAE;gBAC7B,IAAI;gBACJ,UAAU;YACZ;YAAI;YAAM,CAAA,GAAA,yNAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBACrB,UAAU;oBAAC,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,MAAM;wBACpB,OAAO;wBACP,MAAM;oBACR;oBAAI,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,MAAM;wBACb,OAAO;wBACP,MAAM;oBACR;iBAAG;YACL;SAAG;IACL;AACF;AACe,SAAS,WAAW,QAAQ,CAAC,CAAC;IAC3C,MAAM,EAAC,SAAS,SAAS,EAAC,GAAG,MAAM,UAAU,IAAK,CAAC;IACnD,OAAO,YAAY,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;QACjC,GAAG,KAAK;QACR,UAAU,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,mBAAmB;YAChC,GAAG,KAAK;QACV;IACF,KAAK,kBAAkB;AACzB;AACA,SAAS,qBAAqB,EAAE,EAAE,SAAS;IACzC,MAAM,IAAI,MAAM,cAAc,CAAC,YAAY,cAAc,QAAQ,IAAI,OAAO,KAAK;AACnF", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/content/docs/index.mdx.js%3Fcollection%3Ddocs%26hash%3D1755743334004"], "sourcesContent": ["import {Fragment as _Fragment, jsx as _jsx, jsxs as _jsxs} from \"react/jsx-runtime\";\nexport let lastModified = undefined;\nexport let frontmatter = {\n  \"title\": \"Hello World\",\n  \"description\": \"Your first document\"\n};\nexport let structuredData = {\n  \"contents\": [{\n    \"heading\": \"\",\n    \"content\": \"Welcome to the docs! You can start writing documents in /content/docs.\"\n  }],\n  \"headings\": [{\n    \"id\": \"what-is-next\",\n    \"content\": \"What is Next?\"\n  }]\n};\nexport const toc = [{\n  depth: 2,\n  url: \"#what-is-next\",\n  title: _jsx(_Fragment, {\n    children: \"What is Next?\"\n  })\n}];\nfunction _createMdxContent(props) {\n  const _components = {\n    code: \"code\",\n    h2: \"h2\",\n    p: \"p\",\n    ...props.components\n  }, {Card, Cards} = _components;\n  if (!Card) _missingMdxReference(\"Card\", true);\n  if (!Cards) _missingMdxReference(\"Cards\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsxs(_components.p, {\n      children: [\"Welcome to the docs! You can start writing documents in \", _jsx(_components.code, {\n        children: \"/content/docs\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"what-is-next\",\n      children: \"What is Next?\"\n    }), \"\\n\", _jsxs(Cards, {\n      children: [_jsx(Card, {\n        title: \"Learn more about Next.js\",\n        href: \"https://nextjs.org/docs\"\n      }), _jsx(Card, {\n        title: \"Learn more about Fumadocs\",\n        href: \"https://fumadocs.vercel.app\"\n      })]\n    })]\n  });\n}\nexport default function MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = props.components || ({});\n  return MDXLayout ? _jsx(MDXLayout, {\n    ...props,\n    children: _jsx(_createMdxContent, {\n      ...props\n    })\n  }) : _createMdxContent(props);\n}\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,IAAI,eAAe;AACnB,IAAI,cAAc;IACvB,SAAS;IACT,eAAe;AACjB;AACO,IAAI,iBAAiB;IAC1B,YAAY;QAAC;YACX,WAAW;YACX,WAAW;QACb;KAAE;IACF,YAAY;QAAC;YACX,MAAM;YACN,WAAW;QACb;KAAE;AACJ;AACO,MAAM,MAAM;IAAC;QAClB,OAAO;QACP,KAAK;QACL,OAAO,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,yNAAA,CAAA,WAAS,EAAE;YACrB,UAAU;QACZ;IACF;CAAE;AACF,SAAS,kBAAkB,KAAK;IAC9B,MAAM,cAAc;QAClB,MAAM;QACN,IAAI;QACJ,GAAG;QACH,GAAG,MAAM,UAAU;IACrB,GAAG,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG;IACnB,IAAI,CAAC,MAAM,qBAAqB,QAAQ;IACxC,IAAI,CAAC,OAAO,qBAAqB,SAAS;IAC1C,OAAO,CAAA,GAAA,yNAAA,CAAA,OAAK,AAAD,EAAE,yNAAA,CAAA,WAAS,EAAE;QACtB,UAAU;YAAC,CAAA,GAAA,yNAAA,CAAA,OAAK,AAAD,EAAE,YAAY,CAAC,EAAE;gBAC9B,UAAU;oBAAC;oBAA4D,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,IAAI,EAAE;wBAC5F,UAAU;oBACZ;oBAAI;iBAAI;YACV;YAAI;YAAM,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,EAAE,EAAE;gBAC7B,IAAI;gBACJ,UAAU;YACZ;YAAI;YAAM,CAAA,GAAA,yNAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBACrB,UAAU;oBAAC,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,MAAM;wBACpB,OAAO;wBACP,MAAM;oBACR;oBAAI,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,MAAM;wBACb,OAAO;wBACP,MAAM;oBACR;iBAAG;YACL;SAAG;IACL;AACF;AACe,SAAS,WAAW,QAAQ,CAAC,CAAC;IAC3C,MAAM,EAAC,SAAS,SAAS,EAAC,GAAG,MAAM,UAAU,IAAK,CAAC;IACnD,OAAO,YAAY,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;QACjC,GAAG,KAAK;QACR,UAAU,CAAA,GAAA,yNAAA,CAAA,MAAI,AAAD,EAAE,mBAAmB;YAChC,GAAG,KAAK;QACV;IACF,KAAK,kBAAkB;AACzB;AACA,SAAS,qBAAqB,EAAE,EAAE,SAAS;IACzC,MAAM,IAAI,MAAM,cAAc,CAAC,YAAY,cAAc,QAAQ,IAAI,OAAO,KAAK;AACnF", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/.source/index.ts"], "sourcesContent": ["// @ts-nocheck -- skip type checking\nimport * as docs_1 from \"../content/docs/test.mdx?collection=docs&hash=1755743334004\"\nimport * as docs_0 from \"../content/docs/index.mdx?collection=docs&hash=1755743334004\"\nimport { _runtime } from \"fumadocs-mdx\"\nimport * as _source from \"../source.config\"\nexport const docs = _runtime.docs<typeof _source.docs>([{ info: {\"path\":\"index.mdx\",\"absolutePath\":\"C:/Users/<USER>/Desktop/test-dosc/my-app/content/docs/index.mdx\"}, data: docs_0 }, { info: {\"path\":\"test.mdx\",\"absolutePath\":\"C:/Users/<USER>/Desktop/test-dosc/my-app/content/docs/test.mdx\"}, data: docs_1 }], [])"], "names": [], "mappings": "AAAA,oCAAoC;;;;AACpC;AACA;AACA;AAAA;;;;AAEO,MAAM,OAAO,8JAAA,CAAA,WAAQ,CAAC,IAAI,CAAsB;IAAC;QAAE,MAAM;YAAC,QAAO;YAAY,gBAAe;QAAsE;QAAG,MAAM;IAAO;IAAG;QAAE,MAAM;YAAC,QAAO;YAAW,gBAAe;QAAqE;QAAG,MAAM;IAAO;CAAE,EAAE,EAAE", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/lib/source.ts"], "sourcesContent": ["import { docs } from '@/.source';\nimport { loader } from 'fumadocs-core/source';\n\n// See https://fumadocs.vercel.app/docs/headless/source-api for more info\nexport const source = loader({\n  // it assigns a URL to your pages\n  baseUrl: '/docs',\n  source: docs.toFumadocsSource(),\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAGO,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,iCAAiC;IACjC,SAAS;IACT,QAAQ,mHAAA,CAAA,OAAI,CAAC,gBAAgB;AAC/B", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/app/api/search/route.ts"], "sourcesContent": ["import { source } from '@/lib/source';\nimport { createFromSource } from 'fumadocs-core/search/server';\n\n// 缓存搜索索引以支持静态导出\nexport const revalidate = false;\n\nconst searchHandler = createFromSource(source, {\n  // https://docs.orama.com/docs/orama-js/supported-languages\n  language: 'english',\n});\n\n// 在开发模式下使用 GET，在生产模式下使用 staticGET\nexport const GET = process.env.NODE_ENV === 'development'\n  ? searchHandler.GET\n  : searchHandler.staticGET;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAGO,MAAM,aAAa;AAE1B,MAAM,gBAAgB,CAAA,GAAA,8KAAA,CAAA,mBAAgB,AAAD,EAAE,+GAAA,CAAA,SAAM,EAAE;IAC7C,2DAA2D;IAC3D,UAAU;AACZ;AAGO,MAAM,MAAM,uCACf,cAAc,GAAG,GACjB", "debugId": null}}]}