<!DOCTYPE html><!--_UfTfY9npJwVLAeBnUpXo--><html lang="en" class="__className_e8ce0c"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/97e78c7008a781cb.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-8845dc663ed01e54.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-38db4bd4892fef52.js" async=""></script><script src="/_next/static/chunks/main-app-1c694ee390b4c5ff.js" async=""></script><script src="/_next/static/chunks/407-0a1678b22623f2df.js" async=""></script><script src="/_next/static/chunks/607-decfe929aa00677c.js" async=""></script><script src="/_next/static/chunks/874-437a265a67d6cfee.js" async=""></script><script src="/_next/static/chunks/379-50e981a99501adfb.js" async=""></script><script src="/_next/static/chunks/app/layout-417276e6d105b1a1.js" async=""></script><script src="/_next/static/chunks/244-904cb1778dd50666.js" async=""></script><script src="/_next/static/chunks/811-792768cab0bb19ab.js" async=""></script><script src="/_next/static/chunks/app/docs/layout-56b992df650dde51.js" async=""></script><script src="/_next/static/chunks/328-a0d4f8e3eb6521f3.js" async=""></script><script src="/_next/static/chunks/475-f4ab7565c1dc7718.js" async=""></script><script src="/_next/static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js" async=""></script><title>Components</title><meta name="description" content="Components"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="flex flex-col min-h-screen"><div hidden=""><!--$--><!--/$--></div><script>((a,b,c,d,e,f,g,h)=>{let i=document.documentElement,j=["light","dark"];function k(b){var c;(Array.isArray(a)?a:[a]).forEach(a=>{let c="class"===a,d=c&&f?e.map(a=>f[a]||a):e;c?(i.classList.remove(...d),i.classList.add(f&&f[b]?f[b]:b)):i.setAttribute(a,b)}),c=b,h&&j.includes(c)&&(i.style.colorScheme=c)}if(d)k(d);else try{let a=localStorage.getItem(b)||c,d=g&&"system"===a?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":a;k(d)}catch(a){}})("class","theme","system",null,["light","dark"],null,true,true)</script><header id="nd-subnav" class="fixed top-(--fd-banner-height) inset-x-0 z-30 flex items-center ps-4 pe-2.5 border-b transition-colors backdrop-blur-sm bg-fd-background/80 h-14 md:hidden"><a class="inline-flex items-center gap-2.5 font-semibold" href="/"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" aria-label="Logo"><circle cx="12" cy="12" r="12" fill="currentColor"></circle></svg>My App</a><div class="flex-1"></div><button type="button" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground [&amp;_svg]:size-4.5 p-2" data-search="" aria-label="Open Search"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg></button><button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground [&amp;_svg]:size-4.5 p-2" aria-label="Open Sidebar"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M9 3v18"></path></svg></button></header><main id="nd-docs-layout" class="flex flex-1 flex-col pt-(--fd-nav-height) transition-[padding] xl:[--fd-toc-width:286px] md:[--fd-sidebar-width:268px] lg:[--fd-sidebar-width:286px] [--fd-nav-height:56px] md:[--fd-nav-height:0px]" style="padding-inline-start:calc(var(--fd-sidebar-width) + var(--fd-layout-offset));padding-inline-end:var(--fd-layout-offset)"><div class="fixed flex shadow-lg transition-opacity rounded-xl p-0.5 border bg-fd-muted text-fd-muted-foreground z-10 max-md:hidden xl:start-4 max-xl:end-4 pointer-events-none opacity-0" style="top:calc(var(--fd-banner-height) + var(--fd-tocnav-height) + var(--spacing) * 4)"><button type="button" aria-label="Collapse Sidebar" data-collapsed="false" class="inline-flex items-center justify-center text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground p-1.5 [&amp;_svg]:size-4.5 rounded-lg"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M9 3v18"></path></svg></button><button type="button" class="inline-flex items-center justify-center text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground p-1.5 [&amp;_svg]:size-4.5 rounded-lg" data-search="" aria-label="Open Search"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg></button></div><aside id="nd-sidebar" data-collapsed="false" class="fixed start-0 flex flex-col items-end top-(--fd-sidebar-top) bottom-(--fd-sidebar-margin) z-20 bg-fd-card text-sm border-e max-md:hidden *:w-(--fd-sidebar-width)" style="transition:top ease 250ms, opacity ease 250ms, translate ease 250ms, width ease 250ms;--fd-sidebar-offset:calc(16px - 100%);--fd-sidebar-margin:0px;--fd-sidebar-top:calc(var(--fd-banner-height) + var(--fd-nav-height) + var(--fd-sidebar-margin));width:calc(var(--fd-sidebar-width) + var(--fd-layout-offset))"><div class="flex flex-col gap-3 p-4 pb-2"><div class="flex"><a class="inline-flex text-[15px] items-center gap-2.5 font-medium me-auto" href="/"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" aria-label="Logo"><circle cx="12" cy="12" r="12" fill="currentColor"></circle></svg>My App</a><button type="button" aria-label="Collapse Sidebar" data-collapsed="false" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground p-1.5 [&amp;_svg]:size-4.5 mb-auto text-fd-muted-foreground"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M9 3v18"></path></svg></button></div><button type="button" data-search-full="" class="inline-flex items-center gap-2 rounded-lg border bg-fd-secondary/50 p-1.5 ps-2 text-sm text-fd-muted-foreground transition-colors hover:bg-fd-accent hover:text-fd-accent-foreground"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide size-4"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>Search<div class="ms-auto inline-flex gap-0.5"><kbd class="rounded-md border bg-fd-background px-1.5">⌘</kbd><kbd class="rounded-md border bg-fd-background px-1.5">K</kbd></div></button></div><div dir="ltr" class="overflow-hidden h-full" style="position:relative;--radix-scroll-area-corner-width:0px;--radix-scroll-area-corner-height:0px"><style>[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}</style><div data-radix-scroll-area-viewport="" class="size-full rounded-[inherit] p-4" style="overflow-x:hidden;overflow-y:hidden;--sidebar-item-offset:calc(var(--spacing) * 2);mask-image:linear-gradient(to bottom, transparent, white 12px, white calc(100% - 12px), transparent)"><div style="min-width:100%;display:table"><a data-active="false" class="relative flex flex-row items-center gap-2 rounded-xl p-2 ps-(--sidebar-item-offset) text-start text-fd-muted-foreground [overflow-wrap:anywhere] [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 transition-colors hover:bg-fd-accent/50 hover:text-fd-accent-foreground/80 hover:transition-none" href="/docs/">Hello World</a><a data-active="true" class="relative flex flex-row items-center gap-2 rounded-xl p-2 ps-(--sidebar-item-offset) text-start [overflow-wrap:anywhere] [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-fd-primary/10 text-fd-primary" href="/docs/test/">Components</a></div></div></div><div data-fd-if-empty="_R_3j9flb_" class="flex flex-col border-t px-4 py-3"><div class="flex text-fd-muted-foreground items-center justify-end empty:hidden"><button class="inline-flex items-center rounded-full border p-0" aria-label="Toggle Theme" data-theme-toggle=""><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide size-6.5 rounded-full p-1.5 text-fd-muted-foreground"><circle cx="12" cy="12" r="4"></circle><path d="M12 2v2"></path><path d="M12 20v2"></path><path d="m4.93 4.93 1.41 1.41"></path><path d="m17.66 17.66 1.41 1.41"></path><path d="M2 12h2"></path><path d="M20 12h2"></path><path d="m6.34 17.66-1.41 1.41"></path><path d="m19.07 4.93-1.41 1.41"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide size-6.5 rounded-full p-1.5 text-fd-muted-foreground"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path></svg></button></div></div><script>{function g(a){return document.querySelector(`[data-fd-if-empty="${a}"]`)};function h(a){for(let b=0;b<a.childNodes.length;b++){let c=a.childNodes.item(b);if(c.nodeType===Node.TEXT_NODE||c.nodeType===Node.ELEMENT_NODE&&"none"!==window.getComputedStyle(c).display)return!1}return!0};(a=>{let b=g(a);b&&(b.hidden=h(b));let c=document.currentScript;c&&c.parentNode?.removeChild(c)})("_R_3j9flb_")}</script></aside><div id="nd-page" class="flex flex-1 w-full mx-auto max-w-(--fd-page-width) pt-(--fd-tocnav-height)"><header id="nd-tocnav" class="fixed inset-x-0 z-10 border-b backdrop-blur-sm transition-colors xl:hidden bg-fd-background/80" style="top:calc(var(--fd-banner-height) + var(--fd-nav-height));inset-inline-start:calc(var(--fd-sidebar-width) + var(--fd-layout-offset))" data-state="closed"><button type="button" aria-controls="radix-_R_2lubt9flb_" aria-expanded="false" data-state="closed" class="flex w-full h-(--fd-tocnav-height) items-center text-sm text-fd-muted-foreground gap-2.5 px-4 py-2.5 text-start focus-visible:outline-none [&amp;_svg]:size-4 md:px-6"><svg role="progressbar" viewBox="0 0 24 24" aria-valuenow="0" aria-valuemin="0" aria-valuemax="1" class="shrink-0"><circle cx="12" cy="12" r="11" fill="none" stroke-width="2" class="stroke-current/25"></circle><circle cx="12" cy="12" r="11" fill="none" stroke-width="2" stroke="currentColor" stroke-dasharray="69.11503837897544" stroke-dashoffset="69.11503837897544" stroke-linecap="round" transform="rotate(-90 12 12)" class="transition-all"></circle></svg><span class="grid flex-1 *:my-auto *:row-start-1 *:col-start-1"><span class="truncate transition-all">Components</span><span class="truncate transition-all opacity-0 translate-y-full pointer-events-none"></span></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide shrink-0 transition-transform mx-0.5"><path d="m6 9 6 6 6-6"></path></svg></button><div data-state="closed" id="radix-_R_2lubt9flb_" hidden="" data-toc-popover="" class="overflow-hidden flex flex-col px-4 max-h-[50vh] md:px-6"></div></header><article class="flex min-w-0 w-full flex-col gap-4 px-4 pt-8 md:px-6 md:mx-auto xl:pt-12 xl:px-12"><h1 class="text-3xl font-semibold">Components</h1><p class="mb-8 text-lg text-fd-muted-foreground">Components</p><div class="prose"><h2 class="flex scroll-m-28 flex-row items-center gap-2" id="code-block"><a data-card="" href="#code-block" class="peer">Code Block</a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide size-3.5 shrink-0 text-fd-muted-foreground opacity-0 transition-opacity peer-hover:opacity-100" aria-label="Link to section"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg></h2>
<figure dir="ltr" class="my-4 rounded-xl bg-fd-card p-1 shiki relative border outline-none not-prose overflow-hidden text-sm shiki shiki-themes github-light github-dark" style="--shiki-light:#24292e;--shiki-dark:#e1e4e8;--shiki-light-bg:#fff;--shiki-dark-bg:#24292e" tabindex="0"><div class="empty:hidden absolute top-1 right-1 z-2 bg-fd-card rounded-bl-lg border-l border-b text-fd-muted-foreground"><button type="button" class="inline-flex items-center justify-center rounded-md p-2 text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground [&amp;_svg]:size-3.5" aria-label="Copy Text"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><div class="bg-fd-secondary rounded-lg border text-[13px] py-3.5 overflow-auto max-h-[600px] fd-scroll-container" style="--padding-right:calc(var(--spacing) * 8)"><pre class="min-w-full w-max *:flex *:flex-col"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">console.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0">log</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF">&#x27;Hello World&#x27;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">);</span></span></code></pre></div></figure>
<h2 class="flex scroll-m-28 flex-row items-center gap-2" id="cards"><a data-card="" href="#cards" class="peer">Cards</a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide size-3.5 shrink-0 text-fd-muted-foreground opacity-0 transition-opacity peer-hover:opacity-100" aria-label="Link to section"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg></h2>
<div class="grid grid-cols-2 gap-3 @container"><a href="https://nextjs.org/docs" rel="noreferrer noopener" target="_blank" data-card="true" class="block rounded-xl border bg-fd-card p-4 text-fd-card-foreground transition-colors @max-lg:col-span-full hover:bg-fd-accent/80"><h3 class="not-prose mb-1 text-sm font-medium">Learn more about Next.js</h3><div class="text-sm text-fd-muted-foreground prose-no-margin empty:hidden"></div></a><a href="https://fumadocs.vercel.app" rel="noreferrer noopener" target="_blank" data-card="true" class="block rounded-xl border bg-fd-card p-4 text-fd-card-foreground transition-colors @max-lg:col-span-full hover:bg-fd-accent/80"><h3 class="not-prose mb-1 text-sm font-medium">Learn more about Fumadocs</h3><div class="text-sm text-fd-muted-foreground prose-no-margin empty:hidden"></div></a></div></div><div role="none" class="flex-1"></div><div class="flex flex-row flex-wrap items-center justify-between gap-4 empty:hidden"></div><div class="@container grid gap-4 pb-6 grid-cols-1"><a class="flex flex-col gap-2 rounded-lg border p-4 text-sm transition-colors hover:bg-fd-accent/80 hover:text-fd-accent-foreground @max-lg:col-span-full" href="/docs/"><div class="inline-flex items-center gap-1.5 font-medium"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide -mx-1 size-4 shrink-0 rtl:rotate-180"><path d="m15 18-6-6 6-6"></path></svg><p>Hello World</p></div><p class="text-fd-muted-foreground truncate">Your first document</p></a></div></article><div id="nd-toc" class="sticky pb-2 pt-12 max-xl:hidden" style="top:calc(var(--fd-banner-height) + var(--fd-nav-height));height:calc(100dvh - var(--fd-banner-height) - var(--fd-nav-height))"><div class="flex h-full w-(--fd-toc-width) max-w-full flex-col pe-4"><h3 class="inline-flex items-center gap-1.5 text-sm text-fd-muted-foreground"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide size-4"><path d="M15 18H3"></path><path d="M17 6H3"></path><path d="M21 12H3"></path></svg>On this page</h3><div class="relative min-h-0 text-sm ms-px overflow-auto [scrollbar-width:none] [mask-image:linear-gradient(to_bottom,transparent,white_16px,white_calc(100%-16px),transparent)] py-3"><div role="none" class="absolute top-(--fd-top) h-(--fd-height) w-px bg-fd-primary transition-all"></div><div class="flex flex-col border-s border-fd-foreground/10"><a data-active="false" href="#code-block" class="prose py-1.5 text-sm text-fd-muted-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary ps-3">Code Block</a><a data-active="false" href="#cards" class="prose py-1.5 text-sm text-fd-muted-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary ps-3">Cards</a></div></div></div></div></div><!--$--><!--/$--></main><script src="/_next/static/chunks/webpack-8845dc663ed01e54.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[9513,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"379\",\"static/chunks/379-50e981a99501adfb.js\",\"177\",\"static/chunks/app/layout-417276e6d105b1a1.js\"],\"Provider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[8693,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"TreeContextProvider\"]\n6:I[1339,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"NavProvider\"]\n7:I[7479,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"Navbar\"]\n8:I[2808,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"default\"]\n9:I[5403,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"SearchToggle\"]\na:I[6776,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"SidebarTrigger\"]\nb:I[7479,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811"])</script><script>self.__next_f.push([1,"\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"LayoutBody\"]\nc:I[6776,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"Sidebar\"]\nd:I[6776,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"SidebarContentMobile\"]\ne:I[6776,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"SidebarHeader\"]\nf:I[1624,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"ThemeToggle\"]\n17:I[8393,[],\"\"]\n:HL[\"/_next/static/css/97e78c7008a781cb.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"-UfTfY9npJwVLAeBnUpXo\",\"p\":\"\",\"c\":[\"\",\"docs\",\"test\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"docs\",{\"children\":[[\"slug\",\"test\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/97e78c7008a781cb.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"__className_e8ce0c\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"flex flex-col min-h-screen\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"docs\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L5\",null,{\"tree\":{\"$id\":\"root\",\"name\":\"Hello World\",\"children\":[{\"$id\":\"index.mdx\",\"type\":\"page\",\"name\":\"Hello World\",\"description\":\"Your first document\",\"icon\":\"$undefined\",\"url\":\"/docs\",\"$ref\":{\"file\":\"index.mdx\"}},{\"$id\":\"test.mdx\",\"type\":\"page\",\"name\":\"Components\",\"description\":\"Components\",\"icon\":\"$undefined\",\"url\":\"/docs/test\",\"$ref\":{\"file\":\"test.mdx\"}}]},\"children\":[\"$\",\"$L6\",null,{\"transparentMode\":\"$undefined\",\"children\":[[\"$\",\"$L7\",null,{\"className\":\"h-14 md:hidden\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"inline-flex items-center gap-2.5 font-semibold\",\"children\":[[\"$\",\"svg\",null,{\"width\":\"24\",\"height\":\"24\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"aria-label\":\"Logo\",\"children\":[\"$\",\"circle\",null,{\"cx\":12,\"cy\":12,\"r\":12,\"fill\":\"currentColor\"}]}],\"My App\"]}],[\"$\",\"div\",null,{\"className\":\"flex-1\",\"children\":\"$undefined\"}],[\"$\",\"$L9\",null,{\"className\":\"p-2\",\"hideIfDisabled\":true}],[\"$\",\"$La\",null,{\"className\":\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground [\u0026_svg]:size-4.5 p-2\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide\",\"children\":[[[\"$\",\"rect\",\"afitv7\",{\"width\":\"18\",\"height\":\"18\",\"x\":\"3\",\"y\":\"3\",\"rx\":\"2\"}],[\"$\",\"path\",\"fh3hqa\",{\"d\":\"M9 3v18\"}]],\"$undefined\"]}]}]]}],[\"$\",\"$Lb\",null,{\"className\":\"xl:[--fd-toc-width:286px] md:[--fd-sidebar-width:268px] lg:[--fd-sidebar-width:286px] [--fd-nav-height:56px] md:[--fd-nav-height:0px]\",\"children\":[[\"$\",\"$Lc\",null,{\"defaultOpenLevel\":\"$undefined\",\"prefetch\":\"$undefined\",\"Mobile\":[\"$\",\"$Ld\",null,{\"children\":[[\"$\",\"$Le\",null,{\"children\":[[\"$\",\"div\",null,{\"className\":\"flex text-fd-muted-foreground items-center gap-1.5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-1\",\"children\":[]}],null,[\"$\",\"$Lf\",null,{\"className\":\"p-0\",\"mode\":\"$undefined\"}],[\"$\",\"$La\",null,{\"className\":\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground [\u0026_svg]:size-4.5 p-2\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide\",\"children\":[[[\"$\",\"rect\",\"afitv7\",{\"width\":\"18\",\"height\":\"18\",\"x\":\"3\",\"y\":\"3\",\"rx\":\"2\"}],[\"$\",\"path\",\"fh3hqa\",{\"d\":\"M9 3v18\"}]],\"$undefined\"]}]}]]}],false,\"$undefined\"]}],\"$L10\",\"$L11\"]}],\"Content\":\"$L12\"}],\"$L13\"]}]]}]}]]}],{\"children\":[[\"slug\",\"test\",\"oc\"],\"$L14\",{\"children\":[\"__PAGE__\",\"$L15\",{},null,false]},null,false]},null,false]},null,false],\"$L16\",false]],\"m\":\"$undefined\",\"G\":[\"$17\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"18:I[6776,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"SidebarViewport\"]\n19:I[6776,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"SidebarPageTree\"]\n1a:I[6776,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"SidebarFooter\"]\n1b:I[7479,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"CollapsibleControl\"]\n1c:I[6776,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"SidebarContent\"]\n1d:I[6776,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"SidebarCollapseTrigger\"]\n1e:I[5403,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"LargeSearchToggle\"]\n1f:I[8055,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"607\",\"static/chunks/607-decfe929aa00677c.js\",\"244\",\"static/chunks/244-"])</script><script>self.__next_f.push([1,"904cb1778dd50666.js\",\"811\",\"static/chunks/811-792768cab0bb19ab.js\",\"499\",\"static/chunks/app/docs/layout-56b992df650dde51.js\"],\"HideIfEmpty\"]\n21:I[9665,[],\"OutletBoundary\"]\n23:I[4911,[],\"AsyncMetadataOutlet\"]\n25:I[9665,[],\"ViewportBoundary\"]\n27:I[9665,[],\"MetadataBoundary\"]\n28:\"$Sreact.suspense\"\n10:[\"$\",\"$L18\",null,{\"children\":[[],[\"$\",\"$L19\",null,{\"components\":\"$undefined\"}]]}]\n11:[\"$\",\"$L1a\",null,{\"className\":\"empty:hidden\",\"children\":\"$undefined\"}]\n"])</script><script>self.__next_f.push([1,"12:[[\"$\",\"$L1b\",null,{}],[\"$\",\"$L1c\",null,{\"children\":[[\"$\",\"$Le\",null,{\"children\":[[\"$\",\"div\",null,{\"className\":\"flex\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"inline-flex text-[15px] items-center gap-2.5 font-medium me-auto\",\"children\":\"$0:f:0:1:2:children:1:props:children:1:props:children:props:children:0:props:children:0:props:children\"}],\"$undefined\",[\"$\",\"$L1d\",null,{\"className\":\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50 focus-visible:outline-none hover:bg-fd-accent hover:text-fd-accent-foreground p-1.5 [\u0026_svg]:size-4.5 mb-auto text-fd-muted-foreground\",\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide\",\"children\":[[[\"$\",\"rect\",\"afitv7\",{\"width\":\"18\",\"height\":\"18\",\"x\":\"3\",\"y\":\"3\",\"rx\":\"2\"}],[\"$\",\"path\",\"fh3hqa\",{\"d\":\"M9 3v18\"}]],\"$undefined\"]}]}]]}],[\"$\",\"$L1e\",null,{\"hideIfDisabled\":true}],false,\"$undefined\"]}],\"$10\",[\"$\",\"$L1f\",null,{\"as\":\"$1a\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex text-fd-muted-foreground items-center justify-end empty:hidden\",\"children\":[[],null,[\"$\",\"$Lf\",null,{\"className\":\"p-0\",\"mode\":\"$undefined\"}]]}],\"$undefined\"]}]]}]]\n"])</script><script>self.__next_f.push([1,"13:[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]\n14:[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]\n15:[\"$\",\"$1\",\"c\",{\"children\":[\"$L20\",null,[\"$\",\"$L21\",null,{\"children\":[\"$L22\",[\"$\",\"$L23\",null,{\"promise\":\"$@24\"}]]}]]}]\n16:[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L25\",null,{\"children\":\"$L26\"}],null],[\"$\",\"$L27\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$28\",null,{\"fallback\":null,\"children\":\"$L29\"}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"2a:I[8070,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"TOCProvider\"]\n2b:I[5549,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"PageTOCPopover\"]\n2c:I[5549,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"PageTOCPopoverTrigger\"]\n2d:I[5549,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"PageTOCPopoverContent\"]\n2e:I[8070,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"TOCScrollArea\"]\n2f:I[8070,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"TOCItems\"]\n30:I[5549,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"PageBreadcrumb\"]\n31:I[8202,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"CodeBlock\"]\n32:I[8202,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f"])</script><script>self.__next_f.push([1,"3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"Pre\"]\n"])</script><script>self.__next_f.push([1,"20:[\"$\",\"$L2a\",null,{\"toc\":[{\"depth\":2,\"url\":\"#code-block\",\"title\":\"Code Block\"},{\"depth\":2,\"url\":\"#cards\",\"title\":\"Cards\"}],\"single\":\"$undefined\",\"children\":[\"$\",\"div\",null,{\"id\":\"nd-page\",\"className\":\"flex flex-1 w-full mx-auto max-w-(--fd-page-width) pt-(--fd-tocnav-height)\",\"children\":[[\"$\",\"$L2b\",null,{\"children\":[[\"$\",\"$L2c\",null,{}],[\"$\",\"$L2d\",null,{\"children\":[\"$undefined\",[\"$\",\"$L2e\",null,{\"children\":[\"$\",\"$L2f\",null,{}]}],\"$undefined\"]}]]}],[\"$\",\"article\",null,{\"children\":[[\"$\",\"$L30\",null,{}],[[\"$\",\"h1\",null,{\"ref\":\"$undefined\",\"children\":\"Components\",\"className\":\"text-3xl font-semibold\"}],[\"$\",\"p\",null,{\"ref\":\"$undefined\",\"children\":\"Components\",\"className\":\"mb-8 text-lg text-fd-muted-foreground\"}],[\"$\",\"div\",null,{\"ref\":\"$undefined\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"flex scroll-m-28 flex-row items-center gap-2\",\"id\":\"code-block\",\"children\":[[\"$\",\"a\",null,{\"data-card\":\"\",\"href\":\"#code-block\",\"className\":\"peer\",\"children\":\"Code Block\"}],[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide size-3.5 shrink-0 text-fd-muted-foreground opacity-0 transition-opacity peer-hover:opacity-100\",\"aria-label\":\"Link to section\",\"children\":[[[\"$\",\"path\",\"1cjeqo\",{\"d\":\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"}],[\"$\",\"path\",\"19qd67\",{\"d\":\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"}]],\"$undefined\"]}]]}],\"\\n\",[\"$\",\"$L31\",null,{\"className\":\"shiki shiki-themes github-light github-dark\",\"style\":{\"--shiki-light\":\"#24292e\",\"--shiki-dark\":\"#e1e4e8\",\"--shiki-light-bg\":\"#fff\",\"--shiki-dark-bg\":\"#24292e\"},\"tabIndex\":\"0\",\"icon\":\"\u003csvg viewBox=\\\"0 0 24 24\\\"\u003e\u003cpath d=\\\"M0 0h24v24H0V0zm22.034 18.276c-.175-1.095-.888-2.015-3.003-2.873-.736-.345-1.554-.585-1.797-1.14-.091-.33-.105-.51-.046-.705.15-.646.915-.84 1.515-.66.39.12.75.42.976.9 1.034-.676 1.034-.676 1.755-1.125-.27-.42-.404-.601-.586-.78-.63-.705-1.469-1.065-2.834-1.034l-.705.089c-.676.165-1.32.525-1.71 1.005-1.14 1.291-.811 3.541.569 4.471 1.365 1.02 3.361 1.244 3.616 2.205.24 1.17-.87 1.545-1.966 1.41-.811-.18-1.26-.586-1.755-1.336l-1.83 1.051c.21.48.45.689.81 1.109 1.74 1.756 6.09 1.666 6.871-1.004.029-.09.24-.705.074-1.65l.046.067zm-8.983-7.245h-2.248c0 1.938-.009 3.864-.009 5.805 0 1.232.063 2.363-.138 2.711-.33.689-1.18.601-1.566.48-.396-.196-.597-.466-.83-.855-.063-.105-.11-.196-.127-.196l-1.825 1.125c.305.63.75 1.172 1.324 1.517.855.51 2.004.675 3.207.405.783-.226 1.458-.691 1.811-1.411.51-.93.402-2.07.397-3.346.012-2.054 0-4.109 0-6.179l.004-.056z\\\" fill=\\\"currentColor\\\" /\u003e\u003c/svg\u003e\",\"children\":[\"$\",\"$L32\",null,{\"children\":[\"$\",\"code\",null,{\"children\":[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"--shiki-light\":\"#24292E\",\"--shiki-dark\":\"#E1E4E8\"},\"children\":\"console.\"}],[\"$\",\"span\",null,{\"style\":{\"--shiki-light\":\"#6F42C1\",\"--shiki-dark\":\"#B392F0\"},\"children\":\"log\"}],[\"$\",\"span\",null,{\"style\":{\"--shiki-light\":\"#24292E\",\"--shiki-dark\":\"#E1E4E8\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"--shiki-light\":\"#032F62\",\"--shiki-dark\":\"#9ECBFF\"},\"children\":\"'Hello World'\"}],[\"$\",\"span\",null,{\"style\":{\"--shiki-light\":\"#24292E\",\"--shiki-dark\":\"#E1E4E8\"},\"children\":\");\"}]]}]}]}]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"flex scroll-m-28 flex-row items-center gap-2\",\"id\":\"cards\",\"children\":[[\"$\",\"a\",null,{\"data-card\":\"\",\"href\":\"#cards\",\"className\":\"peer\",\"children\":\"Cards\"}],[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide size-3.5 shrink-0 text-fd-muted-foreground opacity-0 transition-opacity peer-hover:opacity-100\",\"aria-label\":\"Link to section\",\"children\":[[[\"$\",\"path\",\"1cjeqo\",{\"d\":\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"}],[\"$\",\"path\",\"19qd67\",{\"d\":\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"}]],\"$undefined\"]}]]}],\"\\n\",[\"$\",\"div\",null,{\"children\":[[\"$\",\"$L8\",null,{\"href\":\"https://nextjs.org/docs\",\"data-card\":true,\"className\":\"block rounded-xl border bg-fd-card p-4 text-fd-card-foreground transition-colors @max-lg:col-span-full hover:bg-fd-accent/80\",\"children\":[null,\"$L33\",null,\"$L34\"]}],\"$L35\"],\"className\":\"grid grid-cols-2 gap-3 @container\"}]],\"className\":\"prose\"}]],\"$L36\",\"$L37\",\"$L38\"],\"className\":\"flex min-w-0 w-full flex-col gap-4 px-4 pt-8 md:px-6 md:mx-auto xl:pt-12 xl:px-12\"}],\"$L39\"]}]}]\n"])</script><script>self.__next_f.push([1,"3a:I[5549,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"PageFooter\"]\n3b:I[5549,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"PageTOC\"]\n3c:I[9697,[\"407\",\"static/chunks/407-0a1678b22623f2df.js\",\"328\",\"static/chunks/328-a0d4f8e3eb6521f3.js\",\"475\",\"static/chunks/475-f4ab7565c1dc7718.js\",\"870\",\"static/chunks/app/docs/%5B%5B...slug%5D%5D/page-65aae1a56896c463.js\"],\"I18nLabel\"]\n33:[\"$\",\"h3\",null,{\"className\":\"not-prose mb-1 text-sm font-medium\",\"children\":\"Learn more about Next.js\"}]\n34:[\"$\",\"div\",null,{\"className\":\"text-sm text-fd-muted-foreground prose-no-margin empty:hidden\",\"children\":\"$undefined\"}]\n35:[\"$\",\"$L8\",null,{\"href\":\"https://fumadocs.vercel.app\",\"data-card\":true,\"className\":\"block rounded-xl border bg-fd-card p-4 text-fd-card-foreground transition-colors @max-lg:col-span-full hover:bg-fd-accent/80\",\"children\":[null,[\"$\",\"h3\",null,{\"className\":\"not-prose mb-1 text-sm font-medium\",\"children\":\"Learn more about Fumadocs\"}],null,[\"$\",\"div\",null,{\"className\":\"text-sm text-fd-muted-foreground prose-no-margin empty:hidden\",\"children\":\"$undefined\"}]]}]\n36:[\"$\",\"div\",null,{\"role\":\"none\",\"className\":\"flex-1\"}]\n37:[\"$\",\"div\",null,{\"className\":\"flex flex-row flex-wrap items-center justify-between gap-4 empty:hidden\",\"children\":[\"$undefined\",\"$undefined\"]}]\n38:[\"$\",\"$L3a\",null,{\"items\":\"$undefined\"}]\n39:[\"$\",\"$L3b\",null,{\"children\":[\"$undefined\",[\"$\",\"h3\",null,{\"className\":\"inline-flex items-center gap-1.5 text-sm text-fd-muted-foreground\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"luc"])</script><script>self.__next_f.push([1,"ide size-4\",\"children\":[[[\"$\",\"path\",\"olowqp\",{\"d\":\"M15 18H3\"}],[\"$\",\"path\",\"16j9eg\",{\"d\":\"M17 6H3\"}],[\"$\",\"path\",\"2avoz0\",{\"d\":\"M21 12H3\"}]],\"$undefined\"]}],[\"$\",\"$L3c\",null,{\"label\":\"toc\"}]]}],[\"$\",\"$L2e\",null,{\"children\":[\"$\",\"$L2f\",null,{}]}],\"$undefined\"]}]\n"])</script><script>self.__next_f.push([1,"26:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n22:null\n"])</script><script>self.__next_f.push([1,"24:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Components\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Components\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"29:\"$24:metadata\"\n"])</script></body></html>