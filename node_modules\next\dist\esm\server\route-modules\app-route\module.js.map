{"version": 3, "sources": ["../../../../src/server/route-modules/app-route/module.ts"], "sourcesContent": ["import type { NextConfig } from '../../config-shared'\nimport type { AppRouteRouteDefinition } from '../../route-definitions/app-route-route-definition'\nimport type { AppSegmentConfig } from '../../../build/segment-config/app/app-segment-config'\nimport type { NextRequest } from '../../web/spec-extension/request'\nimport type { PrerenderManifest } from '../../../build'\nimport type { NextURL } from '../../web/next-url'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\nimport type { WorkUnitStore } from '../../app-render/work-unit-async-storage.external'\n\nimport {\n  RouteModule,\n  type RouteModuleHandleContext,\n  type RouteModuleOptions,\n} from '../route-module'\nimport { createRequestStoreForAPI } from '../../async-storage/request-store'\nimport {\n  createWorkStore,\n  type WorkStoreContext,\n} from '../../async-storage/work-store'\nimport { type HTTP_METHOD, HTTP_METHODS, isHTTPMethod } from '../../web/http'\nimport { getImplicitTags, type ImplicitTags } from '../../lib/implicit-tags'\nimport { patchFetch } from '../../lib/patch-fetch'\nimport { getTracer } from '../../lib/trace/tracer'\nimport { AppRouteRouteHandlersSpan } from '../../lib/trace/constants'\nimport { getPathnameFromAbsolutePath } from './helpers/get-pathname-from-absolute-path'\nimport * as Log from '../../../build/output/log'\nimport { autoImplementMethods } from './helpers/auto-implement-methods'\nimport {\n  appendMutableCookies,\n  type ReadonlyRequestCookies,\n} from '../../web/spec-extension/adapters/request-cookies'\nimport { HeadersAdapter } from '../../web/spec-extension/adapters/headers'\nimport { RequestCookiesAdapter } from '../../web/spec-extension/adapters/request-cookies'\nimport { parsedUrlQueryToParams } from './helpers/parsed-url-query-to-params'\nimport { printDebugThrownValueForProspectiveRender } from '../../app-render/prospective-render-utils'\n\nimport * as serverHooks from '../../../client/components/hooks-server-context'\nimport { DynamicServerError } from '../../../client/components/hooks-server-context'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type RequestStore,\n  type PrerenderStore,\n} from '../../app-render/work-unit-async-storage.external'\nimport {\n  actionAsyncStorage,\n  type ActionStore,\n} from '../../app-render/action-async-storage.external'\nimport * as sharedModules from './shared-modules'\nimport { getIsPossibleServerAction } from '../../lib/server-action-request-meta'\nimport { RequestCookies } from 'next/dist/compiled/@edge-runtime/cookies'\nimport { cleanURL } from './helpers/clean-url'\nimport { StaticGenBailoutError } from '../../../client/components/static-generation-bailout'\nimport { isStaticGenEnabled } from './helpers/is-static-gen-enabled'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n  createDynamicTrackingState,\n  getFirstDynamicReason,\n} from '../../app-render/dynamic-rendering'\nimport { ReflectAdapter } from '../../web/spec-extension/adapters/reflect'\nimport type { RenderOptsPartial } from '../../app-render/types'\nimport { CacheSignal } from '../../app-render/cache-signal'\nimport { scheduleImmediate } from '../../../lib/scheduler'\nimport { createServerParamsForRoute } from '../../request/params'\nimport type { AppSegment } from '../../../build/segment-config/app/app-segments'\nimport {\n  getRedirectStatusCodeFromError,\n  getURLFromRedirectError,\n} from '../../../client/components/redirect'\nimport {\n  isRedirectError,\n  type RedirectError,\n} from '../../../client/components/redirect-error'\nimport {\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../../client/components/http-access-fallback/http-access-fallback'\nimport { RedirectStatusCode } from '../../../client/components/redirect-status-code'\nimport { INFINITE_CACHE } from '../../../lib/constants'\nimport { executeRevalidates } from '../../revalidation-utils'\nimport { trackPendingModules } from '../../app-render/module-loading/track-module-loading.external'\nimport { InvariantError } from '../../../shared/lib/invariant-error'\n\nexport class WrappedNextRouterError {\n  constructor(\n    public readonly error: RedirectError,\n    public readonly headers?: Headers\n  ) {}\n}\n\n/**\n * The AppRouteModule is the type of the module exported by the bundled App\n * Route module.\n */\nexport type AppRouteModule = typeof import('../../../build/templates/app-route')\n\nexport type AppRouteSharedContext = {\n  buildId: string\n}\n\n/**\n * AppRouteRouteHandlerContext is the context that is passed to the route\n * handler for app routes.\n */\nexport interface AppRouteRouteHandlerContext extends RouteModuleHandleContext {\n  renderOpts: WorkStoreContext['renderOpts'] &\n    Pick<RenderOptsPartial, 'onInstrumentationRequestError'> &\n    CollectedCacheInfo\n  prerenderManifest: DeepReadonly<PrerenderManifest>\n  sharedContext: AppRouteSharedContext\n}\n\ntype CollectedCacheInfo = {\n  collectedTags?: string\n  collectedRevalidate?: number\n  collectedExpire?: number\n  collectedStale?: number\n}\n\n/**\n * AppRouteHandlerFnContext is the context that is passed to the handler as the\n * second argument.\n */\ntype AppRouteHandlerFnContext = {\n  params?: Promise<Record<string, string | string[] | undefined>>\n}\n\n/**\n * Handler function for app routes. If a non-Response value is returned, an error\n * will be thrown.\n */\nexport type AppRouteHandlerFn = (\n  /**\n   * Incoming request object.\n   */\n  req: NextRequest,\n  /**\n   * Context properties on the request (including the parameters if this was a\n   * dynamic route).\n   */\n  ctx: AppRouteHandlerFnContext\n) => unknown\n\n/**\n * AppRouteHandlers describes the handlers for app routes that is provided by\n * the userland module.\n */\nexport type AppRouteHandlers = {\n  [method in HTTP_METHOD]?: AppRouteHandlerFn\n}\n\n/**\n * AppRouteUserlandModule is the userland module that is provided for app\n * routes. This contains all the user generated code.\n */\nexport type AppRouteUserlandModule = AppRouteHandlers &\n  Pick<\n    AppSegmentConfig,\n    'dynamic' | 'revalidate' | 'dynamicParams' | 'fetchCache'\n  > &\n  Pick<AppSegment, 'generateStaticParams'>\n\n/**\n * AppRouteRouteModuleOptions is the options that are passed to the app route\n * module from the bundled code.\n */\nexport interface AppRouteRouteModuleOptions\n  extends RouteModuleOptions<AppRouteRouteDefinition, AppRouteUserlandModule> {\n  readonly resolvedPagePath: string\n  readonly nextConfigOutput: NextConfig['output']\n}\n\n/**\n * AppRouteRouteHandler is the handler for app routes.\n */\nexport class AppRouteRouteModule extends RouteModule<\n  AppRouteRouteDefinition,\n  AppRouteUserlandModule\n> {\n  /**\n   * A reference to the request async storage.\n   */\n  public readonly workUnitAsyncStorage = workUnitAsyncStorage\n\n  /**\n   * A reference to the static generation async storage.\n   */\n  public readonly workAsyncStorage = workAsyncStorage\n\n  /**\n   * An interface to call server hooks which interact with the underlying\n   * storage.\n   */\n  public readonly serverHooks = serverHooks\n\n  public static readonly sharedModules = sharedModules\n\n  /**\n   * A reference to the mutation related async storage, such as mutations of\n   * cookies.\n   */\n  public readonly actionAsyncStorage = actionAsyncStorage\n\n  public readonly resolvedPagePath: string\n  public readonly nextConfigOutput: NextConfig['output'] | undefined\n\n  private readonly methods: Record<HTTP_METHOD, AppRouteHandlerFn>\n  private readonly hasNonStaticMethods: boolean\n  private readonly dynamic: AppRouteUserlandModule['dynamic']\n\n  constructor({\n    userland,\n    definition,\n    distDir,\n    relativeProjectDir,\n    resolvedPagePath,\n    nextConfigOutput,\n  }: AppRouteRouteModuleOptions) {\n    super({ userland, definition, distDir, relativeProjectDir })\n\n    this.resolvedPagePath = resolvedPagePath\n    this.nextConfigOutput = nextConfigOutput\n\n    // Automatically implement some methods if they aren't implemented by the\n    // userland module.\n    this.methods = autoImplementMethods(userland)\n    this.isAppRouter = true\n\n    // Get the non-static methods for this route.\n    this.hasNonStaticMethods = hasNonStaticMethods(userland)\n\n    // Get the dynamic property from the userland module.\n    this.dynamic = this.userland.dynamic\n    if (this.nextConfigOutput === 'export') {\n      if (this.dynamic === 'force-dynamic') {\n        throw new Error(\n          `export const dynamic = \"force-dynamic\" on page \"${definition.pathname}\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`\n        )\n      } else if (!isStaticGenEnabled(this.userland) && this.userland['GET']) {\n        throw new Error(\n          `export const dynamic = \"force-static\"/export const revalidate not configured on route \"${definition.pathname}\" with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`\n        )\n      } else {\n        this.dynamic = 'error'\n      }\n    }\n\n    // We only warn in development after here, so return if we're not in\n    // development.\n    if (process.env.NODE_ENV === 'development') {\n      // Print error in development if the exported handlers are in lowercase, only\n      // uppercase handlers are supported.\n      const lowercased = HTTP_METHODS.map((method) => method.toLowerCase())\n      for (const method of lowercased) {\n        if (method in this.userland) {\n          Log.error(\n            `Detected lowercase method '${method}' in '${\n              this.resolvedPagePath\n            }'. Export the uppercase '${method.toUpperCase()}' method name to fix this error.`\n          )\n        }\n      }\n\n      // Print error if the module exports a default handler, they must use named\n      // exports for each HTTP method.\n      if ('default' in this.userland) {\n        Log.error(\n          `Detected default export in '${this.resolvedPagePath}'. Export a named export for each HTTP method instead.`\n        )\n      }\n\n      // If there is no methods exported by this module, then return a not found\n      // response.\n      if (!HTTP_METHODS.some((method) => method in this.userland)) {\n        Log.error(\n          `No HTTP methods exported in '${this.resolvedPagePath}'. Export a named export for each HTTP method.`\n        )\n      }\n    }\n  }\n\n  /**\n   * Resolves the handler function for the given method.\n   *\n   * @param method the requested method\n   * @returns the handler function for the given method\n   */\n  private resolve(method: string): AppRouteHandlerFn {\n    // Ensure that the requested method is a valid method (to prevent RCE's).\n    if (!isHTTPMethod(method)) return () => new Response(null, { status: 400 })\n\n    // Return the handler.\n    return this.methods[method]\n  }\n\n  private async do(\n    handler: AppRouteHandlerFn,\n    actionStore: ActionStore,\n    workStore: WorkStore,\n    // @TODO refactor to not take this argument but instead construct the RequestStore\n    // inside this function. Right now we get passed a RequestStore even when\n    // we're going to do a prerender. We should probably just split do up into prexecute and execute\n    requestStore: RequestStore,\n    implicitTags: ImplicitTags,\n    request: NextRequest,\n    context: AppRouteRouteHandlerContext\n  ) {\n    const isStaticGeneration = workStore.isStaticGeneration\n    const cacheComponentsEnabled =\n      !!context.renderOpts.experimental?.cacheComponents\n\n    // Patch the global fetch.\n    patchFetch({\n      workAsyncStorage: this.workAsyncStorage,\n      workUnitAsyncStorage: this.workUnitAsyncStorage,\n    })\n\n    const handlerContext: AppRouteHandlerFnContext = {\n      params: context.params\n        ? createServerParamsForRoute(\n            parsedUrlQueryToParams(context.params),\n            workStore\n          )\n        : undefined,\n    }\n\n    const resolvePendingRevalidations = () => {\n      context.renderOpts.pendingWaitUntil = executeRevalidates(\n        workStore\n      ).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log(\n            'pending revalidates promise finished for:',\n            requestStore.url\n          )\n        }\n      })\n    }\n\n    let prerenderStore: null | PrerenderStore = null\n\n    let res: unknown\n    try {\n      if (isStaticGeneration) {\n        const userlandRevalidate = this.userland.revalidate\n        const defaultRevalidate: number =\n          // If the static generation store does not have a revalidate value\n          // set, then we should set it the revalidate value from the userland\n          // module or default to false.\n          userlandRevalidate === false || userlandRevalidate === undefined\n            ? INFINITE_CACHE\n            : userlandRevalidate\n\n        if (cacheComponentsEnabled) {\n          /**\n           * When we are attempting to statically prerender the GET handler of a route.ts module\n           * and cacheComponents is on we follow a similar pattern to rendering.\n           *\n           * We first run the handler letting caches fill. If something synchronously dynamic occurs\n           * during this prospective render then we can infer it will happen on every render and we\n           * just bail out of prerendering.\n           *\n           * Next we run the handler again and we check if we get a result back in a microtask.\n           * Next.js expects the return value to be a Response or a Thenable that resolves to a Response.\n           * Unfortunately Response's do not allow for accessing the response body synchronously or in\n           * a microtask so we need to allow one more task to unwrap the response body. This is a slightly\n           * different semantic than what we have when we render and it means that certain tasks can still\n           * execute before a prerender completes such as a carefully timed setImmediate.\n           *\n           * Functionally though IO should still take longer than the time it takes to unwrap the response body\n           * so our heuristic of excluding any IO should be preserved.\n           */\n          const prospectiveController = new AbortController()\n          let prospectiveRenderIsDynamic = false\n          const cacheSignal = new CacheSignal()\n          let dynamicTracking = createDynamicTrackingState(undefined)\n\n          const prospectiveRoutePrerenderStore: PrerenderStore =\n            (prerenderStore = {\n              type: 'prerender',\n              phase: 'action',\n              // This replicates prior behavior where rootParams is empty in routes\n              // TODO we need to make this have the proper rootParams for this route\n              rootParams: {},\n              fallbackRouteParams: null,\n              implicitTags,\n              renderSignal: prospectiveController.signal,\n              controller: prospectiveController,\n              cacheSignal,\n              // During prospective render we don't use a controller\n              // because we need to let all caches fill.\n              dynamicTracking,\n              allowEmptyStaticShell: false,\n              revalidate: defaultRevalidate,\n              expire: INFINITE_CACHE,\n              stale: INFINITE_CACHE,\n              tags: [...implicitTags.tags],\n              // TODO: Shouldn't we provide an RDC here?\n              prerenderResumeDataCache: null,\n              renderResumeDataCache: null,\n              hmrRefreshHash: undefined,\n              captureOwnerStack: undefined,\n            })\n\n          let prospectiveResult\n          try {\n            prospectiveResult = this.workUnitAsyncStorage.run(\n              prospectiveRoutePrerenderStore,\n              handler,\n              request,\n              handlerContext\n            )\n          } catch (err) {\n            if (prospectiveController.signal.aborted) {\n              // the route handler called an API which is always dynamic\n              // there is no need to try again\n              prospectiveRenderIsDynamic = true\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          }\n          if (\n            typeof prospectiveResult === 'object' &&\n            prospectiveResult !== null &&\n            typeof (prospectiveResult as any).then === 'function'\n          ) {\n            // The handler returned a Thenable. We'll listen for rejections to determine\n            // if the route is erroring for dynamic reasons.\n            ;(prospectiveResult as any as Promise<unknown>).then(\n              () => {},\n              (err) => {\n                if (prospectiveController.signal.aborted) {\n                  // the route handler called an API which is always dynamic\n                  // there is no need to try again\n                  prospectiveRenderIsDynamic = true\n                } else if (process.env.NEXT_DEBUG_BUILD) {\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              }\n            )\n          }\n\n          trackPendingModules(cacheSignal)\n          await cacheSignal.cacheReady()\n\n          if (prospectiveRenderIsDynamic) {\n            // the route handler called an API which is always dynamic\n            // there is no need to try again\n            const dynamicReason = getFirstDynamicReason(dynamicTracking)\n            if (dynamicReason) {\n              throw new DynamicServerError(\n                `Route ${workStore.route} couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n              )\n            } else {\n              console.error(\n                'Expected Next.js to keep track of reason for opting out of static rendering but one was not found. This is a bug in Next.js'\n              )\n              throw new DynamicServerError(\n                `Route ${workStore.route} couldn't be rendered statically because it used a dynamic API. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n              )\n            }\n          }\n\n          // TODO start passing this controller to the route handler. We should expose\n          // it so the handler to abort inflight requests and other operations if we abort\n          // the prerender.\n          const finalController = new AbortController()\n          dynamicTracking = createDynamicTrackingState(undefined)\n\n          const finalRoutePrerenderStore: PrerenderStore = (prerenderStore = {\n            type: 'prerender',\n            phase: 'action',\n            rootParams: {},\n            fallbackRouteParams: null,\n            implicitTags,\n            renderSignal: finalController.signal,\n            controller: finalController,\n            cacheSignal: null,\n            dynamicTracking,\n            allowEmptyStaticShell: false,\n            revalidate: defaultRevalidate,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags.tags],\n            // TODO: Shouldn't we provide an RDC here?\n            prerenderResumeDataCache: null,\n            renderResumeDataCache: null,\n            hmrRefreshHash: undefined,\n            captureOwnerStack: undefined,\n          })\n\n          let responseHandled = false\n          res = await new Promise((resolve, reject) => {\n            scheduleImmediate(async () => {\n              try {\n                const result = await (this.workUnitAsyncStorage.run(\n                  finalRoutePrerenderStore,\n                  handler,\n                  request,\n                  handlerContext\n                ) as Promise<Response>)\n                if (responseHandled) {\n                  // we already rejected in the followup task\n                  return\n                } else if (!(result instanceof Response)) {\n                  // This is going to error but we let that happen below\n                  resolve(result)\n                  return\n                }\n\n                responseHandled = true\n\n                let bodyHandled = false\n                result.arrayBuffer().then((body) => {\n                  if (!bodyHandled) {\n                    bodyHandled = true\n\n                    resolve(\n                      new Response(body, {\n                        headers: result.headers,\n                        status: result.status,\n                        statusText: result.statusText,\n                      })\n                    )\n                  }\n                }, reject)\n                scheduleImmediate(() => {\n                  if (!bodyHandled) {\n                    bodyHandled = true\n                    finalController.abort()\n                    reject(createCacheComponentsError(workStore.route))\n                  }\n                })\n              } catch (err) {\n                reject(err)\n              }\n            })\n            scheduleImmediate(() => {\n              if (!responseHandled) {\n                responseHandled = true\n                finalController.abort()\n                reject(createCacheComponentsError(workStore.route))\n              }\n            })\n          })\n          if (finalController.signal.aborted) {\n            // We aborted from within the execution\n            throw createCacheComponentsError(workStore.route)\n          } else {\n            // We didn't abort during the execution. We can abort now as a matter of semantics\n            // though at the moment nothing actually consumes this signal so it won't halt any\n            // inflight work.\n            finalController.abort()\n          }\n        } else {\n          prerenderStore = {\n            type: 'prerender-legacy',\n            phase: 'action',\n            rootParams: {},\n            implicitTags,\n            revalidate: defaultRevalidate,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags.tags],\n          }\n\n          res = await workUnitAsyncStorage.run(\n            prerenderStore,\n            handler,\n            request,\n            handlerContext\n          )\n        }\n      } else {\n        res = await workUnitAsyncStorage.run(\n          requestStore,\n          handler,\n          request,\n          handlerContext\n        )\n      }\n    } catch (err) {\n      if (isRedirectError(err)) {\n        const url = getURLFromRedirectError(err)\n        if (!url) {\n          throw new Error('Invariant: Unexpected redirect url format')\n        }\n\n        // We need to capture any headers that should be sent on\n        // the response.\n        const headers = new Headers({ Location: url })\n\n        // Let's append any cookies that were added by the\n        // cookie API.\n        // TODO leaving the gate here b/c it indicates that we might not actually want to do this\n        // on every `do` call. During prerender there should be no mutableCookies because\n        appendMutableCookies(headers, requestStore.mutableCookies)\n\n        resolvePendingRevalidations()\n\n        // Return the redirect response.\n        return new Response(null, {\n          // If we're in an action, we want to use a 303 redirect as we don't\n          // want the POST request to follow the redirect, as it could result in\n          // erroneous re-submissions.\n          status: actionStore.isAction\n            ? RedirectStatusCode.SeeOther\n            : getRedirectStatusCodeFromError(err),\n          headers,\n        })\n      } else if (isHTTPAccessFallbackError(err)) {\n        const httpStatus = getAccessFallbackHTTPStatus(err)\n        return new Response(null, { status: httpStatus })\n      }\n\n      throw err\n    }\n\n    // Validate that the response is a valid response object.\n    if (!(res instanceof Response)) {\n      throw new Error(\n        `No response is returned from route handler '${this.resolvedPagePath}'. Ensure you return a \\`Response\\` or a \\`NextResponse\\` in all branches of your handler.`\n      )\n    }\n\n    context.renderOpts.fetchMetrics = workStore.fetchMetrics\n\n    resolvePendingRevalidations()\n\n    if (prerenderStore) {\n      context.renderOpts.collectedTags = prerenderStore.tags?.join(',')\n      context.renderOpts.collectedRevalidate = prerenderStore.revalidate\n      context.renderOpts.collectedExpire = prerenderStore.expire\n      context.renderOpts.collectedStale = prerenderStore.stale\n    }\n\n    // It's possible cookies were set in the handler, so we need\n    // to merge the modified cookies and the returned response\n    // here.\n    const headers = new Headers(res.headers)\n    if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n      return new Response(res.body, {\n        status: res.status,\n        statusText: res.statusText,\n        headers,\n      })\n    }\n\n    return res\n  }\n\n  public async handle(\n    req: NextRequest,\n    context: AppRouteRouteHandlerContext\n  ): Promise<Response> {\n    // Get the handler function for the given method.\n    const handler = this.resolve(req.method)\n\n    // Get the context for the static generation.\n    const staticGenerationContext: WorkStoreContext = {\n      page: this.definition.page,\n      renderOpts: context.renderOpts,\n      buildId: context.sharedContext.buildId,\n      previouslyRevalidatedTags: [],\n    }\n\n    // Add the fetchCache option to the renderOpts.\n    staticGenerationContext.renderOpts.fetchCache = this.userland.fetchCache\n\n    const actionStore: ActionStore = {\n      isAppRoute: true,\n      isAction: getIsPossibleServerAction(req),\n    }\n\n    const implicitTags = await getImplicitTags(\n      this.definition.page,\n      req.nextUrl,\n      // App Routes don't support unknown route params.\n      null\n    )\n\n    const requestStore = createRequestStoreForAPI(\n      req,\n      req.nextUrl,\n      implicitTags,\n      undefined,\n      context.prerenderManifest.preview\n    )\n\n    const workStore = createWorkStore(staticGenerationContext)\n\n    // Run the handler with the request AsyncLocalStorage to inject the helper\n    // support. We set this to `unknown` because the type is not known until\n    // runtime when we do a instanceof check below.\n    const response: unknown = await this.actionAsyncStorage.run(\n      actionStore,\n      () =>\n        this.workUnitAsyncStorage.run(requestStore, () =>\n          this.workAsyncStorage.run(workStore, async () => {\n            // Check to see if we should bail out of static generation based on\n            // having non-static methods.\n            if (this.hasNonStaticMethods) {\n              if (workStore.isStaticGeneration) {\n                const err = new DynamicServerError(\n                  'Route is configured with methods that cannot be statically generated.'\n                )\n                workStore.dynamicUsageDescription = err.message\n                workStore.dynamicUsageStack = err.stack\n                throw err\n              }\n            }\n\n            // We assume we can pass the original request through however we may end up\n            // proxying it in certain circumstances based on execution type and configuration\n            let request = req\n\n            // Update the static generation store based on the dynamic property.\n            switch (this.dynamic) {\n              case 'force-dynamic': {\n                // Routes of generated paths should be dynamic\n                workStore.forceDynamic = true\n                if (workStore.isStaticGeneration) {\n                  const err = new DynamicServerError(\n                    'Route is configured with dynamic = error which cannot be statically generated.'\n                  )\n                  workStore.dynamicUsageDescription = err.message\n                  workStore.dynamicUsageStack = err.stack\n                  throw err\n                }\n                break\n              }\n              case 'force-static':\n                // The dynamic property is set to force-static, so we should\n                // force the page to be static.\n                workStore.forceStatic = true\n                // We also Proxy the request to replace dynamic data on the request\n                // with empty stubs to allow for safely executing as static\n                request = new Proxy(req, forceStaticRequestHandlers)\n                break\n              case 'error':\n                // The dynamic property is set to error, so we should throw an\n                // error if the page is being statically generated.\n                workStore.dynamicShouldError = true\n                if (workStore.isStaticGeneration)\n                  request = new Proxy(req, requireStaticRequestHandlers)\n                break\n              case undefined:\n              case 'auto':\n                // We proxy `NextRequest` to track dynamic access, and\n                // potentially bail out of static generation.\n                request = proxyNextRequest(req, workStore)\n                break\n              default:\n                this.dynamic satisfies never\n            }\n\n            // TODO: propagate this pathname from route matcher\n            const route = getPathnameFromAbsolutePath(this.resolvedPagePath)\n\n            const tracer = getTracer()\n\n            // Update the root span attribute for the route.\n            tracer.setRootSpanAttribute('next.route', route)\n\n            return tracer.trace(\n              AppRouteRouteHandlersSpan.runHandler,\n              {\n                spanName: `executing api route (app) ${route}`,\n                attributes: {\n                  'next.route': route,\n                },\n              },\n              async () =>\n                this.do(\n                  handler,\n                  actionStore,\n                  workStore,\n                  requestStore,\n                  implicitTags,\n                  request,\n                  context\n                )\n            )\n          })\n        )\n    )\n\n    // If the handler did't return a valid response, then return the internal\n    // error response.\n    if (!(response instanceof Response)) {\n      // TODO: validate the correct handling behavior, maybe log something?\n      return new Response(null, { status: 500 })\n    }\n\n    if (response.headers.has('x-middleware-rewrite')) {\n      throw new Error(\n        'NextResponse.rewrite() was used in a app route handler, this is not currently supported. Please remove the invocation to continue.'\n      )\n    }\n\n    if (response.headers.get('x-middleware-next') === '1') {\n      // TODO: move this error into the `NextResponse.next()` function.\n      throw new Error(\n        'NextResponse.next() was used in a app route handler, this is not supported. See here for more info: https://nextjs.org/docs/messages/next-response-next-in-app-route-handler'\n      )\n    }\n\n    return response\n  }\n}\n\nexport default AppRouteRouteModule\n\n/**\n * Gets all the method names for handlers that are not considered static.\n *\n * @param handlers the handlers from the userland module\n * @returns the method names that are not considered static or false if all\n *          methods are static\n */\nexport function hasNonStaticMethods(handlers: AppRouteHandlers): boolean {\n  if (\n    // Order these by how common they are to be used\n    handlers.POST ||\n    handlers.PUT ||\n    handlers.DELETE ||\n    handlers.PATCH ||\n    handlers.OPTIONS\n  ) {\n    return true\n  }\n  return false\n}\n\n// These symbols will be used to stash cached values on Proxied requests without requiring\n// additional closures or storage such as WeakMaps.\nconst nextURLSymbol = Symbol('nextUrl')\nconst requestCloneSymbol = Symbol('clone')\nconst urlCloneSymbol = Symbol('clone')\nconst searchParamsSymbol = Symbol('searchParams')\nconst hrefSymbol = Symbol('href')\nconst toStringSymbol = Symbol('toString')\nconst headersSymbol = Symbol('headers')\nconst cookiesSymbol = Symbol('cookies')\n\ntype RequestSymbolTarget = {\n  [headersSymbol]?: Headers\n  [cookiesSymbol]?: RequestCookies | ReadonlyRequestCookies\n  [nextURLSymbol]?: NextURL\n  [requestCloneSymbol]?: () => NextRequest\n}\n\ntype UrlSymbolTarget = {\n  [searchParamsSymbol]?: URLSearchParams\n  [hrefSymbol]?: string\n  [toStringSymbol]?: () => string\n  [urlCloneSymbol]?: () => NextURL\n}\n\n/**\n * The general technique with these proxy handlers is to prioritize keeping them static\n * by stashing computed values on the Proxy itself. This is safe because the Proxy is\n * inaccessible to the consumer since all operations are forwarded\n */\nconst forceStaticRequestHandlers = {\n  get(\n    target: NextRequest & RequestSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'headers':\n        return (\n          target[headersSymbol] ||\n          (target[headersSymbol] = HeadersAdapter.seal(new Headers({})))\n        )\n      case 'cookies':\n        return (\n          target[cookiesSymbol] ||\n          (target[cookiesSymbol] = RequestCookiesAdapter.seal(\n            new RequestCookies(new Headers({}))\n          ))\n        )\n      case 'nextUrl':\n        return (\n          target[nextURLSymbol] ||\n          (target[nextURLSymbol] = new Proxy(\n            target.nextUrl,\n            forceStaticNextUrlHandlers\n          ))\n        )\n      case 'url':\n        // we don't need to separately cache this we can just read the nextUrl\n        // and return the href since we know it will have been stripped of any\n        // dynamic parts. We access via the receiver to trigger the get trap\n        return receiver.nextUrl.href\n      case 'geo':\n      case 'ip':\n        return undefined\n      case 'clone':\n        return (\n          target[requestCloneSymbol] ||\n          (target[requestCloneSymbol] = () =>\n            new Proxy(\n              // This is vaguely unsafe but it's required since NextRequest does not implement\n              // clone. The reason we might expect this to work in this context is the Proxy will\n              // respond with static-amenable values anyway somewhat restoring the interface.\n              // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n              // sophisticated to adequately represent themselves in all contexts. A better approach is\n              // to probably embed the static generation logic into the class itself removing the need\n              // for any kind of proxying\n              target.clone() as NextRequest,\n              forceStaticRequestHandlers\n            ))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n  // We don't need to proxy set because all the properties we proxy are ready only\n  // and will be ignored\n}\n\nconst forceStaticNextUrlHandlers = {\n  get(\n    target: NextURL & UrlSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      // URL properties\n      case 'search':\n        return ''\n      case 'searchParams':\n        return (\n          target[searchParamsSymbol] ||\n          (target[searchParamsSymbol] = new URLSearchParams())\n        )\n      case 'href':\n        return (\n          target[hrefSymbol] ||\n          (target[hrefSymbol] = cleanURL(target.href).href)\n        )\n      case 'toJSON':\n      case 'toString':\n        return (\n          target[toStringSymbol] ||\n          (target[toStringSymbol] = () => receiver.href)\n        )\n\n      // NextUrl properties\n      case 'url':\n        // Currently nextURL does not expose url but our Docs indicate that it is an available property\n        // I am forcing this to undefined here to avoid accidentally exposing a dynamic value later if\n        // the underlying nextURL ends up adding this property\n        return undefined\n      case 'clone':\n        return (\n          target[urlCloneSymbol] ||\n          (target[urlCloneSymbol] = () =>\n            new Proxy(target.clone(), forceStaticNextUrlHandlers))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n}\n\nfunction proxyNextRequest(request: NextRequest, workStore: WorkStore) {\n  const nextUrlHandlers = {\n    get(\n      target: NextURL & UrlSymbolTarget,\n      prop: string | symbol,\n      receiver: any\n    ): unknown {\n      switch (prop) {\n        case 'search':\n        case 'searchParams':\n        case 'url':\n        case 'href':\n        case 'toJSON':\n        case 'toString':\n        case 'origin': {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamic(workStore, workUnitStore, `nextUrl.${prop}`)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'clone':\n          return (\n            target[urlCloneSymbol] ||\n            (target[urlCloneSymbol] = () =>\n              new Proxy(target.clone(), nextUrlHandlers))\n          )\n        default:\n          return ReflectAdapter.get(target, prop, receiver)\n      }\n    },\n  }\n\n  const nextRequestHandlers = {\n    get(\n      target: NextRequest & RequestSymbolTarget,\n      prop: string | symbol\n    ): unknown {\n      switch (prop) {\n        case 'nextUrl':\n          return (\n            target[nextURLSymbol] ||\n            (target[nextURLSymbol] = new Proxy(target.nextUrl, nextUrlHandlers))\n          )\n        case 'headers':\n        case 'cookies':\n        case 'url':\n        case 'body':\n        case 'blob':\n        case 'json':\n        case 'text':\n        case 'arrayBuffer':\n        case 'formData': {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamic(workStore, workUnitStore, `request.${prop}`)\n          // The receiver arg is intentionally the same as the target to fix an issue with\n          // edge runtime, where attempting to access internal slots with the wrong `this` context\n          // results in an error.\n          return ReflectAdapter.get(target, prop, target)\n        }\n        case 'clone':\n          return (\n            target[requestCloneSymbol] ||\n            (target[requestCloneSymbol] = () =>\n              new Proxy(\n                // This is vaguely unsafe but it's required since NextRequest does not implement\n                // clone. The reason we might expect this to work in this context is the Proxy will\n                // respond with static-amenable values anyway somewhat restoring the interface.\n                // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n                // sophisticated to adequately represent themselves in all contexts. A better approach is\n                // to probably embed the static generation logic into the class itself removing the need\n                // for any kind of proxying\n                target.clone() as NextRequest,\n                nextRequestHandlers\n              ))\n          )\n        default:\n          // The receiver arg is intentionally the same as the target to fix an issue with\n          // edge runtime, where attempting to access internal slots with the wrong `this` context\n          // results in an error.\n          return ReflectAdapter.get(target, prop, target)\n      }\n    },\n    // We don't need to proxy set because all the properties we proxy are ready only\n    // and will be ignored\n  }\n\n  return new Proxy(request, nextRequestHandlers)\n}\n\nconst requireStaticRequestHandlers = {\n  get(\n    target: NextRequest & RequestSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'nextUrl':\n        return (\n          target[nextURLSymbol] ||\n          (target[nextURLSymbol] = new Proxy(\n            target.nextUrl,\n            requireStaticNextUrlHandlers\n          ))\n        )\n      case 'headers':\n      case 'cookies':\n      case 'url':\n      case 'body':\n      case 'blob':\n      case 'json':\n      case 'text':\n      case 'arrayBuffer':\n      case 'formData':\n        throw new StaticGenBailoutError(\n          `Route ${target.nextUrl.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`request.${prop}\\`.`\n        )\n      case 'clone':\n        return (\n          target[requestCloneSymbol] ||\n          (target[requestCloneSymbol] = () =>\n            new Proxy(\n              // This is vaguely unsafe but it's required since NextRequest does not implement\n              // clone. The reason we might expect this to work in this context is the Proxy will\n              // respond with static-amenable values anyway somewhat restoring the interface.\n              // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n              // sophisticated to adequately represent themselves in all contexts. A better approach is\n              // to probably embed the static generation logic into the class itself removing the need\n              // for any kind of proxying\n              target.clone() as NextRequest,\n              requireStaticRequestHandlers\n            ))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n  // We don't need to proxy set because all the properties we proxy are ready only\n  // and will be ignored\n}\n\nconst requireStaticNextUrlHandlers = {\n  get(\n    target: NextURL & UrlSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'search':\n      case 'searchParams':\n      case 'url':\n      case 'href':\n      case 'toJSON':\n      case 'toString':\n      case 'origin':\n        throw new StaticGenBailoutError(\n          `Route ${target.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`nextUrl.${prop}\\`.`\n        )\n      case 'clone':\n        return (\n          target[urlCloneSymbol] ||\n          (target[urlCloneSymbol] = () =>\n            new Proxy(target.clone(), requireStaticNextUrlHandlers))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n}\n\nfunction createCacheComponentsError(route: string) {\n  return new DynamicServerError(\n    `Route ${route} couldn't be rendered statically because it used IO that was not cached. See more info here: https://nextjs.org/docs/messages/cache-components`\n  )\n}\n\nfunction trackDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | WorkUnitStore,\n  expression: string\n): void {\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'private-cache':\n        // TODO: Should we allow reading cookies and search params from the\n        // request for private caches in route handlers?\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      case 'unstable-cache':\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      case 'prerender':\n        const error = new Error(\n          `Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-request`\n        )\n        return abortAndThrowOnSynchronousRequestDataAccess(\n          store.route,\n          expression,\n          error,\n          workUnitStore\n        )\n      case 'prerender-client':\n        throw new InvariantError(\n          'A client prerender store should not be used for a route handler.'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'A runtime prerender store should not be used for a route handler.'\n        )\n      case 'prerender-ppr':\n        return postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      case 'prerender-legacy':\n        workUnitStore.revalidate = 0\n\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      case 'request':\n        if (process.env.NODE_ENV !== 'production') {\n          // TODO: This is currently not really needed for route handlers, as it\n          // only controls the ISR status that's shown for pages.\n          workUnitStore.usedDynamic = true\n        }\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n"], "names": ["RouteModule", "createRequestStoreForAPI", "createWorkStore", "HTTP_METHODS", "isHTTPMethod", "getImplicitTags", "patchFetch", "getTracer", "AppRouteRouteHandlersSpan", "getPathnameFromAbsolutePath", "Log", "autoImplementMethods", "appendMutableCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RequestCookiesAdapter", "parsedUrlQueryToParams", "printDebugThrownValueForProspectiveRender", "serverHooks", "DynamicServerError", "workAsyncStorage", "workUnitAsyncStorage", "actionAsyncStorage", "sharedModules", "getIsPossibleServerAction", "RequestCookies", "cleanURL", "StaticGenBailoutError", "isStaticGenEnabled", "abortAndThrowOnSynchronousRequestDataAccess", "postponeWithTracking", "createDynamicTrackingState", "getFirstDynamicReason", "ReflectAdapter", "CacheSignal", "scheduleImmediate", "createServerParamsForRoute", "getRedirectStatusCodeFromError", "getURLFromRedirectError", "isRedirectError", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "RedirectStatusCode", "INFINITE_CACHE", "executeRevalidates", "trackPendingModules", "InvariantError", "WrappedNextRouterError", "constructor", "error", "headers", "AppRouteRouteModule", "userland", "definition", "distDir", "relativeProjectDir", "resolvedPagePath", "nextConfigOutput", "methods", "isAppRouter", "hasNonStaticMethods", "dynamic", "Error", "pathname", "process", "env", "NODE_ENV", "lowercased", "map", "method", "toLowerCase", "toUpperCase", "some", "resolve", "Response", "status", "do", "handler", "actionStore", "workStore", "requestStore", "implicitTags", "request", "context", "isStaticGeneration", "cacheComponentsEnabled", "renderOpts", "experimental", "cacheComponents", "handlerContext", "params", "undefined", "resolvePendingRevalidations", "pendingWaitUntil", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "url", "prerenderStore", "res", "userlandRevalidate", "revalidate", "defaultRevalidate", "prospectiveController", "AbortController", "prospectiveRenderIsDynamic", "cacheSignal", "dynamicTracking", "prospectiveRoutePrerenderStore", "type", "phase", "rootParams", "fallbackRouteParams", "renderSignal", "signal", "controller", "allowEmptyStaticShell", "expire", "stale", "tags", "prerenderResumeDataCache", "renderResumeDataCache", "hmrRefreshHash", "captureOwnerStack", "prospectiveResult", "run", "err", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "route", "then", "cacheReady", "dynamicReason", "finalController", "finalRoutePrerenderStore", "responseHandled", "Promise", "reject", "result", "bodyHandled", "arrayBuffer", "body", "statusText", "abort", "createCacheComponentsError", "Headers", "Location", "mutableCookies", "isAction", "<PERSON><PERSON><PERSON>", "httpStatus", "fetchMetrics", "collectedTags", "join", "collectedRevalidate", "collectedExpire", "collectedStale", "handle", "req", "staticGenerationContext", "page", "buildId", "sharedContext", "previouslyRevalidatedTags", "fetchCache", "isAppRoute", "nextUrl", "prerenderManifest", "preview", "response", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceDynamic", "forceStatic", "Proxy", "forceStaticRequestHandlers", "dynamicShouldError", "requireStaticRequestHandlers", "proxyNextRequest", "tracer", "setRootSpanAttribute", "trace", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "attributes", "has", "get", "handlers", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "nextURLSymbol", "Symbol", "requestCloneSymbol", "urlCloneSymbol", "searchParamsSymbol", "hrefSymbol", "toStringSymbol", "headersSymbol", "cookiesSymbol", "target", "prop", "receiver", "seal", "forceStaticNextUrlHandlers", "href", "clone", "URLSearchParams", "nextUrlHandlers", "workUnitStore", "getStore", "trackDynamic", "nextRequestHandlers", "requireStaticNextUrlHandlers", "store", "expression", "usedDynamic"], "mappings": "AASA,SACEA,WAAW,QAGN,kBAAiB;AACxB,SAASC,wBAAwB,QAAQ,oCAAmC;AAC5E,SACEC,eAAe,QAEV,iCAAgC;AACvC,SAA2BC,YAAY,EAAEC,YAAY,QAAQ,iBAAgB;AAC7E,SAASC,eAAe,QAA2B,0BAAyB;AAC5E,SAASC,UAAU,QAAQ,wBAAuB;AAClD,SAASC,SAAS,QAAQ,yBAAwB;AAClD,SAASC,yBAAyB,QAAQ,4BAA2B;AACrE,SAASC,2BAA2B,QAAQ,4CAA2C;AACvF,YAAYC,SAAS,4BAA2B;AAChD,SAASC,oBAAoB,QAAQ,mCAAkC;AACvE,SACEC,oBAAoB,QAEf,oDAAmD;AAC1D,SAASC,cAAc,QAAQ,4CAA2C;AAC1E,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,sBAAsB,QAAQ,uCAAsC;AAC7E,SAASC,yCAAyC,QAAQ,4CAA2C;AAErG,YAAYC,iBAAiB,kDAAiD;AAC9E,SAASC,kBAAkB,QAAQ,kDAAiD;AAEpF,SACEC,gBAAgB,QAEX,+CAA8C;AACrD,SACEC,oBAAoB,QAGf,oDAAmD;AAC1D,SACEC,kBAAkB,QAEb,iDAAgD;AACvD,YAAYC,mBAAmB,mBAAkB;AACjD,SAASC,yBAAyB,QAAQ,uCAAsC;AAChF,SAASC,cAAc,QAAQ,2CAA0C;AACzE,SAASC,QAAQ,QAAQ,sBAAqB;AAC9C,SAASC,qBAAqB,QAAQ,uDAAsD;AAC5F,SAASC,kBAAkB,QAAQ,kCAAiC;AACpE,SACEC,2CAA2C,EAC3CC,oBAAoB,EACpBC,0BAA0B,EAC1BC,qBAAqB,QAChB,qCAAoC;AAC3C,SAASC,cAAc,QAAQ,4CAA2C;AAE1E,SAASC,WAAW,QAAQ,gCAA+B;AAC3D,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,0BAA0B,QAAQ,uBAAsB;AAEjE,SACEC,8BAA8B,EAC9BC,uBAAuB,QAClB,sCAAqC;AAC5C,SACEC,eAAe,QAEV,4CAA2C;AAClD,SACEC,2BAA2B,EAC3BC,yBAAyB,QACpB,uEAAsE;AAC7E,SAASC,kBAAkB,QAAQ,kDAAiD;AACpF,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,kBAAkB,QAAQ,2BAA0B;AAC7D,SAASC,mBAAmB,QAAQ,gEAA+D;AACnG,SAASC,cAAc,QAAQ,sCAAqC;AAEpE,OAAO,MAAMC;IACXC,YACE,AAAgBC,KAAoB,EACpC,AAAgBC,OAAiB,CACjC;aAFgBD,QAAAA;aACAC,UAAAA;IACf;AACL;AAoFA;;CAEC,GACD,OAAO,MAAMC,4BAA4BlD;qBAoBhBsB,gBAAgBA;IAevCyB,YAAY,EACVI,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EACW,CAAE;QAC7B,KAAK,CAAC;YAAEL;YAAUC;YAAYC;YAASC;QAAmB,IAvC5D;;GAEC,QACelC,uBAAuBA,sBAEvC;;GAEC,QACeD,mBAAmBA,kBAEnC;;;GAGC,QACeF,cAAcA,aAI9B;;;GAGC,QACeI,qBAAqBA;QAmBnC,IAAI,CAACkC,gBAAgB,GAAGA;QACxB,IAAI,CAACC,gBAAgB,GAAGA;QAExB,yEAAyE;QACzE,mBAAmB;QACnB,IAAI,CAACC,OAAO,GAAG9C,qBAAqBwC;QACpC,IAAI,CAACO,WAAW,GAAG;QAEnB,6CAA6C;QAC7C,IAAI,CAACC,mBAAmB,GAAGA,oBAAoBR;QAE/C,qDAAqD;QACrD,IAAI,CAACS,OAAO,GAAG,IAAI,CAACT,QAAQ,CAACS,OAAO;QACpC,IAAI,IAAI,CAACJ,gBAAgB,KAAK,UAAU;YACtC,IAAI,IAAI,CAACI,OAAO,KAAK,iBAAiB;gBACpC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,gDAAgD,EAAET,WAAWU,QAAQ,CAAC,wHAAwH,CAAC,GAD5L,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAI,CAACnC,mBAAmB,IAAI,CAACwB,QAAQ,KAAK,IAAI,CAACA,QAAQ,CAAC,MAAM,EAAE;gBACrE,MAAM,qBAEL,CAFK,IAAIU,MACR,CAAC,uFAAuF,EAAET,WAAWU,QAAQ,CAAC,yGAAyG,CAAC,GADpN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO;gBACL,IAAI,CAACF,OAAO,GAAG;YACjB;QACF;QAEA,oEAAoE;QACpE,eAAe;QACf,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,6EAA6E;YAC7E,oCAAoC;YACpC,MAAMC,aAAa/D,aAAagE,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;YAClE,KAAK,MAAMD,UAAUF,WAAY;gBAC/B,IAAIE,UAAU,IAAI,CAACjB,QAAQ,EAAE;oBAC3BzC,IAAIsC,KAAK,CACP,CAAC,2BAA2B,EAAEoB,OAAO,MAAM,EACzC,IAAI,CAACb,gBAAgB,CACtB,yBAAyB,EAAEa,OAAOE,WAAW,GAAG,gCAAgC,CAAC;gBAEtF;YACF;YAEA,2EAA2E;YAC3E,gCAAgC;YAChC,IAAI,aAAa,IAAI,CAACnB,QAAQ,EAAE;gBAC9BzC,IAAIsC,KAAK,CACP,CAAC,4BAA4B,EAAE,IAAI,CAACO,gBAAgB,CAAC,sDAAsD,CAAC;YAEhH;YAEA,0EAA0E;YAC1E,YAAY;YACZ,IAAI,CAACpD,aAAaoE,IAAI,CAAC,CAACH,SAAWA,UAAU,IAAI,CAACjB,QAAQ,GAAG;gBAC3DzC,IAAIsC,KAAK,CACP,CAAC,6BAA6B,EAAE,IAAI,CAACO,gBAAgB,CAAC,8CAA8C,CAAC;YAEzG;QACF;IACF;IAEA;;;;;GAKC,GACD,AAAQiB,QAAQJ,MAAc,EAAqB;QACjD,yEAAyE;QACzE,IAAI,CAAChE,aAAagE,SAAS,OAAO,IAAM,IAAIK,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAEzE,sBAAsB;QACtB,OAAO,IAAI,CAACjB,OAAO,CAACW,OAAO;IAC7B;IAEA,MAAcO,GACZC,OAA0B,EAC1BC,WAAwB,EACxBC,SAAoB,EACpB,kFAAkF;IAClF,yEAAyE;IACzE,gGAAgG;IAChGC,YAA0B,EAC1BC,YAA0B,EAC1BC,OAAoB,EACpBC,OAAoC,EACpC;YAGIA;QAFJ,MAAMC,qBAAqBL,UAAUK,kBAAkB;QACvD,MAAMC,yBACJ,CAAC,GAACF,mCAAAA,QAAQG,UAAU,CAACC,YAAY,qBAA/BJ,iCAAiCK,eAAe;QAEpD,0BAA0B;QAC1BjF,WAAW;YACTa,kBAAkB,IAAI,CAACA,gBAAgB;YACvCC,sBAAsB,IAAI,CAACA,oBAAoB;QACjD;QAEA,MAAMoE,iBAA2C;YAC/CC,QAAQP,QAAQO,MAAM,GAClBtD,2BACEpB,uBAAuBmE,QAAQO,MAAM,GACrCX,aAEFY;QACN;QAEA,MAAMC,8BAA8B;YAClCT,QAAQG,UAAU,CAACO,gBAAgB,GAAGjD,mBACpCmC,WACAe,OAAO,CAAC;gBACR,IAAI9B,QAAQC,GAAG,CAAC8B,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CACT,6CACAjB,aAAakB,GAAG;gBAEpB;YACF;QACF;QAEA,IAAIC,iBAAwC;QAE5C,IAAIC;QACJ,IAAI;YACF,IAAIhB,oBAAoB;gBACtB,MAAMiB,qBAAqB,IAAI,CAACjD,QAAQ,CAACkD,UAAU;gBACnD,MAAMC,oBACJ,kEAAkE;gBAClE,oEAAoE;gBACpE,8BAA8B;gBAC9BF,uBAAuB,SAASA,uBAAuBV,YACnDhD,iBACA0D;gBAEN,IAAIhB,wBAAwB;oBAC1B;;;;;;;;;;;;;;;;;WAiBC,GACD,MAAMmB,wBAAwB,IAAIC;oBAClC,IAAIC,6BAA6B;oBACjC,MAAMC,cAAc,IAAIzE;oBACxB,IAAI0E,kBAAkB7E,2BAA2B4D;oBAEjD,MAAMkB,iCACHV,iBAAiB;wBAChBW,MAAM;wBACNC,OAAO;wBACP,qEAAqE;wBACrE,sEAAsE;wBACtEC,YAAY,CAAC;wBACbC,qBAAqB;wBACrBhC;wBACAiC,cAAcV,sBAAsBW,MAAM;wBAC1CC,YAAYZ;wBACZG;wBACA,sDAAsD;wBACtD,0CAA0C;wBAC1CC;wBACAS,uBAAuB;wBACvBf,YAAYC;wBACZe,QAAQ3E;wBACR4E,OAAO5E;wBACP6E,MAAM;+BAAIvC,aAAauC,IAAI;yBAAC;wBAC5B,0CAA0C;wBAC1CC,0BAA0B;wBAC1BC,uBAAuB;wBACvBC,gBAAgBhC;wBAChBiC,mBAAmBjC;oBACrB;oBAEF,IAAIkC;oBACJ,IAAI;wBACFA,oBAAoB,IAAI,CAACxG,oBAAoB,CAACyG,GAAG,CAC/CjB,gCACAhC,SACAK,SACAO;oBAEJ,EAAE,OAAOsC,KAAK;wBACZ,IAAIvB,sBAAsBW,MAAM,CAACa,OAAO,EAAE;4BACxC,0DAA0D;4BAC1D,gCAAgC;4BAChCtB,6BAA6B;wBAC/B,OAAO,IACL1C,QAAQC,GAAG,CAACgE,gBAAgB,IAC5BjE,QAAQC,GAAG,CAACiE,sBAAsB,EAClC;4BACAjH,0CAA0C8G,KAAKhD,UAAUoD,KAAK;wBAChE;oBACF;oBACA,IACE,OAAON,sBAAsB,YAC7BA,sBAAsB,QACtB,OAAO,AAACA,kBAA0BO,IAAI,KAAK,YAC3C;wBACA,4EAA4E;wBAC5E,gDAAgD;;wBAC9CP,kBAA8CO,IAAI,CAClD,KAAO,GACP,CAACL;4BACC,IAAIvB,sBAAsBW,MAAM,CAACa,OAAO,EAAE;gCACxC,0DAA0D;gCAC1D,gCAAgC;gCAChCtB,6BAA6B;4BAC/B,OAAO,IAAI1C,QAAQC,GAAG,CAACgE,gBAAgB,EAAE;gCACvChH,0CACE8G,KACAhD,UAAUoD,KAAK;4BAEnB;wBACF;oBAEJ;oBAEAtF,oBAAoB8D;oBACpB,MAAMA,YAAY0B,UAAU;oBAE5B,IAAI3B,4BAA4B;wBAC9B,0DAA0D;wBAC1D,gCAAgC;wBAChC,MAAM4B,gBAAgBtG,sBAAsB4E;wBAC5C,IAAI0B,eAAe;4BACjB,MAAM,qBAEL,CAFK,IAAInH,mBACR,CAAC,MAAM,EAAE4D,UAAUoD,KAAK,CAAC,mDAAmD,EAAEG,cAAc,6EAA6E,CAAC,GADtK,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF,OAAO;4BACLtC,QAAQ/C,KAAK,CACX;4BAEF,MAAM,qBAEL,CAFK,IAAI9B,mBACR,CAAC,MAAM,EAAE4D,UAAUoD,KAAK,CAAC,yIAAyI,CAAC,GAD/J,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;oBACF;oBAEA,4EAA4E;oBAC5E,gFAAgF;oBAChF,iBAAiB;oBACjB,MAAMI,kBAAkB,IAAI9B;oBAC5BG,kBAAkB7E,2BAA2B4D;oBAE7C,MAAM6C,2BAA4CrC,iBAAiB;wBACjEW,MAAM;wBACNC,OAAO;wBACPC,YAAY,CAAC;wBACbC,qBAAqB;wBACrBhC;wBACAiC,cAAcqB,gBAAgBpB,MAAM;wBACpCC,YAAYmB;wBACZ5B,aAAa;wBACbC;wBACAS,uBAAuB;wBACvBf,YAAYC;wBACZe,QAAQ3E;wBACR4E,OAAO5E;wBACP6E,MAAM;+BAAIvC,aAAauC,IAAI;yBAAC;wBAC5B,0CAA0C;wBAC1CC,0BAA0B;wBAC1BC,uBAAuB;wBACvBC,gBAAgBhC;wBAChBiC,mBAAmBjC;oBACrB;oBAEA,IAAI8C,kBAAkB;oBACtBrC,MAAM,MAAM,IAAIsC,QAAQ,CAACjE,SAASkE;wBAChCxG,kBAAkB;4BAChB,IAAI;gCACF,MAAMyG,SAAS,MAAO,IAAI,CAACvH,oBAAoB,CAACyG,GAAG,CACjDU,0BACA3D,SACAK,SACAO;gCAEF,IAAIgD,iBAAiB;oCACnB,2CAA2C;oCAC3C;gCACF,OAAO,IAAI,CAAEG,CAAAA,kBAAkBlE,QAAO,GAAI;oCACxC,sDAAsD;oCACtDD,QAAQmE;oCACR;gCACF;gCAEAH,kBAAkB;gCAElB,IAAII,cAAc;gCAClBD,OAAOE,WAAW,GAAGV,IAAI,CAAC,CAACW;oCACzB,IAAI,CAACF,aAAa;wCAChBA,cAAc;wCAEdpE,QACE,IAAIC,SAASqE,MAAM;4CACjB7F,SAAS0F,OAAO1F,OAAO;4CACvByB,QAAQiE,OAAOjE,MAAM;4CACrBqE,YAAYJ,OAAOI,UAAU;wCAC/B;oCAEJ;gCACF,GAAGL;gCACHxG,kBAAkB;oCAChB,IAAI,CAAC0G,aAAa;wCAChBA,cAAc;wCACdN,gBAAgBU,KAAK;wCACrBN,OAAOO,2BAA2BnE,UAAUoD,KAAK;oCACnD;gCACF;4BACF,EAAE,OAAOJ,KAAK;gCACZY,OAAOZ;4BACT;wBACF;wBACA5F,kBAAkB;4BAChB,IAAI,CAACsG,iBAAiB;gCACpBA,kBAAkB;gCAClBF,gBAAgBU,KAAK;gCACrBN,OAAOO,2BAA2BnE,UAAUoD,KAAK;4BACnD;wBACF;oBACF;oBACA,IAAII,gBAAgBpB,MAAM,CAACa,OAAO,EAAE;wBAClC,uCAAuC;wBACvC,MAAMkB,2BAA2BnE,UAAUoD,KAAK;oBAClD,OAAO;wBACL,kFAAkF;wBAClF,kFAAkF;wBAClF,iBAAiB;wBACjBI,gBAAgBU,KAAK;oBACvB;gBACF,OAAO;oBACL9C,iBAAiB;wBACfW,MAAM;wBACNC,OAAO;wBACPC,YAAY,CAAC;wBACb/B;wBACAqB,YAAYC;wBACZe,QAAQ3E;wBACR4E,OAAO5E;wBACP6E,MAAM;+BAAIvC,aAAauC,IAAI;yBAAC;oBAC9B;oBAEApB,MAAM,MAAM/E,qBAAqByG,GAAG,CAClC3B,gBACAtB,SACAK,SACAO;gBAEJ;YACF,OAAO;gBACLW,MAAM,MAAM/E,qBAAqByG,GAAG,CAClC9C,cACAH,SACAK,SACAO;YAEJ;QACF,EAAE,OAAOsC,KAAK;YACZ,IAAIxF,gBAAgBwF,MAAM;gBACxB,MAAM7B,MAAM5D,wBAAwByF;gBACpC,IAAI,CAAC7B,KAAK;oBACR,MAAM,qBAAsD,CAAtD,IAAIpC,MAAM,8CAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAqD;gBAC7D;gBAEA,wDAAwD;gBACxD,gBAAgB;gBAChB,MAAMZ,UAAU,IAAIiG,QAAQ;oBAAEC,UAAUlD;gBAAI;gBAE5C,kDAAkD;gBAClD,cAAc;gBACd,yFAAyF;gBACzF,iFAAiF;gBACjFrF,qBAAqBqC,SAAS8B,aAAaqE,cAAc;gBAEzDzD;gBAEA,gCAAgC;gBAChC,OAAO,IAAIlB,SAAS,MAAM;oBACxB,mEAAmE;oBACnE,sEAAsE;oBACtE,4BAA4B;oBAC5BC,QAAQG,YAAYwE,QAAQ,GACxB5G,mBAAmB6G,QAAQ,GAC3BlH,+BAA+B0F;oBACnC7E;gBACF;YACF,OAAO,IAAIT,0BAA0BsF,MAAM;gBACzC,MAAMyB,aAAahH,4BAA4BuF;gBAC/C,OAAO,IAAIrD,SAAS,MAAM;oBAAEC,QAAQ6E;gBAAW;YACjD;YAEA,MAAMzB;QACR;QAEA,yDAAyD;QACzD,IAAI,CAAE3B,CAAAA,eAAe1B,QAAO,GAAI;YAC9B,MAAM,qBAEL,CAFK,IAAIZ,MACR,CAAC,4CAA4C,EAAE,IAAI,CAACN,gBAAgB,CAAC,0FAA0F,CAAC,GAD5J,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA2B,QAAQG,UAAU,CAACmE,YAAY,GAAG1E,UAAU0E,YAAY;QAExD7D;QAEA,IAAIO,gBAAgB;gBACiBA;YAAnChB,QAAQG,UAAU,CAACoE,aAAa,IAAGvD,uBAAAA,eAAeqB,IAAI,qBAAnBrB,qBAAqBwD,IAAI,CAAC;YAC7DxE,QAAQG,UAAU,CAACsE,mBAAmB,GAAGzD,eAAeG,UAAU;YAClEnB,QAAQG,UAAU,CAACuE,eAAe,GAAG1D,eAAemB,MAAM;YAC1DnC,QAAQG,UAAU,CAACwE,cAAc,GAAG3D,eAAeoB,KAAK;QAC1D;QAEA,4DAA4D;QAC5D,0DAA0D;QAC1D,QAAQ;QACR,MAAMrE,UAAU,IAAIiG,QAAQ/C,IAAIlD,OAAO;QACvC,IAAIrC,qBAAqBqC,SAAS8B,aAAaqE,cAAc,GAAG;YAC9D,OAAO,IAAI3E,SAAS0B,IAAI2C,IAAI,EAAE;gBAC5BpE,QAAQyB,IAAIzB,MAAM;gBAClBqE,YAAY5C,IAAI4C,UAAU;gBAC1B9F;YACF;QACF;QAEA,OAAOkD;IACT;IAEA,MAAa2D,OACXC,GAAgB,EAChB7E,OAAoC,EACjB;QACnB,iDAAiD;QACjD,MAAMN,UAAU,IAAI,CAACJ,OAAO,CAACuF,IAAI3F,MAAM;QAEvC,6CAA6C;QAC7C,MAAM4F,0BAA4C;YAChDC,MAAM,IAAI,CAAC7G,UAAU,CAAC6G,IAAI;YAC1B5E,YAAYH,QAAQG,UAAU;YAC9B6E,SAAShF,QAAQiF,aAAa,CAACD,OAAO;YACtCE,2BAA2B,EAAE;QAC/B;QAEA,+CAA+C;QAC/CJ,wBAAwB3E,UAAU,CAACgF,UAAU,GAAG,IAAI,CAAClH,QAAQ,CAACkH,UAAU;QAExE,MAAMxF,cAA2B;YAC/ByF,YAAY;YACZjB,UAAU9H,0BAA0BwI;QACtC;QAEA,MAAM/E,eAAe,MAAM3E,gBACzB,IAAI,CAAC+C,UAAU,CAAC6G,IAAI,EACpBF,IAAIQ,OAAO,EACX,iDAAiD;QACjD;QAGF,MAAMxF,eAAe9E,yBACnB8J,KACAA,IAAIQ,OAAO,EACXvF,cACAU,WACAR,QAAQsF,iBAAiB,CAACC,OAAO;QAGnC,MAAM3F,YAAY5E,gBAAgB8J;QAElC,0EAA0E;QAC1E,wEAAwE;QACxE,+CAA+C;QAC/C,MAAMU,WAAoB,MAAM,IAAI,CAACrJ,kBAAkB,CAACwG,GAAG,CACzDhD,aACA,IACE,IAAI,CAACzD,oBAAoB,CAACyG,GAAG,CAAC9C,cAAc,IAC1C,IAAI,CAAC5D,gBAAgB,CAAC0G,GAAG,CAAC/C,WAAW;oBACnC,mEAAmE;oBACnE,6BAA6B;oBAC7B,IAAI,IAAI,CAACnB,mBAAmB,EAAE;wBAC5B,IAAImB,UAAUK,kBAAkB,EAAE;4BAChC,MAAM2C,MAAM,qBAEX,CAFW,IAAI5G,mBACd,0EADU,qBAAA;uCAAA;4CAAA;8CAAA;4BAEZ;4BACA4D,UAAU6F,uBAAuB,GAAG7C,IAAI8C,OAAO;4BAC/C9F,UAAU+F,iBAAiB,GAAG/C,IAAIgD,KAAK;4BACvC,MAAMhD;wBACR;oBACF;oBAEA,2EAA2E;oBAC3E,iFAAiF;oBACjF,IAAI7C,UAAU8E;oBAEd,oEAAoE;oBACpE,OAAQ,IAAI,CAACnG,OAAO;wBAClB,KAAK;4BAAiB;gCACpB,8CAA8C;gCAC9CkB,UAAUiG,YAAY,GAAG;gCACzB,IAAIjG,UAAUK,kBAAkB,EAAE;oCAChC,MAAM2C,MAAM,qBAEX,CAFW,IAAI5G,mBACd,mFADU,qBAAA;+CAAA;oDAAA;sDAAA;oCAEZ;oCACA4D,UAAU6F,uBAAuB,GAAG7C,IAAI8C,OAAO;oCAC/C9F,UAAU+F,iBAAiB,GAAG/C,IAAIgD,KAAK;oCACvC,MAAMhD;gCACR;gCACA;4BACF;wBACA,KAAK;4BACH,4DAA4D;4BAC5D,+BAA+B;4BAC/BhD,UAAUkG,WAAW,GAAG;4BACxB,mEAAmE;4BACnE,2DAA2D;4BAC3D/F,UAAU,IAAIgG,MAAMlB,KAAKmB;4BACzB;wBACF,KAAK;4BACH,8DAA8D;4BAC9D,mDAAmD;4BACnDpG,UAAUqG,kBAAkB,GAAG;4BAC/B,IAAIrG,UAAUK,kBAAkB,EAC9BF,UAAU,IAAIgG,MAAMlB,KAAKqB;4BAC3B;wBACF,KAAK1F;wBACL,KAAK;4BACH,sDAAsD;4BACtD,6CAA6C;4BAC7CT,UAAUoG,iBAAiBtB,KAAKjF;4BAChC;wBACF;4BACE,IAAI,CAAClB,OAAO;oBAChB;oBAEA,mDAAmD;oBACnD,MAAMsE,QAAQzH,4BAA4B,IAAI,CAAC8C,gBAAgB;oBAE/D,MAAM+H,SAAS/K;oBAEf,gDAAgD;oBAChD+K,OAAOC,oBAAoB,CAAC,cAAcrD;oBAE1C,OAAOoD,OAAOE,KAAK,CACjBhL,0BAA0BiL,UAAU,EACpC;wBACEC,UAAU,CAAC,0BAA0B,EAAExD,OAAO;wBAC9CyD,YAAY;4BACV,cAAczD;wBAChB;oBACF,GACA,UACE,IAAI,CAACvD,EAAE,CACLC,SACAC,aACAC,WACAC,cACAC,cACAC,SACAC;gBAGR;QAIN,yEAAyE;QACzE,kBAAkB;QAClB,IAAI,CAAEwF,CAAAA,oBAAoBjG,QAAO,GAAI;YACnC,qEAAqE;YACrE,OAAO,IAAIA,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAC1C;QAEA,IAAIgG,SAASzH,OAAO,CAAC2I,GAAG,CAAC,yBAAyB;YAChD,MAAM,qBAEL,CAFK,IAAI/H,MACR,uIADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI6G,SAASzH,OAAO,CAAC4I,GAAG,CAAC,yBAAyB,KAAK;YACrD,iEAAiE;YACjE,MAAM,qBAEL,CAFK,IAAIhI,MACR,iLADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAO6G;IACT;AACF;AAEA,eAAexH,oBAAmB;AAElC;;;;;;CAMC,GACD,OAAO,SAASS,oBAAoBmI,QAA0B;IAC5D,IACE,gDAAgD;IAChDA,SAASC,IAAI,IACbD,SAASE,GAAG,IACZF,SAASG,MAAM,IACfH,SAASI,KAAK,IACdJ,SAASK,OAAO,EAChB;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEA,0FAA0F;AAC1F,mDAAmD;AACnD,MAAMC,gBAAgBC,OAAO;AAC7B,MAAMC,qBAAqBD,OAAO;AAClC,MAAME,iBAAiBF,OAAO;AAC9B,MAAMG,qBAAqBH,OAAO;AAClC,MAAMI,aAAaJ,OAAO;AAC1B,MAAMK,iBAAiBL,OAAO;AAC9B,MAAMM,gBAAgBN,OAAO;AAC7B,MAAMO,gBAAgBP,OAAO;AAgB7B;;;;CAIC,GACD,MAAMnB,6BAA6B;IACjCW,KACEgB,MAAyC,EACzCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,KAAK;gBACH,OACED,MAAM,CAACF,cAAc,IACpBE,CAAAA,MAAM,CAACF,cAAc,GAAG9L,eAAemM,IAAI,CAAC,IAAI9D,QAAQ,CAAC,GAAE;YAEhE,KAAK;gBACH,OACE2D,MAAM,CAACD,cAAc,IACpBC,CAAAA,MAAM,CAACD,cAAc,GAAG9L,sBAAsBkM,IAAI,CACjD,IAAIxL,eAAe,IAAI0H,QAAQ,CAAC,IAClC;YAEJ,KAAK;gBACH,OACE2D,MAAM,CAACT,cAAc,IACpBS,CAAAA,MAAM,CAACT,cAAc,GAAG,IAAInB,MAC3B4B,OAAOtC,OAAO,EACd0C,2BACF;YAEJ,KAAK;gBACH,sEAAsE;gBACtE,sEAAsE;gBACtE,oEAAoE;gBACpE,OAAOF,SAASxC,OAAO,CAAC2C,IAAI;YAC9B,KAAK;YACL,KAAK;gBACH,OAAOxH;YACT,KAAK;gBACH,OACEmH,MAAM,CAACP,mBAAmB,IACzBO,CAAAA,MAAM,CAACP,mBAAmB,GAAG,IAC5B,IAAIrB,MACF,gFAAgF;oBAChF,mFAAmF;oBACnF,+EAA+E;oBAC/E,sFAAsF;oBACtF,yFAAyF;oBACzF,wFAAwF;oBACxF,2BAA2B;oBAC3B4B,OAAOM,KAAK,IACZjC,2BACF;YAEN;gBACE,OAAOlJ,eAAe6J,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AAGF;AAEA,MAAME,6BAA6B;IACjCpB,KACEgB,MAAiC,EACjCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,iBAAiB;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OACED,MAAM,CAACL,mBAAmB,IACzBK,CAAAA,MAAM,CAACL,mBAAmB,GAAG,IAAIY,iBAAgB;YAEtD,KAAK;gBACH,OACEP,MAAM,CAACJ,WAAW,IACjBI,CAAAA,MAAM,CAACJ,WAAW,GAAGhL,SAASoL,OAAOK,IAAI,EAAEA,IAAI,AAAD;YAEnD,KAAK;YACL,KAAK;gBACH,OACEL,MAAM,CAACH,eAAe,IACrBG,CAAAA,MAAM,CAACH,eAAe,GAAG,IAAMK,SAASG,IAAI,AAAD;YAGhD,qBAAqB;YACrB,KAAK;gBACH,+FAA+F;gBAC/F,8FAA8F;gBAC9F,sDAAsD;gBACtD,OAAOxH;YACT,KAAK;gBACH,OACEmH,MAAM,CAACN,eAAe,IACrBM,CAAAA,MAAM,CAACN,eAAe,GAAG,IACxB,IAAItB,MAAM4B,OAAOM,KAAK,IAAIF,2BAA0B;YAE1D;gBACE,OAAOjL,eAAe6J,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AACF;AAEA,SAAS1B,iBAAiBpG,OAAoB,EAAEH,SAAoB;IAClE,MAAMuI,kBAAkB;QACtBxB,KACEgB,MAAiC,EACjCC,IAAqB,EACrBC,QAAa;YAEb,OAAQD;gBACN,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAU;wBACb,MAAMQ,gBAAgBlM,qBAAqBmM,QAAQ;wBACnDC,aAAa1I,WAAWwI,eAAe,CAAC,QAAQ,EAAER,MAAM;wBACxD,OAAO9K,eAAe6J,GAAG,CAACgB,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBACH,OACEF,MAAM,CAACN,eAAe,IACrBM,CAAAA,MAAM,CAACN,eAAe,GAAG,IACxB,IAAItB,MAAM4B,OAAOM,KAAK,IAAIE,gBAAe;gBAE/C;oBACE,OAAOrL,eAAe6J,GAAG,CAACgB,QAAQC,MAAMC;YAC5C;QACF;IACF;IAEA,MAAMU,sBAAsB;QAC1B5B,KACEgB,MAAyC,EACzCC,IAAqB;YAErB,OAAQA;gBACN,KAAK;oBACH,OACED,MAAM,CAACT,cAAc,IACpBS,CAAAA,MAAM,CAACT,cAAc,GAAG,IAAInB,MAAM4B,OAAOtC,OAAO,EAAE8C,gBAAe;gBAEtE,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAY;wBACf,MAAMC,gBAAgBlM,qBAAqBmM,QAAQ;wBACnDC,aAAa1I,WAAWwI,eAAe,CAAC,QAAQ,EAAER,MAAM;wBACxD,gFAAgF;wBAChF,wFAAwF;wBACxF,uBAAuB;wBACvB,OAAO9K,eAAe6J,GAAG,CAACgB,QAAQC,MAAMD;oBAC1C;gBACA,KAAK;oBACH,OACEA,MAAM,CAACP,mBAAmB,IACzBO,CAAAA,MAAM,CAACP,mBAAmB,GAAG,IAC5B,IAAIrB,MACF,gFAAgF;wBAChF,mFAAmF;wBACnF,+EAA+E;wBAC/E,sFAAsF;wBACtF,yFAAyF;wBACzF,wFAAwF;wBACxF,2BAA2B;wBAC3B4B,OAAOM,KAAK,IACZM,oBACF;gBAEN;oBACE,gFAAgF;oBAChF,wFAAwF;oBACxF,uBAAuB;oBACvB,OAAOzL,eAAe6J,GAAG,CAACgB,QAAQC,MAAMD;YAC5C;QACF;IAGF;IAEA,OAAO,IAAI5B,MAAMhG,SAASwI;AAC5B;AAEA,MAAMrC,+BAA+B;IACnCS,KACEgB,MAAyC,EACzCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,KAAK;gBACH,OACED,MAAM,CAACT,cAAc,IACpBS,CAAAA,MAAM,CAACT,cAAc,GAAG,IAAInB,MAC3B4B,OAAOtC,OAAO,EACdmD,6BACF;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIhM,sBACR,CAAC,MAAM,EAAEmL,OAAOtC,OAAO,CAACzG,QAAQ,CAAC,sFAAsF,EAAEgJ,KAAK,GAAG,CAAC,GAD9H,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OACED,MAAM,CAACP,mBAAmB,IACzBO,CAAAA,MAAM,CAACP,mBAAmB,GAAG,IAC5B,IAAIrB,MACF,gFAAgF;oBAChF,mFAAmF;oBACnF,+EAA+E;oBAC/E,sFAAsF;oBACtF,yFAAyF;oBACzF,wFAAwF;oBACxF,2BAA2B;oBAC3B4B,OAAOM,KAAK,IACZ/B,6BACF;YAEN;gBACE,OAAOpJ,eAAe6J,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AAGF;AAEA,MAAMW,+BAA+B;IACnC7B,KACEgB,MAAiC,EACjCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIpL,sBACR,CAAC,MAAM,EAAEmL,OAAO/I,QAAQ,CAAC,sFAAsF,EAAEgJ,KAAK,GAAG,CAAC,GADtH,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OACED,MAAM,CAACN,eAAe,IACrBM,CAAAA,MAAM,CAACN,eAAe,GAAG,IACxB,IAAItB,MAAM4B,OAAOM,KAAK,IAAIO,6BAA4B;YAE5D;gBACE,OAAO1L,eAAe6J,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AACF;AAEA,SAAS9D,2BAA2Bf,KAAa;IAC/C,OAAO,qBAEN,CAFM,IAAIhH,mBACT,CAAC,MAAM,EAAEgH,MAAM,8IAA8I,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEP;AACF;AAEA,SAASsF,aACPG,KAAgB,EAChBL,aAAwC,EACxCM,UAAkB;IAElB,IAAID,MAAMxC,kBAAkB,EAAE;QAC5B,MAAM,qBAEL,CAFK,IAAIzJ,sBACR,CAAC,MAAM,EAAEiM,MAAMzF,KAAK,CAAC,8EAA8E,EAAE0F,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIN,eAAe;QACjB,OAAQA,cAAczG,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,mEAAmE;gBACnE,gDAAgD;gBAChD,MAAM,qBAEL,CAFK,IAAIhD,MACR,CAAC,MAAM,EAAE8J,MAAMzF,KAAK,CAAC,OAAO,EAAE0F,WAAW,gJAAgJ,EAAEA,WAAW,qKAAqK,CAAC,GADxW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAI/J,MACR,CAAC,MAAM,EAAE8J,MAAMzF,KAAK,CAAC,OAAO,EAAE0F,WAAW,iLAAiL,EAAEA,WAAW,6KAA6K,CAAC,GADjZ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAM5K,QAAQ,qBAEb,CAFa,IAAIa,MAChB,CAAC,MAAM,EAAE8J,MAAMzF,KAAK,CAAC,MAAM,EAAE0F,WAAW,+HAA+H,CAAC,GAD5J,qBAAA;2BAAA;gCAAA;kCAAA;gBAEd;gBACA,OAAOhM,4CACL+L,MAAMzF,KAAK,EACX0F,YACA5K,OACAsK;YAEJ,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIzK,eACR,qEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIA,eACR,sEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOhB,qBACL8L,MAAMzF,KAAK,EACX0F,YACAN,cAAc3G,eAAe;YAEjC,KAAK;gBACH2G,cAAcjH,UAAU,GAAG;gBAE3B,MAAMyB,MAAM,qBAEX,CAFW,IAAI5G,mBACd,CAAC,MAAM,EAAEyM,MAAMzF,KAAK,CAAC,mDAAmD,EAAE0F,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAD,MAAMhD,uBAAuB,GAAGiD;gBAChCD,MAAM9C,iBAAiB,GAAG/C,IAAIgD,KAAK;gBAEnC,MAAMhD;YACR,KAAK;gBACH,IAAI/D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;oBACzC,sEAAsE;oBACtE,uDAAuD;oBACvDqJ,cAAcO,WAAW,GAAG;gBAC9B;gBACA;YACF;gBACEP;QACJ;IACF;AACF", "ignoreList": [0]}