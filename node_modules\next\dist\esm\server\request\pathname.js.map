{"version": 3, "sources": ["../../../src/server/request/pathname.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport {\n  delayUntilRuntimeStage,\n  postponeWithTracking,\n  type DynamicTrackingState,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  throwInvariantForMissingStore,\n  workUnitAsyncStorage,\n  type StaticPrerenderStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nexport function createServerPathnameForMetadata(\n  underlyingPathname: string,\n  workStore: WorkStore\n): Promise<string> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy': {\n        return createPrerenderPathname(\n          underlyingPathname,\n          workStore,\n          workUnitStore\n        )\n      }\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerPathnameForMetadata should not be called in cache contexts.'\n        )\n\n      case 'prerender-runtime':\n        return delayUntilRuntimeStage(\n          workUnitStore,\n          createRenderPathname(underlyingPathname)\n        )\n      case 'request':\n        return createRenderPathname(underlyingPathname)\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nfunction createPrerenderPathname(\n  underlyingPathname: string,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<string> {\n  switch (prerenderStore.type) {\n    case 'prerender-client':\n      throw new InvariantError(\n        'createPrerenderPathname was called inside a client component scope.'\n      )\n    case 'prerender': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams && fallbackParams.size > 0) {\n        return makeHangingPromise<string>(\n          prerenderStore.renderSignal,\n          workStore.route,\n          '`pathname`'\n        )\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams && fallbackParams.size > 0) {\n        return makeErroringPathname(workStore, prerenderStore.dynamicTracking)\n      }\n      break\n    }\n    case 'prerender-legacy':\n      break\n    default:\n      prerenderStore satisfies never\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return Promise.resolve(underlyingPathname)\n}\n\nfunction makeErroringPathname<T>(\n  workStore: WorkStore,\n  dynamicTracking: null | DynamicTrackingState\n): Promise<T> {\n  let reject: null | ((reason: unknown) => void) = null\n  const promise = new Promise<T>((_, re) => {\n    reject = re\n  })\n\n  const originalThen = promise.then.bind(promise)\n\n  // We instrument .then so that we can generate a tracking event only if you actually\n  // await this promise, not just that it is created.\n  promise.then = (onfulfilled, onrejected) => {\n    if (reject) {\n      try {\n        postponeWithTracking(\n          workStore.route,\n          'metadata relative url resolving',\n          dynamicTracking\n        )\n      } catch (error) {\n        reject(error)\n        reject = null\n      }\n    }\n    return originalThen(onfulfilled, onrejected)\n  }\n\n  // We wrap in a noop proxy to trick the runtime into thinking it\n  // isn't a native promise (it's not really). This is so that awaiting\n  // the promise will call the `then` property triggering the lazy postpone\n  return new Proxy(promise, {})\n}\n\nfunction createRenderPathname(underlyingPathname: string): Promise<string> {\n  return Promise.resolve(underlyingPathname)\n}\n"], "names": ["delayUntilRuntimeStage", "postponeWithTracking", "throwInvariantForMissingStore", "workUnitAsyncStorage", "makeHangingPromise", "InvariantError", "createServerPathnameForMetadata", "underlyingPathname", "workStore", "workUnitStore", "getStore", "type", "createPrerenderPathname", "createRenderPathname", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "renderSignal", "route", "makeErroringPathname", "dynamicTracking", "Promise", "resolve", "reject", "promise", "_", "re", "originalThen", "then", "bind", "onfulfilled", "onrejected", "error", "Proxy"], "mappings": "AAEA,SACEA,sBAAsB,EACtBC,oBAAoB,QAEf,kCAAiC;AAExC,SACEC,6BAA6B,EAC7BC,oBAAoB,QAEf,iDAAgD;AACvD,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,cAAc,QAAQ,mCAAkC;AAEjE,OAAO,SAASC,gCACdC,kBAA0B,EAC1BC,SAAoB;IAEpB,MAAMC,gBAAgBN,qBAAqBO,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAoB;oBACvB,OAAOC,wBACLL,oBACAC,WACAC;gBAEJ;YACA,KAAK;YACL,KAAK;YACL,KAAK;g<PERSON><PERSON>,MAAM,qBAEL,CAFK,IAAIJ,eACR,4EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YAEF,KAAK;gBACH,OAAOL,uBACLS,eACAI,qBAAqBN;YAEzB,KAAK;gBACH,OAAOM,qBAAqBN;YAC9B;gBACEE;QACJ;IACF;IACAP;AACF;AAEA,SAASU,wBACPL,kBAA0B,EAC1BC,SAAoB,EACpBM,cAAoC;IAEpC,OAAQA,eAAeH,IAAI;QACzB,KAAK;YACH,MAAM,qBAEL,CAFK,IAAIN,eACR,wEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YAAa;gBAChB,MAAMU,iBAAiBD,eAAeE,mBAAmB;gBACzD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;oBAC7C,OAAOb,mBACLU,eAAeI,YAAY,EAC3BV,UAAUW,KAAK,EACf;gBAEJ;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,MAAMJ,iBAAiBD,eAAeE,mBAAmB;gBACzD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;oBAC7C,OAAOG,qBAAqBZ,WAAWM,eAAeO,eAAe;gBACvE;gBACA;YACF;QACA,KAAK;YACH;QACF;YACEP;IACJ;IAEA,qFAAqF;IACrF,OAAOQ,QAAQC,OAAO,CAAChB;AACzB;AAEA,SAASa,qBACPZ,SAAoB,EACpBa,eAA4C;IAE5C,IAAIG,SAA6C;IACjD,MAAMC,UAAU,IAAIH,QAAW,CAACI,GAAGC;QACjCH,SAASG;IACX;IAEA,MAAMC,eAAeH,QAAQI,IAAI,CAACC,IAAI,CAACL;IAEvC,oFAAoF;IACpF,mDAAmD;IACnDA,QAAQI,IAAI,GAAG,CAACE,aAAaC;QAC3B,IAAIR,QAAQ;YACV,IAAI;gBACFvB,qBACEO,UAAUW,KAAK,EACf,mCACAE;YAEJ,EAAE,OAAOY,OAAO;gBACdT,OAAOS;gBACPT,SAAS;YACX;QACF;QACA,OAAOI,aAAaG,aAAaC;IACnC;IAEA,gEAAgE;IAChE,qEAAqE;IACrE,yEAAyE;IACzE,OAAO,IAAIE,MAAMT,SAAS,CAAC;AAC7B;AAEA,SAASZ,qBAAqBN,kBAA0B;IACtD,OAAOe,QAAQC,OAAO,CAAChB;AACzB", "ignoreList": [0]}