{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/mdx-components.tsx"], "sourcesContent": ["import defaultMdxComponents from 'fumadocs-ui/mdx';\nimport type { MDXComponents } from 'mdx/types';\n\n// use this function to get MDX components, you will need it for rendering MDX\nexport function getMDXComponents(components?: MDXComponents): MDXComponents {\n  return {\n    ...defaultMdxComponents,\n    ...components,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAIO,SAAS,iBAAiB,UAA0B;IACzD,OAAO;QACL,GAAG,6IAAA,CAAA,UAAoB;QACvB,GAAG,UAAU;IACf;AACF", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/app/docs/%5B%5B...slug%5D%5D/page.tsx"], "sourcesContent": ["import { source } from '@/lib/source';\nimport {\n  DocsPage,\n  DocsBody,\n  DocsDescription,\n  DocsTitle,\n} from 'fumadocs-ui/page';\nimport { Metadata } from 'next';\nimport { notFound } from 'next/navigation';\nimport { createRelativeLink } from 'fumadocs-ui/mdx';\nimport { getMDXComponents } from '@/mdx-components';\n\nexport default async function Page(props: {\n  params: Promise<{ slug?: string[] }>;\n}) {\n  const params = await props.params;\n  const page = source.getPage(params.slug);\n  if (!page) notFound();\n\n  const MDXContent = page.data.body;\n\n  return (\n    <DocsPage toc={page.data.toc} full={page.data.full}>\n      <DocsTitle>{page.data.title}</DocsTitle>\n      <DocsDescription>{page.data.description}</DocsDescription>\n      <DocsBody>\n        <MDXContent\n          components={getMDXComponents({\n            // this allows you to link to other pages with relative file paths\n            a: createRelativeLink(source, page),\n          })}\n        />\n      </DocsBody>\n    </DocsPage>\n  );\n}\n\nexport async function generateStaticParams() {\n  return source.generateParams();\n}\n\nexport async function generateMetadata(props: {\n  params: Promise<{ slug?: string[] }>;\n}): Promise<Metadata> {\n  const params = await props.params;\n  const page = source.getPage(params.slug);\n  if (!page) notFound();\n\n  return {\n    title: page.data.title,\n    description: page.data.description,\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAOA;AAAA;AACA;AAAA;AACA;;;;;;;AAEe,eAAe,KAAK,KAElC;IACC,MAAM,SAAS,MAAM,MAAM,MAAM;IACjC,MAAM,OAAO,6GAAA,CAAA,SAAM,CAAC,OAAO,CAAC,OAAO,IAAI;IACvC,IAAI,CAAC,MAAM,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAElB,MAAM,aAAa,KAAK,IAAI,CAAC,IAAI;IAEjC,qBACE,8OAAC,8IAAA,CAAA,WAAQ;QAAC,KAAK,KAAK,IAAI,CAAC,GAAG;QAAE,MAAM,KAAK,IAAI,CAAC,IAAI;;0BAChD,8OAAC,8IAAA,CAAA,YAAS;0BAAE,KAAK,IAAI,CAAC,KAAK;;;;;;0BAC3B,8OAAC,8IAAA,CAAA,kBAAe;0BAAE,KAAK,IAAI,CAAC,WAAW;;;;;;0BACvC,8OAAC,8IAAA,CAAA,WAAQ;0BACP,cAAA,8OAAC;oBACC,YAAY,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD,EAAE;wBAC3B,kEAAkE;wBAClE,GAAG,CAAA,GAAA,uKAAA,CAAA,qBAAkB,AAAD,EAAE,6GAAA,CAAA,SAAM,EAAE;oBAChC;;;;;;;;;;;;;;;;;AAKV;AAEO,eAAe;IACpB,OAAO,6GAAA,CAAA,SAAM,CAAC,cAAc;AAC9B;AAEO,eAAe,iBAAiB,KAEtC;IACC,MAAM,SAAS,MAAM,MAAM,MAAM;IACjC,MAAM,OAAO,6GAAA,CAAA,SAAM,CAAC,OAAO,CAAC,OAAO,IAAI;IACvC,IAAI,CAAC,MAAM,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAElB,OAAO;QACL,OAAO,KAAK,IAAI,CAAC,KAAK;QACtB,aAAa,KAAK,IAAI,CAAC,WAAW;IACpC;AACF", "debugId": null}}]}