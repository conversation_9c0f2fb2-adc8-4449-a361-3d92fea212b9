{"version": 3, "file": "parse.d.ts", "sourceRoot": "", "sources": ["../../src/parser/parse.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAA4G,kBAAkB,EAAE,cAAc,EAA2D,qBAAqB,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,mBAAmB,EAAC,MAAM,0BAA0B,CAAC;AAG5U,OAAO,EAAC,YAAY,EAAE,sBAAsB,EAAE,cAAc,EAAC,MAAM,iBAAiB,CAAC;AAGrF,KAAK,IAAI,GACP,mBAAmB,GACnB,eAAe,GACf,aAAa,GACb,iBAAiB,GACjB,kBAAkB,GAClB,aAAa,GACb,kBAAkB,GAClB,uBAAuB,GACvB,gBAAgB,GAChB,aAAa,GACb,SAAS,GACT,SAAS,GACT,uBAAuB,GACvB,gBAAgB,GAChB,cAAc,GACd,SAAS,GACT,cAAc,CAAC;AAEjB,KAAK,YAAY,GAAG,SAAS,CAAC;AAE9B,KAAK,UAAU,GACb,wBAAwB,GACxB,eAAe,GACf,kBAAkB,GAClB,uBAAuB,GACvB,cAAc,CAAC;AAGjB,KAAK,wBAAwB,GAC3B,mBAAmB,GAAG,+CAA+C;AACrE,kBAAkB,GAClB,SAAS,GACT,uBAAuB,GACvB,SAAS,CAAC;AAEZ,KAAK,sBAAsB,GACzB,mBAAmB,GACnB,aAAa,GACb,iBAAiB,GACjB,kBAAkB,GAClB,aAAa,GACb,kBAAkB,GAClB,gBAAgB,GAChB,aAAa,GACb,SAAS,GACT,uBAAuB,GACvB,gBAAgB,GAChB,cAAc,GACd,cAAc,CAAC;AAEjB,KAAK,yBAAyB,GAC5B,aAAa,GACb,kBAAkB,GAClB,uBAAuB,GACvB,gBAAgB,CAAC;AAGnB,KAAK,gBAAgB,GACnB,mBAAmB,GACnB,iBAAiB,GACjB,kBAAkB,GAClB,aAAa,GACb,kBAAkB,GAClB,gBAAgB,GAChB,SAAS,GACT,cAAc,GACd,cAAc,CAAC;AAGjB,KAAK,uBAAuB,GAC1B,UAAU,CAAC;AAEb,KAAK,iBAAiB,GACpB,UAAU,GACV,YAAY,GACZ,cAAc,GACd,YAAY,GACZ,oBAAoB,GACpB,cAAc,GACd,uBAAuB,GACvB,eAAe,CAAC;AAElB,KAAK,sBAAsB,GACzB,OAAO,GACP,cAAc,CAAC;AAEjB,KAAK,oBAAoB,GAAG,qBAAqB,CAAC;AAElD,KAAK,iBAAiB,GAAG,kBAAkB,CAAC;AAE5C,KAAK,2BAA2B,GAC9B,WAAW,GACX,YAAY,CAAC;AAEf,KAAK,oBAAoB,GAAG,qBAAqB,CAAC;AAElD,KAAK,kBAAkB,GAAG,mBAAmB,CAAC;AAE9C,KAAK,kBAAkB,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AA0B9C,KAAK,YAAY,GAAG;IAClB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,6BAA6B,CAAC,EAAE,OAAO,CAAC;IACxC,KAAK,CAAC,EAAE;QACN,YAAY,CAAC,EAAE,OAAO,CAAC;QACvB,UAAU,CAAC,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,wBAAwB,CAAC,EAAE,OAAO,CAAC;IACnC,0BAA0B,CAAC,EAAE,OAAO,CAAC;IACrC,kBAAkB,CAAC,EAAE,kBAAkB,GAAG,IAAI,CAAC;CAChD,CAAC;AAEF,iBAAS,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,YAAiB,GAAG,YAAY,CAiHxE;AAoSD,KAAK,mBAAmB,GAAG;IACzB,IAAI,EAAE,iBAAiB,CAAC;IACxB,IAAI,EAAE,uBAAuB,CAAC;IAC9B,IAAI,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;CAC9B,CAAC;AACF,iBAAS,qBAAqB,CAAC,IAAI,EAAE,uBAAuB,EAAE,OAAO,CAAC,EAAE;IACtE,IAAI,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;CAC/B,GAAG,mBAAmB,CAStB;AAED,KAAK,eAAe,GAAG;IACrB,IAAI,EAAE,aAAa,CAAC;IACpB,IAAI,EAAE,KAAK,CAAC,sBAAsB,CAAC,CAAC;CACrC,CAAC;AACF,iBAAS,iBAAiB,CAAC,OAAO,CAAC,EAAE;IACnC,IAAI,CAAC,EAAE,KAAK,CAAC,sBAAsB,CAAC,CAAC;CACtC,GAAG,eAAe,CAKlB;AAED,KAAK,aAAa,GAAG;IACnB,IAAI,EAAE,WAAW,CAAC;IAClB,IAAI,EAAE,iBAAiB,CAAC;IACxB,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB,CAAC;AACF,iBAAS,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE;IAC1D,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB,GAAG,aAAa,CAShB;AAED,KAAK,iBAAiB,GAAG;IACvB,IAAI,EAAE,eAAe,CAAC;IACtB,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC;IACrB,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB,CAAC;AACF,iBAAS,mBAAmB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,CAAC,EAAE;IAC3D,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB,GAAG,iBAAiB,CAOpB;AAED,KAAK,kBAAkB,GAAG;IACxB,IAAI,EAAE,gBAAgB,CAAC;IACvB,IAAI,CAAC,EAAE,KAAK,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,IAAI,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;CAC9B,CAAC;AACF,iBAAS,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE;IACtD,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,IAAI,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;CAC/B,GAAG,kBAAkB,CAgBrB;AAED,KAAK,aAAa,GAAG;IACnB,IAAI,EAAE,WAAW,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AACF,iBAAS,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE;IACnD,YAAY,CAAC,EAAE,OAAO,CAAC;CACxB,GAAG,aAAa,CAmBhB;AAED,KAAK,kBAAkB,GAAG;IACxB,IAAI,EAAE,gBAAgB,CAAC;IACvB,IAAI,EAAE,sBAAsB,CAAC;IAC7B,MAAM,EAAE,OAAO,CAAC;IAChB,IAAI,EAAE,KAAK,CAAC,yBAAyB,CAAC,CAAC;CACxC,CAAC;AACF,iBAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;IACtC,IAAI,CAAC,EAAE,sBAAsB,CAAC;IAC9B,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,KAAK,CAAC,yBAAyB,CAAC,CAAC;CACzC,GAAG,kBAAkB,CAYrB;AAED,KAAK,uBAAuB,GAAG;IAC7B,IAAI,EAAE,qBAAqB,CAAC;IAC5B,GAAG,EAAE,aAAa,CAAC;IACnB,GAAG,EAAE,aAAa,CAAC;CACpB,CAAC;AACF,iBAAS,yBAAyB,CAAC,GAAG,EAAE,aAAa,EAAE,GAAG,EAAE,aAAa,GAAG,uBAAuB,CASlG;AAED,KAAK,qBAAqB,GAAG;IAC3B,IAAI,EAAE,cAAc,CAAC;IACrB,IAAI,EAAE,OAAO,GAAG,UAAU,CAAC;IAC3B,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,OAAO,CAAC;IAChB,cAAc,CAAC,EAAE,KAAK,CAAC;CACxB,CAAC;AACF,KAAK,uBAAuB,GAAG;IAC7B,IAAI,EAAE,cAAc,CAAC;IACrB,IAAI,EAAE,OAAO,CAAC,oBAAoB,EAAE,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC;IACnE,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,cAAc,CAAC,EAAE,OAAO,CAAC;CAC1B,CAAC;AACF,KAAK,gBAAgB,GAAG,qBAAqB,GAAG,uBAAuB,CAAC;AACxE;;EAEE;AACF,iBAAS,kBAAkB,CAAC,IAAI,EAAE,uBAAuB,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE;IAC3E,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB,GAAG,uBAAuB,CAsB1B;AAED,KAAK,aAAa,GAAG;IACnB,IAAI,EAAE,WAAW,CAAC;CACnB,GAAG,CAAC;IACH,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,KAAK,CAAC;CACf,GAAG;IACF,IAAI,EAAE,OAAO,CAAC;IACd,KAAK,EAAE,kBAAkB,CAAC;CAC3B,CAAC,CAAC;AACH,iBAAS,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,OAAO,GAAE;IAAC,KAAK,CAAC,EAAE,kBAAkB,CAAA;CAAM,GAAG,aAAa,CAiB3G;AAED,KAAK,SAAS,GAAG;IACf,IAAI,EAAE,OAAO,CAAC;CACf,GAAG,cAAc,CAAC;AACnB,iBAAS,WAAW,CAAC,KAAK,EAAE,cAAc,GAAG,SAAS,CAKrD;AAED,KAAK,SAAS,GAAG;IACf,IAAI,EAAE,OAAO,CAAC;IACd,IAAI,CAAC,EAAE,KAAK,CAAC;IACb,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,kBAAkB,CAAC;IAC3B,IAAI,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;CAC9B,CAAC;AACF,iBAAS,WAAW,CAAC,OAAO,CAAC,EAAE;IAC7B,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,kBAAkB,CAAC;IAC3B,IAAI,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;CAC/B,GAAG,SAAS,CAYZ;AAED,KAAK,uBAAuB,GAAG;IAC7B,IAAI,EAAE,qBAAqB,CAAC;IAC5B,IAAI,EAAE,2BAA2B,CAAC;IAClC,MAAM,EAAE,OAAO,CAAC;IAChB,IAAI,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;CAC9B,CAAC;AACF,iBAAS,yBAAyB,CAAC,OAAO,CAAC,EAAE;IAC3C,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;CAC/B,GAAG,uBAAuB,CAY1B;AAED,KAAK,gBAAgB,GAAG;IACtB,IAAI,EAAE,cAAc,CAAC;IACrB,IAAI,EAAE,oBAAoB,CAAC;IAC3B,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;IACnB,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC;CAC1C,CAAC;AACF,iBAAS,kBAAkB,CACzB,IAAI,EAAE,oBAAoB,EAC1B,GAAG,EAAE,MAAM,GAAG,IAAI,EAClB,IAAI,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,GAClC,gBAAgB,CAOlB;AAED,iBAAS,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE;IAChD,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB,GAAG,qBAAqB,GAAG;IAAC,IAAI,EAAE,OAAO,CAAA;CAAC,CAW1C;AAED,KAAK,cAAc,GAAG;IACpB,IAAI,EAAE,YAAY,CAAC;IACnB,IAAI,EAAE,kBAAkB,CAAC;IACzB,GAAG,EAAE,MAAM,CAAC;IACZ,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,EAAE,gBAAgB,CAAC;CACxB,CAAC;AACF,iBAAS,gBAAgB,CAAC,IAAI,EAAE,kBAAkB,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,GAAG,cAAc,CAWpH;AAED,KAAK,SAAS,GAAG;IACf,IAAI,EAAE,OAAO,CAAC;IACd,IAAI,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;IAC7B,KAAK,EAAE,SAAS,CAAC;CAClB,CAAC;AACF,iBAAS,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE;IAC/C,IAAI,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;CAC/B,GAAG,SAAS,CAMZ;AAED,KAAK,cAAc,GAAG;IACpB,IAAI,EAAE,YAAY,CAAC;IACnB,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC;CACtB,CAAC;AACF,iBAAS,gBAAgB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,cAAc,CAK9D;AAED,KAAK,4BAA4B,GAAG;IAClC,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,6BAA6B,CAAC,EAAE,OAAO,CAAC;IACxC,0BAA0B,CAAC,EAAE,OAAO,CAAC;IACrC,kBAAkB,CAAC,EAAE,kBAAkB,GAAG,IAAI,CAAC;CAChD,CAAC;AACF,iBAAS,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,4BAA4B,GAAG,qBAAqB,GAAG;IAAC,IAAI,EAAE,UAAU,CAAA;CAAC,CAuB/H;AAsED;;EAEE;AACF,iBAAS,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAElC;AAgBD,OAAO,EACL,KAAK,mBAAmB,EACxB,KAAK,eAAe,EACpB,KAAK,wBAAwB,EAC7B,KAAK,sBAAsB,EAC3B,KAAK,aAAa,EAClB,KAAK,iBAAiB,EACtB,KAAK,kBAAkB,EACvB,KAAK,yBAAyB,EAC9B,KAAK,kBAAkB,EACvB,KAAK,uBAAuB,EAC5B,KAAK,aAAa,EAClB,KAAK,gBAAgB,EACrB,KAAK,aAAa,EAClB,KAAK,SAAS,EACd,KAAK,SAAS,EACd,KAAK,uBAAuB,EAC5B,KAAK,gBAAgB,EACrB,KAAK,IAAI,EACT,KAAK,uBAAuB,EAC5B,KAAK,iBAAiB,EACtB,KAAK,sBAAsB,EAC3B,KAAK,oBAAoB,EACzB,KAAK,iBAAiB,EACtB,KAAK,2BAA2B,EAChC,KAAK,kBAAkB,EACvB,KAAK,YAAY,EACjB,KAAK,UAAU,EACf,KAAK,YAAY,EACjB,KAAK,gBAAgB,EACrB,KAAK,cAAc,EACnB,KAAK,SAAS,EACd,KAAK,cAAc,EACnB,KAAK,kBAAkB,EACvB,qBAAqB,EACrB,iBAAiB,EACjB,eAAe,EACf,mBAAmB,EACnB,oBAAoB,EACpB,eAAe,EACf,oBAAoB,EACpB,yBAAyB,EACzB,kBAAkB,EAClB,eAAe,EACf,WAAW,EACX,WAAW,EACX,yBAAyB,EACzB,kBAAkB,EAClB,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,EACX,gBAAgB,EAChB,qBAAqB,EACrB,YAAY,EACZ,sBAAsB,EACtB,cAAc,EACd,KAAK,EACL,IAAI,GACL,CAAC"}