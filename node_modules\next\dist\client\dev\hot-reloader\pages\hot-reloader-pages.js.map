{"version": 3, "sources": ["../../../../../src/client/dev/hot-reloader/pages/hot-reloader-pages.ts"], "sourcesContent": ["// TODO: Remove use of `any` type. Fix no-use-before-define violations.\n/* eslint-disable @typescript-eslint/no-use-before-define */\n/**\n * MIT License\n *\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n/// <reference types=\"webpack/module.d.ts\" />\n\n// This file is a modified version of the Create React App HMR dev client that\n// can be found here:\n// https://github.com/facebook/create-react-app/blob/v3.4.1/packages/react-dev-utils/webpackHotDevClient.js\n\n/// <reference types=\"webpack/module.d.ts\" />\n\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { register } from '../../../../next-devtools/userspace/pages/pages-dev-overlay-setup'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { addMessageListener, sendMessage } from './websocket'\nimport formatWebpackMessages from '../../../../shared/lib/format-webpack-messages'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport {\n  REACT_REFRESH_FULL_RELOAD,\n  REACT_REFRESH_FULL_RELOAD_FROM_ERROR,\n  reportInvalidHmrMessage,\n} from '../shared'\nimport { RuntimeErrorHandler } from '../../runtime-error-handler'\nimport reportHmrLatency from '../../report-hmr-latency'\nimport { TurbopackHmr } from '../turbopack-hot-reloader-common'\n\n// This alternative WebpackDevServer combines the functionality of:\n// https://github.com/webpack/webpack-dev-server/blob/webpack-1/client/index.js\n// https://github.com/webpack/webpack/blob/webpack-1/hot/dev-server.js\n\n// It only supports their simplest configuration (hot updates on same server).\n// It makes some opinionated choices on top, like adding a syntax error overlay\n// that looks similar to our console output. The error overlay is inspired by:\n// https://github.com/glenjamin/webpack-hot-middleware\n\ndeclare global {\n  interface Window {\n    __nextDevClientId: number\n  }\n}\n\nwindow.__nextDevClientId = Math.round(Math.random() * 100 + Date.now())\n\nlet customHmrEventHandler: any\nlet turbopackMessageListeners: ((msg: TurbopackMsgToBrowser) => void)[] = []\nexport default function connect() {\n  register()\n\n  addMessageListener((payload) => {\n    if (!('action' in payload)) {\n      return\n    }\n\n    try {\n      processMessage(payload)\n    } catch (err: unknown) {\n      reportInvalidHmrMessage(payload, err)\n    }\n  })\n\n  return {\n    subscribeToHmrEvent(handler: any) {\n      customHmrEventHandler = handler\n    },\n    onUnrecoverableError() {\n      RuntimeErrorHandler.hadRuntimeError = true\n    },\n    addTurbopackMessageListener(cb: (msg: TurbopackMsgToBrowser) => void) {\n      turbopackMessageListeners.push(cb)\n    },\n    sendTurbopackMessage(msg: string) {\n      sendMessage(msg)\n    },\n    handleUpdateError(err: unknown) {\n      performFullReload(err)\n    },\n  }\n}\n\n// Remember some state related to hot module replacement.\nvar isFirstCompilation = true\nvar mostRecentCompilationHash: string | null = null\nvar hasCompileErrors = false\n\nfunction clearOutdatedErrors() {\n  // Clean up outdated compile errors, if any.\n  if (typeof console !== 'undefined' && typeof console.clear === 'function') {\n    if (hasCompileErrors) {\n      console.clear()\n    }\n  }\n}\n\n// Successful compilation.\nfunction handleSuccess() {\n  clearOutdatedErrors()\n  hasCompileErrors = false\n\n  if (process.env.TURBOPACK) {\n    const hmrUpdate = turbopackHmr!.onBuilt()\n    if (hmrUpdate != null) {\n      reportHmrLatency(\n        sendMessage,\n        [...hmrUpdate.updatedModules],\n        hmrUpdate.startMsSinceEpoch,\n        hmrUpdate.endMsSinceEpoch,\n        hmrUpdate.hasUpdates\n      )\n    }\n    dispatcher.onBuildOk()\n  } else {\n    const isHotUpdate =\n      !isFirstCompilation ||\n      (window.__NEXT_DATA__.page !== '/_error' && isUpdateAvailable())\n\n    // Attempt to apply hot updates or reload.\n    if (isHotUpdate) {\n      tryApplyUpdatesWebpack()\n    }\n  }\n\n  isFirstCompilation = false\n}\n\n// Compilation with warnings (e.g. ESLint).\nfunction handleWarnings(warnings: any) {\n  clearOutdatedErrors()\n\n  const isHotUpdate = !isFirstCompilation\n  isFirstCompilation = false\n  hasCompileErrors = false\n\n  function printWarnings() {\n    // Print warnings to the console.\n    const formatted = formatWebpackMessages({\n      warnings: warnings,\n      errors: [],\n    })\n\n    if (typeof console !== 'undefined' && typeof console.warn === 'function') {\n      for (let i = 0; i < formatted.warnings?.length; i++) {\n        if (i === 5) {\n          console.warn(\n            'There were more warnings in other files.\\n' +\n              'You can find a complete log in the terminal.'\n          )\n          break\n        }\n        console.warn(stripAnsi(formatted.warnings[i]))\n      }\n    }\n  }\n\n  printWarnings()\n\n  // Attempt to apply hot updates or reload.\n  if (isHotUpdate) {\n    tryApplyUpdatesWebpack()\n  }\n}\n\n// Compilation with errors (e.g. syntax error or missing modules).\nfunction handleErrors(errors: any) {\n  clearOutdatedErrors()\n\n  isFirstCompilation = false\n  hasCompileErrors = true\n\n  // \"Massage\" webpack messages.\n  var formatted = formatWebpackMessages({\n    errors: errors,\n    warnings: [],\n  })\n\n  // Only show the first error.\n\n  dispatcher.onBuildError(formatted.errors[0])\n\n  // Also log them to the console.\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    for (var i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n  }\n\n  // Do not attempt to reload now.\n  // We will reload on next success instead.\n  if (process.env.__NEXT_TEST_MODE) {\n    if (self.__NEXT_HMR_CB) {\n      self.__NEXT_HMR_CB(formatted.errors[0])\n      self.__NEXT_HMR_CB = null\n    }\n  }\n}\n\nlet webpackStartMsSinceEpoch: number | null = null\nconst turbopackHmr: TurbopackHmr | null = process.env.TURBOPACK\n  ? new TurbopackHmr()\n  : null\nlet isrManifest: Record<string, boolean> = {}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\nexport function handleStaticIndicator() {\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    const routeInfo = window.next.router.components[window.next.router.pathname]\n    const pageComponent = routeInfo?.Component\n    const appComponent = window.next.router.components['/_app']?.Component\n    const isDynamicPage =\n      Boolean(pageComponent?.getInitialProps) || Boolean(routeInfo?.__N_SSP)\n    const hasAppGetInitialProps =\n      Boolean(appComponent?.getInitialProps) &&\n      appComponent?.getInitialProps !== appComponent?.origGetInitialProps\n\n    const isPageStatic =\n      window.location.pathname in isrManifest ||\n      (!isDynamicPage && !hasAppGetInitialProps)\n\n    dispatcher.onStaticIndicator(isPageStatic)\n  }\n}\n\n/** Handles messages from the server for the Pages Router. */\nfunction processMessage(obj: HMR_ACTION_TYPES) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      isrManifest = obj.data\n      handleStaticIndicator()\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      dispatcher.buildingIndicatorShow()\n\n      if (process.env.TURBOPACK) {\n        turbopackHmr!.onBuilding()\n      } else {\n        webpackStartMsSinceEpoch = Date.now()\n        console.log('[Fast Refresh] rebuilding')\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      dispatcher.buildingIndicatorHide()\n\n      if (obj.hash) handleAvailableHash(obj.hash)\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator)\n      if ('devToolsConfig' in obj)\n        dispatcher.onDevToolsConfig(obj.devToolsConfig)\n\n      const hasErrors = Boolean(errors && errors.length)\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleErrors(errors)\n      }\n\n      // NOTE: Turbopack does not currently send warnings\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleWarnings(warnings)\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: window.__nextDevClientId,\n        })\n      )\n      return handleSuccess()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      turbopackHmr?.onServerComponentChanges()\n      if (hasCompileErrors || RuntimeErrorHandler.hadRuntimeError) {\n        window.location.reload()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n          data: obj.data,\n        })\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      turbopackHmr!.onTurbopackMessage(obj)\n      dispatcher.onBeforeRefresh()\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n          data: obj.data,\n        })\n      }\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null)\n      }\n      dispatcher.onRefresh()\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE:\n      if (customHmrEventHandler) {\n        customHmrEventHandler(obj)\n      }\n      break\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEVTOOLS_CONFIG:\n      dispatcher.onDevToolsConfig(obj.data)\n      break\n    default:\n      obj satisfies never\n  }\n}\n\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: () => void) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: string) {\n      if (status === 'idle') {\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    module.hot.addStatusHandler(handler)\n  }\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdatesWebpack() {\n  if (!module.hot) {\n    // HotModuleReplacementPlugin is not in Webpack configuration.\n    console.error('HotModuleReplacementPlugin is not in Webpack configuration.')\n    // window.location.reload();\n    return\n  }\n\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    dispatcher.onBuildOk()\n    return\n  }\n\n  function handleApplyUpdates(\n    err: any,\n    updatedModules: (string | number)[] | null\n  ) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || updatedModules == null) {\n      if (err) {\n        console.warn(REACT_REFRESH_FULL_RELOAD)\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err)\n      return\n    }\n\n    dispatcher.onBuildOk()\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdatesWebpack()\n      return\n    }\n\n    dispatcher.onRefresh()\n    reportHmrLatency(\n      sendMessage,\n      updatedModules,\n      webpackStartMsSinceEpoch!,\n      Date.now()\n    )\n\n    if (process.env.__NEXT_TEST_MODE) {\n      afterApplyUpdates(() => {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      })\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: (string | number)[] | null) => {\n      if (updatedModules == null) {\n        return null\n      }\n\n      // We should always handle an update, even if updatedModules is empty (but\n      // non-null) for any reason. That's what webpack would normally do:\n      // https://github.com/webpack/webpack/blob/3aa6b6bc3a64/lib/hmr/HotModuleReplacement.runtime.js#L296-L298\n      dispatcher.onBeforeRefresh()\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: (string | number)[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\nexport function performFullReload(err: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  window.location.reload()\n}\n"], "names": ["connect", "handleStaticIndicator", "performFullReload", "window", "__nextDevClientId", "Math", "round", "random", "Date", "now", "customHmrEventHandler", "turbopackMessageListeners", "register", "addMessageListener", "payload", "processMessage", "err", "reportInvalidHmrMessage", "subscribeToHmrEvent", "handler", "onUnrecoverableError", "RuntimeError<PERSON>andler", "hadRuntimeError", "addTurbopackMessageListener", "cb", "push", "sendTurbopackMessage", "msg", "sendMessage", "handleUpdateError", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "console", "clear", "handleSuccess", "process", "env", "TURBOPACK", "hmrUpdate", "turbopackHmr", "onBuilt", "reportHmrLatency", "updatedModules", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdates", "dispatcher", "onBuildOk", "isHotUpdate", "__NEXT_DATA__", "page", "isUpdateAvailable", "tryApplyUpdatesWebpack", "handleWarnings", "warnings", "printWarnings", "formatted", "formatWebpackMessages", "errors", "warn", "i", "length", "stripAnsi", "handleErrors", "onBuildError", "error", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "webpackStartMsSinceEpoch", "TurbopackHmr", "isrManifest", "handleAvailableHash", "hash", "__NEXT_DEV_INDICATOR", "routeInfo", "next", "router", "components", "pathname", "pageComponent", "Component", "appComponent", "isDynamicPage", "Boolean", "getInitialProps", "__N_SSP", "hasAppGetInitialProps", "origGetInitialProps", "isPageStatic", "location", "onStaticIndicator", "obj", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "ISR_MANIFEST", "data", "BUILDING", "buildingIndicatorShow", "onBuilding", "log", "BUILT", "SYNC", "buildingIndicatorHide", "onVersionInfo", "versionInfo", "onDevIndicator", "devIndicator", "onDevToolsConfig", "devToolsConfig", "hasErrors", "JSON", "stringify", "event", "errorCount", "clientId", "hasWarnings", "warningCount", "SERVER_COMPONENT_CHANGES", "onServerComponentChanges", "reload", "SERVER_ERROR", "errorJSON", "message", "stack", "parse", "Error", "TURBOPACK_CONNECTED", "listener", "type", "TURBOPACK_MESSAGE", "onTurbopackMessage", "onBeforeRefresh", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "onRefresh", "ADDED_PAGE", "REMOVED_PAGE", "RELOAD_PAGE", "DEV_PAGES_MANIFEST_UPDATE", "DEVTOOLS_CONFIG", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "handleApplyUpdates", "REACT_REFRESH_FULL_RELOAD", "check", "then", "apply", "stackTrace", "split", "slice", "join", "dependency<PERSON><PERSON>n", "undefined"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,6CAA6C;AAE7C,8EAA8E;AAC9E,qBAAqB;AACrB,2GAA2G;AAE3G,6CAA6C;;;;;;;;;;;;;;;;;IAwC7C,OAgCC;eAhCuBA;;IAkKRC,qBAAqB;eAArBA;;IA4PAC,iBAAiB;eAAjBA;;;;8BApcW;sCACF;oEACH;2BAC0B;gFACd;kCACU;wBASrC;qCAC6B;2EACP;4CACA;AAiB7BC,OAAOC,iBAAiB,GAAGC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEpE,IAAIC;AACJ,IAAIC,4BAAsE,EAAE;AAC7D,SAASX;IACtBY,IAAAA,8BAAQ;IAERC,IAAAA,6BAAkB,EAAC,CAACC;QAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;YAC1B;QACF;QAEA,IAAI;YACFC,eAAeD;QACjB,EAAE,OAAOE,KAAc;YACrBC,IAAAA,+BAAuB,EAACH,SAASE;QACnC;IACF;IAEA,OAAO;QACLE,qBAAoBC,OAAY;YAC9BT,wBAAwBS;QAC1B;QACAC;YACEC,wCAAmB,CAACC,eAAe,GAAG;QACxC;QACAC,6BAA4BC,EAAwC;YAClEb,0BAA0Bc,IAAI,CAACD;QACjC;QACAE,sBAAqBC,GAAW;YAC9BC,IAAAA,sBAAW,EAACD;QACd;QACAE,mBAAkBb,GAAY;YAC5Bd,kBAAkBc;QACpB;IACF;AACF;AAEA,yDAAyD;AACzD,IAAIc,qBAAqB;AACzB,IAAIC,4BAA2C;AAC/C,IAAIC,mBAAmB;AAEvB,SAASC;IACP,4CAA4C;IAC5C,IAAI,OAAOC,YAAY,eAAe,OAAOA,QAAQC,KAAK,KAAK,YAAY;QACzE,IAAIH,kBAAkB;YACpBE,QAAQC,KAAK;QACf;IACF;AACF;AAEA,0BAA0B;AAC1B,SAASC;IACPH;IACAD,mBAAmB;IAEnB,IAAIK,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,MAAMC,YAAYC,aAAcC,OAAO;QACvC,IAAIF,aAAa,MAAM;YACrBG,IAAAA,yBAAgB,EACdf,sBAAW,EACX;mBAAIY,UAAUI,cAAc;aAAC,EAC7BJ,UAAUK,iBAAiB,EAC3BL,UAAUM,eAAe,EACzBN,UAAUO,UAAU;QAExB;QACAC,wBAAU,CAACC,SAAS;IACtB,OAAO;QACL,MAAMC,cACJ,CAACpB,sBACA3B,OAAOgD,aAAa,CAACC,IAAI,KAAK,aAAaC;QAE9C,0CAA0C;QAC1C,IAAIH,aAAa;YACfI;QACF;IACF;IAEAxB,qBAAqB;AACvB;AAEA,2CAA2C;AAC3C,SAASyB,eAAeC,QAAa;IACnCvB;IAEA,MAAMiB,cAAc,CAACpB;IACrBA,qBAAqB;IACrBE,mBAAmB;IAEnB,SAASyB;QACP,iCAAiC;QACjC,MAAMC,YAAYC,IAAAA,8BAAqB,EAAC;YACtCH,UAAUA;YACVI,QAAQ,EAAE;QACZ;QAEA,IAAI,OAAO1B,YAAY,eAAe,OAAOA,QAAQ2B,IAAI,KAAK,YAAY;gBACpDH;YAApB,IAAK,IAAII,IAAI,GAAGA,MAAIJ,sBAAAA,UAAUF,QAAQ,qBAAlBE,oBAAoBK,MAAM,GAAED,IAAK;gBACnD,IAAIA,MAAM,GAAG;oBACX5B,QAAQ2B,IAAI,CACV,+CACE;oBAEJ;gBACF;gBACA3B,QAAQ2B,IAAI,CAACG,IAAAA,kBAAS,EAACN,UAAUF,QAAQ,CAACM,EAAE;YAC9C;QACF;IACF;IAEAL;IAEA,0CAA0C;IAC1C,IAAIP,aAAa;QACfI;IACF;AACF;AAEA,kEAAkE;AAClE,SAASW,aAAaL,MAAW;IAC/B3B;IAEAH,qBAAqB;IACrBE,mBAAmB;IAEnB,8BAA8B;IAC9B,IAAI0B,YAAYC,IAAAA,8BAAqB,EAAC;QACpCC,QAAQA;QACRJ,UAAU,EAAE;IACd;IAEA,6BAA6B;IAE7BR,wBAAU,CAACkB,YAAY,CAACR,UAAUE,MAAM,CAAC,EAAE;IAE3C,gCAAgC;IAChC,IAAI,OAAO1B,YAAY,eAAe,OAAOA,QAAQiC,KAAK,KAAK,YAAY;QACzE,IAAK,IAAIL,IAAI,GAAGA,IAAIJ,UAAUE,MAAM,CAACG,MAAM,EAAED,IAAK;YAChD5B,QAAQiC,KAAK,CAACH,IAAAA,kBAAS,EAACN,UAAUE,MAAM,CAACE,EAAE;QAC7C;IACF;IAEA,gCAAgC;IAChC,0CAA0C;IAC1C,IAAIzB,QAAQC,GAAG,CAAC8B,gBAAgB,EAAE;QAChC,IAAIC,KAAKC,aAAa,EAAE;YACtBD,KAAKC,aAAa,CAACZ,UAAUE,MAAM,CAAC,EAAE;YACtCS,KAAKC,aAAa,GAAG;QACvB;IACF;AACF;AAEA,IAAIC,2BAA0C;AAC9C,MAAM9B,eAAoCJ,QAAQC,GAAG,CAACC,SAAS,GAC3D,IAAIiC,wCAAY,KAChB;AACJ,IAAIC,cAAuC,CAAC;AAE5C,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtC5C,4BAA4B4C;AAC9B;AAEO,SAAS1E;IACd,IAAIoC,QAAQC,GAAG,CAACsC,oBAAoB,EAAE;YAGfzE;QAFrB,MAAM0E,YAAY1E,OAAO2E,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC7E,OAAO2E,IAAI,CAACC,MAAM,CAACE,QAAQ,CAAC;QAC5E,MAAMC,gBAAgBL,6BAAAA,UAAWM,SAAS;QAC1C,MAAMC,gBAAejF,sCAAAA,OAAO2E,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC,QAAQ,qBAAtC7E,oCAAwCgF,SAAS;QACtE,MAAME,gBACJC,QAAQJ,iCAAAA,cAAeK,eAAe,KAAKD,QAAQT,6BAAAA,UAAWW,OAAO;QACvE,MAAMC,wBACJH,QAAQF,gCAAAA,aAAcG,eAAe,KACrCH,CAAAA,gCAAAA,aAAcG,eAAe,OAAKH,gCAAAA,aAAcM,mBAAmB;QAErE,MAAMC,eACJxF,OAAOyF,QAAQ,CAACX,QAAQ,IAAIR,eAC3B,CAACY,iBAAiB,CAACI;QAEtBzC,wBAAU,CAAC6C,iBAAiB,CAACF;IAC/B;AACF;AAEA,2DAA2D,GAC3D,SAAS5E,eAAe+E,GAAqB;IAC3C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;QACtB;IACF;IAEA,OAAQA,IAAIC,MAAM;QAChB,KAAKC,6CAA2B,CAACC,YAAY;YAAE;gBAC7CxB,cAAcqB,IAAII,IAAI;gBACtBjG;gBACA;YACF;QACA,KAAK+F,6CAA2B,CAACG,QAAQ;YAAE;gBACzCnD,wBAAU,CAACoD,qBAAqB;gBAEhC,IAAI/D,QAAQC,GAAG,CAACC,SAAS,EAAE;oBACzBE,aAAc4D,UAAU;gBAC1B,OAAO;oBACL9B,2BAA2B/D,KAAKC,GAAG;oBACnCyB,QAAQoE,GAAG,CAAC;gBACd;gBACA;YACF;QACA,KAAKN,6CAA2B,CAACO,KAAK;QACtC,KAAKP,6CAA2B,CAACQ,IAAI;YAAE;gBACrCxD,wBAAU,CAACyD,qBAAqB;gBAEhC,IAAIX,IAAInB,IAAI,EAAED,oBAAoBoB,IAAInB,IAAI;gBAE1C,MAAM,EAAEf,MAAM,EAAEJ,QAAQ,EAAE,GAAGsC;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAK9C,wBAAU,CAAC0D,aAAa,CAACZ,IAAIa,WAAW;gBAClE,IAAI,kBAAkBb,KAAK9C,wBAAU,CAAC4D,cAAc,CAACd,IAAIe,YAAY;gBACrE,IAAI,oBAAoBf,KACtB9C,wBAAU,CAAC8D,gBAAgB,CAAChB,IAAIiB,cAAc;gBAEhD,MAAMC,YAAY1B,QAAQ1B,UAAUA,OAAOG,MAAM;gBACjD,IAAIiD,WAAW;oBACbpF,IAAAA,sBAAW,EACTqF,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,YAAYxD,OAAOG,MAAM;wBACzBsD,UAAUlH,OAAOC,iBAAiB;oBACpC;oBAEF,OAAO6D,aAAaL;gBACtB;gBAEA,mDAAmD;gBACnD,MAAM0D,cAAchC,QAAQ9B,YAAYA,SAASO,MAAM;gBACvD,IAAIuD,aAAa;oBACf1F,IAAAA,sBAAW,EACTqF,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPI,cAAc/D,SAASO,MAAM;wBAC7BsD,UAAUlH,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOmD,eAAeC;gBACxB;gBAEA5B,IAAAA,sBAAW,EACTqF,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPE,UAAUlH,OAAOC,iBAAiB;gBACpC;gBAEF,OAAOgC;YACT;QACA,KAAK4D,6CAA2B,CAACwB,wBAAwB;YAAE;gBACzD/E,gCAAAA,aAAcgF,wBAAwB;gBACtC,IAAIzF,oBAAoBX,wCAAmB,CAACC,eAAe,EAAE;oBAC3DnB,OAAOyF,QAAQ,CAAC8B,MAAM;gBACxB;gBACA;YACF;QACA,KAAK1B,6CAA2B,CAAC2B,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAG9B;gBACtB,IAAI8B,WAAW;oBACb,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE,GAAGb,KAAKc,KAAK,CAACH;oBACtC,MAAMzD,QAAQ,qBAAkB,CAAlB,IAAI6D,MAAMH,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/B1D,MAAM2D,KAAK,GAAGA;oBACd7D,aAAa;wBAACE;qBAAM;gBACtB;gBACA;YACF;QACA,KAAK6B,6CAA2B,CAACiC,mBAAmB;YAAE;gBACpD,KAAK,MAAMC,YAAYvH,0BAA2B;oBAChDuH,SAAS;wBACPC,MAAMnC,6CAA2B,CAACiC,mBAAmB;wBACrD/B,MAAMJ,IAAII,IAAI;oBAChB;gBACF;gBACA;YACF;QACA,KAAKF,6CAA2B,CAACoC,iBAAiB;YAAE;gBAClD3F,aAAc4F,kBAAkB,CAACvC;gBACjC9C,wBAAU,CAACsF,eAAe;gBAC1B,KAAK,MAAMJ,YAAYvH,0BAA2B;oBAChDuH,SAAS;wBACPC,MAAMnC,6CAA2B,CAACoC,iBAAiB;wBACnDlC,MAAMJ,IAAII,IAAI;oBAChB;gBACF;gBACA,IAAI7E,wCAAmB,CAACC,eAAe,EAAE;oBACvCY,QAAQ2B,IAAI,CAAC0E,4CAAoC;oBACjDrI,kBAAkB;gBACpB;gBACA8C,wBAAU,CAACwF,SAAS;gBACpB;YACF;QACA,KAAKxC,6CAA2B,CAACyC,UAAU;QAC3C,KAAKzC,6CAA2B,CAAC0C,YAAY;QAC7C,KAAK1C,6CAA2B,CAAC2C,WAAW;QAC5C,KAAK3C,6CAA2B,CAAC4C,yBAAyB;YACxD,IAAIlI,uBAAuB;gBACzBA,sBAAsBoF;YACxB;YACA;QACF,KAAKE,6CAA2B,CAAC6C,eAAe;YAC9C7F,wBAAU,CAAC8D,gBAAgB,CAAChB,IAAII,IAAI;YACpC;QACF;YACEJ;IACJ;AACF;AAEA,mDAAmD;AACnD,SAASzC;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOtB,8BAA8B+G;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAc;IACvC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASjI,QAAQ+H,MAAc;YAC7B,IAAIA,WAAW,QAAQ;gBACrBF,OAAOC,GAAG,CAACI,mBAAmB,CAAClI;gBAC/BiI;YACF;QACF;QACAJ,OAAOC,GAAG,CAACK,gBAAgB,CAACnI;IAC9B;AACF;AAEA,iEAAiE;AACjE,SAASmC;IACP,IAAI,CAAC0F,OAAOC,GAAG,EAAE;QACf,8DAA8D;QAC9D/G,QAAQiC,KAAK,CAAC;QACd,4BAA4B;QAC5B;IACF;IAEA,IAAI,CAACd,uBAAuB,CAAC0F,mBAAmB;QAC9C/F,wBAAU,CAACC,SAAS;QACpB;IACF;IAEA,SAASsG,mBACPvI,GAAQ,EACR4B,cAA0C;QAE1C,IAAI5B,OAAOK,wCAAmB,CAACC,eAAe,IAAIsB,kBAAkB,MAAM;YACxE,IAAI5B,KAAK;gBACPkB,QAAQ2B,IAAI,CAAC2F,iCAAyB;YACxC,OAAO,IAAInI,wCAAmB,CAACC,eAAe,EAAE;gBAC9CY,QAAQ2B,IAAI,CAAC0E,4CAAoC;YACnD;YACArI,kBAAkBc;YAClB;QACF;QAEAgC,wBAAU,CAACC,SAAS;QAEpB,IAAII,qBAAqB;YACvB,+DAA+D;YAC/DC;YACA;QACF;QAEAN,wBAAU,CAACwF,SAAS;QACpB7F,IAAAA,yBAAgB,EACdf,sBAAW,EACXgB,gBACA2B,0BACA/D,KAAKC,GAAG;QAGV,IAAI4B,QAAQC,GAAG,CAAC8B,gBAAgB,EAAE;YAChC+E,kBAAkB;gBAChB,IAAI9E,KAAKC,aAAa,EAAE;oBACtBD,KAAKC,aAAa;oBAClBD,KAAKC,aAAa,GAAG;gBACvB;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D0E,OAAOC,GAAG,CACPQ,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAAC9G;QACL,IAAIA,kBAAkB,MAAM;YAC1B,OAAO;QACT;QAEA,0EAA0E;QAC1E,mEAAmE;QACnE,yGAAyG;QACzGI,wBAAU,CAACsF,eAAe;QAC1B,2DAA2D;QAC3D,OAAOU,OAAOC,GAAG,CAACU,KAAK;IACzB,GACCD,IAAI,CACH,CAAC9G;QACC2G,mBAAmB,MAAM3G;IAC3B,GACA,CAAC5B;QACCuI,mBAAmBvI,KAAK;IAC1B;AAEN;AAEO,SAASd,kBAAkBc,GAAQ;IACxC,MAAM4I,aACJ5I,OACC,CAAA,AAACA,IAAI8G,KAAK,IAAI9G,IAAI8G,KAAK,CAAC+B,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpD/I,IAAI6G,OAAO,IACX7G,MAAM,EAAC;IAEXY,IAAAA,sBAAW,EACTqF,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPyC;QACAtI,iBAAiB,CAAC,CAACD,wCAAmB,CAACC,eAAe;QACtD0I,iBAAiBhJ,MAAMA,IAAIgJ,eAAe,GAAGC;IAC/C;IAGF9J,OAAOyF,QAAQ,CAAC8B,MAAM;AACxB", "ignoreList": [0]}