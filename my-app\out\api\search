{"type": "advanced", "internalDocumentIDStore": {"internalIdToId": ["/docs", "/docs-0", "/docs-1", "/docs-2", "/docs/test", "/docs/test-0", "/docs/test-1", "/docs/test-2"]}, "index": {"indexes": {"content": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "e": false, "k": "", "d": [], "c": [["h", {"w": "hello", "s": "hello", "e": true, "k": "h", "d": [1], "c": []}], ["w", {"w": "w", "s": "w", "e": false, "k": "w", "d": [], "c": [["o", {"w": "world", "s": "orld", "e": true, "k": "o", "d": [1], "c": []}], ["h", {"w": "what", "s": "hat", "e": true, "k": "h", "d": [3], "c": []}], ["e", {"w": "welcome", "s": "el<PERSON>", "e": true, "k": "e", "d": [4], "c": []}], ["r", {"w": "writing", "s": "riting", "e": true, "k": "r", "d": [4], "c": []}]]}], ["y", {"w": "you", "s": "you", "e": true, "k": "y", "d": [4], "c": [["r", {"w": "your", "s": "r", "e": true, "k": "r", "d": [2], "c": []}]]}], ["f", {"w": "first", "s": "first", "e": true, "k": "f", "d": [2], "c": []}], ["d", {"w": "doc", "s": "doc", "e": false, "k": "d", "d": [], "c": [["u", {"w": "document", "s": "ument", "e": true, "k": "u", "d": [2], "c": [["s", {"w": "documents", "s": "s", "e": true, "k": "s", "d": [4], "c": []}]]}], ["s", {"w": "docs", "s": "s", "e": true, "k": "s", "d": [4], "c": []}]]}], ["i", {"w": "i", "s": "i", "e": false, "k": "i", "d": [], "c": [["s", {"w": "is", "s": "s", "e": true, "k": "s", "d": [3], "c": []}], ["n", {"w": "in", "s": "n", "e": true, "k": "n", "d": [4], "c": []}]]}], ["n", {"w": "next", "s": "next", "e": true, "k": "n", "d": [3], "c": []}], ["t", {"w": "t", "s": "t", "e": false, "k": "t", "d": [], "c": [["o", {"w": "to", "s": "o", "e": true, "k": "o", "d": [4], "c": []}], ["h", {"w": "the", "s": "he", "e": true, "k": "h", "d": [4], "c": []}]]}], ["c", {"w": "c", "s": "c", "e": false, "k": "c", "d": [], "c": [["a", {"w": "ca", "s": "a", "e": false, "k": "a", "d": [], "c": [["n", {"w": "can", "s": "n", "e": true, "k": "n", "d": [4], "c": []}], ["r", {"w": "cards", "s": "rds", "e": true, "k": "r", "d": [8], "c": []}]]}], ["o", {"w": "co", "s": "o", "e": false, "k": "o", "d": [], "c": [["n", {"w": "content", "s": "ntent", "e": true, "k": "n", "d": [4], "c": []}], ["m", {"w": "components", "s": "mponents", "e": true, "k": "m", "d": [5, 6], "c": []}], ["d", {"w": "code", "s": "de", "e": true, "k": "d", "d": [7], "c": []}]]}]]}], ["s", {"w": "start", "s": "start", "e": true, "k": "s", "d": [4], "c": []}], ["b", {"w": "block", "s": "block", "e": true, "k": "b", "d": [7], "c": []}]]}, "isArray": false}, "page_id": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "e": false, "k": "", "d": [], "c": [["d", {"w": "docs", "s": "docs", "e": true, "k": "d", "d": [1, 2, 3, 4, 5, 6, 7, 8], "c": []}], ["t", {"w": "test", "s": "test", "e": true, "k": "t", "d": [5, 6, 7, 8], "c": []}]]}, "isArray": false}, "type": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "e": false, "k": "", "d": [], "c": [["p", {"w": "page", "s": "page", "e": true, "k": "p", "d": [1, 5], "c": []}], ["t", {"w": "text", "s": "text", "e": true, "k": "t", "d": [2, 4, 6], "c": []}], ["h", {"w": "heading", "s": "heading", "e": true, "k": "h", "d": [3, 7, 8], "c": []}]]}, "isArray": false}, "tags": {"type": "Flat", "node": {"numberToDocumentId": []}, "isArray": true}, "url": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "e": false, "k": "", "d": [], "c": [["d", {"w": "docs", "s": "docs", "e": true, "k": "d", "d": [1, 2, 3, 4, 5, 6, 7, 8], "c": []}], ["w", {"w": "what-is-next", "s": "what-is-next", "e": true, "k": "w", "d": [3], "c": []}], ["t", {"w": "test", "s": "test", "e": true, "k": "t", "d": [5, 6, 7, 8], "c": []}], ["c", {"w": "c", "s": "c", "e": false, "k": "c", "d": [], "c": [["o", {"w": "code-block", "s": "ode-block", "e": true, "k": "o", "d": [7], "c": []}], ["a", {"w": "cards", "s": "ards", "e": true, "k": "a", "d": [8], "c": []}]]}]]}, "isArray": false}}, "vectorIndexes": {}, "searchableProperties": ["content", "page_id", "type", "tags", "url"], "searchablePropertiesWithTypes": {"content": "string", "page_id": "string", "type": "string", "tags": "enum[]", "url": "string"}, "frequencies": {"content": {"1": {"hello": 0.5, "world": 0.5}, "2": {"your": 0.3333333333333333, "first": 0.3333333333333333, "document": 0.3333333333333333}, "3": {"what": 0.3333333333333333, "is": 0.3333333333333333, "next": 0.3333333333333333}, "4": {"welcome": 0.09090909090909091, "to": 0.09090909090909091, "the": 0.09090909090909091, "docs": 0.09090909090909091, "you": 0.09090909090909091, "can": 0.09090909090909091, "start": 0.09090909090909091, "writing": 0.09090909090909091, "documents": 0.09090909090909091, "in": 0.09090909090909091, "content": 0.09090909090909091}, "5": {"components": 1}, "6": {"components": 1}, "7": {"code": 0.5, "block": 0.5}, "8": {"cards": 1}}, "page_id": {"1": {"docs": 1}, "2": {"docs": 1}, "3": {"docs": 1}, "4": {"docs": 1}, "5": {"docs": 0.5, "test": 0.5}, "6": {"docs": 0.5, "test": 0.5}, "7": {"docs": 0.5, "test": 0.5}, "8": {"docs": 0.5, "test": 0.5}}, "type": {"1": {"page": 1}, "2": {"text": 1}, "3": {"heading": 1}, "4": {"text": 1}, "5": {"page": 1}, "6": {"text": 1}, "7": {"heading": 1}, "8": {"heading": 1}}, "url": {"1": {"docs": 1}, "2": {"docs": 1}, "3": {"docs": 0.5, "what-is-next": 0.5}, "4": {"docs": 1}, "5": {"docs": 0.5, "test": 0.5}, "6": {"docs": 0.5, "test": 0.5}, "7": {"docs": 0.3333333333333333, "test": 0.3333333333333333, "code-block": 0.3333333333333333}, "8": {"docs": 0.3333333333333333, "test": 0.3333333333333333, "cards": 0.3333333333333333}}}, "tokenOccurrences": {"content": {"hello": 1, "world": 1, "your": 1, "first": 1, "document": 1, "what": 1, "is": 1, "next": 1, "welcome": 1, "to": 1, "the": 1, "docs": 1, "you": 1, "can": 1, "start": 1, "writing": 1, "documents": 1, "in": 1, "content": 1, "components": 2, "code": 1, "block": 1, "cards": 1}, "page_id": {"docs": 8, "test": 4}, "type": {"page": 2, "text": 3, "heading": 3}, "url": {"docs": 8, "what-is-next": 1, "test": 4, "code-block": 1, "cards": 1}}, "avgFieldLength": {"content": 3, "page_id": 1.5, "type": 1, "url": 1.875}, "fieldLengths": {"content": {"1": 2, "2": 3, "3": 3, "4": 11, "5": 1, "6": 1, "7": 2, "8": 1}, "page_id": {"1": 1, "2": 1, "3": 1, "4": 1, "5": 2, "6": 2, "7": 2, "8": 2}, "type": {"1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1}, "url": {"1": 1, "2": 1, "3": 2, "4": 1, "5": 2, "6": 2, "7": 3, "8": 3}}}, "docs": {"docs": {"1": {"id": "/docs", "page_id": "/docs", "type": "page", "content": "Hello World", "tags": [], "url": "/docs"}, "2": {"id": "/docs-0", "page_id": "/docs", "tags": [], "type": "text", "url": "/docs", "content": "Your first document"}, "3": {"id": "/docs-1", "page_id": "/docs", "type": "heading", "tags": [], "url": "/docs#what-is-next", "content": "What is Next?"}, "4": {"id": "/docs-2", "page_id": "/docs", "tags": [], "type": "text", "url": "/docs", "content": "Welcome to the docs! You can start writing documents in /content/docs."}, "5": {"id": "/docs/test", "page_id": "/docs/test", "type": "page", "content": "Components", "tags": [], "url": "/docs/test"}, "6": {"id": "/docs/test-0", "page_id": "/docs/test", "tags": [], "type": "text", "url": "/docs/test", "content": "Components"}, "7": {"id": "/docs/test-1", "page_id": "/docs/test", "type": "heading", "tags": [], "url": "/docs/test#code-block", "content": "Code Block"}, "8": {"id": "/docs/test-2", "page_id": "/docs/test", "type": "heading", "tags": [], "url": "/docs/test#cards", "content": "Cards"}}, "count": 8}, "sorting": {"language": "english", "sortableProperties": ["content", "page_id", "type", "url"], "sortablePropertiesWithTypes": {"content": "string", "page_id": "string", "type": "string", "url": "string"}, "sorts": {"content": {"docs": {"1": 4, "2": 7, "3": 6, "4": 5, "5": 2, "6": 3, "7": 1, "8": 0}, "orderedDocs": [[8, "Cards"], [7, "Code Block"], [5, "Components"], [6, "Components"], [1, "Hello World"], [4, "Welcome to the docs! You can start writing documents in /content/docs."], [3, "What is Next?"], [2, "Your first document"]], "type": "string"}, "page_id": {"docs": {"1": 0, "2": 1, "3": 2, "4": 3, "5": 4, "6": 5, "7": 6, "8": 7}, "orderedDocs": [[1, "/docs"], [2, "/docs"], [3, "/docs"], [4, "/docs"], [5, "/docs/test"], [6, "/docs/test"], [7, "/docs/test"], [8, "/docs/test"]], "type": "string"}, "type": {"docs": {"1": 3, "2": 5, "3": 0, "4": 6, "5": 4, "6": 7, "7": 1, "8": 2}, "orderedDocs": [[3, "heading"], [7, "heading"], [8, "heading"], [1, "page"], [5, "page"], [2, "text"], [4, "text"], [6, "text"]], "type": "string"}, "url": {"docs": {"1": 0, "2": 1, "3": 7, "4": 2, "5": 3, "6": 4, "7": 6, "8": 5}, "orderedDocs": [[1, "/docs"], [2, "/docs"], [4, "/docs"], [5, "/docs/test"], [6, "/docs/test"], [8, "/docs/test#cards"], [7, "/docs/test#code-block"], [3, "/docs#what-is-next"]], "type": "string"}}, "enabled": true, "isSorted": true}, "language": "english"}