{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js"], "sourcesContent": ["// src/utils/remove-undefined.ts\nfunction removeUndefined(value, deep = false) {\n  const obj = value;\n  for (const key of Object.keys(obj)) {\n    if (obj[key] === void 0) delete obj[key];\n    if (deep && typeof obj[key] === \"object\" && obj[key] !== null) {\n      removeUndefined(obj[key], deep);\n    } else if (deep && Array.isArray(obj[key])) {\n      obj[key].forEach((v) => removeUndefined(v, deep));\n    }\n  }\n  return value;\n}\n\nexport {\n  removeUndefined\n};\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;AAChC,SAAS,gBAAgB,KAAK;QAAE,OAAA,iEAAO;IACrC,MAAM,MAAM;IACZ,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,KAAM;QAClC,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,CAAC,IAAI;QACxC,IAAI,QAAQ,OAAO,GAAG,CAAC,IAAI,KAAK,YAAY,GAAG,CAAC,IAAI,KAAK,MAAM;YAC7D,gBAAgB,GAAG,CAAC,IAAI,EAAE;QAC5B,OAAO,IAAI,QAAQ,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG;YAC1C,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAM,gBAAgB,GAAG;QAC7C;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js"], "sourcesContent": ["// src/search/shared.ts\nfunction escapeRegExp(input) {\n  return input.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction buildRegexFromQuery(q) {\n  const trimmed = q.trim();\n  if (trimmed.length === 0) return null;\n  const terms = Array.from(\n    new Set(\n      trimmed.split(/\\s+/).map((t) => t.trim()).filter(Boolean)\n    )\n  );\n  if (terms.length === 0) return null;\n  const escaped = terms.map(escapeRegExp).join(\"|\");\n  return new RegExp(`(${escaped})`, \"gi\");\n}\nfunction createContentHighlighter(query) {\n  const regex = typeof query === \"string\" ? buildRegexFromQuery(query) : query;\n  return {\n    highlight(content) {\n      if (!regex) return [{ type: \"text\", content }];\n      const out = [];\n      let i = 0;\n      for (const match of content.matchAll(regex)) {\n        if (i < match.index) {\n          out.push({\n            type: \"text\",\n            content: content.substring(i, match.index)\n          });\n        }\n        out.push({\n          type: \"text\",\n          content: match[0],\n          styles: {\n            highlight: true\n          }\n        });\n        i = match.index + match[0].length;\n      }\n      if (i < content.length) {\n        out.push({\n          type: \"text\",\n          content: content.substring(i)\n        });\n      }\n      return out;\n    }\n  };\n}\n\nexport {\n  createContentHighlighter\n};\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AACvB,SAAS,aAAa,KAAK;IACzB,OAAO,MAAM,OAAO,CAAC,uBAAuB;AAC9C;AACA,SAAS,oBAAoB,CAAC;IAC5B,MAAM,UAAU,EAAE,IAAI;IACtB,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IACjC,MAAM,QAAQ,MAAM,IAAI,CACtB,IAAI,IACF,QAAQ,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,MAAM,CAAC;IAGrD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAC/B,MAAM,UAAU,MAAM,GAAG,CAAC,cAAc,IAAI,CAAC;IAC7C,OAAO,IAAI,OAAO,AAAC,IAAW,OAAR,SAAQ,MAAI;AACpC;AACA,SAAS,yBAAyB,KAAK;IACrC,MAAM,QAAQ,OAAO,UAAU,WAAW,oBAAoB,SAAS;IACvE,OAAO;QACL,WAAU,OAAO;YACf,IAAI,CAAC,OAAO,OAAO;gBAAC;oBAAE,MAAM;oBAAQ;gBAAQ;aAAE;YAC9C,MAAM,MAAM,EAAE;YACd,IAAI,IAAI;YACR,KAAK,MAAM,SAAS,QAAQ,QAAQ,CAAC,OAAQ;gBAC3C,IAAI,IAAI,MAAM,KAAK,EAAE;oBACnB,IAAI,IAAI,CAAC;wBACP,MAAM;wBACN,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,KAAK;oBAC3C;gBACF;gBACA,IAAI,IAAI,CAAC;oBACP,MAAM;oBACN,SAAS,KAAK,CAAC,EAAE;oBACjB,QAAQ;wBACN,WAAW;oBACb;gBACF;gBACA,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;YACnC;YACA,IAAI,IAAI,QAAQ,MAAM,EAAE;gBACtB,IAAI,IAAI,CAAC;oBACP,MAAM;oBACN,SAAS,QAAQ,SAAS,CAAC;gBAC7B;YACF;YACA,OAAO;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/chunk-KIJ7AMBP.js"], "sourcesContent": ["import {\n  removeUndefined\n} from \"./chunk-KAOEMCTI.js\";\nimport {\n  createContentHighlighter\n} from \"./chunk-CNWEGOUF.js\";\n\n// src/search/orama/search/simple.ts\nimport { search } from \"@orama/orama\";\nasync function searchSimple(db, query, params = {}) {\n  const highlighter = createContentHighlighter(query);\n  const result = await search(db, {\n    term: query,\n    tolerance: 1,\n    ...params,\n    boost: {\n      title: 2,\n      ...\"boost\" in params ? params.boost : void 0\n    }\n  });\n  return result.hits.map((hit) => ({\n    type: \"page\",\n    content: hit.document.title,\n    contentWithHighlights: highlighter.highlight(hit.document.title),\n    id: hit.document.url,\n    url: hit.document.url\n  }));\n}\n\n// src/search/orama/search/advanced.ts\nimport { getByID, search as search2 } from \"@orama/orama\";\nasync function searchAdvanced(db, query, tag = [], extraParams = {}) {\n  if (typeof tag === \"string\") tag = [tag];\n  let params = {\n    ...extraParams,\n    where: removeUndefined({\n      tags: tag.length > 0 ? {\n        containsAll: tag\n      } : void 0,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 8,\n      ...extraParams.groupBy\n    }\n  };\n  if (query.length > 0) {\n    params = {\n      ...params,\n      term: query,\n      properties: [\"content\"]\n    };\n  }\n  const highlighter = createContentHighlighter(query);\n  const result = await search2(db, params);\n  const list = [];\n  for (const item of result.groups ?? []) {\n    const pageId = item.values[0];\n    const page = getByID(db, pageId);\n    if (!page) continue;\n    list.push({\n      id: pageId,\n      type: \"page\",\n      content: page.content,\n      contentWithHighlights: highlighter.highlight(page.content),\n      url: page.url\n    });\n    for (const hit of item.result) {\n      if (hit.document.type === \"page\") continue;\n      list.push({\n        id: hit.document.id.toString(),\n        content: hit.document.content,\n        contentWithHighlights: highlighter.highlight(hit.document.content),\n        type: hit.document.type,\n        url: hit.document.url\n      });\n    }\n  }\n  return list;\n}\n\nexport {\n  searchSimple,\n  searchAdvanced\n};\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AAIA,oCAAoC;AACpC;AAAA;AAqBA,sCAAsC;AACtC;;;;AArBA,eAAe,aAAa,EAAE,EAAE,KAAK;QAAE,SAAA,iEAAS,CAAC;IAC/C,MAAM,cAAc,CAAA,GAAA,gKAAA,CAAA,2BAAwB,AAAD,EAAE;IAC7C,MAAM,SAAS,MAAM,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,EAAE,IAAI;QAC9B,MAAM;QACN,WAAW;QACX,GAAG,MAAM;QACT,OAAO;YACL,OAAO;YACP,GAAG,WAAW,SAAS,OAAO,KAAK,GAAG,KAAK,CAAC;QAC9C;IACF;IACA,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAQ,CAAC;YAC/B,MAAM;YACN,SAAS,IAAI,QAAQ,CAAC,KAAK;YAC3B,uBAAuB,YAAY,SAAS,CAAC,IAAI,QAAQ,CAAC,KAAK;YAC/D,IAAI,IAAI,QAAQ,CAAC,GAAG;YACpB,KAAK,IAAI,QAAQ,CAAC,GAAG;QACvB,CAAC;AACH;;AAIA,eAAe,eAAe,EAAE,EAAE,KAAK;QAAE,MAAA,iEAAM,EAAE,EAAE,cAAA,iEAAc,CAAC;IAChE,IAAI,OAAO,QAAQ,UAAU,MAAM;QAAC;KAAI;IACxC,IAAI,SAAS;QACX,GAAG,WAAW;QACd,OAAO,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;YACrB,MAAM,IAAI,MAAM,GAAG,IAAI;gBACrB,aAAa;YACf,IAAI,KAAK;YACT,GAAG,YAAY,KAAK;QACtB;QACA,SAAS;YACP,YAAY;gBAAC;aAAU;YACvB,WAAW;YACX,GAAG,YAAY,OAAO;QACxB;IACF;IACA,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,SAAS;YACP,GAAG,MAAM;YACT,MAAM;YACN,YAAY;gBAAC;aAAU;QACzB;IACF;IACA,MAAM,cAAc,CAAA,GAAA,gKAAA,CAAA,2BAAwB,AAAD,EAAE;IAC7C,MAAM,SAAS,MAAM,CAAA,GAAA,2KAAA,CAAA,SAAO,AAAD,EAAE,IAAI;IACjC,MAAM,OAAO,EAAE;QACI;IAAnB,KAAK,MAAM,QAAQ,CAAA,iBAAA,OAAO,MAAM,cAAb,4BAAA,iBAAiB,EAAE,CAAE;QACtC,MAAM,SAAS,KAAK,MAAM,CAAC,EAAE;QAC7B,MAAM,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE,IAAI;QACzB,IAAI,CAAC,MAAM;QACX,KAAK,IAAI,CAAC;YACR,IAAI;YACJ,MAAM;YACN,SAAS,KAAK,OAAO;YACrB,uBAAuB,YAAY,SAAS,CAAC,KAAK,OAAO;YACzD,KAAK,KAAK,GAAG;QACf;QACA,KAAK,MAAM,OAAO,KAAK,MAAM,CAAE;YAC7B,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ;YAClC,KAAK,IAAI,CAAC;gBACR,IAAI,IAAI,QAAQ,CAAC,EAAE,CAAC,QAAQ;gBAC5B,SAAS,IAAI,QAAQ,CAAC,OAAO;gBAC7B,uBAAuB,YAAY,SAAS,CAAC,IAAI,QAAQ,CAAC,OAAO;gBACjE,MAAM,IAAI,QAAQ,CAAC,IAAI;gBACvB,KAAK,IAAI,QAAQ,CAAC,GAAG;YACvB;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/node_modules/fumadocs-core/dist/static-IWYDJ3C5.js"], "sourcesContent": ["import {\n  searchAdvanced,\n  searchSimple\n} from \"./chunk-KIJ7AMBP.js\";\nimport \"./chunk-KAOEMCTI.js\";\nimport \"./chunk-CNWEGOUF.js\";\nimport \"./chunk-JSBRDJBE.js\";\n\n// src/search/client/static.ts\nimport { create, load } from \"@orama/orama\";\nvar cache = /* @__PURE__ */ new Map();\nasync function loadDB({\n  from = \"/api/search\",\n  initOrama = (locale) => create({ schema: { _: \"string\" }, language: locale })\n}) {\n  const cacheKey = from;\n  const cached = cache.get(cacheKey);\n  if (cached) return cached;\n  async function init() {\n    const res = await fetch(from);\n    if (!res.ok)\n      throw new Error(\n        `failed to fetch exported search indexes from ${from}, make sure the search database is exported and available for client.`\n      );\n    const data = await res.json();\n    const dbs = /* @__PURE__ */ new Map();\n    if (data.type === \"i18n\") {\n      await Promise.all(\n        Object.entries(data.data).map(async ([k, v]) => {\n          const db2 = await initOrama(k);\n          load(db2, v);\n          dbs.set(k, {\n            type: v.type,\n            db: db2\n          });\n        })\n      );\n      return dbs;\n    }\n    const db = await initOrama();\n    load(db, data);\n    dbs.set(\"\", {\n      type: data.type,\n      db\n    });\n    return dbs;\n  }\n  const result = init();\n  cache.set(cacheKey, result);\n  return result;\n}\nasync function search(query, options) {\n  const { tag, locale } = options;\n  const db = (await loadDB(options)).get(locale ?? \"\");\n  if (!db) return [];\n  if (db.type === \"simple\")\n    return searchSimple(db, query);\n  return searchAdvanced(db.db, query, tag);\n}\nexport {\n  search\n};\n"], "names": [], "mappings": ";;;AAAA;AAIA;AACA;AACA;AAEA,8BAA8B;AAC9B;AAAA;AAAA;;;;;;AACA,IAAI,QAAQ,aAAa,GAAG,IAAI;AAChC,eAAe,OAAO,KAGrB;QAHqB,EACpB,OAAO,aAAa,EACpB,YAAY,CAAC,SAAW,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,EAAE;YAAE,QAAQ;gBAAE,GAAG;YAAS;YAAG,UAAU;QAAO,EAAE,EAC9E,GAHqB;IAIpB,MAAM,WAAW;IACjB,MAAM,SAAS,MAAM,GAAG,CAAC;IACzB,IAAI,QAAQ,OAAO;IACnB,eAAe;QACb,MAAM,MAAM,MAAM,MAAM;QACxB,IAAI,CAAC,IAAI,EAAE,EACT,MAAM,IAAI,MACR,AAAC,gDAAoD,OAAL,MAAK;QAEzD,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,MAAM,aAAa,GAAG,IAAI;QAChC,IAAI,KAAK,IAAI,KAAK,QAAQ;YACxB,MAAM,QAAQ,GAAG,CACf,OAAO,OAAO,CAAC,KAAK,IAAI,EAAE,GAAG,CAAC;oBAAO,CAAC,GAAG,EAAE;gBACzC,MAAM,MAAM,MAAM,UAAU;gBAC5B,CAAA,GAAA,kLAAA,CAAA,OAAI,AAAD,EAAE,KAAK;gBACV,IAAI,GAAG,CAAC,GAAG;oBACT,MAAM,EAAE,IAAI;oBACZ,IAAI;gBACN;YACF;YAEF,OAAO;QACT;QACA,MAAM,KAAK,MAAM;QACjB,CAAA,GAAA,kLAAA,CAAA,OAAI,AAAD,EAAE,IAAI;QACT,IAAI,GAAG,CAAC,IAAI;YACV,MAAM,KAAK,IAAI;YACf;QACF;QACA,OAAO;IACT;IACA,MAAM,SAAS;IACf,MAAM,GAAG,CAAC,UAAU;IACpB,OAAO;AACT;AACA,eAAe,OAAO,KAAK,EAAE,OAAO;IAClC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG;IACxB,MAAM,KAAK,CAAC,MAAM,OAAO,QAAQ,EAAE,GAAG,CAAC,mBAAA,oBAAA,SAAU;IACjD,IAAI,CAAC,IAAI,OAAO,EAAE;IAClB,IAAI,GAAG,IAAI,KAAK,UACd,OAAO,CAAA,GAAA,gKAAA,CAAA,eAAY,AAAD,EAAE,IAAI;IAC1B,OAAO,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE,GAAG,EAAE,EAAE,OAAO;AACtC", "ignoreList": [0], "debugId": null}}]}