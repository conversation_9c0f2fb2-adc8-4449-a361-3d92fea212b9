{"version": 3, "sources": ["../../src/client/app-next-dev.ts"], "sourcesContent": ["// TODO-APP: hydration warning\n\nimport './app-webpack'\n\nimport { renderAppDevOverlay } from 'next/dist/compiled/next-devtools'\nimport { appBootstrap } from './app-bootstrap'\nimport { getOwnerStack } from '../next-devtools/userspace/app/errors/stitched-error'\nimport { isRecoverableError } from './react-client-callbacks/on-recoverable-error'\n\n// eslint-disable-next-line @next/internal/typechecked-require\nconst instrumentationHooks = require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index') as typeof import('./app-index')\n  try {\n    hydrate(instrumentationHooks)\n  } finally {\n    renderAppDevOverlay(getOwnerStack, isRecoverableError)\n  }\n})\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "require", "appBootstrap", "hydrate", "renderAppDevOverlay", "getOwnerStack", "isRecoverableError"], "mappings": "AAAA,8BAA8B;;;;;QAEvB;8BAE6B;8BACP;+BACC;oCACK;AAEnC,8DAA8D;AAC9D,MAAMA,uBAAuBC,QAAQ;AAErCC,IAAAA,0BAAY,EAAC;IACX,MAAM,EAAEC,OAAO,EAAE,GAAGF,QAAQ;IAC5B,IAAI;QACFE,QAAQH;IACV,SAAU;QACRI,IAAAA,iCAAmB,EAACC,4BAAa,EAAEC,sCAAkB;IACvD;AACF", "ignoreList": [0]}