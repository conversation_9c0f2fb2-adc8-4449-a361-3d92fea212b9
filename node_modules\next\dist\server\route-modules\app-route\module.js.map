{"version": 3, "sources": ["../../../../src/server/route-modules/app-route/module.ts"], "sourcesContent": ["import type { NextConfig } from '../../config-shared'\nimport type { AppRouteRouteDefinition } from '../../route-definitions/app-route-route-definition'\nimport type { AppSegmentConfig } from '../../../build/segment-config/app/app-segment-config'\nimport type { NextRequest } from '../../web/spec-extension/request'\nimport type { PrerenderManifest } from '../../../build'\nimport type { NextURL } from '../../web/next-url'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\nimport type { WorkUnitStore } from '../../app-render/work-unit-async-storage.external'\n\nimport {\n  RouteModule,\n  type RouteModuleHandleContext,\n  type RouteModuleOptions,\n} from '../route-module'\nimport { createRequestStoreForAPI } from '../../async-storage/request-store'\nimport {\n  createWorkStore,\n  type WorkStoreContext,\n} from '../../async-storage/work-store'\nimport { type HTTP_METHOD, HTTP_METHODS, isHTTPMethod } from '../../web/http'\nimport { getImplicitTags, type ImplicitTags } from '../../lib/implicit-tags'\nimport { patchFetch } from '../../lib/patch-fetch'\nimport { getTracer } from '../../lib/trace/tracer'\nimport { AppRouteRouteHandlersSpan } from '../../lib/trace/constants'\nimport { getPathnameFromAbsolutePath } from './helpers/get-pathname-from-absolute-path'\nimport * as Log from '../../../build/output/log'\nimport { autoImplementMethods } from './helpers/auto-implement-methods'\nimport {\n  appendMutableCookies,\n  type ReadonlyRequestCookies,\n} from '../../web/spec-extension/adapters/request-cookies'\nimport { HeadersAdapter } from '../../web/spec-extension/adapters/headers'\nimport { RequestCookiesAdapter } from '../../web/spec-extension/adapters/request-cookies'\nimport { parsedUrlQueryToParams } from './helpers/parsed-url-query-to-params'\nimport { printDebugThrownValueForProspectiveRender } from '../../app-render/prospective-render-utils'\n\nimport * as serverHooks from '../../../client/components/hooks-server-context'\nimport { DynamicServerError } from '../../../client/components/hooks-server-context'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type RequestStore,\n  type PrerenderStore,\n} from '../../app-render/work-unit-async-storage.external'\nimport {\n  actionAsyncStorage,\n  type ActionStore,\n} from '../../app-render/action-async-storage.external'\nimport * as sharedModules from './shared-modules'\nimport { getIsPossibleServerAction } from '../../lib/server-action-request-meta'\nimport { RequestCookies } from 'next/dist/compiled/@edge-runtime/cookies'\nimport { cleanURL } from './helpers/clean-url'\nimport { StaticGenBailoutError } from '../../../client/components/static-generation-bailout'\nimport { isStaticGenEnabled } from './helpers/is-static-gen-enabled'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n  createDynamicTrackingState,\n  getFirstDynamicReason,\n} from '../../app-render/dynamic-rendering'\nimport { ReflectAdapter } from '../../web/spec-extension/adapters/reflect'\nimport type { RenderOptsPartial } from '../../app-render/types'\nimport { CacheSignal } from '../../app-render/cache-signal'\nimport { scheduleImmediate } from '../../../lib/scheduler'\nimport { createServerParamsForRoute } from '../../request/params'\nimport type { AppSegment } from '../../../build/segment-config/app/app-segments'\nimport {\n  getRedirectStatusCodeFromError,\n  getURLFromRedirectError,\n} from '../../../client/components/redirect'\nimport {\n  isRedirectError,\n  type RedirectError,\n} from '../../../client/components/redirect-error'\nimport {\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../../client/components/http-access-fallback/http-access-fallback'\nimport { RedirectStatusCode } from '../../../client/components/redirect-status-code'\nimport { INFINITE_CACHE } from '../../../lib/constants'\nimport { executeRevalidates } from '../../revalidation-utils'\nimport { trackPendingModules } from '../../app-render/module-loading/track-module-loading.external'\nimport { InvariantError } from '../../../shared/lib/invariant-error'\n\nexport class WrappedNextRouterError {\n  constructor(\n    public readonly error: RedirectError,\n    public readonly headers?: Headers\n  ) {}\n}\n\n/**\n * The AppRouteModule is the type of the module exported by the bundled App\n * Route module.\n */\nexport type AppRouteModule = typeof import('../../../build/templates/app-route')\n\nexport type AppRouteSharedContext = {\n  buildId: string\n}\n\n/**\n * AppRouteRouteHandlerContext is the context that is passed to the route\n * handler for app routes.\n */\nexport interface AppRouteRouteHandlerContext extends RouteModuleHandleContext {\n  renderOpts: WorkStoreContext['renderOpts'] &\n    Pick<RenderOptsPartial, 'onInstrumentationRequestError'> &\n    CollectedCacheInfo\n  prerenderManifest: DeepReadonly<PrerenderManifest>\n  sharedContext: AppRouteSharedContext\n}\n\ntype CollectedCacheInfo = {\n  collectedTags?: string\n  collectedRevalidate?: number\n  collectedExpire?: number\n  collectedStale?: number\n}\n\n/**\n * AppRouteHandlerFnContext is the context that is passed to the handler as the\n * second argument.\n */\ntype AppRouteHandlerFnContext = {\n  params?: Promise<Record<string, string | string[] | undefined>>\n}\n\n/**\n * Handler function for app routes. If a non-Response value is returned, an error\n * will be thrown.\n */\nexport type AppRouteHandlerFn = (\n  /**\n   * Incoming request object.\n   */\n  req: NextRequest,\n  /**\n   * Context properties on the request (including the parameters if this was a\n   * dynamic route).\n   */\n  ctx: AppRouteHandlerFnContext\n) => unknown\n\n/**\n * AppRouteHandlers describes the handlers for app routes that is provided by\n * the userland module.\n */\nexport type AppRouteHandlers = {\n  [method in HTTP_METHOD]?: AppRouteHandlerFn\n}\n\n/**\n * AppRouteUserlandModule is the userland module that is provided for app\n * routes. This contains all the user generated code.\n */\nexport type AppRouteUserlandModule = AppRouteHandlers &\n  Pick<\n    AppSegmentConfig,\n    'dynamic' | 'revalidate' | 'dynamicParams' | 'fetchCache'\n  > &\n  Pick<AppSegment, 'generateStaticParams'>\n\n/**\n * AppRouteRouteModuleOptions is the options that are passed to the app route\n * module from the bundled code.\n */\nexport interface AppRouteRouteModuleOptions\n  extends RouteModuleOptions<AppRouteRouteDefinition, AppRouteUserlandModule> {\n  readonly resolvedPagePath: string\n  readonly nextConfigOutput: NextConfig['output']\n}\n\n/**\n * AppRouteRouteHandler is the handler for app routes.\n */\nexport class AppRouteRouteModule extends RouteModule<\n  AppRouteRouteDefinition,\n  AppRouteUserlandModule\n> {\n  /**\n   * A reference to the request async storage.\n   */\n  public readonly workUnitAsyncStorage = workUnitAsyncStorage\n\n  /**\n   * A reference to the static generation async storage.\n   */\n  public readonly workAsyncStorage = workAsyncStorage\n\n  /**\n   * An interface to call server hooks which interact with the underlying\n   * storage.\n   */\n  public readonly serverHooks = serverHooks\n\n  public static readonly sharedModules = sharedModules\n\n  /**\n   * A reference to the mutation related async storage, such as mutations of\n   * cookies.\n   */\n  public readonly actionAsyncStorage = actionAsyncStorage\n\n  public readonly resolvedPagePath: string\n  public readonly nextConfigOutput: NextConfig['output'] | undefined\n\n  private readonly methods: Record<HTTP_METHOD, AppRouteHandlerFn>\n  private readonly hasNonStaticMethods: boolean\n  private readonly dynamic: AppRouteUserlandModule['dynamic']\n\n  constructor({\n    userland,\n    definition,\n    distDir,\n    relativeProjectDir,\n    resolvedPagePath,\n    nextConfigOutput,\n  }: AppRouteRouteModuleOptions) {\n    super({ userland, definition, distDir, relativeProjectDir })\n\n    this.resolvedPagePath = resolvedPagePath\n    this.nextConfigOutput = nextConfigOutput\n\n    // Automatically implement some methods if they aren't implemented by the\n    // userland module.\n    this.methods = autoImplementMethods(userland)\n    this.isAppRouter = true\n\n    // Get the non-static methods for this route.\n    this.hasNonStaticMethods = hasNonStaticMethods(userland)\n\n    // Get the dynamic property from the userland module.\n    this.dynamic = this.userland.dynamic\n    if (this.nextConfigOutput === 'export') {\n      if (this.dynamic === 'force-dynamic') {\n        throw new Error(\n          `export const dynamic = \"force-dynamic\" on page \"${definition.pathname}\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`\n        )\n      } else if (!isStaticGenEnabled(this.userland) && this.userland['GET']) {\n        throw new Error(\n          `export const dynamic = \"force-static\"/export const revalidate not configured on route \"${definition.pathname}\" with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`\n        )\n      } else {\n        this.dynamic = 'error'\n      }\n    }\n\n    // We only warn in development after here, so return if we're not in\n    // development.\n    if (process.env.NODE_ENV === 'development') {\n      // Print error in development if the exported handlers are in lowercase, only\n      // uppercase handlers are supported.\n      const lowercased = HTTP_METHODS.map((method) => method.toLowerCase())\n      for (const method of lowercased) {\n        if (method in this.userland) {\n          Log.error(\n            `Detected lowercase method '${method}' in '${\n              this.resolvedPagePath\n            }'. Export the uppercase '${method.toUpperCase()}' method name to fix this error.`\n          )\n        }\n      }\n\n      // Print error if the module exports a default handler, they must use named\n      // exports for each HTTP method.\n      if ('default' in this.userland) {\n        Log.error(\n          `Detected default export in '${this.resolvedPagePath}'. Export a named export for each HTTP method instead.`\n        )\n      }\n\n      // If there is no methods exported by this module, then return a not found\n      // response.\n      if (!HTTP_METHODS.some((method) => method in this.userland)) {\n        Log.error(\n          `No HTTP methods exported in '${this.resolvedPagePath}'. Export a named export for each HTTP method.`\n        )\n      }\n    }\n  }\n\n  /**\n   * Resolves the handler function for the given method.\n   *\n   * @param method the requested method\n   * @returns the handler function for the given method\n   */\n  private resolve(method: string): AppRouteHandlerFn {\n    // Ensure that the requested method is a valid method (to prevent RCE's).\n    if (!isHTTPMethod(method)) return () => new Response(null, { status: 400 })\n\n    // Return the handler.\n    return this.methods[method]\n  }\n\n  private async do(\n    handler: AppRouteHandlerFn,\n    actionStore: ActionStore,\n    workStore: WorkStore,\n    // @TODO refactor to not take this argument but instead construct the RequestStore\n    // inside this function. Right now we get passed a RequestStore even when\n    // we're going to do a prerender. We should probably just split do up into prexecute and execute\n    requestStore: RequestStore,\n    implicitTags: ImplicitTags,\n    request: NextRequest,\n    context: AppRouteRouteHandlerContext\n  ) {\n    const isStaticGeneration = workStore.isStaticGeneration\n    const cacheComponentsEnabled =\n      !!context.renderOpts.experimental?.cacheComponents\n\n    // Patch the global fetch.\n    patchFetch({\n      workAsyncStorage: this.workAsyncStorage,\n      workUnitAsyncStorage: this.workUnitAsyncStorage,\n    })\n\n    const handlerContext: AppRouteHandlerFnContext = {\n      params: context.params\n        ? createServerParamsForRoute(\n            parsedUrlQueryToParams(context.params),\n            workStore\n          )\n        : undefined,\n    }\n\n    const resolvePendingRevalidations = () => {\n      context.renderOpts.pendingWaitUntil = executeRevalidates(\n        workStore\n      ).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log(\n            'pending revalidates promise finished for:',\n            requestStore.url\n          )\n        }\n      })\n    }\n\n    let prerenderStore: null | PrerenderStore = null\n\n    let res: unknown\n    try {\n      if (isStaticGeneration) {\n        const userlandRevalidate = this.userland.revalidate\n        const defaultRevalidate: number =\n          // If the static generation store does not have a revalidate value\n          // set, then we should set it the revalidate value from the userland\n          // module or default to false.\n          userlandRevalidate === false || userlandRevalidate === undefined\n            ? INFINITE_CACHE\n            : userlandRevalidate\n\n        if (cacheComponentsEnabled) {\n          /**\n           * When we are attempting to statically prerender the GET handler of a route.ts module\n           * and cacheComponents is on we follow a similar pattern to rendering.\n           *\n           * We first run the handler letting caches fill. If something synchronously dynamic occurs\n           * during this prospective render then we can infer it will happen on every render and we\n           * just bail out of prerendering.\n           *\n           * Next we run the handler again and we check if we get a result back in a microtask.\n           * Next.js expects the return value to be a Response or a Thenable that resolves to a Response.\n           * Unfortunately Response's do not allow for accessing the response body synchronously or in\n           * a microtask so we need to allow one more task to unwrap the response body. This is a slightly\n           * different semantic than what we have when we render and it means that certain tasks can still\n           * execute before a prerender completes such as a carefully timed setImmediate.\n           *\n           * Functionally though IO should still take longer than the time it takes to unwrap the response body\n           * so our heuristic of excluding any IO should be preserved.\n           */\n          const prospectiveController = new AbortController()\n          let prospectiveRenderIsDynamic = false\n          const cacheSignal = new CacheSignal()\n          let dynamicTracking = createDynamicTrackingState(undefined)\n\n          const prospectiveRoutePrerenderStore: PrerenderStore =\n            (prerenderStore = {\n              type: 'prerender',\n              phase: 'action',\n              // This replicates prior behavior where rootParams is empty in routes\n              // TODO we need to make this have the proper rootParams for this route\n              rootParams: {},\n              fallbackRouteParams: null,\n              implicitTags,\n              renderSignal: prospectiveController.signal,\n              controller: prospectiveController,\n              cacheSignal,\n              // During prospective render we don't use a controller\n              // because we need to let all caches fill.\n              dynamicTracking,\n              allowEmptyStaticShell: false,\n              revalidate: defaultRevalidate,\n              expire: INFINITE_CACHE,\n              stale: INFINITE_CACHE,\n              tags: [...implicitTags.tags],\n              // TODO: Shouldn't we provide an RDC here?\n              prerenderResumeDataCache: null,\n              renderResumeDataCache: null,\n              hmrRefreshHash: undefined,\n              captureOwnerStack: undefined,\n            })\n\n          let prospectiveResult\n          try {\n            prospectiveResult = this.workUnitAsyncStorage.run(\n              prospectiveRoutePrerenderStore,\n              handler,\n              request,\n              handlerContext\n            )\n          } catch (err) {\n            if (prospectiveController.signal.aborted) {\n              // the route handler called an API which is always dynamic\n              // there is no need to try again\n              prospectiveRenderIsDynamic = true\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          }\n          if (\n            typeof prospectiveResult === 'object' &&\n            prospectiveResult !== null &&\n            typeof (prospectiveResult as any).then === 'function'\n          ) {\n            // The handler returned a Thenable. We'll listen for rejections to determine\n            // if the route is erroring for dynamic reasons.\n            ;(prospectiveResult as any as Promise<unknown>).then(\n              () => {},\n              (err) => {\n                if (prospectiveController.signal.aborted) {\n                  // the route handler called an API which is always dynamic\n                  // there is no need to try again\n                  prospectiveRenderIsDynamic = true\n                } else if (process.env.NEXT_DEBUG_BUILD) {\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              }\n            )\n          }\n\n          trackPendingModules(cacheSignal)\n          await cacheSignal.cacheReady()\n\n          if (prospectiveRenderIsDynamic) {\n            // the route handler called an API which is always dynamic\n            // there is no need to try again\n            const dynamicReason = getFirstDynamicReason(dynamicTracking)\n            if (dynamicReason) {\n              throw new DynamicServerError(\n                `Route ${workStore.route} couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n              )\n            } else {\n              console.error(\n                'Expected Next.js to keep track of reason for opting out of static rendering but one was not found. This is a bug in Next.js'\n              )\n              throw new DynamicServerError(\n                `Route ${workStore.route} couldn't be rendered statically because it used a dynamic API. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n              )\n            }\n          }\n\n          // TODO start passing this controller to the route handler. We should expose\n          // it so the handler to abort inflight requests and other operations if we abort\n          // the prerender.\n          const finalController = new AbortController()\n          dynamicTracking = createDynamicTrackingState(undefined)\n\n          const finalRoutePrerenderStore: PrerenderStore = (prerenderStore = {\n            type: 'prerender',\n            phase: 'action',\n            rootParams: {},\n            fallbackRouteParams: null,\n            implicitTags,\n            renderSignal: finalController.signal,\n            controller: finalController,\n            cacheSignal: null,\n            dynamicTracking,\n            allowEmptyStaticShell: false,\n            revalidate: defaultRevalidate,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags.tags],\n            // TODO: Shouldn't we provide an RDC here?\n            prerenderResumeDataCache: null,\n            renderResumeDataCache: null,\n            hmrRefreshHash: undefined,\n            captureOwnerStack: undefined,\n          })\n\n          let responseHandled = false\n          res = await new Promise((resolve, reject) => {\n            scheduleImmediate(async () => {\n              try {\n                const result = await (this.workUnitAsyncStorage.run(\n                  finalRoutePrerenderStore,\n                  handler,\n                  request,\n                  handlerContext\n                ) as Promise<Response>)\n                if (responseHandled) {\n                  // we already rejected in the followup task\n                  return\n                } else if (!(result instanceof Response)) {\n                  // This is going to error but we let that happen below\n                  resolve(result)\n                  return\n                }\n\n                responseHandled = true\n\n                let bodyHandled = false\n                result.arrayBuffer().then((body) => {\n                  if (!bodyHandled) {\n                    bodyHandled = true\n\n                    resolve(\n                      new Response(body, {\n                        headers: result.headers,\n                        status: result.status,\n                        statusText: result.statusText,\n                      })\n                    )\n                  }\n                }, reject)\n                scheduleImmediate(() => {\n                  if (!bodyHandled) {\n                    bodyHandled = true\n                    finalController.abort()\n                    reject(createCacheComponentsError(workStore.route))\n                  }\n                })\n              } catch (err) {\n                reject(err)\n              }\n            })\n            scheduleImmediate(() => {\n              if (!responseHandled) {\n                responseHandled = true\n                finalController.abort()\n                reject(createCacheComponentsError(workStore.route))\n              }\n            })\n          })\n          if (finalController.signal.aborted) {\n            // We aborted from within the execution\n            throw createCacheComponentsError(workStore.route)\n          } else {\n            // We didn't abort during the execution. We can abort now as a matter of semantics\n            // though at the moment nothing actually consumes this signal so it won't halt any\n            // inflight work.\n            finalController.abort()\n          }\n        } else {\n          prerenderStore = {\n            type: 'prerender-legacy',\n            phase: 'action',\n            rootParams: {},\n            implicitTags,\n            revalidate: defaultRevalidate,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags.tags],\n          }\n\n          res = await workUnitAsyncStorage.run(\n            prerenderStore,\n            handler,\n            request,\n            handlerContext\n          )\n        }\n      } else {\n        res = await workUnitAsyncStorage.run(\n          requestStore,\n          handler,\n          request,\n          handlerContext\n        )\n      }\n    } catch (err) {\n      if (isRedirectError(err)) {\n        const url = getURLFromRedirectError(err)\n        if (!url) {\n          throw new Error('Invariant: Unexpected redirect url format')\n        }\n\n        // We need to capture any headers that should be sent on\n        // the response.\n        const headers = new Headers({ Location: url })\n\n        // Let's append any cookies that were added by the\n        // cookie API.\n        // TODO leaving the gate here b/c it indicates that we might not actually want to do this\n        // on every `do` call. During prerender there should be no mutableCookies because\n        appendMutableCookies(headers, requestStore.mutableCookies)\n\n        resolvePendingRevalidations()\n\n        // Return the redirect response.\n        return new Response(null, {\n          // If we're in an action, we want to use a 303 redirect as we don't\n          // want the POST request to follow the redirect, as it could result in\n          // erroneous re-submissions.\n          status: actionStore.isAction\n            ? RedirectStatusCode.SeeOther\n            : getRedirectStatusCodeFromError(err),\n          headers,\n        })\n      } else if (isHTTPAccessFallbackError(err)) {\n        const httpStatus = getAccessFallbackHTTPStatus(err)\n        return new Response(null, { status: httpStatus })\n      }\n\n      throw err\n    }\n\n    // Validate that the response is a valid response object.\n    if (!(res instanceof Response)) {\n      throw new Error(\n        `No response is returned from route handler '${this.resolvedPagePath}'. Ensure you return a \\`Response\\` or a \\`NextResponse\\` in all branches of your handler.`\n      )\n    }\n\n    context.renderOpts.fetchMetrics = workStore.fetchMetrics\n\n    resolvePendingRevalidations()\n\n    if (prerenderStore) {\n      context.renderOpts.collectedTags = prerenderStore.tags?.join(',')\n      context.renderOpts.collectedRevalidate = prerenderStore.revalidate\n      context.renderOpts.collectedExpire = prerenderStore.expire\n      context.renderOpts.collectedStale = prerenderStore.stale\n    }\n\n    // It's possible cookies were set in the handler, so we need\n    // to merge the modified cookies and the returned response\n    // here.\n    const headers = new Headers(res.headers)\n    if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n      return new Response(res.body, {\n        status: res.status,\n        statusText: res.statusText,\n        headers,\n      })\n    }\n\n    return res\n  }\n\n  public async handle(\n    req: NextRequest,\n    context: AppRouteRouteHandlerContext\n  ): Promise<Response> {\n    // Get the handler function for the given method.\n    const handler = this.resolve(req.method)\n\n    // Get the context for the static generation.\n    const staticGenerationContext: WorkStoreContext = {\n      page: this.definition.page,\n      renderOpts: context.renderOpts,\n      buildId: context.sharedContext.buildId,\n      previouslyRevalidatedTags: [],\n    }\n\n    // Add the fetchCache option to the renderOpts.\n    staticGenerationContext.renderOpts.fetchCache = this.userland.fetchCache\n\n    const actionStore: ActionStore = {\n      isAppRoute: true,\n      isAction: getIsPossibleServerAction(req),\n    }\n\n    const implicitTags = await getImplicitTags(\n      this.definition.page,\n      req.nextUrl,\n      // App Routes don't support unknown route params.\n      null\n    )\n\n    const requestStore = createRequestStoreForAPI(\n      req,\n      req.nextUrl,\n      implicitTags,\n      undefined,\n      context.prerenderManifest.preview\n    )\n\n    const workStore = createWorkStore(staticGenerationContext)\n\n    // Run the handler with the request AsyncLocalStorage to inject the helper\n    // support. We set this to `unknown` because the type is not known until\n    // runtime when we do a instanceof check below.\n    const response: unknown = await this.actionAsyncStorage.run(\n      actionStore,\n      () =>\n        this.workUnitAsyncStorage.run(requestStore, () =>\n          this.workAsyncStorage.run(workStore, async () => {\n            // Check to see if we should bail out of static generation based on\n            // having non-static methods.\n            if (this.hasNonStaticMethods) {\n              if (workStore.isStaticGeneration) {\n                const err = new DynamicServerError(\n                  'Route is configured with methods that cannot be statically generated.'\n                )\n                workStore.dynamicUsageDescription = err.message\n                workStore.dynamicUsageStack = err.stack\n                throw err\n              }\n            }\n\n            // We assume we can pass the original request through however we may end up\n            // proxying it in certain circumstances based on execution type and configuration\n            let request = req\n\n            // Update the static generation store based on the dynamic property.\n            switch (this.dynamic) {\n              case 'force-dynamic': {\n                // Routes of generated paths should be dynamic\n                workStore.forceDynamic = true\n                if (workStore.isStaticGeneration) {\n                  const err = new DynamicServerError(\n                    'Route is configured with dynamic = error which cannot be statically generated.'\n                  )\n                  workStore.dynamicUsageDescription = err.message\n                  workStore.dynamicUsageStack = err.stack\n                  throw err\n                }\n                break\n              }\n              case 'force-static':\n                // The dynamic property is set to force-static, so we should\n                // force the page to be static.\n                workStore.forceStatic = true\n                // We also Proxy the request to replace dynamic data on the request\n                // with empty stubs to allow for safely executing as static\n                request = new Proxy(req, forceStaticRequestHandlers)\n                break\n              case 'error':\n                // The dynamic property is set to error, so we should throw an\n                // error if the page is being statically generated.\n                workStore.dynamicShouldError = true\n                if (workStore.isStaticGeneration)\n                  request = new Proxy(req, requireStaticRequestHandlers)\n                break\n              case undefined:\n              case 'auto':\n                // We proxy `NextRequest` to track dynamic access, and\n                // potentially bail out of static generation.\n                request = proxyNextRequest(req, workStore)\n                break\n              default:\n                this.dynamic satisfies never\n            }\n\n            // TODO: propagate this pathname from route matcher\n            const route = getPathnameFromAbsolutePath(this.resolvedPagePath)\n\n            const tracer = getTracer()\n\n            // Update the root span attribute for the route.\n            tracer.setRootSpanAttribute('next.route', route)\n\n            return tracer.trace(\n              AppRouteRouteHandlersSpan.runHandler,\n              {\n                spanName: `executing api route (app) ${route}`,\n                attributes: {\n                  'next.route': route,\n                },\n              },\n              async () =>\n                this.do(\n                  handler,\n                  actionStore,\n                  workStore,\n                  requestStore,\n                  implicitTags,\n                  request,\n                  context\n                )\n            )\n          })\n        )\n    )\n\n    // If the handler did't return a valid response, then return the internal\n    // error response.\n    if (!(response instanceof Response)) {\n      // TODO: validate the correct handling behavior, maybe log something?\n      return new Response(null, { status: 500 })\n    }\n\n    if (response.headers.has('x-middleware-rewrite')) {\n      throw new Error(\n        'NextResponse.rewrite() was used in a app route handler, this is not currently supported. Please remove the invocation to continue.'\n      )\n    }\n\n    if (response.headers.get('x-middleware-next') === '1') {\n      // TODO: move this error into the `NextResponse.next()` function.\n      throw new Error(\n        'NextResponse.next() was used in a app route handler, this is not supported. See here for more info: https://nextjs.org/docs/messages/next-response-next-in-app-route-handler'\n      )\n    }\n\n    return response\n  }\n}\n\nexport default AppRouteRouteModule\n\n/**\n * Gets all the method names for handlers that are not considered static.\n *\n * @param handlers the handlers from the userland module\n * @returns the method names that are not considered static or false if all\n *          methods are static\n */\nexport function hasNonStaticMethods(handlers: AppRouteHandlers): boolean {\n  if (\n    // Order these by how common they are to be used\n    handlers.POST ||\n    handlers.PUT ||\n    handlers.DELETE ||\n    handlers.PATCH ||\n    handlers.OPTIONS\n  ) {\n    return true\n  }\n  return false\n}\n\n// These symbols will be used to stash cached values on Proxied requests without requiring\n// additional closures or storage such as WeakMaps.\nconst nextURLSymbol = Symbol('nextUrl')\nconst requestCloneSymbol = Symbol('clone')\nconst urlCloneSymbol = Symbol('clone')\nconst searchParamsSymbol = Symbol('searchParams')\nconst hrefSymbol = Symbol('href')\nconst toStringSymbol = Symbol('toString')\nconst headersSymbol = Symbol('headers')\nconst cookiesSymbol = Symbol('cookies')\n\ntype RequestSymbolTarget = {\n  [headersSymbol]?: Headers\n  [cookiesSymbol]?: RequestCookies | ReadonlyRequestCookies\n  [nextURLSymbol]?: NextURL\n  [requestCloneSymbol]?: () => NextRequest\n}\n\ntype UrlSymbolTarget = {\n  [searchParamsSymbol]?: URLSearchParams\n  [hrefSymbol]?: string\n  [toStringSymbol]?: () => string\n  [urlCloneSymbol]?: () => NextURL\n}\n\n/**\n * The general technique with these proxy handlers is to prioritize keeping them static\n * by stashing computed values on the Proxy itself. This is safe because the Proxy is\n * inaccessible to the consumer since all operations are forwarded\n */\nconst forceStaticRequestHandlers = {\n  get(\n    target: NextRequest & RequestSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'headers':\n        return (\n          target[headersSymbol] ||\n          (target[headersSymbol] = HeadersAdapter.seal(new Headers({})))\n        )\n      case 'cookies':\n        return (\n          target[cookiesSymbol] ||\n          (target[cookiesSymbol] = RequestCookiesAdapter.seal(\n            new RequestCookies(new Headers({}))\n          ))\n        )\n      case 'nextUrl':\n        return (\n          target[nextURLSymbol] ||\n          (target[nextURLSymbol] = new Proxy(\n            target.nextUrl,\n            forceStaticNextUrlHandlers\n          ))\n        )\n      case 'url':\n        // we don't need to separately cache this we can just read the nextUrl\n        // and return the href since we know it will have been stripped of any\n        // dynamic parts. We access via the receiver to trigger the get trap\n        return receiver.nextUrl.href\n      case 'geo':\n      case 'ip':\n        return undefined\n      case 'clone':\n        return (\n          target[requestCloneSymbol] ||\n          (target[requestCloneSymbol] = () =>\n            new Proxy(\n              // This is vaguely unsafe but it's required since NextRequest does not implement\n              // clone. The reason we might expect this to work in this context is the Proxy will\n              // respond with static-amenable values anyway somewhat restoring the interface.\n              // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n              // sophisticated to adequately represent themselves in all contexts. A better approach is\n              // to probably embed the static generation logic into the class itself removing the need\n              // for any kind of proxying\n              target.clone() as NextRequest,\n              forceStaticRequestHandlers\n            ))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n  // We don't need to proxy set because all the properties we proxy are ready only\n  // and will be ignored\n}\n\nconst forceStaticNextUrlHandlers = {\n  get(\n    target: NextURL & UrlSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      // URL properties\n      case 'search':\n        return ''\n      case 'searchParams':\n        return (\n          target[searchParamsSymbol] ||\n          (target[searchParamsSymbol] = new URLSearchParams())\n        )\n      case 'href':\n        return (\n          target[hrefSymbol] ||\n          (target[hrefSymbol] = cleanURL(target.href).href)\n        )\n      case 'toJSON':\n      case 'toString':\n        return (\n          target[toStringSymbol] ||\n          (target[toStringSymbol] = () => receiver.href)\n        )\n\n      // NextUrl properties\n      case 'url':\n        // Currently nextURL does not expose url but our Docs indicate that it is an available property\n        // I am forcing this to undefined here to avoid accidentally exposing a dynamic value later if\n        // the underlying nextURL ends up adding this property\n        return undefined\n      case 'clone':\n        return (\n          target[urlCloneSymbol] ||\n          (target[urlCloneSymbol] = () =>\n            new Proxy(target.clone(), forceStaticNextUrlHandlers))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n}\n\nfunction proxyNextRequest(request: NextRequest, workStore: WorkStore) {\n  const nextUrlHandlers = {\n    get(\n      target: NextURL & UrlSymbolTarget,\n      prop: string | symbol,\n      receiver: any\n    ): unknown {\n      switch (prop) {\n        case 'search':\n        case 'searchParams':\n        case 'url':\n        case 'href':\n        case 'toJSON':\n        case 'toString':\n        case 'origin': {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamic(workStore, workUnitStore, `nextUrl.${prop}`)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'clone':\n          return (\n            target[urlCloneSymbol] ||\n            (target[urlCloneSymbol] = () =>\n              new Proxy(target.clone(), nextUrlHandlers))\n          )\n        default:\n          return ReflectAdapter.get(target, prop, receiver)\n      }\n    },\n  }\n\n  const nextRequestHandlers = {\n    get(\n      target: NextRequest & RequestSymbolTarget,\n      prop: string | symbol\n    ): unknown {\n      switch (prop) {\n        case 'nextUrl':\n          return (\n            target[nextURLSymbol] ||\n            (target[nextURLSymbol] = new Proxy(target.nextUrl, nextUrlHandlers))\n          )\n        case 'headers':\n        case 'cookies':\n        case 'url':\n        case 'body':\n        case 'blob':\n        case 'json':\n        case 'text':\n        case 'arrayBuffer':\n        case 'formData': {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamic(workStore, workUnitStore, `request.${prop}`)\n          // The receiver arg is intentionally the same as the target to fix an issue with\n          // edge runtime, where attempting to access internal slots with the wrong `this` context\n          // results in an error.\n          return ReflectAdapter.get(target, prop, target)\n        }\n        case 'clone':\n          return (\n            target[requestCloneSymbol] ||\n            (target[requestCloneSymbol] = () =>\n              new Proxy(\n                // This is vaguely unsafe but it's required since NextRequest does not implement\n                // clone. The reason we might expect this to work in this context is the Proxy will\n                // respond with static-amenable values anyway somewhat restoring the interface.\n                // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n                // sophisticated to adequately represent themselves in all contexts. A better approach is\n                // to probably embed the static generation logic into the class itself removing the need\n                // for any kind of proxying\n                target.clone() as NextRequest,\n                nextRequestHandlers\n              ))\n          )\n        default:\n          // The receiver arg is intentionally the same as the target to fix an issue with\n          // edge runtime, where attempting to access internal slots with the wrong `this` context\n          // results in an error.\n          return ReflectAdapter.get(target, prop, target)\n      }\n    },\n    // We don't need to proxy set because all the properties we proxy are ready only\n    // and will be ignored\n  }\n\n  return new Proxy(request, nextRequestHandlers)\n}\n\nconst requireStaticRequestHandlers = {\n  get(\n    target: NextRequest & RequestSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'nextUrl':\n        return (\n          target[nextURLSymbol] ||\n          (target[nextURLSymbol] = new Proxy(\n            target.nextUrl,\n            requireStaticNextUrlHandlers\n          ))\n        )\n      case 'headers':\n      case 'cookies':\n      case 'url':\n      case 'body':\n      case 'blob':\n      case 'json':\n      case 'text':\n      case 'arrayBuffer':\n      case 'formData':\n        throw new StaticGenBailoutError(\n          `Route ${target.nextUrl.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`request.${prop}\\`.`\n        )\n      case 'clone':\n        return (\n          target[requestCloneSymbol] ||\n          (target[requestCloneSymbol] = () =>\n            new Proxy(\n              // This is vaguely unsafe but it's required since NextRequest does not implement\n              // clone. The reason we might expect this to work in this context is the Proxy will\n              // respond with static-amenable values anyway somewhat restoring the interface.\n              // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n              // sophisticated to adequately represent themselves in all contexts. A better approach is\n              // to probably embed the static generation logic into the class itself removing the need\n              // for any kind of proxying\n              target.clone() as NextRequest,\n              requireStaticRequestHandlers\n            ))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n  // We don't need to proxy set because all the properties we proxy are ready only\n  // and will be ignored\n}\n\nconst requireStaticNextUrlHandlers = {\n  get(\n    target: NextURL & UrlSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'search':\n      case 'searchParams':\n      case 'url':\n      case 'href':\n      case 'toJSON':\n      case 'toString':\n      case 'origin':\n        throw new StaticGenBailoutError(\n          `Route ${target.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`nextUrl.${prop}\\`.`\n        )\n      case 'clone':\n        return (\n          target[urlCloneSymbol] ||\n          (target[urlCloneSymbol] = () =>\n            new Proxy(target.clone(), requireStaticNextUrlHandlers))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n}\n\nfunction createCacheComponentsError(route: string) {\n  return new DynamicServerError(\n    `Route ${route} couldn't be rendered statically because it used IO that was not cached. See more info here: https://nextjs.org/docs/messages/cache-components`\n  )\n}\n\nfunction trackDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | WorkUnitStore,\n  expression: string\n): void {\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'private-cache':\n        // TODO: Should we allow reading cookies and search params from the\n        // request for private caches in route handlers?\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      case 'unstable-cache':\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      case 'prerender':\n        const error = new Error(\n          `Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-request`\n        )\n        return abortAndThrowOnSynchronousRequestDataAccess(\n          store.route,\n          expression,\n          error,\n          workUnitStore\n        )\n      case 'prerender-client':\n        throw new InvariantError(\n          'A client prerender store should not be used for a route handler.'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'A runtime prerender store should not be used for a route handler.'\n        )\n      case 'prerender-ppr':\n        return postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      case 'prerender-legacy':\n        workUnitStore.revalidate = 0\n\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      case 'request':\n        if (process.env.NODE_ENV !== 'production') {\n          // TODO: This is currently not really needed for route handlers, as it\n          // only controls the ISR status that's shown for pages.\n          workUnitStore.usedDynamic = true\n        }\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n"], "names": ["AppRouteRouteModule", "WrappedNextRouterError", "hasNonStaticMethods", "constructor", "error", "headers", "RouteModule", "sharedModules", "userland", "definition", "distDir", "relativeProjectDir", "resolvedPagePath", "nextConfigOutput", "workUnitAsyncStorage", "workAsyncStorage", "serverHooks", "actionAsyncStorage", "methods", "autoImplementMethods", "isAppRouter", "dynamic", "Error", "pathname", "isStaticGenEnabled", "process", "env", "NODE_ENV", "lowercased", "HTTP_METHODS", "map", "method", "toLowerCase", "Log", "toUpperCase", "some", "resolve", "isHTTPMethod", "Response", "status", "do", "handler", "actionStore", "workStore", "requestStore", "implicitTags", "request", "context", "isStaticGeneration", "cacheComponentsEnabled", "renderOpts", "experimental", "cacheComponents", "patchFetch", "handlerContext", "params", "createServerParamsForRoute", "parsedUrlQueryToParams", "undefined", "resolvePendingRevalidations", "pendingWaitUntil", "executeRevalidates", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "url", "prerenderStore", "res", "userlandRevalidate", "revalidate", "defaultRevalidate", "INFINITE_CACHE", "prospectiveController", "AbortController", "prospectiveRenderIsDynamic", "cacheSignal", "CacheSignal", "dynamicTracking", "createDynamicTrackingState", "prospectiveRoutePrerenderStore", "type", "phase", "rootParams", "fallbackRouteParams", "renderSignal", "signal", "controller", "allowEmptyStaticShell", "expire", "stale", "tags", "prerenderResumeDataCache", "renderResumeDataCache", "hmrRefreshHash", "captureOwnerStack", "prospectiveResult", "run", "err", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "printDebugThrownValueForProspectiveRender", "route", "then", "trackPendingModules", "cacheReady", "dynamicReason", "getFirstDynamicReason", "DynamicServerError", "finalController", "finalRoutePrerenderStore", "responseHandled", "Promise", "reject", "scheduleImmediate", "result", "bodyHandled", "arrayBuffer", "body", "statusText", "abort", "createCacheComponentsError", "isRedirectError", "getURLFromRedirectError", "Headers", "Location", "appendMutableCookies", "mutableCookies", "isAction", "RedirectStatusCode", "<PERSON><PERSON><PERSON>", "getRedirectStatusCodeFromError", "isHTTPAccessFallbackError", "httpStatus", "getAccessFallbackHTTPStatus", "fetchMetrics", "collectedTags", "join", "collectedRevalidate", "collectedExpire", "collectedStale", "handle", "req", "staticGenerationContext", "page", "buildId", "sharedContext", "previouslyRevalidatedTags", "fetchCache", "isAppRoute", "getIsPossibleServerAction", "getImplicitTags", "nextUrl", "createRequestStoreForAPI", "prerenderManifest", "preview", "createWorkStore", "response", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceDynamic", "forceStatic", "Proxy", "forceStaticRequestHandlers", "dynamicShouldError", "requireStaticRequestHandlers", "proxyNextRequest", "getPathnameFromAbsolutePath", "tracer", "getTracer", "setRootSpanAttribute", "trace", "AppRouteRouteHandlersSpan", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "attributes", "has", "get", "handlers", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "nextURLSymbol", "Symbol", "requestCloneSymbol", "urlCloneSymbol", "searchParamsSymbol", "hrefSymbol", "toStringSymbol", "headersSymbol", "cookiesSymbol", "target", "prop", "receiver", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seal", "RequestCookiesAdapter", "RequestCookies", "forceStaticNextUrlHandlers", "href", "clone", "ReflectAdapter", "URLSearchParams", "cleanURL", "nextUrlHandlers", "workUnitStore", "getStore", "trackDynamic", "nextRequestHandlers", "requireStaticNextUrlHandlers", "StaticGenBailoutError", "store", "expression", "abortAndThrowOnSynchronousRequestDataAccess", "InvariantError", "postponeWithTracking", "usedDynamic"], "mappings": ";;;;;;;;;;;;;;;;;IAoLaA,mBAAmB;eAAnBA;;IA5FAC,sBAAsB;eAAtBA;;IA8tBb,OAAkC;eAAlC;;IASgBC,mBAAmB;eAAnBA;;;6BAlzBT;8BACkC;2BAIlC;sBACsD;8BACV;4BACxB;wBACD;2BACgB;6CACE;6DACvB;sCACgB;gCAI9B;yBACwB;wCAEQ;wCACmB;4EAE7B;0CAMtB;8CAKA;4CAIA;uEACwB;yCACW;yBACX;0BACN;yCACa;oCACH;kCAM5B;yBACwB;6BAEH;2BACM;wBACS;0BAKpC;+BAIA;oCAIA;oCAC4B;4BACJ;mCACI;4CACC;gCACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExB,MAAMD;IACXE,YACE,AAAgBC,KAAoB,EACpC,AAAgBC,OAAiB,CACjC;aAFgBD,QAAAA;aACAC,UAAAA;IACf;AACL;AAuFO,MAAML,4BAA4BM,wBAAW;qBAoB3BC,gBAAgBA;IAevCJ,YAAY,EACVK,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EACW,CAAE;QAC7B,KAAK,CAAC;YAAEL;YAAUC;YAAYC;YAASC;QAAmB,IAvC5D;;GAEC,QACeG,uBAAuBA,kDAAoB,EAE3D;;GAEC,QACeC,mBAAmBA,0CAAgB,EAEnD;;;GAGC,QACeC,cAAcA,qBAI9B;;;GAGC,QACeC,qBAAqBA,8CAAkB;QAmBrD,IAAI,CAACL,gBAAgB,GAAGA;QACxB,IAAI,CAACC,gBAAgB,GAAGA;QAExB,yEAAyE;QACzE,mBAAmB;QACnB,IAAI,CAACK,OAAO,GAAGC,IAAAA,0CAAoB,EAACX;QACpC,IAAI,CAACY,WAAW,GAAG;QAEnB,6CAA6C;QAC7C,IAAI,CAAClB,mBAAmB,GAAGA,oBAAoBM;QAE/C,qDAAqD;QACrD,IAAI,CAACa,OAAO,GAAG,IAAI,CAACb,QAAQ,CAACa,OAAO;QACpC,IAAI,IAAI,CAACR,gBAAgB,KAAK,UAAU;YACtC,IAAI,IAAI,CAACQ,OAAO,KAAK,iBAAiB;gBACpC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,gDAAgD,EAAEb,WAAWc,QAAQ,CAAC,wHAAwH,CAAC,GAD5L,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAI,CAACC,IAAAA,sCAAkB,EAAC,IAAI,CAAChB,QAAQ,KAAK,IAAI,CAACA,QAAQ,CAAC,MAAM,EAAE;gBACrE,MAAM,qBAEL,CAFK,IAAIc,MACR,CAAC,uFAAuF,EAAEb,WAAWc,QAAQ,CAAC,yGAAyG,CAAC,GADpN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO;gBACL,IAAI,CAACF,OAAO,GAAG;YACjB;QACF;QAEA,oEAAoE;QACpE,eAAe;QACf,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,6EAA6E;YAC7E,oCAAoC;YACpC,MAAMC,aAAaC,kBAAY,CAACC,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;YAClE,KAAK,MAAMD,UAAUH,WAAY;gBAC/B,IAAIG,UAAU,IAAI,CAACvB,QAAQ,EAAE;oBAC3ByB,KAAI7B,KAAK,CACP,CAAC,2BAA2B,EAAE2B,OAAO,MAAM,EACzC,IAAI,CAACnB,gBAAgB,CACtB,yBAAyB,EAAEmB,OAAOG,WAAW,GAAG,gCAAgC,CAAC;gBAEtF;YACF;YAEA,2EAA2E;YAC3E,gCAAgC;YAChC,IAAI,aAAa,IAAI,CAAC1B,QAAQ,EAAE;gBAC9ByB,KAAI7B,KAAK,CACP,CAAC,4BAA4B,EAAE,IAAI,CAACQ,gBAAgB,CAAC,sDAAsD,CAAC;YAEhH;YAEA,0EAA0E;YAC1E,YAAY;YACZ,IAAI,CAACiB,kBAAY,CAACM,IAAI,CAAC,CAACJ,SAAWA,UAAU,IAAI,CAACvB,QAAQ,GAAG;gBAC3DyB,KAAI7B,KAAK,CACP,CAAC,6BAA6B,EAAE,IAAI,CAACQ,gBAAgB,CAAC,8CAA8C,CAAC;YAEzG;QACF;IACF;IAEA;;;;;GAKC,GACD,AAAQwB,QAAQL,MAAc,EAAqB;QACjD,yEAAyE;QACzE,IAAI,CAACM,IAAAA,kBAAY,EAACN,SAAS,OAAO,IAAM,IAAIO,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAEzE,sBAAsB;QACtB,OAAO,IAAI,CAACrB,OAAO,CAACa,OAAO;IAC7B;IAEA,MAAcS,GACZC,OAA0B,EAC1BC,WAAwB,EACxBC,SAAoB,EACpB,kFAAkF;IAClF,yEAAyE;IACzE,gGAAgG;IAChGC,YAA0B,EAC1BC,YAA0B,EAC1BC,OAAoB,EACpBC,OAAoC,EACpC;YAGIA;QAFJ,MAAMC,qBAAqBL,UAAUK,kBAAkB;QACvD,MAAMC,yBACJ,CAAC,GAACF,mCAAAA,QAAQG,UAAU,CAACC,YAAY,qBAA/BJ,iCAAiCK,eAAe;QAEpD,0BAA0B;QAC1BC,IAAAA,sBAAU,EAAC;YACTtC,kBAAkB,IAAI,CAACA,gBAAgB;YACvCD,sBAAsB,IAAI,CAACA,oBAAoB;QACjD;QAEA,MAAMwC,iBAA2C;YAC/CC,QAAQR,QAAQQ,MAAM,GAClBC,IAAAA,kCAA0B,EACxBC,IAAAA,8CAAsB,EAACV,QAAQQ,MAAM,GACrCZ,aAEFe;QACN;QAEA,MAAMC,8BAA8B;YAClCZ,QAAQG,UAAU,CAACU,gBAAgB,GAAGC,IAAAA,qCAAkB,EACtDlB,WACAmB,OAAO,CAAC;gBACR,IAAIrC,QAAQC,GAAG,CAACqC,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CACT,6CACArB,aAAasB,GAAG;gBAEpB;YACF;QACF;QAEA,IAAIC,iBAAwC;QAE5C,IAAIC;QACJ,IAAI;YACF,IAAIpB,oBAAoB;gBACtB,MAAMqB,qBAAqB,IAAI,CAAC7D,QAAQ,CAAC8D,UAAU;gBACnD,MAAMC,oBACJ,kEAAkE;gBAClE,oEAAoE;gBACpE,8BAA8B;gBAC9BF,uBAAuB,SAASA,uBAAuBX,YACnDc,0BAAc,GACdH;gBAEN,IAAIpB,wBAAwB;oBAC1B;;;;;;;;;;;;;;;;;WAiBC,GACD,MAAMwB,wBAAwB,IAAIC;oBAClC,IAAIC,6BAA6B;oBACjC,MAAMC,cAAc,IAAIC,wBAAW;oBACnC,IAAIC,kBAAkBC,IAAAA,4CAA0B,EAACrB;oBAEjD,MAAMsB,iCACHb,iBAAiB;wBAChBc,MAAM;wBACNC,OAAO;wBACP,qEAAqE;wBACrE,sEAAsE;wBACtEC,YAAY,CAAC;wBACbC,qBAAqB;wBACrBvC;wBACAwC,cAAcZ,sBAAsBa,MAAM;wBAC1CC,YAAYd;wBACZG;wBACA,sDAAsD;wBACtD,0CAA0C;wBAC1CE;wBACAU,uBAAuB;wBACvBlB,YAAYC;wBACZkB,QAAQjB,0BAAc;wBACtBkB,OAAOlB,0BAAc;wBACrBmB,MAAM;+BAAI9C,aAAa8C,IAAI;yBAAC;wBAC5B,0CAA0C;wBAC1CC,0BAA0B;wBAC1BC,uBAAuB;wBACvBC,gBAAgBpC;wBAChBqC,mBAAmBrC;oBACrB;oBAEF,IAAIsC;oBACJ,IAAI;wBACFA,oBAAoB,IAAI,CAAClF,oBAAoB,CAACmF,GAAG,CAC/CjB,gCACAvC,SACAK,SACAQ;oBAEJ,EAAE,OAAO4C,KAAK;wBACZ,IAAIzB,sBAAsBa,MAAM,CAACa,OAAO,EAAE;4BACxC,0DAA0D;4BAC1D,gCAAgC;4BAChCxB,6BAA6B;wBAC/B,OAAO,IACLlD,QAAQC,GAAG,CAAC0E,gBAAgB,IAC5B3E,QAAQC,GAAG,CAAC2E,sBAAsB,EAClC;4BACAC,IAAAA,iEAAyC,EAACJ,KAAKvD,UAAU4D,KAAK;wBAChE;oBACF;oBACA,IACE,OAAOP,sBAAsB,YAC7BA,sBAAsB,QACtB,OAAO,AAACA,kBAA0BQ,IAAI,KAAK,YAC3C;wBACA,4EAA4E;wBAC5E,gDAAgD;;wBAC9CR,kBAA8CQ,IAAI,CAClD,KAAO,GACP,CAACN;4BACC,IAAIzB,sBAAsBa,MAAM,CAACa,OAAO,EAAE;gCACxC,0DAA0D;gCAC1D,gCAAgC;gCAChCxB,6BAA6B;4BAC/B,OAAO,IAAIlD,QAAQC,GAAG,CAAC0E,gBAAgB,EAAE;gCACvCE,IAAAA,iEAAyC,EACvCJ,KACAvD,UAAU4D,KAAK;4BAEnB;wBACF;oBAEJ;oBAEAE,IAAAA,+CAAmB,EAAC7B;oBACpB,MAAMA,YAAY8B,UAAU;oBAE5B,IAAI/B,4BAA4B;wBAC9B,0DAA0D;wBAC1D,gCAAgC;wBAChC,MAAMgC,gBAAgBC,IAAAA,uCAAqB,EAAC9B;wBAC5C,IAAI6B,eAAe;4BACjB,MAAM,qBAEL,CAFK,IAAIE,sCAAkB,CAC1B,CAAC,MAAM,EAAElE,UAAU4D,KAAK,CAAC,mDAAmD,EAAEI,cAAc,6EAA6E,CAAC,GADtK,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF,OAAO;4BACL3C,QAAQ5D,KAAK,CACX;4BAEF,MAAM,qBAEL,CAFK,IAAIyG,sCAAkB,CAC1B,CAAC,MAAM,EAAElE,UAAU4D,KAAK,CAAC,yIAAyI,CAAC,GAD/J,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;oBACF;oBAEA,4EAA4E;oBAC5E,gFAAgF;oBAChF,iBAAiB;oBACjB,MAAMO,kBAAkB,IAAIpC;oBAC5BI,kBAAkBC,IAAAA,4CAA0B,EAACrB;oBAE7C,MAAMqD,2BAA4C5C,iBAAiB;wBACjEc,MAAM;wBACNC,OAAO;wBACPC,YAAY,CAAC;wBACbC,qBAAqB;wBACrBvC;wBACAwC,cAAcyB,gBAAgBxB,MAAM;wBACpCC,YAAYuB;wBACZlC,aAAa;wBACbE;wBACAU,uBAAuB;wBACvBlB,YAAYC;wBACZkB,QAAQjB,0BAAc;wBACtBkB,OAAOlB,0BAAc;wBACrBmB,MAAM;+BAAI9C,aAAa8C,IAAI;yBAAC;wBAC5B,0CAA0C;wBAC1CC,0BAA0B;wBAC1BC,uBAAuB;wBACvBC,gBAAgBpC;wBAChBqC,mBAAmBrC;oBACrB;oBAEA,IAAIsD,kBAAkB;oBACtB5C,MAAM,MAAM,IAAI6C,QAAQ,CAAC7E,SAAS8E;wBAChCC,IAAAA,4BAAiB,EAAC;4BAChB,IAAI;gCACF,MAAMC,SAAS,MAAO,IAAI,CAACtG,oBAAoB,CAACmF,GAAG,CACjDc,0BACAtE,SACAK,SACAQ;gCAEF,IAAI0D,iBAAiB;oCACnB,2CAA2C;oCAC3C;gCACF,OAAO,IAAI,CAAEI,CAAAA,kBAAkB9E,QAAO,GAAI;oCACxC,sDAAsD;oCACtDF,QAAQgF;oCACR;gCACF;gCAEAJ,kBAAkB;gCAElB,IAAIK,cAAc;gCAClBD,OAAOE,WAAW,GAAGd,IAAI,CAAC,CAACe;oCACzB,IAAI,CAACF,aAAa;wCAChBA,cAAc;wCAEdjF,QACE,IAAIE,SAASiF,MAAM;4CACjBlH,SAAS+G,OAAO/G,OAAO;4CACvBkC,QAAQ6E,OAAO7E,MAAM;4CACrBiF,YAAYJ,OAAOI,UAAU;wCAC/B;oCAEJ;gCACF,GAAGN;gCACHC,IAAAA,4BAAiB,EAAC;oCAChB,IAAI,CAACE,aAAa;wCAChBA,cAAc;wCACdP,gBAAgBW,KAAK;wCACrBP,OAAOQ,2BAA2B/E,UAAU4D,KAAK;oCACnD;gCACF;4BACF,EAAE,OAAOL,KAAK;gCACZgB,OAAOhB;4BACT;wBACF;wBACAiB,IAAAA,4BAAiB,EAAC;4BAChB,IAAI,CAACH,iBAAiB;gCACpBA,kBAAkB;gCAClBF,gBAAgBW,KAAK;gCACrBP,OAAOQ,2BAA2B/E,UAAU4D,KAAK;4BACnD;wBACF;oBACF;oBACA,IAAIO,gBAAgBxB,MAAM,CAACa,OAAO,EAAE;wBAClC,uCAAuC;wBACvC,MAAMuB,2BAA2B/E,UAAU4D,KAAK;oBAClD,OAAO;wBACL,kFAAkF;wBAClF,kFAAkF;wBAClF,iBAAiB;wBACjBO,gBAAgBW,KAAK;oBACvB;gBACF,OAAO;oBACLtD,iBAAiB;wBACfc,MAAM;wBACNC,OAAO;wBACPC,YAAY,CAAC;wBACbtC;wBACAyB,YAAYC;wBACZkB,QAAQjB,0BAAc;wBACtBkB,OAAOlB,0BAAc;wBACrBmB,MAAM;+BAAI9C,aAAa8C,IAAI;yBAAC;oBAC9B;oBAEAvB,MAAM,MAAMtD,kDAAoB,CAACmF,GAAG,CAClC9B,gBACA1B,SACAK,SACAQ;gBAEJ;YACF,OAAO;gBACLc,MAAM,MAAMtD,kDAAoB,CAACmF,GAAG,CAClCrD,cACAH,SACAK,SACAQ;YAEJ;QACF,EAAE,OAAO4C,KAAK;YACZ,IAAIyB,IAAAA,8BAAe,EAACzB,MAAM;gBACxB,MAAMhC,MAAM0D,IAAAA,iCAAuB,EAAC1B;gBACpC,IAAI,CAAChC,KAAK;oBACR,MAAM,qBAAsD,CAAtD,IAAI5C,MAAM,8CAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAqD;gBAC7D;gBAEA,wDAAwD;gBACxD,gBAAgB;gBAChB,MAAMjB,UAAU,IAAIwH,QAAQ;oBAAEC,UAAU5D;gBAAI;gBAE5C,kDAAkD;gBAClD,cAAc;gBACd,yFAAyF;gBACzF,iFAAiF;gBACjF6D,IAAAA,oCAAoB,EAAC1H,SAASuC,aAAaoF,cAAc;gBAEzDrE;gBAEA,gCAAgC;gBAChC,OAAO,IAAIrB,SAAS,MAAM;oBACxB,mEAAmE;oBACnE,sEAAsE;oBACtE,4BAA4B;oBAC5BC,QAAQG,YAAYuF,QAAQ,GACxBC,sCAAkB,CAACC,QAAQ,GAC3BC,IAAAA,wCAA8B,EAAClC;oBACnC7F;gBACF;YACF,OAAO,IAAIgI,IAAAA,6CAAyB,EAACnC,MAAM;gBACzC,MAAMoC,aAAaC,IAAAA,+CAA2B,EAACrC;gBAC/C,OAAO,IAAI5D,SAAS,MAAM;oBAAEC,QAAQ+F;gBAAW;YACjD;YAEA,MAAMpC;QACR;QAEA,yDAAyD;QACzD,IAAI,CAAE9B,CAAAA,eAAe9B,QAAO,GAAI;YAC9B,MAAM,qBAEL,CAFK,IAAIhB,MACR,CAAC,4CAA4C,EAAE,IAAI,CAACV,gBAAgB,CAAC,0FAA0F,CAAC,GAD5J,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAmC,QAAQG,UAAU,CAACsF,YAAY,GAAG7F,UAAU6F,YAAY;QAExD7E;QAEA,IAAIQ,gBAAgB;gBACiBA;YAAnCpB,QAAQG,UAAU,CAACuF,aAAa,IAAGtE,uBAAAA,eAAewB,IAAI,qBAAnBxB,qBAAqBuE,IAAI,CAAC;YAC7D3F,QAAQG,UAAU,CAACyF,mBAAmB,GAAGxE,eAAeG,UAAU;YAClEvB,QAAQG,UAAU,CAAC0F,eAAe,GAAGzE,eAAesB,MAAM;YAC1D1C,QAAQG,UAAU,CAAC2F,cAAc,GAAG1E,eAAeuB,KAAK;QAC1D;QAEA,4DAA4D;QAC5D,0DAA0D;QAC1D,QAAQ;QACR,MAAMrF,UAAU,IAAIwH,QAAQzD,IAAI/D,OAAO;QACvC,IAAI0H,IAAAA,oCAAoB,EAAC1H,SAASuC,aAAaoF,cAAc,GAAG;YAC9D,OAAO,IAAI1F,SAAS8B,IAAImD,IAAI,EAAE;gBAC5BhF,QAAQ6B,IAAI7B,MAAM;gBAClBiF,YAAYpD,IAAIoD,UAAU;gBAC1BnH;YACF;QACF;QAEA,OAAO+D;IACT;IAEA,MAAa0E,OACXC,GAAgB,EAChBhG,OAAoC,EACjB;QACnB,iDAAiD;QACjD,MAAMN,UAAU,IAAI,CAACL,OAAO,CAAC2G,IAAIhH,MAAM;QAEvC,6CAA6C;QAC7C,MAAMiH,0BAA4C;YAChDC,MAAM,IAAI,CAACxI,UAAU,CAACwI,IAAI;YAC1B/F,YAAYH,QAAQG,UAAU;YAC9BgG,SAASnG,QAAQoG,aAAa,CAACD,OAAO;YACtCE,2BAA2B,EAAE;QAC/B;QAEA,+CAA+C;QAC/CJ,wBAAwB9F,UAAU,CAACmG,UAAU,GAAG,IAAI,CAAC7I,QAAQ,CAAC6I,UAAU;QAExE,MAAM3G,cAA2B;YAC/B4G,YAAY;YACZrB,UAAUsB,IAAAA,kDAAyB,EAACR;QACtC;QAEA,MAAMlG,eAAe,MAAM2G,IAAAA,6BAAe,EACxC,IAAI,CAAC/I,UAAU,CAACwI,IAAI,EACpBF,IAAIU,OAAO,EACX,iDAAiD;QACjD;QAGF,MAAM7G,eAAe8G,IAAAA,sCAAwB,EAC3CX,KACAA,IAAIU,OAAO,EACX5G,cACAa,WACAX,QAAQ4G,iBAAiB,CAACC,OAAO;QAGnC,MAAMjH,YAAYkH,IAAAA,0BAAe,EAACb;QAElC,0EAA0E;QAC1E,wEAAwE;QACxE,+CAA+C;QAC/C,MAAMc,WAAoB,MAAM,IAAI,CAAC7I,kBAAkB,CAACgF,GAAG,CACzDvD,aACA,IACE,IAAI,CAAC5B,oBAAoB,CAACmF,GAAG,CAACrD,cAAc,IAC1C,IAAI,CAAC7B,gBAAgB,CAACkF,GAAG,CAACtD,WAAW;oBACnC,mEAAmE;oBACnE,6BAA6B;oBAC7B,IAAI,IAAI,CAACzC,mBAAmB,EAAE;wBAC5B,IAAIyC,UAAUK,kBAAkB,EAAE;4BAChC,MAAMkD,MAAM,qBAEX,CAFW,IAAIW,sCAAkB,CAChC,0EADU,qBAAA;uCAAA;4CAAA;8CAAA;4BAEZ;4BACAlE,UAAUoH,uBAAuB,GAAG7D,IAAI8D,OAAO;4BAC/CrH,UAAUsH,iBAAiB,GAAG/D,IAAIgE,KAAK;4BACvC,MAAMhE;wBACR;oBACF;oBAEA,2EAA2E;oBAC3E,iFAAiF;oBACjF,IAAIpD,UAAUiG;oBAEd,oEAAoE;oBACpE,OAAQ,IAAI,CAAC1H,OAAO;wBAClB,KAAK;4BAAiB;gCACpB,8CAA8C;gCAC9CsB,UAAUwH,YAAY,GAAG;gCACzB,IAAIxH,UAAUK,kBAAkB,EAAE;oCAChC,MAAMkD,MAAM,qBAEX,CAFW,IAAIW,sCAAkB,CAChC,mFADU,qBAAA;+CAAA;oDAAA;sDAAA;oCAEZ;oCACAlE,UAAUoH,uBAAuB,GAAG7D,IAAI8D,OAAO;oCAC/CrH,UAAUsH,iBAAiB,GAAG/D,IAAIgE,KAAK;oCACvC,MAAMhE;gCACR;gCACA;4BACF;wBACA,KAAK;4BACH,4DAA4D;4BAC5D,+BAA+B;4BAC/BvD,UAAUyH,WAAW,GAAG;4BACxB,mEAAmE;4BACnE,2DAA2D;4BAC3DtH,UAAU,IAAIuH,MAAMtB,KAAKuB;4BACzB;wBACF,KAAK;4BACH,8DAA8D;4BAC9D,mDAAmD;4BACnD3H,UAAU4H,kBAAkB,GAAG;4BAC/B,IAAI5H,UAAUK,kBAAkB,EAC9BF,UAAU,IAAIuH,MAAMtB,KAAKyB;4BAC3B;wBACF,KAAK9G;wBACL,KAAK;4BACH,sDAAsD;4BACtD,6CAA6C;4BAC7CZ,UAAU2H,iBAAiB1B,KAAKpG;4BAChC;wBACF;4BACE,IAAI,CAACtB,OAAO;oBAChB;oBAEA,mDAAmD;oBACnD,MAAMkF,QAAQmE,IAAAA,wDAA2B,EAAC,IAAI,CAAC9J,gBAAgB;oBAE/D,MAAM+J,SAASC,IAAAA,iBAAS;oBAExB,gDAAgD;oBAChDD,OAAOE,oBAAoB,CAAC,cAActE;oBAE1C,OAAOoE,OAAOG,KAAK,CACjBC,oCAAyB,CAACC,UAAU,EACpC;wBACEC,UAAU,CAAC,0BAA0B,EAAE1E,OAAO;wBAC9C2E,YAAY;4BACV,cAAc3E;wBAChB;oBACF,GACA,UACE,IAAI,CAAC/D,EAAE,CACLC,SACAC,aACAC,WACAC,cACAC,cACAC,SACAC;gBAGR;QAIN,yEAAyE;QACzE,kBAAkB;QAClB,IAAI,CAAE+G,CAAAA,oBAAoBxH,QAAO,GAAI;YACnC,qEAAqE;YACrE,OAAO,IAAIA,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAC1C;QAEA,IAAIuH,SAASzJ,OAAO,CAAC8K,GAAG,CAAC,yBAAyB;YAChD,MAAM,qBAEL,CAFK,IAAI7J,MACR,uIADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIwI,SAASzJ,OAAO,CAAC+K,GAAG,CAAC,yBAAyB,KAAK;YACrD,iEAAiE;YACjE,MAAM,qBAEL,CAFK,IAAI9J,MACR,iLADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOwI;IACT;AACF;MAEA,WAAe9J;AASR,SAASE,oBAAoBmL,QAA0B;IAC5D,IACE,gDAAgD;IAChDA,SAASC,IAAI,IACbD,SAASE,GAAG,IACZF,SAASG,MAAM,IACfH,SAASI,KAAK,IACdJ,SAASK,OAAO,EAChB;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEA,0FAA0F;AAC1F,mDAAmD;AACnD,MAAMC,gBAAgBC,OAAO;AAC7B,MAAMC,qBAAqBD,OAAO;AAClC,MAAME,iBAAiBF,OAAO;AAC9B,MAAMG,qBAAqBH,OAAO;AAClC,MAAMI,aAAaJ,OAAO;AAC1B,MAAMK,iBAAiBL,OAAO;AAC9B,MAAMM,gBAAgBN,OAAO;AAC7B,MAAMO,gBAAgBP,OAAO;AAgB7B;;;;CAIC,GACD,MAAMtB,6BAA6B;IACjCc,KACEgB,MAAyC,EACzCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,KAAK;gBACH,OACED,MAAM,CAACF,cAAc,IACpBE,CAAAA,MAAM,CAACF,cAAc,GAAGK,uBAAc,CAACC,IAAI,CAAC,IAAI3E,QAAQ,CAAC,GAAE;YAEhE,KAAK;gBACH,OACEuE,MAAM,CAACD,cAAc,IACpBC,CAAAA,MAAM,CAACD,cAAc,GAAGM,qCAAqB,CAACD,IAAI,CACjD,IAAIE,uBAAc,CAAC,IAAI7E,QAAQ,CAAC,IAClC;YAEJ,KAAK;gBACH,OACEuE,MAAM,CAACT,cAAc,IACpBS,CAAAA,MAAM,CAACT,cAAc,GAAG,IAAItB,MAC3B+B,OAAO3C,OAAO,EACdkD,2BACF;YAEJ,KAAK;gBACH,sEAAsE;gBACtE,sEAAsE;gBACtE,oEAAoE;gBACpE,OAAOL,SAAS7C,OAAO,CAACmD,IAAI;YAC9B,KAAK;YACL,KAAK;gBACH,OAAOlJ;YACT,KAAK;gBACH,OACE0I,MAAM,CAACP,mBAAmB,IACzBO,CAAAA,MAAM,CAACP,mBAAmB,GAAG,IAC5B,IAAIxB,MACF,gFAAgF;oBAChF,mFAAmF;oBACnF,+EAA+E;oBAC/E,sFAAsF;oBACtF,yFAAyF;oBACzF,wFAAwF;oBACxF,2BAA2B;oBAC3B+B,OAAOS,KAAK,IACZvC,2BACF;YAEN;gBACE,OAAOwC,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AAGF;AAEA,MAAMK,6BAA6B;IACjCvB,KACEgB,MAAiC,EACjCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,iBAAiB;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OACED,MAAM,CAACL,mBAAmB,IACzBK,CAAAA,MAAM,CAACL,mBAAmB,GAAG,IAAIgB,iBAAgB;YAEtD,KAAK;gBACH,OACEX,MAAM,CAACJ,WAAW,IACjBI,CAAAA,MAAM,CAACJ,WAAW,GAAGgB,IAAAA,kBAAQ,EAACZ,OAAOQ,IAAI,EAAEA,IAAI,AAAD;YAEnD,KAAK;YACL,KAAK;gBACH,OACER,MAAM,CAACH,eAAe,IACrBG,CAAAA,MAAM,CAACH,eAAe,GAAG,IAAMK,SAASM,IAAI,AAAD;YAGhD,qBAAqB;YACrB,KAAK;gBACH,+FAA+F;gBAC/F,8FAA8F;gBAC9F,sDAAsD;gBACtD,OAAOlJ;YACT,KAAK;gBACH,OACE0I,MAAM,CAACN,eAAe,IACrBM,CAAAA,MAAM,CAACN,eAAe,GAAG,IACxB,IAAIzB,MAAM+B,OAAOS,KAAK,IAAIF,2BAA0B;YAE1D;gBACE,OAAOG,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AACF;AAEA,SAAS7B,iBAAiB3H,OAAoB,EAAEH,SAAoB;IAClE,MAAMsK,kBAAkB;QACtB7B,KACEgB,MAAiC,EACjCC,IAAqB,EACrBC,QAAa;YAEb,OAAQD;gBACN,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAU;wBACb,MAAMa,gBAAgBpM,kDAAoB,CAACqM,QAAQ;wBACnDC,aAAazK,WAAWuK,eAAe,CAAC,QAAQ,EAAEb,MAAM;wBACxD,OAAOS,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBACH,OACEF,MAAM,CAACN,eAAe,IACrBM,CAAAA,MAAM,CAACN,eAAe,GAAG,IACxB,IAAIzB,MAAM+B,OAAOS,KAAK,IAAII,gBAAe;gBAE/C;oBACE,OAAOH,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;YAC5C;QACF;IACF;IAEA,MAAMe,sBAAsB;QAC1BjC,KACEgB,MAAyC,EACzCC,IAAqB;YAErB,OAAQA;gBACN,KAAK;oBACH,OACED,MAAM,CAACT,cAAc,IACpBS,CAAAA,MAAM,CAACT,cAAc,GAAG,IAAItB,MAAM+B,OAAO3C,OAAO,EAAEwD,gBAAe;gBAEtE,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAY;wBACf,MAAMC,gBAAgBpM,kDAAoB,CAACqM,QAAQ;wBACnDC,aAAazK,WAAWuK,eAAe,CAAC,QAAQ,EAAEb,MAAM;wBACxD,gFAAgF;wBAChF,wFAAwF;wBACxF,uBAAuB;wBACvB,OAAOS,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMD;oBAC1C;gBACA,KAAK;oBACH,OACEA,MAAM,CAACP,mBAAmB,IACzBO,CAAAA,MAAM,CAACP,mBAAmB,GAAG,IAC5B,IAAIxB,MACF,gFAAgF;wBAChF,mFAAmF;wBACnF,+EAA+E;wBAC/E,sFAAsF;wBACtF,yFAAyF;wBACzF,wFAAwF;wBACxF,2BAA2B;wBAC3B+B,OAAOS,KAAK,IACZQ,oBACF;gBAEN;oBACE,gFAAgF;oBAChF,wFAAwF;oBACxF,uBAAuB;oBACvB,OAAOP,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMD;YAC5C;QACF;IAGF;IAEA,OAAO,IAAI/B,MAAMvH,SAASuK;AAC5B;AAEA,MAAM7C,+BAA+B;IACnCY,KACEgB,MAAyC,EACzCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,KAAK;gBACH,OACED,MAAM,CAACT,cAAc,IACpBS,CAAAA,MAAM,CAACT,cAAc,GAAG,IAAItB,MAC3B+B,OAAO3C,OAAO,EACd6D,6BACF;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEnB,OAAO3C,OAAO,CAAClI,QAAQ,CAAC,sFAAsF,EAAE8K,KAAK,GAAG,CAAC,GAD9H,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OACED,MAAM,CAACP,mBAAmB,IACzBO,CAAAA,MAAM,CAACP,mBAAmB,GAAG,IAC5B,IAAIxB,MACF,gFAAgF;oBAChF,mFAAmF;oBACnF,+EAA+E;oBAC/E,sFAAsF;oBACtF,yFAAyF;oBACzF,wFAAwF;oBACxF,2BAA2B;oBAC3B+B,OAAOS,KAAK,IACZrC,6BACF;YAEN;gBACE,OAAOsC,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AAGF;AAEA,MAAMgB,+BAA+B;IACnClC,KACEgB,MAAiC,EACjCC,IAAqB,EACrBC,QAAa;QAEb,OAAQD;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIkB,8CAAqB,CAC7B,CAAC,MAAM,EAAEnB,OAAO7K,QAAQ,CAAC,sFAAsF,EAAE8K,KAAK,GAAG,CAAC,GADtH,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OACED,MAAM,CAACN,eAAe,IACrBM,CAAAA,MAAM,CAACN,eAAe,GAAG,IACxB,IAAIzB,MAAM+B,OAAOS,KAAK,IAAIS,6BAA4B;YAE5D;gBACE,OAAOR,uBAAc,CAAC1B,GAAG,CAACgB,QAAQC,MAAMC;QAC5C;IACF;AACF;AAEA,SAAS5E,2BAA2BnB,KAAa;IAC/C,OAAO,qBAEN,CAFM,IAAIM,sCAAkB,CAC3B,CAAC,MAAM,EAAEN,MAAM,8IAA8I,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEP;AACF;AAEA,SAAS6G,aACPI,KAAgB,EAChBN,aAAwC,EACxCO,UAAkB;IAElB,IAAID,MAAMjD,kBAAkB,EAAE;QAC5B,MAAM,qBAEL,CAFK,IAAIgD,8CAAqB,CAC7B,CAAC,MAAM,EAAEC,MAAMjH,KAAK,CAAC,8EAA8E,EAAEkH,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIP,eAAe;QACjB,OAAQA,cAAcjI,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,mEAAmE;gBACnE,gDAAgD;gBAChD,MAAM,qBAEL,CAFK,IAAI3D,MACR,CAAC,MAAM,EAAEkM,MAAMjH,KAAK,CAAC,OAAO,EAAEkH,WAAW,gJAAgJ,EAAEA,WAAW,qKAAqK,CAAC,GADxW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAInM,MACR,CAAC,MAAM,EAAEkM,MAAMjH,KAAK,CAAC,OAAO,EAAEkH,WAAW,iLAAiL,EAAEA,WAAW,6KAA6K,CAAC,GADjZ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAMrN,QAAQ,qBAEb,CAFa,IAAIkB,MAChB,CAAC,MAAM,EAAEkM,MAAMjH,KAAK,CAAC,MAAM,EAAEkH,WAAW,+HAA+H,CAAC,GAD5J,qBAAA;2BAAA;gCAAA;kCAAA;gBAEd;gBACA,OAAOC,IAAAA,6DAA2C,EAChDF,MAAMjH,KAAK,EACXkH,YACArN,OACA8M;YAEJ,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIS,8BAAc,CACtB,qEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIA,8BAAc,CACtB,sEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOC,IAAAA,sCAAoB,EACzBJ,MAAMjH,KAAK,EACXkH,YACAP,cAAcpI,eAAe;YAEjC,KAAK;gBACHoI,cAAc5I,UAAU,GAAG;gBAE3B,MAAM4B,MAAM,qBAEX,CAFW,IAAIW,sCAAkB,CAChC,CAAC,MAAM,EAAE2G,MAAMjH,KAAK,CAAC,mDAAmD,EAAEkH,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAD,MAAMzD,uBAAuB,GAAG0D;gBAChCD,MAAMvD,iBAAiB,GAAG/D,IAAIgE,KAAK;gBAEnC,MAAMhE;YACR,KAAK;gBACH,IAAIzE,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;oBACzC,sEAAsE;oBACtE,uDAAuD;oBACvDuL,cAAcW,WAAW,GAAG;gBAC9B;gBACA;YACF;gBACEX;QACJ;IACF;AACF", "ignoreList": [0]}