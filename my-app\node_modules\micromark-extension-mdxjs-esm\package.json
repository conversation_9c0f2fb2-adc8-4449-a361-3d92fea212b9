{"name": "micromark-extension-mdxjs-esm", "version": "3.0.0", "description": "micromark extension to support MDX JS import/exports", "license": "MIT", "keywords": ["micromark", "micromark-extension", "mdx", "mdxjs", "import", "export", "js", "javascript", "es", "ecmascript", "markdown", "unified"], "repository": "micromark/micromark-extension-mdxjs-esm", "bugs": "https://github.com/micromark/micromark-extension-mdxjs-esm/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": {"development": "./dev/index.js", "default": "./index.js"}, "files": ["dev/", "lib/", "index.d.ts", "index.js"], "dependencies": {"@types/estree": "^1.0.0", "devlop": "^1.0.0", "micromark-core-commonmark": "^2.0.0", "micromark-util-character": "^2.0.0", "micromark-util-events-to-acorn": "^2.0.0", "micromark-util-symbol": "^2.0.0", "micromark-util-types": "^2.0.0", "unist-util-position-from-estree": "^2.0.0", "vfile-message": "^4.0.0"}, "devDependencies": {"@types/acorn": "^4.0.0", "@types/node": "^20.0.0", "acorn": "^8.0.0", "acorn-jsx": "^5.0.0", "c8": "^8.0.0", "micromark": "^4.0.0", "micromark-build": "^2.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.56.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage && micromark-build", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api-prod": "node --conditions production test/index.js", "test-api-dev": "node --conditions development test/index.js", "test-api": "npm run test-api-dev && npm run test-api-prod", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"prettier": true, "rules": {"n/file-extension-in-import": "off", "unicorn/no-this-assignment": "off"}, "overrides": [{"files": "test/**/*.js", "rules": {"no-await-in-loop": "off"}}]}}