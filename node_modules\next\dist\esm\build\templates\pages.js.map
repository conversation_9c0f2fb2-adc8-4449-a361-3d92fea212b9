{"version": 3, "sources": ["../../../src/build/templates/pages.ts"], "sourcesContent": ["import { PagesRouteModule } from '../../server/route-modules/pages/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\n\nimport { hoist } from './helpers'\n\n// Import the app and document modules.\nimport * as document from 'VAR_MODULE_DOCUMENT'\nimport * as app from 'VAR_MODULE_APP'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\nimport { getHandler } from '../../server/route-modules/pages/pages-handler'\n\n// Re-export the component (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export methods.\nexport const getStaticProps = hoist(userland, 'getStaticProps')\nexport const getStaticPaths = hoist(userland, 'getStaticPaths')\nexport const getServerSideProps = hoist(userland, 'getServerSideProps')\nexport const config = hoist(userland, 'config')\nexport const reportWebVitals = hoist(userland, 'reportWebVitals')\n\n// Re-export legacy methods.\nexport const unstable_getStaticProps = hoist(\n  userland,\n  'unstable_getStaticProps'\n)\nexport const unstable_getStaticPaths = hoist(\n  userland,\n  'unstable_getStaticPaths'\n)\nexport const unstable_getStaticParams = hoist(\n  userland,\n  'unstable_getStaticParams'\n)\nexport const unstable_getServerProps = hoist(\n  userland,\n  'unstable_getServerProps'\n)\nexport const unstable_getServerSideProps = hoist(\n  userland,\n  'unstable_getServerSideProps'\n)\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesRouteModule({\n  definition: {\n    kind: RouteKind.PAGES,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n  components: {\n    // default export might not exist when optimized for data only\n    App: app.default,\n    Document: document.default,\n  },\n  userland,\n})\n\nexport const handler = getHandler({\n  srcPage: 'VAR_DEFINITION_PAGE',\n  config,\n  userland,\n  routeModule,\n  getStaticPaths,\n  getStaticProps,\n  getServerSideProps,\n})\n"], "names": ["PagesRouteModule", "RouteKind", "hoist", "document", "app", "userland", "<PERSON><PERSON><PERSON><PERSON>", "getStaticProps", "getStaticPaths", "getServerSideProps", "config", "reportWebVitals", "unstable_getStaticProps", "unstable_getStaticPaths", "unstable_getStaticParams", "unstable_getServerProps", "unstable_getServerSideProps", "routeModule", "definition", "kind", "PAGES", "page", "pathname", "bundlePath", "filename", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "relativeProjectDir", "__NEXT_RELATIVE_PROJECT_DIR", "components", "App", "default", "Document", "handler", "srcPage"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,SAAS,QAAQ,0BAAyB;AAEnD,SAASC,KAAK,QAAQ,YAAW;AAEjC,uCAAuC;AACvC,YAAYC,cAAc,sBAAqB;AAC/C,YAAYC,SAAS,iBAAgB;AAErC,4BAA4B;AAC5B,YAAYC,cAAc,eAAc;AACxC,SAASC,UAAU,QAAQ,iDAAgD;AAE3E,0DAA0D;AAC1D,eAAeJ,MAAMG,UAAU,WAAU;AAEzC,qBAAqB;AACrB,OAAO,MAAME,iBAAiBL,MAAMG,UAAU,kBAAiB;AAC/D,OAAO,MAAMG,iBAAiBN,MAAMG,UAAU,kBAAiB;AAC/D,OAAO,MAAMI,qBAAqBP,MAAMG,UAAU,sBAAqB;AACvE,OAAO,MAAMK,SAASR,MAAMG,UAAU,UAAS;AAC/C,OAAO,MAAMM,kBAAkBT,MAAMG,UAAU,mBAAkB;AAEjE,4BAA4B;AAC5B,OAAO,MAAMO,0BAA0BV,MACrCG,UACA,2BACD;AACD,OAAO,MAAMQ,0BAA0BX,MACrCG,UACA,2BACD;AACD,OAAO,MAAMS,2BAA2BZ,MACtCG,UACA,4BACD;AACD,OAAO,MAAMU,0BAA0Bb,MACrCG,UACA,2BACD;AACD,OAAO,MAAMW,8BAA8Bd,MACzCG,UACA,+BACD;AAED,4DAA4D;AAC5D,OAAO,MAAMY,cAAc,IAAIjB,iBAAiB;IAC9CkB,YAAY;QACVC,MAAMlB,UAAUmB,KAAK;QACrBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAC,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,oBAAoBH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;IAC/DC,YAAY;QACV,8DAA8D;QAC9DC,KAAK5B,IAAI6B,OAAO;QAChBC,UAAU/B,SAAS8B,OAAO;IAC5B;IACA5B;AACF,GAAE;AAEF,OAAO,MAAM8B,UAAU7B,WAAW;IAChC8B,SAAS;IACT1B;IACAL;IACAY;IACAT;IACAD;IACAE;AACF,GAAE", "ignoreList": [0]}