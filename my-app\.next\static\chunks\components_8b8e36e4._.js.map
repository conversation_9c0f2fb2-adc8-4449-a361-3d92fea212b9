{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/components/search.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  SearchDialog,\n  SearchDialogClose,\n  SearchDialogContent,\n  SearchDialogHeader,\n  SearchDialogIcon,\n  SearchDialogInput,\n  SearchDialogList,\n  SearchDialogOverlay,\n  type SharedProps,\n} from 'fumadocs-ui/components/dialog/search';\nimport { useDocsSearch } from 'fumadocs-core/search/client';\nimport { create } from '@orama/orama';\nimport { useI18n } from 'fumadocs-ui/contexts/i18n';\n\nfunction initOrama() {\n  return create({\n    schema: { _: 'string' },\n    // https://docs.orama.com/docs/orama-js/supported-languages\n    language: 'english',\n  });\n}\n\nexport default function DefaultSearchDialog(props: SharedProps) {\n  const { locale } = useI18n(); // (optional) for i18n\n\n  // 根据环境选择搜索类型\n  const isStatic = process.env.NODE_ENV === 'production' && typeof window !== 'undefined' && !window.location.host.includes('localhost');\n\n  const { search, setSearch, query } = useDocsSearch({\n    type: isStatic ? 'static' : 'fetch',\n    api: isStatic ? undefined : '/api/search',\n    initOrama: isStatic ? initOrama : undefined,\n    locale,\n  });\n\n  return (\n    <SearchDialog\n      search={search}\n      onSearchChange={setSearch}\n      isLoading={query.isLoading}\n      {...props}\n    >\n      <SearchDialogOverlay />\n      <SearchDialogContent>\n        <SearchDialogHeader>\n          <SearchDialogIcon />\n          <SearchDialogInput />\n          <SearchDialogClose />\n        </SearchDialogHeader>\n        <SearchDialogList items={query.data !== 'empty' ? query.data : null} />\n      </SearchDialogContent>\n    </SearchDialog>\n  );\n}\n"], "names": [], "mappings": ";;;AA6BmB;;AA3BnB;AAWA;AACA;AAAA;AACA;;;AAfA;;;;;AAiBA,SAAS;IACP,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,EAAE;QACZ,QAAQ;YAAE,GAAG;QAAS;QACtB,2DAA2D;QAC3D,UAAU;IACZ;AACF;AAEe,SAAS,oBAAoB,KAAkB;;IAC5D,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,KAAK,sBAAsB;IAEpD,aAAa;IACb,MAAM,WAAW,oDAAyB,gBAAgB,aAAkB,eAAe,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;IAE1H,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE;QACjD,MAAM,sCAAW,0BAAW;QAC5B,KAAK,sCAAW,0BAAY;QAC5B,WAAW,sCAAW,0BAAY;QAClC;IACF;IAEA,qBACE,6LAAC,2KAAA,CAAA,eAAY;QACX,QAAQ;QACR,gBAAgB;QAChB,WAAW,MAAM,SAAS;QACzB,GAAG,KAAK;;0BAET,6LAAC,2KAAA,CAAA,sBAAmB;;;;;0BACpB,6LAAC,2KAAA,CAAA,sBAAmB;;kCAClB,6LAAC,2KAAA,CAAA,qBAAkB;;0CACjB,6LAAC,2KAAA,CAAA,mBAAgB;;;;;0CACjB,6LAAC,2KAAA,CAAA,oBAAiB;;;;;0CAClB,6LAAC,2KAAA,CAAA,oBAAiB;;;;;;;;;;;kCAEpB,6LAAC,2KAAA,CAAA,mBAAgB;wBAAC,OAAO,MAAM,IAAI,KAAK,UAAU,MAAM,IAAI,GAAG;;;;;;;;;;;;;;;;;;AAIvE;GA/BwB;;QACH,6JAAA,CAAA,UAAO;QAKW,+JAAA,CAAA,gBAAa;;;KAN5B", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test-dosc/my-app/components/provider.tsx"], "sourcesContent": ["'use client';\n\nimport { RootProvider } from 'fumadocs-ui/provider';\n// your custom dialog\nimport SearchDialog from '@/components/search';\nimport type { ReactNode } from 'react';\n\nexport function Provider({ children }: { children: ReactNode }) {\n  return (\n    <RootProvider\n      search={{\n        SearchDialog,\n      }}\n    >\n      {children}\n    </RootProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA,qBAAqB;AACrB;AAJA;;;;AAOO,SAAS,SAAS,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;IACvB,qBACE,6LAAC,8KAAA,CAAA,eAAY;QACX,QAAQ;YACN,cAAA,wHAAA,CAAA,UAAY;QACd;kBAEC;;;;;;AAGP;KAVgB", "debugId": null}}]}