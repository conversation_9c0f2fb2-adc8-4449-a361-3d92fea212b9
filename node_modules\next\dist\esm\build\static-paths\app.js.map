{"version": 3, "sources": ["../../../src/build/static-paths/app.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\nimport type { AppPageModule } from '../../server/route-modules/app-page/module'\nimport type { AppSegment } from '../segment-config/app/app-segments'\nimport type { PrerenderedRoute, StaticPathsResult } from './types'\n\nimport path from 'node:path'\nimport { AfterRunner } from '../../server/after/run-with-after'\nimport { createWorkStore } from '../../server/async-storage/work-store'\nimport { FallbackMode } from '../../lib/fallback'\nimport { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport {\n  getRouteRegex,\n  type RouteRegex,\n} from '../../shared/lib/router/utils/route-regex'\nimport type { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { normalizePathname, encodeParam } from './utils'\nimport escapePathDelimiters from '../../shared/lib/router/utils/escape-path-delimiters'\nimport { createIncrementalCache } from '../../export/helpers/create-incremental-cache'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\n\n/**\n * Filters out duplicate parameters from a list of parameters.\n * This function uses a Map to efficiently store and retrieve unique parameter combinations.\n *\n * @param routeParamKeys - The keys of the parameters. These should be sorted to ensure consistent key generation.\n * @param routeParams - The list of parameter objects to filter.\n * @returns A new array containing only the unique parameter combinations.\n */\nexport function filterUniqueParams(\n  routeParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  // A Map is used to store unique parameter combinations. The key of the Map\n  // is a string representation of the parameter combination, and the value\n  // is the actual `Params` object.\n  const unique = new Map<string, Params>()\n\n  // Iterate over each parameter object in the input array.\n  for (const params of routeParams) {\n    let key = '' // Initialize an empty string to build the unique key for the current `params` object.\n\n    // Iterate through the `routeParamKeys` (which are assumed to be sorted).\n    // This consistent order is crucial for generating a stable and unique key\n    // for each parameter combination.\n    for (const paramKey of routeParamKeys) {\n      const value = params[paramKey]\n\n      // Construct a part of the key using the parameter key and its value.\n      // A type prefix (`A:` for Array, `S:` for String, `U:` for undefined) is added to the value\n      // to prevent collisions. For example, `['a', 'b']` and `'a,b'` would\n      // otherwise generate the same string representation, leading to incorrect\n      // deduplication. This ensures that different types with the same string\n      // representation are treated as distinct.\n      let valuePart: string\n      if (Array.isArray(value)) {\n        valuePart = `A:${value.join(',')}`\n      } else if (value === undefined) {\n        valuePart = `U:undefined`\n      } else {\n        valuePart = `S:${value}`\n      }\n      key += `${paramKey}:${valuePart}|`\n    }\n\n    // If the generated key is not already in the `unique` Map, it means this\n    // parameter combination is unique so far. Add it to the Map.\n    if (!unique.has(key)) {\n      unique.set(key, params)\n    }\n  }\n\n  // Convert the Map's values (the unique `Params` objects) back into an array\n  // and return it.\n  return Array.from(unique.values())\n}\n\n/**\n * Generates all unique sub-combinations of Route Parameters from a list of Static Parameters.\n * This function creates all possible prefixes of the Route Parameters, which is\n * useful for generating Static Shells that can serve as Fallback Shells for more specific Route Shells.\n *\n * When Root Parameters are provided, the function ensures that Static Shells only\n * include complete sets of Root Parameters. This prevents generating invalid Static Shells\n * that are missing required Root Parameters.\n *\n * Example with Root Parameters ('lang', 'region') and Route Parameters ('lang', 'region', 'slug'):\n *\n * Given the following Static Parameters:\n * ```\n * [\n *   { lang: 'en', region: 'US', slug: ['home'] },\n *   { lang: 'en', region: 'US', slug: ['about'] },\n *   { lang: 'fr', region: 'CA', slug: ['about'] },\n * ]\n * ```\n *\n * The result will be:\n * ```\n * [\n *   { lang: 'en', region: 'US' },  // Complete Root Parameters\n *   { lang: 'en', region: 'US', slug: ['home'] },\n *   { lang: 'en', region: 'US', slug: ['about'] },\n *   { lang: 'fr', region: 'CA' },  // Complete Root Parameters\n *   { lang: 'fr', region: 'CA', slug: ['about'] },\n * ]\n * ```\n *\n * Note that partial combinations like `{ lang: 'en' }` are NOT generated because\n * they don't include the complete set of Root Parameters.\n *\n * For routes without Root Parameters (e.g., `/[slug]`), all sub-combinations are generated\n * as before.\n *\n * @param routeParamKeys - The keys of the Route Parameters. These should be sorted\n *   to ensure consistent key generation for the internal Map.\n * @param routeParams - The list of Static Parameters to filter.\n * @param rootParamKeys - The keys of the Root Parameters. When provided, ensures Static Shells\n *   include all Root Parameters.\n * @returns A new array containing all unique sub-combinations of Route Parameters.\n */\nexport function generateAllParamCombinations(\n  routeParamKeys: readonly string[],\n  routeParams: readonly Params[],\n  rootParamKeys: readonly string[]\n): Params[] {\n  // A Map is used to store unique combinations of Route Parameters.\n  // The key of the Map is a string representation of the Route Parameter\n  // combination, and the value is the `Params` object containing only\n  // the Route Parameters.\n  const combinations = new Map<string, Params>()\n\n  // Determine the minimum index where all Root Parameters are included.\n  // This optimization ensures we only generate combinations that include\n  // a complete set of Root Parameters, preventing invalid Static Shells.\n  //\n  // For example, if rootParamKeys = ['lang', 'region'] and routeParamKeys = ['lang', 'region', 'slug']:\n  // - 'lang' is at index 0, 'region' is at index 1\n  // - minIndexForCompleteRootParams = max(0, 1) = 1\n  // - We'll only generate combinations starting from index 1 (which includes both lang and region)\n  let minIndexForCompleteRootParams = -1\n  if (rootParamKeys.length > 0) {\n    // Find the index of the last Root Parameter in routeParamKeys.\n    // This tells us the minimum combination length needed to include all Root Parameters.\n    for (const rootParamKey of rootParamKeys) {\n      const index = routeParamKeys.indexOf(rootParamKey)\n      if (index === -1) {\n        // Root Parameter not found in Route Parameters - this shouldn't happen in normal cases\n        // but we handle it gracefully by treating it as if there are no Root Parameters.\n        // This allows the function to fall back to generating all sub-combinations.\n        minIndexForCompleteRootParams = -1\n        break\n      }\n      // Track the highest index among all Root Parameters.\n      // This ensures all Root Parameters are included in any generated combination.\n      minIndexForCompleteRootParams = Math.max(\n        minIndexForCompleteRootParams,\n        index\n      )\n    }\n  }\n\n  // Iterate over each Static Parameter object in the input array.\n  // Each params object represents one potential route combination (e.g., { lang: 'en', region: 'US', slug: 'home' })\n  for (const params of routeParams) {\n    // Generate all possible prefix combinations for this Static Parameter set.\n    // For routeParamKeys = ['lang', 'region', 'slug'], we'll generate combinations at:\n    // - i=0: { lang: 'en' }\n    // - i=1: { lang: 'en', region: 'US' }\n    // - i=2: { lang: 'en', region: 'US', slug: 'home' }\n    //\n    // The iteration order is crucial for generating stable and unique keys\n    // for each Route Parameter combination.\n    for (let i = 0; i < routeParamKeys.length; i++) {\n      // Skip generating combinations that don't include all Root Parameters.\n      // This prevents creating invalid Static Shells that are missing required Root Parameters.\n      //\n      // For example, if Root Parameters are ['lang', 'region'] and minIndexForCompleteRootParams = 1:\n      // - Skip i=0 (would only include 'lang', missing 'region')\n      // - Process i=1 and higher (includes both 'lang' and 'region')\n      if (\n        minIndexForCompleteRootParams >= 0 &&\n        i < minIndexForCompleteRootParams\n      ) {\n        continue\n      }\n\n      // Initialize data structures for building this specific combination\n      const combination: Params = {}\n      const keyParts: string[] = []\n      let hasAllRootParams = true\n\n      // Build the sub-combination with parameters from index 0 to i (inclusive).\n      // This creates a prefix of the full parameter set, building up combinations incrementally.\n      //\n      // For example, if routeParamKeys = ['lang', 'region', 'slug'] and i = 1:\n      // - j=0: Add 'lang' parameter\n      // - j=1: Add 'region' parameter\n      // Result: { lang: 'en', region: 'US' }\n      for (let j = 0; j <= i; j++) {\n        const routeKey = routeParamKeys[j]\n\n        // Check if the parameter exists in the original params object and has a defined value.\n        // This handles cases where generateStaticParams doesn't provide all possible parameters,\n        // or where some parameters are optional/undefined.\n        if (\n          !params.hasOwnProperty(routeKey) ||\n          params[routeKey] === undefined\n        ) {\n          // If this missing parameter is a Root Parameter, mark the combination as invalid.\n          // Root Parameters are required for Static Shells, so we can't generate partial combinations without them.\n          if (rootParamKeys.includes(routeKey)) {\n            hasAllRootParams = false\n          }\n          // Stop building this combination since we've hit a missing parameter.\n          // This ensures we only generate valid prefix combinations with consecutive parameters.\n          break\n        }\n\n        const value = params[routeKey]\n        combination[routeKey] = value\n\n        // Construct a unique key part for this parameter to enable deduplication.\n        // We use type prefixes to prevent collisions between different value types\n        // that might have the same string representation.\n        //\n        // Examples:\n        // - Array ['foo', 'bar'] becomes \"A:foo,bar\"\n        // - String \"foo,bar\" becomes \"S:foo,bar\"\n        // - This prevents collisions between ['foo', 'bar'] and \"foo,bar\"\n        let valuePart: string\n        if (Array.isArray(value)) {\n          valuePart = `A:${value.join(',')}`\n        } else {\n          valuePart = `S:${value}`\n        }\n        keyParts.push(`${routeKey}:${valuePart}`)\n      }\n\n      // Build the final unique key by joining all parameter parts.\n      // This key is used for deduplication in the combinations Map.\n      // Format: \"lang:S:en|region:S:US|slug:A:home,about\"\n      const currentKey = keyParts.join('|')\n\n      // Only add the combination if it meets our criteria:\n      // 1. hasAllRootParams: Contains all required Root Parameters\n      // 2. !combinations.has(currentKey): Is not a duplicate of an existing combination\n      //\n      // This ensures we only generate valid, unique parameter combinations for Static Shells.\n      if (hasAllRootParams && !combinations.has(currentKey)) {\n        combinations.set(currentKey, combination)\n      }\n    }\n  }\n\n  // Convert the Map's values back into an array and return the final result.\n  // The Map ensures all combinations are unique, and we return only the\n  // parameter objects themselves, discarding the internal deduplication keys.\n  return Array.from(combinations.values())\n}\n\n/**\n * Calculates the fallback mode based on the given parameters.\n *\n * @param dynamicParams - Whether dynamic params are enabled.\n * @param fallbackRootParams - The root params that are part of the fallback.\n * @param baseFallbackMode - The base fallback mode to use.\n * @returns The calculated fallback mode.\n */\nexport function calculateFallbackMode(\n  dynamicParams: boolean,\n  fallbackRootParams: readonly string[],\n  baseFallbackMode: FallbackMode | undefined\n): FallbackMode {\n  return dynamicParams\n    ? // If the fallback params includes any root params, then we need to\n      // perform a blocking static render.\n      fallbackRootParams.length > 0\n      ? FallbackMode.BLOCKING_STATIC_RENDER\n      : baseFallbackMode ?? FallbackMode.NOT_FOUND\n    : FallbackMode.NOT_FOUND\n}\n\n/**\n * Validates the parameters to ensure they're accessible and have the correct\n * types.\n *\n * @param page - The page to validate.\n * @param regex - The route regex.\n * @param isRoutePPREnabled - Whether the route has partial prerendering enabled.\n * @param routeParamKeys - The keys of the parameters.\n * @param rootParamKeys - The keys of the root params.\n * @param routeParams - The list of parameters to validate.\n * @returns The list of validated parameters.\n */\nfunction validateParams(\n  page: string,\n  regex: RouteRegex,\n  isRoutePPREnabled: boolean,\n  routeParamKeys: readonly string[],\n  rootParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  const valid: Params[] = []\n\n  // Validate that if there are any root params, that the user has provided at\n  // least one value for them only if we're using partial prerendering.\n  if (isRoutePPREnabled && rootParamKeys.length > 0) {\n    if (\n      routeParams.length === 0 ||\n      rootParamKeys.some((key) =>\n        routeParams.some((params) => !(key in params))\n      )\n    ) {\n      if (rootParamKeys.length === 1) {\n        throw new Error(\n          `A required root parameter (${rootParamKeys[0]}) was not provided in generateStaticParams for ${page}, please provide at least one value.`\n        )\n      }\n\n      throw new Error(\n        `Required root params (${rootParamKeys.join(', ')}) were not provided in generateStaticParams for ${page}, please provide at least one value for each.`\n      )\n    }\n  }\n\n  for (const params of routeParams) {\n    const item: Params = {}\n\n    for (const key of routeParamKeys) {\n      const { repeat, optional } = regex.groups[key]\n\n      let paramValue = params[key]\n\n      if (\n        optional &&\n        params.hasOwnProperty(key) &&\n        (paramValue === null ||\n          paramValue === undefined ||\n          (paramValue as any) === false)\n      ) {\n        paramValue = []\n      }\n\n      // A parameter is missing, so the rest of the params are not accessible.\n      // We only support this when the route has partial prerendering enabled.\n      // This will make it so that the remaining params are marked as missing so\n      // we can generate a fallback route for them.\n      if (!paramValue && isRoutePPREnabled) {\n        break\n      }\n\n      // Perform validation for the parameter based on whether it's a repeat\n      // parameter or not.\n      if (repeat) {\n        if (!Array.isArray(paramValue)) {\n          throw new Error(\n            `A required parameter (${key}) was not provided as an array received ${typeof paramValue} in generateStaticParams for ${page}`\n          )\n        }\n      } else {\n        if (typeof paramValue !== 'string') {\n          throw new Error(\n            `A required parameter (${key}) was not provided as a string received ${typeof paramValue} in generateStaticParams for ${page}`\n          )\n        }\n      }\n\n      item[key] = paramValue\n    }\n\n    valid.push(item)\n  }\n\n  return valid\n}\n\ninterface TrieNode {\n  /**\n   * The children of the node. Each key is a unique string representation of a parameter value,\n   * and the value is the next TrieNode in the path.\n   */\n  children: Map<string, TrieNode>\n\n  /**\n   * The routes that are associated with this specific parameter combination (node).\n   * These are the routes whose concrete parameters lead to this node in the Trie.\n   */\n  routes: PrerenderedRoute[]\n}\n\n/**\n * Assigns the throwOnEmptyStaticShell property to each of the prerendered routes.\n * This function uses a Trie data structure to efficiently determine whether each route\n * should throw an error when its static shell is empty.\n *\n * A route should not throw on empty static shell if it has child routes in the Trie. For example,\n * if we have two routes, `/blog/first-post` and `/blog/[slug]`, the route for\n * `/blog/[slug]` should not throw because `/blog/first-post` is a more specific concrete route.\n *\n * @param prerenderedRoutes - The prerendered routes.\n * @param routeParamKeys - The keys of the route parameters.\n */\nexport function assignErrorIfEmpty(\n  prerenderedRoutes: readonly PrerenderedRoute[],\n  routeParamKeys: readonly string[]\n): void {\n  // If there are no routes to process, exit early.\n  if (prerenderedRoutes.length === 0) {\n    return\n  }\n\n  // Initialize the root of the Trie. This node represents the starting point\n  // before any parameters have been considered.\n  const root: TrieNode = { children: new Map(), routes: [] }\n\n  // Phase 1: Build the Trie.\n  // Iterate over each prerendered route and insert it into the Trie.\n  // Each route's concrete parameter values form a path in the Trie.\n  for (const route of prerenderedRoutes) {\n    let currentNode = root // Start building the path from the root for each route.\n\n    // Iterate through the sorted parameter keys. The order of keys is crucial\n    // for ensuring that routes with the same concrete parameters follow the\n    // same path in the Trie, regardless of the original order of properties\n    // in the `params` object.\n    for (const key of routeParamKeys) {\n      // Check if the current route actually has a concrete value for this parameter.\n      // If a dynamic segment is not filled (i.e., it's a fallback), it won't have\n      // this property, and we stop building the path for this route at this point.\n      if (route.params.hasOwnProperty(key)) {\n        const value = route.params[key]\n\n        // Generate a unique key for the parameter's value. This is critical\n        // to prevent collisions between different data types that might have\n        // the same string representation (e.g., `['a', 'b']` vs `'a,b'`).\n        // A type prefix (`A:` for Array, `S:` for String, `U:` for undefined)\n        // is added to the value to prevent collisions. This ensures that\n        // different types with the same string representation are treated as\n        // distinct.\n        let valueKey: string\n        if (Array.isArray(value)) {\n          valueKey = `A:${value.join(',')}`\n        } else if (value === undefined) {\n          valueKey = `U:undefined`\n        } else {\n          valueKey = `S:${value}`\n        }\n\n        // Look for a child node corresponding to this `valueKey` from the `currentNode`.\n        let childNode = currentNode.children.get(valueKey)\n        if (!childNode) {\n          // If the child node doesn't exist, create a new one and add it to\n          // the current node's children.\n          childNode = { children: new Map(), routes: [] }\n          currentNode.children.set(valueKey, childNode)\n        }\n        // Move deeper into the Trie to the `childNode` for the next parameter.\n        currentNode = childNode\n      }\n    }\n    // After processing all concrete parameters for the route, add the full\n    // `PrerenderedRoute` object to the `routes` array of the `currentNode`.\n    // This node represents the unique concrete parameter combination for this route.\n    currentNode.routes.push(route)\n  }\n\n  // Phase 2: Traverse the Trie to assign the `throwOnEmptyStaticShell` property.\n  // This is done using an iterative Depth-First Search (DFS) approach with an\n  // explicit stack to avoid JavaScript's recursion depth limits (stack overflow)\n  // for very deep routing structures.\n  const stack: TrieNode[] = [root] // Initialize the stack with the root node.\n\n  while (stack.length > 0) {\n    const node = stack.pop()! // Pop the next node to process from the stack.\n\n    // `hasChildren` indicates if this node has any more specific concrete\n    // parameter combinations branching off from it. If true, it means this\n    // node represents a prefix for other, more specific routes.\n    const hasChildren = node.children.size > 0\n\n    // If the current node has routes associated with it (meaning, routes whose\n    // concrete parameters lead to this node's path in the Trie).\n    if (node.routes.length > 0) {\n      // Determine the minimum number of fallback parameters among all routes\n      // that are associated with this current Trie node. This is used to\n      // identify if a route should not throw on empty static shell relative to another route *at the same level*\n      // of concrete parameters, but with fewer fallback parameters.\n      let minFallbacks = Infinity\n      for (const r of node.routes) {\n        // `fallbackRouteParams?.length ?? 0` handles cases where `fallbackRouteParams`\n        // might be `undefined` or `null`, treating them as 0 length.\n        minFallbacks = Math.min(\n          minFallbacks,\n          r.fallbackRouteParams?.length ?? 0\n        )\n      }\n\n      // Now, for each `PrerenderedRoute` associated with this node:\n      for (const route of node.routes) {\n        // A route is ok not to throw on an empty static shell (and thus\n        // `throwOnEmptyStaticShell` should be `false`) if either of the\n        // following conditions is met:\n        // 1. `hasChildren` is true: This node has further concrete parameter children.\n        //    This means the current route is a parent to more specific routes (e.g.,\n        //    `/blog/[slug]` should not throw when concrete routes like `/blog/first-post` exist).\n        // OR\n        // 2. `route.fallbackRouteParams.length > minFallbacks`: This route has\n        //    more fallback parameters than another route at the same Trie node.\n        //    This implies the current route is a more general version that should not throw\n        //    compared to a more specific route that has fewer fallback parameters\n        //    (e.g., `/1234/[...slug]` should not throw relative to `/[id]/[...slug]`).\n        if (\n          hasChildren ||\n          (route.fallbackRouteParams &&\n            route.fallbackRouteParams.length > minFallbacks)\n        ) {\n          route.throwOnEmptyStaticShell = false // Should not throw on empty static shell.\n        } else {\n          route.throwOnEmptyStaticShell = true // Should throw on empty static shell.\n        }\n      }\n    }\n\n    // Add all children of the current node to the stack. This ensures that\n    // the traversal continues to explore deeper paths in the Trie.\n    for (const child of node.children.values()) {\n      stack.push(child)\n    }\n  }\n}\n\n/**\n * Processes app directory segments to build route parameters from generateStaticParams functions.\n * This function walks through the segments array and calls generateStaticParams for each segment that has it,\n * combining parent parameters with child parameters to build the complete parameter combinations.\n * Uses iterative processing instead of recursion for better performance.\n *\n * @param segments - Array of app directory segments to process\n * @param store - Work store for tracking fetch cache configuration\n * @returns Promise that resolves to an array of all parameter combinations\n */\nexport async function generateRouteStaticParams(\n  segments: Pick<AppSegment, 'config' | 'generateStaticParams'>[],\n  store: Pick<WorkStore, 'fetchCache'>\n): Promise<Params[]> {\n  // Early return if no segments to process\n  if (segments.length === 0) return []\n\n  // Use iterative processing with a work queue to avoid recursion overhead\n  interface WorkItem {\n    segmentIndex: number\n    params: Params[]\n  }\n\n  const queue: WorkItem[] = [{ segmentIndex: 0, params: [] }]\n  let currentParams: Params[] = []\n\n  while (queue.length > 0) {\n    const { segmentIndex, params } = queue.shift()!\n\n    // If we've processed all segments, this is our final result\n    if (segmentIndex >= segments.length) {\n      currentParams = params\n      break\n    }\n\n    const current = segments[segmentIndex]\n\n    // Skip segments without generateStaticParams and continue to next\n    if (typeof current.generateStaticParams !== 'function') {\n      queue.push({ segmentIndex: segmentIndex + 1, params })\n      continue\n    }\n\n    // Configure fetchCache if specified\n    if (current.config?.fetchCache !== undefined) {\n      store.fetchCache = current.config.fetchCache\n    }\n\n    const nextParams: Params[] = []\n\n    // If there are parent params, we need to process them.\n    if (params.length > 0) {\n      // Process each parent parameter combination\n      for (const parentParams of params) {\n        const result = await current.generateStaticParams({\n          params: parentParams,\n        })\n\n        if (result.length > 0) {\n          // Merge parent params with each result item\n          for (const item of result) {\n            nextParams.push({ ...parentParams, ...item })\n          }\n        } else {\n          // No results, just pass through parent params\n          nextParams.push(parentParams)\n        }\n      }\n    } else {\n      // No parent params, call generateStaticParams with empty object\n      const result = await current.generateStaticParams({ params: {} })\n      nextParams.push(...result)\n    }\n\n    // Add next segment to work queue\n    queue.push({ segmentIndex: segmentIndex + 1, params: nextParams })\n  }\n\n  return currentParams\n}\n\n/**\n * Builds the static paths for an app using `generateStaticParams`.\n *\n * @param params - The parameters for the build.\n * @returns The static paths.\n */\nexport async function buildAppStaticPaths({\n  dir,\n  page,\n  distDir,\n  cacheComponents,\n  authInterrupts,\n  segments,\n  isrFlushToDisk,\n  cacheHandler,\n  cacheLifeProfiles,\n  requestHeaders,\n  cacheHandlers,\n  maxMemoryCacheSize,\n  fetchCacheKeyPrefix,\n  nextConfigOutput,\n  ComponentMod,\n  isRoutePPREnabled = false,\n  buildId,\n  rootParamKeys,\n}: {\n  dir: string\n  page: string\n  cacheComponents: boolean\n  authInterrupts: boolean\n  segments: AppSegment[]\n  distDir: string\n  isrFlushToDisk?: boolean\n  fetchCacheKeyPrefix?: string\n  cacheHandler?: string\n  cacheHandlers?: NextConfigComplete['experimental']['cacheHandlers']\n  cacheLifeProfiles?: {\n    [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n  }\n  maxMemoryCacheSize?: number\n  requestHeaders: IncrementalCache['requestHeaders']\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  ComponentMod: AppPageModule\n  isRoutePPREnabled: boolean\n  buildId: string\n  rootParamKeys: readonly string[]\n}): Promise<StaticPathsResult> {\n  if (\n    segments.some((generate) => generate.config?.dynamicParams === true) &&\n    nextConfigOutput === 'export'\n  ) {\n    throw new Error(\n      '\"dynamicParams: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/app/building-your-application/deploying/static-exports'\n    )\n  }\n\n  ComponentMod.patchFetch()\n\n  const incrementalCache = await createIncrementalCache({\n    dir,\n    distDir,\n    cacheHandler,\n    cacheHandlers,\n    requestHeaders,\n    fetchCacheKeyPrefix,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  const regex = getRouteRegex(page)\n  const routeParamKeys = Object.keys(getRouteMatcher(regex)(page) || {})\n\n  const afterRunner = new AfterRunner()\n\n  const store = createWorkStore({\n    page,\n    renderOpts: {\n      incrementalCache,\n      cacheLifeProfiles,\n      supportsDynamicResponse: true,\n      isRevalidate: false,\n      experimental: {\n        cacheComponents,\n        authInterrupts,\n      },\n      waitUntil: afterRunner.context.waitUntil,\n      onClose: afterRunner.context.onClose,\n      onAfterTaskError: afterRunner.context.onTaskError,\n    },\n    buildId,\n    previouslyRevalidatedTags: [],\n  })\n\n  const routeParams = await ComponentMod.workAsyncStorage.run(store, () =>\n    generateRouteStaticParams(segments, store)\n  )\n\n  await afterRunner.executeAfter()\n\n  let lastDynamicSegmentHadGenerateStaticParams = false\n  for (const segment of segments) {\n    // Check to see if there are any missing params for segments that have\n    // dynamicParams set to false.\n    if (\n      segment.param &&\n      segment.isDynamicSegment &&\n      segment.config?.dynamicParams === false\n    ) {\n      for (const params of routeParams) {\n        if (segment.param in params) continue\n\n        const relative = segment.filePath\n          ? path.relative(dir, segment.filePath)\n          : undefined\n\n        throw new Error(\n          `Segment \"${relative}\" exports \"dynamicParams: false\" but the param \"${segment.param}\" is missing from the generated route params.`\n        )\n      }\n    }\n\n    if (\n      segment.isDynamicSegment &&\n      typeof segment.generateStaticParams !== 'function'\n    ) {\n      lastDynamicSegmentHadGenerateStaticParams = false\n    } else if (typeof segment.generateStaticParams === 'function') {\n      lastDynamicSegmentHadGenerateStaticParams = true\n    }\n  }\n\n  // Determine if all the segments have had their parameters provided.\n  const hadAllParamsGenerated =\n    routeParamKeys.length === 0 ||\n    (routeParams.length > 0 &&\n      routeParams.every((params) => {\n        for (const key of routeParamKeys) {\n          if (key in params) continue\n          return false\n        }\n        return true\n      }))\n\n  // TODO: dynamic params should be allowed to be granular per segment but\n  // we need additional information stored/leveraged in the prerender\n  // manifest to allow this behavior.\n  const dynamicParams = segments.every(\n    (segment) => segment.config?.dynamicParams !== false\n  )\n\n  const supportsRoutePreGeneration =\n    hadAllParamsGenerated || process.env.NODE_ENV === 'production'\n\n  const fallbackMode = dynamicParams\n    ? supportsRoutePreGeneration\n      ? isRoutePPREnabled\n        ? FallbackMode.PRERENDER\n        : FallbackMode.BLOCKING_STATIC_RENDER\n      : undefined\n    : FallbackMode.NOT_FOUND\n\n  const prerenderedRoutesByPathname = new Map<string, PrerenderedRoute>()\n\n  // Precompile the regex patterns for the route params.\n  const paramPatterns = new Map<string, string>()\n  for (const key of routeParamKeys) {\n    const { repeat, optional } = regex.groups[key]\n    let pattern = `[${repeat ? '...' : ''}${key}]`\n    if (optional) {\n      pattern = `[${pattern}]`\n    }\n    paramPatterns.set(key, pattern)\n  }\n\n  // Convert rootParamKeys to Set for O(1) lookup.\n  const rootParamSet = new Set(rootParamKeys)\n\n  if (hadAllParamsGenerated || isRoutePPREnabled) {\n    let paramsToProcess = routeParams\n\n    if (isRoutePPREnabled) {\n      // Discover all unique combinations of the routeParams so we can generate\n      // routes that won't throw on empty static shell for each of them if\n      // they're available.\n      paramsToProcess = generateAllParamCombinations(\n        routeParamKeys,\n        routeParams,\n        rootParamKeys\n      )\n\n      // Add the base route, this is the route with all the placeholders as it's\n      // derived from the `page` string.\n      prerenderedRoutesByPathname.set(page, {\n        params: {},\n        pathname: page,\n        encodedPathname: page,\n        fallbackRouteParams: routeParamKeys,\n        fallbackMode: calculateFallbackMode(\n          dynamicParams,\n          rootParamKeys,\n          fallbackMode\n        ),\n        fallbackRootParams: rootParamKeys,\n        throwOnEmptyStaticShell: true,\n      })\n    }\n\n    filterUniqueParams(\n      routeParamKeys,\n      validateParams(\n        page,\n        regex,\n        isRoutePPREnabled,\n        routeParamKeys,\n        rootParamKeys,\n        paramsToProcess\n      )\n    ).forEach((params) => {\n      let pathname = page\n      let encodedPathname = page\n\n      const fallbackRouteParams: string[] = []\n\n      for (const key of routeParamKeys) {\n        const paramValue = params[key]\n\n        if (!paramValue) {\n          if (isRoutePPREnabled) {\n            // Mark remaining params as fallback params.\n            fallbackRouteParams.push(key)\n            for (\n              let i = routeParamKeys.indexOf(key) + 1;\n              i < routeParamKeys.length;\n              i++\n            ) {\n              fallbackRouteParams.push(routeParamKeys[i])\n            }\n            break\n          } else {\n            // This route is not complete, and we aren't performing a partial\n            // prerender, so we should return, skipping this route.\n            return\n          }\n        }\n\n        // Use pre-compiled pattern for replacement\n        const pattern = paramPatterns.get(key)!\n        pathname = pathname.replace(\n          pattern,\n          encodeParam(paramValue, (value) => escapePathDelimiters(value, true))\n        )\n        encodedPathname = encodedPathname.replace(\n          pattern,\n          encodeParam(paramValue, encodeURIComponent)\n        )\n      }\n\n      const fallbackRootParams: string[] = []\n      for (const param of fallbackRouteParams) {\n        if (rootParamSet.has(param)) {\n          fallbackRootParams.push(param)\n        }\n      }\n\n      pathname = normalizePathname(pathname)\n\n      prerenderedRoutesByPathname.set(pathname, {\n        params,\n        pathname,\n        encodedPathname: normalizePathname(encodedPathname),\n        fallbackRouteParams,\n        fallbackMode: calculateFallbackMode(\n          dynamicParams,\n          fallbackRootParams,\n          fallbackMode\n        ),\n        fallbackRootParams,\n        throwOnEmptyStaticShell: true,\n      })\n    })\n  }\n\n  const prerenderedRoutes =\n    prerenderedRoutesByPathname.size > 0 ||\n    lastDynamicSegmentHadGenerateStaticParams\n      ? [...prerenderedRoutesByPathname.values()]\n      : undefined\n\n  // Now we have to set the throwOnEmptyStaticShell for each of the routes.\n  if (prerenderedRoutes && cacheComponents) {\n    assignErrorIfEmpty(prerenderedRoutes, routeParamKeys)\n  }\n\n  return { fallbackMode, prerenderedRoutes }\n}\n"], "names": ["path", "After<PERSON><PERSON>ner", "createWorkStore", "FallbackMode", "getRouteMatcher", "getRouteRegex", "normalizePathname", "encodeParam", "escapePathDelimiters", "createIncrementalCache", "filterUniqueParams", "routeParamKeys", "routeParams", "unique", "Map", "params", "key", "<PERSON><PERSON><PERSON><PERSON>", "value", "valuePart", "Array", "isArray", "join", "undefined", "has", "set", "from", "values", "generateAllParamCombinations", "rootParamKeys", "combinations", "minIndexForCompleteRootParams", "length", "rootParamKey", "index", "indexOf", "Math", "max", "i", "combination", "keyParts", "hasAllRootParams", "j", "routeKey", "hasOwnProperty", "includes", "push", "current<PERSON><PERSON>", "calculateFallbackMode", "dynamicParams", "fallbackRootParams", "baseFallbackMode", "BLOCKING_STATIC_RENDER", "NOT_FOUND", "validateParams", "page", "regex", "isRoutePPREnabled", "valid", "some", "Error", "item", "repeat", "optional", "groups", "paramValue", "assignErrorIfEmpty", "prerenderedRoutes", "root", "children", "routes", "route", "currentNode", "valueKey", "childNode", "get", "stack", "node", "pop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "minFallbacks", "Infinity", "r", "min", "fallbackRouteParams", "throwOnEmptyStaticShell", "child", "generateRouteStaticParams", "segments", "store", "queue", "segmentIndex", "currentParams", "current", "shift", "generateStaticParams", "config", "fetchCache", "nextParams", "parentParams", "result", "buildAppStaticPaths", "dir", "distDir", "cacheComponents", "authInterrupts", "isrFlushToDisk", "cache<PERSON><PERSON><PERSON>", "cacheLifeProfiles", "requestHeaders", "cacheHandlers", "maxMemoryCacheSize", "fetchCacheKeyPrefix", "nextConfigOutput", "ComponentMod", "buildId", "generate", "patchFetch", "incrementalCache", "flushToDisk", "cacheMaxMemorySize", "Object", "keys", "after<PERSON><PERSON>ner", "renderOpts", "supportsDynamicResponse", "isRevalidate", "experimental", "waitUntil", "context", "onClose", "onAfterTaskError", "onTaskError", "previouslyRevalidatedTags", "workAsyncStorage", "run", "executeAfter", "lastDynamicSegmentHadGenerateStaticParams", "segment", "param", "isDynamicSegment", "relative", "filePath", "hadAllParamsGenerated", "every", "supportsRoutePreGeneration", "process", "env", "NODE_ENV", "fallbackMode", "PRERENDER", "prerenderedRoutesByPathname", "paramPatterns", "pattern", "rootParamSet", "Set", "paramsToProcess", "pathname", "encodedPathname", "for<PERSON>ach", "replace", "encodeURIComponent"], "mappings": "AAKA,OAAOA,UAAU,YAAW;AAC5B,SAASC,WAAW,QAAQ,oCAAmC;AAC/D,SAASC,eAAe,QAAQ,wCAAuC;AACvE,SAASC,YAAY,QAAQ,qBAAoB;AACjD,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SACEC,aAAa,QAER,4CAA2C;AAElD,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,UAAS;AACxD,OAAOC,0BAA0B,uDAAsD;AACvF,SAASC,sBAAsB,QAAQ,gDAA+C;AAItF;;;;;;;CAOC,GACD,OAAO,SAASC,mBACdC,cAAiC,EACjCC,WAA8B;IAE9B,2EAA2E;IAC3E,yEAAyE;IACzE,iCAAiC;IACjC,MAAMC,SAAS,IAAIC;IAEnB,yDAAyD;IACzD,KAAK,MAAMC,UAAUH,YAAa;QAChC,IAAII,MAAM,GAAG,sFAAsF;;QAEnG,yEAAyE;QACzE,0EAA0E;QAC1E,kCAAkC;QAClC,KAAK,MAAMC,YAAYN,eAAgB;YACrC,MAAMO,QAAQH,MAAM,CAACE,SAAS;YAE9B,qEAAqE;YACrE,4FAA4F;YAC5F,qEAAqE;YACrE,0EAA0E;YAC1E,wEAAwE;YACxE,0CAA0C;YAC1C,IAAIE;YACJ,IAAIC,MAAMC,OAAO,CAACH,QAAQ;gBACxBC,YAAY,CAAC,EAAE,EAAED,MAAMI,IAAI,CAAC,MAAM;YACpC,OAAO,IAAIJ,UAAUK,WAAW;gBAC9BJ,YAAY,CAAC,WAAW,CAAC;YAC3B,OAAO;gBACLA,YAAY,CAAC,EAAE,EAAED,OAAO;YAC1B;YACAF,OAAO,GAAGC,SAAS,CAAC,EAAEE,UAAU,CAAC,CAAC;QACpC;QAEA,yEAAyE;QACzE,6DAA6D;QAC7D,IAAI,CAACN,OAAOW,GAAG,CAACR,MAAM;YACpBH,OAAOY,GAAG,CAACT,KAAKD;QAClB;IACF;IAEA,4EAA4E;IAC5E,iBAAiB;IACjB,OAAOK,MAAMM,IAAI,CAACb,OAAOc,MAAM;AACjC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2CC,GACD,OAAO,SAASC,6BACdjB,cAAiC,EACjCC,WAA8B,EAC9BiB,aAAgC;IAEhC,kEAAkE;IAClE,uEAAuE;IACvE,oEAAoE;IACpE,wBAAwB;IACxB,MAAMC,eAAe,IAAIhB;IAEzB,sEAAsE;IACtE,uEAAuE;IACvE,uEAAuE;IACvE,EAAE;IACF,sGAAsG;IACtG,iDAAiD;IACjD,kDAAkD;IAClD,iGAAiG;IACjG,IAAIiB,gCAAgC,CAAC;IACrC,IAAIF,cAAcG,MAAM,GAAG,GAAG;QAC5B,+DAA+D;QAC/D,sFAAsF;QACtF,KAAK,MAAMC,gBAAgBJ,cAAe;YACxC,MAAMK,QAAQvB,eAAewB,OAAO,CAACF;YACrC,IAAIC,UAAU,CAAC,GAAG;gBAChB,uFAAuF;gBACvF,iFAAiF;gBACjF,4EAA4E;gBAC5EH,gCAAgC,CAAC;gBACjC;YACF;YACA,qDAAqD;YACrD,8EAA8E;YAC9EA,gCAAgCK,KAAKC,GAAG,CACtCN,+BACAG;QAEJ;IACF;IAEA,gEAAgE;IAChE,mHAAmH;IACnH,KAAK,MAAMnB,UAAUH,YAAa;QAChC,2EAA2E;QAC3E,mFAAmF;QACnF,wBAAwB;QACxB,sCAAsC;QACtC,oDAAoD;QACpD,EAAE;QACF,uEAAuE;QACvE,wCAAwC;QACxC,IAAK,IAAI0B,IAAI,GAAGA,IAAI3B,eAAeqB,MAAM,EAAEM,IAAK;YAC9C,uEAAuE;YACvE,0FAA0F;YAC1F,EAAE;YACF,gGAAgG;YAChG,2DAA2D;YAC3D,+DAA+D;YAC/D,IACEP,iCAAiC,KACjCO,IAAIP,+BACJ;gBACA;YACF;YAEA,oEAAoE;YACpE,MAAMQ,cAAsB,CAAC;YAC7B,MAAMC,WAAqB,EAAE;YAC7B,IAAIC,mBAAmB;YAEvB,2EAA2E;YAC3E,2FAA2F;YAC3F,EAAE;YACF,yEAAyE;YACzE,8BAA8B;YAC9B,gCAAgC;YAChC,uCAAuC;YACvC,IAAK,IAAIC,IAAI,GAAGA,KAAKJ,GAAGI,IAAK;gBAC3B,MAAMC,WAAWhC,cAAc,CAAC+B,EAAE;gBAElC,uFAAuF;gBACvF,yFAAyF;gBACzF,mDAAmD;gBACnD,IACE,CAAC3B,OAAO6B,cAAc,CAACD,aACvB5B,MAAM,CAAC4B,SAAS,KAAKpB,WACrB;oBACA,kFAAkF;oBAClF,0GAA0G;oBAC1G,IAAIM,cAAcgB,QAAQ,CAACF,WAAW;wBACpCF,mBAAmB;oBACrB;oBAGA;gBACF;gBAEA,MAAMvB,QAAQH,MAAM,CAAC4B,SAAS;gBAC9BJ,WAAW,CAACI,SAAS,GAAGzB;gBAExB,0EAA0E;gBAC1E,2EAA2E;gBAC3E,kDAAkD;gBAClD,EAAE;gBACF,YAAY;gBACZ,6CAA6C;gBAC7C,yCAAyC;gBACzC,kEAAkE;gBAClE,IAAIC;gBACJ,IAAIC,MAAMC,OAAO,CAACH,QAAQ;oBACxBC,YAAY,CAAC,EAAE,EAAED,MAAMI,IAAI,CAAC,MAAM;gBACpC,OAAO;oBACLH,YAAY,CAAC,EAAE,EAAED,OAAO;gBAC1B;gBACAsB,SAASM,IAAI,CAAC,GAAGH,SAAS,CAAC,EAAExB,WAAW;YAC1C;YAEA,6DAA6D;YAC7D,8DAA8D;YAC9D,oDAAoD;YACpD,MAAM4B,aAAaP,SAASlB,IAAI,CAAC;YAEjC,qDAAqD;YACrD,6DAA6D;YAC7D,kFAAkF;YAClF,EAAE;YACF,wFAAwF;YACxF,IAAImB,oBAAoB,CAACX,aAAaN,GAAG,CAACuB,aAAa;gBACrDjB,aAAaL,GAAG,CAACsB,YAAYR;YAC/B;QACF;IACF;IAEA,2EAA2E;IAC3E,sEAAsE;IACtE,4EAA4E;IAC5E,OAAOnB,MAAMM,IAAI,CAACI,aAAaH,MAAM;AACvC;AAEA;;;;;;;CAOC,GACD,OAAO,SAASqB,sBACdC,aAAsB,EACtBC,kBAAqC,EACrCC,gBAA0C;IAE1C,OAAOF,gBAEH,oCAAoC;IACpCC,mBAAmBlB,MAAM,GAAG,IAC1B7B,aAAaiD,sBAAsB,GACnCD,oBAAoBhD,aAAakD,SAAS,GAC5ClD,aAAakD,SAAS;AAC5B;AAEA;;;;;;;;;;;CAWC,GACD,SAASC,eACPC,IAAY,EACZC,KAAiB,EACjBC,iBAA0B,EAC1B9C,cAAiC,EACjCkB,aAAgC,EAChCjB,WAA8B;IAE9B,MAAM8C,QAAkB,EAAE;IAE1B,4EAA4E;IAC5E,qEAAqE;IACrE,IAAID,qBAAqB5B,cAAcG,MAAM,GAAG,GAAG;QACjD,IACEpB,YAAYoB,MAAM,KAAK,KACvBH,cAAc8B,IAAI,CAAC,CAAC3C,MAClBJ,YAAY+C,IAAI,CAAC,CAAC5C,SAAW,CAAEC,CAAAA,OAAOD,MAAK,KAE7C;YACA,IAAIc,cAAcG,MAAM,KAAK,GAAG;gBAC9B,MAAM,qBAEL,CAFK,IAAI4B,MACR,CAAC,2BAA2B,EAAE/B,aAAa,CAAC,EAAE,CAAC,+CAA+C,EAAE0B,KAAK,oCAAoC,CAAC,GADtI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAM,qBAEL,CAFK,IAAIK,MACR,CAAC,sBAAsB,EAAE/B,cAAcP,IAAI,CAAC,MAAM,gDAAgD,EAAEiC,KAAK,6CAA6C,CAAC,GADnJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,KAAK,MAAMxC,UAAUH,YAAa;QAChC,MAAMiD,OAAe,CAAC;QAEtB,KAAK,MAAM7C,OAAOL,eAAgB;YAChC,MAAM,EAAEmD,MAAM,EAAEC,QAAQ,EAAE,GAAGP,MAAMQ,MAAM,CAAChD,IAAI;YAE9C,IAAIiD,aAAalD,MAAM,CAACC,IAAI;YAE5B,IACE+C,YACAhD,OAAO6B,cAAc,CAAC5B,QACrBiD,CAAAA,eAAe,QACdA,eAAe1C,aACf,AAAC0C,eAAuB,KAAI,GAC9B;gBACAA,aAAa,EAAE;YACjB;YAEA,wEAAwE;YACxE,wEAAwE;YACxE,0EAA0E;YAC1E,6CAA6C;YAC7C,IAAI,CAACA,cAAcR,mBAAmB;gBACpC;YACF;YAEA,sEAAsE;YACtE,oBAAoB;YACpB,IAAIK,QAAQ;gBACV,IAAI,CAAC1C,MAAMC,OAAO,CAAC4C,aAAa;oBAC9B,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,sBAAsB,EAAE5C,IAAI,wCAAwC,EAAE,OAAOiD,WAAW,6BAA6B,EAAEV,MAAM,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF,OAAO;gBACL,IAAI,OAAOU,eAAe,UAAU;oBAClC,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,sBAAsB,EAAE5C,IAAI,wCAAwC,EAAE,OAAOiD,WAAW,6BAA6B,EAAEV,MAAM,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEAM,IAAI,CAAC7C,IAAI,GAAGiD;QACd;QAEAP,MAAMZ,IAAI,CAACe;IACb;IAEA,OAAOH;AACT;AAgBA;;;;;;;;;;;CAWC,GACD,OAAO,SAASQ,mBACdC,iBAA8C,EAC9CxD,cAAiC;IAEjC,iDAAiD;IACjD,IAAIwD,kBAAkBnC,MAAM,KAAK,GAAG;QAClC;IACF;IAEA,2EAA2E;IAC3E,8CAA8C;IAC9C,MAAMoC,OAAiB;QAAEC,UAAU,IAAIvD;QAAOwD,QAAQ,EAAE;IAAC;IAEzD,2BAA2B;IAC3B,mEAAmE;IACnE,kEAAkE;IAClE,KAAK,MAAMC,SAASJ,kBAAmB;QACrC,IAAIK,cAAcJ,KAAK,wDAAwD;;QAE/E,0EAA0E;QAC1E,wEAAwE;QACxE,wEAAwE;QACxE,0BAA0B;QAC1B,KAAK,MAAMpD,OAAOL,eAAgB;YAChC,+EAA+E;YAC/E,4EAA4E;YAC5E,6EAA6E;YAC7E,IAAI4D,MAAMxD,MAAM,CAAC6B,cAAc,CAAC5B,MAAM;gBACpC,MAAME,QAAQqD,MAAMxD,MAAM,CAACC,IAAI;gBAE/B,oEAAoE;gBACpE,qEAAqE;gBACrE,kEAAkE;gBAClE,sEAAsE;gBACtE,iEAAiE;gBACjE,qEAAqE;gBACrE,YAAY;gBACZ,IAAIyD;gBACJ,IAAIrD,MAAMC,OAAO,CAACH,QAAQ;oBACxBuD,WAAW,CAAC,EAAE,EAAEvD,MAAMI,IAAI,CAAC,MAAM;gBACnC,OAAO,IAAIJ,UAAUK,WAAW;oBAC9BkD,WAAW,CAAC,WAAW,CAAC;gBAC1B,OAAO;oBACLA,WAAW,CAAC,EAAE,EAAEvD,OAAO;gBACzB;gBAEA,iFAAiF;gBACjF,IAAIwD,YAAYF,YAAYH,QAAQ,CAACM,GAAG,CAACF;gBACzC,IAAI,CAACC,WAAW;oBACd,kEAAkE;oBAClE,+BAA+B;oBAC/BA,YAAY;wBAAEL,UAAU,IAAIvD;wBAAOwD,QAAQ,EAAE;oBAAC;oBAC9CE,YAAYH,QAAQ,CAAC5C,GAAG,CAACgD,UAAUC;gBACrC;gBACA,uEAAuE;gBACvEF,cAAcE;YAChB;QACF;QACA,uEAAuE;QACvE,wEAAwE;QACxE,iFAAiF;QACjFF,YAAYF,MAAM,CAACxB,IAAI,CAACyB;IAC1B;IAEA,+EAA+E;IAC/E,4EAA4E;IAC5E,+EAA+E;IAC/E,oCAAoC;IACpC,MAAMK,QAAoB;QAACR;KAAK,CAAC,2CAA2C;;IAE5E,MAAOQ,MAAM5C,MAAM,GAAG,EAAG;QACvB,MAAM6C,OAAOD,MAAME,GAAG,EAAI,+CAA+C;;QAEzE,sEAAsE;QACtE,uEAAuE;QACvE,4DAA4D;QAC5D,MAAMC,cAAcF,KAAKR,QAAQ,CAACW,IAAI,GAAG;QAEzC,2EAA2E;QAC3E,6DAA6D;QAC7D,IAAIH,KAAKP,MAAM,CAACtC,MAAM,GAAG,GAAG;YAC1B,uEAAuE;YACvE,mEAAmE;YACnE,2GAA2G;YAC3G,8DAA8D;YAC9D,IAAIiD,eAAeC;YACnB,KAAK,MAAMC,KAAKN,KAAKP,MAAM,CAAE;oBAKzBa;gBAJF,+EAA+E;gBAC/E,6DAA6D;gBAC7DF,eAAe7C,KAAKgD,GAAG,CACrBH,cACAE,EAAAA,yBAAAA,EAAEE,mBAAmB,qBAArBF,uBAAuBnD,MAAM,KAAI;YAErC;YAEA,8DAA8D;YAC9D,KAAK,MAAMuC,SAASM,KAAKP,MAAM,CAAE;gBAC/B,gEAAgE;gBAChE,gEAAgE;gBAChE,+BAA+B;gBAC/B,+EAA+E;gBAC/E,6EAA6E;gBAC7E,0FAA0F;gBAC1F,KAAK;gBACL,uEAAuE;gBACvE,wEAAwE;gBACxE,oFAAoF;gBACpF,0EAA0E;gBAC1E,+EAA+E;gBAC/E,IACES,eACCR,MAAMc,mBAAmB,IACxBd,MAAMc,mBAAmB,CAACrD,MAAM,GAAGiD,cACrC;oBACAV,MAAMe,uBAAuB,GAAG,MAAM,0CAA0C;;gBAClF,OAAO;oBACLf,MAAMe,uBAAuB,GAAG,KAAK,sCAAsC;;gBAC7E;YACF;QACF;QAEA,uEAAuE;QACvE,+DAA+D;QAC/D,KAAK,MAAMC,SAASV,KAAKR,QAAQ,CAAC1C,MAAM,GAAI;YAC1CiD,MAAM9B,IAAI,CAACyC;QACb;IACF;AACF;AAEA;;;;;;;;;CASC,GACD,OAAO,eAAeC,0BACpBC,QAA+D,EAC/DC,KAAoC;IAEpC,yCAAyC;IACzC,IAAID,SAASzD,MAAM,KAAK,GAAG,OAAO,EAAE;IAQpC,MAAM2D,QAAoB;QAAC;YAAEC,cAAc;YAAG7E,QAAQ,EAAE;QAAC;KAAE;IAC3D,IAAI8E,gBAA0B,EAAE;IAEhC,MAAOF,MAAM3D,MAAM,GAAG,EAAG;YAkBnB8D;QAjBJ,MAAM,EAAEF,YAAY,EAAE7E,MAAM,EAAE,GAAG4E,MAAMI,KAAK;QAE5C,4DAA4D;QAC5D,IAAIH,gBAAgBH,SAASzD,MAAM,EAAE;YACnC6D,gBAAgB9E;YAChB;QACF;QAEA,MAAM+E,UAAUL,QAAQ,CAACG,aAAa;QAEtC,kEAAkE;QAClE,IAAI,OAAOE,QAAQE,oBAAoB,KAAK,YAAY;YACtDL,MAAM7C,IAAI,CAAC;gBAAE8C,cAAcA,eAAe;gBAAG7E;YAAO;YACpD;QACF;QAEA,oCAAoC;QACpC,IAAI+E,EAAAA,kBAAAA,QAAQG,MAAM,qBAAdH,gBAAgBI,UAAU,MAAK3E,WAAW;YAC5CmE,MAAMQ,UAAU,GAAGJ,QAAQG,MAAM,CAACC,UAAU;QAC9C;QAEA,MAAMC,aAAuB,EAAE;QAE/B,uDAAuD;QACvD,IAAIpF,OAAOiB,MAAM,GAAG,GAAG;YACrB,4CAA4C;YAC5C,KAAK,MAAMoE,gBAAgBrF,OAAQ;gBACjC,MAAMsF,SAAS,MAAMP,QAAQE,oBAAoB,CAAC;oBAChDjF,QAAQqF;gBACV;gBAEA,IAAIC,OAAOrE,MAAM,GAAG,GAAG;oBACrB,4CAA4C;oBAC5C,KAAK,MAAM6B,QAAQwC,OAAQ;wBACzBF,WAAWrD,IAAI,CAAC;4BAAE,GAAGsD,YAAY;4BAAE,GAAGvC,IAAI;wBAAC;oBAC7C;gBACF,OAAO;oBACL,8CAA8C;oBAC9CsC,WAAWrD,IAAI,CAACsD;gBAClB;YACF;QACF,OAAO;YACL,gEAAgE;YAChE,MAAMC,SAAS,MAAMP,QAAQE,oBAAoB,CAAC;gBAAEjF,QAAQ,CAAC;YAAE;YAC/DoF,WAAWrD,IAAI,IAAIuD;QACrB;QAEA,iCAAiC;QACjCV,MAAM7C,IAAI,CAAC;YAAE8C,cAAcA,eAAe;YAAG7E,QAAQoF;QAAW;IAClE;IAEA,OAAON;AACT;AAEA;;;;;CAKC,GACD,OAAO,eAAeS,oBAAoB,EACxCC,GAAG,EACHhD,IAAI,EACJiD,OAAO,EACPC,eAAe,EACfC,cAAc,EACdjB,QAAQ,EACRkB,cAAc,EACdC,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,kBAAkB,EAClBC,mBAAmB,EACnBC,gBAAgB,EAChBC,YAAY,EACZ1D,oBAAoB,KAAK,EACzB2D,OAAO,EACPvF,aAAa,EAsBd;IACC,IACE4D,SAAS9B,IAAI,CAAC,CAAC0D;YAAaA;eAAAA,EAAAA,mBAAAA,SAASpB,MAAM,qBAAfoB,iBAAiBpE,aAAa,MAAK;UAC/DiE,qBAAqB,UACrB;QACA,MAAM,qBAEL,CAFK,IAAItD,MACR,mKADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAuD,aAAaG,UAAU;IAEvB,MAAMC,mBAAmB,MAAM9G,uBAAuB;QACpD8F;QACAC;QACAI;QACAG;QACAD;QACAG;QACAO,aAAab;QACbc,oBAAoBT;IACtB;IAEA,MAAMxD,QAAQnD,cAAckD;IAC5B,MAAM5C,iBAAiB+G,OAAOC,IAAI,CAACvH,gBAAgBoD,OAAOD,SAAS,CAAC;IAEpE,MAAMqE,cAAc,IAAI3H;IAExB,MAAMyF,QAAQxF,gBAAgB;QAC5BqD;QACAsE,YAAY;YACVN;YACAV;YACAiB,yBAAyB;YACzBC,cAAc;YACdC,cAAc;gBACZvB;gBACAC;YACF;YACAuB,WAAWL,YAAYM,OAAO,CAACD,SAAS;YACxCE,SAASP,YAAYM,OAAO,CAACC,OAAO;YACpCC,kBAAkBR,YAAYM,OAAO,CAACG,WAAW;QACnD;QACAjB;QACAkB,2BAA2B,EAAE;IAC/B;IAEA,MAAM1H,cAAc,MAAMuG,aAAaoB,gBAAgB,CAACC,GAAG,CAAC9C,OAAO,IACjEF,0BAA0BC,UAAUC;IAGtC,MAAMkC,YAAYa,YAAY;IAE9B,IAAIC,4CAA4C;IAChD,KAAK,MAAMC,WAAWlD,SAAU;YAM5BkD;QALF,sEAAsE;QACtE,8BAA8B;QAC9B,IACEA,QAAQC,KAAK,IACbD,QAAQE,gBAAgB,IACxBF,EAAAA,kBAAAA,QAAQ1C,MAAM,qBAAd0C,gBAAgB1F,aAAa,MAAK,OAClC;YACA,KAAK,MAAMlC,UAAUH,YAAa;gBAChC,IAAI+H,QAAQC,KAAK,IAAI7H,QAAQ;gBAE7B,MAAM+H,WAAWH,QAAQI,QAAQ,GAC7B/I,KAAK8I,QAAQ,CAACvC,KAAKoC,QAAQI,QAAQ,IACnCxH;gBAEJ,MAAM,qBAEL,CAFK,IAAIqC,MACR,CAAC,SAAS,EAAEkF,SAAS,gDAAgD,EAAEH,QAAQC,KAAK,CAAC,6CAA6C,CAAC,GAD/H,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IACED,QAAQE,gBAAgB,IACxB,OAAOF,QAAQ3C,oBAAoB,KAAK,YACxC;YACA0C,4CAA4C;QAC9C,OAAO,IAAI,OAAOC,QAAQ3C,oBAAoB,KAAK,YAAY;YAC7D0C,4CAA4C;QAC9C;IACF;IAEA,oEAAoE;IACpE,MAAMM,wBACJrI,eAAeqB,MAAM,KAAK,KACzBpB,YAAYoB,MAAM,GAAG,KACpBpB,YAAYqI,KAAK,CAAC,CAAClI;QACjB,KAAK,MAAMC,OAAOL,eAAgB;YAChC,IAAIK,OAAOD,QAAQ;YACnB,OAAO;QACT;QACA,OAAO;IACT;IAEJ,wEAAwE;IACxE,mEAAmE;IACnE,mCAAmC;IACnC,MAAMkC,gBAAgBwC,SAASwD,KAAK,CAClC,CAACN;YAAYA;eAAAA,EAAAA,kBAAAA,QAAQ1C,MAAM,qBAAd0C,gBAAgB1F,aAAa,MAAK;;IAGjD,MAAMiG,6BACJF,yBAAyBG,QAAQC,GAAG,CAACC,QAAQ,KAAK;IAEpD,MAAMC,eAAerG,gBACjBiG,6BACEzF,oBACEtD,aAAaoJ,SAAS,GACtBpJ,aAAaiD,sBAAsB,GACrC7B,YACFpB,aAAakD,SAAS;IAE1B,MAAMmG,8BAA8B,IAAI1I;IAExC,sDAAsD;IACtD,MAAM2I,gBAAgB,IAAI3I;IAC1B,KAAK,MAAME,OAAOL,eAAgB;QAChC,MAAM,EAAEmD,MAAM,EAAEC,QAAQ,EAAE,GAAGP,MAAMQ,MAAM,CAAChD,IAAI;QAC9C,IAAI0I,UAAU,CAAC,CAAC,EAAE5F,SAAS,QAAQ,KAAK9C,IAAI,CAAC,CAAC;QAC9C,IAAI+C,UAAU;YACZ2F,UAAU,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC;QAC1B;QACAD,cAAchI,GAAG,CAACT,KAAK0I;IACzB;IAEA,gDAAgD;IAChD,MAAMC,eAAe,IAAIC,IAAI/H;IAE7B,IAAImH,yBAAyBvF,mBAAmB;QAC9C,IAAIoG,kBAAkBjJ;QAEtB,IAAI6C,mBAAmB;YACrB,yEAAyE;YACzE,oEAAoE;YACpE,qBAAqB;YACrBoG,kBAAkBjI,6BAChBjB,gBACAC,aACAiB;YAGF,0EAA0E;YAC1E,kCAAkC;YAClC2H,4BAA4B/H,GAAG,CAAC8B,MAAM;gBACpCxC,QAAQ,CAAC;gBACT+I,UAAUvG;gBACVwG,iBAAiBxG;gBACjB8B,qBAAqB1E;gBACrB2I,cAActG,sBACZC,eACApB,eACAyH;gBAEFpG,oBAAoBrB;gBACpByD,yBAAyB;YAC3B;QACF;QAEA5E,mBACEC,gBACA2C,eACEC,MACAC,OACAC,mBACA9C,gBACAkB,eACAgI,kBAEFG,OAAO,CAAC,CAACjJ;YACT,IAAI+I,WAAWvG;YACf,IAAIwG,kBAAkBxG;YAEtB,MAAM8B,sBAAgC,EAAE;YAExC,KAAK,MAAMrE,OAAOL,eAAgB;gBAChC,MAAMsD,aAAalD,MAAM,CAACC,IAAI;gBAE9B,IAAI,CAACiD,YAAY;oBACf,IAAIR,mBAAmB;wBACrB,4CAA4C;wBAC5C4B,oBAAoBvC,IAAI,CAAC9B;wBACzB,IACE,IAAIsB,IAAI3B,eAAewB,OAAO,CAACnB,OAAO,GACtCsB,IAAI3B,eAAeqB,MAAM,EACzBM,IACA;4BACA+C,oBAAoBvC,IAAI,CAACnC,cAAc,CAAC2B,EAAE;wBAC5C;wBACA;oBACF,OAAO;wBACL,iEAAiE;wBACjE,uDAAuD;wBACvD;oBACF;gBACF;gBAEA,2CAA2C;gBAC3C,MAAMoH,UAAUD,cAAc9E,GAAG,CAAC3D;gBAClC8I,WAAWA,SAASG,OAAO,CACzBP,SACAnJ,YAAY0D,YAAY,CAAC/C,QAAUV,qBAAqBU,OAAO;gBAEjE6I,kBAAkBA,gBAAgBE,OAAO,CACvCP,SACAnJ,YAAY0D,YAAYiG;YAE5B;YAEA,MAAMhH,qBAA+B,EAAE;YACvC,KAAK,MAAM0F,SAASvD,oBAAqB;gBACvC,IAAIsE,aAAanI,GAAG,CAACoH,QAAQ;oBAC3B1F,mBAAmBJ,IAAI,CAAC8F;gBAC1B;YACF;YAEAkB,WAAWxJ,kBAAkBwJ;YAE7BN,4BAA4B/H,GAAG,CAACqI,UAAU;gBACxC/I;gBACA+I;gBACAC,iBAAiBzJ,kBAAkByJ;gBACnC1E;gBACAiE,cAActG,sBACZC,eACAC,oBACAoG;gBAEFpG;gBACAoC,yBAAyB;YAC3B;QACF;IACF;IAEA,MAAMnB,oBACJqF,4BAA4BxE,IAAI,GAAG,KACnC0D,4CACI;WAAIc,4BAA4B7H,MAAM;KAAG,GACzCJ;IAEN,yEAAyE;IACzE,IAAI4C,qBAAqBsC,iBAAiB;QACxCvC,mBAAmBC,mBAAmBxD;IACxC;IAEA,OAAO;QAAE2I;QAAcnF;IAAkB;AAC3C", "ignoreList": [0]}