{"version": 3, "sources": ["../../../src/server/app-render/encryption.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nimport 'server-only'\n\n/* eslint-disable import/no-extraneous-dependencies */\nimport { renderToReadableStream } from 'react-server-dom-webpack/server'\n/* eslint-disable import/no-extraneous-dependencies */\nimport { createFromReadableStream } from 'react-server-dom-webpack/client'\n\nimport { streamToString } from '../stream-utils/node-web-streams-helper'\nimport {\n  arrayBufferToString,\n  decrypt,\n  encrypt,\n  getActionEncryptionKey,\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n  stringToUint8Array,\n} from './encryption-utils'\nimport {\n  getCacheSignal,\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { createHangingInputAbortSignal } from './dynamic-rendering'\nimport React from 'react'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst textEncoder = new TextEncoder()\nconst textDecoder = new TextDecoder()\n\nconst filterStackFrame =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .filterStackFrameDEV\n    : undefined\nconst findSourceMapURL =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .findSourceMapURLDEV\n    : undefined\n\n/**\n * Decrypt the serialized string with the action id as the salt.\n */\nasync function decodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (typeof key === 'undefined') {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get the iv (16 bytes) and the payload from the arg.\n  const originalPayload = atob(arg)\n  const ivValue = originalPayload.slice(0, 16)\n  const payload = originalPayload.slice(16)\n\n  const decrypted = textDecoder.decode(\n    await decrypt(key, stringToUint8Array(ivValue), stringToUint8Array(payload))\n  )\n\n  if (!decrypted.startsWith(actionId)) {\n    throw new Error('Invalid Server Action payload: failed to decrypt.')\n  }\n\n  return decrypted.slice(actionId.length)\n}\n\n/**\n * Encrypt the serialized string with the action id as the salt. Add a prefix to\n * later ensure that the payload is correctly decrypted, similar to a checksum.\n */\nasync function encodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (key === undefined) {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get 16 random bytes as iv.\n  const randomBytes = new Uint8Array(16)\n  workUnitAsyncStorage.exit(() => crypto.getRandomValues(randomBytes))\n  const ivValue = arrayBufferToString(randomBytes.buffer)\n\n  const encrypted = await encrypt(\n    key,\n    randomBytes,\n    textEncoder.encode(actionId + arg)\n  )\n\n  return btoa(ivValue + arrayBufferToString(encrypted))\n}\n\nenum ReadStatus {\n  Ready,\n  Pending,\n  Complete,\n}\n\n// Encrypts the action's bound args into a string. For the same combination of\n// actionId and args the same cached promise is returned. This ensures reference\n// equality for returned objects from \"use cache\" functions when they're invoked\n// multiple times within one render pass using the same bound args.\nexport const encryptActionBoundArgs = React.cache(\n  async function encryptActionBoundArgs(actionId: string, ...args: any[]) {\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    const cacheSignal = workUnitStore\n      ? getCacheSignal(workUnitStore)\n      : undefined\n\n    const { clientModules } = getClientReferenceManifestForRsc()\n\n    // Create an error before any asynchronous calls, to capture the original\n    // call stack in case we need it when the serialization errors.\n    const error = new Error()\n    Error.captureStackTrace(error, encryptActionBoundArgs)\n\n    let didCatchError = false\n\n    const hangingInputAbortSignal = workUnitStore\n      ? createHangingInputAbortSignal(workUnitStore)\n      : undefined\n\n    let readStatus = ReadStatus.Ready\n    function startReadOnce() {\n      if (readStatus === ReadStatus.Ready) {\n        readStatus = ReadStatus.Pending\n        cacheSignal?.beginRead()\n      }\n    }\n\n    function endReadIfStarted() {\n      if (readStatus === ReadStatus.Pending) {\n        cacheSignal?.endRead()\n      }\n      readStatus = ReadStatus.Complete\n    }\n\n    // streamToString might take longer than a microtask to resolve and then other things\n    // waiting on the cache signal might not realize there is another cache to fill so if\n    // we are no longer waiting on the bound args serialization via the hangingInputAbortSignal\n    // we should eagerly start the cache read to prevent other readers of the cache signal from\n    // missing this cache fill. We use a idempotent function to only start reading once because\n    // it's also possible that streamToString finishes before the hangingInputAbortSignal aborts.\n    if (hangingInputAbortSignal && cacheSignal) {\n      hangingInputAbortSignal.addEventListener('abort', startReadOnce, {\n        once: true,\n      })\n    }\n\n    // Using Flight to serialize the args into a string.\n    const serialized = await streamToString(\n      renderToReadableStream(args, clientModules, {\n        filterStackFrame,\n        signal: hangingInputAbortSignal,\n        onError(err) {\n          if (hangingInputAbortSignal?.aborted) {\n            return\n          }\n\n          // We're only reporting one error at a time, starting with the first.\n          if (didCatchError) {\n            return\n          }\n\n          didCatchError = true\n\n          // Use the original error message together with the previously created\n          // stack, because err.stack is a useless Flight Server call stack.\n          error.message = err instanceof Error ? err.message : String(err)\n        },\n      }),\n      // We pass the abort signal to `streamToString` so that no chunks are\n      // included that are emitted after the signal was already aborted. This\n      // ensures that we can encode hanging promises.\n      hangingInputAbortSignal\n    )\n\n    if (didCatchError) {\n      if (process.env.NODE_ENV === 'development') {\n        // Logging the error is needed for server functions that are passed to the\n        // client where the decryption is not done during rendering. Console\n        // replaying allows us to still show the error dev overlay in this case.\n        console.error(error)\n      }\n\n      endReadIfStarted()\n      throw error\n    }\n\n    if (!workUnitStore) {\n      // We don't need to call cacheSignal.endRead here because we can't have a cacheSignal\n      // if we do not have a workUnitStore.\n      return encodeActionBoundArg(actionId, serialized)\n    }\n\n    startReadOnce()\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n    const cacheKey = actionId + serialized\n\n    const cachedEncrypted =\n      prerenderResumeDataCache?.encryptedBoundArgs.get(cacheKey) ??\n      renderResumeDataCache?.encryptedBoundArgs.get(cacheKey)\n\n    if (cachedEncrypted) {\n      return cachedEncrypted\n    }\n\n    const encrypted = await encodeActionBoundArg(actionId, serialized)\n\n    endReadIfStarted()\n    prerenderResumeDataCache?.encryptedBoundArgs.set(cacheKey, encrypted)\n\n    return encrypted\n  }\n)\n\n// Decrypts the action's bound args from the encrypted string.\nexport async function decryptActionBoundArgs(\n  actionId: string,\n  encryptedPromise: Promise<string>\n) {\n  const encrypted = await encryptedPromise\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  let decrypted: string | undefined\n\n  if (workUnitStore) {\n    const cacheSignal = getCacheSignal(workUnitStore)\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n\n    decrypted =\n      prerenderResumeDataCache?.decryptedBoundArgs.get(encrypted) ??\n      renderResumeDataCache?.decryptedBoundArgs.get(encrypted)\n\n    if (!decrypted) {\n      cacheSignal?.beginRead()\n      decrypted = await decodeActionBoundArg(actionId, encrypted)\n      cacheSignal?.endRead()\n      prerenderResumeDataCache?.decryptedBoundArgs.set(encrypted, decrypted)\n    }\n  } else {\n    decrypted = await decodeActionBoundArg(actionId, encrypted)\n  }\n\n  const { edgeRscModuleMapping, rscModuleMapping } =\n    getClientReferenceManifestForRsc()\n\n  // Using Flight to deserialize the args from the string.\n  const deserialized = await createFromReadableStream(\n    new ReadableStream({\n      start(controller) {\n        controller.enqueue(textEncoder.encode(decrypted))\n\n        switch (workUnitStore?.type) {\n          case 'prerender':\n          case 'prerender-runtime':\n            // Explicitly don't close the stream here (until prerendering is\n            // complete) so that hanging promises are not rejected.\n            if (workUnitStore.renderSignal.aborted) {\n              controller.close()\n            } else {\n              workUnitStore.renderSignal.addEventListener(\n                'abort',\n                () => controller.close(),\n                { once: true }\n              )\n            }\n            break\n          case 'prerender-client':\n          case 'prerender-ppr':\n          case 'prerender-legacy':\n          case 'request':\n          case 'cache':\n          case 'private-cache':\n          case 'unstable-cache':\n          case undefined:\n            return controller.close()\n          default:\n            workUnitStore satisfies never\n        }\n      },\n    }),\n    {\n      findSourceMapURL,\n      serverConsumerManifest: {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the current execution. Instead, we'll wait for any ClientReference\n        // to be emitted which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime ? edgeRscModuleMapping : rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      },\n    }\n  )\n\n  return deserialized\n}\n"], "names": ["renderToReadableStream", "createFromReadableStream", "streamToString", "arrayBufferToString", "decrypt", "encrypt", "getActionEncryptionKey", "getClientReferenceManifestForRsc", "getServerModuleMap", "stringToUint8Array", "getCacheSignal", "getPrerenderResumeDataCache", "getRenderResumeDataCache", "workUnitAsyncStorage", "createHangingInputAbortSignal", "React", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "textEncoder", "TextEncoder", "textDecoder", "TextDecoder", "filterStackFrame", "NODE_ENV", "require", "filterStackFrameDEV", "undefined", "findSourceMapURL", "findSourceMapURLDEV", "decodeActionBoundArg", "actionId", "arg", "key", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "decrypted", "decode", "startsWith", "length", "encodeActionBoundArg", "randomBytes", "Uint8Array", "exit", "crypto", "getRandomValues", "buffer", "encrypted", "encode", "btoa", "ReadStatus", "encryptActionBoundArgs", "cache", "args", "workUnitStore", "getStore", "cacheSignal", "clientModules", "error", "captureStackTrace", "didCatchError", "hangingInputAbortSignal", "readStatus", "startReadOnce", "beginRead", "endReadIfStarted", "endRead", "addEventListener", "once", "serialized", "signal", "onError", "err", "aborted", "message", "String", "console", "prerenderResumeDataCache", "renderResumeDataCache", "cache<PERSON>ey", "cachedEncrypted", "encryptedBoundArgs", "get", "set", "decryptActionBoundArgs", "encryptedPromise", "decryptedBoundArgs", "edgeRscModuleMapping", "rscModuleMapping", "deserialized", "ReadableStream", "start", "controller", "enqueue", "type", "renderSignal", "close", "serverConsumerManifest", "moduleLoading", "moduleMap", "serverModuleMap"], "mappings": "AAAA,oDAAoD,GACpD,OAAO,cAAa;AAEpB,oDAAoD,GACpD,SAASA,sBAAsB,QAAQ,kCAAiC;AACxE,oDAAoD,GACpD,SAASC,wBAAwB,QAAQ,kCAAiC;AAE1E,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SACEC,mBAAmB,EACnBC,OAAO,EACPC,OAAO,EACPC,sBAAsB,EACtBC,gCAAgC,EAChCC,kBAAkB,EAClBC,kBAAkB,QACb,qBAAoB;AAC3B,SACEC,cAAc,EACdC,2BAA2B,EAC3BC,wBAAwB,EACxBC,oBAAoB,QACf,qCAAoC;AAC3C,SAASC,6BAA6B,QAAQ,sBAAqB;AACnE,OAAOC,WAAW,QAAO;AAEzB,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,cAAc,IAAIC;AACxB,MAAMC,cAAc,IAAIC;AAExB,MAAMC,mBACJP,QAAQC,GAAG,CAACO,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBC;AACN,MAAMC,mBACJZ,QAAQC,GAAG,CAACO,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNI,mBAAmB,GACtBF;AAEN;;CAEC,GACD,eAAeG,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAM5B;IAClB,IAAI,OAAO4B,QAAQ,aAAa;QAC9B,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKJ;IAC7B,MAAMK,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IAEtC,MAAME,YAAYnB,YAAYoB,MAAM,CAClC,MAAMtC,QAAQ8B,KAAKzB,mBAAmB6B,UAAU7B,mBAAmB+B;IAGrE,IAAI,CAACC,UAAUE,UAAU,CAACX,WAAW;QACnC,MAAM,qBAA8D,CAA9D,IAAIG,MAAM,sDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;IACrE;IAEA,OAAOM,UAAUF,KAAK,CAACP,SAASY,MAAM;AACxC;AAEA;;;CAGC,GACD,eAAeC,qBAAqBb,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAM5B;IAClB,IAAI4B,QAAQN,WAAW;QACrB,MAAM,qBAEL,CAFK,IAAIO,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,6BAA6B;IAC7B,MAAMW,cAAc,IAAIC,WAAW;IACnClC,qBAAqBmC,IAAI,CAAC,IAAMC,OAAOC,eAAe,CAACJ;IACvD,MAAMR,UAAUnC,oBAAoB2C,YAAYK,MAAM;IAEtD,MAAMC,YAAY,MAAM/C,QACtB6B,KACAY,aACA1B,YAAYiC,MAAM,CAACrB,WAAWC;IAGhC,OAAOqB,KAAKhB,UAAUnC,oBAAoBiD;AAC5C;AAEA,IAAA,AAAKG,oCAAAA;;;;WAAAA;EAAAA;AAML,8EAA8E;AAC9E,gFAAgF;AAChF,gFAAgF;AAChF,mEAAmE;AACnE,OAAO,MAAMC,yBAAyBzC,MAAM0C,KAAK,CAC/C,eAAeD,uBAAuBxB,QAAgB,EAAE,GAAG0B,IAAW;IACpE,MAAMC,gBAAgB9C,qBAAqB+C,QAAQ;IACnD,MAAMC,cAAcF,gBAChBjD,eAAeiD,iBACf/B;IAEJ,MAAM,EAAEkC,aAAa,EAAE,GAAGvD;IAE1B,yEAAyE;IACzE,+DAA+D;IAC/D,MAAMwD,QAAQ,IAAI5B;IAClBA,MAAM6B,iBAAiB,CAACD,OAAOP;IAE/B,IAAIS,gBAAgB;IAEpB,MAAMC,0BAA0BP,gBAC5B7C,8BAA8B6C,iBAC9B/B;IAEJ,IAAIuC;IACJ,SAASC;QACP,IAAID,kBAAiC;YACnCA;YACAN,+BAAAA,YAAaQ,SAAS;QACxB;IACF;IAEA,SAASC;QACP,IAAIH,kBAAmC;YACrCN,+BAAAA,YAAaU,OAAO;QACtB;QACAJ;IACF;IAEA,qFAAqF;IACrF,qFAAqF;IACrF,2FAA2F;IAC3F,2FAA2F;IAC3F,2FAA2F;IAC3F,6FAA6F;IAC7F,IAAID,2BAA2BL,aAAa;QAC1CK,wBAAwBM,gBAAgB,CAAC,SAASJ,eAAe;YAC/DK,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,MAAMC,aAAa,MAAMxE,eACvBF,uBAAuB0D,MAAMI,eAAe;QAC1CtC;QACAmD,QAAQT;QACRU,SAAQC,GAAG;YACT,IAAIX,2CAAAA,wBAAyBY,OAAO,EAAE;gBACpC;YACF;YAEA,qEAAqE;YACrE,IAAIb,eAAe;gBACjB;YACF;YAEAA,gBAAgB;YAEhB,sEAAsE;YACtE,kEAAkE;YAClEF,MAAMgB,OAAO,GAAGF,eAAe1C,QAAQ0C,IAAIE,OAAO,GAAGC,OAAOH;QAC9D;IACF,IACA,qEAAqE;IACrE,uEAAuE;IACvE,+CAA+C;IAC/CX;IAGF,IAAID,eAAe;QACjB,IAAIhD,QAAQC,GAAG,CAACO,QAAQ,KAAK,eAAe;YAC1C,0EAA0E;YAC1E,oEAAoE;YACpE,wEAAwE;YACxEwD,QAAQlB,KAAK,CAACA;QAChB;QAEAO;QACA,MAAMP;IACR;IAEA,IAAI,CAACJ,eAAe;QAClB,qFAAqF;QACrF,qCAAqC;QACrC,OAAOd,qBAAqBb,UAAU0C;IACxC;IAEAN;IAEA,MAAMc,2BAA2BvE,4BAA4BgD;IAC7D,MAAMwB,wBAAwBvE,yBAAyB+C;IACvD,MAAMyB,WAAWpD,WAAW0C;IAE5B,MAAMW,kBACJH,CAAAA,4CAAAA,yBAA0BI,kBAAkB,CAACC,GAAG,CAACH,eACjDD,yCAAAA,sBAAuBG,kBAAkB,CAACC,GAAG,CAACH;IAEhD,IAAIC,iBAAiB;QACnB,OAAOA;IACT;IAEA,MAAMjC,YAAY,MAAMP,qBAAqBb,UAAU0C;IAEvDJ;IACAY,4CAAAA,yBAA0BI,kBAAkB,CAACE,GAAG,CAACJ,UAAUhC;IAE3D,OAAOA;AACT,GACD;AAED,8DAA8D;AAC9D,OAAO,eAAeqC,uBACpBzD,QAAgB,EAChB0D,gBAAiC;IAEjC,MAAMtC,YAAY,MAAMsC;IACxB,MAAM/B,gBAAgB9C,qBAAqB+C,QAAQ;IAEnD,IAAInB;IAEJ,IAAIkB,eAAe;QACjB,MAAME,cAAcnD,eAAeiD;QACnC,MAAMuB,2BAA2BvE,4BAA4BgD;QAC7D,MAAMwB,wBAAwBvE,yBAAyB+C;QAEvDlB,YACEyC,CAAAA,4CAAAA,yBAA0BS,kBAAkB,CAACJ,GAAG,CAACnC,gBACjD+B,yCAAAA,sBAAuBQ,kBAAkB,CAACJ,GAAG,CAACnC;QAEhD,IAAI,CAACX,WAAW;YACdoB,+BAAAA,YAAaQ,SAAS;YACtB5B,YAAY,MAAMV,qBAAqBC,UAAUoB;YACjDS,+BAAAA,YAAaU,OAAO;YACpBW,4CAAAA,yBAA0BS,kBAAkB,CAACH,GAAG,CAACpC,WAAWX;QAC9D;IACF,OAAO;QACLA,YAAY,MAAMV,qBAAqBC,UAAUoB;IACnD;IAEA,MAAM,EAAEwC,oBAAoB,EAAEC,gBAAgB,EAAE,GAC9CtF;IAEF,wDAAwD;IACxD,MAAMuF,eAAe,MAAM7F,yBACzB,IAAI8F,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAAC9E,YAAYiC,MAAM,CAACZ;YAEtC,OAAQkB,iCAAAA,cAAewC,IAAI;gBACzB,KAAK;gBACL,KAAK;oBACH,gEAAgE;oBAChE,uDAAuD;oBACvD,IAAIxC,cAAcyC,YAAY,CAACtB,OAAO,EAAE;wBACtCmB,WAAWI,KAAK;oBAClB,OAAO;wBACL1C,cAAcyC,YAAY,CAAC5B,gBAAgB,CACzC,SACA,IAAMyB,WAAWI,KAAK,IACtB;4BAAE5B,MAAM;wBAAK;oBAEjB;oBACA;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK7C;oBACH,OAAOqE,WAAWI,KAAK;gBACzB;oBACE1C;YACJ;QACF;IACF,IACA;QACE9B;QACAyE,wBAAwB;YACtB,2FAA2F;YAC3F,oFAAoF;YACpF,6DAA6D;YAC7DC,eAAe;YACfC,WAAWxF,gBAAgB4E,uBAAuBC;YAClDY,iBAAiBjG;QACnB;IACF;IAGF,OAAOsF;AACT", "ignoreList": [0]}