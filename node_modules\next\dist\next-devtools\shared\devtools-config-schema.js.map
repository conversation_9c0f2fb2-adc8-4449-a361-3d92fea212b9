{"version": 3, "sources": ["../../../src/next-devtools/shared/devtools-config-schema.ts"], "sourcesContent": ["import type { DevToolsConfig } from '../dev-overlay/shared'\nimport { z } from 'next/dist/compiled/zod'\n\nexport const devToolsConfigSchema: z.ZodType<DevToolsConfig> = z.object({\n  theme: z.enum(['light', 'dark', 'system']).optional(),\n  disableDevIndicator: z.boolean().optional(),\n  devToolsPosition: z\n    .enum(['top-left', 'top-right', 'bottom-left', 'bottom-right'])\n    .optional(),\n  devToolsPanelPosition: z\n    .record(\n      z.string(),\n      z.enum(['top-left', 'top-right', 'bottom-left', 'bottom-right'])\n    )\n    .optional(),\n  devToolsPanelSize: z\n    .record(z.string(), z.object({ width: z.number(), height: z.number() }))\n    .optional(),\n  scale: z.number().optional(),\n  hideShortcut: z.string().nullable().optional(),\n})\n"], "names": ["devToolsConfigSchema", "z", "object", "theme", "enum", "optional", "disableDevIndicator", "boolean", "devToolsPosition", "devToolsPanelPosition", "record", "string", "devToolsPanelSize", "width", "number", "height", "scale", "hideShortcut", "nullable"], "mappings": ";;;;+BAGaA;;;eAAAA;;;qBAFK;AAEX,MAAMA,uBAAkDC,MAAC,CAACC,MAAM,CAAC;IACtEC,OAAOF,MAAC,CAACG,IAAI,CAAC;QAAC;QAAS;QAAQ;KAAS,EAAEC,QAAQ;IACnDC,qBAAqBL,MAAC,CAACM,OAAO,GAAGF,QAAQ;IACzCG,kBAAkBP,MAAC,CAChBG,IAAI,CAAC;QAAC;QAAY;QAAa;QAAe;KAAe,EAC7DC,QAAQ;IACXI,uBAAuBR,MAAC,CACrBS,MAAM,CACLT,MAAC,CAACU,MAAM,IACRV,MAAC,CAACG,IAAI,CAAC;QAAC;QAAY;QAAa;QAAe;KAAe,GAEhEC,QAAQ;IACXO,mBAAmBX,MAAC,CACjBS,MAAM,CAACT,MAAC,CAACU,MAAM,IAAIV,MAAC,CAACC,MAAM,CAAC;QAAEW,OAAOZ,MAAC,CAACa,MAAM;QAAIC,QAAQd,MAAC,CAACa,MAAM;IAAG,IACpET,QAAQ;IACXW,OAAOf,MAAC,CAACa,MAAM,GAAGT,QAAQ;IAC1BY,cAAchB,MAAC,CAACU,MAAM,GAAGO,QAAQ,GAAGb,QAAQ;AAC9C", "ignoreList": [0]}