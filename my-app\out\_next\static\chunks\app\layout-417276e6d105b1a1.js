(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{3905:()=>{},6702:(e,s,n)=>{Promise.resolve().then(n.t.bind(n,3905,23)),Promise.resolve().then(n.bind(n,9513)),Promise.resolve().then(n.t.bind(n,9840,23))},9513:(e,s,n)=>{"use strict";n.d(s,{Provider:()=>u});var i=n(5155),r=n(3834),t=n(2119),a=n(9309),h=n(3279),c=n(9697);function l(){return(0,h.vt)({schema:{_:"string"},language:"english"})}function o(e){let{locale:s}=(0,c.useI18n)(),{search:n,setSearch:r,query:h}=(0,a.J)({type:"static",initOrama:l,locale:s});return(0,i.jsxs)(t.Rc,{search:n,onSearchChange:r,isLoading:h.isLoading,...e,children:[(0,i.jsx)(t.Xq,{}),(0,i.jsxs)(t.Ct,{children:[(0,i.jsxs)(t.i,{children:[(0,i.jsx)(t.y,{}),(0,i.jsx)(t.vo,{}),(0,i.jsx)(t.hx,{})]}),(0,i.jsx)(t.FI,{items:"empty"!==h.data?h.data:null})]})]})}function u(e){let{children:s}=e;return(0,i.jsx)(r.u4,{search:{SearchDialog:o},children:s})}}},e=>{e.O(0,[36,407,607,874,379,441,964,358],()=>e(e.s=6702)),_N_E=e.O()}]);