{"version": 3, "sources": ["../../src/parser/node-utils.ts"], "sourcesContent": ["import type {AlternativeContainerNode, Node, ParentNode, QuantifiableNode} from './parse.js';\n\ntype KeysOfUnion<T> = T extends T ? keyof T: never;\ntype Props = {[key in KeysOfUnion<Node>]?: any} & {type?: Node['type']};\n\nfunction hasOnlyChild(node: ParentNode & {body: Array<Node>}, props?: Props): boolean {\n  if (!Array.isArray(node.body)) {\n    throw new Error('Expected node with body array');\n  }\n  if (node.body.length !== 1) {\n    return false;\n  }\n  const kid = node.body[0] as Props;\n  return !props || Object.keys(props).every(key => props[key as keyof Props] === kid[key as keyof Props]);\n}\n\nfunction isAlternativeContainer(node: Node): node is AlternativeContainerNode {\n  if (\n    !alternativeContainerTypes.has(node.type) ||\n    (node.type === 'AbsenceFunction' && node.kind !== 'repeater')\n  ) {\n    return false;\n  }\n  return true;\n}\nconst alternativeContainerTypes = new Set<Node['type']>([\n  'AbsenceFunction',\n  'CapturingGroup',\n  'Group',\n  'LookaroundAssertion',\n  'Regex',\n]);\n\nfunction isQuantifiable(node: Node): node is QuantifiableNode {\n  return quantifiableTypes.has(node.type);\n}\nconst quantifiableTypes = new Set<Node['type']>([\n  'AbsenceFunction',\n  'Backreference',\n  'CapturingGroup',\n  'Character',\n  'CharacterClass',\n  'CharacterSet',\n  'Group',\n  'Quantifier',\n  'Subroutine',\n]);\n\nexport {\n  hasOnlyChild,\n  isAlternativeContainer,\n  isQuantifiable,\n};\n"], "mappings": "aAKA,SAASA,EAAaC,EAAwCC,EAAwB,CACpF,GAAI,CAAC,MAAM,QAAQD,EAAK,IAAI,EAC1B,MAAM,IAAI,MAAM,+BAA+B,EAEjD,GAAIA,EAAK,KAAK,SAAW,EACvB,MAAO,GAET,MAAME,EAAMF,EAAK,KAAK,CAAC,EACvB,MAAO,CAACC,GAAS,OAAO,KAAKA,CAAK,EAAE,MAAME,GAAOF,EAAME,CAAkB,IAAMD,EAAIC,CAAkB,CAAC,CACxG,CAEA,SAASC,EAAuBJ,EAA8C,CAC5E,MACE,GAACK,EAA0B,IAAIL,EAAK,IAAI,GACvCA,EAAK,OAAS,mBAAqBA,EAAK,OAAS,WAKtD,CACA,MAAMK,EAA4B,IAAI,IAAkB,CACtD,kBACA,iBACA,QACA,sBACA,OACF,CAAC,EAED,SAASC,EAAeN,EAAsC,CAC5D,OAAOO,EAAkB,IAAIP,EAAK,IAAI,CACxC,CACA,MAAMO,EAAoB,IAAI,IAAkB,CAC9C,kBACA,gBACA,iBACA,YACA,iBACA,eACA,QACA,aACA,YACF,CAAC,EAED,OACER,KAAA,aACAK,KAAA,uBACAE,KAAA", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "props", "kid", "key", "isAlternativeContainer", "alternativeContainerTypes", "isQuantifiable", "quantifiableTypes"]}