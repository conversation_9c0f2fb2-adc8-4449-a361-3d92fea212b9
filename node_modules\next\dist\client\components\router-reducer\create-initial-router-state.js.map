{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightDataPath } from '../../../server/app-render/types'\n\nimport { createHrefFromUrl } from './create-href-from-url'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { extractPathFromFlightRouterState } from './compute-changed-path'\nimport {\n  createSeededPrefetchCacheEntry,\n  STATIC_STALETIME_MS,\n} from './prefetch-cache-utils'\nimport { PrefetchKind, type PrefetchCacheEntry } from './router-reducer-types'\nimport { addRefreshMarkerToActiveParallelSegments } from './refetch-inactive-parallel-segments'\nimport { getFlightDataPartsFromPath } from '../../flight-data-helpers'\n\nexport interface InitialRouterStateParameters {\n  navigatedAt: number\n  initialCanonicalUrlParts: string[]\n  initialParallelRoutes: CacheNode['parallelRoutes']\n  initialFlightData: FlightDataPath[]\n  location: Location | null\n  couldBeIntercepted: boolean\n  postponed: boolean\n  prerendered: boolean\n}\n\nexport function createInitialRouterState({\n  navigatedAt,\n  initialFlightData,\n  initialCanonicalUrlParts,\n  initialParallelRoutes,\n  location,\n  couldBeIntercepted,\n  postponed,\n  prerendered,\n}: InitialRouterStateParameters) {\n  // When initialized on the server, the canonical URL is provided as an array of parts.\n  // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n  // as a URL that should be crawled.\n  const initialCanonicalUrl = initialCanonicalUrlParts.join('/')\n\n  const normalizedFlightData = getFlightDataPartsFromPath(initialFlightData[0])\n  const {\n    tree: initialTree,\n    seedData: initialSeedData,\n    head: initialHead,\n  } = normalizedFlightData\n  // For the SSR render, seed data should always be available (we only send back a `null` response\n  // in the case of a `loading` segment, pre-PPR.)\n  const rsc = initialSeedData?.[1]\n  const loading = initialSeedData?.[3] ?? null\n\n  const cache: CacheNode = {\n    lazyData: null,\n    rsc,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n    parallelRoutes: initialParallelRoutes,\n    loading,\n    navigatedAt,\n  }\n\n  const canonicalUrl =\n    // location.href is read as the initial value for canonicalUrl in the browser\n    // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location\n      ? // window.location does not have the same type as URL but has all the fields createHrefFromUrl needs.\n        createHrefFromUrl(location)\n      : initialCanonicalUrl\n\n  addRefreshMarkerToActiveParallelSegments(initialTree, canonicalUrl)\n\n  const prefetchCache = new Map<string, PrefetchCacheEntry>()\n\n  // When the cache hasn't been seeded yet we fill the cache with the head.\n  if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n    fillLazyItemsTillLeafWithHead(\n      navigatedAt,\n      cache,\n      undefined,\n      initialTree,\n      initialSeedData,\n      initialHead,\n      undefined\n    )\n  }\n\n  const initialState = {\n    tree: initialTree,\n    cache,\n    prefetchCache,\n    pushRef: {\n      pendingPush: false,\n      mpaNavigation: false,\n      // First render needs to preserve the previous window.history.state\n      // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n      preserveCustomHistoryState: true,\n    },\n    focusAndScrollRef: {\n      apply: false,\n      onlyHashChange: false,\n      hashFragment: null,\n      segmentPaths: [],\n    },\n    canonicalUrl,\n    nextUrl:\n      // the || operator is intentional, the pathname can be an empty string\n      (extractPathFromFlightRouterState(initialTree) || location?.pathname) ??\n      null,\n  }\n\n  if (process.env.NODE_ENV !== 'development' && location) {\n    // Seed the prefetch cache with this page's data.\n    // This is to prevent needlessly re-prefetching a page that is already reusable,\n    // and will avoid triggering a loading state/data fetch stall when navigating back to the page.\n    // We don't currently do this in development because links aren't prefetched in development\n    // so having a mismatch between prefetch/no prefetch provides inconsistent behavior based on which page\n    // was loaded first.\n    const url = new URL(\n      `${location.pathname}${location.search}`,\n      location.origin\n    )\n\n    createSeededPrefetchCacheEntry({\n      url,\n      data: {\n        flightData: [normalizedFlightData],\n        canonicalUrl: undefined,\n        couldBeIntercepted: !!couldBeIntercepted,\n        prerendered,\n        postponed,\n        // TODO: The initial RSC payload includes both static and dynamic data\n        // in the same response, even if PPR is enabled. So if there's any\n        // dynamic data at all, we can't set a stale time. In the future we may\n        // add a way to split a single Flight stream into static and dynamic\n        // parts. But in the meantime we should at least make this work for\n        // fully static pages.\n        staleTime:\n          // In the old router, there was only a single configurable staleTime (experimental.staleTimes)\n          // As an abundance of caution, this will only set the initial staleTime to the configured value\n          // if we're not leveraging the segment cache, which has its own prefetching semantics.\n          prerendered && !process.env.__NEXT_CLIENT_SEGMENT_CACHE\n            ? STATIC_STALETIME_MS\n            : -1,\n      },\n      tree: initialState.tree,\n      prefetchCache: initialState.prefetchCache,\n      nextUrl: initialState.nextUrl,\n      kind: prerendered ? PrefetchKind.FULL : PrefetchKind.AUTO,\n    })\n  }\n\n  return initialState\n}\n"], "names": ["createInitialRouterState", "navigatedAt", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "location", "couldBeIntercepted", "postponed", "prerendered", "initialCanonicalUrl", "join", "normalizedFlightData", "getFlightDataPartsFromPath", "tree", "initialTree", "seedData", "initialSeedData", "head", "initialHead", "rsc", "loading", "cache", "lazyData", "prefetchRsc", "prefetchHead", "parallelRoutes", "canonicalUrl", "createHrefFromUrl", "addRefreshMarkerToActiveParallelSegments", "prefetchCache", "Map", "size", "fillLazyItemsTillLeafWithHead", "undefined", "extractPathFromFlightRouterState", "initialState", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "nextUrl", "pathname", "process", "env", "NODE_ENV", "url", "URL", "search", "origin", "createSeededPrefetchCacheEntry", "data", "flightData", "staleTime", "__NEXT_CLIENT_SEGMENT_CACHE", "STATIC_STALETIME_MS", "kind", "PrefetchKind", "FULL", "AUTO"], "mappings": ";;;;+BAyBgBA;;;eAAAA;;;mCAtBkB;+CACY;oCACG;oCAI1C;oCAC+C;iDACG;mCACd;AAapC,SAASA,yBAAyB,KASV;IATU,IAAA,EACvCC,WAAW,EACXC,iBAAiB,EACjBC,wBAAwB,EACxBC,qBAAqB,EACrBC,QAAQ,EACRC,kBAAkB,EAClBC,SAAS,EACTC,WAAW,EACkB,GATU;IAUvC,sFAAsF;IACtF,kGAAkG;IAClG,mCAAmC;IACnC,MAAMC,sBAAsBN,yBAAyBO,IAAI,CAAC;IAE1D,MAAMC,uBAAuBC,IAAAA,6CAA0B,EAACV,iBAAiB,CAAC,EAAE;IAC5E,MAAM,EACJW,MAAMC,WAAW,EACjBC,UAAUC,eAAe,EACzBC,MAAMC,WAAW,EAClB,GAAGP;IACJ,gGAAgG;IAChG,gDAAgD;IAChD,MAAMQ,MAAMH,mCAAAA,eAAiB,CAAC,EAAE;QAChBA;IAAhB,MAAMI,UAAUJ,CAAAA,oBAAAA,mCAAAA,eAAiB,CAAC,EAAE,YAApBA,oBAAwB;IAExC,MAAMK,QAAmB;QACvBC,UAAU;QACVH;QACAI,aAAa;QACbN,MAAM;QACNO,cAAc;QACd,oJAAoJ;QACpJC,gBAAgBrB;QAChBgB;QACAnB;IACF;IAEA,MAAMyB,eACJ,6EAA6E;IAC7E,kJAAkJ;IAClJrB,WAEIsB,IAAAA,oCAAiB,EAACtB,YAClBI;IAENmB,IAAAA,yEAAwC,EAACd,aAAaY;IAEtD,MAAMG,gBAAgB,IAAIC;IAE1B,yEAAyE;IACzE,IAAI1B,0BAA0B,QAAQA,sBAAsB2B,IAAI,KAAK,GAAG;QACtEC,IAAAA,4DAA6B,EAC3B/B,aACAoB,OACAY,WACAnB,aACAE,iBACAE,aACAe;IAEJ;QAqBI,sEAAsE;IACrEC;IApBL,MAAMC,eAAe;QACnBtB,MAAMC;QACNO;QACAQ;QACAO,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,mEAAmE;YACnE,gFAAgF;YAChFC,4BAA4B;QAC9B;QACAC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAlB;QACAmB,SAEE,CAACX,OAAAA,IAAAA,oDAAgC,EAACpB,iBAAgBT,4BAAAA,SAAUyC,QAAQ,aAAnEZ,OACD;IACJ;IAEA,IAAIa,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB5C,UAAU;QACtD,iDAAiD;QACjD,gFAAgF;QAChF,+FAA+F;QAC/F,2FAA2F;QAC3F,uGAAuG;QACvG,oBAAoB;QACpB,MAAM6C,MAAM,IAAIC,IACd,AAAC,KAAE9C,SAASyC,QAAQ,GAAGzC,SAAS+C,MAAM,EACtC/C,SAASgD,MAAM;QAGjBC,IAAAA,kDAA8B,EAAC;YAC7BJ;YACAK,MAAM;gBACJC,YAAY;oBAAC7C;iBAAqB;gBAClCe,cAAcO;gBACd3B,oBAAoB,CAAC,CAACA;gBACtBE;gBACAD;gBACA,sEAAsE;gBACtE,kEAAkE;gBAClE,uEAAuE;gBACvE,oEAAoE;gBACpE,mEAAmE;gBACnE,sBAAsB;gBACtBkD,WACE,8FAA8F;gBAC9F,+FAA+F;gBAC/F,sFAAsF;gBACtFjD,eAAe,CAACuC,QAAQC,GAAG,CAACU,2BAA2B,GACnDC,uCAAmB,GACnB,CAAC;YACT;YACA9C,MAAMsB,aAAatB,IAAI;YACvBgB,eAAeM,aAAaN,aAAa;YACzCgB,SAASV,aAAaU,OAAO;YAC7Be,MAAMpD,cAAcqD,gCAAY,CAACC,IAAI,GAAGD,gCAAY,CAACE,IAAI;QAC3D;IACF;IAEA,OAAO5B;AACT", "ignoreList": [0]}