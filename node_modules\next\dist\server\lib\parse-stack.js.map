{"version": 3, "sources": ["../../../src/server/lib/parse-stack.ts"], "sourcesContent": ["import { parse } from 'next/dist/compiled/stacktrace-parser'\n\nconst regexNextStatic = /\\/_next(\\/static\\/.+)/\n\nexport interface StackFrame {\n  file: string | null\n  methodName: string\n  arguments: string[]\n  /** 1-based */\n  line1: number | null\n  /** 1-based */\n  column1: number | null\n}\n\nexport function parseStack(\n  stack: string,\n  distDir = process.env.__NEXT_DIST_DIR\n): StackFrame[] {\n  if (!stack) return []\n\n  // throw away eval information that stacktrace-parser doesn't support\n  // adapted from https://github.com/stacktracejs/error-stack-parser/blob/9f33c224b5d7b607755eb277f9d51fcdb7287e24/error-stack-parser.js#L59C33-L59C62\n  stack = stack\n    .split('\\n')\n    .map((line) => {\n      if (line.includes('(eval ')) {\n        line = line\n          .replace(/eval code/g, 'eval')\n          .replace(/\\(eval at [^()]* \\(/, '(file://')\n          .replace(/\\),.*$/g, ')')\n      }\n\n      return line\n    })\n    .join('\\n')\n\n  const frames = parse(stack)\n  return frames.map((frame) => {\n    try {\n      const url = new URL(frame.file!)\n      const res = regexNextStatic.exec(url.pathname)\n      if (res) {\n        const effectiveDistDir = distDir\n          ?.replace(/\\\\/g, '/')\n          ?.replace(/\\/$/, '')\n        if (effectiveDistDir) {\n          frame.file =\n            'file://' + effectiveDistDir.concat(res.pop()!) + url.search\n        }\n      }\n    } catch {}\n    return {\n      file: frame.file,\n      line1: frame.lineNumber,\n      column1: frame.column,\n      methodName: frame.methodName,\n      arguments: frame.arguments,\n    }\n  })\n}\n"], "names": ["parseStack", "regexNextStatic", "stack", "distDir", "process", "env", "__NEXT_DIST_DIR", "split", "map", "line", "includes", "replace", "join", "frames", "parse", "frame", "url", "URL", "file", "res", "exec", "pathname", "effectiveDistDir", "concat", "pop", "search", "line1", "lineNumber", "column1", "column", "methodName", "arguments"], "mappings": ";;;;+BAcgBA;;;eAAAA;;;kCAdM;AAEtB,MAAMC,kBAAkB;AAYjB,SAASD,WACdE,KAAa,EACbC,UAAUC,QAAQC,GAAG,CAACC,eAAe;IAErC,IAAI,CAACJ,OAAO,OAAO,EAAE;IAErB,qEAAqE;IACrE,oJAAoJ;IACpJA,QAAQA,MACLK,KAAK,CAAC,MACNC,GAAG,CAAC,CAACC;QACJ,IAAIA,KAAKC,QAAQ,CAAC,WAAW;YAC3BD,OAAOA,KACJE,OAAO,CAAC,cAAc,QACtBA,OAAO,CAAC,uBAAuB,YAC/BA,OAAO,CAAC,WAAW;QACxB;QAEA,OAAOF;IACT,GACCG,IAAI,CAAC;IAER,MAAMC,SAASC,IAAAA,uBAAK,EAACZ;IACrB,OAAOW,OAAOL,GAAG,CAAC,CAACO;QACjB,IAAI;YACF,MAAMC,MAAM,IAAIC,IAAIF,MAAMG,IAAI;YAC9B,MAAMC,MAAMlB,gBAAgBmB,IAAI,CAACJ,IAAIK,QAAQ;YAC7C,IAAIF,KAAK;oBACkBhB;gBAAzB,MAAMmB,mBAAmBnB,4BAAAA,mBAAAA,QACrBQ,OAAO,CAAC,OAAO,yBADMR,iBAErBQ,OAAO,CAAC,OAAO;gBACnB,IAAIW,kBAAkB;oBACpBP,MAAMG,IAAI,GACR,YAAYI,iBAAiBC,MAAM,CAACJ,IAAIK,GAAG,MAAOR,IAAIS,MAAM;gBAChE;YACF;QACF,EAAE,OAAM,CAAC;QACT,OAAO;YACLP,MAAMH,MAAMG,IAAI;YAChBQ,OAAOX,MAAMY,UAAU;YACvBC,SAASb,MAAMc,MAAM;YACrBC,YAAYf,MAAMe,UAAU;YAC5BC,WAAWhB,MAAMgB,SAAS;QAC5B;IACF;AACF", "ignoreList": [0]}