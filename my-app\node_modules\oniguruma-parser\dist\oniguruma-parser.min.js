"use strict";var OnigurumaParser=(()=>{var ne=Object.defineProperty;var nt=Object.getOwnPropertyDescriptor;var at=Object.getOwnPropertyNames;var ot=Object.prototype.hasOwnProperty;var it=(e,t)=>{for(var r in t)ne(e,r,{get:t[r],enumerable:!0})},st=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of at(t))!ot.call(e,i)&&i!==r&&ne(e,i,{get:()=>t[i],enumerable:!(a=nt(t,i))||a.enumerable});return e};var lt=e=>st(ne({},"__esModule",{value:!0}),e);var mr={};it(mr,{OnigUnicodePropertyMap:()=>G,generate:()=>H,getOptionalOptimizations:()=>le,optimize:()=>rt,parse:()=>M,toOnigurumaAst:()=>Ee,traverse:()=>Y});function y(e){if([...e].length!==1)throw new Error(`Expected "${e}" to be a single code point`);return e.codePointAt(0)}function pe(e,t,r){return e.has(t)||e.set(t,r),e.get(t)}var U=new Set(["alnum","alpha","ascii","blank","cntrl","digit","graph","lower","print","punct","space","upper","word","xdigit"]),c=String.raw;function m(e,t){if(e==null)throw new Error(t??"Value expected");return e}function H(e){let t=[],r=e,a={inCharClass:!1,lastNode:r,parent:e},i=o=>{a.lastNode=r,r=o,dt(a.lastNode)===o&&(a.parent=a.lastNode,t.push(a.parent));let s=m(ut[o.type],`Unexpected node type "${o.type}"`)(o,a,i);return ft(a.parent)===o&&(t.pop(),a.parent=t.at(-1)??e),s};return{pattern:e.body.map(i).join("|"),flags:i(e.flags)}}var ut={AbsenceFunction({body:e,kind:t},r,a){if(t!=="repeater")throw new Error(`Unexpected absence function kind "${t}"`);return`(?~${e.map(a).join("|")})`},Alternative({body:e},t,r){return e.map(r).join("")},Assertion({kind:e,negate:t}){return e==="text_segment_boundary"?t?c`\Y`:c`\y`:e==="word_boundary"?t?c`\B`:c`\b`:m({line_end:"$",line_start:"^",search_start:c`\G`,string_end:c`\z`,string_end_newline:c`\Z`,string_start:c`\A`}[e],`Unexpected assertion kind "${e}"`)},Backreference({ref:e}){return typeof e=="number"?"\\"+e:`\\k<${e}>`},CapturingGroup({body:e,name:t},r,a){return`(${t?`?${t.includes(">")?`'${t}'`:`<${t}>`}`:""}${e.map(a).join("|")})`},Character(e,{inCharClass:t,lastNode:r,parent:a}){let{value:i}=e;if(ce.has(i))return ce.get(i);let o=r.type==="Backreference";if(i<32||i>126&&i<160||i>262143||o&&_t(i))return i>127?`\\x{${i.toString(16).toUpperCase()}}`:`\\x${i.toString(16).toUpperCase().padStart(2,"0")}`;let n=String.fromCodePoint(i),s=!1;if(t){let l=a.type==="CharacterClass",p=l&&a.body[0]===e,d=l&&a.body.at(-1)===e;n==="^"?s=p&&!a.negate:n==="]"?s=!p:n==="-"?s=!p&&!d:s=ct.has(n)}else s=pt.has(n);return`${s?"\\":""}${n}`},CharacterClass({body:e,kind:t,negate:r},a,i){function o(){return a.parent.type==="CharacterClass"&&a.parent.kind==="intersection"&&t==="union"&&!e.length?"":`[${r?"^":""}${e.map(i).join(t==="intersection"?"&&":"")}]`}if(!a.inCharClass){a.inCharClass=!0;let n=o();return a.inCharClass=!1,n}return o()},CharacterClassRange({min:e,max:t},r,a){return`${a(e)}-${a(t)}`},CharacterSet({kind:e,negate:t,value:r},{inCharClass:a}){switch(e){case"any":return c`\O`;case"digit":return t?c`\D`:c`\d`;case"dot":return".";case"hex":return t?c`\H`:c`\h`;case"newline":return t?c`\N`:c`\R`;case"posix":return a?`[:${t?"^":""}${r}:]`:`${t?c`\P`:c`\p`}{${r}}`;case"property":return`${t?c`\P`:c`\p`}{${r}}`;case"space":return t?c`\S`:c`\s`;case"text_segment":return c`\X`;case"word":return t?c`\W`:c`\w`;default:throw new Error(`Unexpected character set kind "${e}"`)}},Directive({kind:e,flags:t}){if(e==="flags"){let{enable:r={},disable:a={}}=t,i=$(r),o=$(a);return i||o?`(?${i}${o?`-${o}`:""})`:""}if(e==="keep")return c`\K`;throw new Error(`Unexpected directive kind "${e}"`)},Flags(e){return $(e)},Group({atomic:e,body:t,flags:r},a,i){let o=t.map(i).join("|");return`(?${ht(e,r)}${o})`},LookaroundAssertion({body:e,kind:t,negate:r},a,i){return`(?${`${t==="lookahead"?"":"<"}${r?"!":"="}`}${e.map(i).join("|")})`},NamedCallout({kind:e,tag:t,arguments:r}){if(e==="custom")throw new Error(`Unexpected named callout kind "${e}"`);return`(*${e.toUpperCase()}${t?`[${t}]`:""}${r?`{${r.join(",")}}`:""})`},Quantifier(e,{parent:t},r){let{body:a,kind:i,max:o,min:n}=e;if(n===1/0)throw new Error("Invalid quantifier: infinite min");if(n>o)throw new Error(`Invalid quantifier: min "${n}" > max "${o}"`);let s=a.type==="Quantifier"&&a.kind==="greedy",l=t.type==="Quantifier"&&t.kind==="possessive"&&t.min===1&&t.max===1/0,p=i==="greedy"&&l,d;de(e)&&!p&&(!n&&o===1&&!s?d="?":!n&&o===1/0?d="*":n===1&&o===1/0&&(!(s&&de(a))||i==="possessive")&&(d="+"));let _=!d;if(_)if(i==="possessive"){if(n===o)throw new Error(`Invalid possessive quantifier: min and max are equal "${n}"`);if(o===1/0)throw new Error(`Invalid possessive quantifier: min "${n}" with infinite max"`);d=`{${o},${n}}`}else n===o?d=`{${n}}`:d=`{${n},${o===1/0?"":o}}`;let u={greedy:"",lazy:"?",possessive:_?"":"+"}[i];return`${r(a)}${d}${u}`},Subroutine({ref:e}){return typeof e=="string"&&e.includes(">")?c`\g'${e}'`:c`\g<${e}>`}},pt=new Set(["$","(",")","*","+",".","?","[","\\","^","{","|"]),ct=new Set(["&","-","[","\\","]","^"]),ce=new Map([[7,c`\a`],[9,c`\t`],[10,c`\n`],[11,c`\v`],[12,c`\f`],[13,c`\r`],[27,c`\e`],[8232,c`\u2028`],[8233,c`\u2029`],[65279,c`\uFEFF`]]);function dt(e){return"body"in e?Array.isArray(e.body)?e.body[0]??null:e.body:"min"in e&&e.min.type?e.min:null}function ft(e){return"body"in e?Array.isArray(e.body)?e.body.at(-1)??null:e.body:"max"in e&&e.max.type?e.max:null}function $({ignoreCase:e,dotAll:t,extended:r,digitIsAscii:a,posixIsAscii:i,spaceIsAscii:o,wordIsAscii:n,textSegmentMode:s}){return`${e?"i":""}${t?"m":""}${r?"x":""}${a?"D":""}${i?"P":""}${o?"S":""}${n?"W":""}${s?m({grapheme:"y{g}",word:"y{w}"}[s],`Unexpected text segment mode "${s}"`):""}`}function ht(e,t){if(e)return">";let r="";if(t){let{enable:a={},disable:i={}}=t,o=$(a),n=$(i);r=`${o}${n?`-${n}`:""}`}return`${r}:`}function _t(e){return e>47&&e<58}function de({min:e,max:t}){return!e&&t===1||!e&&t===1/0||e===1&&t===1/0}var ge=c`\[\^?`,Ce=`c.? | C(?:-.?)?|${c`[pP]\{(?:\^?[-\x20_]*[A-Za-z][-\x20\w]*\})?`}|${c`x[89A-Fa-f]\p{AHex}(?:\\x[89A-Fa-f]\p{AHex})*`}|${c`u(?:\p{AHex}{4})? | x\{[^\}]*\}? | x\p{AHex}{0,2}`}|${c`o\{[^\}]*\}?`}|${c`\d{1,3}`}`,oe=/[?*+][?+]?|\{(?:\d+(?:,\d*)?|,\d+)\}\??/,Q=new RegExp(c`
  \\ (?:
    ${Ce}
    | [gk]<[^>]*>?
    | [gk]'[^']*'?
    | .
  )
  | \( (?:
    \? (?:
      [:=!>({]
      | <[=!]
      | <[^>]*>
      | '[^']*'
      | ~\|?
      | #(?:[^)\\]|\\.?)*
      | [^:)]*[:)]
    )?
    | \*[^\)]*\)?
  )?
  | (?:${oe.source})+
  | ${ge}
  | .
`.replace(/\s+/g,""),"gsu"),ae=new RegExp(c`
  \\ (?:
    ${Ce}
    | .
  )
  | \[:(?:\^?\p{Alpha}+|\^):\]
  | ${ge}
  | &&
  | .
`.replace(/\s+/g,""),"gsu");function be(e,t={}){let r={flags:"",...t,rules:{captureGroup:!1,singleline:!1,...t.rules}};if(typeof e!="string")throw new Error("String expected as pattern");let a=Gt(r.flags),i=[a.extended],o={captureGroup:r.rules.captureGroup,getCurrentModX(){return i.at(-1)},numOpenGroups:0,popModX(){i.pop()},pushModX(u){i.push(u)},replaceCurrentModX(u){i[i.length-1]=u},singleline:r.rules.singleline},n=[],s;for(Q.lastIndex=0;s=Q.exec(e);){let u=mt(o,e,s[0],Q.lastIndex);u.tokens?n.push(...u.tokens):u.token&&n.push(u.token),u.lastIndex!==void 0&&(Q.lastIndex=u.lastIndex)}let l=[],p=0;n.filter(u=>u.type==="GroupOpen").forEach(u=>{u.kind==="capturing"?u.number=++p:u.raw==="("&&l.push(u)}),p||l.forEach((u,h)=>{u.kind="capturing",u.number=h+1});let d=p||l.length;return{tokens:n.map(u=>u.type==="EscapedNumber"?Ft(u,d):u).flat(),flags:a}}function mt(e,t,r,a){let[i,o]=r;if(r==="["||r==="[^"){let n=yt(t,r,a);return{tokens:n.tokens,lastIndex:n.lastIndex}}if(i==="\\"){if("AbBGyYzZ".includes(o))return{token:fe(r,r)};if(/^\\g[<']/.test(r)){if(!/^\\g(?:<[^>]+>|'[^']+')$/.test(r))throw new Error(`Invalid group name "${r}"`);return{token:vt(r)}}if(/^\\k[<']/.test(r)){if(!/^\\k(?:<[^>]+>|'[^']+')$/.test(r))throw new Error(`Invalid group name "${r}"`);return{token:ke(r)}}if(o==="K")return{token:Ie("keep",r)};if(o==="N"||o==="R")return{token:T("newline",r,{negate:o==="N"})};if(o==="O")return{token:T("any",r)};if(o==="X")return{token:T("text_segment",r)};let n=Ne(r,{inCharClass:!1});return Array.isArray(n)?{tokens:n}:{token:n}}if(i==="("){if(o==="*")return{token:Pt(r)};if(r==="(?{")throw new Error(`Unsupported callout "${r}"`);if(r.startsWith("(?#")){if(t[a]!==")")throw new Error('Unclosed comment group "(?#"');return{lastIndex:a+1}}if(/^\(\?[-imx]+[:)]$/.test(r))return{token:Et(r,e)};if(e.pushModX(e.getCurrentModX()),e.numOpenGroups++,r==="("&&!e.captureGroup||r==="(?:")return{token:P("group",r)};if(r==="(?>")return{token:P("atomic",r)};if(r==="(?="||r==="(?!"||r==="(?<="||r==="(?<!")return{token:P(r[2]==="<"?"lookbehind":"lookahead",r,{negate:r.endsWith("!")})};if(r==="("&&e.captureGroup||r.startsWith("(?<")&&r.endsWith(">")||r.startsWith("(?'")&&r.endsWith("'"))return{token:P("capturing",r,{...r!=="("&&{name:r.slice(3,-1)}})};if(r.startsWith("(?~")){if(r==="(?~|")throw new Error(`Unsupported absence function kind "${r}"`);return{token:P("absence_repeater",r)}}throw r==="(?("?new Error(`Unsupported conditional "${r}"`):new Error(`Invalid or unsupported group option "${r}"`)}if(r===")"){if(e.popModX(),e.numOpenGroups--,e.numOpenGroups<0)throw new Error('Unmatched ")"');return{token:At(r)}}if(e.getCurrentModX()){if(r==="#"){let n=t.indexOf(`
`,a);return{lastIndex:n===-1?t.length:n}}if(/^\s$/.test(r)){let n=/\s+/y;return n.lastIndex=a,{lastIndex:n.exec(t)?n.lastIndex:a}}}if(r===".")return{token:T("dot",r)};if(r==="^"||r==="$"){let n=e.singleline?{"^":c`\A`,$:c`\Z`}[r]:r;return{token:fe(n,r)}}return r==="|"?{token:Ct(r)}:oe.test(r)?{tokens:Ut(r)}:{token:A(y(r),r)}}function yt(e,t,r){let a=[he(t[1]==="^",t)],i=1,o;for(ae.lastIndex=r;o=ae.exec(e);){let n=o[0];if(n[0]==="["&&n[1]!==":")i++,a.push(he(n[1]==="^",n));else if(n==="]"){if(a.at(-1).type==="CharacterClassOpen")a.push(A(93,n));else if(i--,a.push(bt(n)),!i)break}else{let s=gt(n);Array.isArray(s)?a.push(...s):a.push(s)}}return{tokens:a,lastIndex:ae.lastIndex||e.length}}function gt(e){if(e[0]==="\\")return Ne(e,{inCharClass:!0});if(e[0]==="["){let t=/\[:(?<negate>\^?)(?<name>[a-z]+):\]/.exec(e);if(!t||!U.has(t.groups.name))throw new Error(`Invalid POSIX class "${e}"`);return T("posix",e,{value:t.groups.name,negate:!!t.groups.negate})}return e==="-"?Nt(e):e==="&&"?kt(e):A(y(e),e)}function Ne(e,{inCharClass:t}){let r=e[1];if(r==="c"||r==="C")return Tt(e);if("dDhHsSwW".includes(r))return Ot(e);if(e.startsWith(c`\o{`))throw new Error(`Incomplete, invalid, or unsupported octal code point "${e}"`);if(/^\\[pP]\{/.test(e)){if(e.length===3)throw new Error(`Incomplete or invalid Unicode property "${e}"`);return Mt(e)}if(/^\\x[89A-Fa-f]\p{AHex}/u.test(e))try{let a=e.split(/\\x/).slice(1).map(s=>parseInt(s,16)),i=new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}).decode(new Uint8Array(a)),o=new TextEncoder;return[...i].map(s=>{let l=[...o.encode(s)].map(p=>`\\x${p.toString(16)}`).join("");return A(y(s),l)})}catch{throw new Error(`Multibyte code "${e}" incomplete or invalid in Oniguruma`)}if(r==="u"||r==="x")return A(Lt(e),e);if(_e.has(r))return A(_e.get(r),e);if(/\d/.test(r))return It(t,e);if(e==="\\")throw new Error(c`Incomplete escape "\"`);if(r==="M")throw new Error(`Unsupported meta "${e}"`);if([...e].length===2)return A(e.codePointAt(1),e);throw new Error(`Unexpected escape "${e}"`)}function Ct(e){return{type:"Alternator",raw:e}}function fe(e,t){return{type:"Assertion",kind:e,raw:t}}function ke(e){return{type:"Backreference",raw:e}}function A(e,t){return{type:"Character",value:e,raw:t}}function bt(e){return{type:"CharacterClassClose",raw:e}}function Nt(e){return{type:"CharacterClassHyphen",raw:e}}function kt(e){return{type:"CharacterClassIntersector",raw:e}}function he(e,t){return{type:"CharacterClassOpen",negate:e,raw:t}}function T(e,t,r={}){return{type:"CharacterSet",kind:e,...r,raw:t}}function Ie(e,t,r={}){return e==="keep"?{type:"Directive",kind:e,raw:t}:{type:"Directive",kind:e,flags:m(r.flags),raw:t}}function It(e,t){return{type:"EscapedNumber",inCharClass:e,raw:t}}function At(e){return{type:"GroupClose",raw:e}}function P(e,t,r={}){return{type:"GroupOpen",kind:e,...r,raw:t}}function St(e,t,r,a){return{type:"NamedCallout",kind:e,tag:t,arguments:r,raw:a}}function xt(e,t,r,a){return{type:"Quantifier",kind:e,min:t,max:r,raw:a}}function vt(e){return{type:"Subroutine",raw:e}}var wt=new Set(["COUNT","CMP","ERROR","FAIL","MAX","MISMATCH","SKIP","TOTAL_COUNT"]),_e=new Map([["a",7],["b",8],["e",27],["f",12],["n",10],["r",13],["t",9],["v",11]]);function Tt(e){let t=e[1]==="c"?e[2]:e[3];if(!t||!/[A-Za-z]/.test(t))throw new Error(`Unsupported control character "${e}"`);return A(y(t.toUpperCase())-64,e)}function Et(e,t){let{on:r,off:a}=/^\(\?(?<on>[imx]*)(?:-(?<off>[-imx]*))?/.exec(e).groups;a??="";let i=(t.getCurrentModX()||r.includes("x"))&&!a.includes("x"),o=ye(r),n=ye(a),s={};if(o&&(s.enable=o),n&&(s.disable=n),e.endsWith(")"))return t.replaceCurrentModX(i),Ie("flags",e,{flags:s});if(e.endsWith(":"))return t.pushModX(i),t.numOpenGroups++,P("group",e,{...(o||n)&&{flags:s}});throw new Error(`Unexpected flag modifier "${e}"`)}function Pt(e){let t=/\(\*(?<name>[A-Za-z_]\w*)?(?:\[(?<tag>(?:[A-Za-z_]\w*)?)\])?(?:\{(?<args>[^}]*)\})?\)/.exec(e);if(!t)throw new Error(`Incomplete or invalid named callout "${e}"`);let{name:r,tag:a,args:i}=t.groups;if(!r)throw new Error(`Invalid named callout "${e}"`);if(a==="")throw new Error(`Named callout tag with empty value not allowed "${e}"`);let o=i?i.split(",").filter(d=>d!=="").map(d=>/^[+-]?\d+$/.test(d)?+d:d):[],[n,s,l]=o,p=wt.has(r)?r.toLowerCase():"custom";switch(p){case"fail":case"mismatch":case"skip":if(o.length>0)throw new Error(`Named callout arguments not allowed "${o}"`);break;case"error":if(o.length>1)throw new Error(`Named callout allows only one argument "${o}"`);if(typeof n=="string")throw new Error(`Named callout argument must be a number "${n}"`);break;case"max":if(!o.length||o.length>2)throw new Error(`Named callout must have one or two arguments "${o}"`);if(typeof n=="string"&&!/^[A-Za-z_]\w*$/.test(n))throw new Error(`Named callout argument one must be a tag or number "${n}"`);if(o.length===2&&(typeof s=="number"||!/^[<>X]$/.test(s)))throw new Error(`Named callout optional argument two must be '<', '>', or 'X' "${s}"`);break;case"count":case"total_count":if(o.length>1)throw new Error(`Named callout allows only one argument "${o}"`);if(o.length===1&&(typeof n=="number"||!/^[<>X]$/.test(n)))throw new Error(`Named callout optional argument must be '<', '>', or 'X' "${n}"`);break;case"cmp":if(o.length!==3)throw new Error(`Named callout must have three arguments "${o}"`);if(typeof n=="string"&&!/^[A-Za-z_]\w*$/.test(n))throw new Error(`Named callout argument one must be a tag or number "${n}"`);if(typeof s=="number"||!/^(?:[<>!=]=|[<>])$/.test(s))throw new Error(`Named callout argument two must be '==', '!=', '>', '<', '>=', or '<=' "${s}"`);if(typeof l=="string"&&!/^[A-Za-z_]\w*$/.test(l))throw new Error(`Named callout argument three must be a tag or number "${l}"`);break;case"custom":throw new Error(`Undefined callout name "${r}"`);default:throw new Error(`Unexpected named callout kind "${p}"`)}return St(p,a??null,i?.split(",")??null,e)}function me(e){let t=null,r,a;if(e[0]==="{"){let{minStr:i,maxStr:o}=/^\{(?<minStr>\d*)(?:,(?<maxStr>\d*))?/.exec(e).groups,n=1e5;if(+i>n||o&&+o>n)throw new Error("Quantifier value unsupported in Oniguruma");if(r=+i,a=o===void 0?+i:o===""?1/0:+o,r>a&&(t="possessive",[r,a]=[a,r]),e.endsWith("?")){if(t==="possessive")throw new Error('Unsupported possessive interval quantifier chain with "?"');t="lazy"}else t||(t="greedy")}else r=e[0]==="+"?1:0,a=e[0]==="?"?1:1/0,t=e[1]==="+"?"possessive":e[1]==="?"?"lazy":"greedy";return xt(t,r,a,e)}function Ot(e){let t=e[1].toLowerCase();return T({d:"digit",h:"hex",s:"space",w:"word"}[t],e,{negate:e[1]!==t})}function Mt(e){let{p:t,neg:r,value:a}=/^\\(?<p>[pP])\{(?<neg>\^?)(?<value>[^}]+)/.exec(e).groups;return T("property",e,{value:a,negate:t==="P"&&!r||t==="p"&&!!r})}function ye(e){let t={};return e.includes("i")&&(t.ignoreCase=!0),e.includes("m")&&(t.dotAll=!0),e.includes("x")&&(t.extended=!0),Object.keys(t).length?t:null}function Gt(e){let t={ignoreCase:!1,dotAll:!1,extended:!1,digitIsAscii:!1,posixIsAscii:!1,spaceIsAscii:!1,wordIsAscii:!1,textSegmentMode:null};for(let r=0;r<e.length;r++){let a=e[r];if(!"imxDPSWy".includes(a))throw new Error(`Invalid flag "${a}"`);if(a==="y"){if(!/^y{[gw]}/.test(e.slice(r)))throw new Error('Invalid or unspecified flag "y" mode');t.textSegmentMode=e[r+2]==="g"?"grapheme":"word",r+=3;continue}t[{i:"ignoreCase",m:"dotAll",x:"extended",D:"digitIsAscii",P:"posixIsAscii",S:"spaceIsAscii",W:"wordIsAscii"}[a]]=!0}return t}function Lt(e){if(/^(?:\\u(?!\p{AHex}{4})|\\x(?!\p{AHex}{1,2}|\{\p{AHex}{1,8}\}))/u.test(e))throw new Error(`Incomplete or invalid escape "${e}"`);let t=e[2]==="{"?/^\\x\{\s*(?<hex>\p{AHex}+)/u.exec(e).groups.hex:e.slice(2);return parseInt(t,16)}function Ft(e,t){let{raw:r,inCharClass:a}=e,i=r.slice(1);if(!a&&(i!=="0"&&i.length===1||i[0]!=="0"&&+i<=t))return[ke(r)];let o=[],n=i.match(/^[0-7]+|\d/g);for(let s=0;s<n.length;s++){let l=n[s],p;if(s===0&&l!=="8"&&l!=="9"){if(p=parseInt(l,8),p>127)throw new Error(c`Octal encoded byte above 177 unsupported "${r}"`)}else p=y(l);o.push(A(p,(s===0?"\\":"")+l))}return o}function Ut(e){let t=[],r=new RegExp(oe,"gy"),a;for(;a=r.exec(e);){let i=a[0];if(i[0]==="{"){let o=/^\{(?<min>\d+),(?<max>\d+)\}\??$/.exec(i);if(o){let{min:n,max:s}=o.groups;if(+n>+s&&i.endsWith("?")){r.lastIndex--,t.push(me(i.slice(0,-1)));continue}}}t.push(me(i))}return t}function O(e,t){if(!Array.isArray(e.body))throw new Error("Expected node with body array");if(e.body.length!==1)return!1;let r=e.body[0];return!t||Object.keys(t).every(a=>t[a]===r[a])}function b(e){return!(!$t.has(e.type)||e.type==="AbsenceFunction"&&e.kind!=="repeater")}var $t=new Set(["AbsenceFunction","CapturingGroup","Group","LookaroundAssertion","Regex"]);function E(e){return Dt.has(e.type)}var Dt=new Set(["AbsenceFunction","Backreference","CapturingGroup","Character","CharacterClass","CharacterSet","Group","Quantifier","Subroutine"]);function M(e,t={}){let r={flags:"",normalizeUnknownPropertyNames:!1,skipBackrefValidation:!1,skipLookbehindValidation:!1,skipPropertyNameValidation:!1,unicodePropertyMap:null,...t,rules:{captureGroup:!1,singleline:!1,...t.rules}},a=be(e,{flags:r.flags,rules:{captureGroup:r.rules.captureGroup,singleline:r.rules.singleline}}),i=(u,h)=>{let f=a.tokens[o.nextIndex];switch(o.parent=u,o.nextIndex++,f.type){case"Alternator":return N();case"Assertion":return Kt(f);case"Backreference":return Rt(f,o);case"Character":return K(f.value,{useLastValid:!!h.isCheckingRangeEnd});case"CharacterClassHyphen":return Bt(f,o,h);case"CharacterClassOpen":return Vt(f,o,h);case"CharacterSet":return jt(f,o);case"Directive":return Zt(f.kind,{flags:f.flags});case"GroupOpen":return zt(f,o,h);case"NamedCallout":return Yt(f.kind,f.tag,f.arguments);case"Quantifier":return Ht(f,o);case"Subroutine":return Qt(f,o);default:throw new Error(`Unexpected token type "${f.type}"`)}},o={capturingGroups:[],hasNumberedRef:!1,namedGroupsByName:new Map,nextIndex:0,normalizeUnknownPropertyNames:r.normalizeUnknownPropertyNames,parent:null,skipBackrefValidation:r.skipBackrefValidation,skipLookbehindValidation:r.skipLookbehindValidation,skipPropertyNameValidation:r.skipPropertyNameValidation,subroutines:[],tokens:a.tokens,unicodePropertyMap:r.unicodePropertyMap,walk:i},n=tr(qt(a.flags)),s=n.body[0];for(;o.nextIndex<a.tokens.length;){let u=i(s,{});u.type==="Alternative"?(n.body.push(u),s=u):s.body.push(u)}let{capturingGroups:l,hasNumberedRef:p,namedGroupsByName:d,subroutines:_}=o;if(p&&d.size&&!r.rules.captureGroup)throw new Error("Numbered backref/subroutine not allowed when using named capture");for(let{ref:u}of _)if(typeof u=="number"){if(u>l.length)throw new Error("Subroutine uses a group number that's not defined");u&&(l[u-1].isSubroutined=!0)}else if(d.has(u)){if(d.get(u).length>1)throw new Error(c`Subroutine uses a duplicate group name "\g<${u}>"`);d.get(u)[0].isSubroutined=!0}else throw new Error(c`Subroutine uses a group name that's not defined "\g<${u}>"`);return n}function Kt({kind:e}){return Xt(m({"^":"line_start",$:"line_end","\\A":"string_start","\\b":"word_boundary","\\B":"word_boundary","\\G":"search_start","\\y":"text_segment_boundary","\\Y":"text_segment_boundary","\\z":"string_end","\\Z":"string_end_newline"}[e],`Unexpected assertion kind "${e}"`),{negate:e===c`\B`||e===c`\Y`})}function Rt({raw:e},t){let r=/^\\k[<']/.test(e),a=r?e.slice(3,-1):e.slice(1),i=(o,n=!1)=>{let s=t.capturingGroups.length,l=!1;if(o>s)if(t.skipBackrefValidation)l=!0;else throw new Error(`Not enough capturing groups defined to the left "${e}"`);return t.hasNumberedRef=!0,Ae(n?s+1-o:o,{orphan:l})};if(r){let o=/^(?<sign>-?)0*(?<num>[1-9]\d*)$/.exec(a);if(o)return i(+o.groups.num,!!o.groups.sign);if(/[-+]/.test(a))throw new Error(`Invalid backref name "${e}"`);if(!t.namedGroupsByName.has(a))throw new Error(`Group name not defined to the left "${e}"`);return Ae(a)}return i(+a)}function Bt(e,t,r){let{tokens:a,walk:i}=t,o=t.parent,n=o.body.at(-1),s=a[t.nextIndex];if(!r.isCheckingRangeEnd&&n&&n.type!=="CharacterClass"&&n.type!=="CharacterClassRange"&&s&&s.type!=="CharacterClassOpen"&&s.type!=="CharacterClassClose"&&s.type!=="CharacterClassIntersector"){let l=i(o,{...r,isCheckingRangeEnd:!0});if(n.type==="Character"&&l.type==="Character")return o.body.pop(),W(n,l);throw new Error("Invalid character class range")}return K(y("-"))}function Vt({negate:e},t,r){let{tokens:a,walk:i}=t,o=a[t.nextIndex],n=[D()],s=ve(o);for(;s.type!=="CharacterClassClose";){if(s.type==="CharacterClassIntersector")n.push(D()),t.nextIndex++;else{let p=n.at(-1);p.body.push(i(p,r))}s=ve(a[t.nextIndex],o)}let l=D({negate:e});return n.length===1?l.body=n[0].body:(l.kind="intersection",l.body=n.map(p=>p.body.length===1?p.body[0]:p)),t.nextIndex++,l}function jt({kind:e,negate:t,value:r},a){let{normalizeUnknownPropertyNames:i,skipPropertyNameValidation:o,unicodePropertyMap:n}=a;if(e==="property"){let s=Z(r);if(U.has(s)&&!n?.has(s))e="posix",r=s;else return J(r,{negate:t,normalizeUnknownPropertyNames:i,skipPropertyNameValidation:o,unicodePropertyMap:n})}return e==="posix"?er(r,{negate:t}):I(e,{negate:t})}function zt(e,t,r){let{tokens:a,capturingGroups:i,namedGroupsByName:o,skipLookbehindValidation:n,walk:s}=t,l=nr(e),p=l.type==="AbsenceFunction",d=xe(l),_=d&&l.negate;if(l.type==="CapturingGroup"&&(i.push(l),l.name&&pe(o,l.name,[]).push(l)),p&&r.isInAbsenceFunction)throw new Error("Nested absence function not supported by Oniguruma");let u=we(a[t.nextIndex]);for(;u.type!=="GroupClose";){if(u.type==="Alternator")l.body.push(N()),t.nextIndex++;else{let h=l.body.at(-1),f=s(h,{...r,isInAbsenceFunction:r.isInAbsenceFunction||p,isInLookbehind:r.isInLookbehind||d,isInNegLookbehind:r.isInNegLookbehind||_});if(h.body.push(f),(d||r.isInLookbehind)&&!n){let C="Lookbehind includes a pattern not allowed by Oniguruma";if(_||r.isInNegLookbehind){if(Se(f)||f.type==="CapturingGroup")throw new Error(C)}else if(Se(f)||xe(f)&&f.negate)throw new Error(C)}}u=we(a[t.nextIndex])}return t.nextIndex++,l}function Ht({kind:e,min:t,max:r},a){let i=a.parent,o=i.body.at(-1);if(!o||!E(o))throw new Error("Quantifier requires a repeatable token");let n=X(e,t,r,o);return i.body.pop(),n}function Qt({raw:e},t){let{capturingGroups:r,subroutines:a}=t,i=e.slice(3,-1),o=/^(?<sign>[-+]?)0*(?<num>[1-9]\d*)$/.exec(i);if(o){let s=+o.groups.num,l=r.length;if(t.hasNumberedRef=!0,i={"":s,"+":l+s,"-":l+1-s}[o.groups.sign],i<1)throw new Error("Invalid subroutine number")}else i==="0"&&(i=0);let n=rr(i);return a.push(n),n}function Wt(e,t){if(e!=="repeater")throw new Error(`Unexpected absence function kind "${e}"`);return{type:"AbsenceFunction",kind:e,body:R(t?.body)}}function N(e){return{type:"Alternative",body:Te(e?.body)}}function Xt(e,t){let r={type:"Assertion",kind:e};return(e==="word_boundary"||e==="text_segment_boundary")&&(r.negate=!!t?.negate),r}function Ae(e,t){let r=!!t?.orphan;return{type:"Backreference",ref:e,...r&&{orphan:r}}}function Jt(e,t){let r={name:void 0,isSubroutined:!1,...t};if(r.name!==void 0&&!ar(r.name))throw new Error(`Group name "${r.name}" invalid in Oniguruma`);return{type:"CapturingGroup",number:e,...r.name&&{name:r.name},...r.isSubroutined&&{isSubroutined:r.isSubroutined},body:R(t?.body)}}function K(e,t){let r={useLastValid:!1,...t};if(e>1114111){let a=e.toString(16);if(r.useLastValid)e=1114111;else throw e>1310719?new Error(`Invalid code point out of range "\\x{${a}}"`):new Error(`Invalid code point out of range in JS "\\x{${a}}"`)}return{type:"Character",value:e}}function D(e){let t={kind:"union",negate:!1,...e};return{type:"CharacterClass",kind:t.kind,negate:t.negate,body:Te(e?.body)}}function W(e,t){if(t.value<e.value)throw new Error("Character class range out of order");return{type:"CharacterClassRange",min:e,max:t}}function I(e,t){let r=!!t?.negate,a={type:"CharacterSet",kind:e};return(e==="digit"||e==="hex"||e==="newline"||e==="space"||e==="word")&&(a.negate=r),(e==="text_segment"||e==="newline"&&!r)&&(a.variableLength=!0),a}function Zt(e,t={}){if(e==="keep")return{type:"Directive",kind:e};if(e==="flags")return{type:"Directive",kind:e,flags:m(t.flags)};throw new Error(`Unexpected directive kind "${e}"`)}function qt(e){return{type:"Flags",...e}}function S(e){let t=e?.atomic,r=e?.flags;if(t&&r)throw new Error("Atomic group cannot have flags");return{type:"Group",...t&&{atomic:t},...r&&{flags:r},body:R(e?.body)}}function ie(e){let t={behind:!1,negate:!1,...e};return{type:"LookaroundAssertion",kind:t.behind?"lookbehind":"lookahead",negate:t.negate,body:R(e?.body)}}function Yt(e,t,r){return{type:"NamedCallout",kind:e,tag:t,arguments:r}}function er(e,t){let r=!!t?.negate;if(!U.has(e))throw new Error(`Invalid POSIX class "${e}"`);return{type:"CharacterSet",kind:"posix",value:e,negate:r}}function X(e,t,r,a){if(t>r)throw new Error("Invalid reversed quantifier range");return{type:"Quantifier",kind:e,min:t,max:r,body:a}}function tr(e,t){return{type:"Regex",body:R(t?.body),flags:e}}function rr(e){return{type:"Subroutine",ref:e}}function J(e,t){let r={negate:!1,normalizeUnknownPropertyNames:!1,skipPropertyNameValidation:!1,unicodePropertyMap:null,...t},a=r.unicodePropertyMap?.get(Z(e));if(!a){if(r.normalizeUnknownPropertyNames)a=or(e);else if(r.unicodePropertyMap&&!r.skipPropertyNameValidation)throw new Error(c`Invalid Unicode property "\p{${e}}"`)}return{type:"CharacterSet",kind:"property",value:a??e,negate:r.negate}}function nr({flags:e,kind:t,name:r,negate:a,number:i}){switch(t){case"absence_repeater":return Wt("repeater");case"atomic":return S({atomic:!0});case"capturing":return Jt(i,{name:r});case"group":return S({flags:e});case"lookahead":case"lookbehind":return ie({behind:t==="lookbehind",negate:a});default:throw new Error(`Unexpected group kind "${t}"`)}}function R(e){if(e===void 0)e=[N()];else if(!Array.isArray(e)||!e.length||!e.every(t=>t.type==="Alternative"))throw new Error("Invalid body; expected array of one or more Alternative nodes");return e}function Te(e){if(e===void 0)e=[];else if(!Array.isArray(e)||!e.every(t=>!!t.type))throw new Error("Invalid body; expected array of nodes");return e}function Se(e){return e.type==="LookaroundAssertion"&&e.kind==="lookahead"}function xe(e){return e.type==="LookaroundAssertion"&&e.kind==="lookbehind"}function ar(e){return/^[\p{Alpha}\p{Pc}][^)]*$/u.test(e)}function or(e){return e.trim().replace(/[- _]+/g,"_").replace(/[A-Z][a-z]+(?=[A-Z])/g,"$&_").replace(/[A-Za-z]+/g,t=>t[0].toUpperCase()+t.slice(1).toLowerCase())}function Z(e){return e.replace(/[- _]+/g,"").toLowerCase()}function ve(e,t){return m(e,`${t?.type==="Character"&&t.value===93?"Empty":"Unclosed"} character class`)}function we(e){return m(e,"Unclosed group")}var G=new Map(`ASCII_Hex_Digit
Adlam
Ahom
Alphabetic
Anatolian_Hieroglyphs
Any
Arabic
Armenian
Assigned
Avestan
Balinese
Bamum
Bassa_Vah
Batak
Bengali
Bhaiksuki
Bidi_Control
Bopomofo
Brahmi
Braille
Buginese
Buhid
C
Canadian_Aboriginal
Carian
Case_Ignorable
Cased
Caucasian_Albanian
Cc
Cf
Chakma
Cham
Changes_When_Casefolded
Changes_When_Casemapped
Changes_When_Lowercased
Changes_When_Titlecased
Changes_When_Uppercased
Cherokee
Chorasmian
Cn
Co
Common
Coptic
Cs
Cuneiform
Cypriot
Cypro_Minoan
Cyrillic
Dash
Default_Ignorable_Code_Point
Deprecated
Deseret
Devanagari
Diacritic
Dives_Akuru
Dogra
Duployan
Egyptian_Hieroglyphs
Elbasan
Elymaic
Emoji
Emoji_Component
Emoji_Modifier
Emoji_Modifier_Base
Emoji_Presentation
Ethiopic
Extended_Pictographic
Extender
Garay
Georgian
Glagolitic
Gothic
Grantha
Grapheme_Base
Grapheme_Extend
Grapheme_Link
Greek
Gujarati
Gunjala_Gondi
Gurmukhi
Gurung_Khema
Han
Hangul
Hanifi_Rohingya
Hanunoo
Hatran
Hebrew
Hex_Digit
Hiragana
Hyphen
IDS_Binary_Operator
IDS_Trinary_Operator
IDS_Unary_Operator
ID_Compat_Math_Continue
ID_Compat_Math_Start
ID_Continue
ID_Start
Ideographic
Imperial_Aramaic
InCB
Inherited
Inscriptional_Pahlavi
Inscriptional_Parthian
Javanese
Join_Control
Kaithi
Kannada
Katakana
Kawi
Kayah_Li
Kharoshthi
Khitan_Small_Script
Khmer
Khojki
Khudawadi
Kirat_Rai
L
LC
Lao
Latin
Lepcha
Limbu
Linear_A
Linear_B
Lisu
Ll
Lm
Lo
Logical_Order_Exception
Lowercase
Lt
Lu
Lycian
Lydian
M
Mahajani
Makasar
Malayalam
Mandaic
Manichaean
Marchen
Masaram_Gondi
Math
Mc
Me
Medefaidrin
Meetei_Mayek
Mende_Kikakui
Meroitic_Cursive
Meroitic_Hieroglyphs
Miao
Mn
Modi
Modifier_Combining_Mark
Mongolian
Mro
Multani
Myanmar
N
Nabataean
Nag_Mundari
Nandinagari
Nd
New_Tai_Lue
Newa
Nko
Nl
No
Noncharacter_Code_Point
Nushu
Nyiakeng_Puachue_Hmong
Ogham
Ol_Chiki
Ol_Onal
Old_Hungarian
Old_Italic
Old_North_Arabian
Old_Permic
Old_Persian
Old_Sogdian
Old_South_Arabian
Old_Turkic
Old_Uyghur
Oriya
Osage
Osmanya
Other_Alphabetic
Other_Default_Ignorable_Code_Point
Other_Grapheme_Extend
Other_ID_Continue
Other_ID_Start
Other_Lowercase
Other_Math
Other_Uppercase
P
Pahawh_Hmong
Palmyrene
Pattern_Syntax
Pattern_White_Space
Pau_Cin_Hau
Pc
Pd
Pe
Pf
Phags_Pa
Phoenician
Pi
Po
Prepended_Concatenation_Mark
Ps
Psalter_Pahlavi
Quotation_Mark
Radical
Regional_Indicator
Rejang
Runic
S
Samaritan
Saurashtra
Sc
Sentence_Terminal
Sharada
Shavian
Siddham
SignWriting
Sinhala
Sk
Sm
So
Soft_Dotted
Sogdian
Sora_Sompeng
Soyombo
Sundanese
Sunuwar
Syloti_Nagri
Syriac
Tagalog
Tagbanwa
Tai_Le
Tai_Tham
Tai_Viet
Takri
Tamil
Tangsa
Tangut
Telugu
Terminal_Punctuation
Thaana
Thai
Tibetan
Tifinagh
Tirhuta
Todhri
Toto
Tulu_Tigalari
Ugaritic
Unified_Ideograph
Unknown
Uppercase
Vai
Variation_Selector
Vithkuqi
Wancho
Warang_Citi
White_Space
XID_Continue
XID_Start
Yezidi
Yi
Z
Zanabazar_Square
Zl
Zp
Zs
Adlm
Aghb
AHex
Arab
Armi
Armn
Avst
Bali
Bamu
Bass
Batk
Beng
Bhks
Bidi_C
Bopo
Brah
Brai
Bugi
Buhd
Cakm
Cans
Cari
Cased_Letter
Cher
Chrs
CI
Close_Punctuation
Combining_Mark
Connector_Punctuation
Control
Copt
Cpmn
Cprt
Currency_Symbol
CWCF
CWCM
CWL
CWT
CWU
Cyrl
Dash_Punctuation
Decimal_Number
Dep
Deva
DI
Dia
Diak
Dogr
Dsrt
Dupl
EBase
EComp
Egyp
Elba
Elym
EMod
Enclosing_Mark
EPres
Ethi
Ext
ExtPict
Final_Punctuation
Format
Gara
Geor
Glag
Gong
Gonm
Goth
Gran
Gr_Base
Grek
Gr_Ext
Gr_Link
Gujr
Gukh
Guru
Hang
Hani
Hano
Hatr
Hebr
Hex
Hira
Hluw
Hmng
Hmnp
Hung
IDC
Ideo
IDS
IDSB
IDST
IDSU
Initial_Punctuation
Ital
Java
Join_C
Kali
Kana
Khar
Khmr
Khoj
Kits
Knda
Krai
Kthi
Lana
Laoo
Latn
Lepc
Letter
Letter_Number
Limb
Lina
Linb
Line_Separator
LOE
Lowercase_Letter
Lyci
Lydi
Mahj
Maka
Mand
Mani
Marc
Mark
Math_Symbol
MCM
Medf
Mend
Merc
Mero
Mlym
Modifier_Letter
Modifier_Symbol
Mong
Mroo
Mtei
Mult
Mymr
Nagm
Nand
Narb
Nbat
NChar
Nkoo
Nonspacing_Mark
Nshu
Number
OAlpha
ODI
Ogam
OGr_Ext
OIDC
OIDS
Olck
OLower
OMath
Onao
Open_Punctuation
Orkh
Orya
Osge
Osma
Other
Other_Letter
Other_Number
Other_Punctuation
Other_Symbol
Ougr
OUpper
Palm
Paragraph_Separator
Pat_Syn
Pat_WS
Pauc
PCM
Perm
Phag
Phli
Phlp
Phnx
Plrd
Private_Use
Prti
punct
Punctuation
Qaac
Qaai
QMark
RI
Rjng
Rohg
Runr
Samr
Sarb
Saur
SD
Separator
Sgnw
Shaw
Shrd
Sidd
Sind
Sinh
Sogd
Sogo
Sora
Soyo
Space_Separator
Spacing_Mark
STerm
Sund
Sunu
Surrogate
Sylo
Symbol
Syrc
Tagb
Takr
Tale
Talu
Taml
Tang
Tavt
Telu
Term
Tfng
Tglg
Thaa
Tibt
Tirh
Titlecase_Letter
Tnsa
Todr
Tutg
Ugar
UIdeo
Unassigned
Uppercase_Letter
Vaii
Vith
VS
Wara
Wcho
WSpace
XIDC
XIDS
Xpeo
Xsux
Yezi
Yiii
Zanb
Zinh
Zyyy
Zzzz
In_Basic_Latin
In_Latin_1_Supplement
In_Latin_Extended_A
In_Latin_Extended_B
In_IPA_Extensions
In_Spacing_Modifier_Letters
In_Combining_Diacritical_Marks
In_Greek_and_Coptic
In_Cyrillic
In_Cyrillic_Supplement
In_Armenian
In_Hebrew
In_Arabic
In_Syriac
In_Arabic_Supplement
In_Thaana
In_NKo
In_Samaritan
In_Mandaic
In_Syriac_Supplement
In_Arabic_Extended_B
In_Arabic_Extended_A
In_Devanagari
In_Bengali
In_Gurmukhi
In_Gujarati
In_Oriya
In_Tamil
In_Telugu
In_Kannada
In_Malayalam
In_Sinhala
In_Thai
In_Lao
In_Tibetan
In_Myanmar
In_Georgian
In_Hangul_Jamo
In_Ethiopic
In_Ethiopic_Supplement
In_Cherokee
In_Unified_Canadian_Aboriginal_Syllabics
In_Ogham
In_Runic
In_Tagalog
In_Hanunoo
In_Buhid
In_Tagbanwa
In_Khmer
In_Mongolian
In_Unified_Canadian_Aboriginal_Syllabics_Extended
In_Limbu
In_Tai_Le
In_New_Tai_Lue
In_Khmer_Symbols
In_Buginese
In_Tai_Tham
In_Combining_Diacritical_Marks_Extended
In_Balinese
In_Sundanese
In_Batak
In_Lepcha
In_Ol_Chiki
In_Cyrillic_Extended_C
In_Georgian_Extended
In_Sundanese_Supplement
In_Vedic_Extensions
In_Phonetic_Extensions
In_Phonetic_Extensions_Supplement
In_Combining_Diacritical_Marks_Supplement
In_Latin_Extended_Additional
In_Greek_Extended
In_General_Punctuation
In_Superscripts_and_Subscripts
In_Currency_Symbols
In_Combining_Diacritical_Marks_for_Symbols
In_Letterlike_Symbols
In_Number_Forms
In_Arrows
In_Mathematical_Operators
In_Miscellaneous_Technical
In_Control_Pictures
In_Optical_Character_Recognition
In_Enclosed_Alphanumerics
In_Box_Drawing
In_Block_Elements
In_Geometric_Shapes
In_Miscellaneous_Symbols
In_Dingbats
In_Miscellaneous_Mathematical_Symbols_A
In_Supplemental_Arrows_A
In_Braille_Patterns
In_Supplemental_Arrows_B
In_Miscellaneous_Mathematical_Symbols_B
In_Supplemental_Mathematical_Operators
In_Miscellaneous_Symbols_and_Arrows
In_Glagolitic
In_Latin_Extended_C
In_Coptic
In_Georgian_Supplement
In_Tifinagh
In_Ethiopic_Extended
In_Cyrillic_Extended_A
In_Supplemental_Punctuation
In_CJK_Radicals_Supplement
In_Kangxi_Radicals
In_Ideographic_Description_Characters
In_CJK_Symbols_and_Punctuation
In_Hiragana
In_Katakana
In_Bopomofo
In_Hangul_Compatibility_Jamo
In_Kanbun
In_Bopomofo_Extended
In_CJK_Strokes
In_Katakana_Phonetic_Extensions
In_Enclosed_CJK_Letters_and_Months
In_CJK_Compatibility
In_CJK_Unified_Ideographs_Extension_A
In_Yijing_Hexagram_Symbols
In_CJK_Unified_Ideographs
In_Yi_Syllables
In_Yi_Radicals
In_Lisu
In_Vai
In_Cyrillic_Extended_B
In_Bamum
In_Modifier_Tone_Letters
In_Latin_Extended_D
In_Syloti_Nagri
In_Common_Indic_Number_Forms
In_Phags_pa
In_Saurashtra
In_Devanagari_Extended
In_Kayah_Li
In_Rejang
In_Hangul_Jamo_Extended_A
In_Javanese
In_Myanmar_Extended_B
In_Cham
In_Myanmar_Extended_A
In_Tai_Viet
In_Meetei_Mayek_Extensions
In_Ethiopic_Extended_A
In_Latin_Extended_E
In_Cherokee_Supplement
In_Meetei_Mayek
In_Hangul_Syllables
In_Hangul_Jamo_Extended_B
In_High_Surrogates
In_High_Private_Use_Surrogates
In_Low_Surrogates
In_Private_Use_Area
In_CJK_Compatibility_Ideographs
In_Alphabetic_Presentation_Forms
In_Arabic_Presentation_Forms_A
In_Variation_Selectors
In_Vertical_Forms
In_Combining_Half_Marks
In_CJK_Compatibility_Forms
In_Small_Form_Variants
In_Arabic_Presentation_Forms_B
In_Halfwidth_and_Fullwidth_Forms
In_Specials
In_Linear_B_Syllabary
In_Linear_B_Ideograms
In_Aegean_Numbers
In_Ancient_Greek_Numbers
In_Ancient_Symbols
In_Phaistos_Disc
In_Lycian
In_Carian
In_Coptic_Epact_Numbers
In_Old_Italic
In_Gothic
In_Old_Permic
In_Ugaritic
In_Old_Persian
In_Deseret
In_Shavian
In_Osmanya
In_Osage
In_Elbasan
In_Caucasian_Albanian
In_Vithkuqi
In_Todhri
In_Linear_A
In_Latin_Extended_F
In_Cypriot_Syllabary
In_Imperial_Aramaic
In_Palmyrene
In_Nabataean
In_Hatran
In_Phoenician
In_Lydian
In_Meroitic_Hieroglyphs
In_Meroitic_Cursive
In_Kharoshthi
In_Old_South_Arabian
In_Old_North_Arabian
In_Manichaean
In_Avestan
In_Inscriptional_Parthian
In_Inscriptional_Pahlavi
In_Psalter_Pahlavi
In_Old_Turkic
In_Old_Hungarian
In_Hanifi_Rohingya
In_Garay
In_Rumi_Numeral_Symbols
In_Yezidi
In_Arabic_Extended_C
In_Old_Sogdian
In_Sogdian
In_Old_Uyghur
In_Chorasmian
In_Elymaic
In_Brahmi
In_Kaithi
In_Sora_Sompeng
In_Chakma
In_Mahajani
In_Sharada
In_Sinhala_Archaic_Numbers
In_Khojki
In_Multani
In_Khudawadi
In_Grantha
In_Tulu_Tigalari
In_Newa
In_Tirhuta
In_Siddham
In_Modi
In_Mongolian_Supplement
In_Takri
In_Myanmar_Extended_C
In_Ahom
In_Dogra
In_Warang_Citi
In_Dives_Akuru
In_Nandinagari
In_Zanabazar_Square
In_Soyombo
In_Unified_Canadian_Aboriginal_Syllabics_Extended_A
In_Pau_Cin_Hau
In_Devanagari_Extended_A
In_Sunuwar
In_Bhaiksuki
In_Marchen
In_Masaram_Gondi
In_Gunjala_Gondi
In_Makasar
In_Kawi
In_Lisu_Supplement
In_Tamil_Supplement
In_Cuneiform
In_Cuneiform_Numbers_and_Punctuation
In_Early_Dynastic_Cuneiform
In_Cypro_Minoan
In_Egyptian_Hieroglyphs
In_Egyptian_Hieroglyph_Format_Controls
In_Egyptian_Hieroglyphs_Extended_A
In_Anatolian_Hieroglyphs
In_Gurung_Khema
In_Bamum_Supplement
In_Mro
In_Tangsa
In_Bassa_Vah
In_Pahawh_Hmong
In_Kirat_Rai
In_Medefaidrin
In_Miao
In_Ideographic_Symbols_and_Punctuation
In_Tangut
In_Tangut_Components
In_Khitan_Small_Script
In_Tangut_Supplement
In_Kana_Extended_B
In_Kana_Supplement
In_Kana_Extended_A
In_Small_Kana_Extension
In_Nushu
In_Duployan
In_Shorthand_Format_Controls
In_Symbols_for_Legacy_Computing_Supplement
In_Znamenny_Musical_Notation
In_Byzantine_Musical_Symbols
In_Musical_Symbols
In_Ancient_Greek_Musical_Notation
In_Kaktovik_Numerals
In_Mayan_Numerals
In_Tai_Xuan_Jing_Symbols
In_Counting_Rod_Numerals
In_Mathematical_Alphanumeric_Symbols
In_Sutton_SignWriting
In_Latin_Extended_G
In_Glagolitic_Supplement
In_Cyrillic_Extended_D
In_Nyiakeng_Puachue_Hmong
In_Toto
In_Wancho
In_Nag_Mundari
In_Ol_Onal
In_Ethiopic_Extended_B
In_Mende_Kikakui
In_Adlam
In_Indic_Siyaq_Numbers
In_Ottoman_Siyaq_Numbers
In_Arabic_Mathematical_Alphabetic_Symbols
In_Mahjong_Tiles
In_Domino_Tiles
In_Playing_Cards
In_Enclosed_Alphanumeric_Supplement
In_Enclosed_Ideographic_Supplement
In_Miscellaneous_Symbols_and_Pictographs
In_Emoticons
In_Ornamental_Dingbats
In_Transport_and_Map_Symbols
In_Alchemical_Symbols
In_Geometric_Shapes_Extended
In_Supplemental_Arrows_C
In_Supplemental_Symbols_and_Pictographs
In_Chess_Symbols
In_Symbols_and_Pictographs_Extended_A
In_Symbols_for_Legacy_Computing
In_CJK_Unified_Ideographs_Extension_B
In_CJK_Unified_Ideographs_Extension_C
In_CJK_Unified_Ideographs_Extension_D
In_CJK_Unified_Ideographs_Extension_E
In_CJK_Unified_Ideographs_Extension_F
In_CJK_Unified_Ideographs_Extension_I
In_CJK_Compatibility_Ideographs_Supplement
In_CJK_Unified_Ideographs_Extension_G
In_CJK_Unified_Ideographs_Extension_H
In_Tags
In_Variation_Selectors_Supplement
In_Supplementary_Private_Use_Area_A
In_Supplementary_Private_Use_Area_B
In_No_Block`.split(/\s/).map(e=>[Z(e),e]));function Ee(e,t={}){if({}.toString.call(t)!=="[object Object]")throw new Error("Unexpected options");return M(e,{flags:t.flags??"",rules:{captureGroup:t.rules?.captureGroup??!1,singleline:t.rules?.singleline??!1},unicodePropertyMap:G})}function Y(e,t,r=null){function a(o,n){for(let s=0;s<o.length;s++){let l=i(o[s],n,s,o);s=Math.max(-1,s+l)}}function i(o,n=null,s=null,l=null){let p=0,d=!1,_={node:o,parent:n,key:s,container:l,root:e,remove(){q(l).splice(Math.max(0,L(s)+p),1),p--,d=!0},removeAllNextSiblings(){return q(l).splice(L(s)+1)},removeAllPrevSiblings(){let g=L(s)+p;return p-=g,q(l).splice(0,Math.max(0,g))},replaceWith(g,F={}){let re=!!F.traverse;l?l[Math.max(0,L(s)+p)]=g:m(n,"Can't replace root node")[s]=g,re&&i(g,n,s,l),d=!0},replaceWithMultiple(g,F={}){let re=!!F.traverse;if(q(l).splice(Math.max(0,L(s)+p),1,...g),p+=g.length-1,re){let ue=0;for(let z=0;z<g.length;z++)ue+=i(g[z],n,L(s)+z+ue,l)}d=!0},skip(){d=!0}},{type:u}=o,h=t["*"],f=t[u],C=typeof h=="function"?h:h?.enter,j=typeof f=="function"?f:f?.enter;if(C?.(_,r),j?.(_,r),!d)switch(u){case"AbsenceFunction":case"CapturingGroup":case"Group":a(o.body,o);break;case"Alternative":case"CharacterClass":a(o.body,o);break;case"Assertion":case"Backreference":case"Character":case"CharacterSet":case"Directive":case"Flags":case"NamedCallout":case"Subroutine":break;case"CharacterClassRange":i(o.min,o,"min"),i(o.max,o,"max");break;case"LookaroundAssertion":a(o.body,o);break;case"Quantifier":i(o.body,o,"body");break;case"Regex":a(o.body,o),i(o.flags,o,"flags");break;default:throw new Error(`Unexpected node type "${u}"`)}return f?.exit?.(_,r),h?.exit?.(_,r),p}return i(e),e}function q(e){if(!Array.isArray(e))throw new Error("Container expected");return e}function L(e){if(typeof e!="number")throw new Error("Numeric key expected");return e}var Oe={"*"({node:e}){if(!b(e)||e.body.length<2)return;let t=[],r=[];for(let a of e.body){let i=a.body[0];a.body.length===1&&(i.type==="Character"||i.type==="CharacterClass"||i.type==="CharacterSet"&&ir.has(i.kind))?r.push(i):(r.length&&(t.push(Pe(r)),r=[]),t.push(a))}r.length&&t.push(Pe(r)),e.body=t}};function Pe(e){let t=N(),r=e.length>1?D({body:e}):e[0];return r&&t.body.push(r),t}var ir=new Set(["digit","hex","posix","property","space","word"]);var Me={CapturingGroup({node:e,parent:t,replaceWithMultiple:r}){if(t.type==="Quantifier"||e.body.length>1||e.isSubroutined)return;let a=e.body[0],i=a.body,o=i[0],n=i.length>1?i.at(-1):null,s=o&&o.type==="Assertion",l=n&&n.type==="Assertion",p=s?1:0,d=i.length-(l?1:0);if(s||l){a.body=i.slice(p,d);let _=[];s&&_.push(o),_.push(e),l&&_.push(n),r(_,{traverse:!0})}}};var Ge={"*"({node:e}){if(!b(e)||e.body.length<2)return;let t=[],r=!1,a=0;for(;!r;){t.push(e.body[0].body[a]);for(let n of e.body){let s=n.body[a];if(!s||!x(s)||!v(s,t[a])){r=!0;break}}a++}if(t.pop(),!t.length)return;for(let n of e.body)n.body=n.body.slice(t.length);let i=N({body:t}),o=S({body:e.body});o.body.every(n=>!n.body.length)||i.body.push(o),e.body=[i]}};function x(e){return e.type==="Assertion"||e.type==="Character"||e.type==="CharacterSet"}function v(e,t){if(e.type!==t.type)return!1;if(e.type==="Assertion"||e.type==="CharacterSet")return e.kind===t.kind&&e.negate===t.negate;if(e.type==="Character")return e.value===t.value;throw new Error(`Unexpected node type "${e.type}"`)}var Le={"*"({node:e}){if(!b(e))return;let t=2,r=e.body.length;if(r<t*2||r%t)return;let a=[...e.body.slice(0,t).map(h=>h.body)],i=Array.from({length:t},()=>[]),o=Array(t).fill(!1),n=Math.max(...a.map(h=>h.length));for(let h=0;h<n;h++)for(let f=0;f<t;f++)if(!o[f]){let C=a[f][h];!C||!x(C)||!sr(C,e.body,f,h,t)?o[f]=!0:i[f].push(C)}if(!i.some(h=>h.length))return;let s=[],l=0;for(let h=0;h<r;h++)s.push(N({body:e.body[h].body.slice(i[l].length)})),l=l<t-1?l+1:0;for(let h=0;h<r/t;h++){let f=s.slice(h*t,h*t+t);for(let C=1;C<f.length;C++){let j=f[C].body;if(j.length!==f[0].body.length||!j.every((g,F)=>x(g)&&v(g,f[0].body[F])))return}}let p=[];for(let h=0;h<t;h++)p.push(N({body:i[h]}));let d=S({body:p}),_=N({body:[d]}),u=S({body:s.filter((h,f)=>f%t)});u.body.every(h=>!h.body.length)?e.body=d.body:(_.body.push(u),e.body=[_])}};function sr(e,t,r,a,i){for(let o=r;o<t.length;o+=i){let s=t[o].body[a];if(!s||!v(s,e))return!1}return!0}var Fe={"*"({node:e}){if(!b(e)||e.body.length<2)return;let t=e.body[0].body,r=[],a=!1,i=0;for(;!a;){let s=t.length-1-i;r.push(t[s]);for(let l of e.body){let p=l.body.length-1-i,d=l.body[p];if(!d||!x(d)||!v(d,r[i])){a=!0;break}}i++}if(r.pop(),!r.length||r.length<3&&r[0].type!=="Assertion"&&r.length*(e.body.length-1)<4&&!e.body.some((s,l,p)=>{let d=p[l-1],_=r.length;return s.body.length-_<2&&d&&d.body.length-_<2}))return;r.reverse();for(let s of e.body)s.body=s.body.slice(0,-r.length);let o=N(),n=S({body:e.body});n.body.every(s=>!s.body.length)||o.body.push(n),o.body.push(...r),e.body=[o]}};var $e={CharacterClass({node:e}){if(e.kind!=="union"||!e.body.length)return;let t=[];for(let n of e.body)n.type==="CharacterSet"&&t.some(s=>s.type===n.type&&s.kind===n.kind&&s.negate===n.negate&&s.value===n.value)||t.push(n);e.body=t;let r=[],a=[];for(let n of e.body)n.type==="Character"||n.type==="CharacterClassRange"?a.push(n):r.push(n);if(!a.length)return;a.sort((n,s)=>{let l=n.type==="Character"?n.value:n.min.value,p=s.type==="Character"?s.value:s.min.value;return l-p});let i=[a[0]];for(let n=1;n<a.length;n++){let s=a[n],l=i.at(-1),p=s.type==="Character"?s.value:s.min.value,d=l.type==="Character"?l.value:l.max.value;if(p<=d+1)if(l.type==="Character"&&s.type==="Character")l.value!==s.value&&(i[i.length-1]=W(l,s));else if(l.type==="Character"&&s.type==="CharacterClassRange")i[i.length-1]=W(K(l.value),s.max);else if(l.type==="CharacterClassRange"&&s.type==="Character")l.max.value=Math.max(s.value,l.max.value);else if(l.type==="CharacterClassRange"&&s.type==="CharacterClassRange")l.max.value=Math.max(s.max.value,l.max.value);else throw new Error("Unexpected merge case");else i.push(s)}let o=i.flatMap(n=>{if(n.type==="CharacterClassRange"){let s=n.max.value-n.min.value;if(n.min.value>262143&&s>1)return n;if(s){if(s===1)return[n.min,n.max];if(s===2)return[n.min,K(n.min.value+1),n.max]}else return n.min}return n});e.body=[...o.filter(n=>Ue(n)),...o.filter(n=>!Ue(n)),...r]}};function Ue(e){return e.type==="Character"&&(e.value===45||e.value===93)}var Ke={"*"({node:e}){if(!b(e)||e.body.length<2)return;let{body:t}=e,r=[t[0]],a=t[0];for(let i=1;i<t.length;i++){let o=t[i],n=o.body,s=a.body,l=Math.abs(n.length-s.length);if(l){if(l===1){let p=s.length>n.length,d=p?n:n.slice(0,-1),_=p?s.slice(0,-1):s;if(De(d,_)){if(p){let u=m(s.at(-1));if(E(u))if(u.type==="Quantifier")if(u.min){if(u.min===1&&u.kind!=="lazy"){u.min=0;continue}}else continue;else{s.pop(),s.push(X("greedy",0,1,u));continue}}else if(s.length>0||t.length===2){let u=m(n.at(-1));if(E(u))if(u.type==="Quantifier"){if(u.kind!=="possessive"){if(u.min<=1&&u.kind==="lazy"){u.min=0,s.push(u);continue}else if(!u.min&&u.max===1){u.kind="lazy",s.push(u);continue}}}else{s.push(X("lazy",0,1,u));continue}}}}}else if(De(n,s))continue;r.push(o),a=o}e.body=r}};function De(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(!x(e[r])||!x(t[r])||!v(e[r],t[r]))return!1;return!0}var Re={Quantifier({node:e}){let{body:t,max:r}=e;if(r!==1/0||t.type!=="Group"||t.atomic)return;let a=t.body[0];if(!O(a,{type:"Quantifier"}))return;let i=a.body[0];i.kind==="possessive"||i.min>1||i.max<2||(i.min?i.min===1&&(a.body[0]=i.body):i.max=1)}};var Be={AbsenceFunction({node:e,remove:t}){ee(e)&&t()},Group({node:e,remove:t}){ee(e)&&t()},LookaroundAssertion({node:e,remove:t}){ee(e)&&t()},Quantifier({node:e,remove:t}){let r=e.body;for(;r.type==="Quantifier";)r=r.body;ee(r)&&t()}};function se(e){return e.body.every(t=>!t.body.length)}function ee(e){switch(e.type){case"AbsenceFunction":return e.kind==="repeater"&&se(e);case"Group":return se(e);case"LookaroundAssertion":return!e.negate&&se(e);default:return!1}}var ze={Flags({node:e}){e.extended=!1,e.textSegmentMode==="grapheme"&&(e.textSegmentMode=null)},Directive({node:e,remove:t}){e.kind==="flags"&&(je(e),Ve(e)&&t())},Group({node:e}){e.flags&&(je(e),Ve(e))}};function Ve(e){let{flags:t}=e;return t&&!t.enable&&!t.disable?(delete e.flags,!0):!1}function je({flags:e}){if(!e)throw new Error("Expected flags");e.enable&&delete e.enable.extended,e.disable&&delete e.disable.extended,lr(e)}function lr(e){e.enable&&!Object.keys(e.enable).length&&delete e.enable,e.disable&&!Object.keys(e.disable).length&&delete e.disable}var He={NamedCallout({node:e,replaceWith:t}){let{arguments:r,kind:a}=e;if(a==="fail"){t(ie({negate:!0}));return}if(!r)return;let i=r.filter(o=>o!=="").map(o=>typeof o=="string"&&/^[+-]?\d+$/.test(o)?+o:o);e.arguments=i.length?i:null}};var Qe={CharacterClass({node:e,parent:t,replaceWith:r,replaceWithMultiple:a}){let{body:i,kind:o,negate:n}=e;if(t.type!=="CharacterClass"||o!=="union"||!i.length)return;let s=i[0];if(O(t,{type:"CharacterClass",kind:"union"})){t.negate=t.negate!==n,a(i,{traverse:!0});return}n||(t.kind==="union"?a(i,{traverse:!0}):O(e)&&r(s,{traverse:!0}))}};var We={CharacterClass({node:e,parent:t,replaceWith:r}){let{body:a,kind:i,negate:o}=e;if(!o||i!=="union"||a.length!==1)return;let n=a[0];if(n.type==="CharacterSet")n.negate=!n.negate,r(n);else if(t.type!=="CharacterClass"&&n.type==="Character"&&n.value===10){if(t.type==="Quantifier"&&t.kind!=="lazy")return;r(I("newline",{negate:!0}))}}};var Xe={CharacterClass({node:e,parent:t,replaceWith:r}){let{body:a,kind:i,negate:o}=e,n=a[0];t.type==="CharacterClass"||o||i!=="union"||a.length!==1||n.type!=="Character"&&n.type!=="CharacterSet"||r(n,{traverse:!0})}};var Ze={"*"({node:e}){b(e)&&ur(e)&&(e.body=e.body[0].body[0].body)},Group({node:e,parent:t,replaceWithMultiple:r}){let{atomic:a,body:i,flags:o}=e,n=i[0].body;if(i.length>1||t.type==="Quantifier")return;let s=!1;a?n.every(({type:l})=>Je.has(l))&&(s=!0):o||(s=!0),s&&r(n,{traverse:!0})},Quantifier({node:e}){if(e.body.type!=="Group")return;let t=e.body;if(t.body.length>1)return;let r=t.body[0].body;if(r.length!==1)return;let a=r[0];!E(a)||t.atomic&&!Je.has(a.type)||t.flags||(e.body=a)}},Je=new Set(["Assertion","Backreference","Character","CharacterClass","CharacterSet","Directive","NamedCallout"]);function ur({body:e}){let t=e[0].body;return e.length===1&&t.length===1&&t[0].type==="Group"&&!t[0].atomic&&!t[0].flags&&t[0].body.length>1}var qe={CharacterSet({node:e,parent:t,root:r,replaceWith:a}){let{kind:i,negate:o,value:n}=e,s=null;i==="property"&&(n==="Decimal_Number"||n==="Nd")&&!r.flags.digitIsAscii&&!r.flags.posixIsAscii||i==="posix"&&n==="digit"?s=I("digit",{negate:o}):i==="property"&&(n==="ASCII_Hex_Digit"||n==="AHex")||i==="posix"&&n==="xdigit"?s=I("hex",{negate:o}):i==="property"&&(n==="White_Space"||n==="WSpace")&&!r.flags.spaceIsAscii&&!r.flags.posixIsAscii||i==="posix"&&n==="space"?s=I("space",{negate:o}):t.type!=="CharacterClass"&&i==="property"&&!o&&n==="Any"&&(s=I("any")),s&&a(s)},CharacterClass({node:e,root:t}){if(e.kind!=="union")return;let r={rangeDigit0To9:!1,rangeAToFLower:!1,rangeAToFUpper:!1,unicodeL:!1,unicodeM:!1,unicodeN:!1,unicodePc:!1};for(let a of e.body)a.type==="CharacterClassRange"?(r.rangeDigit0To9||=w(a,k.n0,k.n9),r.rangeAToFLower||=w(a,k.a,k.f),r.rangeAToFUpper||=w(a,k.A,k.F)):a.type==="CharacterSet"&&(r.unicodeL||=B(a,"L"),r.unicodeM||=B(a,"M"),r.unicodeN||=B(a,"N"),r.unicodePc||=B(a,"Pc",{includeSupercategories:!0}));r.rangeDigit0To9&&r.rangeAToFUpper&&r.rangeAToFLower&&(e.body=e.body.filter(a=>!(w(a,k.n0,k.n9)||w(a,k.a,k.f)||w(a,k.A,k.F))),e.body.push(I("hex"))),r.unicodeL&&r.unicodeM&&r.unicodeN&&r.unicodePc&&!t.flags.wordIsAscii&&!t.flags.posixIsAscii&&(e.body=e.body.filter(a=>!B(a,["L","M","N","Pc"],{includeSubcategories:!0})),e.body.push(I("word")))}},k={n0:y("0"),n9:y("9"),A:y("A"),F:y("F"),a:y("a"),f:y("f")};function w(e,t,r){return e.type==="CharacterClassRange"&&e.min.value===t&&e.max.value===r}function B(e,t,r={}){if(e.type!=="CharacterSet"||e.kind!=="property"||e.negate)return!1;let a=Array.isArray(t)?t:[t],i=[];for(let o of a){i.push(o);let n=V[o]?.full,s=Ye[o],l=V[o]?.sub;n&&i.push(n),r.includeSupercategories&&s&&(i.push(s),i.push(V[s].full)),r.includeSubcategories&&l&&i.push(...l)}return i.includes(e.value)}var pr=["Ll","Lm","Lo","Lt","Lu"],cr=["Mc","Me","Mn"],dr=["Nd","Nl","No"],fr=["Pc","Pd","Pe","Pf","Pi","Po","Ps"],V={L:{full:"Letter",sub:pr},M:{full:"Mark",sub:cr},N:{full:"Number",sub:dr},P:{full:"Punctuation",sub:fr}},Ye={};for(let e of Object.keys(V))for(let t of V[e].sub)Ye[t]=e;var et={CharacterSet({node:e}){if(e.kind!=="property")return;let t=hr.get(e.value);t&&(e.value=t)}},hr=new Map([["Other","C"],["Control","Cc"],["Format","Cf"],["Unassigned","Cn"],["Private_Use","Co"],["Surrogate","Cs"],["Letter","L"],["Cased_Letter","LC"],["Lowercase_Letter","Ll"],["Modifier_Letter","Lm"],["Other_Letter","Lo"],["Titlecase_Letter","Lt"],["Uppercase_Letter","Lu"],["Mark","M"],["Combining_Mark","M"],["Spacing_Mark","Mc"],["Enclosing_Mark","Me"],["Nonspacing_Mark","Mn"],["Number","N"],["Decimal_Number","Nd"],["Letter_Number","Nl"],["Other_Number","No"],["Punctuation","P"],["punct","P"],["Connector_Punctuation","Pc"],["Dash_Punctuation","Pd"],["Close_Punctuation","Pe"],["Final_Punctuation","Pf"],["Initial_Punctuation","Pi"],["Other_Punctuation","Po"],["Open_Punctuation","Ps"],["Symbol","S"],["Currency_Symbol","Sc"],["Modifier_Symbol","Sk"],["Math_Symbol","Sm"],["Other_Symbol","So"],["Separator","Z"],["Line_Separator","Zl"],["Paragraph_Separator","Zp"],["Space_Separator","Zs"],["ASCII_Hex_Digit","AHex"],["Bidi_Control","Bidi_C"],["Case_Ignorable","CI"],["Changes_When_Casefolded","CWCF"],["Changes_When_Casemapped","CWCM"],["Changes_When_Lowercased","CWL"],["Changes_When_Titlecased","CWT"],["Changes_When_Uppercased","CWU"],["Default_Ignorable_Code_Point","DI"],["Deprecated","Dep"],["Diacritic","Dia"],["Emoji_Component","EComp"],["Emoji_Modifier","EMod"],["Emoji_Modifier_Base","EBase"],["Emoji_Presentation","EPres"],["Extended_Pictographic","ExtPict"],["Extender","Ext"],["Grapheme_Base","Gr_Base"],["Grapheme_Extend","Gr_Ext"],["Grapheme_Link","Gr_Link"],["Hex_Digit","Hex"],["IDS_Binary_Operator","IDSB"],["IDS_Trinary_Operator","IDST"],["IDS_Unary_Operator","IDSU"],["ID_Continue","IDC"],["ID_Start","IDS"],["Ideographic","Ideo"],["Join_Control","Join_C"],["Logical_Order_Exception","LOE"],["Noncharacter_Code_Point","NChar"],["Other_Alphabetic","OAlpha"],["Other_Default_Ignorable_Code_Point","ODI"],["Other_Grapheme_Extend","OGr_Ext"],["Other_ID_Continue","OIDC"],["Other_ID_Start","OIDS"],["Other_Lowercase","OLower"],["Other_Math","OMath"],["Other_Uppercase","OUpper"],["Pattern_Syntax","Pat_Syn"],["Pattern_White_Space","Pat_WS"],["Prepended_Concatenation_Mark","PCM"],["Quotation_Mark","QMark"],["Regional_Indicator","RI"],["Sentence_Terminal","STerm"],["Soft_Dotted","SD"],["Terminal_Punctuation","Term"],["Unified_Ideograph","UIdeo"],["Variation_Selector","VS"],["White_Space","WSpace"],["XID_Continue","XIDC"],["XID_Start","XIDS"]]);var tt={CharacterSet({node:e,root:t,replaceWith:r}){let{kind:a,negate:i,value:o}=e,n=null;a==="posix"&&o==="cntrl"&&!t.flags.posixIsAscii&&(n=J("Cc",{negate:i})),n&&r(n)},CharacterClassRange({node:e,replaceWith:t}){w(e,0,1114111)&&t(J("Any"))}};var te=new Map([["alternationToClass",Oe],["exposeAnchors",Me],["extractPrefix",Ge],["extractPrefix2",Le],["extractSuffix",Fe],["optionalize",Ke],["preventReDoS",Re],["removeEmptyGroups",Be],["removeUselessFlags",ze],["simplifyCallouts",He],["unnestUselessClasses",Qe],["unwrapNegationWrappers",We],["unwrapUselessClasses",Xe],["unwrapUselessGroups",Ze],["useShorthands",qe],["useUnicodeAliases",et],["useUnicodeProps",tt],["mergeRanges",$e]]);function rt(e,t){let r=_r(t),a=M(e,{flags:r.flags,rules:{captureGroup:r.rules.captureGroup,singleline:r.rules.singleline},skipBackrefValidation:r.rules.allowOrphanBackrefs,unicodePropertyMap:G}),i=Object.assign(le(),r.override);for(let l of te.keys())i[l]||delete i[l];let o=Object.keys(i),n={pattern:e,flags:r.flags},s=0;do{if(++s>200)throw new Error("Optimization exceeded maximum iterations; possible infinite loop");e=n.pattern;for(let l of o)Y(a,te.get(l));n=H(a)}while(e!==n.pattern);return n}function _r(e={}){return{flags:"",override:{},...e,rules:{allowOrphanBackrefs:!1,captureGroup:!1,singleline:!1,...e.rules}}}function le(e={}){let t={};for(let r of te.keys())t[r]=!e.disable;return t}return lt(mr);})();
//# sourceMappingURL=oniguruma-parser.min.js.map
