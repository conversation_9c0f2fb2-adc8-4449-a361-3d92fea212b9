"use strict";import{r as n,throwIfNullish as d}from"../utils.js";function h(e){const r=[];let a=e;const t={inCharClass:!1,lastNode:a,parent:e},i=s=>{t.lastNode=a,a=s,b(t.lastNode)===s&&(t.parent=t.lastNode,r.push(t.parent));const u=d(C[s.type],`Unexpected node type "${s.type}"`)(s,t,i);return w(t.parent)===s&&(r.pop(),t.parent=r.at(-1)??e),u};return{pattern:e.body.map(i).join("|"),flags:i(e.flags)}}const C={AbsenceFunction({body:e,kind:r},a,t){if(r!=="repeater")throw new Error(`Unexpected absence function kind "${r}"`);return`(?~${e.map(t).join("|")})`},Alternative({body:e},r,a){return e.map(a).join("")},Assertion({kind:e,negate:r}){return e==="text_segment_boundary"?r?n`\Y`:n`\y`:e==="word_boundary"?r?n`\B`:n`\b`:d({line_end:"$",line_start:"^",search_start:n`\G`,string_end:n`\z`,string_end_newline:n`\Z`,string_start:n`\A`}[e],`Unexpected assertion kind "${e}"`)},Backreference({ref:e}){return typeof e=="number"?"\\"+e:`\\k<${e}>`},CapturingGroup({body:e,name:r},a,t){return`(${r?`?${r.includes(">")?`'${r}'`:`<${r}>`}`:""}${e.map(t).join("|")})`},Character(e,{inCharClass:r,lastNode:a,parent:t}){const{value:i}=e;if(y.has(i))return y.get(i);const s=a.type==="Backreference";if(i<32||i>126&&i<160||i>262143||s&&S(i))return i>127?`\\x{${i.toString(16).toUpperCase()}}`:`\\x${i.toString(16).toUpperCase().padStart(2,"0")}`;const o=String.fromCodePoint(i);let u=!1;if(r){const f=t.type==="CharacterClass",l=f&&t.body[0]===e,c=f&&t.body.at(-1)===e;o==="^"?u=l&&!t.negate:o==="]"?u=!l:o==="-"?u=!l&&!c:u=x.has(o)}else u=N.has(o);return`${u?"\\":""}${o}`},CharacterClass({body:e,kind:r,negate:a},t,i){function s(){return t.parent.type==="CharacterClass"&&t.parent.kind==="intersection"&&r==="union"&&!e.length?"":`[${a?"^":""}${e.map(i).join(r==="intersection"?"&&":"")}]`}if(!t.inCharClass){t.inCharClass=!0;const o=s();return t.inCharClass=!1,o}return s()},CharacterClassRange({min:e,max:r},a,t){return`${t(e)}-${t(r)}`},CharacterSet({kind:e,negate:r,value:a},{inCharClass:t}){switch(e){case"any":return n`\O`;case"digit":return r?n`\D`:n`\d`;case"dot":return".";case"hex":return r?n`\H`:n`\h`;case"newline":return r?n`\N`:n`\R`;case"posix":return t?`[:${r?"^":""}${a}:]`:`${r?n`\P`:n`\p`}{${a}}`;case"property":return`${r?n`\P`:n`\p`}{${a}}`;case"space":return r?n`\S`:n`\s`;case"text_segment":return n`\X`;case"word":return r?n`\W`:n`\w`;default:throw new Error(`Unexpected character set kind "${e}"`)}},Directive({kind:e,flags:r}){if(e==="flags"){const{enable:a={},disable:t={}}=r,i=p(a),s=p(t);return i||s?`(?${i}${s?`-${s}`:""})`:""}if(e==="keep")return n`\K`;throw new Error(`Unexpected directive kind "${e}"`)},Flags(e){return p(e)},Group({atomic:e,body:r,flags:a},t,i){const s=r.map(i).join("|");return`(?${F(e,a)}${s})`},LookaroundAssertion({body:e,kind:r,negate:a},t,i){return`(?${`${r==="lookahead"?"":"<"}${a?"!":"="}`}${e.map(i).join("|")})`},NamedCallout({kind:e,tag:r,arguments:a}){if(e==="custom")throw new Error(`Unexpected named callout kind "${e}"`);return`(*${e.toUpperCase()}${r?`[${r}]`:""}${a?`{${a.join(",")}}`:""})`},Quantifier(e,{parent:r},a){const{body:t,kind:i,max:s,min:o}=e;if(o===1/0)throw new Error("Invalid quantifier: infinite min");if(o>s)throw new Error(`Invalid quantifier: min "${o}" > max "${s}"`);const u=t.type==="Quantifier"&&t.kind==="greedy",f=r.type==="Quantifier"&&r.kind==="possessive"&&r.min===1&&r.max===1/0,l=i==="greedy"&&f;let c;g(e)&&!l&&(!o&&s===1&&!u?c="?":!o&&s===1/0?c="*":o===1&&s===1/0&&(!(u&&g(t))||i==="possessive")&&(c="+"));const $=!c;if($)if(i==="possessive"){if(o===s)throw new Error(`Invalid possessive quantifier: min and max are equal "${o}"`);if(s===1/0)throw new Error(`Invalid possessive quantifier: min "${o}" with infinite max"`);c=`{${s},${o}}`}else o===s?c=`{${o}}`:c=`{${o},${s===1/0?"":s}}`;const m={greedy:"",lazy:"?",possessive:$?"":"+"}[i];return`${a(t)}${c}${m}`},Subroutine({ref:e}){return typeof e=="string"&&e.includes(">")?n`\g'${e}'`:n`\g<${e}>`}},N=new Set(["$","(",")","*","+",".","?","[","\\","^","{","|"]),x=new Set(["&","-","[","\\","]","^"]),y=new Map([[7,n`\a`],[9,n`\t`],[10,n`\n`],[11,n`\v`],[12,n`\f`],[13,n`\r`],[27,n`\e`],[8232,n`\u2028`],[8233,n`\u2029`],[65279,n`\uFEFF`]]);function b(e){return"body"in e?Array.isArray(e.body)?e.body[0]??null:e.body:"min"in e&&e.min.type?e.min:null}function w(e){return"body"in e?Array.isArray(e.body)?e.body.at(-1)??null:e.body:"max"in e&&e.max.type?e.max:null}function p({ignoreCase:e,dotAll:r,extended:a,digitIsAscii:t,posixIsAscii:i,spaceIsAscii:s,wordIsAscii:o,textSegmentMode:u}){return`${e?"i":""}${r?"m":""}${a?"x":""}${t?"D":""}${i?"P":""}${s?"S":""}${o?"W":""}${u?d({grapheme:"y{g}",word:"y{w}"}[u],`Unexpected text segment mode "${u}"`):""}`}function F(e,r){if(e)return">";let a="";if(r){const{enable:t={},disable:i={}}=r,s=p(t),o=p(i);a=`${s}${o?`-${o}`:""}`}return`${a}:`}function S(e){return e>47&&e<58}function g({min:e,max:r}){return!e&&r===1||!e&&r===1/0||e===1&&r===1/0}export{h as generate};
//# sourceMappingURL=generate.js.map
