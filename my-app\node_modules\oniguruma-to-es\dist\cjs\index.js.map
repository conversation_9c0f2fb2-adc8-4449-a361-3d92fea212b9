{"version": 3, "sources": ["../../src/index.js", "../../src/utils.js", "../../src/options.js", "../../node_modules/.pnpm/oniguruma-parser@0.12.1/node_modules/oniguruma-parser/src/utils.ts", "../../node_modules/.pnpm/oniguruma-parser@0.12.1/node_modules/oniguruma-parser/src/tokenizer/tokenize.ts", "../../node_modules/.pnpm/oniguruma-parser@0.12.1/node_modules/oniguruma-parser/src/parser/node-utils.ts", "../../node_modules/.pnpm/oniguruma-parser@0.12.1/node_modules/oniguruma-parser/src/parser/parse.ts", "../../src/unicode.js", "../../node_modules/.pnpm/oniguruma-parser@0.12.1/node_modules/oniguruma-parser/src/traverser/traverse.ts", "../../src/transform.js", "../../src/generate.js", "../../src/subclass.js", "../../node_modules/.pnpm/regex@6.0.1/node_modules/regex/src/utils-internals.js", "../../node_modules/.pnpm/regex-utilities@2.3.0/node_modules/regex-utilities/src/index.js", "../../node_modules/.pnpm/regex@6.0.1/node_modules/regex/src/atomic.js", "../../node_modules/.pnpm/regex-recursion@6.0.2/node_modules/regex-recursion/src/index.js"], "sourcesContent": ["import {transform} from './transform.js';\nimport {generate} from './generate.js';\nimport {Accuracy, getOptions, Target} from './options.js';\nimport {EmulatedRegExp} from './subclass.js';\nimport {JsUnicodePropertyMap} from './unicode.js';\nimport {parse} from 'oniguruma-parser/parser';\nimport {atomic, possessive} from 'regex/internals';\nimport {recursion} from 'regex-recursion';\n/**\n@import {EmulatedRegExpOptions} from './subclass.js';\n*/\n\n// The validation and transformation for Oniguruma's unique syntax and behavior differences\n// compared to native JS RegExp is layered into all steps of the compilation process:\n// 1. Parser: Uses `oniguruma-parser` to build an Oniguruma AST, which accounts for many\n//    differences between Oniguruma and JS.\n// 2. Transformer: Converts the Oniguruma AST to a Regex+ AST that preserves all Oniguruma\n//    behavior. This is true even in cases of non-native-JS features that are supported by both\n//    Regex+ and Oniguruma but with subtly different behavior in each (subroutines, flag x).\n// 3. Generator: Converts the Regex+ AST to a Regex+ pattern, flags, and options.\n// 4. Postprocessing: Regex+ internals and plugins are used to transpile several remaining features\n//    (atomic groups, possessive quantifiers, recursion). Regex+ uses a strict superset of JS\n//    RegExp syntax, so using it allows this library to benefit from not reinventing the wheel for\n//    complex features that Regex+ already knows how to transpile to JS.\n\n/**\n@typedef {{\n  accuracy?: keyof Accuracy;\n  avoidSubclass?: boolean;\n  flags?: string;\n  global?: boolean;\n  hasIndices?: boolean;\n  lazyCompileLength?: number;\n  rules?: {\n    allowOrphanBackrefs?: boolean;\n    asciiWordBoundaries?: boolean;\n    captureGroup?: boolean;\n    recursionLimit?: number;\n    singleline?: boolean;\n  };\n  target?: keyof Target;\n  verbose?: boolean;\n}} ToRegExpOptions\n*/\n\n/**\nAccepts an Oniguruma pattern and returns an equivalent JavaScript `RegExp`.\n@param {string} pattern Oniguruma regex pattern.\n@param {ToRegExpOptions} [options]\n@returns {RegExp | EmulatedRegExp}\n*/\nfunction toRegExp(pattern, options) {\n  const d = toRegExpDetails(pattern, options);\n  if (d.options) {\n    return new EmulatedRegExp(d.pattern, d.flags, d.options);\n  }\n  return new RegExp(d.pattern, d.flags);\n}\n\n/**\nAccepts an Oniguruma pattern and returns the details for an equivalent JavaScript `RegExp`.\n@param {string} pattern Oniguruma regex pattern.\n@param {ToRegExpOptions} [options]\n@returns {{\n  pattern: string;\n  flags: string;\n  options?: EmulatedRegExpOptions;\n}}\n*/\nfunction toRegExpDetails(pattern, options) {\n  const opts = getOptions(options);\n  const onigurumaAst = parse(pattern, {\n    flags: opts.flags,\n    normalizeUnknownPropertyNames: true,\n    rules: {\n      captureGroup: opts.rules.captureGroup,\n      singleline: opts.rules.singleline,\n    },\n    skipBackrefValidation: opts.rules.allowOrphanBackrefs,\n    unicodePropertyMap: JsUnicodePropertyMap,\n  });\n  const regexPlusAst = transform(onigurumaAst, {\n    accuracy: opts.accuracy,\n    asciiWordBoundaries: opts.rules.asciiWordBoundaries,\n    avoidSubclass: opts.avoidSubclass,\n    bestEffortTarget: opts.target,\n  });\n  const generated = generate(regexPlusAst, opts);\n  const recursionResult = recursion(generated.pattern, {\n    captureTransfers: generated._captureTransfers,\n    hiddenCaptures: generated._hiddenCaptures,\n    mode: 'external',\n  });\n  const possessiveResult = possessive(recursionResult.pattern);\n  const atomicResult = atomic(possessiveResult.pattern, {\n    captureTransfers: recursionResult.captureTransfers,\n    hiddenCaptures: recursionResult.hiddenCaptures,\n  });\n  const details = {\n    pattern: atomicResult.pattern,\n    flags: `${opts.hasIndices ? 'd' : ''}${opts.global ? 'g' : ''}${generated.flags}${generated.options.disable.v ? 'u' : 'v'}`,\n  };\n  if (opts.avoidSubclass) {\n    if (opts.lazyCompileLength !== Infinity) {\n      throw new Error('Lazy compilation requires subclass');\n    }\n  } else {\n    // Sort isn't required; only for readability when serialized\n    const hiddenCaptures = atomicResult.hiddenCaptures.sort((a, b) => a - b);\n    // Change the map to the `EmulatedRegExp` format, serializable as JSON\n    const transfers = Array.from(atomicResult.captureTransfers);\n    const strategy = regexPlusAst._strategy;\n    const lazyCompile = details.pattern.length >= opts.lazyCompileLength;\n    if (hiddenCaptures.length || transfers.length || strategy || lazyCompile) {\n      details.options = {\n        ...(hiddenCaptures.length && {hiddenCaptures}),\n        ...(transfers.length && {transfers}),\n        ...(strategy && {strategy}),\n        ...(lazyCompile && {lazyCompile}),\n      };\n    }\n  }\n  return details;\n}\n\n// function toOnigurumaAst(pattern, options) {\n//   return parse(pattern, {\n//     flags: options?.flags ?? '',\n//     normalizeUnknownPropertyNames: true,\n//     rules: options?.rules ?? {},\n//     unicodePropertyMap: JsUnicodePropertyMap,\n//   });\n// }\n\n// function toRegexPlusAst(pattern, options) {\n//   return transform(toOnigurumaAst(pattern, options));\n// }\n\nexport {\n  EmulatedRegExp,\n  toRegExp,\n  toRegExpDetails,\n  // toOnigurumaAst,\n  // toRegexPlusAst,\n};\n", "import {EsVersion, Target} from './options.js';\n\nconst cp = String.fromCodePoint;\nconst r = String.raw;\n\nconst envFlags = {\n  flagGroups: (() => {\n    try {\n      new RegExp('(?i:)');\n    } catch {\n      return false;\n    }\n    return true;\n  })(),\n  unicodeSets: (() => {\n    try {\n      new RegExp('', 'v');\n    } catch {\n      return false;\n    }\n    return true;\n  })(),\n};\n// Detect WebKit bug: <github.com/slevithan/oniguruma-to-es/issues/30>\nenvFlags.bugFlagVLiteralHyphenIsRange = envFlags.unicodeSets ? (() => {\n  try {\n    new RegExp(r`[\\d\\-a]`, 'v');\n  } catch {\n    return true;\n  }\n  return false;\n})() : false;\n// Detect WebKit bug: <github.com/slevithan/oniguruma-to-es/issues/38>\nenvFlags.bugNestedClassIgnoresNegation = envFlags.unicodeSets && new RegExp('[[^a]]', 'v').test('a');\n\nfunction getNewCurrentFlags(current, {enable, disable}) {\n  return {\n    dotAll: !disable?.dotAll && !!(enable?.dotAll || current.dotAll),\n    ignoreCase: !disable?.ignoreCase && !!(enable?.ignoreCase || current.ignoreCase),\n  };\n}\n\nfunction getOrInsert(map, key, defaultValue) {\n  if (!map.has(key)) {\n    map.set(key, defaultValue);\n  }\n  return map.get(key);\n}\n\n/**\n@param {keyof Target} target\n@param {keyof Target} min\n@returns {boolean}\n*/\nfunction isMinTarget(target, min) {\n  return EsVersion[target] >= EsVersion[min];\n}\n\nfunction throwIfNullish(value, msg) {\n  if (value == null) {\n    throw new Error(msg ?? 'Value expected');\n  }\n  return value;\n}\n\nexport {\n  cp,\n  envFlags,\n  getNewCurrentFlags,\n  getOrInsert,\n  isMinTarget,\n  r,\n  throwIfNullish,\n};\n", "import {envFlags} from './utils.js';\n/**\n@import {ToRegExpOptions} from './index.js';\n*/\n\nconst Accuracy = /** @type {const} */ ({\n  default: 'default',\n  strict: 'strict',\n});\n\nconst EsVersion = {\n  ES2025: 2025,\n  ES2024: 2024,\n  ES2018: 2018,\n};\n\nconst Target = /** @type {const} */ ({\n  auto: 'auto',\n  ES2025: 'ES2025',\n  ES2024: 'ES2024',\n  ES2018: 'ES2018',\n});\n\n/**\nReturns a complete set of options, with default values set for options that weren't provided.\n@param {ToRegExpOptions} [options]\n@returns {Required<ToRegExpOptions>}\n*/\nfunction getOptions(options = {}) {\n  if ({}.toString.call(options) !== '[object Object]') {\n    throw new Error('Unexpected options');\n  }\n  if (options.target !== undefined && !Target[options.target]) {\n    throw new Error(`Unexpected target \"${options.target}\"`)\n  }\n  // Set default values\n  const opts = {\n    // Sets the level of emulation rigor/strictness.\n    accuracy: 'default',\n    // Disables advanced emulation that relies on returning a `RegExp` subclass, resulting in\n    // certain patterns not being emulatable.\n    avoidSubclass: false,\n    // Oniguruma flags; a string with `i`, `m`, `x`, `D`, `S`, `W`, `y{g}` in any order (all\n    // optional). Oniguruma's `m` is equivalent to JavaScript's `s` (`dotAll`).\n    flags: '',\n    // Include JavaScript flag `g` (`global`) in the result.\n    global: false,\n    // Include JavaScript flag `d` (`hasIndices`) in the result.\n    hasIndices: false,\n    // Delay regex construction until first use if the transpiled pattern is at least this length.\n    lazyCompileLength: Infinity,\n    // JavaScript version used for generated regexes. Using `auto` detects the best value based on\n    // your environment. Later targets allow faster processing, simpler generated source, and\n    // support for additional features.\n    target: 'auto',\n    // Disables minifications that simplify the pattern without changing the meaning.\n    verbose: false,\n    ...options,\n    // Advanced options that override standard behavior, error checking, and flags when enabled.\n    rules: {\n      // Useful with TextMate grammars that merge backreferences across patterns.\n      allowOrphanBackrefs: false,\n      // Use ASCII `\\b` and `\\B`, which increases search performance of generated regexes.\n      asciiWordBoundaries: false,\n      // Allow unnamed captures and numbered calls (backreferences and subroutines) when using\n      // named capture. This is Oniguruma option `ONIG_OPTION_CAPTURE_GROUP`; on by default in\n      // `vscode-oniguruma`.\n      captureGroup: false,\n      // Change the recursion depth limit from Oniguruma's `20` to an integer `2`–`20`.\n      recursionLimit: 20,\n      // `^` as `\\A`; `$` as`\\Z`. Improves search performance of generated regexes without changing\n      // the meaning if searching line by line. This is Oniguruma option `ONIG_OPTION_SINGLELINE`.\n      singleline: false,\n      ...options.rules,\n    },\n  };\n  if (opts.target === 'auto') {\n    opts.target = envFlags.flagGroups ? 'ES2025' : (envFlags.unicodeSets ? 'ES2024' : 'ES2018');\n  }\n  return opts;\n}\n\nexport {\n  Accuracy,\n  EsVersion,\n  getOptions,\n  Target,\n};\n", "function cpOf(char: string): number {\n  // Count code point length\n  if ([...char].length !== 1) {\n    throw new Error(`Expected \"${char}\" to be a single code point`);\n  }\n  return char.codePointAt(0)!;\n}\n\nfunction getOrInsert<Key, Value>(map: Map<Key, Value>, key: Key, defaultValue: Value): Value {\n  if (!map.has(key)) {\n    map.set(key, defaultValue);\n  }\n  return map.get(key)!;\n}\n\nconst PosixClassNames = new Set([\n  'alnum',\n  'alpha',\n  'ascii',\n  'blank',\n  'cntrl',\n  'digit',\n  'graph',\n  'lower',\n  'print',\n  'punct',\n  'space',\n  'upper',\n  'word',\n  'xdigit',\n]);\n\nconst r = String.raw;\n\nfunction throwIfNullish<Value>(value: Value, msg?: string): NonNullable<Value> {\n  if (value == null) {\n    throw new Error(msg ?? 'Value expected');\n  }\n  return value;\n}\n\nexport {\n  cpOf,\n  getOrInsert,\n  PosixClassNames,\n  r,\n  throwIfNullish,\n};\n", "import {cpOf, PosixClassNames, r, throwIfNullish} from '../utils.js';\n\ntype Token =\n  AlternatorToken |\n  AssertionToken |\n  BackreferenceToken |\n  CharacterToken |\n  CharacterClassCloseToken |\n  CharacterClassHyphenToken |\n  CharacterClassIntersectorToken |\n  CharacterClassOpenToken |\n  CharacterSetToken |\n  DirectiveToken |\n  GroupCloseToken |\n  GroupOpenToken |\n  NamedCalloutToken |\n  QuantifierToken |\n  SubroutineToken;\n\ntype IntermediateToken =\n  EscapedNumberToken;\n\ntype TokenCharacterSetKind =\n  'any' |\n  'digit' |\n  'dot' |\n  'hex' |\n  'newline' |\n  'posix' |\n  'property' |\n  'space' |\n  'text_segment' |\n  'word';\n\ntype TokenDirectiveKind =\n  'flags' |\n  'keep';\n\ntype TokenGroupOpenKind =\n  'absence_repeater' |\n  'atomic' |\n  'capturing' |\n  'group' |\n  'lookahead' |\n  'lookbehind';\n\ntype TokenQuantifierKind =\n  'greedy' |\n  'lazy' |\n  'possessive';\n\ntype TokenNamedCalloutKind =\n  'count' |\n  'cmp' |\n  'error' |\n  'fail' |\n  'max' |\n  'mismatch' |\n  'skip' |\n  'total_count' |\n  'custom';\n\nconst charClassOpenPattern = r`\\[\\^?`;\nconst sharedEscapesPattern = `${\n  // Control char\n  'c.? | C(?:-.?)?'\n}|${\n  // Unicode property; Onig considers `\\p` an identity escape, but e.g. `\\p{`, `\\p{ ^L}`, and\n  // `\\p{gc=L}` are invalid\n  r`[pP]\\{(?:\\^?[-\\x20_]*[A-Za-z][-\\x20\\w]*\\})?`\n}|${\n  // Hex encoded byte sequence; attempt match before other `\\xNN` hex char\n  r`x[89A-Fa-f]\\p{AHex}(?:\\\\x[89A-Fa-f]\\p{AHex})*`\n}|${\n  // Hex char\n  r`u(?:\\p{AHex}{4})? | x\\{[^\\}]*\\}? | x\\p{AHex}{0,2}`\n}|${\n  // Enclosed octal code point\n  r`o\\{[^\\}]*\\}?`\n}|${\n  // Escaped number\n  r`\\d{1,3}`\n}`;\n// Even with flag x, Onig doesn't allow whitespace to separate a quantifier from the `?` or `+`\n// that makes it lazy or possessive. Possessive suffixes don't apply to interval quantifiers\nconst quantifierRe = /[?*+][?+]?|\\{(?:\\d+(?:,\\d*)?|,\\d+)\\}\\??/;\nconst tokenRe = new RegExp(r`\n  \\\\ (?:\n    ${sharedEscapesPattern}\n    | [gk]<[^>]*>?\n    | [gk]'[^']*'?\n    | .\n  )\n  | \\( (?:\n    \\? (?:\n      [:=!>({]\n      | <[=!]\n      | <[^>]*>\n      | '[^']*'\n      | ~\\|?\n      | #(?:[^)\\\\]|\\\\.?)*\n      | [^:)]*[:)]\n    )?\n    | \\*[^\\)]*\\)?\n  )?\n  | (?:${quantifierRe.source})+\n  | ${charClassOpenPattern}\n  | .\n`.replace(/\\s+/g, ''), 'gsu');\nconst charClassTokenRe = new RegExp(r`\n  \\\\ (?:\n    ${sharedEscapesPattern}\n    | .\n  )\n  | \\[:(?:\\^?\\p{Alpha}+|\\^):\\]\n  | ${charClassOpenPattern}\n  | &&\n  | .\n`.replace(/\\s+/g, ''), 'gsu');\n\ntype Context = {\n  captureGroup: boolean;\n  getCurrentModX(): boolean;\n  numOpenGroups: number;\n  popModX(): void;\n  pushModX(isXOn: boolean): void;\n  replaceCurrentModX(isXOn: boolean): void;\n  singleline: boolean;\n};\n\ntype TokenizeOptions = {\n  flags?: string;\n  rules?: {\n    captureGroup?: boolean;\n    singleline?: boolean;\n  };\n};\n\nfunction tokenize(pattern: string, options: TokenizeOptions = {}): {\n  tokens: Array<Token>;\n  flags: FlagProperties;\n} {\n  const opts = {\n    flags: '',\n    ...options,\n    rules: {\n      captureGroup: false, // `ONIG_OPTION_CAPTURE_GROUP`\n      singleline: false, // `ONIG_OPTION_SINGLELINE`\n      ...options.rules,\n    },\n  };\n  if (typeof pattern !== 'string') {\n    throw new Error('String expected as pattern');\n  }\n  const flagProperties = getFlagProperties(opts.flags);\n  const xStack = [flagProperties.extended];\n  const context: Context = {\n    captureGroup: opts.rules.captureGroup,\n    // Always at least has the top-level flag x\n    getCurrentModX() {return xStack.at(-1)!},\n    numOpenGroups: 0,\n    popModX() {xStack.pop()},\n    pushModX(isXOn) {xStack.push(isXOn)},\n    replaceCurrentModX(isXOn) {xStack[xStack.length - 1] = isXOn},\n    singleline: opts.rules.singleline,\n  };\n  let tokens: Array<Token | IntermediateToken> = [];\n  let match: RegExpExecArray | null;\n  tokenRe.lastIndex = 0;\n  while ((match = tokenRe.exec(pattern))) {\n    const result = getTokenWithDetails(context, pattern, match[0], tokenRe.lastIndex);\n    if (result.tokens) {\n      tokens.push(...result.tokens);\n    } else if (result.token) {\n      tokens.push(result.token);\n    }\n    if (result.lastIndex !== undefined) {\n      tokenRe.lastIndex = result.lastIndex;\n    }\n  }\n\n  const potentialUnnamedCaptureTokens: Array<GroupOpenToken> = [];\n  let numNamedAndOptInUnnamedCaptures = 0;\n  tokens.filter(t => t.type === 'GroupOpen').forEach(t => {\n    if (t.kind === 'capturing') {\n      t.number = ++numNamedAndOptInUnnamedCaptures;\n    } else if (t.raw === '(') {\n      potentialUnnamedCaptureTokens.push(t);\n    }\n  });\n  // Enable unnamed capturing groups if no named captures (when `captureGroup` not enabled)\n  if (!numNamedAndOptInUnnamedCaptures) {\n    potentialUnnamedCaptureTokens.forEach((t, i) => {\n      t.kind = 'capturing';\n      t.number = i + 1;\n    });\n  }\n  const numCaptures = numNamedAndOptInUnnamedCaptures || potentialUnnamedCaptureTokens.length;\n  // Can now split escaped nums accurately, accounting for number of captures\n  const tokensWithoutIntermediate = tokens.map(\n    t => t.type === 'EscapedNumber' ? splitEscapedNumberToken(t, numCaptures) : t\n  ).flat();\n\n  return {\n    tokens: tokensWithoutIntermediate,\n    flags: flagProperties,\n  };\n}\n\nfunction getTokenWithDetails(context: Context, pattern: string, m: string, lastIndex: number): {\n  token: Token | IntermediateToken;\n  tokens?: never;\n  lastIndex?: number;\n} | {\n  token?: never;\n  tokens: Array<Token | IntermediateToken>;\n  lastIndex?: number;\n} | {\n  token?: never;\n  tokens?: never;\n  lastIndex: number;\n} {\n  const [m0, m1] = m;\n\n  if (m === '[' || m === '[^') {\n    const result = getAllTokensForCharClass(pattern, m, lastIndex);\n    return {\n      // Array of all of the char class's tokens\n      tokens: result.tokens,\n      // Jump forward to the end of the char class\n      lastIndex: result.lastIndex,\n    };\n  }\n\n  if (m0 === '\\\\') {\n    if ('AbBGyYzZ'.includes(m1)) {\n      return {\n        token: createAssertionToken(m, m),\n      };\n    }\n    if (/^\\\\g[<']/.test(m)) {\n      if (!/^\\\\g(?:<[^>]+>|'[^']+')$/.test(m)) {\n        throw new Error(`Invalid group name \"${m}\"`);\n      }\n      return {\n        token: createSubroutineToken(m),\n      };\n    }\n    if (/^\\\\k[<']/.test(m)) {\n      if (!/^\\\\k(?:<[^>]+>|'[^']+')$/.test(m)) {\n        throw new Error(`Invalid group name \"${m}\"`);\n      }\n      return {\n        token: createBackreferenceToken(m),\n      };\n    }\n    if (m1 === 'K') {\n      return {\n        token: createDirectiveToken('keep', m),\n      };\n    }\n    if (m1 === 'N' || m1 === 'R') {\n      return {\n        token: createCharacterSetToken('newline', m, {\n          // `\\N` and `\\R` are not actually opposites since the former only excludes `\\n`\n          negate: m1 === 'N',\n        }),\n      };\n    }\n    if (m1 === 'O') {\n      return {\n        token: createCharacterSetToken('any', m),\n      };\n    }\n    if (m1 === 'X') {\n      return {\n        token: createCharacterSetToken('text_segment', m),\n      };\n    }\n    // Run last since it assumes an identity escape as final condition\n    const result = tokenizeSharedEscape(m, {inCharClass: false});\n    return Array.isArray(result) ? {tokens: result} : {token: result};\n  }\n\n  if (m0 === '(') {\n    if (m1 === '*') {\n      return {\n        token: tokenizeNamedCallout(m),\n      };\n    }\n    if (m === '(?{') {\n      throw new Error(`Unsupported callout \"${m}\"`);\n    }\n    // Comment group\n    if (m.startsWith('(?#')) {\n      // Everything except the closing unescaped `)` is included in the match\n      if (pattern[lastIndex] !== ')') {\n        throw new Error('Unclosed comment group \"(?#\"');\n      }\n      return {\n        // Jump forward to after the closing paren\n        lastIndex: lastIndex + 1,\n      };\n    }\n    // Flag modifier (directive or group opener)\n    if (/^\\(\\?[-imx]+[:)]$/.test(m)) {\n      return {\n        token: tokenizeFlagModifier(m, context),\n      };\n    }\n    // --- Remaining group types all reuse current flag x status ---\n    context.pushModX(context.getCurrentModX());\n    context.numOpenGroups++;\n    if (\n      // Unnamed capture if no named captures present and `captureGroup` not enabled, else\n      // noncapturing group\n      (m === '(' && !context.captureGroup) ||\n      // Noncapturing group\n      m === '(?:'\n    ) {\n      return {\n        // For `(`, will later change to `capturing` and add `number` prop if no named captures\n        token: createGroupOpenToken('group', m),\n      };\n    }\n    // Atomic group\n    if (m === '(?>') {\n      return {\n        token: createGroupOpenToken('atomic', m),\n      };\n    }\n    // Lookaround\n    if (m === '(?=' || m === '(?!' || m === '(?<=' || m === '(?<!') {\n      return {\n        token: createGroupOpenToken(m[2] === '<' ? 'lookbehind' : 'lookahead', m, {\n          negate: m.endsWith('!'),\n        }),\n      };\n    }\n    // Unnamed capture when `captureGroup` enabled, or named capture (checked after lookbehind due\n    // to similar syntax)\n    if (\n      (m === '(' && context.captureGroup) ||\n      (m.startsWith('(?<') && m.endsWith('>')) ||\n      (m.startsWith(\"(?'\") && m.endsWith(\"'\"))\n    ) {\n      return {\n        token: createGroupOpenToken('capturing', m, {\n          // Will add `number` prop in a second pass\n          ...(m !== '(' && {name: m.slice(3, -1)}),\n        }),\n      };\n    }\n    if (m.startsWith('(?~')) {\n      if (m === '(?~|') {\n        throw new Error(`Unsupported absence function kind \"${m}\"`);\n      }\n      return {\n        token: createGroupOpenToken('absence_repeater', m),\n      };\n    }\n    if (m === '(?(') {\n      // TODO: Add support\n      throw new Error(`Unsupported conditional \"${m}\"`);\n    }\n    throw new Error(`Invalid or unsupported group option \"${m}\"`);\n  }\n  if (m === ')') {\n    context.popModX();\n    context.numOpenGroups--;\n    if (context.numOpenGroups < 0) {\n      throw new Error('Unmatched \")\"');\n    }\n    return {\n      token: createGroupCloseToken(m),\n    };\n  }\n\n  if (context.getCurrentModX()) {\n    if (m === '#') {\n      // Onig's only line break char is line feed\n      const end = pattern.indexOf('\\n', lastIndex);\n      return {\n        // Jump forward to the end of the comment\n        lastIndex: end === -1 ? pattern.length : end,\n      };\n    }\n    if (/^\\s$/.test(m)) {\n      const re = /\\s+/y;\n      re.lastIndex = lastIndex;\n      const rest = re.exec(pattern);\n      return {\n        // Jump forward to the end of the whitespace\n        lastIndex: rest ? re.lastIndex : lastIndex,\n      };\n    }\n  }\n\n  if (m === '.') {\n    return {\n      token: createCharacterSetToken('dot', m),\n    };\n  }\n\n  if (m === '^' || m === '$') {\n    const kind = context.singleline ? {\n      '^': r`\\A`,\n      '$': r`\\Z`,\n    }[m] : m;\n    return {\n      token: createAssertionToken(kind, m),\n    };\n  }\n\n  if (m === '|') {\n    return {\n      token: createAlternatorToken(m),\n    };\n  }\n\n  if (quantifierRe.test(m)) {\n    return {\n      tokens: splitQuantifierMatch(m),\n    };\n  }\n\n  // `cpOf` asserts that it's a single code point\n  return {\n    token: createCharacterToken(cpOf(m), m),\n  };\n}\n\nfunction getAllTokensForCharClass(pattern: string, opener: CharacterClassOpener, lastIndex: number): {\n  tokens: Array<Token | IntermediateToken>;\n  lastIndex: number;\n} {\n  const tokens: Array<Token | IntermediateToken> = [createCharacterClassOpenToken(opener[1] === '^', opener)];\n  let numCharClassesOpen = 1;\n  let match: RegExpExecArray | null;\n  charClassTokenRe.lastIndex = lastIndex;\n  while ((match = charClassTokenRe.exec(pattern))) {\n    const m = match[0];\n    // Start of nested char class\n    // POSIX classes are handled as a single token; not as a nested char class\n    if (m[0] === '[' && m[1] !== ':') {\n      numCharClassesOpen++;\n      tokens.push(createCharacterClassOpenToken(m[1] === '^', m as CharacterClassOpener));\n    } else if (m === ']') {\n      // Always at least includes the char class opener\n      if (tokens.at(-1)!.type === 'CharacterClassOpen') {\n        // Allow unescaped `]` as leading char\n        tokens.push(createCharacterToken(93, m));\n      } else {\n        numCharClassesOpen--;\n        tokens.push(createCharacterClassCloseToken(m));\n        if (!numCharClassesOpen) {\n          break;\n        }\n      }\n    } else {\n      const result = tokenizeAnyTokenWithinCharClass(m);\n      if (Array.isArray(result)) {\n        tokens.push(...result);\n      } else {\n        tokens.push(result);\n      }\n    }\n  }\n  return {\n    tokens,\n    lastIndex: charClassTokenRe.lastIndex || pattern.length,\n  };\n}\n\nfunction tokenizeAnyTokenWithinCharClass(raw: string): Token | IntermediateToken | Array<Token> {\n  if (raw[0] === '\\\\') {\n    // Assumes an identity escape as final condition\n    return tokenizeSharedEscape(raw, {inCharClass: true});\n  }\n  // POSIX class: `[:name:]` or `[:^name:]`\n  if (raw[0] === '[') {\n    const posix = /\\[:(?<negate>\\^?)(?<name>[a-z]+):\\]/.exec(raw);\n    if (!posix || !PosixClassNames.has(posix.groups!.name)) {\n      throw new Error(`Invalid POSIX class \"${raw}\"`);\n    }\n    return createCharacterSetToken('posix', raw, {\n      value: posix.groups!.name,\n      negate: !!posix.groups!.negate,\n    });\n  }\n  // Range (possibly invalid) or literal hyphen\n  if (raw === '-') {\n    return createCharacterClassHyphenToken(raw);\n  }\n  if (raw === '&&') {\n    return createCharacterClassIntersectorToken(raw);\n  }\n  // `cpOf` asserts that it's a single code point\n  return createCharacterToken(cpOf(raw), raw);\n}\n\n// Tokens shared by base syntax and char class syntax that start with `\\`\nfunction tokenizeSharedEscape(raw: string, {inCharClass}: {inCharClass: boolean}): Token | IntermediateToken | Array<Token> {\n  const char1 = raw[1];\n  if (char1 === 'c' || char1 === 'C') {\n    return tokenizeControlCharacter(raw);\n  }\n  if ('dDhHsSwW'.includes(char1)) {\n    return tokenizeShorthand(raw);\n  }\n  if (raw.startsWith(r`\\o{`)) {\n    throw new Error(`Incomplete, invalid, or unsupported octal code point \"${raw}\"`);\n  }\n  if (/^\\\\[pP]\\{/.test(raw)) {\n    if (raw.length === 3) {\n      throw new Error(`Incomplete or invalid Unicode property \"${raw}\"`);\n    }\n    return tokenizeUnicodeProperty(raw);\n  }\n  // Hex UTF-8 encoded byte sequence\n  if (/^\\\\x[89A-Fa-f]\\p{AHex}/u.test(raw)) {\n    try {\n      const bytes = raw.split(/\\\\x/).slice(1).map(hex => parseInt(hex, 16));\n      const decoded = new TextDecoder('utf-8', {\n        ignoreBOM: true,\n        fatal: true,\n      }).decode(new Uint8Array(bytes));\n      const encoder = new TextEncoder();\n      const tokens = [...decoded].map(char => {\n        // Since this regenerates `raw`, it might have different casing for hex A-F than the input\n        const raw = [...encoder.encode(char)].map(byte => `\\\\x${byte.toString(16)}`).join('');\n        return createCharacterToken(cpOf(char), raw);\n      });\n      return tokens;\n    } catch {\n      throw new Error(`Multibyte code \"${raw}\" incomplete or invalid in Oniguruma`);\n    }\n  }\n  if (char1 === 'u' || char1 === 'x') {\n    return createCharacterToken(getValidatedHexCharCode(raw), raw);\n  }\n  if (EscapeCharCodes.has(char1)) {\n    return createCharacterToken(EscapeCharCodes.get(char1)!, raw);\n  }\n  // Escaped number: backref (possibly invalid), null, octal, or identity escape, possibly followed\n  // by 1-2 literal digits\n  if (/\\d/.test(char1)) {\n    return createEscapedNumberToken(inCharClass, raw);\n  }\n  if (raw === '\\\\') {\n    throw new Error(r`Incomplete escape \"\\\"`);\n  }\n  // Meta `\\M-x` and `\\M-\\C-x` are unsupported; avoid treating as an identity escape\n  if (char1 === 'M') {\n    // TODO: Add support. See:\n    // - <github.com/kkos/oniguruma/blob/master/doc/SYNTAX.md#12-onig_syn_op2_esc_capital_m_bar_meta-enable-m-x>\n    // - <github.com/kkos/oniguruma/blob/43a8c3f3daf263091f3a74019d4b32ebb6417093/src/regparse.c#L4695>\n    // - <github.com/ammar/regexp_parser/blob/8851030feda68223d74f502335fb254a20d77016/lib/regexp_parser/expression/classes/escape_sequence.rb#L75>\n    throw new Error(`Unsupported meta \"${raw}\"`);\n  }\n  // Identity escape; count code point length\n  if ([...raw].length === 2) {\n    return createCharacterToken(raw.codePointAt(1)!, raw);\n  }\n  throw new Error(`Unexpected escape \"${raw}\"`);\n}\n\n// --------------------------------\n// --- Token creation and types ---\n// --------------------------------\n\ntype AlternatorToken = {\n  type: 'Alternator';\n  raw: '|';\n};\nfunction createAlternatorToken(raw: '|'): AlternatorToken {\n  return {\n    type: 'Alternator',\n    raw,\n  };\n}\n\ntype AssertionToken = {\n  type: 'Assertion';\n  kind: string;\n  raw: string;\n};\nfunction createAssertionToken(kind: string, raw: string): AssertionToken {\n  return {\n    type: 'Assertion',\n    kind,\n    raw,\n  };\n}\n\ntype BackreferenceToken = {\n  type: 'Backreference';\n  raw: string;\n};\nfunction createBackreferenceToken(raw: string): BackreferenceToken {\n  return {\n    type: 'Backreference',\n    raw,\n  };\n}\n\ntype CharacterToken = {\n  type: 'Character';\n  value: number;\n  raw: string;\n};\nfunction createCharacterToken(value: number, raw: string): CharacterToken {\n  return {\n    type: 'Character',\n    value,\n    raw,\n  };\n}\n\ntype CharacterClassCloseToken = {\n  type: 'CharacterClassClose';\n  raw: ']';\n};\nfunction createCharacterClassCloseToken(raw: ']'): CharacterClassCloseToken {\n  return {\n    type: 'CharacterClassClose',\n    raw,\n  };\n}\n\ntype CharacterClassHyphenToken = {\n  type: 'CharacterClassHyphen';\n  raw: '-';\n};\nfunction createCharacterClassHyphenToken(raw: '-'): CharacterClassHyphenToken {\n  return {\n    type: 'CharacterClassHyphen',\n    raw,\n  };\n}\n\ntype CharacterClassIntersectorToken = {\n  type: 'CharacterClassIntersector';\n  raw: '&&';\n};\nfunction createCharacterClassIntersectorToken(raw: '&&'): CharacterClassIntersectorToken {\n  return {\n    type: 'CharacterClassIntersector',\n    raw,\n  };\n}\n\ntype CharacterClassOpenToken = {\n  type: 'CharacterClassOpen';\n  negate: boolean;\n  raw: CharacterClassOpener;\n};\ntype CharacterClassOpener = '[' | '[^';\nfunction createCharacterClassOpenToken(negate: boolean, raw: CharacterClassOpener): CharacterClassOpenToken {\n  return {\n    type: 'CharacterClassOpen',\n    negate,\n    raw,\n  };\n}\n\ntype CharacterSetToken = {\n  type: 'CharacterSet';\n  kind: TokenCharacterSetKind;\n  value?: string;\n  negate?: boolean;\n  raw: string;\n};\nfunction createCharacterSetToken(\n  kind: TokenCharacterSetKind,\n  raw: string,\n  options: {\n    value?: string;\n    negate?: boolean;\n  } = {}\n): CharacterSetToken {\n  return {\n    type: 'CharacterSet',\n    kind,\n    ...options,\n    raw,\n  };\n}\n\ntype DirectiveToken = {\n  type: 'Directive';\n  raw: string;\n} & ({\n  kind: 'keep';\n  flags?: never;\n} | {\n  kind: 'flags';\n  flags: FlagGroupModifiers;\n});\nfunction createDirectiveToken(kind: TokenDirectiveKind, raw: string, options: {flags?: FlagGroupModifiers} = {}): DirectiveToken {\n  if (kind === 'keep') {\n    return {\n      type: 'Directive',\n      kind,\n      raw,\n    };\n  }\n  return {\n    type: 'Directive',\n    kind,\n    flags: throwIfNullish(options.flags),\n    raw,\n  };\n}\n\ntype EscapedNumberToken = {\n  type: 'EscapedNumber';\n  inCharClass: boolean;\n  raw: string;\n};\n/**\nIntermediate representation only; will become a `Backreference` or one or more `Character`s.\n*/\nfunction createEscapedNumberToken(inCharClass: boolean, raw: string): EscapedNumberToken {\n  return {\n    type: 'EscapedNumber',\n    inCharClass,\n    raw,\n  };\n}\n\ntype GroupCloseToken = {\n  type: 'GroupClose';\n  raw: ')';\n};\nfunction createGroupCloseToken(raw: ')'): GroupCloseToken {\n  return {\n    type: 'GroupClose',\n    raw,\n  };\n}\n\ntype GroupOpenToken = {\n  type: 'GroupOpen';\n  kind: TokenGroupOpenKind;\n  flags?: FlagGroupModifiers;\n  name?: string;\n  number?: number;\n  negate?: boolean;\n  raw: string;\n};\nfunction createGroupOpenToken(\n  kind: TokenGroupOpenKind,\n  raw: string,\n  options: {\n    flags?: FlagGroupModifiers;\n    name?: string;\n    number?: number;\n    negate?: boolean;\n  } = {}\n): GroupOpenToken {\n  return {\n    type: 'GroupOpen',\n    kind,\n    ...options,\n    raw,\n  };\n}\n\ntype NamedCalloutToken = {\n  type: 'NamedCallout';\n  kind: TokenNamedCalloutKind;\n  tag: string | null;\n  arguments: Array<string | number> | null;\n  raw: string;\n};\nfunction createNamedCalloutToken(\n  kind: TokenNamedCalloutKind,\n  tag: string | null,\n  args: Array<string | number> | null,\n  raw: string\n): NamedCalloutToken {\n  return {\n    type: 'NamedCallout',\n    kind,\n    tag,\n    arguments: args,\n    raw,\n  };\n};\n\ntype QuantifierToken = {\n  type: 'Quantifier';\n  kind: TokenQuantifierKind;\n  min: number;\n  max: number;\n  raw: string;\n};\nfunction createQuantifierToken(\n  kind: TokenQuantifierKind,\n  min: number,\n  max: number,\n  raw: string\n): QuantifierToken {\n  return {\n    type: 'Quantifier',\n    kind,\n    min,\n    max,\n    raw,\n  };\n}\n\ntype SubroutineToken = {\n  type: 'Subroutine';\n  raw: string;\n};\nfunction createSubroutineToken(raw: string): SubroutineToken {\n  return {\n    type: 'Subroutine',\n    raw,\n  };\n}\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\ntype FlagProperties = {\n  ignoreCase: boolean;\n  dotAll: boolean;\n  extended: boolean;\n  digitIsAscii: boolean;\n  posixIsAscii: boolean;\n  spaceIsAscii: boolean;\n  wordIsAscii: boolean;\n  textSegmentMode: 'grapheme' | 'word' | null;\n};\n\ntype FlagGroupModifiers = {\n  enable?: FlagGroupSwitches;\n  disable?: FlagGroupSwitches;\n};\n\ntype FlagGroupSwitches = {\n  ignoreCase?: true;\n  dotAll?: true;\n  extended?: true;\n};\n\nconst CalloutNames = new Set<Uppercase<Exclude<TokenNamedCalloutKind, 'custom'>>>([\n  'COUNT',\n  'CMP',\n  'ERROR',\n  'FAIL',\n  'MAX',\n  'MISMATCH',\n  'SKIP',\n  'TOTAL_COUNT',\n]);\n\nconst EscapeCharCodes = new Map([\n  ['a',  7], // alert/bell (Not available in JS)\n  ['b',  8], // backspace (only in char classes)\n  ['e', 27], // escape (Not available in JS)\n  ['f', 12], // form feed\n  ['n', 10], // line feed\n  ['r', 13], // carriage return\n  ['t',  9], // horizontal tab\n  ['v', 11], // vertical tab\n]);\n\n// Expects `\\cx` or `\\C-x`\nfunction tokenizeControlCharacter(raw: string): CharacterToken {\n  const char = raw[1] === 'c' ? raw[2] : raw[3];\n  if (!char || !/[A-Za-z]/.test(char)) {\n    // Unlike JS, Onig allows any char to follow `\\c` or `\\C-`, but this is an extreme edge case\n    // TODO: Add support. See:\n    // - <github.com/kkos/oniguruma/blob/master/doc/SYNTAX.md#11-onig_syn_op2_esc_capital_c_bar_control-enable-c-x>\n    // - <github.com/kkos/oniguruma/blob/43a8c3f3daf263091f3a74019d4b32ebb6417093/src/regparse.c#L4695>\n    throw new Error(`Unsupported control character \"${raw}\"`);\n  }\n  return createCharacterToken(cpOf(char.toUpperCase()) - 64, raw);\n}\n\nfunction tokenizeFlagModifier(raw: string, context: Context): DirectiveToken | GroupOpenToken {\n  // Allows multiple `-` and solo `-` without `on` or `off` flags\n  let {on, off} = /^\\(\\?(?<on>[imx]*)(?:-(?<off>[-imx]*))?/.exec(raw)!.groups as {on: string, off: string | undefined};\n  off ??= '';\n  // Flag x is used directly by the tokenizer since it changes how to interpret the pattern\n  const isXOn = (context.getCurrentModX() || on.includes('x')) && !off.includes('x');\n  const enabledFlags = getFlagGroupSwitches(on);\n  const disabledFlags = getFlagGroupSwitches(off);\n  const flagChanges: FlagGroupModifiers = {};\n  enabledFlags && (flagChanges.enable = enabledFlags);\n  disabledFlags && (flagChanges.disable = disabledFlags);\n  // Flag directive; ex: `(?im-x)`\n  if (raw.endsWith(')')) {\n    // Replace flag x value until the end of the current group\n    context.replaceCurrentModX(isXOn);\n    // Can't remove flag directives without flags like `(?-)`; they affect following quantifiers\n    return createDirectiveToken('flags', raw, {\n      flags: flagChanges,\n    });\n  }\n  // Flag group opener; ex: `(?im-x:`\n  if (raw.endsWith(':')) {\n    context.pushModX(isXOn);\n    context.numOpenGroups++;\n    return createGroupOpenToken('group', raw, {\n      ...((enabledFlags || disabledFlags) && {flags: flagChanges}),\n    });\n  }\n  throw new Error(`Unexpected flag modifier \"${raw}\"`);\n}\n\nfunction tokenizeNamedCallout(raw: string): NamedCalloutToken {\n  const callout = /\\(\\*(?<name>[A-Za-z_]\\w*)?(?:\\[(?<tag>(?:[A-Za-z_]\\w*)?)\\])?(?:\\{(?<args>[^}]*)\\})?\\)/.exec(raw);\n  if (!callout) {\n    throw new Error(`Incomplete or invalid named callout \"${raw}\"`);\n  }\n  const {name, tag, args} = callout.groups as Partial<{\n    name: string;\n    tag: string;\n    args: string;\n  }>;\n  if (!name) {\n    throw new Error(`Invalid named callout \"${raw}\"`);\n  }\n  if (tag === '') {\n    throw new Error(`Named callout tag with empty value not allowed \"${raw}\"`);\n  }\n  const argsArray: Array<string | number> = args ?\n    args.split(',').\n      // Onig skips over/ignores redundant/unnecessary commas\n      filter(arg => arg !== '').\n      map(arg => /^[+-]?\\d+$/.test(arg) ? +arg : arg) :\n    [];\n  const [arg0, arg1, arg2] = argsArray;\n  const kind: TokenNamedCalloutKind = CalloutNames.has(name as Uppercase<Exclude<TokenNamedCalloutKind, 'custom'>>) ?\n    name.toLowerCase() as TokenNamedCalloutKind :\n    'custom';\n  switch (kind) {\n    case 'fail':\n    case 'mismatch':\n    case 'skip':\n      if (argsArray.length > 0) {\n        throw new Error(`Named callout arguments not allowed \"${argsArray}\"`);\n      }\n      break;\n    case 'error':\n      if (argsArray.length > 1) {\n        throw new Error(`Named callout allows only one argument \"${argsArray}\"`);\n      }\n      if (typeof arg0 === 'string') {\n        throw new Error(`Named callout argument must be a number \"${arg0}\"`);\n      }\n      break;\n    case 'max':\n      if (!argsArray.length || argsArray.length > 2) {\n        throw new Error(`Named callout must have one or two arguments \"${argsArray}\"`);\n      }\n      if (typeof arg0 === 'string' && !/^[A-Za-z_]\\w*$/.test(arg0)) {\n        throw new Error(`Named callout argument one must be a tag or number \"${arg0}\"`);\n      }\n      if (argsArray.length === 2 && (typeof arg1 === 'number' || !/^[<>X]$/.test(arg1))) {\n        throw new Error(`Named callout optional argument two must be '<', '>', or 'X' \"${arg1}\"`);\n      }\n      break;\n    case 'count':\n    case 'total_count':\n      if (argsArray.length > 1) {\n        throw new Error(`Named callout allows only one argument \"${argsArray}\"`);\n      }\n      if (argsArray.length === 1 && (typeof arg0 === 'number' || !/^[<>X]$/.test(arg0))) {\n        throw new Error(`Named callout optional argument must be '<', '>', or 'X' \"${arg0}\"`);\n      }\n      break;\n    case 'cmp':\n      if (argsArray.length !== 3) {\n        throw new Error(`Named callout must have three arguments \"${argsArray}\"`);\n      }\n      if (typeof arg0 === 'string' && !/^[A-Za-z_]\\w*$/.test(arg0)) {\n        throw new Error(`Named callout argument one must be a tag or number \"${arg0}\"`);\n      }\n      if (typeof arg1 === 'number' || !/^(?:[<>!=]=|[<>])$/.test(arg1)) {\n        throw new Error(`Named callout argument two must be '==', '!=', '>', '<', '>=', or '<=' \"${arg1}\"`);\n      }\n      if (typeof arg2 === 'string' && !/^[A-Za-z_]\\w*$/.test(arg2)) {\n        throw new Error(`Named callout argument three must be a tag or number \"${arg2}\"`);\n      }\n      break;\n    case 'custom':\n      // TODO: Can support custom callout names via a new option that allows providing a list of\n      // allowed, non-built-in names\n      throw new Error(`Undefined callout name \"${name}\"`);\n    default:\n      throw new Error(`Unexpected named callout kind \"${kind}\"`);\n  }\n  // TODO: If supporting custom callout names in the future (with an added `name` property for\n  // `NamedCalloutToken`), will need to set `name` to `null` if `kind` isn't `'custom'`\n  return createNamedCalloutToken(kind, tag ?? null, args?.split(',') ?? null, raw);\n}\n\nfunction tokenizeQuantifier(raw: string): QuantifierToken {\n  let kind: TokenQuantifierKind = null!;\n  let min: number;\n  let max: number;\n  if (raw[0] === '{') {\n    const {minStr, maxStr} =\n      /^\\{(?<minStr>\\d*)(?:,(?<maxStr>\\d*))?/.exec(raw)!.groups as {minStr: string, maxStr: string | undefined};\n    const limit = 100_000;\n    if (+minStr > limit || (maxStr && +maxStr > limit)) {\n      throw new Error('Quantifier value unsupported in Oniguruma');\n    }\n    min = +minStr;\n    max = maxStr === undefined ? +minStr : (maxStr === '' ? Infinity : +maxStr);\n    // By default, Onig doesn't support making interval quantifiers possessive with a `+` suffix;\n    // uses reversed range instead\n    if (min > max) {\n      kind = 'possessive';\n      [min, max] = [max, min];\n    }\n    if (raw.endsWith('?')) {\n      if (kind === 'possessive') {\n        // TODO: <github.com/slevithan/oniguruma-parser/issues/10>\n        throw new Error('Unsupported possessive interval quantifier chain with \"?\"');\n      }\n      kind = 'lazy';\n    } else if (!kind) {\n      kind = 'greedy';\n    }\n  } else {\n    min = raw[0] === '+' ? 1 : 0;\n    max = raw[0] === '?' ? 1 : Infinity;\n    kind = raw[1] === '+' ? 'possessive' : (raw[1] === '?' ? 'lazy' : 'greedy');\n  }\n  return createQuantifierToken(kind, min, max, raw);\n}\n\nfunction tokenizeShorthand(raw: string): CharacterSetToken {\n  const lower = raw[1].toLowerCase();\n  return createCharacterSetToken({\n    'd': 'digit',\n    'h': 'hex',\n    's': 'space',\n    'w': 'word',\n  }[lower] as TokenCharacterSetKind, raw, {\n    negate: raw[1] !== lower,\n  });\n}\n\nfunction tokenizeUnicodeProperty(raw: string): CharacterSetToken {\n  const {p, neg, value} = /^\\\\(?<p>[pP])\\{(?<neg>\\^?)(?<value>[^}]+)/.exec(raw)!.groups!;\n  const negate = (p === 'P' && !neg) || (p === 'p' && !!neg);\n  return createCharacterSetToken('property', raw, {\n    value,\n    negate,\n  });\n}\n\nfunction getFlagGroupSwitches(flags: string): FlagGroupSwitches | null {\n  // Don't include `false` for flags that aren't included\n  const obj: FlagGroupSwitches = {};\n  if (flags.includes('i')) {\n    obj.ignoreCase = true;\n  }\n  if (flags.includes('m')) {\n    // Onig flag m is equivalent to JS flag s\n    obj.dotAll = true;\n  }\n  if (flags.includes('x')) {\n    obj.extended = true;\n  }\n  return Object.keys(obj).length ? obj : null;\n}\n\nfunction getFlagProperties(flags: string): FlagProperties {\n  const flagProperties: FlagProperties = {\n    ignoreCase: false,\n    dotAll: false,\n    extended: false,\n    digitIsAscii: false,\n    posixIsAscii: false,\n    spaceIsAscii: false,\n    wordIsAscii: false,\n    textSegmentMode: null,\n  };\n  for (let i = 0; i < flags.length; i++) {\n    const char = flags[i];\n    if (!'imxDPSWy'.includes(char)) {\n      throw new Error(`Invalid flag \"${char}\"`);\n    }\n    // Flags y{g}, y{w} are currently only supported via the top-level `flags` option\n    if (char === 'y') {\n      if (!/^y{[gw]}/.test(flags.slice(i))) {\n        throw new Error('Invalid or unspecified flag \"y\" mode');\n      }\n      // If text segment mode flags appear multiple times, use the last one\n      flagProperties.textSegmentMode = flags[i + 2] === 'g' ? 'grapheme' : 'word';\n      i += 3;\n      continue;\n    }\n    flagProperties[{\n      i: 'ignoreCase',\n      // Flag m is called `multiline` in Onig, but that has a different meaning in JS. Onig flag m\n      // is equivalent to JS flag s\n      m: 'dotAll',\n      // Flag x is fully handled during tokenization\n      x: 'extended',\n      // Flags D, P, S, W are currently only supported via the top-level `flags` option\n      D: 'digitIsAscii',\n      P: 'posixIsAscii',\n      S: 'spaceIsAscii',\n      W: 'wordIsAscii',\n    }[char] as Exclude<keyof FlagProperties, 'textSegmentMode'>] = true;\n  }\n  return flagProperties;\n}\n\n// - Unenclosed `\\xNN` above 0x7F is handled elsewhere as a UTF-8 encoded byte sequence\n// - Enclosed `\\x{}` with value above 0x10FFFF is allowed here; handled in the parser\nfunction getValidatedHexCharCode(raw: string): number {\n  // Note: Onig 6.9.10 and earlier have a bug where pattern-terminating `\\u` and `\\x` are treated\n  // as identity escapes; see <github.com/kkos/oniguruma/issues/343>. Don't emulate these bugs.\n  // Additionally, Onig treats bare `\\x` as equivalent to `\\0`, and treats incomplete `\\x{` (with\n  // the brace but not immediately followed by a hex digit) as an identity escape, so e.g. `\\x{`\n  // matches `x{` and `^\\x{,2}$` matches `xx`, but `\\x{2,}` and `\\x{0,2}` are errors. Currently,\n  // this library treats all such cases as errors\n  if (/^(?:\\\\u(?!\\p{AHex}{4})|\\\\x(?!\\p{AHex}{1,2}|\\{\\p{AHex}{1,8}\\}))/u.test(raw)) {\n    throw new Error(`Incomplete or invalid escape \"${raw}\"`);\n  }\n  // Might include leading 0s\n  const hex = raw[2] === '{' ?\n    /^\\\\x\\{\\s*(?<hex>\\p{AHex}+)/u.exec(raw)!.groups!.hex :\n    raw.slice(2);\n  return parseInt(hex, 16);\n}\n\n// Value is 1-3 digits, which can be a backref (possibly invalid), null, octal, or identity escape,\n// possibly followed by 1-2 literal digits\nfunction splitEscapedNumberToken(token: EscapedNumberToken, numCaptures: number): Array<BackreferenceToken> | Array<CharacterToken> {\n  const {raw, inCharClass} = token;\n  // Keep any leading 0s since they indicate octal\n  const value = raw.slice(1);\n  // Backref (possibly invalid)\n  if (\n    !inCharClass &&\n    ( // Single digit 1-9 outside a char class is always treated as a backref\n      (value !== '0' && value.length === 1) ||\n      // Leading 0 makes it octal; backrefs can't include following literal digits\n      (value[0] !== '0' && +value <= numCaptures)\n    )\n  ) {\n    return [createBackreferenceToken(raw)];\n  }\n  const tokens: Array<CharacterToken> = [];\n  // Returns 1-3 matches; the first (only) might be octal\n  const matches = value.match(/^[0-7]+|\\d/g)!;\n  for (let i = 0; i < matches.length; i++) {\n    const m = matches[i];\n    let value: number;\n    // Octal digits are 0-7\n    if (i === 0 && m !== '8' && m !== '9') {\n      value = parseInt(m, 8);\n      if (value > 0o177) {\n        // Octal UTF-8 encoded byte sequence; not yet supported\n        throw new Error(r`Octal encoded byte above 177 unsupported \"${raw}\"`);\n      }\n    } else {\n      value = cpOf(m);\n    }\n    tokens.push(createCharacterToken(value, (i === 0 ? '\\\\' : '') + m));\n  }\n  return tokens;\n}\n\nfunction splitQuantifierMatch(str: string): Array<QuantifierToken> {\n  const tokens: Array<QuantifierToken> = [];\n  // `str` is one or more quantifiers in a chain. It can't be split by a regex because of one edge\n  // case where we have to compare numeric values: although `{1,2}?` is a single, lazy quantifier,\n  // a reversed (possessive) interval quantifier like `{2,1}` can't be both possessive and lazy, so\n  // any following `?`, `??`, or `?+` is a second, chained quantifier (i.e., `{2,1}?` is equivalent\n  // to `{2,1}{0,1}` or `{2,0}`)\n  const withG = new RegExp(quantifierRe, 'gy');\n  let match: RegExpExecArray | null;\n  while ((match = withG.exec(str))) {\n    const m = match[0];\n    if (m[0] === '{') {\n      // Doesn't need to handle fixed `{n}`, infinite max `{n,}`, or implicit zero min `{,n}`\n      // since, according to Onig syntax rules, those can't be possessive\n      const parts = /^\\{(?<min>\\d+),(?<max>\\d+)\\}\\??$/.exec(m);\n      if (parts) {\n        const {min, max} = parts.groups as {min: string, max: string};\n        if (+min > +max && m.endsWith('?')) {\n          // Leave the trailing `?` for the next match\n          withG.lastIndex--;\n          tokens.push(tokenizeQuantifier(m.slice(0, -1)));\n          continue;\n        }\n      }\n    }\n    tokens.push(tokenizeQuantifier(m));\n  }\n  return tokens;\n}\n\nexport {\n  type AlternatorToken,\n  type AssertionToken,\n  type BackreferenceToken,\n  type CharacterToken,\n  type CharacterClassCloseToken,\n  type CharacterClassHyphenToken,\n  type CharacterClassIntersectorToken,\n  type CharacterClassOpenToken,\n  type CharacterSetToken,\n  type DirectiveToken,\n  type FlagGroupModifiers,\n  type FlagProperties,\n  type GroupCloseToken,\n  type GroupOpenToken,\n  type NamedCalloutToken,\n  type QuantifierToken,\n  type SubroutineToken,\n  type Token,\n  type TokenCharacterSetKind,\n  type TokenDirectiveKind,\n  type TokenNamedCalloutKind,\n  type TokenQuantifierKind,\n  tokenize,\n};\n", "import type {AlternativeContainerNode, Node, ParentNode, QuantifiableNode} from './parse.js';\n\ntype KeysOfUnion<T> = T extends T ? keyof T: never;\ntype Props = {[key in KeysOfUnion<Node>]?: any} & {type?: Node['type']};\n\nfunction hasOnlyChild(node: ParentNode & {body: Array<Node>}, props?: Props): boolean {\n  if (!Array.isArray(node.body)) {\n    throw new Error('Expected node with body array');\n  }\n  if (node.body.length !== 1) {\n    return false;\n  }\n  const kid = node.body[0] as Props;\n  return !props || Object.keys(props).every(key => props[key as keyof Props] === kid[key as keyof Props]);\n}\n\nfunction isAlternativeContainer(node: Node): node is AlternativeContainerNode {\n  if (\n    !alternativeContainerTypes.has(node.type) ||\n    (node.type === 'AbsenceFunction' && node.kind !== 'repeater')\n  ) {\n    return false;\n  }\n  return true;\n}\nconst alternativeContainerTypes = new Set<Node['type']>([\n  'AbsenceFunction',\n  'CapturingGroup',\n  'Group',\n  'LookaroundAssertion',\n  'Regex',\n]);\n\nfunction isQuantifiable(node: Node): node is QuantifiableNode {\n  return quantifiableTypes.has(node.type);\n}\nconst quantifiableTypes = new Set<Node['type']>([\n  'AbsenceFunction',\n  'Backreference',\n  'CapturingGroup',\n  'Character',\n  'CharacterClass',\n  'CharacterSet',\n  'Group',\n  'Quantifier',\n  'Subroutine',\n]);\n\nexport {\n  hasOnlyChild,\n  isAlternativeContainer,\n  isQuantifiable,\n};\n", "import type {AssertionToken, BackreferenceToken, CharacterClassHyphenToken, CharacterClassOpenToken, CharacterSetToken, FlagGroupModifiers, FlagProperties, GroupOpenToken, QuantifierToken, SubroutineToken, Token, TokenCharacterSetKind, TokenDirectiveKind, TokenNamedCalloutKind, TokenQuantifierKind} from '../tokenizer/tokenize.js';\nimport {tokenize} from '../tokenizer/tokenize.js';\nimport {cpOf, getOrInsert, PosixClassNames, r, throwIfNullish} from '../utils.js';\nimport {hasOnlyChild, isAlternativeContainer, isQuantifiable} from './node-utils.js';\n\n// Watch out for the DOM `Node` interface!\ntype Node =\n  AbsenceFunctionNode |\n  AlternativeNode |\n  AssertionNode |\n  BackreferenceNode |\n  CapturingGroupNode |\n  CharacterNode |\n  CharacterClassNode |\n  CharacterClassRangeNode |\n  CharacterSetNode |\n  DirectiveNode |\n  FlagsNode |\n  GroupNode |\n  LookaroundAssertionNode |\n  NamedCalloutNode |\n  QuantifierNode |\n  RegexNode |\n  SubroutineNode;\n\ntype OnigurumaAst = RegexNode;\n\ntype ParentNode =\n  AlternativeContainerNode |\n  AlternativeNode |\n  CharacterClassNode |\n  CharacterClassRangeNode |\n  QuantifierNode;\n\n// See also `isAlternativeContainer`\ntype AlternativeContainerNode =\n  AbsenceFunctionNode | // Some sub-kinds aren't alternative containers\n  CapturingGroupNode |\n  GroupNode |\n  LookaroundAssertionNode |\n  RegexNode;\n\ntype AlternativeElementNode =\n  AbsenceFunctionNode |\n  AssertionNode |\n  BackreferenceNode |\n  CapturingGroupNode |\n  CharacterNode |\n  CharacterClassNode |\n  CharacterSetNode |\n  DirectiveNode |\n  GroupNode |\n  LookaroundAssertionNode |\n  NamedCalloutNode |\n  QuantifierNode |\n  SubroutineNode;\n\ntype CharacterClassElementNode =\n  CharacterNode |\n  CharacterClassNode |\n  CharacterClassRangeNode |\n  CharacterSetNode;\n\n// See also `isQuantifiable`\ntype QuantifiableNode =\n  AbsenceFunctionNode |\n  BackreferenceNode |\n  CapturingGroupNode |\n  CharacterNode |\n  CharacterClassNode |\n  CharacterSetNode |\n  GroupNode |\n  QuantifierNode |\n  SubroutineNode;\n\n// TODO: Support remaining kinds; see <github.com/slevithan/oniguruma-to-es/issues/13>\ntype NodeAbsenceFunctionKind =\n  'repeater';\n\ntype NodeAssertionKind =\n  'line_end' |\n  'line_start' |\n  'search_start' |\n  'string_end' |\n  'string_end_newline' |\n  'string_start' |\n  'text_segment_boundary' |\n  'word_boundary';\n\ntype NodeCharacterClassKind =\n  'union' |\n  'intersection';\n\ntype NodeCharacterSetKind = TokenCharacterSetKind;\n\ntype NodeDirectiveKind = TokenDirectiveKind;\n\ntype NodeLookaroundAssertionKind =\n  'lookahead' |\n  'lookbehind';\n\ntype NodeNamedCalloutKind = TokenNamedCalloutKind;\n\ntype NodeQuantifierKind = TokenQuantifierKind;\n\ntype UnicodePropertyMap = Map<string, string>;\n\ntype Context = {\n  capturingGroups: Array<CapturingGroupNode>;\n  hasNumberedRef: boolean;\n  namedGroupsByName: Map<string, Array<CapturingGroupNode>>;\n  nextIndex: number;\n  normalizeUnknownPropertyNames: boolean;\n  parent: ParentNode;\n  skipBackrefValidation: boolean;\n  skipLookbehindValidation: boolean;\n  skipPropertyNameValidation: boolean;\n  subroutines: Array<SubroutineNode>;\n  tokens: Array<Token>;\n  unicodePropertyMap: UnicodePropertyMap | null;\n  walk: (parent: ParentNode, state: State) => Node;\n};\n\n// Top-level `walk` calls are given empty state; nested calls can add data specific to their `walk`\ntype State = {\n  isCheckingRangeEnd?: boolean;\n  isInAbsenceFunction?: boolean;\n  isInLookbehind?: boolean;\n  isInNegLookbehind?: boolean;\n};\n\ntype ParseOptions = {\n  flags?: string;\n  normalizeUnknownPropertyNames?: boolean;\n  rules?: {\n    captureGroup?: boolean;\n    singleline?: boolean;\n  };\n  skipBackrefValidation?: boolean;\n  skipLookbehindValidation?: boolean;\n  skipPropertyNameValidation?: boolean;\n  unicodePropertyMap?: UnicodePropertyMap | null;\n};\n\nfunction parse(pattern: string, options: ParseOptions = {}): OnigurumaAst {\n  const opts: Required<ParseOptions> = {\n    flags: '',\n    normalizeUnknownPropertyNames: false,\n    skipBackrefValidation: false,\n    skipLookbehindValidation: false,\n    skipPropertyNameValidation: false,\n    // `toOnigurumaAst` provides `OnigUnicodePropertyMap`, but it can be custom or `null`\n    unicodePropertyMap: null,\n    ...options,\n    rules: {\n      captureGroup: false, // `ONIG_OPTION_CAPTURE_GROUP`\n      singleline: false, // `ONIG_OPTION_SINGLELINE`\n      ...options.rules,\n    },\n  };\n  const tokenized = tokenize(pattern, {\n    // Limit to the tokenizer's options\n    flags: opts.flags,\n    rules: {\n      captureGroup: opts.rules.captureGroup,\n      singleline: opts.rules.singleline,\n    },\n  });\n  const walk: Context['walk'] = (parent, state) => {\n    const token = tokenized.tokens[context.nextIndex];\n    context.parent = parent;\n    // Advance for the next iteration\n    context.nextIndex++;\n    switch (token.type) {\n      case 'Alternator':\n        // Top-level only; groups handle their own alternators\n        return createAlternative();\n      case 'Assertion':\n        return parseAssertion(token);\n      case 'Backreference':\n        return parseBackreference(token, context);\n      case 'Character':\n        return createCharacter(token.value, {useLastValid: !!state.isCheckingRangeEnd});\n      case 'CharacterClassHyphen':\n        return parseCharacterClassHyphen(token, context, state);\n      case 'CharacterClassOpen':\n        return parseCharacterClassOpen(token, context, state);\n      case 'CharacterSet':\n        return parseCharacterSet(token, context);\n      case 'Directive':\n        return createDirective(token.kind, {flags: token.flags});\n      case 'GroupOpen':\n        return parseGroupOpen(token, context, state);\n      case 'NamedCallout':\n        return createNamedCallout(token.kind, token.tag, token.arguments);\n      case 'Quantifier':\n        return parseQuantifier(token, context);\n      case 'Subroutine':\n        return parseSubroutine(token, context);\n      default:\n        throw new Error(`Unexpected token type \"${token.type}\"`);\n    }\n  }\n  const context: Context = {\n    capturingGroups: [],\n    hasNumberedRef: false,\n    namedGroupsByName: new Map(),\n    nextIndex: 0,\n    normalizeUnknownPropertyNames: opts.normalizeUnknownPropertyNames,\n    parent: null!, // Assigned by `walk`\n    skipBackrefValidation: opts.skipBackrefValidation,\n    skipLookbehindValidation: opts.skipLookbehindValidation,\n    skipPropertyNameValidation: opts.skipPropertyNameValidation,\n    subroutines: [],\n    tokens: tokenized.tokens,\n    unicodePropertyMap: opts.unicodePropertyMap,\n    walk,\n  };\n\n  // ## AST construction from tokens\n  const ast = createRegex(createFlags(tokenized.flags));\n  let top = ast.body[0]; // First alt\n  while (context.nextIndex < tokenized.tokens.length) {\n    const node = walk(top, {});\n    if (node.type === 'Alternative') {\n      ast.body.push(node);\n      top = node;\n    } else {\n      top.body.push(node as AlternativeElementNode);\n    }\n  }\n\n  // ## Validation that requires knowledge about the complete pattern\n  // `context` updated by the preceding `walk` loop\n  const {capturingGroups, hasNumberedRef, namedGroupsByName, subroutines} = context;\n  if (hasNumberedRef && namedGroupsByName.size && !opts.rules.captureGroup) {\n    throw new Error('Numbered backref/subroutine not allowed when using named capture');\n  }\n  for (const {ref} of subroutines) {\n    if (typeof ref === 'number') {\n      // Relative nums are already resolved\n      if (ref > capturingGroups.length) {\n        throw new Error(`Subroutine uses a group number that's not defined`);\n      }\n      if (ref) {\n        capturingGroups[ref - 1].isSubroutined = true;\n      }\n    } else if (!namedGroupsByName.has(ref)) {\n      throw new Error(r`Subroutine uses a group name that's not defined \"\\g<${ref}>\"`);\n    } else if (namedGroupsByName.get(ref)!.length > 1) {\n      throw new Error(r`Subroutine uses a duplicate group name \"\\g<${ref}>\"`);\n    } else {\n      namedGroupsByName.get(ref)![0].isSubroutined = true;\n    }\n  }\n\n  return ast;\n}\n\nfunction parseAssertion({kind}: AssertionToken): AssertionNode {\n  return createAssertion(\n    throwIfNullish({\n      '^': 'line_start',\n      '$': 'line_end',\n      '\\\\A': 'string_start',\n      '\\\\b': 'word_boundary',\n      '\\\\B': 'word_boundary',\n      '\\\\G': 'search_start',\n      '\\\\y': 'text_segment_boundary',\n      '\\\\Y': 'text_segment_boundary',\n      '\\\\z': 'string_end',\n      '\\\\Z': 'string_end_newline',\n    }[kind], `Unexpected assertion kind \"${kind}\"`) as NodeAssertionKind,\n    {negate: kind === r`\\B` || kind === r`\\Y`}\n  );\n}\n\n// Supported (if the backref appears to the right of the reffed capture's opening paren):\n// - `\\k<name>`, `\\k'name'`\n// - When named capture not used:\n//   - `\\n`, `\\nn`, `\\nnn`\n//   - `\\k<n>`, `\\k'n'\n//   - `\\k<-n>`, `\\k'-n'`\n// Unsupported:\n// - `\\k<+n>`, `\\k'+n'` - Note that, Unlike Oniguruma, Onigmo doesn't support this as special\n//   syntax and therefore considers it a valid group name.\n// - Backref with recursion level (with num or name): `\\k<n+level>`, `\\k<n-level>`, etc.\n//   (Onigmo also supports `\\k<-n+level>`, `\\k<-n-level>`, etc.)\n// Backrefs in Onig use multiplexing for duplicate group names (the rules can be complicated when\n// overlapping with subroutines), but a `Backreference`'s simple `ref` prop doesn't capture these\n// details so multiplexed ref pointers need to be derived when working with the AST\nfunction parseBackreference({raw}: BackreferenceToken, context: Context): BackreferenceNode {\n  const hasKWrapper = /^\\\\k[<']/.test(raw);\n  const ref = hasKWrapper ? raw.slice(3, -1) : raw.slice(1);\n  const fromNum = (num: number, isRelative = false) => {\n    const numCapturesToLeft = context.capturingGroups.length;\n    let orphan = false;\n    // Note: It's not an error for numbered backrefs to come before their referenced group in Onig,\n    // but it's currently an error in this library.\n    // - Most such placements are mistakes and can never match, due to Onig's behavior for backrefs\n    //   to nonparticipating groups.\n    //   - The edge cases where they're matchable rely on rules for backref resetting within\n    //     quantified groups that are different in JS (thus not emulatable in `oniguruma-to-es`).\n    // - Erroring matches the correct behavior of named backrefs.\n    // - For unenclosed backrefs, this only affects `\\1`-`\\9` since it's not a backref in the first\n    //   place if using `\\10` or higher and not as many capturing groups are defined to the left\n    //   (it's an octal or identity escape).\n    // TODO: Ideally this would be refactored to include the backref in the AST when it's not an\n    // error in Onig (due to the reffed group being defined to the right), and the error handling\n    // would move to the `oniguruma-to-es` transformer\n    if (num > numCapturesToLeft) {\n      // Skipping the error breaks assumptions and might create edge case issues, since backrefs\n      // are required to come after their captures; unfortunately this option is needed for\n      // TextMate grammars\n      if (context.skipBackrefValidation) {\n        orphan = true;\n      } else {\n        throw new Error(`Not enough capturing groups defined to the left \"${raw}\"`);\n      }\n    }\n    context.hasNumberedRef = true;\n    return createBackreference(isRelative ? numCapturesToLeft + 1 - num : num, {orphan});\n  };\n  if (hasKWrapper) {\n    const numberedRef = /^(?<sign>-?)0*(?<num>[1-9]\\d*)$/.exec(ref);\n    if (numberedRef) {\n      return fromNum(+numberedRef.groups!.num, !!numberedRef.groups!.sign);\n    }\n    // Invalid in a backref name even when valid in a group name\n    if (/[-+]/.test(ref)) {\n      throw new Error(`Invalid backref name \"${raw}\"`);\n    }\n    if (!context.namedGroupsByName.has(ref)) {\n      throw new Error(`Group name not defined to the left \"${raw}\"`);\n    }\n    return createBackreference(ref);\n  }\n  return fromNum(+ref);\n}\n\nfunction parseCharacterClassHyphen(_: CharacterClassHyphenToken, context: Context, state: State): CharacterNode | CharacterClassRangeNode {\n  const {tokens, walk} = context;\n  const parent = context.parent as CharacterClassNode;\n  const prevSiblingNode = parent.body.at(-1);\n  const nextToken = tokens[context.nextIndex];\n  if (\n    !state.isCheckingRangeEnd &&\n    prevSiblingNode &&\n    prevSiblingNode.type !== 'CharacterClass' &&\n    prevSiblingNode.type !== 'CharacterClassRange' &&\n    nextToken &&\n    nextToken.type !== 'CharacterClassOpen' &&\n    nextToken.type !== 'CharacterClassClose' &&\n    nextToken.type !== 'CharacterClassIntersector'\n  ) {\n    const nextNode = walk(parent, {\n      ...state,\n      isCheckingRangeEnd: true,\n    });\n    if (prevSiblingNode.type === 'Character' && nextNode.type === 'Character') {\n      parent.body.pop();\n      return createCharacterClassRange(prevSiblingNode, nextNode);\n    }\n    throw new Error('Invalid character class range');\n  }\n  return createCharacter(cpOf('-'));\n}\n\nfunction parseCharacterClassOpen({negate}: CharacterClassOpenToken, context: Context, state: State): CharacterClassNode {\n  const {tokens, walk} = context;\n  const firstClassToken = tokens[context.nextIndex];\n  const intersections = [createCharacterClass()];\n  let nextToken = throwIfUnclosedCharacterClass(firstClassToken);\n  while (nextToken.type !== 'CharacterClassClose') {\n    if (nextToken.type === 'CharacterClassIntersector') {\n      intersections.push(createCharacterClass());\n      // Skip the intersector\n      context.nextIndex++;\n    } else {\n      const cc = intersections.at(-1)!; // Always at least one\n      cc.body.push(walk(cc, state) as CharacterClassElementNode);\n    }\n    nextToken = throwIfUnclosedCharacterClass(tokens[context.nextIndex], firstClassToken);\n  }\n  const node = createCharacterClass({negate});\n  if (intersections.length === 1) {\n    node.body = intersections[0].body;\n  } else {\n    node.kind = 'intersection';\n    node.body = intersections.map(cc => cc.body.length === 1 ? cc.body[0] : cc);\n  }\n  // Skip the closing square bracket\n  context.nextIndex++;\n  return node;\n}\n\nfunction parseCharacterSet({kind, negate, value}: CharacterSetToken, context: Context): CharacterSetNode {\n  const {normalizeUnknownPropertyNames, skipPropertyNameValidation, unicodePropertyMap} = context;\n  if (kind === 'property') {\n    const normalized = slug(value!);\n    // Don't treat as POSIX if it's in the provided list of Unicode property names\n    if (PosixClassNames.has(normalized) && !unicodePropertyMap?.has(normalized)) {\n      kind = 'posix';\n      value = normalized;\n    } else {\n      return createUnicodeProperty(value!, {\n        negate,\n        normalizeUnknownPropertyNames,\n        skipPropertyNameValidation,\n        unicodePropertyMap,\n      });\n    }\n  }\n  if (kind === 'posix') {\n    return createPosixClass(value!, {negate});\n  }\n  return createCharacterSet(kind, {negate});\n}\n\nfunction parseGroupOpen(token: GroupOpenToken, context: Context, state: State): AbsenceFunctionNode | CapturingGroupNode | GroupNode | LookaroundAssertionNode {\n  const {tokens, capturingGroups, namedGroupsByName, skipLookbehindValidation, walk} = context;\n  const node = createByGroupKind(token);\n  const isThisAbsenceFunction = node.type === 'AbsenceFunction';\n  const isThisLookbehind = isLookbehind(node);\n  const isThisNegLookbehind = isThisLookbehind && node.negate;\n  // Track capturing group details for backrefs and subroutines (before parsing the group's\n  // contents so nested groups with the same name are tracked in order)\n  if (node.type === 'CapturingGroup') {\n    capturingGroups.push(node);\n    if (node.name) {\n      getOrInsert(namedGroupsByName, node.name, []).push(node);\n    }\n  }\n  // Don't allow nested absence functions\n  if (isThisAbsenceFunction && state.isInAbsenceFunction) {\n    // Is officially unsupported in Onig but doesn't throw, gives strange results\n    throw new Error('Nested absence function not supported by Oniguruma');\n  }\n  let nextToken = throwIfUnclosedGroup(tokens[context.nextIndex]);\n  while (nextToken.type !== 'GroupClose') {\n    if (nextToken.type === 'Alternator') {\n      node.body.push(createAlternative());\n      // Skip the alternator\n      context.nextIndex++;\n    } else {\n      const alt = node.body.at(-1)!; // Always at least one\n      const child = walk(alt, {\n        ...state,\n        isInAbsenceFunction: state.isInAbsenceFunction || isThisAbsenceFunction,\n        isInLookbehind: state.isInLookbehind || isThisLookbehind,\n        isInNegLookbehind: state.isInNegLookbehind || isThisNegLookbehind,\n      }) as AlternativeElementNode;\n      alt.body.push(child);\n      // Centralized validation of lookbehind contents\n      if ((isThisLookbehind || state.isInLookbehind) && !skipLookbehindValidation) {\n        // JS supports all features within lookbehind, but Onig doesn't. Absence functions of form\n        // `(?~|)` and `(?~|…)` are also invalid in lookbehind (the `(?~…)` and `(?~|…|…)` forms\n        // are allowed), but all forms with `(?~|` throw since they aren't yet supported\n        const msg = 'Lookbehind includes a pattern not allowed by Oniguruma';\n        if (isThisNegLookbehind || state.isInNegLookbehind) {\n          // - Invalid: `(?=…)`, `(?!…)`, capturing groups\n          // - Valid: `(?<=…)`, `(?<!…)`\n          if (isLookahead(child) || child.type === 'CapturingGroup') {\n            throw new Error(msg);\n          }\n        } else {\n          // - Invalid: `(?=…)`, `(?!…)`, `(?<!…)`\n          // - Valid: `(?<=…)`, capturing groups\n          if (isLookahead(child) || (isLookbehind(child) && child.negate)) {\n            throw new Error(msg);\n          }\n        }\n      }\n    }\n    nextToken = throwIfUnclosedGroup(tokens[context.nextIndex]);\n  }\n  // Skip the closing parenthesis\n  context.nextIndex++;\n  return node;\n}\n\nfunction parseQuantifier({kind, min, max}: QuantifierToken, context: Context): QuantifierNode {\n  const parent = context.parent as AlternativeNode;\n  const quantifiedNode = parent.body.at(-1);\n  if (!quantifiedNode || !isQuantifiable(quantifiedNode)) {\n    throw new Error('Quantifier requires a repeatable token');\n  }\n  const node = createQuantifier(kind, min, max, quantifiedNode);\n  parent.body.pop();\n  return node;\n}\n\n// Onig subroutine behavior:\n// - Subroutines can appear before the groups they reference; ex: `\\g<1>(a)` is valid.\n// - Multiple subroutines can reference the same group.\n// - Subroutines can reference groups that themselves contain subroutines, followed to any depth.\n// - Subroutines can be used recursively, and `\\g<0>` recursively references the whole pattern.\n// - Subroutines can use relative references (backward or forward); ex: `\\g<+1>(.)\\g<-1>`.\n// - Subroutines don't get their own capturing group numbers; ex: `(.)\\g<1>\\2` is invalid.\n// - Subroutines use the flags that apply to their referenced group, so e.g.\n//   `(?-i)(?<a>a)(?i)\\g<a>` is fully case sensitive.\n// - Differences from PCRE/Perl/Regex+ subroutines:\n//   - Subroutines can't reference duplicate group names (though duplicate names are valid if no\n//     subroutines reference them).\n//   - Subroutines can't use absolute or relative numbers if named capture is used anywhere.\n//   - Named backrefs must be to the right of their group definition, so the backref in\n//     `\\g<a>\\k<a>(?<a>)` is invalid (not directly related to subroutines).\n//   - Subroutines don't restore capturing group match values (for backrefs) upon exit, so e.g.\n//     `(?<a>(?<b>[ab]))\\g<a>\\k<b>` matches `abb` but not `aba`; same for numbered.\n// The interaction of backref multiplexing (an Onig-specific feature) and subroutines is complex:\n// - Only the most recent value matched by a capturing group and its subroutines is considered for\n//   backref multiplexing, and this also applies to capturing groups nested within a group that's\n//   referenced by a subroutine.\n// - Although a subroutine can't reference a group with a duplicate name, it can reference a group\n//   with a nested capture whose name is duplicated (e.g. outside of the referenced group).\n//   - These duplicate names can then multiplex; but only the most recent value matched from within\n//     the outer group (or the subroutines that reference it) is available for multiplexing.\n//   - Ex: With `(?<a>(?<b>[123]))\\g<a>\\g<a>(?<b>0)\\k<b>`, the backref `\\k<b>` can only match `0`\n//     or whatever was matched by the most recently matched subroutine. If you took out `(?<b>0)`,\n//     no multiplexing would occur.\nfunction parseSubroutine({raw}: SubroutineToken, context: Context): SubroutineNode {\n  const {capturingGroups, subroutines} = context;\n  let ref: string | number = raw.slice(3, -1);\n  const numberedRef = /^(?<sign>[-+]?)0*(?<num>[1-9]\\d*)$/.exec(ref);\n  if (numberedRef) {\n    const num = +numberedRef.groups!.num;\n    const numCapturesToLeft = capturingGroups.length;\n    context.hasNumberedRef = true;\n    ref = {\n      '': num,\n      '+': numCapturesToLeft + num,\n      '-': numCapturesToLeft + 1 - num,\n    }[numberedRef.groups!.sign]!;\n    if (ref < 1) {\n      throw new Error('Invalid subroutine number');\n    }\n  // Special case for full-pattern recursion; can't be `+0`, `-0`, `00`, etc.\n  } else if (ref === '0') {\n    ref = 0;\n  }\n  const node = createSubroutine(ref);\n  subroutines.push(node);\n  return node;\n}\n\n// -------------------------------\n// --- Node creation and types ---\n// -------------------------------\n\ntype AbsenceFunctionNode = {\n  type: 'AbsenceFunction';\n  kind: NodeAbsenceFunctionKind;\n  body: Array<AlternativeNode>;\n};\nfunction createAbsenceFunction(kind: NodeAbsenceFunctionKind, options?: {\n  body?: Array<AlternativeNode>;\n}): AbsenceFunctionNode {\n  if (kind !== 'repeater') {\n    throw new Error(`Unexpected absence function kind \"${kind}\"`);\n  }\n  return {\n    type: 'AbsenceFunction',\n    kind,\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype AlternativeNode = {\n  type: 'Alternative';\n  body: Array<AlternativeElementNode>;\n};\nfunction createAlternative(options?: {\n  body?: Array<AlternativeElementNode>;\n}): AlternativeNode {\n  return {\n    type: 'Alternative',\n    body: getBodyForElementContainer(options?.body) as Array<AlternativeElementNode>,\n  };\n}\n\ntype AssertionNode = {\n  type: 'Assertion';\n  kind: NodeAssertionKind;\n  negate?: boolean;\n};\nfunction createAssertion(kind: NodeAssertionKind, options?: {\n  negate?: boolean;\n}): AssertionNode {\n  const node: AssertionNode = {\n    type: 'Assertion',\n    kind,\n  };\n  if (kind === 'word_boundary' || kind === 'text_segment_boundary') {\n    node.negate = !!options?.negate;\n  }\n  return node;\n}\n\ntype BackreferenceNode = {\n  type: 'Backreference';\n  ref: string | number;\n  orphan?: boolean;\n};\nfunction createBackreference(ref: string | number, options?: {\n  orphan?: boolean;\n}): BackreferenceNode {\n  const orphan = !!options?.orphan;\n  return {\n    type: 'Backreference',\n    ref,\n    ...(orphan && {orphan}),\n  };\n}\n\ntype CapturingGroupNode = {\n  type: 'CapturingGroup';\n  kind?: never;\n  number: number;\n  name?: string;\n  // One or more subroutines in the regex reference this group\n  isSubroutined?: boolean;\n  body: Array<AlternativeNode>;\n};\nfunction createCapturingGroup(number: number, options?: {\n  name?: string;\n  isSubroutined?: boolean;\n  body?: Array<AlternativeNode>;\n}): CapturingGroupNode {\n  const opts = {\n    name: undefined,\n    isSubroutined: false,\n    ...options,\n  };\n  if (opts.name !== undefined && !isValidGroupName(opts.name)) {\n    throw new Error(`Group name \"${opts.name}\" invalid in Oniguruma`);\n  }\n  return {\n    type: 'CapturingGroup',\n    number,\n    ...(opts.name && {name: opts.name}),\n    ...(opts.isSubroutined && {isSubroutined: opts.isSubroutined}),\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype CharacterNode = {\n  type: 'Character';\n  value: number;\n};\nfunction createCharacter(charCode: number, options?: {\n  useLastValid?: boolean;\n}): CharacterNode {\n  const opts = {\n    useLastValid: false,\n    ...options,\n  };\n  if (charCode > 0x10FFFF) {\n    const hex = charCode.toString(16);\n    if (opts.useLastValid) {\n      charCode = 0x10FFFF;\n    } else if (charCode > 0x13FFFF) {\n      throw new Error(`Invalid code point out of range \"\\\\x{${hex}}\"`);\n    } else {\n      throw new Error(`Invalid code point out of range in JS \"\\\\x{${hex}}\"`);\n    }\n  }\n  return {\n    type: 'Character',\n    value: charCode,\n  };\n}\n\ntype CharacterClassNode = {\n  type: 'CharacterClass';\n  kind: NodeCharacterClassKind;\n  negate: boolean;\n  body: Array<CharacterClassElementNode>;\n};\nfunction createCharacterClass(options?: {\n  kind?: NodeCharacterClassKind;\n  negate?: boolean;\n  body?: Array<CharacterClassElementNode>;\n}): CharacterClassNode {\n  const opts = {\n    kind: 'union' as NodeCharacterClassKind,\n    negate: false,\n    ...options,\n  };\n  return {\n    type: 'CharacterClass',\n    kind: opts.kind,\n    negate: opts.negate,\n    body: getBodyForElementContainer(options?.body) as Array<CharacterClassElementNode>,\n  };\n}\n\ntype CharacterClassRangeNode = {\n  type: 'CharacterClassRange';\n  min: CharacterNode;\n  max: CharacterNode;\n};\nfunction createCharacterClassRange(min: CharacterNode, max: CharacterNode): CharacterClassRangeNode {\n  if (max.value < min.value) {\n    throw new Error('Character class range out of order');\n  }\n  return {\n    type: 'CharacterClassRange',\n    min,\n    max,\n  };\n}\n\ntype NamedCharacterSetNode = {\n  type: 'CharacterSet';\n  kind: 'posix' | 'property';\n  value: string;\n  negate: boolean;\n  variableLength?: never;\n};\ntype UnnamedCharacterSetNode = {\n  type: 'CharacterSet';\n  kind: Exclude<NodeCharacterSetKind, NamedCharacterSetNode['kind']>;\n  value?: never;\n  negate?: boolean;\n  variableLength?: boolean;\n};\ntype CharacterSetNode = NamedCharacterSetNode | UnnamedCharacterSetNode;\n/**\nUse `createUnicodeProperty` and `createPosixClass` for `kind` values `'property'` and `'posix'`.\n*/\nfunction createCharacterSet(kind: UnnamedCharacterSetNode['kind'], options?: {\n  negate?: boolean;\n}): UnnamedCharacterSetNode {\n  const negate = !!options?.negate;\n  const node: UnnamedCharacterSetNode = {\n    type: 'CharacterSet',\n    kind,\n  };\n  if (\n    kind === 'digit' ||\n    kind === 'hex' ||\n    kind === 'newline' ||\n    kind === 'space' ||\n    kind === 'word'\n  ) {\n    node.negate = negate;\n  }\n  if (\n    kind === 'text_segment' ||\n    (kind === 'newline' && !negate)\n  ) {\n    node.variableLength = true;\n  }\n  return node;\n}\n\ntype DirectiveNode = {\n  type: 'Directive';\n} & ({\n  kind: 'keep';\n  flags?: never;\n} | {\n  kind: 'flags';\n  flags: FlagGroupModifiers;\n});\nfunction createDirective(kind: NodeDirectiveKind, options: {flags?: FlagGroupModifiers} = {}): DirectiveNode {\n  if (kind === 'keep') {\n    return {\n      type: 'Directive',\n      kind,\n    };\n  }\n  if (kind === 'flags') {\n    // Note: Flag effects might extend across alternation; ex: `a(?i)b|c` is equivalent to\n    // `a(?i:b)|(?i:c)`, not `a(?i:b|c)`\n    return {\n      type: 'Directive',\n      kind,\n      flags: throwIfNullish(options.flags),\n    };\n  }\n  throw new Error(`Unexpected directive kind \"${kind}\"`);\n}\n\ntype FlagsNode = {\n  type: 'Flags';\n} & FlagProperties;\nfunction createFlags(flags: FlagProperties): FlagsNode {\n  return {\n    type: 'Flags',\n    ...flags,\n  };\n}\n\ntype GroupNode = {\n  type: 'Group';\n  kind?: never;\n  atomic?: boolean;\n  flags?: FlagGroupModifiers;\n  body: Array<AlternativeNode>;\n};\nfunction createGroup(options?: {\n  atomic?: boolean;\n  flags?: FlagGroupModifiers;\n  body?: Array<AlternativeNode>;\n}): GroupNode {\n  const atomic = options?.atomic;\n  const flags = options?.flags;\n  if (atomic && flags) {\n    throw new Error('Atomic group cannot have flags');\n  }\n  return {\n    type: 'Group',\n    ...(atomic && {atomic}),\n    ...(flags && {flags}),\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype LookaroundAssertionNode = {\n  type: 'LookaroundAssertion';\n  kind: NodeLookaroundAssertionKind;\n  negate: boolean;\n  body: Array<AlternativeNode>;\n};\nfunction createLookaroundAssertion(options?: {\n  behind?: boolean;\n  negate?: boolean;\n  body?: Array<AlternativeNode>;\n}): LookaroundAssertionNode {\n  const opts = {\n    behind: false,\n    negate: false,\n    ...options,\n  };\n  return {\n    type: 'LookaroundAssertion',\n    kind: opts.behind ? 'lookbehind' : 'lookahead',\n    negate: opts.negate,\n    body: getBodyForAlternativeContainer(options?.body),\n  };\n}\n\ntype NamedCalloutNode = {\n  type: 'NamedCallout';\n  kind: NodeNamedCalloutKind;\n  tag: string | null;\n  arguments: Array<string | number> | null;\n};\nfunction createNamedCallout(\n  kind: NodeNamedCalloutKind,\n  tag: string | null,\n  args: Array<string | number> | null\n): NamedCalloutNode {\n  return {\n    type: 'NamedCallout',\n    kind,\n    tag,\n    arguments: args,\n  };\n}\n\nfunction createPosixClass(name: string, options?: {\n  negate?: boolean;\n}): NamedCharacterSetNode & {kind: 'posix'} {\n  const negate = !!options?.negate;\n  if (!PosixClassNames.has(name)) {\n    throw new Error(`Invalid POSIX class \"${name}\"`);\n  }\n  return {\n    type: 'CharacterSet',\n    kind: 'posix',\n    value: name,\n    negate,\n  };\n}\n\ntype QuantifierNode = {\n  type: 'Quantifier';\n  kind: NodeQuantifierKind;\n  min: number;\n  max: number;\n  body: QuantifiableNode;\n};\nfunction createQuantifier(kind: NodeQuantifierKind, min: number, max: number, body: QuantifiableNode): QuantifierNode {\n  if (min > max) {\n    throw new Error('Invalid reversed quantifier range');\n  }\n  return {\n    type: 'Quantifier',\n    kind,\n    min,\n    max,\n    body,\n  };\n}\n\ntype RegexNode = {\n  type: 'Regex';\n  body: Array<AlternativeNode>;\n  flags: FlagsNode;\n};\nfunction createRegex(flags: FlagsNode, options?: {\n  body?: Array<AlternativeNode>;\n}): RegexNode {\n  return {\n    type: 'Regex',\n    body: getBodyForAlternativeContainer(options?.body),\n    flags,\n  };\n}\n\ntype SubroutineNode = {\n  type: 'Subroutine';\n  ref: string | number;\n};\nfunction createSubroutine(ref: string | number): SubroutineNode {\n  return {\n    type: 'Subroutine',\n    ref,\n  };\n}\n\ntype CreateUnicodePropertyOptions = {\n  negate?: boolean;\n  normalizeUnknownPropertyNames?: boolean;\n  skipPropertyNameValidation?: boolean;\n  unicodePropertyMap?: UnicodePropertyMap | null;\n};\nfunction createUnicodeProperty(name: string, options?: CreateUnicodePropertyOptions): NamedCharacterSetNode & {kind: 'property'} {\n  const opts: Required<CreateUnicodePropertyOptions> = {\n    negate: false,\n    normalizeUnknownPropertyNames: false,\n    skipPropertyNameValidation: false,\n    unicodePropertyMap: null,\n    ...options,\n  };\n  let normalized = opts.unicodePropertyMap?.get(slug(name));\n  if (!normalized) {\n    if (opts.normalizeUnknownPropertyNames) {\n      normalized = normalizeUnicodePropertyName(name);\n    // Let the name through as-is if no map provided and normalization not requested\n    } else if (opts.unicodePropertyMap && !opts.skipPropertyNameValidation) {\n      throw new Error(r`Invalid Unicode property \"\\p{${name}}\"`);\n    }\n  }\n  return {\n    type: 'CharacterSet',\n    kind: 'property',\n    value: normalized ?? name,\n    negate: opts.negate,\n  };\n}\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\nfunction createByGroupKind({flags, kind, name, negate, number}: GroupOpenToken): AbsenceFunctionNode | CapturingGroupNode | GroupNode | LookaroundAssertionNode {\n  switch (kind) {\n    case 'absence_repeater':\n      return createAbsenceFunction('repeater');\n    case 'atomic':\n      return createGroup({atomic: true});\n    case 'capturing':\n      return createCapturingGroup(number!, {name});\n    case 'group':\n      return createGroup({flags});\n    case 'lookahead':\n    case 'lookbehind':\n      return createLookaroundAssertion({\n        behind: kind === 'lookbehind',\n        negate,\n      });\n    default:\n      throw new Error(`Unexpected group kind \"${kind}\"`);\n  }\n}\n\nfunction getBodyForAlternativeContainer(body: unknown): Array<AlternativeNode> {\n  if (body === undefined) {\n    body = [createAlternative()];\n  } else if (!Array.isArray(body) || !body.length || !body.every(node => (node as Node).type === 'Alternative')) {\n    throw new Error('Invalid body; expected array of one or more Alternative nodes');\n  }\n  return body as Array<AlternativeNode>;\n}\n\nfunction getBodyForElementContainer(body: unknown): Array<Node> {\n  if (body === undefined) {\n    body = [];\n  } else if (!Array.isArray(body) || !body.every(node => !!(node as Node).type)) {\n    throw new Error('Invalid body; expected array of nodes');\n  }\n  return body as Array<Node>;\n}\n\nfunction isLookahead(node: Node): node is (LookaroundAssertionNode & {kind: 'lookahead'}) {\n  return node.type === 'LookaroundAssertion' && node.kind === 'lookahead';\n}\n\nfunction isLookbehind(node: Node): node is (LookaroundAssertionNode & {kind: 'lookbehind'}) {\n  return node.type === 'LookaroundAssertion' && node.kind === 'lookbehind';\n}\n\nfunction isValidGroupName(name: string): boolean {\n  // Note that backrefs and subroutines might contextually use `-` and `+` to indicate relative\n  // index or recursion level\n  return /^[\\p{Alpha}\\p{Pc}][^)]*$/u.test(name);\n}\n\nfunction normalizeUnicodePropertyName(name: string): string {\n  // In Onig, Unicode property names ignore case, spaces, hyphens, and underscores. Use best effort\n  // to reformat the name to follow official values (covers a lot, but isn't able to map for all\n  // possible formatting differences)\n  return name.\n    trim().\n    replace(/[- _]+/g, '_').\n    replace(/[A-Z][a-z]+(?=[A-Z])/g, '$&_'). // `PropertyName` to `Property_Name`\n    replace(/[A-Za-z]+/g, m => m[0].toUpperCase() + m.slice(1).toLowerCase());\n}\n\n/**\nGenerates a Unicode property lookup name: lowercase, without spaces, hyphens, or underscores.\n*/\nfunction slug(name: string): string {\n  return name.replace(/[- _]+/g, '').toLowerCase();\n}\n\nfunction throwIfUnclosedCharacterClass<T>(token: T, firstClassToken?: Token): NonNullable<T> {\n  return throwIfNullish(\n    token,\n    // Easier to understand the error if it says \"empty\" when the unclosed class starts with\n    // literal `]`; ex: `[]` or `[]a`\n    `${firstClassToken?.type === 'Character' && firstClassToken.value === 93 ?\n      'Empty' : 'Unclosed'} character class`\n  );\n}\n\nfunction throwIfUnclosedGroup<T>(token: T): NonNullable<T> {\n  return throwIfNullish(token, 'Unclosed group');\n}\n\nexport {\n  type AbsenceFunctionNode,\n  type AlternativeNode,\n  type AlternativeContainerNode,\n  type AlternativeElementNode,\n  type AssertionNode,\n  type BackreferenceNode,\n  type CapturingGroupNode,\n  type CharacterClassElementNode,\n  type CharacterClassNode,\n  type CharacterClassRangeNode,\n  type CharacterNode,\n  type CharacterSetNode,\n  type DirectiveNode,\n  type FlagsNode,\n  type GroupNode,\n  type LookaroundAssertionNode,\n  type NamedCalloutNode,\n  type Node,\n  type NodeAbsenceFunctionKind,\n  type NodeAssertionKind,\n  type NodeCharacterClassKind,\n  type NodeCharacterSetKind,\n  type NodeDirectiveKind,\n  type NodeLookaroundAssertionKind,\n  type NodeQuantifierKind,\n  type OnigurumaAst,\n  type ParentNode,\n  type ParseOptions,\n  type QuantifiableNode,\n  type QuantifierNode,\n  type RegexNode,\n  type SubroutineNode,\n  type UnicodePropertyMap,\n  createAbsenceFunction,\n  createAlternative,\n  createAssertion,\n  createBackreference,\n  createCapturingGroup,\n  createCharacter,\n  createCharacterClass,\n  createCharacterClassRange,\n  createCharacterSet,\n  createDirective,\n  createFlags,\n  createGroup,\n  createLookaroundAssertion,\n  createNamedCallout,\n  createPosixClass,\n  createQuantifier,\n  createRegex,\n  createSubroutine,\n  createUnicodeProperty,\n  hasOnlyChild,\n  isAlternativeContainer,\n  isQuantifiable,\n  parse,\n  slug,\n};\n", "import {cp, r} from './utils.js';\nimport {slug} from 'oniguruma-parser/parser';\n\n// `\\t\\n\\v\\f\\r\\x20`\nconst asciiSpaceChar = '[\\t-\\r ]';\n\nconst CharsWithoutIgnoreCaseExpansion = new Set([\n  cp(0x130), // İ\n  cp(0x131), // ı\n]);\n\n// Different than `PosixClassMap`'s `word`\nconst defaultWordChar = r`[\\p{L}\\p{M}\\p{N}\\p{Pc}]`;\n\nfunction getIgnoreCaseMatchChars(char) {\n  // Some chars should not match the chars they case swap to\n  if (CharsWithoutIgnoreCaseExpansion.has(char)) {\n    return [char];\n  }\n  const set = new Set();\n  const lower = char.toLowerCase();\n  // Everything else is based on `lower`\n  const upper = lower.toUpperCase();\n  const title = LowerToTitleCaseMap.get(lower);\n  const altLower = LowerToAlternativeLowerCaseMap.get(lower);\n  const altUpper = LowerToAlternativeUpperCaseMap.get(lower);\n  // Exclude ucase if multiple chars; count code point length. Excludes ucase versions of German\n  // es-zed 'ß', ligatures like 'ﬀ', and chars with no precomposed ucase like 'ŉ'. See\n  // <unicode.org/Public/UNIDATA/SpecialCasing.txt>\n  if ([...upper].length === 1) {\n    set.add(upper);\n  }\n  altUpper && set.add(altUpper);\n  title && set.add(title);\n  // Lcase of 'İ' is multiple chars, but it's excluded by `CharsWithoutIgnoreCaseExpansion`\n  set.add(lower);\n  altLower && set.add(altLower);\n  return [...set];\n}\n\n// The following set includes:\n// - All ES2024 general categories and their aliases (all are supported by Oniguruma). See\n//   <github.com/mathiasbynens/unicode-match-property-value-ecmascript/blob/main/data/mappings.js>\n// - All ES2024 binary properties and their aliases (all are supported by Oniguruma). See\n//   <tc39.es/ecma262/multipage/text-processing.html#table-binary-unicode-properties>\n// Unicode properties must be mapped to property names supported by JS, and must also apply JS's\n// stricter rules for casing, whitespace, hyphens, and underscores in Unicode property names. In\n// order to remain lightweight, this library assumes properties not in this list are Unicode script\n// names (which require a `Script=` or `sc=` prefix in JS). Unlike JS, Oniguruma doesn't support\n// script extensions, and it supports some properties that aren't supported in JS (including blocks\n// with an `In_` prefix). See also:\n// - Properties supported in Oniguruma: <github.com/kkos/oniguruma/blob/master/doc/UNICODE_PROPERTIES>\n// - Properties supported in JS by spec version: <github.com/eslint-community/regexpp/blob/main/src/unicode/properties.ts>\nconst JsUnicodePropertyMap = /* @__PURE__ */ new Map(\n`C Other\nCc Control cntrl\nCf Format\nCn Unassigned\nCo Private_Use\nCs Surrogate\nL Letter\nLC Cased_Letter\nLl Lowercase_Letter\nLm Modifier_Letter\nLo Other_Letter\nLt Titlecase_Letter\nLu Uppercase_Letter\nM Mark Combining_Mark\nMc Spacing_Mark\nMe Enclosing_Mark\nMn Nonspacing_Mark\nN Number\nNd Decimal_Number digit\nNl Letter_Number\nNo Other_Number\nP Punctuation punct\nPc Connector_Punctuation\nPd Dash_Punctuation\nPe Close_Punctuation\nPf Final_Punctuation\nPi Initial_Punctuation\nPo Other_Punctuation\nPs Open_Punctuation\nS Symbol\nSc Currency_Symbol\nSk Modifier_Symbol\nSm Math_Symbol\nSo Other_Symbol\nZ Separator\nZl Line_Separator\nZp Paragraph_Separator\nZs Space_Separator\nASCII\nASCII_Hex_Digit AHex\nAlphabetic Alpha\nAny\nAssigned\nBidi_Control Bidi_C\nBidi_Mirrored Bidi_M\nCase_Ignorable CI\nCased\nChanges_When_Casefolded CWCF\nChanges_When_Casemapped CWCM\nChanges_When_Lowercased CWL\nChanges_When_NFKC_Casefolded CWKCF\nChanges_When_Titlecased CWT\nChanges_When_Uppercased CWU\nDash\nDefault_Ignorable_Code_Point DI\nDeprecated Dep\nDiacritic Dia\nEmoji\nEmoji_Component EComp\nEmoji_Modifier EMod\nEmoji_Modifier_Base EBase\nEmoji_Presentation EPres\nExtended_Pictographic ExtPict\nExtender Ext\nGrapheme_Base Gr_Base\nGrapheme_Extend Gr_Ext\nHex_Digit Hex\nIDS_Binary_Operator IDSB\nIDS_Trinary_Operator IDST\nID_Continue IDC\nID_Start IDS\nIdeographic Ideo\nJoin_Control Join_C\nLogical_Order_Exception LOE\nLowercase Lower\nMath\nNoncharacter_Code_Point NChar\nPattern_Syntax Pat_Syn\nPattern_White_Space Pat_WS\nQuotation_Mark QMark\nRadical\nRegional_Indicator RI\nSentence_Terminal STerm\nSoft_Dotted SD\nTerminal_Punctuation Term\nUnified_Ideograph UIdeo\nUppercase Upper\nVariation_Selector VS\nWhite_Space space\nXID_Continue XIDC\nXID_Start XIDS`.\n  split(/\\s/).\n  map(p => [slug(p), p])\n);\n\nconst LowerToAlternativeLowerCaseMap = new Map([\n  ['s', cp(0x17F)], // s, ſ\n  [cp(0x17F), 's'], // ſ, s\n]);\n\nconst LowerToAlternativeUpperCaseMap = new Map([\n  [cp(0xDF), cp(0x1E9E)], // ß, ẞ\n  [cp(0x6B), cp(0x212A)], // k, K (Kelvin)\n  [cp(0xE5), cp(0x212B)], // å, Å (Angstrom)\n  [cp(0x3C9), cp(0x2126)], // ω, Ω (Ohm)\n]);\n\n// See <github.com/node-unicode/unicode-16.0.0/tree/main/General_Category/Titlecase_Letter>\nconst LowerToTitleCaseMap = new Map([\n  titleEntry(0x1C5),\n  titleEntry(0x1C8),\n  titleEntry(0x1CB),\n  titleEntry(0x1F2),\n  ...titleRange(0x1F88, 0x1F8F),\n  ...titleRange(0x1F98, 0x1F9F),\n  ...titleRange(0x1FA8, 0x1FAF),\n  titleEntry(0x1FBC),\n  titleEntry(0x1FCC),\n  titleEntry(0x1FFC),\n]);\n\n// Unlike Onig's Unicode properties via `\\p` and `\\P`, these names are case sensitive and don't\n// allow inserting whitespace and underscores. Definitions at\n// <github.com/kkos/oniguruma/blob/master/doc/RE> (see: POSIX bracket: Unicode Case)\n// Note: Handling in the transformer assumes all values here are a single, negateable node that's\n// not pre-negated at the top level. It also uses ASCII versions of `graph` and `print` for target\n// `ES2018` (which doesn't allow intersection) if `accuracy` isn't `strict`\nconst PosixClassMap = new Map([\n  ['alnum', r`[\\p{Alpha}\\p{Nd}]`],\n  ['alpha', r`\\p{Alpha}`],\n  ['ascii', r`\\p{ASCII}`],\n  ['blank', r`[\\p{Zs}\\t]`],\n  ['cntrl', r`\\p{Cc}`],\n  ['digit', r`\\p{Nd}`],\n  ['graph', r`[\\P{space}&&\\P{Cc}&&\\P{Cn}&&\\P{Cs}]`],\n  ['lower', r`\\p{Lower}`],\n  ['print', r`[[\\P{space}&&\\P{Cc}&&\\P{Cn}&&\\P{Cs}]\\p{Zs}]`],\n  ['punct', r`[\\p{P}\\p{S}]`], // Updated value from Onig 6.9.9; changed from Unicode `\\p{punct}`\n  ['space', r`\\p{space}`],\n  ['upper', r`\\p{Upper}`],\n  ['word', r`[\\p{Alpha}\\p{M}\\p{Nd}\\p{Pc}]`],\n  ['xdigit', r`\\p{AHex}`],\n]);\n\nfunction range(start, end) {\n  // const range = Array.from(Array(end + 1 - start), (_, i) => i + start);\n  // const range = Array(end + 1 - start).fill(start).map((x, i) => x + i);\n  const range = [];\n  for (let i = start; i <= end; i++) {\n    range.push(i);\n  }\n  return range;\n}\n\nfunction titleEntry(codePoint) {\n  const char = cp(codePoint);\n  return [char.toLowerCase(), char];\n}\n\nfunction titleRange(start, end) {\n  return range(start, end).map(codePoint => titleEntry(codePoint));\n}\n\nconst UnicodePropertiesWithSpecificCase = new Set([\n  'Lower', 'Lowercase',\n  'Upper', 'Uppercase',\n  'Ll', 'Lowercase_Letter',\n  'Lt', 'Titlecase_Letter',\n  'Lu', 'Uppercase_Letter',\n  // The `Changes_When_*` properties (and their aliases) could be included, but they're very rare.\n  // Some other properties include a handful of chars with specific cases only, but these chars are\n  // generally extreme edge cases and using such properties case insensitively generally produces\n  // undesired behavior anyway\n]);\n\nexport {\n  asciiSpaceChar,\n  defaultWordChar,\n  getIgnoreCaseMatchChars,\n  JsUnicodePropertyMap,\n  PosixClassMap,\n  UnicodePropertiesWithSpecificCase,\n};\n", "import type {AlternativeElementNode, AlternativeNode, CharacterClassElementNode, Node, ParentNode, RegexNode} from '../parser/parse.js';\nimport {throwIfNullish} from '../utils.js';\n\ntype ContainerElementNode =\n  // Used within the `body` container of any `AlternativeContainerNode`\n  AlternativeNode |\n  // Any node type used within the `body` container of an `AlternativeNode`\n  AlternativeElementNode |\n  // Any node type used within the `body` container of a `CharacterClassNode`\n  CharacterClassElementNode;\n\ntype Path<N = Node, Root = RegexNode> = {\n  // The current node being traversed\n  node: N;\n  // Parent node of the current node\n  parent: N extends RegexNode ? null : ParentNode;\n  // String property where the current node in held by the parent node, or numeric index in the\n  // parent's `container` array\n  key: N extends RegexNode ? null : number | string;\n  // Container array holding the current node in the parent node; `null` if the parent isn't a type\n  // that contains a list of nodes\n  container: N extends RegexNode ? null : Array<ContainerElementNode> | null;\n  // Starting node of the AST being traversed; usually a `RegexNode` but can be any node type if\n  // traversing from a midpoint\n  root: Root;\n  // Removes the current node; its kids won't be traversed\n  remove: () => void;\n  // Removes all siblings to the right of the current node, without traversing them; returns the\n  // removed nodes\n  removeAllNextSiblings: () => Array<Node>;\n  // Removes all siblings to the left of the current node, which have already been traversed;\n  // returns the removed nodes\n  removeAllPrevSiblings: () => Array<Node>;\n  // Replaces the current node with a new node; kids of the replaced node won't be traversed;\n  // optionally traverses the new node\n  replaceWith: (newNode: Node, options?: {traverse?: boolean}) => void;\n  // Replaces the current node with multiple new nodes; kids of the replaced node won't be\n  // traversed; optionally traverses the new nodes\n  replaceWithMultiple: (newNodes: Array<Node>, options?: {traverse?: boolean}) => void;\n  // Skips traversing kids of the current node\n  skip: () => void;\n};\n\n// `VisitorNodeFn() {…}` is shorthand for `VisitorNodeFn: {enter() {…}}`.\ntype Visitor<State extends object | null = null, Root extends Node = RegexNode> = {\n  [N in Node as N['type']]?: VisitorNodeFn<Path<N, Root>, State> | {\n    enter?: VisitorNodeFn<Path<N, Root>, State>;\n    exit?: VisitorNodeFn<Path<N, Root>, State>;\n  };\n} & {\n  '*'?: VisitorNodeFn<Path<Node, Root>, State> | {\n    enter?: VisitorNodeFn<Path<Node, Root>, State>;\n    exit?: VisitorNodeFn<Path<Node, Root>, State>;\n  };\n};\n\ntype VisitorNodeFn<P, State> = (path: P, state: State) => void;\n\n/**\nTraverses an AST and calls the provided `visitor`'s node function for each node. Returns the same\nobject, possibly modified.\n\nVisitor node functions can modify the AST in place and use methods on the `path` (provided as their\nfirst argument) to help modify the AST. Provided `state` is passed through to all visitor node\nfunctions as their second argument.\n\nVisitor node functions are called in the following order:\n1. `enter` function of the `'*'` node type (if any)\n2. `enter` function of the given node's type (if any)\n3. [The node's kids (if any) are traversed recursively, unless `skip` is called]\n4. `exit` function of the given node's type (if any)\n5. `exit` function of the `'*'` node type (if any)\n*/\nfunction traverse<State extends object | null = null, Root extends Node = RegexNode>(\n  root: Root,\n  visitor: Visitor<State, Root>,\n  state: State | null = null\n): Root {\n  function traverseArray(array: NonNullable<Path['container']>, parent: Path['parent']) {\n    for (let i = 0; i < array.length; i++) {\n      const keyShift = traverseNode(array[i], parent, i, array);\n      i = Math.max(-1, i + keyShift);\n    }\n  }\n  function traverseNode(\n    node: Path['node'],\n    parent: Path['parent'] = null,\n    key: Path['key'] = null,\n    container: Path['container'] = null\n  ): number {\n    let keyShift = 0;\n    let skipTraversingKidsOfPath = false;\n    const path: Path = {\n      node,\n      parent,\n      key,\n      container,\n      root: root as RegexNode,\n      remove() {\n        arrayContainer(container).splice(Math.max(0, numericKey(key) + keyShift), 1);\n        keyShift--;\n        skipTraversingKidsOfPath = true;\n      },\n      removeAllNextSiblings() {\n        return arrayContainer(container).splice(numericKey(key) + 1);\n      },\n      removeAllPrevSiblings() {\n        const shifted = numericKey(key) + keyShift;\n        keyShift -= shifted;\n        return arrayContainer(container).splice(0, Math.max(0, shifted));\n      },\n      replaceWith(newNode, options = {}) {\n        const traverseNew = !!options.traverse;\n        if (container) {\n          container[Math.max(0, numericKey(key) + keyShift)] = newNode as ContainerElementNode;\n        } else {\n          // `key` will be one of:\n          // - For `CharacterClassRangeNode`: 'min', 'max'\n          // - For `QuantifierNode`: 'body'\n          // - For `RegexNode`: 'flags'\n          // @ts-expect-error\n          throwIfNullish(parent, `Can't replace root node`)[key as string] = newNode;\n        }\n        if (traverseNew) {\n          traverseNode(newNode, parent, key, container);\n        }\n        skipTraversingKidsOfPath = true;\n      },\n      replaceWithMultiple(newNodes, options = {}) {\n        const traverseNew = !!options.traverse;\n        arrayContainer(container).splice(Math.max(0, numericKey(key) + keyShift), 1, ...newNodes);\n        keyShift += newNodes.length - 1;\n        if (traverseNew) {\n          let keyShiftInLoop = 0;\n          for (let i = 0; i < newNodes.length; i++) {\n            keyShiftInLoop += traverseNode(newNodes[i], parent, numericKey(key) + i + keyShiftInLoop, container);\n          }\n        }\n        skipTraversingKidsOfPath = true;\n      },\n      skip() {\n        skipTraversingKidsOfPath = true;\n      },\n    };\n\n    const {type} = node;\n    const anyTypeVisitor = visitor['*'];\n    const thisTypeVisitor = visitor[type];\n    const enterAllFn = typeof anyTypeVisitor === 'function' ? anyTypeVisitor : anyTypeVisitor?.enter;\n    const enterThisFn = typeof thisTypeVisitor === 'function' ? thisTypeVisitor : thisTypeVisitor?.enter;\n    // Type args are too complex to avoid TS errors here, but `VisitorNodeFn`s get correct types\n    // @ts-expect-error\n    enterAllFn?.(path, state);\n    // @ts-expect-error\n    enterThisFn?.(path, state);\n\n    if (!skipTraversingKidsOfPath) {\n      switch (type) {\n        case 'AbsenceFunction':\n        case 'CapturingGroup':\n        case 'Group':\n          traverseArray(node.body, node);\n          break;\n        case 'Alternative':\n        case 'CharacterClass':\n          traverseArray(node.body, node);\n          break;\n        case 'Assertion':\n        case 'Backreference':\n        case 'Character':\n        case 'CharacterSet':\n        case 'Directive':\n        case 'Flags':\n        case 'NamedCallout':\n        case 'Subroutine':\n          break;\n        case 'CharacterClassRange':\n          traverseNode(node.min, node, 'min');\n          traverseNode(node.max, node, 'max');\n          break;\n        case 'LookaroundAssertion':\n          traverseArray(node.body, node);\n          break;\n        case 'Quantifier':\n          traverseNode(node.body, node, 'body');\n          break;\n        case 'Regex':\n          traverseArray(node.body, node);\n          traverseNode(node.flags, node, 'flags');\n          break;\n        default:\n          throw new Error(`Unexpected node type \"${type}\"`);\n      }\n    }\n\n    // @ts-expect-error\n    (thisTypeVisitor as Exclude<typeof thisTypeVisitor, Function>)?.exit?.(path, state);\n    // @ts-expect-error\n    (anyTypeVisitor as Exclude<typeof anyTypeVisitor, Function>)?.exit?.(path, state);\n    return keyShift;\n  }\n  traverseNode(root);\n  return root;\n}\n\nfunction arrayContainer(value: unknown): Array<Node> {\n  if (!Array.isArray(value)) {\n    throw new Error('Container expected');\n  }\n  return value;\n}\n\nfunction numericKey(value: unknown): number {\n  if (typeof value !== 'number') {\n    throw new Error('Numeric key expected');\n  }\n  return value;\n}\n\nexport {\n  type Path,\n  type Visitor,\n  traverse,\n};\n", "import {Accuracy, Target} from './options.js';\nimport {asciiSpaceChar, defaultWordChar, JsUnicodePropertyMap, PosixClassMap} from './unicode.js';\nimport {cp, getNewCurrentFlags, getOrInsert, isMinTarget, r} from './utils.js';\nimport {createAlternative, createAssertion, createBackreference, createCapturingGroup, createCharacter, createCharacterClass, createCharacterSet, createGroup, createLookaroundAssertion, createQuantifier, createSubroutine, createUnicodeProperty, hasOnlyChild, parse, slug} from 'oniguruma-parser/parser';\nimport {traverse} from 'oniguruma-parser/traverser';\n/**\n@import {CapturingGroupNode, OnigurumaAst, Node} from 'oniguruma-parser/parser';\n@import {Visitor} from 'oniguruma-parser/traverser';\n*/\n\n/**\n@typedef {\n  OnigurumaAst & {\n    options: {\n      disable: {[key: string]: boolean};\n      force: {[key: string]: boolean};\n    };\n    _originMap: Map<CapturingGroupNode, CapturingGroupNode>;\n    _strategy: string | null;\n  }\n} RegexPlusAst\n*/\n/**\nTransforms an Oniguruma AST in-place to a [Regex+](https://github.com/slevithan/regex) AST.\nAssumes target ES2025, expecting the generator to down-convert to the desired JS target version.\n\nRegex+'s syntax and behavior is a strict superset of native JavaScript, so the AST is very close\nto representing native ES2025 `RegExp` but with some added features (atomic groups, possessive\nquantifiers, recursion). The AST doesn't use some of Regex+'s extended features like flag x or\nsubroutines because they follow PCRE behavior and work somewhat differently than in Oniguruma. The\nAST represents what's needed to precisely reproduce Oniguruma behavior using Regex+.\n@param {OnigurumaAst} ast\n@param {{\n  accuracy?: keyof Accuracy;\n  asciiWordBoundaries?: boolean;\n  avoidSubclass?: boolean;\n  bestEffortTarget?: keyof Target;\n}} [options]\n@returns {RegexPlusAst}\n*/\nfunction transform(ast, options) {\n  const opts = {\n    // A couple edge cases exist where options `accuracy` and `bestEffortTarget` are used:\n    // - `CharacterSet` kind `text_segment` (`\\X`): An exact representation would require heavy\n    //   Unicode data; a best-effort approximation requires knowing the target.\n    // - `CharacterSet` kind `posix` with values `graph` and `print`: Their complex Unicode\n    //   representations would be hard to change to ASCII versions after the fact in the generator\n    //   based on `target`/`accuracy`, so produce the appropriate structure here.\n    accuracy: 'default',\n    asciiWordBoundaries: false,\n    avoidSubclass: false,\n    bestEffortTarget: 'ES2025',\n    ...options,\n  };\n  // Add `parent` properties to all nodes to help during traversal; also expected by the generator\n  addParentProperties(ast);\n  const firstPassState = {\n    accuracy: opts.accuracy,\n    asciiWordBoundaries: opts.asciiWordBoundaries,\n    avoidSubclass: opts.avoidSubclass,\n    flagDirectivesByAlt: new Map(),\n    jsGroupNameMap: new Map(),\n    minTargetEs2024: isMinTarget(opts.bestEffortTarget, 'ES2024'),\n    passedLookbehind: false,\n    strategy: null,\n    // Subroutines can appear before the groups they ref, so collect reffed nodes for a second pass \n    subroutineRefMap: new Map(),\n    supportedGNodes: new Set(),\n    digitIsAscii: ast.flags.digitIsAscii,\n    spaceIsAscii: ast.flags.spaceIsAscii,\n    wordIsAscii: ast.flags.wordIsAscii,\n  };\n  traverse(ast, FirstPassVisitor, firstPassState);\n  // Global flags modified by the first pass\n  const globalFlags = {\n    dotAll: ast.flags.dotAll,\n    ignoreCase: ast.flags.ignoreCase,\n  };\n  // The interplay of subroutines (with Onig's unique rules/behavior for them; see comments in the\n  // parser for details) with backref multiplexing (a unique Onig feature), flag modifiers, and\n  // duplicate group names (which might be indirectly referenced by subroutines even though\n  // subroutines can't directly reference duplicate names) is extremely complicated to emulate in\n  // JS in a way that handles all edge cases, so we need multiple passes to do it\n  const secondPassState = {\n    currentFlags: globalFlags,\n    prevFlags: null,\n    globalFlags,\n    groupOriginByCopy: new Map(),\n    groupsByName: new Map(),\n    multiplexCapturesToLeftByRef: new Map(),\n    openRefs: new Map(),\n    reffedNodesByReferencer: new Map(),\n    subroutineRefMap: firstPassState.subroutineRefMap,\n  };\n  traverse(ast, SecondPassVisitor, secondPassState);\n  const thirdPassState = {\n    groupsByName: secondPassState.groupsByName,\n    highestOrphanBackref: 0,\n    numCapturesToLeft: 0,\n    reffedNodesByReferencer: secondPassState.reffedNodesByReferencer,\n  };\n  traverse(ast, ThirdPassVisitor, thirdPassState);\n  ast._originMap = secondPassState.groupOriginByCopy;\n  ast._strategy = firstPassState.strategy;\n  return ast;\n}\n\nconst /** @type {Visitor} */ FirstPassVisitor = {\n  AbsenceFunction({node, parent, replaceWith}) {\n    const {body, kind} = node;\n    if (kind === 'repeater') {\n      // Convert `(?~…)` to `(?:(?:(?!…)\\p{Any})*)`\n      const innerGroup = createGroup();\n      innerGroup.body[0].body.push(\n        // Insert own alts as `body`\n        createLookaroundAssertion({negate: true, body}),\n        createUnicodeProperty('Any')\n      );\n      const outerGroup = createGroup();\n      outerGroup.body[0].body.push(\n        createQuantifier('greedy', 0, Infinity, innerGroup)\n      );\n      replaceWith(setParentDeep(outerGroup, parent), {traverse: true});\n    } else {\n      throw new Error(`Unsupported absence function \"(?~|\"`);\n    }\n  },\n\n  Alternative: {\n    enter({node, parent, key}, {flagDirectivesByAlt}) {\n      // Look for own-level flag directives when entering an alternative because after traversing\n      // the directive itself, any subsequent flag directives will no longer be at the same level\n      const flagDirectives = node.body.filter(el => el.kind === 'flags');\n      for (let i = key + 1; i < parent.body.length; i++) {\n        const forwardSiblingAlt = parent.body[i];\n        getOrInsert(flagDirectivesByAlt, forwardSiblingAlt, []).push(...flagDirectives);\n      }\n    },\n    exit({node}, {flagDirectivesByAlt}) {\n      // Wait until exiting to wrap an alternative's nodes with flag groups that extend flag\n      // directives from prior sibling alternatives, because doing this at the end allows inner\n      // nodes to accurately check their level in the tree\n      if (flagDirectivesByAlt.get(node)?.length) {\n        const flags = getCombinedFlagModsFromFlagNodes(flagDirectivesByAlt.get(node));\n        if (flags) {\n          const flagGroup = createGroup({flags});\n          flagGroup.body[0].body = node.body;\n          node.body = [setParentDeep(flagGroup, node)];\n        }\n      }\n    },\n  },\n\n  Assertion({node, parent, key, container, root, remove, replaceWith}, state) {\n    const {kind, negate} = node;\n    const {asciiWordBoundaries, avoidSubclass, supportedGNodes, wordIsAscii} = state;\n    if (kind === 'text_segment_boundary') {\n      // Supported by the parser but not yet for transpilation\n      throw new Error(`Unsupported text segment boundary \"\\\\${negate ? 'Y' : 'y'}\"`);\n    } else if (kind === 'line_end') {\n      replaceWith(setParentDeep(createLookaroundAssertion({body: [\n        createAlternative({body: [createAssertion('string_end')]}),\n        createAlternative({body: [createCharacter(10)]}), // `\\n`\n      ]}), parent));\n    } else if (kind === 'line_start') {\n      // Onig's `^` doesn't match after a string-terminating line feed\n      replaceWith(setParentDeep(parseFragment(r`(?<=\\A|\\n(?!\\z))`, {skipLookbehindValidation: true}), parent));\n    } else if (kind === 'search_start') {\n      if (supportedGNodes.has(node)) {\n        root.flags.sticky = true;\n        remove();\n      } else {\n        const prev = container[key - 1]; // parent.body[key - 1]\n        // Not all ways of blocking the `\\G` from matching are covered here (ex: a node prior to\n        // the `prev` node could block), but blocked `\\G` is an edge case and it's okay if some\n        // blocked cases result in the standard error for being unsupported without a subclass\n        if (prev && isAlwaysNonZeroLength(prev)) {\n          replaceWith(setParentDeep(createLookaroundAssertion({negate: true}), parent));\n        } else if (avoidSubclass) {\n          throw new Error(r`Uses \"\\G\" in a way that requires a subclass`);\n        } else {\n          replaceWith(setParent(createAssertion('string_start'), parent));\n          state.strategy = 'clip_search';\n        }\n      }\n    } else if (kind === 'string_end' || kind === 'string_start') {\n      // Don't need transformation since JS flag m isn't used\n    } else if (kind === 'string_end_newline') {\n      replaceWith(setParentDeep(parseFragment(r`(?=\\n?\\z)`), parent));\n    } else if (kind === 'word_boundary') {\n      if (!wordIsAscii && !asciiWordBoundaries) {\n        const b = `(?:(?<=${defaultWordChar})(?!${defaultWordChar})|(?<!${defaultWordChar})(?=${defaultWordChar}))`;\n        const B = `(?:(?<=${defaultWordChar})(?=${defaultWordChar})|(?<!${defaultWordChar})(?!${defaultWordChar}))`;\n        replaceWith(setParentDeep(parseFragment(negate ? B : b), parent));\n      }\n    } else {\n      throw new Error(`Unexpected assertion kind \"${kind}\"`);\n    }\n  },\n\n  Backreference({node}, {jsGroupNameMap}) {\n    let {ref} = node;\n    if (typeof ref === 'string' && !isValidJsGroupName(ref)) {\n      ref = getAndStoreJsGroupName(ref, jsGroupNameMap);\n      node.ref = ref;\n    }\n  },\n\n  CapturingGroup({node}, {jsGroupNameMap, subroutineRefMap}) {\n    let {name} = node;\n    if (name && !isValidJsGroupName(name)) {\n      name = getAndStoreJsGroupName(name, jsGroupNameMap);\n      node.name = name;\n    }\n    subroutineRefMap.set(node.number, node);\n    if (name) {\n      subroutineRefMap.set(name, node);\n    }\n  },\n\n  CharacterClassRange({node, parent, replaceWith}) {\n    if (parent.kind === 'intersection') {\n      // JS doesn't allow intersection with ranges without a wrapper class\n      const cc = createCharacterClass({body: [node]});\n      replaceWith(setParentDeep(cc, parent), {traverse: true});\n    }\n  },\n\n  CharacterSet({node, parent, replaceWith}, {accuracy, minTargetEs2024, digitIsAscii, spaceIsAscii, wordIsAscii}) {\n    const {kind, negate, value} = node;\n    // Flag D with `\\d`, `\\p{Digit}`, `[[:digit:]]`\n    if (digitIsAscii && (kind === 'digit' || value === 'digit')) {\n      replaceWith(setParent(createCharacterSet('digit', {negate}), parent));\n      return;\n    }\n    // Flag S with `\\s`, `\\p{Space}`, `[[:space:]]`\n    if (spaceIsAscii && (kind === 'space' || value === 'space')) {\n      replaceWith(setParentDeep(setNegate(parseFragment(asciiSpaceChar), negate), parent));\n      return;\n    }\n    // Flag W with `\\w`, `\\p{Word}`, `[[:word:]]`\n    if (wordIsAscii && (kind === 'word' || value === 'word')) {\n      replaceWith(setParent(createCharacterSet('word', {negate}), parent));\n      return;\n    }\n    if (kind === 'any') {\n      replaceWith(setParent(createUnicodeProperty('Any'), parent));\n    } else if (kind === 'digit') {\n      replaceWith(setParent(createUnicodeProperty('Nd', {negate}), parent));\n    } else if (kind === 'dot') {\n      // No-op; doesn't need transformation\n    } else if (kind === 'text_segment') {\n      if (accuracy === 'strict') {\n        throw new Error(r`Use of \"\\X\" requires non-strict accuracy`);\n      }\n      // Emoji pattern based on <github.com/slevithan/emoji-regex-xs> but adapted for our use case\n      // Note: Not using raw strings to work around Bun ≤ 1.1.34 issue <github.com/oven-sh/bun/issues/7540>\n      const eBase = '\\\\p{Emoji}(?:\\\\p{EMod}|\\\\uFE0F\\\\u20E3?|[\\\\x{E0020}-\\\\x{E007E}]+\\\\x{E007F})?';\n      const emoji = r`\\p{RI}{2}|${eBase}(?:\\u200D${eBase})*`;\n      replaceWith(setParentDeep(parseFragment(\n        // Close approximation of an extended grapheme cluster; see: <unicode.org/reports/tr29/>\n        r`(?>\\r\\n|${minTargetEs2024 ? r`\\p{RGI_Emoji}` : emoji}|\\P{M}\\p{M}*)`,\n        // Allow JS property `RGI_Emoji` through\n        {skipPropertyNameValidation: true}\n      ), parent));\n    } else if (kind === 'hex') {\n      replaceWith(setParent(createUnicodeProperty('AHex', {negate}), parent));\n    } else if (kind === 'newline') {\n      replaceWith(setParentDeep(parseFragment(negate ? '[^\\n]' : '(?>\\r\\n?|[\\n\\v\\f\\x85\\u2028\\u2029])'), parent));\n    } else if (kind === 'posix') {\n      if (!minTargetEs2024 && (value === 'graph' || value === 'print')) {\n        if (accuracy === 'strict') {\n          throw new Error(`POSIX class \"${value}\" requires min target ES2024 or non-strict accuracy`);\n        }\n        let ascii = {\n          graph: '!-~',\n          print: ' -~',\n        }[value];\n        if (negate) {\n          // POSIX classes are always nested in a char class; manually invert the range rather than\n          // using `[^…]` so it can be unwrapped since ES2018 doesn't support nested classes\n          ascii = `\\0-${cp(ascii.codePointAt(0) - 1)}${cp(ascii.codePointAt(2) + 1)}-\\u{10FFFF}`;\n        }\n        replaceWith(setParentDeep(parseFragment(`[${ascii}]`), parent));\n      } else {\n        replaceWith(setParentDeep(setNegate(parseFragment(PosixClassMap.get(value)), negate), parent));\n      }\n    } else if (kind === 'property') {\n      if (!JsUnicodePropertyMap.has(slug(value))) {\n        // Assume it's a script; no error checking is the price for avoiding heavyweight Unicode\n        // data for all script names\n        node.key = 'sc';\n      }\n    } else if (kind === 'space') {\n      // Can't use JS's Unicode `\\s` since unlike Onig it includes `\\uFEFF` and excludes `\\x85`\n      replaceWith(setParent(createUnicodeProperty('space', {negate}), parent));\n    } else if (kind === 'word') {\n      replaceWith(setParentDeep(setNegate(parseFragment(defaultWordChar), negate), parent));\n    } else {\n      throw new Error(`Unexpected character set kind \"${kind}\"`);\n    }\n  },\n\n  Directive({node, parent, root, remove, replaceWith, removeAllPrevSiblings, removeAllNextSiblings}) {\n    const {kind, flags} = node;\n    if (kind === 'flags') {\n      if (!flags.enable && !flags.disable) {\n        // Flag directive without flags; ex: `(?-)`, `(?--)`\n        remove();\n      } else {\n        const flagGroup = createGroup({flags});\n        flagGroup.body[0].body = removeAllNextSiblings();\n        replaceWith(setParentDeep(flagGroup, parent), {traverse: true});\n      }\n    } else if (kind === 'keep') {\n      const firstAlt = root.body[0];\n      // Supporting a full-pattern wrapper around `\\K` enables use with flag modifiers\n      const hasWrapperGroup =\n        root.body.length === 1 &&\n        // Not emulatable if within a `CapturingGroup`\n        hasOnlyChild(firstAlt, {type: 'Group'}) &&\n        firstAlt.body[0].body.length === 1;\n      const topLevel = hasWrapperGroup ? firstAlt.body[0] : root;\n      if (parent.parent !== topLevel || topLevel.body.length > 1) {\n        throw new Error(r`Uses \"\\K\" in a way that's unsupported`);\n      }\n      const lookbehind = createLookaroundAssertion({behind: true});\n      lookbehind.body[0].body = removeAllPrevSiblings();\n      replaceWith(setParentDeep(lookbehind, parent));\n    } else {\n      throw new Error(`Unexpected directive kind \"${kind}\"`);\n    }\n  },\n\n  Flags({node, parent}) {\n    // Throw for flags supported by the parser but not yet for transpilation\n    if (node.posixIsAscii) {\n      throw new Error('Unsupported flag \"P\"');\n    }\n    if (node.textSegmentMode === 'word') {\n      throw new Error('Unsupported flag \"y{w}\"');\n    }\n    // Remove Onig flags that aren't available in JS\n    [ 'digitIsAscii', // Flag D\n      'extended', // Flag x\n      'posixIsAscii', // Flag P\n      'spaceIsAscii', // Flag S\n      'wordIsAscii', // Flag W\n      'textSegmentMode', // Flag y{g} or y{w}\n    ].forEach(f => delete node[f]);\n    Object.assign(node, {\n      // JS flag g; no Onig equiv\n      global: false,\n      // JS flag d; no Onig equiv\n      hasIndices: false,\n      // JS flag m; no Onig equiv but its behavior is always on in Onig. Onig's only line break\n      // char is line feed, unlike JS, so this flag isn't used since it would produce inaccurate\n      // results (also allows `^` and `$` to be used in the generator for string start and end)\n      multiline: false,\n      // JS flag y; no Onig equiv, but used for `\\G` emulation\n      sticky: node.sticky ?? false,\n      // Note: Regex+ doesn't allow explicitly adding flags it handles implicitly, so leave out\n      // properties `unicode` (JS flag u) and `unicodeSets` (JS flag v). Keep the existing values\n      // for `ignoreCase` (flag i) and `dotAll` (JS flag s, but Onig flag m)\n    });\n    // Options accepted by Regex+; see <github.com/slevithan/regex#-options>\n    parent.options = {\n      disable: {\n        // Onig uses different rules for flag x than Regex+, so disable the implicit flag\n        x: true,\n        // Onig has no flag to control \"named capture only\" mode but contextually applies its\n        // behavior when named capturing is used, so disable Regex+'s implicit flag for it\n        n: true,\n      },\n      force: {\n        // Always add flag v because we're generating an AST that relies on it (it enables JS\n        // support for Onig features nested classes, intersection, Unicode properties, etc.).\n        // However, the generator might disable flag v based on its `target` option\n        v: true,\n      },\n    };\n  },\n\n  Group({node}) {\n    if (!node.flags) {\n      return;\n    }\n    const {enable, disable} = node.flags;\n    // Onig's flag x (`extended`) isn't available in JS\n    enable?.extended && delete enable.extended;\n    disable?.extended && delete disable.extended;\n    // JS doesn't support flag groups that enable and disable the same flag; ex: `(?i-i:)`\n    enable?.dotAll && disable?.dotAll && delete enable.dotAll;\n    enable?.ignoreCase && disable?.ignoreCase && delete enable.ignoreCase;\n    // Cleanup\n    enable && !Object.keys(enable).length && delete node.flags.enable;\n    disable && !Object.keys(disable).length && delete node.flags.disable;\n    !node.flags.enable && !node.flags.disable && delete node.flags;\n  },\n\n  LookaroundAssertion({node}, state) {\n    const {kind} = node;\n    if (kind === 'lookbehind') {\n      state.passedLookbehind = true;\n    }\n  },\n\n  NamedCallout({node, parent, replaceWith}) {\n    const {kind} = node;\n    if (kind === 'fail') {\n      replaceWith(setParentDeep(createLookaroundAssertion({negate: true}), parent));\n    } else {\n      throw new Error(`Unsupported named callout \"(*${kind.toUpperCase()}\"`);\n    }\n  },\n\n  Quantifier({node}) {\n    if (node.body.type === 'Quantifier') {\n      // Change e.g. `a**` to `(?:a*)*`\n      const group = createGroup();\n      group.body[0].body.push(node.body);\n      node.body = setParentDeep(group, node);\n    }\n  },\n\n  Regex: {\n    enter({node}, {supportedGNodes}) {\n      // For `\\G` to be accurately emulatable using JS flag y, it must be at (and only at) the start\n      // of every top-level alternative (with complex rules for what determines being at the start).\n      // Additional `\\G` error checking in `Assertion` visitor\n      const leadingGs = [];\n      let hasAltWithLeadG = false;\n      let hasAltWithoutLeadG = false;\n      for (const alt of node.body) {\n        if (alt.body.length === 1 && alt.body[0].kind === 'search_start') {\n          // Remove the `\\G` (leaving behind an empty alternative, and without adding JS flag y)\n          // since a top-level alternative that includes only `\\G` always matches at the start of the\n          // match attempt. Note that this is based on Oniguruma's rules, and is different than other\n          // regex flavors where `\\G` matches at the end of the previous match (a subtle distinction\n          // that's relevant after zero-length matches)\n          alt.body.pop();\n        } else {\n          const leadingG = getLeadingG(alt.body);\n          if (leadingG) {\n            hasAltWithLeadG = true;\n            Array.isArray(leadingG) ?\n              leadingGs.push(...leadingG) :\n              leadingGs.push(leadingG);\n          } else {\n            hasAltWithoutLeadG = true;\n          }\n        }\n      }\n      if (hasAltWithLeadG && !hasAltWithoutLeadG) {\n        // Supported `\\G` nodes will be removed (and add flag y) when traversed\n        leadingGs.forEach(g => supportedGNodes.add(g));\n      }\n    },\n    exit(_, {accuracy, passedLookbehind, strategy}) {\n      if (accuracy === 'strict' && passedLookbehind && strategy) {\n        throw new Error(r`Uses \"\\G\" in a way that requires non-strict accuracy`);\n      }\n    },\n  },\n\n  Subroutine({node}, {jsGroupNameMap}) {\n    let {ref} = node;\n    if (typeof ref === 'string' && !isValidJsGroupName(ref)) {\n      ref = getAndStoreJsGroupName(ref, jsGroupNameMap);\n      node.ref = ref;\n    }\n  },\n};\n\nconst /** @type {Visitor} */ SecondPassVisitor = {\n  Backreference({node}, {multiplexCapturesToLeftByRef, reffedNodesByReferencer}) {\n    const {orphan, ref} = node;\n    if (!orphan) {\n      // Copy the current state for later multiplexing expansion. That's done in a subsequent pass\n      // because backref numbers need to be recalculated after subroutine expansion\n      reffedNodesByReferencer.set(node, [...multiplexCapturesToLeftByRef.get(ref).map(({node}) => node)]);\n    }\n  },\n\n  CapturingGroup: {\n    enter(\n      { node,\n        parent,\n        replaceWith,\n        skip,\n      },\n      { groupOriginByCopy,\n        groupsByName,\n        multiplexCapturesToLeftByRef,\n        openRefs,\n        reffedNodesByReferencer,\n      }\n    ) {\n      // Has value if we're within a subroutine expansion\n      const origin = groupOriginByCopy.get(node);\n\n      // ## Handle recursion; runs after subroutine expansion\n      if (origin && openRefs.has(node.number)) {\n        // Recursive subroutines don't affect any following backrefs to their `ref` (unlike other\n        // subroutines), so don't wrap with a capture. The reffed group might have its name removed\n        // due to later subroutine expansion\n        const recursion = setParent(createRecursion(node.number), parent);\n        reffedNodesByReferencer.set(recursion, openRefs.get(node.number));\n        replaceWith(recursion);\n        return;\n      }\n      openRefs.set(node.number, node);\n\n      // ## Track data for backref multiplexing\n      multiplexCapturesToLeftByRef.set(node.number, []);\n      if (node.name) {\n        getOrInsert(multiplexCapturesToLeftByRef, node.name, []);\n      }\n      const multiplexNodes = multiplexCapturesToLeftByRef.get(node.name ?? node.number);\n      for (let i = 0; i < multiplexNodes.length; i++) {\n        // Captures added via subroutine expansion (maybe indirectly because they were descendant\n        // captures of the reffed group or in a nested subroutine expansion) form a set with their\n        // origin group and any other copies of it added via subroutines. Only the most recently\n        // matched within this set is added to backref multiplexing. So search the list of already-\n        // tracked multiplexed nodes for this group name or number to see if there's a node being\n        // replaced by this capture\n        const multiplex = multiplexNodes[i];\n        if (\n          // This group is from subroutine expansion, and there's a multiplex value from either the\n          // origin node or a prior subroutine expansion group with the same origin\n          (origin === multiplex.node || (origin && origin === multiplex.origin)) ||\n          // This group is not from subroutine expansion, and it comes after a subroutine expansion\n          // group that refers to this group\n          node === multiplex.origin\n        ) {\n          multiplexNodes.splice(i, 1);\n          break;\n        }\n      }\n      multiplexCapturesToLeftByRef.get(node.number).push({node, origin});\n      if (node.name) {\n        multiplexCapturesToLeftByRef.get(node.name).push({node, origin});\n      }\n\n      // ## Track data for duplicate names\n      // Pre-ES2025 doesn't allow duplicate names, but ES2025 allows duplicate names that are\n      // unique per mutually exclusive alternation path. However, Oniguruma's handling for named\n      // subpatterns on match results means we can't use this ES2025 feature even when in an ES2025\n      // env. So, if using a duplicate name, remove the name from all but the first instance that\n      // wasn't created by subroutine expansion\n      if (node.name) {\n        const groupsWithSameName = getOrInsert(groupsByName, node.name, new Map());\n        let hasDuplicateNameToRemove = false;\n        if (origin) {\n          // Subroutines and their child captures shouldn't hold duplicate names in the final state\n          hasDuplicateNameToRemove = true;\n        } else {\n          for (const groupInfo of groupsWithSameName.values()) {\n            if (!groupInfo.hasDuplicateNameToRemove) {\n              // Will change to an unnamed capture in a later pass\n              hasDuplicateNameToRemove = true;\n              break;\n            }\n          }\n        }\n        groupsByName.get(node.name).set(node, {node, hasDuplicateNameToRemove});\n      }\n    },\n    exit({node}, {openRefs}) {\n      openRefs.delete(node.number);\n    },\n  },\n\n  Group: {\n    enter({node}, state) {\n      // Flag directives have already been converted to flag groups by the previous pass\n      state.prevFlags = state.currentFlags;\n      if (node.flags) {\n        state.currentFlags = getNewCurrentFlags(state.currentFlags, node.flags);\n      }\n    },\n    exit(_, state) {\n      state.currentFlags = state.prevFlags;\n    },\n  },\n\n  Subroutine({node, parent, replaceWith}, state) {\n    const {isRecursive, ref} = node;\n\n    // Subroutine nodes with `isRecursive` are created during the current traversal; they're only\n    // traversed here if a recursive subroutine created during traversal is then copied by a\n    // subroutine expansion, e.g. with `(?<a>\\g<a>)\\g<a>`\n    if (isRecursive) {\n      // Immediate parent is an alternative or quantifier; can skip\n      let reffed = parent;\n      while ((reffed = reffed.parent)) {\n        if (reffed.type === 'CapturingGroup' && (reffed.name === ref || reffed.number === ref)) {\n          break;\n        }\n      }\n      // Track the referenced node because `ref`s are rewritten in a subsequent pass; capturing\n      // group names and numbers might change due to subroutine expansion and duplicate group names\n      state.reffedNodesByReferencer.set(node, reffed);\n      return;\n    }\n\n    const reffedGroupNode = state.subroutineRefMap.get(ref);\n    // Other forms of recursion are handled by the `CapturingGroup` visitor\n    const isGlobalRecursion = ref === 0;\n    const expandedSubroutine = isGlobalRecursion ?\n      createRecursion(0) :\n      // The reffed group might itself contain subroutines, which are expanded during sub-traversal\n      cloneCapturingGroup(reffedGroupNode, state.groupOriginByCopy, null);\n    let replacement = expandedSubroutine;\n    if (!isGlobalRecursion) {\n      // Subroutines take their flags from the reffed group, not the flags surrounding themselves\n      const reffedGroupFlagMods = getCombinedFlagModsFromFlagNodes(getAllParents(\n        reffedGroupNode,\n        p => p.type === 'Group' && !!p.flags\n      ));\n      const reffedGroupFlags = reffedGroupFlagMods ?\n        getNewCurrentFlags(state.globalFlags, reffedGroupFlagMods) :\n        state.globalFlags;\n      if (!areFlagsEqual(reffedGroupFlags, state.currentFlags)) {\n        replacement = createGroup({\n          flags: getFlagModsFromFlags(reffedGroupFlags),\n        });\n        replacement.body[0].body.push(expandedSubroutine);\n      }\n    }\n    replaceWith(setParentDeep(replacement, parent), {traverse: !isGlobalRecursion});\n  },\n};\n\nconst /** @type {Visitor} */ ThirdPassVisitor = {\n  Backreference({node, parent, replaceWith}, state) {\n    if (node.orphan) {\n      state.highestOrphanBackref = Math.max(state.highestOrphanBackref, node.ref);\n      // Don't renumber; used with `allowOrphanBackrefs`\n      return;\n    }\n    const reffedNodes = state.reffedNodesByReferencer.get(node);\n    const participants = reffedNodes.filter(reffed => canParticipateWithNode(reffed, node));\n    // For the backref's `ref`, use `number` rather than `name` because group names might have been\n    // removed if they're duplicates within their alternation path, or they might be removed later\n    // by the generator (depending on target) if they're duplicates within the overall pattern.\n    // Backrefs must come after groups they ref, so reffed node `number`s are already recalculated\n    if (!participants.length) {\n      // If no participating capture, convert backref to to `(?!)`; backrefs to nonparticipating\n      // groups can't match in Onig but match the empty string in JS\n      replaceWith(setParentDeep(createLookaroundAssertion({negate: true}), parent));\n    } else if (participants.length > 1) {\n      // Multiplex for backrefs to duplicate capture names; try them in reverse order\n      const group = createGroup({\n        atomic: true,\n        body: participants.reverse().map(reffed => createAlternative({\n          body: [createBackreference(reffed.number)],\n        })),\n      });\n      replaceWith(setParentDeep(group, parent));\n    } else {\n      node.ref = participants[0].number;\n    }\n  },\n\n  CapturingGroup({node}, state) {\n    // Recalculate the number since the current value might be wrong due to subroutine expansion\n    node.number = ++state.numCapturesToLeft;\n    if (node.name) {\n      // Removing duplicate names here rather than in an earlier pass avoids extra complexity when\n      // handling subroutine expansion and backref multiplexing\n      if (state.groupsByName.get(node.name).get(node).hasDuplicateNameToRemove) {\n        delete node.name;\n      }\n    }\n  },\n\n  Regex: {\n    exit({node}, state) {\n      // HACK: Add unnamed captures to the end of the regex if needed to allow orphaned backrefs\n      // to be valid in JS with flag u/v. This is needed to support TextMate grammars, which\n      // replace numbered backrefs in their `end` pattern with values matched by captures in their\n      // `begin` pattern! See <github.com/microsoft/vscode-textmate/blob/7e0ea282f4f25fef12a6c84fa4fa7266f67b58dc/src/rule.ts#L661-L663>\n      // An `end` pattern, prior to this substitution, might have backrefs to a group that doesn't\n      // exist within `end`. This presents a dilemma since both Oniguruma and JS (with flag u/v)\n      // error for backrefs to undefined captures. So adding captures to the end is a solution that\n      // doesn't change what the regex matches, and lets invalid numbered backrefs through. Note:\n      // Orphan backrefs are only allowed if `allowOrphanBackrefs` is enabled\n      const numCapsNeeded = Math.max(state.highestOrphanBackref - state.numCapturesToLeft, 0);\n      for (let i = 0; i < numCapsNeeded; i++) {\n        const emptyCapture = createCapturingGroup();\n        node.body.at(-1).body.push(emptyCapture);\n      }\n    },\n  },\n\n  Subroutine({node}, state) {\n    if (!node.isRecursive || node.ref === 0) {\n      return;\n    }\n    // For the recursion's `ref`, use `number` rather than `name` because group names might have\n    // been removed if they're duplicates within their alternation path, or they might be removed\n    // later by the generator (depending on target) if they're duplicates within the overall\n    // pattern. Since recursion appears within the group it refs, the reffed node's `number` has\n    // already been recalculated\n    node.ref = state.reffedNodesByReferencer.get(node).number;\n  },\n};\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\nfunction addParentProperties(root) {\n  traverse(root, {\n    '*'({node, parent}) {\n      node.parent = parent;\n    },\n  });\n}\n\nfunction areFlagsEqual(a, b) {\n  return a.dotAll === b.dotAll && a.ignoreCase === b.ignoreCase;\n}\n\nfunction canParticipateWithNode(capture, node) {\n  // Walks to the left (prev siblings), down (sibling descendants), up (parent), then back down\n  // (parent's prev sibling descendants) the tree in a loop\n  let rightmostPoint = node;\n  do {\n    if (rightmostPoint.type === 'Regex') {\n      // End of the line; capture is not in node's alternation path\n      return false;\n    }\n    if (rightmostPoint.type === 'Alternative') {\n      // Skip past alts to their parent because we don't want to look at the kids of preceding alts\n      continue;\n    }\n    if (rightmostPoint === capture) {\n      // Capture is ancestor of node\n      return false;\n    }\n    const kidsOfParent = getKids(rightmostPoint.parent);\n    for (const kid of kidsOfParent) {\n      if (kid === rightmostPoint) {\n        // Reached rightmost node in sibling list that we want to consider; break to parent loop\n        break;\n      }\n      if (kid === capture || isAncestorOf(kid, capture)) {\n        return true;\n      }\n    }\n  } while ((rightmostPoint = rightmostPoint.parent));\n  throw new Error('Unexpected path');\n}\n\n// Creates a deep copy of the provided node, with special handling:\n// - Make `parent` props point to their parent in the copy\n// - Update the provided `originMap` for each cloned capturing group (outer and nested)\nfunction cloneCapturingGroup(obj, originMap, up, up2) {\n  const store = Array.isArray(obj) ? [] : {};\n  for (const [key, value] of Object.entries(obj)) {\n    if (key === 'parent') {\n      // If the last cloned item was a container array (for holding kids), use the object above it\n      store.parent = Array.isArray(up) ? up2 : up;\n    } else if (value && typeof value === 'object') {\n      store[key] = cloneCapturingGroup(value, originMap, store, up);\n    } else {\n      if (key === 'type' && value === 'CapturingGroup') {\n        // Key is the copied node, value is the origin node\n        originMap.set(store, originMap.get(obj) ?? obj);\n      }\n      store[key] = value;\n    }\n  }\n  return store;\n}\n\nfunction createRecursion(ref) {\n  const node = createSubroutine(ref);\n  // In the future, the parser will set a `recursive` property on subroutines:\n  // <github.com/slevithan/oniguruma-parser/issues/3>. When that's available, this function won't\n  // be needed and the related logic in this transformer should change (simplify) to use it\n  node.isRecursive = true;\n  return node;\n}\n\nfunction getAllParents(node, filterFn) {\n  const results = [];\n  while ((node = node.parent)) {\n    if (!filterFn || filterFn(node)) {\n      results.push(node);\n    }\n  }\n  return results;\n}\n\n// See also `isValidJsGroupName`\nfunction getAndStoreJsGroupName(name, map) {\n  if (map.has(name)) {\n    return map.get(name);\n  }\n  // Onig group names can't start with `$`, but JS names can\n  const jsName = `$${map.size}_${name.replace(/^[^$_\\p{IDS}]|[^$\\u200C\\u200D\\p{IDC}]/ug, '_')}`;\n  map.set(name, jsName);\n  return jsName;\n}\n\nfunction getCombinedFlagModsFromFlagNodes(flagNodes) {\n  const flagProps = ['dotAll', 'ignoreCase'];\n  const combinedFlags = {enable: {}, disable: {}};\n  flagNodes.forEach(({flags}) => {\n    flagProps.forEach(prop => {\n      if (flags.enable?.[prop]) {\n        // Need to remove `disable` since disabled flags take precedence\n        delete combinedFlags.disable[prop];\n        combinedFlags.enable[prop] = true;\n      }\n      if (flags.disable?.[prop]) {\n        combinedFlags.disable[prop] = true;\n      }\n    });\n  });\n  if (!Object.keys(combinedFlags.enable).length) {\n    delete combinedFlags.enable;\n  }\n  if (!Object.keys(combinedFlags.disable).length) {\n    delete combinedFlags.disable;\n  }\n  if (combinedFlags.enable || combinedFlags.disable) {\n    return combinedFlags;\n  }\n  return null;\n}\n\nfunction getFlagModsFromFlags({dotAll, ignoreCase}) {\n  const mods = {};\n  if (dotAll || ignoreCase) {\n    mods.enable = {};\n    dotAll && (mods.enable.dotAll = true);\n    ignoreCase && (mods.enable.ignoreCase = true);\n  }\n  if (!dotAll || !ignoreCase) {\n    mods.disable = {};\n    !dotAll && (mods.disable.dotAll = true);\n    !ignoreCase && (mods.disable.ignoreCase = true);\n  }\n  return mods;\n}\n\nfunction getKids(node) {\n  if (!node) {\n    throw new Error('Node expected');\n  }\n  // NOTE: Not handling `CharacterClassRange`'s `min`/`max` and `Regex`'s `flags`, only because\n  // they haven't been needed by current callers\n  const {body} = node;\n  return Array.isArray(body) ? body : (body ? [body] : null);\n}\n\nfunction getLeadingG(els) {\n  const firstToConsider = els.find(el => (\n    el.kind === 'search_start' ||\n    isLoneGLookaround(el, {negate: false}) ||\n    !isAlwaysZeroLength(el)\n  ));\n  if (!firstToConsider) {\n    return null;\n  }\n  if (firstToConsider.kind === 'search_start') {\n    return firstToConsider;\n  }\n  if (firstToConsider.type === 'LookaroundAssertion') {\n    return firstToConsider.body[0].body[0];\n  }\n  if (firstToConsider.type === 'CapturingGroup' || firstToConsider.type === 'Group') {\n    const gNodesForGroup = [];\n    // Recursively find `\\G` nodes for all alternatives in the group\n    for (const alt of firstToConsider.body) {\n      const leadingG = getLeadingG(alt.body);\n      if (!leadingG) {\n        // Don't return `gNodesForGroup` collected so far since this alt didn't qualify\n        return null;\n      }\n      Array.isArray(leadingG) ?\n        gNodesForGroup.push(...leadingG) :\n        gNodesForGroup.push(leadingG);\n    }\n    return gNodesForGroup;\n  }\n  return null;\n}\n\nfunction isAncestorOf(node, descendant) {\n  const kids = getKids(node) ?? [];\n  for (const kid of kids) {\n    if (kid === descendant || isAncestorOf(kid, descendant)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n@param {Node} node\n@returns {boolean}\n*/\nfunction isAlwaysZeroLength({type}) {\n  return (\n    type === 'Assertion' ||\n    type === 'Directive' ||\n    type === 'LookaroundAssertion'\n  );\n}\n\n/**\n@param {Node} node\n@returns {boolean}\n*/\nfunction isAlwaysNonZeroLength(node) {\n  const types = [\n    'Character',\n    'CharacterClass',\n    'CharacterSet',\n  ];\n  return types.includes(node.type) || (\n    node.type === 'Quantifier' &&\n    node.min &&\n    types.includes(node.body.type)\n  );\n}\n\nfunction isLoneGLookaround(node, options) {\n  const opts = {\n    negate: null,\n    ...options,\n  };\n  return (\n    node.type === 'LookaroundAssertion' &&\n    (opts.negate === null || node.negate === opts.negate) &&\n    node.body.length === 1 &&\n    hasOnlyChild(node.body[0], {\n      type: 'Assertion',\n      kind: 'search_start',\n    })\n  );\n}\n\n// See also `getAndStoreJsGroupName`\nfunction isValidJsGroupName(name) {\n  // JS group names are more restrictive than Onig; see\n  // <developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Lexical_grammar#identifiers>\n  return /^[$_\\p{IDS}][$\\u200C\\u200D\\p{IDC}]*$/u.test(name);\n}\n\n// Returns a single node, either the given node or all nodes wrapped in a noncapturing group\nfunction parseFragment(pattern, options) {\n  const ast = parse(pattern, {\n    ...options,\n    // Providing a custom set of Unicode property names avoids converting some JS Unicode\n    // properties (ex: `\\p{Alpha}`) to Onig POSIX classes\n    unicodePropertyMap: JsUnicodePropertyMap,\n  });\n  const alts = ast.body;\n  if (alts.length > 1 || alts[0].body.length > 1) {\n    return createGroup({body: alts});\n  }\n  return alts[0].body[0];\n}\n\nfunction setNegate(node, negate) {\n  node.negate = negate;\n  return node;\n}\n\nfunction setParent(node, parent) {\n  node.parent = parent;\n  return node;\n}\n\nfunction setParentDeep(node, parent) {\n  addParentProperties(node);\n  node.parent = parent;\n  return node;\n}\n\nexport {\n  transform,\n};\n", "import {getOptions} from './options.js';\nimport {getIgnoreCaseMatchChars, UnicodePropertiesWithSpecificCase} from './unicode.js';\nimport {cp, envFlags, getNewCurrentFlags, getOrInsert, isMinTarget, r, throwIfNullish} from './utils.js';\nimport {createAlternative, createCharacter, createGroup} from 'oniguruma-parser/parser';\nimport {traverse} from 'oniguruma-parser/traverser';\n/**\n@import {ToRegExpOptions} from './index.js';\n@import {RegexPlusAst} from './transform.js';\n@import {AlternativeNode, AssertionNode, BackreferenceNode, CapturingGroupNode, CharacterClassNode, CharacterClassRangeNode, CharacterNode, CharacterSetNode, FlagsNode, GroupNode, LookaroundAssertionNode, Node, QuantifierNode, SubroutineNode} from 'oniguruma-parser/parser';\n@import {Visitor} from 'oniguruma-parser/traverser';\n*/\n\n/**\nGenerates a Regex+ compatible `pattern`, `flags`, and `options` from a Regex+ AST.\n@param {RegexPlusAst} ast\n@param {ToRegExpOptions} [options]\n@returns {{\n  pattern: string;\n  flags: string;\n  options: Object;\n  _captureTransfers: Map<number, Array<number>>;\n  _hiddenCaptures: Array<number>;\n}}\n*/\nfunction generate(ast, options) {\n  const opts = getOptions(options);\n  const minTargetEs2024 = isMinTarget(opts.target, 'ES2024');\n  const minTargetEs2025 = isMinTarget(opts.target, 'ES2025');\n  const recursionLimit = opts.rules.recursionLimit;\n  if (!Number.isInteger(recursionLimit) || recursionLimit < 2 || recursionLimit > 20) {\n    throw new Error('Invalid recursionLimit; use 2-20');\n  }\n\n  // If the output can't use flag groups, we need a pre-pass to check for the use of chars with\n  // case in case sensitive/insensitive states. This minimizes the need for case expansions (though\n  // expansions are lossless, even given Unicode case complexities) and allows supporting case\n  // insensitive backrefs in more cases\n  // TODO: Consider gathering this data in the transformer's final traversal to avoid work here\n  let hasCaseInsensitiveNode = null;\n  let hasCaseSensitiveNode = null;\n  if (!minTargetEs2025) {\n    const iStack = [ast.flags.ignoreCase];\n    traverse(ast, FlagModifierVisitor, {\n      getCurrentModI: () => iStack.at(-1),\n      popModI() {iStack.pop()},\n      pushModI(isIOn) {iStack.push(isIOn)},\n      setHasCasedChar() {\n        if (iStack.at(-1)) {\n          hasCaseInsensitiveNode = true;\n        } else {\n          hasCaseSensitiveNode = true;\n        }\n      },\n    });\n  }\n\n  const appliedGlobalFlags = {\n    dotAll: ast.flags.dotAll,\n    // - Turn global flag i on if a case insensitive node was used and no case sensitive nodes were\n    //   used (to avoid unnecessary node expansion).\n    // - Turn global flag i off if a case sensitive node was used (since case sensitivity can't be\n    //   forced without the use of ES2025 flag groups)\n    ignoreCase: !!((ast.flags.ignoreCase || hasCaseInsensitiveNode) && !hasCaseSensitiveNode),\n  };\n  let /** @type {Node} */ lastNode = ast;\n  const state = {\n    accuracy: opts.accuracy,\n    appliedGlobalFlags,\n    captureMap: new Map(),\n    currentFlags: {\n      dotAll: ast.flags.dotAll,\n      ignoreCase: ast.flags.ignoreCase,\n    },\n    inCharClass: false,\n    lastNode,\n    originMap: ast._originMap,\n    recursionLimit,\n    useAppliedIgnoreCase: !!(!minTargetEs2025 && hasCaseInsensitiveNode && hasCaseSensitiveNode),\n    useFlagMods: minTargetEs2025,\n    useFlagV: minTargetEs2024,\n    verbose: opts.verbose,\n  };\n  function gen(/** @type {Node} */ node) {\n    state.lastNode = lastNode;\n    lastNode = node; // For the next iteration\n    const fn = throwIfNullish(generator[node.type], `Unexpected node type \"${node.type}\"`);\n    return fn(node, state, gen);\n  }\n\n  const result = {\n    pattern: ast.body.map(gen).join('|'),\n    // Could reset `lastNode` at this point via `lastNode = ast`, but it isn't needed by flags\n    flags: gen(ast.flags),\n    options: {...ast.options},\n  };\n  if (!minTargetEs2024) {\n    // Switch from flag v to u; Regex+ implicitly chooses by default\n    delete result.options.force.v;\n    result.options.disable.v = true;\n    result.options.unicodeSetsPlugin = null;\n  }\n  result._captureTransfers = new Map();\n  result._hiddenCaptures = [];\n  state.captureMap.forEach((value, key) => {\n    if (value.hidden) {\n      result._hiddenCaptures.push(key);\n    }\n    if (value.transferTo) {\n      getOrInsert(result._captureTransfers, value.transferTo, []).push(key);\n    }\n  });\n\n  return result;\n}\n\nconst /** @type {Visitor} */ FlagModifierVisitor = {\n  '*': {\n    enter({node}, state) {\n      if (isAnyGroup(node)) {\n        const currentModI = state.getCurrentModI();\n        state.pushModI(\n          node.flags ?\n            getNewCurrentFlags({ignoreCase: currentModI}, node.flags).ignoreCase :\n            currentModI\n        );\n      }\n    },\n    exit({node}, state) {\n      if (isAnyGroup(node)) {\n        state.popModI();\n      }\n    },\n  },\n  Backreference(_, state) {\n    // Can't know for sure, so assume the backref will include chars with case (best that could be\n    // done is not calling `setHasCasedChar` if the reffed group doesn't contain a char with case\n    // or most kinds of char sets)\n    state.setHasCasedChar();\n  },\n  Character({node}, state) {\n    if (charHasCase(cp(node.value))) {\n      state.setHasCasedChar();\n    }\n  },\n  CharacterClassRange({node, skip}, state) {\n    skip();\n    if (getCasesOutsideCharClassRange(node, {firstOnly: true}).length) {\n      state.setHasCasedChar();\n    }\n  },\n  CharacterSet({node}, state) {\n    if (\n      node.kind === 'property' &&\n      UnicodePropertiesWithSpecificCase.has(node.value)\n    ) {\n      state.setHasCasedChar();\n    }\n  },\n};\n\n// `AbsenceFunction`, `Directive`, and `NamedCallout` nodes aren't included in transformer output\nconst generator = {\n  /**\n  @param {AlternativeNode} node\n  */\n  Alternative({body}, _, gen) {\n    return body.map(gen).join('');\n  },\n\n  /**\n  @param {AssertionNode} node\n  */\n  Assertion({kind, negate}) {\n    // Can always use `^` and `$` for string boundaries since JS flag m is never used (Onig uses\n    // different line break chars)\n    if (kind === 'string_end') {\n      return '$';\n    }\n    if (kind === 'string_start') {\n      return '^';\n    }\n    // If a word boundary came through the transformer unaltered, that means `wordIsAscii` or\n    // `asciiWordBoundaries` is enabled\n    if (kind === 'word_boundary') {\n      return negate ? r`\\B` : r`\\b`;\n    }\n    // Kinds `line_end`, `line_start`, `search_start`, `string_end_newline`, and\n    // `text_segment_boundary` are never included in transformer output\n    throw new Error(`Unexpected assertion kind \"${kind}\"`);\n  },\n\n  /**\n  @param {BackreferenceNode} node\n  */\n  Backreference({ref}, state) {\n    if (typeof ref !== 'number') {\n      throw new Error('Unexpected named backref in transformed AST');\n    }\n    if (\n      !state.useFlagMods &&\n      state.accuracy === 'strict' &&\n      state.currentFlags.ignoreCase &&\n      !state.captureMap.get(ref).ignoreCase\n    ) {\n      throw new Error('Use of case-insensitive backref to case-sensitive group requires target ES2025 or non-strict accuracy');\n    }\n    return '\\\\' + ref;\n  },\n\n  /**\n  @param {CapturingGroupNode} node\n  */\n  CapturingGroup(node, state, gen) {\n    const {body, name, number} = node;\n    const data = {ignoreCase: state.currentFlags.ignoreCase};\n    // Has origin if the capture is from an expanded subroutine\n    const origin = state.originMap.get(node);\n    if (origin) {\n      // All captures from/within expanded subroutines are marked as hidden\n      data.hidden = true;\n      // If a subroutine (or descendant capture) occurs after its origin group, it's marked to have\n      // its captured value transferred to the origin's capture slot. `number` and `origin.number`\n      // are the capture numbers *after* subroutine expansion\n      if (number > origin.number) {\n        data.transferTo = origin.number;\n      }\n    }\n    state.captureMap.set(number, data);\n    return `(${name ? `?<${name}>` : ''}${body.map(gen).join('|')})`;\n  },\n\n  /**\n  @param {CharacterNode} node\n  */\n  Character({value}, state) {\n    const char = cp(value);\n    const escaped = getCharEscape(value, {\n      escDigit: state.lastNode.type === 'Backreference',\n      inCharClass: state.inCharClass,\n      useFlagV: state.useFlagV,\n    });\n    if (escaped !== char) {\n      return escaped;\n    }\n    if (state.useAppliedIgnoreCase && state.currentFlags.ignoreCase && charHasCase(char)) {\n      const cases = getIgnoreCaseMatchChars(char);\n      return state.inCharClass ?\n        cases.join('') :\n        (cases.length > 1 ? `[${cases.join('')}]` : cases[0]);\n    }\n    return char;\n  },\n\n  /**\n  @param {CharacterClassNode} node\n  */\n  CharacterClass(node, state, gen) {\n    const {kind, negate, parent} = node;\n    let {body} = node;\n    if (kind === 'intersection' && !state.useFlagV) {\n      throw new Error('Use of class intersection requires min target ES2024');\n    }\n    // Work around a WebKit parser bug by moving literal hyphens to the beginning of the class; see\n    // <github.com/slevithan/oniguruma-to-es/issues/30>\n    if (envFlags.bugFlagVLiteralHyphenIsRange && state.useFlagV && body.some(isLiteralHyphen)) {\n      body = [createCharacter(45), ...body.filter(kid => !isLiteralHyphen(kid))];\n    }\n    const genClass = () => `[${negate ? '^' : ''}${\n      body.map(gen).join(kind === 'intersection' ? '&&' : '')\n    }]`;\n    if (!state.inCharClass) {\n      // HACK: Transform the AST to support top-level-nested, negated classes in non-negated\n      // classes (ex: `[…[^…]]`) with pre-ES2024 `target`, via `(?:[…]|[^…])` or `(?:[^…])`,\n      // possibly with multiple alts that contain negated classes. Would be better to do this in\n      // the transformer, but it doesn't have true `target` since that's supposed to be a concern\n      // of the generator\n      if (\n        // Already established `kind !== 'intersection'` if `!state.useFlagV`; don't check again\n        (!state.useFlagV || envFlags.bugNestedClassIgnoresNegation) &&\n        !negate\n      ) {\n        const negatedChildClasses = body.filter(\n          kid => kid.type === 'CharacterClass' && kid.kind === 'union' && kid.negate\n        );\n        if (negatedChildClasses.length) {\n          const group = createGroup();\n          const groupFirstAlt = group.body[0];\n          group.parent = parent;\n          groupFirstAlt.parent = group;\n          body = body.filter(kid => !negatedChildClasses.includes(kid));\n          node.body = body;\n          if (body.length) {\n            node.parent = groupFirstAlt;\n            groupFirstAlt.body.push(node);\n          } else {\n            // Remove the group's only alt thus far, but since the class's `body` is empty, that\n            // implies there's at least one negated class we removed from it, so we'll add at least\n            // one alt back to the group, next\n            group.body.pop();\n          }\n          negatedChildClasses.forEach(cc => {\n            const newAlt = createAlternative({body: [cc]});\n            cc.parent = newAlt;\n            newAlt.parent = group;\n            group.body.push(newAlt);\n          });\n          return gen(group);\n        }\n      }\n      // For the outermost char class, set state\n      state.inCharClass = true;\n      const result = genClass();\n      state.inCharClass = false;\n      return result;\n    }\n    // No first element for implicit class in empty intersection like `[&&]`\n    const firstEl = body[0];\n    if (\n      // Already established that the parent is a char class via `inCharClass`; don't check again\n      kind === 'union' &&\n      !negate &&\n      firstEl &&\n      (\n        ( // Allows many nested classes to work with `target` ES2018 which doesn't support nesting\n          (!state.useFlagV || !state.verbose) &&\n          parent.kind === 'union' &&\n          !(envFlags.bugFlagVLiteralHyphenIsRange && state.useFlagV)\n        ) ||\n        ( !state.verbose &&\n          parent.kind === 'intersection' &&\n          // JS doesn't allow intersection with union or ranges\n          body.length === 1 &&\n          firstEl.type !== 'CharacterClassRange'\n        )\n      )\n    ) {\n      // Remove unnecessary nesting; unwrap kids into the parent char class\n      return body.map(gen).join('');\n    }\n    if (!state.useFlagV && parent.type === 'CharacterClass') {\n      throw new Error('Use of nested character class requires min target ES2024');\n    }\n    return genClass();\n  },\n\n  /**\n  @param {CharacterClassRangeNode} node\n  */\n  CharacterClassRange(node, state) {\n    const min = node.min.value;\n    const max = node.max.value;\n    const escOpts = {\n      escDigit: false,\n      inCharClass: true,\n      useFlagV: state.useFlagV,\n    };\n    const minStr = getCharEscape(min, escOpts);\n    const maxStr = getCharEscape(max, escOpts);\n    const extraChars = new Set();\n    if (state.useAppliedIgnoreCase && state.currentFlags.ignoreCase) {\n      // TODO: Avoid duplication by considering other chars in the parent char class when expanding\n      const charsOutsideRange = getCasesOutsideCharClassRange(node);\n      const ranges = getCodePointRangesFromChars(charsOutsideRange);\n      ranges.forEach(value => {\n        extraChars.add(\n          Array.isArray(value) ?\n            `${getCharEscape(value[0], escOpts)}-${getCharEscape(value[1], escOpts)}` :\n            getCharEscape(value, escOpts)\n        );\n      });\n    }\n    // Create the range without calling `gen` on the `min`/`max` kids\n    return `${minStr}-${maxStr}${[...extraChars].join('')}`;\n  },\n\n  /**\n  @param {CharacterSetNode} node\n  */\n  CharacterSet({kind, negate, value, key}, state) {\n    if (kind === 'dot') {\n      return state.currentFlags.dotAll ?\n        ((state.appliedGlobalFlags.dotAll || state.useFlagMods) ? '.' : '[^]') :\n        // Onig's only line break char is line feed, unlike JS\n        r`[^\\n]`;\n    }\n    if (kind === 'digit') {\n      return negate ? r`\\D` : r`\\d`;\n    }\n    if (kind === 'property') {\n      if (\n        state.useAppliedIgnoreCase &&\n        state.currentFlags.ignoreCase &&\n        UnicodePropertiesWithSpecificCase.has(value)\n      ) {\n        // Support for this would require heavy Unicode data. Could change e.g. `\\p{Lu}` to\n        // `\\p{LC}` if not using strict `accuracy` (since it's close but not 100%), but this\n        // wouldn't work for e.g. `\\p{Lt}`, and in any case, it's probably user error if using\n        // these case-specific props case-insensitively\n        throw new Error(`Unicode property \"${value}\" can't be case-insensitive when other chars have specific case`);\n      }\n      return `${negate ? r`\\P` : r`\\p`}{${key ? `${key}=` : ''}${value}}`;\n    }\n    if (kind === 'word') {\n      return negate ? r`\\W` : r`\\w`;\n    }\n    // Kinds `any`, `hex`, `newline`, `posix`, `space`, and `text_segment` are never included in\n    // transformer output\n    throw new Error(`Unexpected character set kind \"${kind}\"`);\n  },\n\n  /**\n  @param {FlagsNode} node\n  */\n  Flags(node, state) {\n    return (\n      // The transformer should never turn on the properties for flags d, g, m since Onig doesn't\n      // have equivs. Flag m is never used since Onig uses different line break chars than JS\n      // (node.hasIndices ? 'd' : '') +\n      // (node.global ? 'g' : '') +\n      // (node.multiline ? 'm' : '') +\n      (state.appliedGlobalFlags.ignoreCase ? 'i' : '') +\n      (node.dotAll ? 's' : '') +\n      (node.sticky ? 'y' : '')\n      // Regex+ doesn't allow explicitly adding flags it handles implicitly, so there are no\n      // `unicode` (flag u) or `unicodeSets` (flag v) props; those flags are added separately\n    );\n  },\n\n  /**\n  @param {GroupNode} node\n  */\n  Group({atomic, body, flags, parent}, state, gen) {\n    const currentFlags = state.currentFlags;\n    if (flags) {\n      state.currentFlags = getNewCurrentFlags(currentFlags, flags);\n    }\n    const contents = body.map(gen).join('|');\n    const result = (\n      !state.verbose &&\n      body.length === 1 && // Single alt\n      parent.type !== 'Quantifier' &&\n      !atomic &&\n      (!state.useFlagMods || !flags)\n     ) ? contents : `(?${getGroupPrefix(atomic, flags, state.useFlagMods)}${contents})`;\n    state.currentFlags = currentFlags;\n    return result;\n  },\n\n  /**\n  @param {LookaroundAssertionNode} node\n  */\n  LookaroundAssertion({body, kind, negate}, _, gen) {\n    const prefix = `${kind === 'lookahead' ? '' : '<'}${negate ? '!' : '='}`;\n    return `(?${prefix}${body.map(gen).join('|')})`;\n  },\n\n  /**\n  @param {QuantifierNode} node\n  */\n  Quantifier(node, _, gen) {\n    return gen(node.body) + getQuantifierStr(node);\n  },\n\n  /**\n  @param {SubroutineNode & {isRecursive: true}} node\n  */\n  Subroutine({isRecursive, ref}, state) {\n    if (!isRecursive) {\n      throw new Error('Unexpected non-recursive subroutine in transformed AST');\n    }\n    const limit = state.recursionLimit;\n    // Using the syntax supported by `regex-recursion`\n    return ref === 0 ? `(?R=${limit})` : r`\\g<${ref}&R=${limit}>`;\n  },\n};\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\nconst BaseEscapeChars = new Set([\n  '$', '(', ')', '*', '+', '.', '?', '[', '\\\\', ']', '^', '{', '|', '}',\n]);\n\nconst CharClassEscapeChars = new Set([\n  '-', '\\\\', ']', '^',\n  // Literal `[` doesn't require escaping with flag u, but this can help work around regex source\n  // linters and regex syntax processors that expect unescaped `[` to create a nested class\n  '[',\n]);\n\nconst CharClassEscapeCharsFlagV = new Set([\n  '(', ')', '-', '/', '[', '\\\\', ']', '^', '{', '|', '}',\n  // Double punctuators; also includes already-listed `-` and `^`\n  '!', '#', '$', '%', '&', '*', '+', ',', '.', ':', ';', '<', '=', '>', '?', '@', '`', '~',\n]);\n\nconst CharCodeEscapeMap = new Map([\n  [ 9, r`\\t`], // horizontal tab\n  [10, r`\\n`], // line feed\n  [11, r`\\v`], // vertical tab\n  [12, r`\\f`], // form feed\n  [13, r`\\r`], // carriage return\n  [0x2028, r`\\u2028`], // line separator\n  [0x2029, r`\\u2029`], // paragraph separator\n  [0xFEFF, r`\\uFEFF`], // ZWNBSP/BOM\n]);\n\nconst casedRe = /^\\p{Cased}$/u;\nfunction charHasCase(char) {\n  return casedRe.test(char);\n}\n\n/**\nGiven a `CharacterClassRange` node, returns an array of chars that are a case variant of a char in\nthe range, and aren't already in the range.\n*/\nfunction getCasesOutsideCharClassRange(node, options) {\n  const firstOnly = !!options?.firstOnly;\n  const min = node.min.value;\n  const max = node.max.value;\n  const found = [];\n  // Avoid unneeded work. Assumptions (per Unicode 16):\n  // - No case variants cross the Basic Multilingual Plane boundary\n  // - No cased chars appear beyond the Supplementary Multilingual Plane\n  if ((min < 65 && (max === 0xFFFF || max >= 0x1FFFF)) || (min === 0x10000 && max >= 0x1FFFF)) {\n    return found;\n  }\n  for (let i = min; i <= max; i++) {\n    const char = cp(i);\n    if (!charHasCase(char)) {\n      continue;\n    }\n    const charsOutsideRange = getIgnoreCaseMatchChars(char).filter(caseOfChar => {\n      const num = caseOfChar.codePointAt(0);\n      return num < min || num > max;\n    });\n    if (charsOutsideRange.length) {\n      found.push(...charsOutsideRange);\n      if (firstOnly) {\n        break;\n      }\n    }\n  }\n  return found;\n}\n\n// This shouldn't modifiy any char that has case\nfunction getCharEscape(codePoint, {escDigit, inCharClass, useFlagV}) {\n  if (CharCodeEscapeMap.has(codePoint)) {\n    return CharCodeEscapeMap.get(codePoint);\n  }\n  if (\n    // Control chars, etc.; condition modeled on the Chrome developer console's display for strings\n    codePoint < 32 || (codePoint > 126 && codePoint < 160) ||\n    // Unicode planes 4-16; unassigned, special purpose, and private use area\n    codePoint > 0x3FFFF ||\n    // Avoid corrupting a preceding backref by immediately following it with a literal digit\n    (escDigit && isDigitCharCode(codePoint))\n  ) {\n    // Don't convert codePoint `0` to `\\0` since that's corruptible by following literal digits\n    // Note: Not using raw strings to work around Bun ≤ 1.1.34 issue <github.com/oven-sh/bun/issues/7540>\n    return codePoint > 0xFF ?\n      `\\\\u{${codePoint.toString(16).toUpperCase()}}` :\n      `\\\\x${codePoint.toString(16).toUpperCase().padStart(2, '0')}`;\n  }\n  const escapeChars = inCharClass ?\n    (useFlagV ? CharClassEscapeCharsFlagV : CharClassEscapeChars) :\n    BaseEscapeChars;\n  const char = cp(codePoint);\n  return (escapeChars.has(char) ? '\\\\' : '') + char;\n}\n\nfunction getCodePointRangesFromChars(chars) {\n  const codePoints = chars.map(char => char.codePointAt(0)).sort((a, b) => a - b);\n  const values = [];\n  let start = null;\n  for (let i = 0; i < codePoints.length; i++) {\n    if (codePoints[i + 1] === codePoints[i] + 1) {\n      start ??= codePoints[i];\n    } else if (start === null) {\n      values.push(codePoints[i]);\n    } else {\n      values.push([start, codePoints[i]]);\n      start = null;\n    }\n  }\n  return values;\n}\n\nfunction getGroupPrefix(atomic, flagMods, useFlagMods) {\n  if (atomic) {\n    return '>';\n  }\n  let mods = '';\n  if (flagMods && useFlagMods) {\n    const {enable, disable} = flagMods;\n    mods =\n      (enable?.ignoreCase ? 'i' : '') +\n      (enable?.dotAll ? 's' : '') +\n      (disable ? '-' : '') +\n      (disable?.ignoreCase ? 'i' : '') +\n      (disable?.dotAll ? 's' : '');\n  }\n  return `${mods}:`;\n}\n\n/**\n@param {QuantifierNode} node\n@returns {string}\n*/\nfunction getQuantifierStr({kind, max, min}) {\n  let base;\n  if (!min && max === 1) {\n    base = '?';\n  } else if (!min && max === Infinity) {\n    base = '*';\n  } else if (min === 1 && max === Infinity) {\n    base = '+';\n  } else if (min === max) {\n    base = `{${min}}`;\n  } else {\n    base = `{${min},${max === Infinity ? '' : max}}`;\n  }\n  return base + {\n    greedy: '',\n    lazy: '?',\n    possessive: '+',\n  }[kind];\n}\n\n/**\n@param {Node} node\n@returns {boolean}\n*/\nfunction isAnyGroup({type}) {\n  return type === 'CapturingGroup' ||\n    type === 'Group' ||\n    type === 'LookaroundAssertion';\n}\n\nfunction isDigitCharCode(value) {\n  return value > 47 && value < 58;\n}\n\n/**\n@param {Node} node\n@returns {boolean}\n*/\nfunction isLiteralHyphen({type, value}) {\n  return type === 'Character' && value === 45;\n}\n\nexport {\n  generate,\n};\n", "import {getOrInsert} from './utils.js';\n\n/**\n@typedef {{\n  hiddenCaptures?: Array<number>;\n  lazyCompile?: boolean;\n  strategy?: string | null;\n  transfers?: Array<[number, Array<number>]>;\n}} EmulatedRegExpOptions\n*/\n\n/**\nWorks the same as JavaScript's native `RegExp` constructor in all contexts, but can be given\nresults from `toRegExpDetails` to produce the same result as `toRegExp`.\n*/\nclass EmulatedRegExp extends RegExp {\n  /**\n  @type {Map<number, {\n    hidden?: true;\n    transferTo?: number;\n  }>}\n  */\n  #captureMap = new Map();\n\n  /**\n  @type {RegExp | EmulatedRegExp | null}\n  */\n  #compiled = null;\n\n  /**\n  @type {string}\n  */\n  #pattern;\n\n  /**\n  @type {Map<number, string>?}\n  */\n  #nameMap = null;\n\n  /**\n  @type {string?}\n  */\n  #strategy = null;\n\n  /**\n  Can be used to serialize the instance.\n  @type {EmulatedRegExpOptions}\n  */\n  rawOptions = {};\n\n  // Override the getter with one that works with lazy-compiled regexes\n  get source() {\n    return this.#pattern || '(?:)';\n  }\n\n  /**\n  @overload\n  @param {string} pattern\n  @param {string} [flags]\n  @param {EmulatedRegExpOptions} [options]\n  */\n  /**\n  @overload\n  @param {EmulatedRegExp} pattern\n  @param {string} [flags]\n  */\n  constructor(pattern, flags, options) {\n    const lazyCompile = !!options?.lazyCompile;\n    if (pattern instanceof RegExp) {\n      // Argument `options` isn't provided when regexes are copied, including as part of the\n      // internal handling of string methods `matchAll` and `split`\n      if (options) {\n        throw new Error('Cannot provide options when copying a regexp');\n      }\n      const re = pattern; // Alias for readability\n      super(re, flags);\n      this.#pattern = re.source;\n      if (re instanceof EmulatedRegExp) {\n        this.#captureMap = re.#captureMap;\n        this.#nameMap = re.#nameMap;\n        this.#strategy = re.#strategy;\n        this.rawOptions = re.rawOptions;\n      }\n    } else {\n      const opts = {\n        hiddenCaptures: [],\n        strategy: null,\n        transfers: [],\n        ...options,\n      };\n      super(lazyCompile ? '' : pattern, flags);\n      this.#pattern = pattern;\n      this.#captureMap = createCaptureMap(opts.hiddenCaptures, opts.transfers);\n      this.#strategy = opts.strategy;\n      // Don't add default values from `opts` since this gets serialized\n      this.rawOptions = options ?? {};\n    }\n    if (!lazyCompile) {\n      this.#compiled = this;\n    }\n  }\n\n  /**\n  Called internally by all String/RegExp methods that use regexes.\n  @override\n  @param {string} str\n  @returns {RegExpExecArray?}\n  */\n  exec(str) {\n    // Lazy compilation\n    if (!this.#compiled) {\n      const {lazyCompile, ...rest} = this.rawOptions;\n      this.#compiled = new EmulatedRegExp(this.#pattern, this.flags, rest);\n    }\n\n    const useLastIndex = this.global || this.sticky;\n    const pos = this.lastIndex;\n\n    if (this.#strategy === 'clip_search' && useLastIndex && pos) {\n      // Reset since this tests on a sliced string that we want to match at the start of\n      this.lastIndex = 0;\n      // Slicing the string can lead to mismatches when three edge cases are stacked on each other:\n      // 1. An uncommon use of `\\G` that relies on `clip_search`, combined with...\n      // 2. Lookbehind that searches behind the search start (not match start) position...\n      // 3. During a search when the regex's `lastIndex` isn't `0`.\n      // The `clip_search` strategy is therefore only allowed when lookbehind isn't present, if\n      // using strict `accuracy`\n      const match = this.#execCore(str.slice(pos));\n      if (match) {\n        adjustMatchDetailsForOffset(match, pos, str, this.hasIndices);\n        this.lastIndex += pos;\n      }\n      return match;\n    }\n\n    return this.#execCore(str);\n  }\n\n  /**\n  Adds support for hidden and transfer captures.\n  @param {string} str\n  @returns\n  */\n  #execCore(str) {\n    // Support lazy compilation\n    this.#compiled.lastIndex = this.lastIndex;\n    const match = super.exec.call(this.#compiled, str);\n    this.lastIndex = this.#compiled.lastIndex;\n\n    if (!match || !this.#captureMap.size) {\n      return match;\n    }\n\n    const matchCopy = [...match];\n    // Empty all but the first value of the array while preserving its other properties\n    match.length = 1;\n    let indicesCopy;\n    if (this.hasIndices) {\n      indicesCopy = [...match.indices];\n      match.indices.length = 1;\n    }\n    const mappedNums = [0];\n    for (let i = 1; i < matchCopy.length; i++) {\n      const {hidden, transferTo} = this.#captureMap.get(i) ?? {};\n      if (hidden) {\n        mappedNums.push(null);\n      } else {\n        mappedNums.push(match.length);\n        match.push(matchCopy[i]);\n        if (this.hasIndices) {\n          match.indices.push(indicesCopy[i]);\n        }\n      }\n\n      // Only transfer if the capture participated in the match\n      if (transferTo && matchCopy[i] !== undefined) {\n        const to = mappedNums[transferTo];\n        if (!to) {\n          throw new Error(`Invalid capture transfer to \"${to}\"`);\n        }\n        match[to] = matchCopy[i];\n        if (this.hasIndices) {\n          match.indices[to] = indicesCopy[i];\n        }\n        if (match.groups) {\n          if (!this.#nameMap) {\n            // Generate and cache the first time it's needed\n            this.#nameMap = createNameMap(this.source);\n          }\n          const name = this.#nameMap.get(transferTo);\n          if (name) {\n            match.groups[name] = matchCopy[i];\n            if (this.hasIndices) {\n              match.indices.groups[name] = indicesCopy[i];\n            }\n          }\n        }\n      }\n    }\n\n    return match;\n  }\n}\n\nfunction adjustMatchDetailsForOffset(match, offset, input, hasIndices) {\n  match.index += offset;\n  match.input = input;\n  if (hasIndices) {\n    const indices = match.indices;\n    for (let i = 0; i < indices.length; i++) {\n      const arr = indices[i];\n      if (arr) {\n        // Replace the array rather than updating values since the keys of `match.indices` and\n        // `match.indices.groups` share their value arrays by reference. Need to be precise in case\n        // they were previously altered separately\n        indices[i] = [arr[0] + offset, arr[1] + offset];\n      }\n    }\n    const groupIndices = indices.groups;\n    if (groupIndices) {\n      Object.keys(groupIndices).forEach(key => {\n        const arr = groupIndices[key];\n        if (arr) {\n          groupIndices[key] = [arr[0] + offset, arr[1] + offset];\n        }\n      });\n    }\n  }\n}\n\n/**\nBuild the capturing group map, with hidden/transfer groups marked to indicate their submatches\nshould get special handling in match results.\n@param {Array<number>} hiddenCaptures\n@param {Array<[number, Array<number>]>} transfers\n@returns {Map<number, {\n  hidden?: true;\n  transferTo?: number;\n}>}\n*/\nfunction createCaptureMap(hiddenCaptures, transfers) {\n  const captureMap = new Map();\n  for (const num of hiddenCaptures) {\n    captureMap.set(num, {\n      hidden: true,\n    });\n  }\n  for (const [to, from] of transfers) {\n    for (const num of from) {\n      getOrInsert(captureMap, num, {}).transferTo = to;\n    }\n  }\n  return captureMap;\n}\n\n/**\n@param {string} pattern\n@returns {Map<number, string>}\n*/\nfunction createNameMap(pattern) {\n  const re = /(?<capture>\\((?:\\?<(?![=!])(?<name>[^>]+)>|(?!\\?)))|\\\\?./gsu;\n  const map = new Map();\n  let numCharClassesOpen = 0;\n  let numCaptures = 0;\n  let match;\n  while ((match = re.exec(pattern))) {\n    const {0: m, groups: {capture, name}} = match;\n    // Relies on no unescaped literal `[` in char classes (valid in JS if not using flag v), but\n    // this library's generator never produces unescaped literal `[` even with `target` ES2018 (see\n    // `CharClassEscapeChars`)\n    if (m === '[') {\n      numCharClassesOpen++;\n    } else if (!numCharClassesOpen) {\n      if (capture) {\n        numCaptures++;\n        if (name) {\n          map.set(numCaptures, name);\n        }\n      }\n    } else if (m === ']') {\n      numCharClassesOpen--;\n    }\n  }\n  return map;\n}\n\nexport {\n  EmulatedRegExp,\n};\n", "// Separating some utils for improved tree shaking of the `./internals` export\n\nconst noncapturingDelim = String.raw`\\(\\?(?:[:=!>A-Za-z\\-]|<[=!]|\\(DEFINE\\))`;\n\n/**\nUpdates the array in place by incrementing each value greater than or equal to the threshold.\n@param {Array<number>} arr\n@param {number} threshold\n*/\nfunction incrementIfAtLeast(arr, threshold) {\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i] >= threshold) {\n      arr[i]++;\n    }\n  }\n}\n\n/**\n@param {string} str\n@param {number} pos\n@param {string} oldValue\n@param {string} newValue\n@returns {string}\n*/\nfunction spliceStr(str, pos, oldValue, newValue) {\n  return str.slice(0, pos) + newValue + str.slice(pos + oldValue.length);\n}\n\nexport {\n  incrementIfAtLeast,\n  noncapturingDelim,\n  spliceStr,\n};\n", "// Constant properties for tracking regex syntax context\nexport const Context = Object.freeze({\n  DEFAULT: 'DEFAULT',\n  CHAR_CLASS: 'CHAR_CLASS',\n});\n\n/**\nReplaces all unescaped instances of a regex pattern in the given context, using a replacement\nstring or callback.\n\nDoesn't skip over complete multicharacter tokens (only `\\` plus its folowing char) so must be used\nwith knowledge of what's safe to do given regex syntax. Assumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {string} needle Search as a regex pattern, with flags `su` applied\n@param {string | (match: RegExpExecArray, details: {\n  context: 'DEFAULT' | 'CHAR_CLASS';\n  negated: boolean;\n}) => string} replacement\n@param {'DEFAULT' | 'CHAR_CLASS'} [context] All contexts if not specified\n@returns {string} Updated expression\n@example\nconst str = '.\\\\.\\\\\\\\.[[\\\\.].].';\nreplaceUnescaped(str, '\\\\.', '@');\n// → '@\\\\.\\\\\\\\@[[\\\\.]@]@'\nreplaceUnescaped(str, '\\\\.', '@', Context.DEFAULT);\n// → '@\\\\.\\\\\\\\@[[\\\\.].]@'\nreplaceUnescaped(str, '\\\\.', '@', Context.CHAR_CLASS);\n// → '.\\\\.\\\\\\\\.[[\\\\.]@].'\n*/\nexport function replaceUnescaped(expression, needle, replacement, context) {\n  const re = new RegExp(String.raw`${needle}|(?<$skip>\\[\\^?|\\\\?.)`, 'gsu');\n  const negated = [false];\n  let numCharClassesOpen = 0;\n  let result = '';\n  for (const match of expression.matchAll(re)) {\n    const {0: m, groups: {$skip}} = match;\n    if (!$skip && (!context || (context === Context.DEFAULT) === !numCharClassesOpen)) {\n      if (replacement instanceof Function) {\n        result += replacement(match, {\n          context: numCharClassesOpen ? Context.CHAR_CLASS : Context.DEFAULT,\n          negated: negated[negated.length - 1],\n        });\n      } else {\n        result += replacement;\n      }\n      continue;\n    }\n    if (m[0] === '[') {\n      numCharClassesOpen++;\n      negated.push(m[1] === '^');\n    } else if (m === ']' && numCharClassesOpen) {\n      numCharClassesOpen--;\n      negated.pop();\n    }\n    result += m;\n  }\n  return result;\n}\n\n/**\nRuns a callback for each unescaped instance of a regex pattern in the given context.\n\nDoesn't skip over complete multicharacter tokens (only `\\` plus its folowing char) so must be used\nwith knowledge of what's safe to do given regex syntax. Assumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {string} needle Search as a regex pattern, with flags `su` applied\n@param {(match: RegExpExecArray, details: {\n  context: 'DEFAULT' | 'CHAR_CLASS';\n  negated: boolean;\n}) => void} callback\n@param {'DEFAULT' | 'CHAR_CLASS'} [context] All contexts if not specified\n*/\nexport function forEachUnescaped(expression, needle, callback, context) {\n  // Do this the easy way\n  replaceUnescaped(expression, needle, callback, context);\n}\n\n/**\nReturns a match object for the first unescaped instance of a regex pattern in the given context, or\n`null`.\n\nDoesn't skip over complete multicharacter tokens (only `\\` plus its folowing char) so must be used\nwith knowledge of what's safe to do given regex syntax. Assumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {string} needle Search as a regex pattern, with flags `su` applied\n@param {number} [pos] Offset to start the search\n@param {'DEFAULT' | 'CHAR_CLASS'} [context] All contexts if not specified\n@returns {RegExpExecArray | null}\n*/\nexport function execUnescaped(expression, needle, pos = 0, context) {\n  // Quick partial test; avoid the loop if not needed\n  if (!(new RegExp(needle, 'su').test(expression))) {\n    return null;\n  }\n  const re = new RegExp(`${needle}|(?<$skip>\\\\\\\\?.)`, 'gsu');\n  re.lastIndex = pos;\n  let numCharClassesOpen = 0;\n  let match;\n  while (match = re.exec(expression)) {\n    const {0: m, groups: {$skip}} = match;\n    if (!$skip && (!context || (context === Context.DEFAULT) === !numCharClassesOpen)) {\n      return match;\n    }\n    if (m === '[') {\n      numCharClassesOpen++;\n    } else if (m === ']' && numCharClassesOpen) {\n      numCharClassesOpen--;\n    }\n    // Avoid an infinite loop on zero-length matches\n    if (re.lastIndex == match.index) {\n      re.lastIndex++;\n    }\n  }\n  return null;\n}\n\n/**\nChecks whether an unescaped instance of a regex pattern appears in the given context.\n\nDoesn't skip over complete multicharacter tokens (only `\\` plus its folowing char) so must be used\nwith knowledge of what's safe to do given regex syntax. Assumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {string} needle Search as a regex pattern, with flags `su` applied\n@param {'DEFAULT' | 'CHAR_CLASS'} [context] All contexts if not specified\n@returns {boolean} Whether the pattern was found\n*/\nexport function hasUnescaped(expression, needle, context) {\n  // Do this the easy way\n  return !!execUnescaped(expression, needle, 0, context);\n}\n\n/**\nExtracts the full contents of a group (subpattern) from the given expression, accounting for\nescaped characters, nested groups, and character classes. The group is identified by the position\nwhere its contents start (the string index just after the group's opening delimiter). Returns the\nrest of the string if the group is unclosed.\n\nAssumes UnicodeSets-mode syntax.\n@param {string} expression Search target\n@param {number} contentsStartPos\n@returns {string}\n*/\nexport function getGroupContents(expression, contentsStartPos) {\n  const token = /\\\\?./gsu;\n  token.lastIndex = contentsStartPos;\n  let contentsEndPos = expression.length;\n  let numCharClassesOpen = 0;\n  // Starting search within an open group, after the group's opening\n  let numGroupsOpen = 1;\n  let match;\n  while (match = token.exec(expression)) {\n    const [m] = match;\n    if (m === '[') {\n      numCharClassesOpen++;\n    } else if (!numCharClassesOpen) {\n      if (m === '(') {\n        numGroupsOpen++;\n      } else if (m === ')') {\n        numGroupsOpen--;\n        if (!numGroupsOpen) {\n          contentsEndPos = match.index;\n          break;\n        }\n      }\n    } else if (m === ']') {\n      numCharClassesOpen--;\n    }\n  }\n  return expression.slice(contentsStartPos, contentsEndPos);\n}\n", "import {incrementIfAtLeast, noncapturingDelim, spliceStr} from './utils-internals.js';\nimport {Context, replaceUnescaped} from 'regex-utilities';\n\nconst atomicPluginToken = new RegExp(String.raw`(?<noncapturingStart>${noncapturingDelim})|(?<capturingStart>\\((?:\\?<[^>]+>)?)|\\\\?.`, 'gsu');\n\n/**\nApply transformations for atomic groups: `(?>…)`.\n@param {string} expression\n@param {import('./regex.js').PluginData} [data]\n@returns {Required<import('./regex.js').PluginResult>}\n*/\nfunction atomic(expression, data) {\n  const hiddenCaptures = data?.hiddenCaptures ?? [];\n  // Capture transfer is used by <github.com/slevithan/oniguruma-to-es>\n  let captureTransfers = data?.captureTransfers ?? new Map();\n  if (!/\\(\\?>/.test(expression)) {\n    return {\n      pattern: expression,\n      captureTransfers,\n      hiddenCaptures,\n    };\n  }\n\n  const aGDelim = '(?>';\n  const emulatedAGDelim = '(?:(?=(';\n  const captureNumMap = [0];\n  const addedHiddenCaptures = [];\n  let numCapturesBeforeAG = 0;\n  let numAGs = 0;\n  let aGPos = NaN;\n  let hasProcessedAG;\n  do {\n    hasProcessedAG = false;\n    let numCharClassesOpen = 0;\n    let numGroupsOpenInAG = 0;\n    let inAG = false;\n    let match;\n    atomicPluginToken.lastIndex = Number.isNaN(aGPos) ? 0 : aGPos + emulatedAGDelim.length;\n    while (match = atomicPluginToken.exec(expression)) {\n      const {0: m, index, groups: {capturingStart, noncapturingStart}} = match;\n      if (m === '[') {\n        numCharClassesOpen++;\n      } else if (!numCharClassesOpen) {\n\n        if (m === aGDelim && !inAG) {\n          aGPos = index;\n          inAG = true;\n        } else if (inAG && noncapturingStart) {\n          numGroupsOpenInAG++;\n        } else if (capturingStart) {\n          if (inAG) {\n            numGroupsOpenInAG++;\n          } else {\n            numCapturesBeforeAG++;\n            captureNumMap.push(numCapturesBeforeAG + numAGs);\n          }\n        } else if (m === ')' && inAG) {\n          if (!numGroupsOpenInAG) {\n            numAGs++;\n            const addedCaptureNum = numCapturesBeforeAG + numAGs;\n            // Replace `expression` and use `<$$N>` as a temporary wrapper for the backref so it\n            // can avoid backref renumbering afterward. Wrap the whole substitution (including the\n            // lookahead and following backref) in a noncapturing group to handle following\n            // quantifiers and literal digits\n            expression = `${expression.slice(0, aGPos)}${emulatedAGDelim}${\n                expression.slice(aGPos + aGDelim.length, index)\n              }))<$$${addedCaptureNum}>)${expression.slice(index + 1)}`;\n            hasProcessedAG = true;\n            addedHiddenCaptures.push(addedCaptureNum);\n            incrementIfAtLeast(hiddenCaptures, addedCaptureNum);\n            if (captureTransfers.size) {\n              const newCaptureTransfers = new Map();\n              captureTransfers.forEach((from, to) => {\n                newCaptureTransfers.set(\n                  to >= addedCaptureNum ? to + 1 : to,\n                  from.map(f => f >= addedCaptureNum ? f + 1 : f)\n                );\n              });\n              captureTransfers = newCaptureTransfers;\n            }\n            break;\n          }\n          numGroupsOpenInAG--;\n        }\n\n      } else if (m === ']') {\n        numCharClassesOpen--;\n      }\n    }\n  // Start over from the beginning of the atomic group's contents, in case the processed group\n  // contains additional atomic groups\n  } while (hasProcessedAG);\n\n  hiddenCaptures.push(...addedHiddenCaptures);\n\n  // Second pass to adjust numbered backrefs\n  expression = replaceUnescaped(\n    expression,\n    String.raw`\\\\(?<backrefNum>[1-9]\\d*)|<\\$\\$(?<wrappedBackrefNum>\\d+)>`,\n    ({0: m, groups: {backrefNum, wrappedBackrefNum}}) => {\n      if (backrefNum) {\n        const bNum = +backrefNum;\n        if (bNum > captureNumMap.length - 1) {\n          throw new Error(`Backref \"${m}\" greater than number of captures`);\n        }\n        return `\\\\${captureNumMap[bNum]}`;\n      }\n      return `\\\\${wrappedBackrefNum}`;\n    },\n    Context.DEFAULT\n  );\n\n  return {\n    pattern: expression,\n    captureTransfers,\n    hiddenCaptures,\n  };\n}\n\nconst baseQuantifier = String.raw`(?:[?*+]|\\{\\d+(?:,\\d*)?\\})`;\n// Complete tokenizer for base syntax; doesn't (need to) know about character-class-only syntax\nconst possessivePluginToken = new RegExp(String.raw`\n\\\\(?: \\d+\n  | c[A-Za-z]\n  | [gk]<[^>]+>\n  | [pPu]\\{[^\\}]+\\}\n  | u[A-Fa-f\\d]{4}\n  | x[A-Fa-f\\d]{2}\n  )\n| \\((?: \\? (?: [:=!>]\n  | <(?:[=!]|[^>]+>)\n  | [A-Za-z\\-]+:\n  | \\(DEFINE\\)\n  ))?\n| (?<qBase>${baseQuantifier})(?<qMod>[?+]?)(?<invalidQ>[?*+\\{]?)\n| \\\\?.\n`.replace(/\\s+/g, ''), 'gsu');\n\n/**\nTransform posessive quantifiers into atomic groups. The posessessive quantifiers are:\n`?+`, `*+`, `++`, `{N}+`, `{N,}+`, `{N,N}+`.\nThis follows Java, PCRE, Perl, and Python.\nPossessive quantifiers in Oniguruma and Onigmo are only: `?+`, `*+`, `++`.\n@param {string} expression\n@returns {import('./regex.js').PluginResult}\n*/\nfunction possessive(expression) {\n  if (!(new RegExp(`${baseQuantifier}\\\\+`).test(expression))) {\n    return {\n      pattern: expression,\n    };\n  }\n\n  const openGroupIndices = [];\n  let lastGroupIndex = null;\n  let lastCharClassIndex = null;\n  let lastToken = '';\n  let numCharClassesOpen = 0;\n  let match;\n  possessivePluginToken.lastIndex = 0;\n  while (match = possessivePluginToken.exec(expression)) {\n    const {0: m, index, groups: {qBase, qMod, invalidQ}} = match;\n    if (m === '[') {\n      if (!numCharClassesOpen) {\n        lastCharClassIndex = index;\n      }\n      numCharClassesOpen++;\n    } else if (m === ']') {\n      if (numCharClassesOpen) {\n        numCharClassesOpen--;\n      // Unmatched `]`\n      } else {\n        lastCharClassIndex = null;\n      }\n    } else if (!numCharClassesOpen) {\n\n      if (qMod === '+' && lastToken && !lastToken.startsWith('(')) {\n        // Invalid following quantifier would become valid via the wrapping group\n        if (invalidQ) {\n          throw new Error(`Invalid quantifier \"${m}\"`);\n        }\n        let charsAdded = -1; // -1 for removed trailing `+`\n        // Possessivizing fixed repetition quantifiers like `{2}` does't change their behavior, so\n        // avoid doing so (convert them to greedy)\n        if (/^\\{\\d+\\}$/.test(qBase)) {\n          expression = spliceStr(expression, index + qBase.length, qMod, '');\n        } else {\n          if (lastToken === ')' || lastToken === ']') {\n            const nodeIndex = lastToken === ')' ? lastGroupIndex : lastCharClassIndex;\n            // Unmatched `)` would break out of the wrapping group and mess with handling.\n            // Unmatched `]` wouldn't be a problem, but it's unnecessary to have dedicated support\n            // for unescaped `]++` since this won't work with flag u or v anyway\n            if (nodeIndex === null) {\n              throw new Error(`Invalid unmatched \"${lastToken}\"`);\n            }\n            expression = `${expression.slice(0, nodeIndex)}(?>${expression.slice(nodeIndex, index)}${qBase})${expression.slice(index + m.length)}`;\n          } else {\n            expression = `${expression.slice(0, index - lastToken.length)}(?>${lastToken}${qBase})${expression.slice(index + m.length)}`;\n          }\n          charsAdded += 4; // `(?>)`\n        }\n        possessivePluginToken.lastIndex += charsAdded;\n      } else if (m[0] === '(') {\n        openGroupIndices.push(index);\n      } else if (m === ')') {\n        lastGroupIndex = openGroupIndices.length ? openGroupIndices.pop() : null;\n      }\n\n    }\n    lastToken = m;\n  }\n\n  return {\n    pattern: expression,\n  };\n}\n\nexport {\n  atomic,\n  possessive,\n};\n", "import {Context, forEachUnescaped, getGroupContents, hasUnescaped, replaceUnescaped} from 'regex-utilities';\n\nconst r = String.raw;\nconst gRToken = r`\\\\g<(?<gRNameOrNum>[^>&]+)&R=(?<gRDepth>[^>]+)>`;\nconst recursiveToken = r`\\(\\?R=(?<rDepth>[^\\)]+)\\)|${gRToken}`;\nconst namedCaptureDelim = r`\\(\\?<(?![=!])(?<captureName>[^>]+)>`;\nconst captureDelim = r`${namedCaptureDelim}|(?<unnamed>\\()(?!\\?)`;\nconst token = new RegExp(r`${namedCaptureDelim}|${recursiveToken}|\\(\\?|\\\\?.`, 'gsu');\nconst overlappingRecursionMsg = 'Cannot use multiple overlapping recursions';\n\n/**\n@param {string} pattern\n@param {{\n  flags?: string;\n  captureTransfers?: Map<number, Array<number>>;\n  hiddenCaptures?: Array<number>;\n  mode?: 'plugin' | 'external';\n}} [data]\n@returns {{\n  pattern: string;\n  captureTransfers: Map<number, Array<number>>;\n  hiddenCaptures: Array<number>;\n}}\n*/\nfunction recursion(pattern, data) {\n  const {hiddenCaptures, mode} = {\n    hiddenCaptures: [],\n    mode: 'plugin',\n    ...data,\n  };\n  // Capture transfer is used by <github.com/slevithan/oniguruma-to-es>\n  let captureTransfers = data?.captureTransfers ?? new Map();\n  // Keep the initial fail-check (which avoids unneeded processing) as fast as possible by testing\n  // without the accuracy improvement of using `hasUnescaped` with `Context.DEFAULT`\n  if (!(new RegExp(recursiveToken, 'su').test(pattern))) {\n    return {\n      pattern,\n      captureTransfers,\n      hiddenCaptures,\n    };\n  }\n  if (mode === 'plugin' && hasUnescaped(pattern, r`\\(\\?\\(DEFINE\\)`, Context.DEFAULT)) {\n    throw new Error('DEFINE groups cannot be used with recursion');\n  }\n\n  const addedHiddenCaptures = [];\n  const hasNumberedBackref = hasUnescaped(pattern, r`\\\\[1-9]`, Context.DEFAULT);\n  const groupContentsStartPos = new Map();\n  const openGroups = [];\n  let hasRecursed = false;\n  let numCharClassesOpen = 0;\n  let numCapturesPassed = 0;\n  let match;\n  token.lastIndex = 0;\n  while ((match = token.exec(pattern))) {\n    const {0: m, groups: {captureName, rDepth, gRNameOrNum, gRDepth}} = match;\n    if (m === '[') {\n      numCharClassesOpen++;\n    } else if (!numCharClassesOpen) {\n\n      // `(?R=N)`\n      if (rDepth) {\n        assertMaxInBounds(rDepth);\n        if (hasRecursed) {\n          throw new Error(overlappingRecursionMsg);\n        }\n        if (hasNumberedBackref) {\n          // Could add support for numbered backrefs with extra effort, but it's probably not worth\n          // it. To trigger this error, the regex must include recursion and one of the following:\n          // - An interpolated regex that contains a numbered backref (since other numbered\n          //   backrefs are prevented by implicit flag n).\n          // - A numbered backref, when flag n is explicitly disabled.\n          // Note that Regex+'s extended syntax (atomic groups and sometimes subroutines) can also\n          // add numbered backrefs, but those work fine because external plugins like this one run\n          // *before* the transformation of built-in syntax extensions\n          throw new Error(\n            // When used in `external` mode by transpilers other than Regex+, backrefs might have\n            // gone through conversion from named to numbered, so avoid a misleading error\n            `${mode === 'external' ? 'Backrefs' : 'Numbered backrefs'} cannot be used with global recursion`\n          );\n        }\n        const left = pattern.slice(0, match.index);\n        const right = pattern.slice(token.lastIndex);\n        if (hasUnescaped(right, recursiveToken, Context.DEFAULT)) {\n          throw new Error(overlappingRecursionMsg);\n        }\n        const reps = +rDepth - 1;\n        pattern = makeRecursive(\n          left,\n          right,\n          reps,\n          false,\n          hiddenCaptures,\n          addedHiddenCaptures,\n          numCapturesPassed\n        );\n        captureTransfers = mapCaptureTransfers(\n          captureTransfers,\n          left,\n          reps,\n          addedHiddenCaptures.length,\n          0,\n          numCapturesPassed\n        );\n        // No need to parse further\n        break;\n      // `\\g<name&R=N>`, `\\g<number&R=N>`\n      } else if (gRNameOrNum) {\n        assertMaxInBounds(gRDepth);\n        let isWithinReffedGroup = false;\n        for (const g of openGroups) {\n          if (g.name === gRNameOrNum || g.num === +gRNameOrNum) {\n            isWithinReffedGroup = true;\n            if (g.hasRecursedWithin) {\n              throw new Error(overlappingRecursionMsg);\n            }\n            break;\n          }\n        }\n        if (!isWithinReffedGroup) {\n          throw new Error(r`Recursive \\g cannot be used outside the referenced group \"${\n            mode === 'external' ? gRNameOrNum : r`\\g<${gRNameOrNum}&R=${gRDepth}>`\n          }\"`);\n        }\n        const startPos = groupContentsStartPos.get(gRNameOrNum);\n        const groupContents = getGroupContents(pattern, startPos);\n        if (\n          hasNumberedBackref &&\n          hasUnescaped(groupContents, r`${namedCaptureDelim}|\\((?!\\?)`, Context.DEFAULT)\n        ) {\n          throw new Error(\n            // When used in `external` mode by transpilers other than Regex+, backrefs might have\n            // gone through conversion from named to numbered, so avoid a misleading error\n            `${mode === 'external' ? 'Backrefs' : 'Numbered backrefs'} cannot be used with recursion of capturing groups`\n          );\n        }\n        const groupContentsLeft = pattern.slice(startPos, match.index);\n        const groupContentsRight = groupContents.slice(groupContentsLeft.length + m.length);\n        const numAddedHiddenCapturesPreExpansion = addedHiddenCaptures.length;\n        const reps = +gRDepth - 1;\n        const expansion = makeRecursive(\n          groupContentsLeft,\n          groupContentsRight,\n          reps,\n          true,\n          hiddenCaptures,\n          addedHiddenCaptures,\n          numCapturesPassed\n        );\n        captureTransfers = mapCaptureTransfers(\n          captureTransfers,\n          groupContentsLeft,\n          reps,\n          addedHiddenCaptures.length - numAddedHiddenCapturesPreExpansion,\n          numAddedHiddenCapturesPreExpansion,\n          numCapturesPassed\n        );\n        const pre = pattern.slice(0, startPos);\n        const post = pattern.slice(startPos + groupContents.length);\n        // Modify the string we're looping over\n        pattern = `${pre}${expansion}${post}`;\n        // Step forward for the next loop iteration\n        token.lastIndex += expansion.length - m.length - groupContentsLeft.length - groupContentsRight.length;\n        openGroups.forEach(g => g.hasRecursedWithin = true);\n        hasRecursed = true;\n      } else if (captureName) {\n        numCapturesPassed++;\n        groupContentsStartPos.set(String(numCapturesPassed), token.lastIndex);\n        groupContentsStartPos.set(captureName, token.lastIndex);\n        openGroups.push({\n          num: numCapturesPassed,\n          name: captureName,\n        });\n      } else if (m[0] === '(') {\n        const isUnnamedCapture = m === '(';\n        if (isUnnamedCapture) {\n          numCapturesPassed++;\n          groupContentsStartPos.set(String(numCapturesPassed), token.lastIndex);\n        }\n        openGroups.push(isUnnamedCapture ? {num: numCapturesPassed} : {});\n      } else if (m === ')') {\n        openGroups.pop();\n      }\n\n    } else if (m === ']') {\n      numCharClassesOpen--;\n    }\n  }\n\n  hiddenCaptures.push(...addedHiddenCaptures);\n\n  return {\n    pattern,\n    captureTransfers,\n    hiddenCaptures,\n  };\n}\n\n/**\n@param {string} max\n*/\nfunction assertMaxInBounds(max) {\n  const errMsg = `Max depth must be integer between 2 and 100; used ${max}`;\n  if (!/^[1-9]\\d*$/.test(max)) {\n    throw new Error(errMsg);\n  }\n  max = +max;\n  if (max < 2 || max > 100) {\n    throw new Error(errMsg);\n  }\n}\n\n/**\n@param {string} left\n@param {string} right\n@param {number} reps\n@param {boolean} isSubpattern\n@param {Array<number>} hiddenCaptures\n@param {Array<number>} addedHiddenCaptures\n@param {number} numCapturesPassed\n@returns {string}\n*/\nfunction makeRecursive(\n  left,\n  right,\n  reps,\n  isSubpattern,\n  hiddenCaptures,\n  addedHiddenCaptures,\n  numCapturesPassed\n) {\n  const namesInRecursed = new Set();\n  // Can skip this work if not needed\n  if (isSubpattern) {\n    forEachUnescaped(left + right, namedCaptureDelim, ({groups: {captureName}}) => {\n      namesInRecursed.add(captureName);\n    }, Context.DEFAULT);\n  }\n  const rest = [\n    reps,\n    isSubpattern ? namesInRecursed : null,\n    hiddenCaptures,\n    addedHiddenCaptures,\n    numCapturesPassed,\n  ];\n  // Depth 2: 'left(?:left(?:)right)right'\n  // Depth 3: 'left(?:left(?:left(?:)right)right)right'\n  // Empty group in the middle separates tokens and absorbs a following quantifier if present\n  return `${left}${\n    repeatWithDepth(`(?:${left}`, 'forward', ...rest)\n  }(?:)${\n    repeatWithDepth(`${right})`, 'backward', ...rest)\n  }${right}`;\n}\n\n/**\n@param {string} pattern\n@param {'forward' | 'backward'} direction\n@param {number} reps\n@param {Set<string> | null} namesInRecursed\n@param {Array<number>} hiddenCaptures\n@param {Array<number>} addedHiddenCaptures\n@param {number} numCapturesPassed\n@returns {string}\n*/\nfunction repeatWithDepth(\n  pattern,\n  direction,\n  reps,\n  namesInRecursed,\n  hiddenCaptures,\n  addedHiddenCaptures,\n  numCapturesPassed\n) {\n  const startNum = 2;\n  const getDepthNum = i => direction === 'forward' ? (i + startNum) : (reps - i + startNum - 1);\n  let result = '';\n  for (let i = 0; i < reps; i++) {\n    const depthNum = getDepthNum(i);\n    result += replaceUnescaped(\n      pattern,\n      r`${captureDelim}|\\\\k<(?<backref>[^>]+)>`,\n      ({0: m, groups: {captureName, unnamed, backref}}) => {\n        if (backref && namesInRecursed && !namesInRecursed.has(backref)) {\n          // Don't alter backrefs to groups outside the recursed subpattern\n          return m;\n        }\n        const suffix = `_$${depthNum}`;\n        if (unnamed || captureName) {\n          const addedCaptureNum = numCapturesPassed + addedHiddenCaptures.length + 1;\n          addedHiddenCaptures.push(addedCaptureNum);\n          incrementIfAtLeast(hiddenCaptures, addedCaptureNum);\n          return unnamed ? m : `(?<${captureName}${suffix}>`;\n        }\n        return r`\\k<${backref}${suffix}>`;\n      },\n      Context.DEFAULT\n    );\n  }\n  return result;\n}\n\n/**\nUpdates the array in place by incrementing each value greater than or equal to the threshold.\n@param {Array<number>} arr\n@param {number} threshold\n*/\nfunction incrementIfAtLeast(arr, threshold) {\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i] >= threshold) {\n      arr[i]++;\n    }\n  }\n}\n\n/**\n@param {Map<number, Array<number>>} captureTransfers\n@param {string} left\n@param {number} reps\n@param {number} numCapturesAddedInExpansion\n@param {number} numAddedHiddenCapturesPreExpansion\n@param {number} numCapturesPassed\n@returns {Map<number, Array<number>>}\n*/\nfunction mapCaptureTransfers(captureTransfers, left, reps, numCapturesAddedInExpansion, numAddedHiddenCapturesPreExpansion, numCapturesPassed) {\n  if (captureTransfers.size && numCapturesAddedInExpansion) {\n    let numCapturesInLeft = 0;\n    forEachUnescaped(left, captureDelim, () => numCapturesInLeft++, Context.DEFAULT);\n    // Is 0 for global recursion\n    const recursionDelimCaptureNum = numCapturesPassed - numCapturesInLeft + numAddedHiddenCapturesPreExpansion;\n    const newCaptureTransfers = new Map();\n    captureTransfers.forEach((from, to) => {\n      const numCapturesInRight = (numCapturesAddedInExpansion - (numCapturesInLeft * reps)) / reps;\n      const numCapturesAddedInLeft = numCapturesInLeft * reps;\n      const newTo = to > (recursionDelimCaptureNum + numCapturesInLeft) ? to + numCapturesAddedInExpansion : to;\n      const newFrom = [];\n      for (const f of from) {\n        // Before the recursed subpattern\n        if (f <= recursionDelimCaptureNum) {\n          newFrom.push(f);\n        // After the recursed subpattern\n        } else if (f > (recursionDelimCaptureNum + numCapturesInLeft + numCapturesInRight)) {\n          newFrom.push(f + numCapturesAddedInExpansion);\n        // Within the recursed subpattern, on the left of the recursion token\n        } else if (f <= (recursionDelimCaptureNum + numCapturesInLeft)) {\n          for (let i = 0; i <= reps; i++) {\n            newFrom.push(f + (numCapturesInLeft * i));\n          }\n        // Within the recursed subpattern, on the right of the recursion token\n        } else {\n          for (let i = 0; i <= reps; i++) {\n            newFrom.push(f + numCapturesAddedInLeft + (numCapturesInRight * i));\n          }\n        }\n      }\n      newCaptureTransfers.set(newTo, newFrom);\n    });\n    return newCaptureTransfers;\n  }\n  return captureTransfers;\n}\n\nexport {\n  recursion,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,IAAM,KAAK,OAAO;AAClB,IAAM,IAAI,OAAO;AAEjB,IAAM,WAAW;AAAA,EACf,aAAa,MAAM;AACjB,QAAI;AACF,UAAI,OAAO,OAAO;AAAA,IACpB,QAAQ;AACN,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,GAAG;AAAA,EACH,cAAc,MAAM;AAClB,QAAI;AACF,UAAI,OAAO,IAAI,GAAG;AAAA,IACpB,QAAQ;AACN,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,GAAG;AACL;AAEA,SAAS,+BAA+B,SAAS,eAAe,MAAM;AACpE,MAAI;AACF,QAAI,OAAO,YAAY,GAAG;AAAA,EAC5B,QAAQ;AACN,WAAO;AAAA,EACT;AACA,SAAO;AACT,GAAG,IAAI;AAEP,SAAS,gCAAgC,SAAS,eAAe,IAAI,OAAO,UAAU,GAAG,EAAE,KAAK,GAAG;AAEnG,SAAS,mBAAmB,SAAS,EAAC,QAAQ,QAAO,GAAG;AACtD,SAAO;AAAA,IACL,QAAQ,CAAC,SAAS,UAAU,CAAC,EAAE,QAAQ,UAAU,QAAQ;AAAA,IACzD,YAAY,CAAC,SAAS,cAAc,CAAC,EAAE,QAAQ,cAAc,QAAQ;AAAA,EACvE;AACF;AAEA,SAAS,YAAY,KAAK,KAAK,cAAc;AAC3C,MAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACjB,QAAI,IAAI,KAAK,YAAY;AAAA,EAC3B;AACA,SAAO,IAAI,IAAI,GAAG;AACpB;AAOA,SAAS,YAAY,QAAQ,KAAK;AAChC,SAAO,UAAU,MAAM,KAAK,UAAU,GAAG;AAC3C;AAEA,SAAS,eAAe,OAAO,KAAK;AAClC,MAAI,SAAS,MAAM;AACjB,UAAM,IAAI,MAAM,OAAO,gBAAgB;AAAA,EACzC;AACA,SAAO;AACT;;;ACrDA,IAAM,YAAY;AAAA,EAChB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACV;AAEA,IAAM;AAAA;AAAA,EAA+B;AAAA,IACnC,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA;AAOA,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,MAAI,CAAC,EAAE,SAAS,KAAK,OAAO,MAAM,mBAAmB;AACnD,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACtC;AACA,MAAI,QAAQ,WAAW,UAAa,CAAC,OAAO,QAAQ,MAAM,GAAG;AAC3D,UAAM,IAAI,MAAM,sBAAsB,QAAQ,MAAM,GAAG;AAAA,EACzD;AAEA,QAAM,OAAO;AAAA;AAAA,IAEX,UAAU;AAAA;AAAA;AAAA,IAGV,eAAe;AAAA;AAAA;AAAA,IAGf,OAAO;AAAA;AAAA,IAEP,QAAQ;AAAA;AAAA,IAER,YAAY;AAAA;AAAA,IAEZ,mBAAmB;AAAA;AAAA;AAAA;AAAA,IAInB,QAAQ;AAAA;AAAA,IAER,SAAS;AAAA,IACT,GAAG;AAAA;AAAA,IAEH,OAAO;AAAA;AAAA,MAEL,qBAAqB;AAAA;AAAA,MAErB,qBAAqB;AAAA;AAAA;AAAA;AAAA,MAIrB,cAAc;AAAA;AAAA,MAEd,gBAAgB;AAAA;AAAA;AAAA,MAGhB,YAAY;AAAA,MACZ,GAAG,QAAQ;AAAA,IACb;AAAA,EACF;AACA,MAAI,KAAK,WAAW,QAAQ;AAC1B,SAAK,SAAS,SAAS,aAAa,WAAY,SAAS,cAAc,WAAW;AAAA,EACpF;AACA,SAAO;AACT;;;AChFA,SAASA,GAAKC,GAAsB;AAElC,MAAI,CAAC,GAAGA,CAAI,EAAE,WAAW,EACvB,OAAM,IAAI,MAAM,aAAaA,CAAI,6BAA6B;AAEhE,SAAOA,EAAK,YAAY,CAAC;AAC3B;AAEA,SAASC,EAAwBC,GAAsBC,GAAUC,GAA4B;AAC3F,SAAKF,EAAI,IAAIC,CAAG,KACdD,EAAI,IAAIC,GAAKC,CAAY,GAEpBF,EAAI,IAAIC,CAAG;AACpB;AAEA,IAAME,IAAkB,oBAAI,IAAI,CAC9B,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,QACA,QACF,CAAC;AAfD,IAiBMC,IAAI,OAAO;AAEjB,SAASC,EAAsBC,GAAcC,GAAkC;AAC7E,MAAID,KAAS,KACX,OAAM,IAAI,MAAMC,KAAO,gBAAgB;AAEzC,SAAOD;AACT;;;ACuBA,IAAME,IAAuBC;AAA7B,IACMC,IAAuB,mBAM3BD,8CACF,IAEEA,gDACF,IAEEA,oDACF,IAEEA,eACF,IAEEA,UACF;AApBA,IAuBME,IAAe;AAvBrB,IAwBMC,IAAU,IAAI,OAAOH;;MAErBC,CAAoB;;;;;;;;;;;;;;;;;SAiBjBC,EAAa,MAAM;MACtBH,CAAoB;;EAExB,QAAQ,QAAQ,EAAE,GAAG,KAAK;AA9C5B,IA+CMK,IAAmB,IAAI,OAAOJ;;MAE9BC,CAAoB;;;;MAIpBF,CAAoB;;;EAGxB,QAAQ,QAAQ,EAAE,GAAG,KAAK;AAoB5B,SAASM,EAASC,GAAiBC,IAA2B,CAAC,GAG7D;AACA,QAAMC,IAAO,EACX,OAAO,IACP,GAAGD,GACH,OAAO,EACL,cAAc,OACd,YAAY,OACZ,GAAGA,EAAQ,MACb,EACF;AACA,MAAI,OAAOD,KAAY,SACrB,OAAM,IAAI,MAAM,4BAA4B;AAE9C,QAAMG,KAAiBC,EAAkBF,EAAK,KAAK,GAC7CG,KAAS,CAACF,GAAe,QAAQ,GACjCG,KAAmB,EACvB,cAAcJ,EAAK,MAAM,cAEzB,iBAAiB;AAAC,WAAOG,GAAO,GAAG,EAAE;EAAE,GACvC,eAAe,GACf,UAAU;AAACA,IAAAA,GAAO,IAAI;EAAC,GACvB,SAASE,IAAO;AAACF,IAAAA,GAAO,KAAKE,EAAK;EAAC,GACnC,mBAAmBA,IAAO;AAACF,IAAAA,GAAOA,GAAO,SAAS,CAAC,IAAIE;EAAK,GAC5D,YAAYL,EAAK,MAAM,WACzB;AACA,MAAIM,KAA2C,CAAC,GAC5CC;AAEJ,OADAZ,EAAQ,YAAY,GACZY,KAAQZ,EAAQ,KAAKG,CAAO,KAAI;AACtC,UAAMU,KAASC,EAAoBL,IAASN,GAASS,GAAM,CAAC,GAAGZ,EAAQ,SAAS;AAC5Ea,IAAAA,GAAO,SACTF,GAAO,KAAK,GAAGE,GAAO,MAAM,IACnBA,GAAO,SAChBF,GAAO,KAAKE,GAAO,KAAK,GAEtBA,GAAO,cAAc,WACvBb,EAAQ,YAAYa,GAAO;EAE/B;AAEA,QAAME,KAAuD,CAAC;AAC9D,MAAIC,IAAkC;AACtCL,EAAAA,GAAO,OAAOM,CAAAA,OAAKA,GAAE,SAAS,WAAW,EAAE,QAAQA,CAAAA,OAAK;AAClDA,IAAAA,GAAE,SAAS,cACbA,GAAE,SAAS,EAAED,IACJC,GAAE,QAAQ,OACnBF,GAA8B,KAAKE,EAAC;EAExC,CAAC,GAEID,KACHD,GAA8B,QAAQ,CAACE,IAAGC,OAAM;AAC9CD,IAAAA,GAAE,OAAO,aACTA,GAAE,SAASC,KAAI;EACjB,CAAC;AAEH,QAAMC,IAAcH,KAAmCD,GAA8B;AAMrF,SAAO,EACL,QALgCJ,GAAO,IACvCM,CAAAA,OAAKA,GAAE,SAAS,kBAAkBG,GAAwBH,IAAGE,CAAW,IAAIF,EAC9E,EAAE,KAAK,GAIL,OAAOX,GACT;AACF;AAEA,SAASQ,EAAoBL,GAAkBN,GAAiBkB,GAAWC,IAYzE;AACA,QAAM,CAACC,IAAIC,EAAE,IAAIH;AAEjB,MAAIA,MAAM,OAAOA,MAAM,MAAM;AAC3B,UAAMR,KAASY,EAAyBtB,GAASkB,GAAGC,EAAS;AAC7D,WAAO,EAEL,QAAQT,GAAO,QAEf,WAAWA,GAAO,UACpB;EACF;AAEA,MAAIU,OAAO,MAAM;AACf,QAAI,WAAW,SAASC,EAAE,EACxB,QAAO,EACL,OAAOE,EAAqBL,GAAGA,CAAC,EAClC;AAEF,QAAI,WAAW,KAAKA,CAAC,GAAG;AACtB,UAAI,CAAC,2BAA2B,KAAKA,CAAC,EACpC,OAAM,IAAI,MAAM,uBAAuBA,CAAC,GAAG;AAE7C,aAAO,EACL,OAAOM,EAAsBN,CAAC,EAChC;IACF;AACA,QAAI,WAAW,KAAKA,CAAC,GAAG;AACtB,UAAI,CAAC,2BAA2B,KAAKA,CAAC,EACpC,OAAM,IAAI,MAAM,uBAAuBA,CAAC,GAAG;AAE7C,aAAO,EACL,OAAOO,EAAyBP,CAAC,EACnC;IACF;AACA,QAAIG,OAAO,IACT,QAAO,EACL,OAAOK,EAAqB,QAAQR,CAAC,EACvC;AAEF,QAAIG,OAAO,OAAOA,OAAO,IACvB,QAAO,EACL,OAAOM,EAAwB,WAAWT,GAAG,EAE3C,QAAQG,OAAO,IACjB,CAAC,EACH;AAEF,QAAIA,OAAO,IACT,QAAO,EACL,OAAOM,EAAwB,OAAOT,CAAC,EACzC;AAEF,QAAIG,OAAO,IACT,QAAO,EACL,OAAOM,EAAwB,gBAAgBT,CAAC,EAClD;AAGF,UAAMR,KAASkB,EAAqBV,GAAG,EAAC,aAAa,MAAK,CAAC;AAC3D,WAAO,MAAM,QAAQR,EAAM,IAAI,EAAC,QAAQA,GAAM,IAAI,EAAC,OAAOA,GAAM;EAClE;AAEA,MAAIU,OAAO,KAAK;AACd,QAAIC,OAAO,IACT,QAAO,EACL,OAAOQ,EAAqBX,CAAC,EAC/B;AAEF,QAAIA,MAAM,MACR,OAAM,IAAI,MAAM,wBAAwBA,CAAC,GAAG;AAG9C,QAAIA,EAAE,WAAW,KAAK,GAAG;AAEvB,UAAIlB,EAAQmB,EAAS,MAAM,IACzB,OAAM,IAAI,MAAM,8BAA8B;AAEhD,aAAO,EAEL,WAAWA,KAAY,EACzB;IACF;AAEA,QAAI,oBAAoB,KAAKD,CAAC,EAC5B,QAAO,EACL,OAAOY,EAAqBZ,GAAGZ,CAAO,EACxC;AAKF,QAFAA,EAAQ,SAASA,EAAQ,eAAe,CAAC,GACzCA,EAAQ,iBAILY,MAAM,OAAO,CAACZ,EAAQ,gBAEvBY,MAAM,MAEN,QAAO,EAEL,OAAOa,EAAqB,SAASb,CAAC,EACxC;AAGF,QAAIA,MAAM,MACR,QAAO,EACL,OAAOa,EAAqB,UAAUb,CAAC,EACzC;AAGF,QAAIA,MAAM,SAASA,MAAM,SAASA,MAAM,UAAUA,MAAM,OACtD,QAAO,EACL,OAAOa,EAAqBb,EAAE,CAAC,MAAM,MAAM,eAAe,aAAaA,GAAG,EACxE,QAAQA,EAAE,SAAS,GAAG,EACxB,CAAC,EACH;AAIF,QACGA,MAAM,OAAOZ,EAAQ,gBACrBY,EAAE,WAAW,KAAK,KAAKA,EAAE,SAAS,GAAG,KACrCA,EAAE,WAAW,KAAK,KAAKA,EAAE,SAAS,GAAG,EAEtC,QAAO,EACL,OAAOa,EAAqB,aAAab,GAAG,EAE1C,GAAIA,MAAM,OAAO,EAAC,MAAMA,EAAE,MAAM,GAAG,EAAE,EAAC,EACxC,CAAC,EACH;AAEF,QAAIA,EAAE,WAAW,KAAK,GAAG;AACvB,UAAIA,MAAM,OACR,OAAM,IAAI,MAAM,sCAAsCA,CAAC,GAAG;AAE5D,aAAO,EACL,OAAOa,EAAqB,oBAAoBb,CAAC,EACnD;IACF;AACA,UAAIA,MAAM,QAEF,IAAI,MAAM,4BAA4BA,CAAC,GAAG,IAE5C,IAAI,MAAM,wCAAwCA,CAAC,GAAG;EAC9D;AACA,MAAIA,MAAM,KAAK;AAGb,QAFAZ,EAAQ,QAAQ,GAChBA,EAAQ,iBACJA,EAAQ,gBAAgB,EAC1B,OAAM,IAAI,MAAM,eAAe;AAEjC,WAAO,EACL,OAAO0B,EAAsBd,CAAC,EAChC;EACF;AAEA,MAAIZ,EAAQ,eAAe,GAAG;AAC5B,QAAIY,MAAM,KAAK;AAEb,YAAMe,KAAMjC,EAAQ,QAAQ;GAAMmB,EAAS;AAC3C,aAAO,EAEL,WAAWc,OAAQ,KAAKjC,EAAQ,SAASiC,GAC3C;IACF;AACA,QAAI,OAAO,KAAKf,CAAC,GAAG;AAClB,YAAMgB,KAAK;AACX,aAAAA,GAAG,YAAYf,IAER,EAEL,WAHWe,GAAG,KAAKlC,CAAO,IAGRkC,GAAG,YAAYf,GACnC;IACF;EACF;AAEA,MAAID,MAAM,IACR,QAAO,EACL,OAAOS,EAAwB,OAAOT,CAAC,EACzC;AAGF,MAAIA,MAAM,OAAOA,MAAM,KAAK;AAC1B,UAAMiB,KAAO7B,EAAQ,aAAa,EAChC,KAAKZ,OACL,GAAKA,MACP,EAAEwB,CAAC,IAAIA;AACP,WAAO,EACL,OAAOK,EAAqBY,IAAMjB,CAAC,EACrC;EACF;AAEA,SAAIA,MAAM,MACD,EACL,OAAOkB,EAAsBlB,CAAC,EAChC,IAGEtB,EAAa,KAAKsB,CAAC,IACd,EACL,QAAQmB,GAAqBnB,CAAC,EAChC,IAIK,EACL,OAAOoB,EAAqBC,GAAKrB,CAAC,GAAGA,CAAC,EACxC;AACF;AAEA,SAASI,EAAyBtB,GAAiBwC,GAA8BrB,GAG/E;AACA,QAAMX,KAA2C,CAACiC,EAA8BD,EAAO,CAAC,MAAM,KAAKA,CAAM,CAAC;AAC1G,MAAIE,KAAqB,GACrBjC;AAEJ,OADAX,EAAiB,YAAYqB,GACrBV,KAAQX,EAAiB,KAAKE,CAAO,KAAI;AAC/C,UAAMkB,KAAIT,GAAM,CAAC;AAGjB,QAAIS,GAAE,CAAC,MAAM,OAAOA,GAAE,CAAC,MAAM,IAC3BwB,CAAAA,MACAlC,GAAO,KAAKiC,EAA8BvB,GAAE,CAAC,MAAM,KAAKA,EAAyB,CAAC;aACzEA,OAAM,KAAA;AAEf,UAAIV,GAAO,GAAG,EAAE,EAAG,SAAS,qBAE1BA,CAAAA,GAAO,KAAK8B,EAAqB,IAAIpB,EAAC,CAAC;eAEvCwB,MACAlC,GAAO,KAAKmC,EAA+BzB,EAAC,CAAC,GACzC,CAACwB,GACH;IAAA,OAGC;AACL,YAAMhC,KAASkC,EAAgC1B,EAAC;AAC5C,YAAM,QAAQR,EAAM,IACtBF,GAAO,KAAK,GAAGE,EAAM,IAErBF,GAAO,KAAKE,EAAM;IAEtB;EACF;AACA,SAAO,EACL,QAAAF,IACA,WAAWV,EAAiB,aAAaE,EAAQ,OACnD;AACF;AAEA,SAAS4C,EAAgCC,GAAuD;AAC9F,MAAIA,EAAI,CAAC,MAAM,KAEb,QAAOjB,EAAqBiB,GAAK,EAAC,aAAa,KAAI,CAAC;AAGtD,MAAIA,EAAI,CAAC,MAAM,KAAK;AAClB,UAAMC,IAAQ,sCAAsC,KAAKD,CAAG;AAC5D,QAAI,CAACC,KAAS,CAACC,EAAgB,IAAID,EAAM,OAAQ,IAAI,EACnD,OAAM,IAAI,MAAM,wBAAwBD,CAAG,GAAG;AAEhD,WAAOlB,EAAwB,SAASkB,GAAK,EAC3C,OAAOC,EAAM,OAAQ,MACrB,QAAQ,CAAC,CAACA,EAAM,OAAQ,OAC1B,CAAC;EACH;AAEA,SAAID,MAAQ,MACHG,EAAgCH,CAAG,IAExCA,MAAQ,OACHI,EAAqCJ,CAAG,IAG1CP,EAAqBC,GAAKM,CAAG,GAAGA,CAAG;AAC5C;AAGA,SAASjB,EAAqBiB,GAAa,EAAC,aAAAK,EAAW,GAAqE;AAC1H,QAAMC,IAAQN,EAAI,CAAC;AACnB,MAAIM,MAAU,OAAOA,MAAU,IAC7B,QAAOC,EAAyBP,CAAG;AAErC,MAAI,WAAW,SAASM,CAAK,EAC3B,QAAOE,EAAkBR,CAAG;AAE9B,MAAIA,EAAI,WAAWnD,MAAM,EACvB,OAAM,IAAI,MAAM,yDAAyDmD,CAAG,GAAG;AAEjF,MAAI,YAAY,KAAKA,CAAG,GAAG;AACzB,QAAIA,EAAI,WAAW,EACjB,OAAM,IAAI,MAAM,2CAA2CA,CAAG,GAAG;AAEnE,WAAOS,EAAwBT,CAAG;EACpC;AAEA,MAAI,0BAA0B,KAAKA,CAAG,EACpC,KAAI;AACF,UAAMU,KAAQV,EAAI,MAAM,KAAK,EAAE,MAAM,CAAC,EAAE,IAAIW,CAAAA,OAAO,SAASA,IAAK,EAAE,CAAC,GAC9DC,KAAU,IAAI,YAAY,SAAS,EACvC,WAAW,MACX,OAAO,KACT,CAAC,EAAE,OAAO,IAAI,WAAWF,EAAK,CAAC,GACzBG,KAAU,IAAI;AAMpB,WALe,CAAC,GAAGD,EAAO,EAAE,IAAIE,CAAAA,OAAQ;AAEtC,YAAMd,KAAM,CAAC,GAAGa,GAAQ,OAAOC,EAAI,CAAC,EAAE,IAAIC,OAAQ,MAAMA,EAAK,SAAS,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE;AACpF,aAAOtB,EAAqBC,GAAKoB,EAAI,GAAGd,EAAG;IAC7C,CAAC;EAEH,QAAQ;AACN,UAAM,IAAI,MAAM,mBAAmBA,CAAG,sCAAsC;EAC9E;AAEF,MAAIM,MAAU,OAAOA,MAAU,IAC7B,QAAOb,EAAqBuB,EAAwBhB,CAAG,GAAGA,CAAG;AAE/D,MAAIiB,EAAgB,IAAIX,CAAK,EAC3B,QAAOb,EAAqBwB,EAAgB,IAAIX,CAAK,GAAIN,CAAG;AAI9D,MAAI,KAAK,KAAKM,CAAK,EACjB,QAAOY,EAAyBb,GAAaL,CAAG;AAElD,MAAIA,MAAQ,KACV,OAAM,IAAI,MAAMnD,wBAAwB;AAG1C,MAAIyD,MAAU,IAKZ,OAAM,IAAI,MAAM,qBAAqBN,CAAG,GAAG;AAG7C,MAAI,CAAC,GAAGA,CAAG,EAAE,WAAW,EACtB,QAAOP,EAAqBO,EAAI,YAAY,CAAC,GAAIA,CAAG;AAEtD,QAAM,IAAI,MAAM,sBAAsBA,CAAG,GAAG;AAC9C;AAUA,SAAST,EAAsBS,GAA2B;AACxD,SAAO,EACL,MAAM,cACN,KAAAA,EACF;AACF;AAOA,SAAStB,EAAqBY,GAAcU,GAA6B;AACvE,SAAO,EACL,MAAM,aACN,MAAAV,GACA,KAAAU,EACF;AACF;AAMA,SAASpB,EAAyBoB,GAAiC;AACjE,SAAO,EACL,MAAM,iBACN,KAAAA,EACF;AACF;AAOA,SAASP,EAAqB0B,GAAenB,GAA6B;AACxE,SAAO,EACL,MAAM,aACN,OAAAmB,GACA,KAAAnB,EACF;AACF;AAMA,SAASF,EAA+BE,GAAoC;AAC1E,SAAO,EACL,MAAM,uBACN,KAAAA,EACF;AACF;AAMA,SAASG,EAAgCH,GAAqC;AAC5E,SAAO,EACL,MAAM,wBACN,KAAAA,EACF;AACF;AAMA,SAASI,EAAqCJ,GAA2C;AACvF,SAAO,EACL,MAAM,6BACN,KAAAA,EACF;AACF;AAQA,SAASJ,EAA8BwB,GAAiBpB,GAAoD;AAC1G,SAAO,EACL,MAAM,sBACN,QAAAoB,GACA,KAAApB,EACF;AACF;AASA,SAASlB,EACPQ,GACAU,GACA5C,IAGI,CAAC,GACc;AACnB,SAAO,EACL,MAAM,gBACN,MAAAkC,GACA,GAAGlC,GACH,KAAA4C,EACF;AACF;AAYA,SAASnB,EAAqBS,GAA0BU,GAAa5C,IAAwC,CAAC,GAAmB;AAC/H,SAAIkC,MAAS,SACJ,EACL,MAAM,aACN,MAAAA,GACA,KAAAU,EACF,IAEK,EACL,MAAM,aACN,MAAAV,GACA,OAAO+B,EAAejE,EAAQ,KAAK,GACnC,KAAA4C,EACF;AACF;AAUA,SAASkB,EAAyBb,GAAsBL,GAAiC;AACvF,SAAO,EACL,MAAM,iBACN,aAAAK,GACA,KAAAL,EACF;AACF;AAMA,SAASb,EAAsBa,GAA2B;AACxD,SAAO,EACL,MAAM,cACN,KAAAA,EACF;AACF;AAWA,SAASd,EACPI,GACAU,GACA5C,IAKI,CAAC,GACW;AAChB,SAAO,EACL,MAAM,aACN,MAAAkC,GACA,GAAGlC,GACH,KAAA4C,EACF;AACF;AASA,SAASsB,EACPhC,GACAiC,GACAC,GACAxB,IACmB;AACnB,SAAO,EACL,MAAM,gBACN,MAAAV,GACA,KAAAiC,GACA,WAAWC,GACX,KAAAxB,GACF;AACF;AASA,SAASyB,EACPnC,GACAoC,GACAC,GACA3B,IACiB;AACjB,SAAO,EACL,MAAM,cACN,MAAAV,GACA,KAAAoC,GACA,KAAAC,GACA,KAAA3B,GACF;AACF;AAMA,SAASrB,EAAsBqB,GAA8B;AAC3D,SAAO,EACL,MAAM,cACN,KAAAA,EACF;AACF;AA4BA,IAAM4B,IAAe,oBAAI,IAAyD,CAChF,SACA,OACA,SACA,QACA,OACA,YACA,QACA,aACF,CAAC;AATD,IAWMX,IAAkB,oBAAI,IAAI,CAC9B,CAAC,KAAM,CAAC,GACR,CAAC,KAAM,CAAC,GACR,CAAC,KAAK,EAAE,GACR,CAAC,KAAK,EAAE,GACR,CAAC,KAAK,EAAE,GACR,CAAC,KAAK,EAAE,GACR,CAAC,KAAM,CAAC,GACR,CAAC,KAAK,EAAE,CACV,CAAC;AAGD,SAASV,EAAyBP,GAA6B;AAC7D,QAAMc,IAAOd,EAAI,CAAC,MAAM,MAAMA,EAAI,CAAC,IAAIA,EAAI,CAAC;AAC5C,MAAI,CAACc,KAAQ,CAAC,WAAW,KAAKA,CAAI,EAKhC,OAAM,IAAI,MAAM,kCAAkCd,CAAG,GAAG;AAE1D,SAAOP,EAAqBC,GAAKoB,EAAK,YAAY,CAAC,IAAI,IAAId,CAAG;AAChE;AAEA,SAASf,EAAqBe,GAAavC,GAAmD;AAE5F,MAAI,EAAC,IAAAoE,GAAI,KAAAC,GAAG,IAAI,0CAA0C,KAAK9B,CAAG,EAAG;AACrE8B,EAAAA,OAAQ;AAER,QAAMpE,MAASD,EAAQ,eAAe,KAAKoE,EAAG,SAAS,GAAG,MAAM,CAACC,GAAI,SAAS,GAAG,GAC3EC,KAAeC,EAAqBH,CAAE,GACtCI,KAAgBD,EAAqBF,EAAG,GACxCI,KAAkC,CAAC;AAIzC,MAHAH,OAAiBG,GAAY,SAASH,KACtCE,OAAkBC,GAAY,UAAUD,KAEpCjC,EAAI,SAAS,GAAG,EAElB,QAAAvC,EAAQ,mBAAmBC,EAAK,GAEzBmB,EAAqB,SAASmB,GAAK,EACxC,OAAOkC,GACT,CAAC;AAGH,MAAIlC,EAAI,SAAS,GAAG,EAClB,QAAAvC,EAAQ,SAASC,EAAK,GACtBD,EAAQ,iBACDyB,EAAqB,SAASc,GAAK,EACxC,IAAK+B,MAAgBE,OAAkB,EAAC,OAAOC,GAAW,EAC5D,CAAC;AAEH,QAAM,IAAI,MAAM,6BAA6BlC,CAAG,GAAG;AACrD;AAEA,SAAShB,EAAqBgB,GAAgC;AAC5D,QAAMmC,IAAU,wFAAwF,KAAKnC,CAAG;AAChH,MAAI,CAACmC,EACH,OAAM,IAAI,MAAM,wCAAwCnC,CAAG,GAAG;AAEhE,QAAM,EAAC,MAAAoC,GAAM,KAAAb,IAAK,MAAAC,GAAI,IAAIW,EAAQ;AAKlC,MAAI,CAACC,EACH,OAAM,IAAI,MAAM,0BAA0BpC,CAAG,GAAG;AAElD,MAAIuB,OAAQ,GACV,OAAM,IAAI,MAAM,mDAAmDvB,CAAG,GAAG;AAE3E,QAAMqC,KAAoCb,KACxCA,GAAK,MAAM,GAAG,EAEZ,OAAOc,OAAOA,MAAQ,EAAE,EACxB,IAAIA,OAAO,aAAa,KAAKA,CAAG,IAAI,CAACA,IAAMA,CAAG,IAChD,CAAC,GACG,CAACC,IAAMC,IAAMC,EAAI,IAAIJ,IACrB/C,IAA8BsC,EAAa,IAAIQ,CAA2D,IAC9GA,EAAK,YAAY,IACjB;AACF,UAAQ9C,GAAM;IACZ,KAAK;IACL,KAAK;IACL,KAAK;AACH,UAAI+C,GAAU,SAAS,EACrB,OAAM,IAAI,MAAM,wCAAwCA,EAAS,GAAG;AAEtE;IACF,KAAK;AACH,UAAIA,GAAU,SAAS,EACrB,OAAM,IAAI,MAAM,2CAA2CA,EAAS,GAAG;AAEzE,UAAI,OAAOE,MAAS,SAClB,OAAM,IAAI,MAAM,4CAA4CA,EAAI,GAAG;AAErE;IACF,KAAK;AACH,UAAI,CAACF,GAAU,UAAUA,GAAU,SAAS,EAC1C,OAAM,IAAI,MAAM,iDAAiDA,EAAS,GAAG;AAE/E,UAAI,OAAOE,MAAS,YAAY,CAAC,iBAAiB,KAAKA,EAAI,EACzD,OAAM,IAAI,MAAM,uDAAuDA,EAAI,GAAG;AAEhF,UAAIF,GAAU,WAAW,MAAM,OAAOG,MAAS,YAAY,CAAC,UAAU,KAAKA,EAAI,GAC7E,OAAM,IAAI,MAAM,iEAAiEA,EAAI,GAAG;AAE1F;IACF,KAAK;IACL,KAAK;AACH,UAAIH,GAAU,SAAS,EACrB,OAAM,IAAI,MAAM,2CAA2CA,EAAS,GAAG;AAEzE,UAAIA,GAAU,WAAW,MAAM,OAAOE,MAAS,YAAY,CAAC,UAAU,KAAKA,EAAI,GAC7E,OAAM,IAAI,MAAM,6DAA6DA,EAAI,GAAG;AAEtF;IACF,KAAK;AACH,UAAIF,GAAU,WAAW,EACvB,OAAM,IAAI,MAAM,4CAA4CA,EAAS,GAAG;AAE1E,UAAI,OAAOE,MAAS,YAAY,CAAC,iBAAiB,KAAKA,EAAI,EACzD,OAAM,IAAI,MAAM,uDAAuDA,EAAI,GAAG;AAEhF,UAAI,OAAOC,MAAS,YAAY,CAAC,qBAAqB,KAAKA,EAAI,EAC7D,OAAM,IAAI,MAAM,2EAA2EA,EAAI,GAAG;AAEpG,UAAI,OAAOC,MAAS,YAAY,CAAC,iBAAiB,KAAKA,EAAI,EACzD,OAAM,IAAI,MAAM,yDAAyDA,EAAI,GAAG;AAElF;IACF,KAAK;AAGH,YAAM,IAAI,MAAM,2BAA2BL,CAAI,GAAG;IACpD;AACE,YAAM,IAAI,MAAM,kCAAkC9C,CAAI,GAAG;EAC7D;AAGA,SAAOgC,EAAwBhC,GAAMiC,MAAO,MAAMC,IAAM,MAAM,GAAG,KAAK,MAAMxB,CAAG;AACjF;AAEA,SAAS0C,EAAmB1C,GAA8B;AACxD,MAAIV,IAA4B,MAC5BoC,GACAC;AACJ,MAAI3B,EAAI,CAAC,MAAM,KAAK;AAClB,UAAM,EAAC,QAAA2C,IAAQ,QAAAC,GAAM,IACnB,wCAAwC,KAAK5C,CAAG,EAAG,QAC/C6C,KAAQ;AACd,QAAI,CAACF,KAASE,MAAUD,MAAU,CAACA,KAASC,GAC1C,OAAM,IAAI,MAAM,2CAA2C;AAU7D,QARAnB,IAAM,CAACiB,IACPhB,KAAMiB,OAAW,SAAY,CAACD,KAAUC,OAAW,KAAK,IAAA,IAAW,CAACA,IAGhElB,IAAMC,OACRrC,IAAO,cACP,CAACoC,GAAKC,EAAG,IAAI,CAACA,IAAKD,CAAG,IAEpB1B,EAAI,SAAS,GAAG,GAAG;AACrB,UAAIV,MAAS,aAEX,OAAM,IAAI,MAAM,2DAA2D;AAE7EA,UAAO;IACT,MAAYA,OACVA,IAAO;EAEX,MACEoC,KAAM1B,EAAI,CAAC,MAAM,MAAM,IAAI,GAC3B2B,KAAM3B,EAAI,CAAC,MAAM,MAAM,IAAI,IAAA,GAC3BV,IAAOU,EAAI,CAAC,MAAM,MAAM,eAAgBA,EAAI,CAAC,MAAM,MAAM,SAAS;AAEpE,SAAOyB,EAAsBnC,GAAMoC,GAAKC,IAAK3B,CAAG;AAClD;AAEA,SAASQ,EAAkBR,GAAgC;AACzD,QAAM8C,IAAQ9C,EAAI,CAAC,EAAE,YAAY;AACjC,SAAOlB,EAAwB,EAC7B,GAAK,SACL,GAAK,OACL,GAAK,SACL,GAAK,OACP,EAAEgE,CAAK,GAA4B9C,GAAK,EACtC,QAAQA,EAAI,CAAC,MAAM8C,EACrB,CAAC;AACH;AAEA,SAASrC,EAAwBT,GAAgC;AAC/D,QAAM,EAAC,GAAA+C,GAAG,KAAAC,GAAK,OAAA7B,GAAK,IAAI,4CAA4C,KAAKnB,CAAG,EAAG;AAE/E,SAAOlB,EAAwB,YAAYkB,GAAK,EAC9C,OAAAmB,IACA,QAHc4B,MAAM,OAAO,CAACC,KAASD,MAAM,OAAO,CAAC,CAACC,EAItD,CAAC;AACH;AAEA,SAAShB,EAAqBiB,GAAyC;AAErE,QAAMC,IAAyB,CAAC;AAChC,SAAID,EAAM,SAAS,GAAG,MACpBC,EAAI,aAAa,OAEfD,EAAM,SAAS,GAAG,MAEpBC,EAAI,SAAS,OAEXD,EAAM,SAAS,GAAG,MACpBC,EAAI,WAAW,OAEV,OAAO,KAAKA,CAAG,EAAE,SAASA,IAAM;AACzC;AAEA,SAAS3F,EAAkB0F,GAA+B;AACxD,QAAM3F,IAAiC,EACrC,YAAY,OACZ,QAAQ,OACR,UAAU,OACV,cAAc,OACd,cAAc,OACd,cAAc,OACd,aAAa,OACb,iBAAiB,KACnB;AACA,WAASY,IAAI,GAAGA,IAAI+E,EAAM,QAAQ/E,KAAK;AACrC,UAAM4C,KAAOmC,EAAM/E,CAAC;AACpB,QAAI,CAAC,WAAW,SAAS4C,EAAI,EAC3B,OAAM,IAAI,MAAM,iBAAiBA,EAAI,GAAG;AAG1C,QAAIA,OAAS,KAAK;AAChB,UAAI,CAAC,WAAW,KAAKmC,EAAM,MAAM/E,CAAC,CAAC,EACjC,OAAM,IAAI,MAAM,sCAAsC;AAGxDZ,QAAe,kBAAkB2F,EAAM/E,IAAI,CAAC,MAAM,MAAM,aAAa,QACrEA,KAAK;AACL;IACF;AACAZ,MAAe,EACb,GAAG,cAGH,GAAG,UAEH,GAAG,YAEH,GAAG,gBACH,GAAG,gBACH,GAAG,gBACH,GAAG,cACL,EAAEwD,EAAI,CAAqD,IAAI;EACjE;AACA,SAAOxD;AACT;AAIA,SAAS0D,EAAwBhB,GAAqB;AAOpD,MAAI,kEAAkE,KAAKA,CAAG,EAC5E,OAAM,IAAI,MAAM,iCAAiCA,CAAG,GAAG;AAGzD,QAAMW,IAAMX,EAAI,CAAC,MAAM,MACrB,8BAA8B,KAAKA,CAAG,EAAG,OAAQ,MACjDA,EAAI,MAAM,CAAC;AACb,SAAO,SAASW,GAAK,EAAE;AACzB;AAIA,SAASvC,GAAwB+E,GAA2BhF,GAAwE;AAClI,QAAM,EAAC,KAAA6B,GAAK,aAAAK,GAAW,IAAI8C,GAErBhC,KAAQnB,EAAI,MAAM,CAAC;AAEzB,MACE,CAACK,OAEEc,OAAU,OAAOA,GAAM,WAAW,KAElCA,GAAM,CAAC,MAAM,OAAO,CAACA,MAAShD,GAGjC,QAAO,CAACS,EAAyBoB,CAAG,CAAC;AAEvC,QAAMrC,KAAgC,CAAC,GAEjCyF,KAAUjC,GAAM,MAAM,aAAa;AACzC,WAASjD,KAAI,GAAGA,KAAIkF,GAAQ,QAAQlF,MAAK;AACvC,UAAMG,KAAI+E,GAAQlF,EAAC;AACnB,QAAIiD;AAEJ,QAAIjD,OAAM,KAAKG,OAAM,OAAOA,OAAM,KAAA;AAEhC,UADA8C,IAAQ,SAAS9C,IAAG,CAAC,GACjB8C,IAAQ,IAEV,OAAM,IAAI,MAAMtE,8CAA8CmD,CAAG,GAAG;IAAA,MAGtEmB,KAAQzB,GAAKrB,EAAC;AAEhBV,IAAAA,GAAO,KAAK8B,EAAqB0B,IAAQjD,OAAM,IAAI,OAAO,MAAMG,EAAC,CAAC;EACpE;AACA,SAAOV;AACT;AAEA,SAAS6B,GAAqB6D,GAAqC;AACjE,QAAM1F,IAAiC,CAAC,GAMlC2F,IAAQ,IAAI,OAAOvG,GAAc,IAAI;AAC3C,MAAIa;AACJ,SAAQA,KAAQ0F,EAAM,KAAKD,CAAG,KAAI;AAChC,UAAMhF,KAAIT,GAAM,CAAC;AACjB,QAAIS,GAAE,CAAC,MAAM,KAAK;AAGhB,YAAMkF,KAAQ,mCAAmC,KAAKlF,EAAC;AACvD,UAAIkF,IAAO;AACT,cAAM,EAAC,KAAA7B,IAAK,KAAAC,GAAG,IAAI4B,GAAM;AACzB,YAAI,CAAC7B,KAAM,CAACC,MAAOtD,GAAE,SAAS,GAAG,GAAG;AAElCiF,YAAM,aACN3F,EAAO,KAAK+E,EAAmBrE,GAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AAC9C;QACF;MACF;IACF;AACAV,MAAO,KAAK+E,EAAmBrE,EAAC,CAAC;EACnC;AACA,SAAOV;AACT;;;AChrCA,SAAS6F,GAAaC,GAAwCC,GAAwB;AACpF,MAAI,CAAC,MAAM,QAAQD,EAAK,IAAI,EAC1B,OAAM,IAAI,MAAM,+BAA+B;AAEjD,MAAIA,EAAK,KAAK,WAAW,EACvB,QAAO;AAET,QAAME,KAAMF,EAAK,KAAK,CAAC;AACvB,SAAO,CAACC,KAAS,OAAO,KAAKA,CAAK,EAAE,MAAME,OAAOF,EAAME,CAAkB,MAAMD,GAAIC,CAAkB,CAAC;AACxG;AAmBA,SAASC,EAAeC,GAAsC;AAC5D,SAAOC,GAAkB,IAAID,EAAK,IAAI;AACxC;AACA,IAAMC,KAAoB,oBAAI,IAAkB,CAC9C,mBACA,iBACA,kBACA,aACA,kBACA,gBACA,SACA,cACA,YACF,CAAC;;;ACkGD,SAASC,GAAMC,GAAiBC,KAAwB,CAAC,GAAiB;AACxE,QAAMC,IAA+B,EACnC,OAAO,IACP,+BAA+B,OAC/B,uBAAuB,OACvB,0BAA0B,OAC1B,4BAA4B,OAE5B,oBAAoB,MACpB,GAAGD,IACH,OAAO,EACL,cAAc,OACd,YAAY,OACZ,GAAGA,GAAQ,MACb,EACF,GACME,IAAYC,EAASJ,GAAS,EAElC,OAAOE,EAAK,OACZ,OAAO,EACL,cAAcA,EAAK,MAAM,cACzB,YAAYA,EAAK,MAAM,WACzB,EACF,CAAC,GACKG,KAAwB,CAACC,GAAQC,MAAU;AAC/C,UAAMC,KAAQL,EAAU,OAAOM,GAAQ,SAAS;AAIhD,YAHAA,GAAQ,SAASH,GAEjBG,GAAQ,aACAD,GAAM,MAAM;MAClB,KAAK;AAEH,eAAOE,GAAkB;MAC3B,KAAK;AACH,eAAOC,GAAeH,EAAK;MAC7B,KAAK;AACH,eAAOI,GAAmBJ,IAAOC,EAAO;MAC1C,KAAK;AACH,eAAOI,GAAgBL,GAAM,OAAO,EAAC,cAAc,CAAC,CAACD,EAAM,mBAAkB,CAAC;MAChF,KAAK;AACH,eAAOO,IAA0BN,IAAOC,IAASF,CAAK;MACxD,KAAK;AACH,eAAOQ,GAAwBP,IAAOC,IAASF,CAAK;MACtD,KAAK;AACH,eAAOS,GAAkBR,IAAOC,EAAO;MACzC,KAAK;AACH,eAAOQ,GAAgBT,GAAM,MAAM,EAAC,OAAOA,GAAM,MAAK,CAAC;MACzD,KAAK;AACH,eAAOU,IAAeV,IAAOC,IAASF,CAAK;MAC7C,KAAK;AACH,eAAOY,GAAmBX,GAAM,MAAMA,GAAM,KAAKA,GAAM,SAAS;MAClE,KAAK;AACH,eAAOY,GAAgBZ,IAAOC,EAAO;MACvC,KAAK;AACH,eAAOY,GAAgBb,IAAOC,EAAO;MACvC;AACE,cAAM,IAAI,MAAM,0BAA0BD,GAAM,IAAI,GAAG;IAC3D;EACF,GACMC,KAAmB,EACvB,iBAAiB,CAAC,GAClB,gBAAgB,OAChB,mBAAmB,oBAAI,OACvB,WAAW,GACX,+BAA+BP,EAAK,+BACpC,QAAQ,MACR,uBAAuBA,EAAK,uBAC5B,0BAA0BA,EAAK,0BAC/B,4BAA4BA,EAAK,4BACjC,aAAa,CAAC,GACd,QAAQC,EAAU,QAClB,oBAAoBD,EAAK,oBACzB,MAAAG,GACF,GAGMiB,KAAMC,GAAYC,GAAYrB,EAAU,KAAK,CAAC;AACpD,MAAIsB,KAAMH,GAAI,KAAK,CAAC;AACpB,SAAOb,GAAQ,YAAYN,EAAU,OAAO,UAAQ;AAClD,UAAMuB,IAAOrB,GAAKoB,IAAK,CAAC,CAAC;AACrBC,MAAK,SAAS,iBAChBJ,GAAI,KAAK,KAAKI,CAAI,GAClBD,KAAMC,KAEND,GAAI,KAAK,KAAKC,CAA8B;EAEhD;AAIA,QAAM,EAAC,iBAAAC,IAAiB,gBAAAC,IAAgB,mBAAAC,GAAmB,aAAAC,GAAW,IAAIrB;AAC1E,MAAImB,MAAkBC,EAAkB,QAAQ,CAAC3B,EAAK,MAAM,aAC1D,OAAM,IAAI,MAAM,kEAAkE;AAEpF,aAAW,EAAC,KAAA6B,EAAG,KAAKD,GAClB,KAAI,OAAOC,KAAQ,UAAU;AAE3B,QAAIA,IAAMJ,GAAgB,OACxB,OAAM,IAAI,MAAM,mDAAmD;AAEjEI,UACFJ,GAAgBI,IAAM,CAAC,EAAE,gBAAgB;EAE7C,WAAYF,EAAkB,IAAIE,CAAG,GAE9B;AAAA,QAAIF,EAAkB,IAAIE,CAAG,EAAG,SAAS,EAC9C,OAAM,IAAI,MAAMC,+CAA+CD,CAAG,IAAI;AAEtEF,MAAkB,IAAIE,CAAG,EAAG,CAAC,EAAE,gBAAgB;EAAA,MAJ/C,OAAM,IAAI,MAAMC,wDAAwDD,CAAG,IAAI;AAQnF,SAAOT;AACT;AAEA,SAASX,GAAe,EAAC,MAAAsB,EAAI,GAAkC;AAC7D,SAAOC,GACLC,EAAe,EACb,KAAK,cACL,GAAK,YACL,OAAO,gBACP,OAAO,iBACP,OAAO,iBACP,OAAO,gBACP,OAAO,yBACP,OAAO,yBACP,OAAO,cACP,OAAO,qBACT,EAAEF,CAAI,GAAG,8BAA8BA,CAAI,GAAG,GAC9C,EAAC,QAAQA,MAASD,SAASC,MAASD,MAAK,CAC3C;AACF;AAgBA,SAASpB,GAAmB,EAAC,KAAAwB,EAAG,GAAuB3B,IAAqC;AAC1F,QAAM4B,IAAc,WAAW,KAAKD,CAAG,GACjCL,IAAMM,IAAcD,EAAI,MAAM,GAAG,EAAE,IAAIA,EAAI,MAAM,CAAC,GAClDE,KAAU,CAACC,IAAaC,KAAa,UAAU;AACnD,UAAMC,KAAoBhC,GAAQ,gBAAgB;AAClD,QAAIiC,KAAS;AAcb,QAAIH,KAAME,GAIR,KAAIhC,GAAQ,sBACViC,CAAAA,KAAS;QAET,OAAM,IAAI,MAAM,oDAAoDN,CAAG,GAAG;AAG9E,WAAA3B,GAAQ,iBAAiB,MAClBkC,GAAoBH,KAAaC,KAAoB,IAAIF,KAAMA,IAAK,EAAC,QAAAG,GAAM,CAAC;EACrF;AACA,MAAIL,GAAa;AACf,UAAMO,KAAc,kCAAkC,KAAKb,CAAG;AAC9D,QAAIa,GACF,QAAON,GAAQ,CAACM,GAAY,OAAQ,KAAK,CAAC,CAACA,GAAY,OAAQ,IAAI;AAGrE,QAAI,OAAO,KAAKb,CAAG,EACjB,OAAM,IAAI,MAAM,yBAAyBK,CAAG,GAAG;AAEjD,QAAI,CAAC3B,GAAQ,kBAAkB,IAAIsB,CAAG,EACpC,OAAM,IAAI,MAAM,uCAAuCK,CAAG,GAAG;AAE/D,WAAOO,GAAoBZ,CAAG;EAChC;AACA,SAAOO,GAAQ,CAACP,CAAG;AACrB;AAEA,SAASjB,IAA0B+B,GAA8BpC,IAAkBF,GAAuD;AACxI,QAAM,EAAC,QAAAuC,GAAQ,MAAAzC,GAAI,IAAII,IACjBH,KAASG,GAAQ,QACjBsC,KAAkBzC,GAAO,KAAK,GAAG,EAAE,GACnC0C,KAAYF,EAAOrC,GAAQ,SAAS;AAC1C,MACE,CAACF,EAAM,sBACPwC,MACAA,GAAgB,SAAS,oBACzBA,GAAgB,SAAS,yBACzBC,MACAA,GAAU,SAAS,wBACnBA,GAAU,SAAS,yBACnBA,GAAU,SAAS,6BACnB;AACA,UAAMC,KAAW5C,GAAKC,IAAQ,EAC5B,GAAGC,GACH,oBAAoB,KACtB,CAAC;AACD,QAAIwC,GAAgB,SAAS,eAAeE,GAAS,SAAS,YAC5D,QAAA3C,GAAO,KAAK,IAAI,GACT4C,GAA0BH,IAAiBE,EAAQ;AAE5D,UAAM,IAAI,MAAM,+BAA+B;EACjD;AACA,SAAOpC,GAAgBsC,GAAK,GAAG,CAAC;AAClC;AAEA,SAASpC,GAAwB,EAAC,QAAAqC,EAAM,GAA4B3C,IAAkBF,GAAkC;AACtH,QAAM,EAAC,QAAAuC,GAAQ,MAAAzC,GAAI,IAAII,IACjB4C,KAAkBP,EAAOrC,GAAQ,SAAS,GAC1C6C,KAAgB,CAACC,GAAqB,CAAC;AAC7C,MAAIP,KAAYQ,GAA8BH,EAAe;AAC7D,SAAOL,GAAU,SAAS,yBAAuB;AAC/C,QAAIA,GAAU,SAAS,4BACrBM,CAAAA,GAAc,KAAKC,GAAqB,CAAC,GAEzC9C,GAAQ;SACH;AACL,YAAMgD,KAAKH,GAAc,GAAG,EAAE;AAC9BG,MAAAA,GAAG,KAAK,KAAKpD,GAAKoD,IAAIlD,CAAK,CAA8B;IAC3D;AACAyC,IAAAA,KAAYQ,GAA8BV,EAAOrC,GAAQ,SAAS,GAAG4C,EAAe;EACtF;AACA,QAAM3B,KAAO6B,GAAqB,EAAC,QAAAH,EAAM,CAAC;AAC1C,SAAIE,GAAc,WAAW,IAC3B5B,GAAK,OAAO4B,GAAc,CAAC,EAAE,QAE7B5B,GAAK,OAAO,gBACZA,GAAK,OAAO4B,GAAc,IAAIG,CAAAA,OAAMA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,CAAC,IAAIA,EAAE,IAG5EhD,GAAQ,aACDiB;AACT;AAEA,SAASV,GAAkB,EAAC,MAAAiB,GAAM,QAAAmB,IAAQ,OAAAM,EAAK,GAAsBjD,GAAoC;AACvG,QAAM,EAAC,+BAAAkD,IAA+B,4BAAAC,IAA4B,oBAAAC,GAAkB,IAAIpD;AACxF,MAAIwB,MAAS,YAAY;AACvB,UAAM6B,KAAaC,GAAKL,CAAM;AAE9B,QAAIM,EAAgB,IAAIF,EAAU,KAAK,CAACD,IAAoB,IAAIC,EAAU,EACxE7B,KAAO,SACPyB,IAAQI;QAER,QAAOG,GAAsBP,GAAQ,EACnC,QAAAN,IACA,+BAAAO,IACA,4BAAAC,IACA,oBAAAC,GACF,CAAC;EAEL;AACA,SAAI5B,MAAS,UACJiC,GAAiBR,GAAQ,EAAC,QAAAN,GAAM,CAAC,IAEnCe,GAAmBlC,GAAM,EAAC,QAAAmB,GAAM,CAAC;AAC1C;AAEA,SAASlC,IAAeV,GAAuBC,IAAkBF,GAA8F;AAC7J,QAAM,EAAC,QAAAuC,GAAQ,iBAAAnB,IAAiB,mBAAAE,IAAmB,0BAAAuC,IAA0B,MAAA/D,GAAI,IAAII,IAC/EiB,KAAO2C,GAAkB7D,CAAK,GAC9B8D,KAAwB5C,GAAK,SAAS,mBACtC6C,IAAmBC,GAAa9C,EAAI,GACpC+C,KAAsBF,KAAoB7C,GAAK;AAUrD,MAPIA,GAAK,SAAS,qBAChBC,GAAgB,KAAKD,EAAI,GACrBA,GAAK,QACPgD,EAAY7C,IAAmBH,GAAK,MAAM,CAAC,CAAC,EAAE,KAAKA,EAAI,IAIvD4C,MAAyB/D,EAAM,oBAEjC,OAAM,IAAI,MAAM,oDAAoD;AAEtE,MAAIyC,IAAY2B,GAAqB7B,EAAOrC,GAAQ,SAAS,CAAC;AAC9D,SAAOuC,EAAU,SAAS,gBAAc;AACtC,QAAIA,EAAU,SAAS,aACrBtB,CAAAA,GAAK,KAAK,KAAKhB,GAAkB,CAAC,GAElCD,GAAQ;SACH;AACL,YAAMmE,IAAMlD,GAAK,KAAK,GAAG,EAAE,GACrBmD,KAAQxE,GAAKuE,GAAK,EACtB,GAAGrE,GACH,qBAAqBA,EAAM,uBAAuB+D,IAClD,gBAAgB/D,EAAM,kBAAkBgE,GACxC,mBAAmBhE,EAAM,qBAAqBkE,GAChD,CAAC;AAGD,UAFAG,EAAI,KAAK,KAAKC,EAAK,IAEdN,KAAoBhE,EAAM,mBAAmB,CAAC6D,IAA0B;AAI3E,cAAMU,KAAM;AACZ,YAAIL,MAAuBlE,EAAM,mBAAA;AAG/B,cAAIwE,GAAYF,EAAK,KAAKA,GAAM,SAAS,iBACvC,OAAM,IAAI,MAAMC,EAAG;QAAA,WAKjBC,GAAYF,EAAK,KAAML,GAAaK,EAAK,KAAKA,GAAM,OACtD,OAAM,IAAI,MAAMC,EAAG;MAGzB;IACF;AACA9B,QAAY2B,GAAqB7B,EAAOrC,GAAQ,SAAS,CAAC;EAC5D;AAEA,SAAAA,GAAQ,aACDiB;AACT;AAEA,SAASN,GAAgB,EAAC,MAAAa,GAAM,KAAA+C,IAAK,KAAAC,EAAG,GAAoBxE,GAAkC;AAC5F,QAAMH,KAASG,EAAQ,QACjByE,KAAiB5E,GAAO,KAAK,GAAG,EAAE;AACxC,MAAI,CAAC4E,MAAkB,CAACC,EAAeD,EAAc,EACnD,OAAM,IAAI,MAAM,wCAAwC;AAE1D,QAAMxD,KAAO0D,GAAiBnD,GAAM+C,IAAKC,GAAKC,EAAc;AAC5D,SAAA5E,GAAO,KAAK,IAAI,GACToB;AACT;AA8BA,SAASL,GAAgB,EAAC,KAAAe,EAAG,GAAoB3B,IAAkC;AACjF,QAAM,EAAC,iBAAAkB,GAAiB,aAAAG,EAAW,IAAIrB;AACvC,MAAIsB,KAAuBK,EAAI,MAAM,GAAG,EAAE;AAC1C,QAAMQ,KAAc,qCAAqC,KAAKb,EAAG;AACjE,MAAIa,IAAa;AACf,UAAML,KAAM,CAACK,GAAY,OAAQ,KAC3BH,KAAoBd,EAAgB;AAO1C,QANAlB,GAAQ,iBAAiB,MACzBsB,KAAM,EACJ,IAAIQ,IACJ,KAAKE,KAAoBF,IACzB,KAAKE,KAAoB,IAAIF,GAC/B,EAAEK,GAAY,OAAQ,IAAI,GACtBb,KAAM,EACR,OAAM,IAAI,MAAM,2BAA2B;EAG/C,MAAWA,CAAAA,OAAQ,QACjBA,KAAM;AAER,QAAML,KAAO2D,GAAiBtD,EAAG;AACjC,SAAAD,EAAY,KAAKJ,EAAI,GACdA;AACT;AAWA,SAAS4D,EAAsBrD,GAA+BhC,IAEtC;AACtB,MAAIgC,MAAS,WACX,OAAM,IAAI,MAAM,qCAAqCA,CAAI,GAAG;AAE9D,SAAO,EACL,MAAM,mBACN,MAAAA,GACA,MAAMsD,EAA+BtF,IAAS,IAAI,EACpD;AACF;AAMA,SAASS,GAAkBT,GAEP;AAClB,SAAO,EACL,MAAM,eACN,MAAMuF,GAA2BvF,GAAS,IAAI,EAChD;AACF;AAOA,SAASiC,GAAgBD,GAAyBhC,IAEhC;AAChB,QAAMyB,IAAsB,EAC1B,MAAM,aACN,MAAAO,EACF;AACA,UAAIA,MAAS,mBAAmBA,MAAS,6BACvCP,EAAK,SAAS,CAAC,CAACzB,IAAS,SAEpByB;AACT;AAOA,SAASiB,GAAoBZ,GAAsB9B,IAE7B;AACpB,QAAMyC,IAAS,CAAC,CAACzC,IAAS;AAC1B,SAAO,EACL,MAAM,iBACN,KAAA8B,GACA,GAAIW,KAAU,EAAC,QAAAA,EAAM,EACvB;AACF;AAWA,SAAS+C,GAAqBC,GAAgBzF,IAIvB;AACrB,QAAMC,IAAO,EACX,MAAM,QACN,eAAe,OACf,GAAGD,GACL;AACA,MAAIC,EAAK,SAAS,UAAa,CAACyF,GAAiBzF,EAAK,IAAI,EACxD,OAAM,IAAI,MAAM,eAAeA,EAAK,IAAI,wBAAwB;AAElE,SAAO,EACL,MAAM,kBACN,QAAAwF,GACA,GAAIxF,EAAK,QAAQ,EAAC,MAAMA,EAAK,KAAI,GACjC,GAAIA,EAAK,iBAAiB,EAAC,eAAeA,EAAK,cAAa,GAC5D,MAAMqF,EAA+BtF,IAAS,IAAI,EACpD;AACF;AAMA,SAASY,GAAgB+E,GAAkB3F,IAEzB;AAChB,QAAMC,IAAO,EACX,cAAc,OACd,GAAGD,GACL;AACA,MAAI2F,IAAW,SAAU;AACvB,UAAMC,IAAMD,EAAS,SAAS,EAAE;AAChC,QAAI1F,EAAK,aACP0F,KAAW;QACN,OAAIA,IAAW,UACd,IAAI,MAAM,wCAAwCC,CAAG,IAAI,IAEzD,IAAI,MAAM,8CAA8CA,CAAG,IAAI;EAEzE;AACA,SAAO,EACL,MAAM,aACN,OAAOD,EACT;AACF;AAQA,SAASrC,GAAqBtD,GAIP;AACrB,QAAMC,KAAO,EACX,MAAM,SACN,QAAQ,OACR,GAAGD,EACL;AACA,SAAO,EACL,MAAM,kBACN,MAAMC,GAAK,MACX,QAAQA,GAAK,QACb,MAAMsF,GAA2BvF,GAAS,IAAI,EAChD;AACF;AAOA,SAASiD,GAA0B8B,GAAoBC,IAA6C;AAClG,MAAIA,GAAI,QAAQD,EAAI,MAClB,OAAM,IAAI,MAAM,oCAAoC;AAEtD,SAAO,EACL,MAAM,uBACN,KAAAA,GACA,KAAAC,GACF;AACF;AAoBA,SAASd,GAAmBlC,GAAuChC,IAEvC;AAC1B,QAAMmD,IAAS,CAAC,CAACnD,IAAS,QACpByB,IAAgC,EACpC,MAAM,gBACN,MAAAO,EACF;AACA,UACEA,MAAS,WACTA,MAAS,SACTA,MAAS,aACTA,MAAS,WACTA,MAAS,YAETP,EAAK,SAAS0B,KAGdnB,MAAS,kBACRA,MAAS,aAAa,CAACmB,OAExB1B,EAAK,iBAAiB,OAEjBA;AACT;AAWA,SAAST,GAAgBgB,GAAyBhC,KAAwC,CAAC,GAAkB;AAC3G,MAAIgC,MAAS,OACX,QAAO,EACL,MAAM,aACN,MAAAA,EACF;AAEF,MAAIA,MAAS,QAGX,QAAO,EACL,MAAM,aACN,MAAAA,GACA,OAAOE,EAAelC,GAAQ,KAAK,EACrC;AAEF,QAAM,IAAI,MAAM,8BAA8BgC,CAAI,GAAG;AACvD;AAKA,SAAST,GAAYsE,GAAkC;AACrD,SAAO,EACL,MAAM,SACN,GAAGA,EACL;AACF;AASA,SAASC,GAAY9F,GAIP;AACZ,QAAM+F,KAAS/F,GAAS,QAClB6F,IAAQ7F,GAAS;AACvB,MAAI+F,MAAUF,EACZ,OAAM,IAAI,MAAM,gCAAgC;AAElD,SAAO,EACL,MAAM,SACN,GAAIE,MAAU,EAAC,QAAAA,GAAM,GACrB,GAAIF,KAAS,EAAC,OAAAA,EAAK,GACnB,MAAMP,EAA+BtF,GAAS,IAAI,EACpD;AACF;AAQA,SAASgG,GAA0BhG,GAIP;AAC1B,QAAMC,KAAO,EACX,QAAQ,OACR,QAAQ,OACR,GAAGD,EACL;AACA,SAAO,EACL,MAAM,uBACN,MAAMC,GAAK,SAAS,eAAe,aACnC,QAAQA,GAAK,QACb,MAAMqF,EAA+BtF,GAAS,IAAI,EACpD;AACF;AAQA,SAASkB,GACPc,GACAiE,IACAC,GACkB;AAClB,SAAO,EACL,MAAM,gBACN,MAAAlE,GACA,KAAAiE,IACA,WAAWC,EACb;AACF;AAEA,SAASjC,GAAiBkC,GAAcnG,IAEI;AAC1C,QAAMmD,IAAS,CAAC,CAACnD,IAAS;AAC1B,MAAI,CAAC+D,EAAgB,IAAIoC,CAAI,EAC3B,OAAM,IAAI,MAAM,wBAAwBA,CAAI,GAAG;AAEjD,SAAO,EACL,MAAM,gBACN,MAAM,SACN,OAAOA,GACP,QAAAhD,EACF;AACF;AASA,SAASgC,GAAiBnD,GAA0B+C,IAAaC,GAAaoB,GAAwC;AACpH,MAAIrB,KAAMC,EACR,OAAM,IAAI,MAAM,mCAAmC;AAErD,SAAO,EACL,MAAM,cACN,MAAAhD,GACA,KAAA+C,IACA,KAAAC,GACA,MAAAoB,EACF;AACF;AAOA,SAAS9E,GAAYuE,GAAkB7F,IAEzB;AACZ,SAAO,EACL,MAAM,SACN,MAAMsF,EAA+BtF,IAAS,IAAI,GAClD,OAAA6F,EACF;AACF;AAMA,SAAST,GAAiBtD,GAAsC;AAC9D,SAAO,EACL,MAAM,cACN,KAAAA,EACF;AACF;AAQA,SAASkC,GAAsBmC,GAAcnG,IAAoF;AAC/H,QAAMC,IAA+C,EACnD,QAAQ,OACR,+BAA+B,OAC/B,4BAA4B,OAC5B,oBAAoB,MACpB,GAAGD,GACL;AACA,MAAI6D,IAAa5D,EAAK,oBAAoB,IAAI6D,GAAKqC,CAAI,CAAC;AACxD,MAAI,CAACtC,GAAAA;AACH,QAAI5D,EAAK,8BACP4D,KAAawC,GAA6BF,CAAI;aAErClG,EAAK,sBAAsB,CAACA,EAAK,2BAC1C,OAAM,IAAI,MAAM8B,iCAAiCoE,CAAI,IAAI;EAAA;AAG7D,SAAO,EACL,MAAM,gBACN,MAAM,YACN,OAAOtC,KAAcsC,GACrB,QAAQlG,EAAK,OACf;AACF;AAMA,SAASmE,GAAkB,EAAC,OAAAyB,GAAO,MAAA7D,IAAM,MAAAmE,GAAM,QAAAhD,GAAQ,QAAAsC,GAAM,GAAmG;AAC9J,UAAQzD,IAAM;IACZ,KAAK;AACH,aAAOqD,EAAsB,UAAU;IACzC,KAAK;AACH,aAAOS,GAAY,EAAC,QAAQ,KAAI,CAAC;IACnC,KAAK;AACH,aAAON,GAAqBC,IAAS,EAAC,MAAAU,EAAI,CAAC;IAC7C,KAAK;AACH,aAAOL,GAAY,EAAC,OAAAD,EAAK,CAAC;IAC5B,KAAK;IACL,KAAK;AACH,aAAOG,GAA0B,EAC/B,QAAQhE,OAAS,cACjB,QAAAmB,EACF,CAAC;IACH;AACE,YAAM,IAAI,MAAM,0BAA0BnB,EAAI,GAAG;EACrD;AACF;AAEA,SAASsD,EAA+Bc,GAAuC;AAC7E,MAAIA,MAAS,OACXA,KAAO,CAAC3F,GAAkB,CAAC;WAClB,CAAC,MAAM,QAAQ2F,CAAI,KAAK,CAACA,EAAK,UAAU,CAACA,EAAK,MAAM3E,CAAAA,OAASA,GAAc,SAAS,aAAa,EAC1G,OAAM,IAAI,MAAM,+DAA+D;AAEjF,SAAO2E;AACT;AAEA,SAASb,GAA2Ba,GAA4B;AAC9D,MAAIA,MAAS,OACXA,KAAO,CAAC;WACC,CAAC,MAAM,QAAQA,CAAI,KAAK,CAACA,EAAK,MAAM3E,CAAAA,OAAQ,CAAC,CAAEA,GAAc,IAAI,EAC1E,OAAM,IAAI,MAAM,uCAAuC;AAEzD,SAAO2E;AACT;AAEA,SAAStB,GAAYrD,GAAqE;AACxF,SAAOA,EAAK,SAAS,yBAAyBA,EAAK,SAAS;AAC9D;AAEA,SAAS8C,GAAa9C,GAAsE;AAC1F,SAAOA,EAAK,SAAS,yBAAyBA,EAAK,SAAS;AAC9D;AAEA,SAASiE,GAAiBS,GAAuB;AAG/C,SAAO,4BAA4B,KAAKA,CAAI;AAC9C;AAEA,SAASE,GAA6BF,GAAsB;AAI1D,SAAOA,EACL,KAAK,EACL,QAAQ,WAAW,GAAG,EACtB,QAAQ,yBAAyB,KAAK,EACtC,QAAQ,cAAcG,CAAAA,OAAKA,GAAE,CAAC,EAAE,YAAY,IAAIA,GAAE,MAAM,CAAC,EAAE,YAAY,CAAC;AAC5E;AAKA,SAASxC,GAAKqC,GAAsB;AAClC,SAAOA,EAAK,QAAQ,WAAW,EAAE,EAAE,YAAY;AACjD;AAEA,SAAS5C,GAAiChD,GAAU6C,IAAyC;AAC3F,SAAOlB,EACL3B,GAGA,GAAG6C,IAAiB,SAAS,eAAeA,GAAgB,UAAU,KACpE,UAAU,UAAU,kBACxB;AACF;AAEA,SAASsB,GAAwBnE,GAA0B;AACzD,SAAO2B,EAAe3B,GAAO,gBAAgB;AAC/C;;;AC7gCA,IAAM,iBAAiB;AAEvB,IAAM,kCAAkC,oBAAI,IAAI;AAAA,EAC9C,GAAG,GAAK;AAAA;AAAA,EACR,GAAG,GAAK;AAAA;AACV,CAAC;AAGD,IAAM,kBAAkB;AAExB,SAAS,wBAAwB,MAAM;AAErC,MAAI,gCAAgC,IAAI,IAAI,GAAG;AAC7C,WAAO,CAAC,IAAI;AAAA,EACd;AACA,QAAM,MAAM,oBAAI,IAAI;AACpB,QAAM,QAAQ,KAAK,YAAY;AAE/B,QAAM,QAAQ,MAAM,YAAY;AAChC,QAAM,QAAQ,oBAAoB,IAAI,KAAK;AAC3C,QAAM,WAAW,+BAA+B,IAAI,KAAK;AACzD,QAAM,WAAW,+BAA+B,IAAI,KAAK;AAIzD,MAAI,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG;AAC3B,QAAI,IAAI,KAAK;AAAA,EACf;AACA,cAAY,IAAI,IAAI,QAAQ;AAC5B,WAAS,IAAI,IAAI,KAAK;AAEtB,MAAI,IAAI,KAAK;AACb,cAAY,IAAI,IAAI,QAAQ;AAC5B,SAAO,CAAC,GAAG,GAAG;AAChB;AAeA,IAAM,uBAAuC,oBAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBA2FE,MAAM,IAAI,EACV,IAAI,OAAK,CAACgG,GAAK,CAAC,GAAG,CAAC,CAAC;AACvB;AAEA,IAAM,iCAAiC,oBAAI,IAAI;AAAA,EAC7C,CAAC,KAAK,GAAG,GAAK,CAAC;AAAA;AAAA,EACf,CAAC,GAAG,GAAK,GAAG,GAAG;AAAA;AACjB,CAAC;AAED,IAAM,iCAAiC,oBAAI,IAAI;AAAA,EAC7C,CAAC,GAAG,GAAI,GAAG,GAAG,IAAM,CAAC;AAAA;AAAA,EACrB,CAAC,GAAG,GAAI,GAAG,GAAG,IAAM,CAAC;AAAA;AAAA,EACrB,CAAC,GAAG,GAAI,GAAG,GAAG,IAAM,CAAC;AAAA;AAAA,EACrB,CAAC,GAAG,GAAK,GAAG,GAAG,IAAM,CAAC;AAAA;AACxB,CAAC;AAGD,IAAM,sBAAsB,IAAI,IAAI;AAAA,EAClC,WAAW,GAAK;AAAA,EAChB,WAAW,GAAK;AAAA,EAChB,WAAW,GAAK;AAAA,EAChB,WAAW,GAAK;AAAA,EAChB,GAAG,WAAW,MAAQ,IAAM;AAAA,EAC5B,GAAG,WAAW,MAAQ,IAAM;AAAA,EAC5B,GAAG,WAAW,MAAQ,IAAM;AAAA,EAC5B,WAAW,IAAM;AAAA,EACjB,WAAW,IAAM;AAAA,EACjB,WAAW,IAAM;AACnB,CAAC;AAQD,IAAM,gBAAgB,oBAAI,IAAI;AAAA,EAC5B,CAAC,SAAS,oBAAoB;AAAA,EAC9B,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,SAAS,aAAa;AAAA,EACvB,CAAC,SAAS,SAAS;AAAA,EACnB,CAAC,SAAS,SAAS;AAAA,EACnB,CAAC,SAAS,sCAAsC;AAAA,EAChD,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,SAAS,8CAA8C;AAAA,EACxD,CAAC,SAAS,eAAe;AAAA;AAAA,EACzB,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,SAAS,YAAY;AAAA,EACtB,CAAC,QAAQ,+BAA+B;AAAA,EACxC,CAAC,UAAU,WAAW;AACxB,CAAC;AAED,SAAS,MAAM,OAAO,KAAK;AAGzB,QAAMC,SAAQ,CAAC;AACf,WAASC,KAAI,OAAOA,MAAK,KAAKA,MAAK;AACjC,IAAAD,OAAM,KAAKC,EAAC;AAAA,EACd;AACA,SAAOD;AACT;AAEA,SAAS,WAAW,WAAW;AAC7B,QAAM,OAAO,GAAG,SAAS;AACzB,SAAO,CAAC,KAAK,YAAY,GAAG,IAAI;AAClC;AAEA,SAAS,WAAW,OAAO,KAAK;AAC9B,SAAO,MAAM,OAAO,GAAG,EAAE,IAAI,eAAa,WAAW,SAAS,CAAC;AACjE;AAEA,IAAM,oCAAoC,oBAAI,IAAI;AAAA,EAChD;AAAA,EAAS;AAAA,EACT;AAAA,EAAS;AAAA,EACT;AAAA,EAAM;AAAA,EACN;AAAA,EAAM;AAAA,EACN;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAKR,CAAC;;;AC1JD,SAASE,EACPC,IACAC,IACAC,IAAsB,MAChB;AACN,WAASC,GAAcC,GAAuCC,IAAwB;AACpF,aAASC,IAAI,GAAGA,IAAIF,EAAM,QAAQE,KAAK;AACrC,YAAMC,KAAWC,EAAaJ,EAAME,CAAC,GAAGD,IAAQC,GAAGF,CAAK;AACxDE,UAAI,KAAK,IAAI,IAAIA,IAAIC,EAAQ;IAC/B;EACF;AACA,WAASC,EACPC,GACAJ,KAAyB,MACzBK,IAAmB,MACnBC,KAA+B,MACvB;AACR,QAAIJ,KAAW,GACXK,IAA2B;AAC/B,UAAMC,KAAa,EACjB,MAAAJ,GACA,QAAAJ,IACA,KAAAK,GACA,WAAAC,IACA,MAAMX,IACN,SAAS;AACPc,MAAAA,GAAeH,EAAS,EAAE,OAAO,KAAK,IAAI,GAAGI,GAAWL,CAAG,IAAIH,EAAQ,GAAG,CAAC,GAC3EA,MACAK,IAA2B;IAC7B,GACA,wBAAwB;AACtB,aAAOE,GAAeH,EAAS,EAAE,OAAOI,GAAWL,CAAG,IAAI,CAAC;IAC7D,GACA,wBAAwB;AACtB,YAAMM,KAAUD,GAAWL,CAAG,IAAIH;AAClC,aAAAA,MAAYS,IACLF,GAAeH,EAAS,EAAE,OAAO,GAAG,KAAK,IAAI,GAAGK,EAAO,CAAC;IACjE,GACA,YAAYC,IAASC,KAAU,CAAC,GAAG;AACjC,YAAMC,KAAc,CAAC,CAACD,GAAQ;AAC1BP,MAAAA,KACFA,GAAU,KAAK,IAAI,GAAGI,GAAWL,CAAG,IAAIH,EAAQ,CAAC,IAAIU,KAOrDG,EAAef,IAAQ,yBAAyB,EAAEK,CAAa,IAAIO,IAEjEE,MACFX,EAAaS,IAASZ,IAAQK,GAAKC,EAAS,GAE9CC,IAA2B;IAC7B,GACA,oBAAoBS,IAAUH,KAAU,CAAC,GAAG;AAC1C,YAAMC,KAAc,CAAC,CAACD,GAAQ;AAG9B,UAFAJ,GAAeH,EAAS,EAAE,OAAO,KAAK,IAAI,GAAGI,GAAWL,CAAG,IAAIH,EAAQ,GAAG,GAAG,GAAGc,EAAQ,GACxFd,MAAYc,GAAS,SAAS,GAC1BF,IAAa;AACf,YAAIG,IAAiB;AACrB,iBAAShB,KAAI,GAAGA,KAAIe,GAAS,QAAQf,KACnCgB,MAAkBd,EAAaa,GAASf,EAAC,GAAGD,IAAQU,GAAWL,CAAG,IAAIJ,KAAIgB,GAAgBX,EAAS;MAEvG;AACAC,UAA2B;IAC7B,GACA,OAAO;AACLA,UAA2B;IAC7B,EACF,GAEM,EAAC,MAAAW,GAAI,IAAId,GACTe,KAAiBvB,GAAQ,GAAG,GAC5BwB,IAAkBxB,GAAQsB,EAAI,GAC9BG,KAAa,OAAOF,MAAmB,aAAaA,KAAiBA,IAAgB,OACrFG,KAAc,OAAOF,KAAoB,aAAaA,IAAkBA,GAAiB;AAO/F,QAJAC,KAAab,IAAMX,CAAK,GAExByB,KAAcd,IAAMX,CAAK,GAErB,CAACU,EACH,SAAQW,IAAM;MACZ,KAAK;MACL,KAAK;MACL,KAAK;AACHpB,QAAAA,GAAcM,EAAK,MAAMA,CAAI;AAC7B;MACF,KAAK;MACL,KAAK;AACHN,QAAAA,GAAcM,EAAK,MAAMA,CAAI;AAC7B;MACF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH;MACF,KAAK;AACHD,UAAaC,EAAK,KAAKA,GAAM,KAAK,GAClCD,EAAaC,EAAK,KAAKA,GAAM,KAAK;AAClC;MACF,KAAK;AACHN,QAAAA,GAAcM,EAAK,MAAMA,CAAI;AAC7B;MACF,KAAK;AACHD,UAAaC,EAAK,MAAMA,GAAM,MAAM;AACpC;MACF,KAAK;AACHN,QAAAA,GAAcM,EAAK,MAAMA,CAAI,GAC7BD,EAAaC,EAAK,OAAOA,GAAM,OAAO;AACtC;MACF;AACE,cAAM,IAAI,MAAM,yBAAyBc,EAAI,GAAG;IACpD;AAIF,WAACE,GAA+D,OAAOZ,IAAMX,CAAK,GAEjFsB,IAA6D,OAAOX,IAAMX,CAAK,GACzEK;EACT;AACA,SAAAC,EAAaR,EAAI,GACVA;AACT;AAEA,SAASc,GAAec,IAA6B;AACnD,MAAI,CAAC,MAAM,QAAQA,EAAK,EACtB,OAAM,IAAI,MAAM,oBAAoB;AAEtC,SAAOA;AACT;AAEA,SAASb,GAAWa,IAAwB;AAC1C,MAAI,OAAOA,MAAU,SACnB,OAAM,IAAI,MAAM,sBAAsB;AAExC,SAAOA;AACT;;;ACjLA,SAAS,UAAU,KAAK,SAAS;AAC/B,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOX,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,GAAG;AAAA,EACL;AAEA,sBAAoB,GAAG;AACvB,QAAM,iBAAiB;AAAA,IACrB,UAAU,KAAK;AAAA,IACf,qBAAqB,KAAK;AAAA,IAC1B,eAAe,KAAK;AAAA,IACpB,qBAAqB,oBAAI,IAAI;AAAA,IAC7B,gBAAgB,oBAAI,IAAI;AAAA,IACxB,iBAAiB,YAAY,KAAK,kBAAkB,QAAQ;AAAA,IAC5D,kBAAkB;AAAA,IAClB,UAAU;AAAA;AAAA,IAEV,kBAAkB,oBAAI,IAAI;AAAA,IAC1B,iBAAiB,oBAAI,IAAI;AAAA,IACzB,cAAc,IAAI,MAAM;AAAA,IACxB,cAAc,IAAI,MAAM;AAAA,IACxB,aAAa,IAAI,MAAM;AAAA,EACzB;AACA,IAAS,KAAK,kBAAkB,cAAc;AAE9C,QAAM,cAAc;AAAA,IAClB,QAAQ,IAAI,MAAM;AAAA,IAClB,YAAY,IAAI,MAAM;AAAA,EACxB;AAMA,QAAM,kBAAkB;AAAA,IACtB,cAAc;AAAA,IACd,WAAW;AAAA,IACX;AAAA,IACA,mBAAmB,oBAAI,IAAI;AAAA,IAC3B,cAAc,oBAAI,IAAI;AAAA,IACtB,8BAA8B,oBAAI,IAAI;AAAA,IACtC,UAAU,oBAAI,IAAI;AAAA,IAClB,yBAAyB,oBAAI,IAAI;AAAA,IACjC,kBAAkB,eAAe;AAAA,EACnC;AACA,IAAS,KAAK,mBAAmB,eAAe;AAChD,QAAM,iBAAiB;AAAA,IACrB,cAAc,gBAAgB;AAAA,IAC9B,sBAAsB;AAAA,IACtB,mBAAmB;AAAA,IACnB,yBAAyB,gBAAgB;AAAA,EAC3C;AACA,IAAS,KAAK,kBAAkB,cAAc;AAC9C,MAAI,aAAa,gBAAgB;AACjC,MAAI,YAAY,eAAe;AAC/B,SAAO;AACT;AAEA,IAA6B,mBAAmB;AAAA,EAC9C,gBAAgB,EAAC,MAAM,QAAQ,YAAW,GAAG;AAC3C,UAAM,EAAC,MAAM,KAAI,IAAI;AACrB,QAAI,SAAS,YAAY;AAEvB,YAAM,aAAaC,GAAY;AAC/B,iBAAW,KAAK,CAAC,EAAE,KAAK;AAAA;AAAA,QAEtBC,GAA0B,EAAC,QAAQ,MAAM,KAAI,CAAC;AAAA,QAC9CC,GAAsB,KAAK;AAAA,MAC7B;AACA,YAAM,aAAaF,GAAY;AAC/B,iBAAW,KAAK,CAAC,EAAE,KAAK;AAAA,QACtBG,GAAiB,UAAU,GAAG,UAAU,UAAU;AAAA,MACpD;AACA,kBAAY,cAAc,YAAY,MAAM,GAAG,EAAC,UAAU,KAAI,CAAC;AAAA,IACjE,OAAO;AACL,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,IACX,MAAM,EAAC,MAAM,QAAQ,IAAG,GAAG,EAAC,oBAAmB,GAAG;AAGhD,YAAM,iBAAiB,KAAK,KAAK,OAAO,QAAM,GAAG,SAAS,OAAO;AACjE,eAASC,KAAI,MAAM,GAAGA,KAAI,OAAO,KAAK,QAAQA,MAAK;AACjD,cAAM,oBAAoB,OAAO,KAAKA,EAAC;AACvC,oBAAY,qBAAqB,mBAAmB,CAAC,CAAC,EAAE,KAAK,GAAG,cAAc;AAAA,MAChF;AAAA,IACF;AAAA,IACA,KAAK,EAAC,KAAI,GAAG,EAAC,oBAAmB,GAAG;AAIlC,UAAI,oBAAoB,IAAI,IAAI,GAAG,QAAQ;AACzC,cAAM,QAAQ,iCAAiC,oBAAoB,IAAI,IAAI,CAAC;AAC5E,YAAI,OAAO;AACT,gBAAM,YAAYJ,GAAY,EAAC,MAAK,CAAC;AACrC,oBAAU,KAAK,CAAC,EAAE,OAAO,KAAK;AAC9B,eAAK,OAAO,CAAC,cAAc,WAAW,IAAI,CAAC;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU,EAAC,MAAM,QAAQ,KAAK,WAAW,MAAM,QAAQ,YAAW,GAAG,OAAO;AAC1E,UAAM,EAAC,MAAM,OAAM,IAAI;AACvB,UAAM,EAAC,qBAAqB,eAAe,iBAAiB,YAAW,IAAI;AAC3E,QAAI,SAAS,yBAAyB;AAEpC,YAAM,IAAI,MAAM,wCAAwC,SAAS,MAAM,GAAG,GAAG;AAAA,IAC/E,WAAW,SAAS,YAAY;AAC9B,kBAAY,cAAcC,GAA0B,EAAC,MAAM;AAAA,QACzDI,GAAkB,EAAC,MAAM,CAACC,GAAgB,YAAY,CAAC,EAAC,CAAC;AAAA,QACzDD,GAAkB,EAAC,MAAM,CAACE,GAAgB,EAAE,CAAC,EAAC,CAAC;AAAA;AAAA,MACjD,EAAC,CAAC,GAAG,MAAM,CAAC;AAAA,IACd,WAAW,SAAS,cAAc;AAEhC,kBAAY,cAAc,cAAc,qBAAqB,EAAC,0BAA0B,KAAI,CAAC,GAAG,MAAM,CAAC;AAAA,IACzG,WAAW,SAAS,gBAAgB;AAClC,UAAI,gBAAgB,IAAI,IAAI,GAAG;AAC7B,aAAK,MAAM,SAAS;AACpB,eAAO;AAAA,MACT,OAAO;AACL,cAAM,OAAO,UAAU,MAAM,CAAC;AAI9B,YAAI,QAAQ,sBAAsB,IAAI,GAAG;AACvC,sBAAY,cAAcN,GAA0B,EAAC,QAAQ,KAAI,CAAC,GAAG,MAAM,CAAC;AAAA,QAC9E,WAAW,eAAe;AACxB,gBAAM,IAAI,MAAM,8CAA8C;AAAA,QAChE,OAAO;AACL,sBAAY,UAAUK,GAAgB,cAAc,GAAG,MAAM,CAAC;AAC9D,gBAAM,WAAW;AAAA,QACnB;AAAA,MACF;AAAA,IACF,WAAW,SAAS,gBAAgB,SAAS,gBAAgB;AAAA,IAE7D,WAAW,SAAS,sBAAsB;AACxC,kBAAY,cAAc,cAAc,YAAY,GAAG,MAAM,CAAC;AAAA,IAChE,WAAW,SAAS,iBAAiB;AACnC,UAAI,CAAC,eAAe,CAAC,qBAAqB;AACxC,cAAMD,KAAI,UAAU,eAAe,OAAO,eAAe,SAAS,eAAe,OAAO,eAAe;AACvG,cAAMG,KAAI,UAAU,eAAe,OAAO,eAAe,SAAS,eAAe,OAAO,eAAe;AACvG,oBAAY,cAAc,cAAc,SAASA,KAAIH,EAAC,GAAG,MAAM,CAAC;AAAA,MAClE;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,8BAA8B,IAAI,GAAG;AAAA,IACvD;AAAA,EACF;AAAA,EAEA,cAAc,EAAC,KAAI,GAAG,EAAC,eAAc,GAAG;AACtC,QAAI,EAAC,IAAG,IAAI;AACZ,QAAI,OAAO,QAAQ,YAAY,CAAC,mBAAmB,GAAG,GAAG;AACvD,YAAM,uBAAuB,KAAK,cAAc;AAChD,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EAEA,eAAe,EAAC,KAAI,GAAG,EAAC,gBAAgB,iBAAgB,GAAG;AACzD,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,CAAC,mBAAmB,IAAI,GAAG;AACrC,aAAO,uBAAuB,MAAM,cAAc;AAClD,WAAK,OAAO;AAAA,IACd;AACA,qBAAiB,IAAI,KAAK,QAAQ,IAAI;AACtC,QAAI,MAAM;AACR,uBAAiB,IAAI,MAAM,IAAI;AAAA,IACjC;AAAA,EACF;AAAA,EAEA,oBAAoB,EAAC,MAAM,QAAQ,YAAW,GAAG;AAC/C,QAAI,OAAO,SAAS,gBAAgB;AAElC,YAAM,KAAKI,GAAqB,EAAC,MAAM,CAAC,IAAI,EAAC,CAAC;AAC9C,kBAAY,cAAc,IAAI,MAAM,GAAG,EAAC,UAAU,KAAI,CAAC;AAAA,IACzD;AAAA,EACF;AAAA,EAEA,aAAa,EAAC,MAAM,QAAQ,YAAW,GAAG,EAAC,UAAU,iBAAiB,cAAc,cAAc,YAAW,GAAG;AAC9G,UAAM,EAAC,MAAM,QAAQ,MAAK,IAAI;AAE9B,QAAI,iBAAiB,SAAS,WAAW,UAAU,UAAU;AAC3D,kBAAY,UAAUC,GAAmB,SAAS,EAAC,OAAM,CAAC,GAAG,MAAM,CAAC;AACpE;AAAA,IACF;AAEA,QAAI,iBAAiB,SAAS,WAAW,UAAU,UAAU;AAC3D,kBAAY,cAAc,UAAU,cAAc,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC;AACnF;AAAA,IACF;AAEA,QAAI,gBAAgB,SAAS,UAAU,UAAU,SAAS;AACxD,kBAAY,UAAUA,GAAmB,QAAQ,EAAC,OAAM,CAAC,GAAG,MAAM,CAAC;AACnE;AAAA,IACF;AACA,QAAI,SAAS,OAAO;AAClB,kBAAY,UAAUR,GAAsB,KAAK,GAAG,MAAM,CAAC;AAAA,IAC7D,WAAW,SAAS,SAAS;AAC3B,kBAAY,UAAUA,GAAsB,MAAM,EAAC,OAAM,CAAC,GAAG,MAAM,CAAC;AAAA,IACtE,WAAW,SAAS,OAAO;AAAA,IAE3B,WAAW,SAAS,gBAAgB;AAClC,UAAI,aAAa,UAAU;AACzB,cAAM,IAAI,MAAM,2CAA2C;AAAA,MAC7D;AAGA,YAAM,QAAQ;AACd,YAAM,QAAQ,cAAc,KAAK,YAAY,KAAK;AAClD,kBAAY,cAAc;AAAA;AAAA,QAExB,YAAY,kBAAkB,mBAAmB,KAAK;AAAA;AAAA,QAEtD,EAAC,4BAA4B,KAAI;AAAA,MACnC,GAAG,MAAM,CAAC;AAAA,IACZ,WAAW,SAAS,OAAO;AACzB,kBAAY,UAAUA,GAAsB,QAAQ,EAAC,OAAM,CAAC,GAAG,MAAM,CAAC;AAAA,IACxE,WAAW,SAAS,WAAW;AAC7B,kBAAY,cAAc,cAAc,SAAS,UAAU,oCAAoC,GAAG,MAAM,CAAC;AAAA,IAC3G,WAAW,SAAS,SAAS;AAC3B,UAAI,CAAC,oBAAoB,UAAU,WAAW,UAAU,UAAU;AAChE,YAAI,aAAa,UAAU;AACzB,gBAAM,IAAI,MAAM,gBAAgB,KAAK,qDAAqD;AAAA,QAC5F;AACA,YAAI,QAAQ;AAAA,UACV,OAAO;AAAA,UACP,OAAO;AAAA,QACT,EAAE,KAAK;AACP,YAAI,QAAQ;AAGV,kBAAQ,MAAM,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC;AAAA,QAC3E;AACA,oBAAY,cAAc,cAAc,IAAI,KAAK,GAAG,GAAG,MAAM,CAAC;AAAA,MAChE,OAAO;AACL,oBAAY,cAAc,UAAU,cAAc,cAAc,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC;AAAA,MAC/F;AAAA,IACF,WAAW,SAAS,YAAY;AAC9B,UAAI,CAAC,qBAAqB,IAAIS,GAAK,KAAK,CAAC,GAAG;AAG1C,aAAK,MAAM;AAAA,MACb;AAAA,IACF,WAAW,SAAS,SAAS;AAE3B,kBAAY,UAAUT,GAAsB,SAAS,EAAC,OAAM,CAAC,GAAG,MAAM,CAAC;AAAA,IACzE,WAAW,SAAS,QAAQ;AAC1B,kBAAY,cAAc,UAAU,cAAc,eAAe,GAAG,MAAM,GAAG,MAAM,CAAC;AAAA,IACtF,OAAO;AACL,YAAM,IAAI,MAAM,kCAAkC,IAAI,GAAG;AAAA,IAC3D;AAAA,EACF;AAAA,EAEA,UAAU,EAAC,MAAM,QAAQ,MAAM,QAAQ,aAAa,uBAAuB,sBAAqB,GAAG;AACjG,UAAM,EAAC,MAAM,MAAK,IAAI;AACtB,QAAI,SAAS,SAAS;AACpB,UAAI,CAAC,MAAM,UAAU,CAAC,MAAM,SAAS;AAEnC,eAAO;AAAA,MACT,OAAO;AACL,cAAM,YAAYF,GAAY,EAAC,MAAK,CAAC;AACrC,kBAAU,KAAK,CAAC,EAAE,OAAO,sBAAsB;AAC/C,oBAAY,cAAc,WAAW,MAAM,GAAG,EAAC,UAAU,KAAI,CAAC;AAAA,MAChE;AAAA,IACF,WAAW,SAAS,QAAQ;AAC1B,YAAM,WAAW,KAAK,KAAK,CAAC;AAE5B,YAAM,kBACJ,KAAK,KAAK,WAAW;AAAA,MAErBY,GAAa,UAAU,EAAC,MAAM,QAAO,CAAC,KACtC,SAAS,KAAK,CAAC,EAAE,KAAK,WAAW;AACnC,YAAM,WAAW,kBAAkB,SAAS,KAAK,CAAC,IAAI;AACtD,UAAI,OAAO,WAAW,YAAY,SAAS,KAAK,SAAS,GAAG;AAC1D,cAAM,IAAI,MAAM,wCAAwC;AAAA,MAC1D;AACA,YAAM,aAAaX,GAA0B,EAAC,QAAQ,KAAI,CAAC;AAC3D,iBAAW,KAAK,CAAC,EAAE,OAAO,sBAAsB;AAChD,kBAAY,cAAc,YAAY,MAAM,CAAC;AAAA,IAC/C,OAAO;AACL,YAAM,IAAI,MAAM,8BAA8B,IAAI,GAAG;AAAA,IACvD;AAAA,EACF;AAAA,EAEA,MAAM,EAAC,MAAM,OAAM,GAAG;AAEpB,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACxC;AACA,QAAI,KAAK,oBAAoB,QAAQ;AACnC,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AAEA;AAAA,MAAE;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACF,EAAE,QAAQ,CAAAY,OAAK,OAAO,KAAKA,EAAC,CAAC;AAC7B,WAAO,OAAO,MAAM;AAAA;AAAA,MAElB,QAAQ;AAAA;AAAA,MAER,YAAY;AAAA;AAAA;AAAA;AAAA,MAIZ,WAAW;AAAA;AAAA,MAEX,QAAQ,KAAK,UAAU;AAAA;AAAA;AAAA;AAAA,IAIzB,CAAC;AAED,WAAO,UAAU;AAAA,MACf,SAAS;AAAA;AAAA,QAEP,GAAG;AAAA;AAAA;AAAA,QAGH,GAAG;AAAA,MACL;AAAA,MACA,OAAO;AAAA;AAAA;AAAA;AAAA,QAIL,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,EAAC,KAAI,GAAG;AACZ,QAAI,CAAC,KAAK,OAAO;AACf;AAAA,IACF;AACA,UAAM,EAAC,QAAQ,QAAO,IAAI,KAAK;AAE/B,YAAQ,YAAY,OAAO,OAAO;AAClC,aAAS,YAAY,OAAO,QAAQ;AAEpC,YAAQ,UAAU,SAAS,UAAU,OAAO,OAAO;AACnD,YAAQ,cAAc,SAAS,cAAc,OAAO,OAAO;AAE3D,cAAU,CAAC,OAAO,KAAK,MAAM,EAAE,UAAU,OAAO,KAAK,MAAM;AAC3D,eAAW,CAAC,OAAO,KAAK,OAAO,EAAE,UAAU,OAAO,KAAK,MAAM;AAC7D,KAAC,KAAK,MAAM,UAAU,CAAC,KAAK,MAAM,WAAW,OAAO,KAAK;AAAA,EAC3D;AAAA,EAEA,oBAAoB,EAAC,KAAI,GAAG,OAAO;AACjC,UAAM,EAAC,KAAI,IAAI;AACf,QAAI,SAAS,cAAc;AACzB,YAAM,mBAAmB;AAAA,IAC3B;AAAA,EACF;AAAA,EAEA,aAAa,EAAC,MAAM,QAAQ,YAAW,GAAG;AACxC,UAAM,EAAC,KAAI,IAAI;AACf,QAAI,SAAS,QAAQ;AACnB,kBAAY,cAAcZ,GAA0B,EAAC,QAAQ,KAAI,CAAC,GAAG,MAAM,CAAC;AAAA,IAC9E,OAAO;AACL,YAAM,IAAI,MAAM,gCAAgC,KAAK,YAAY,CAAC,GAAG;AAAA,IACvE;AAAA,EACF;AAAA,EAEA,WAAW,EAAC,KAAI,GAAG;AACjB,QAAI,KAAK,KAAK,SAAS,cAAc;AAEnC,YAAM,QAAQD,GAAY;AAC1B,YAAM,KAAK,CAAC,EAAE,KAAK,KAAK,KAAK,IAAI;AACjC,WAAK,OAAO,cAAc,OAAO,IAAI;AAAA,IACvC;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,IACL,MAAM,EAAC,KAAI,GAAG,EAAC,gBAAe,GAAG;AAI/B,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB;AACtB,UAAI,qBAAqB;AACzB,iBAAW,OAAO,KAAK,MAAM;AAC3B,YAAI,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,CAAC,EAAE,SAAS,gBAAgB;AAMhE,cAAI,KAAK,IAAI;AAAA,QACf,OAAO;AACL,gBAAM,WAAW,YAAY,IAAI,IAAI;AACrC,cAAI,UAAU;AACZ,8BAAkB;AAClB,kBAAM,QAAQ,QAAQ,IACpB,UAAU,KAAK,GAAG,QAAQ,IAC1B,UAAU,KAAK,QAAQ;AAAA,UAC3B,OAAO;AACL,iCAAqB;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AACA,UAAI,mBAAmB,CAAC,oBAAoB;AAE1C,kBAAU,QAAQ,OAAK,gBAAgB,IAAI,CAAC,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,KAAKG,IAAG,EAAC,UAAU,kBAAkB,SAAQ,GAAG;AAC9C,UAAI,aAAa,YAAY,oBAAoB,UAAU;AACzD,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAC,KAAI,GAAG,EAAC,eAAc,GAAG;AACnC,QAAI,EAAC,IAAG,IAAI;AACZ,QAAI,OAAO,QAAQ,YAAY,CAAC,mBAAmB,GAAG,GAAG;AACvD,YAAM,uBAAuB,KAAK,cAAc;AAChD,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACF;AAEA,IAA6B,oBAAoB;AAAA,EAC/C,cAAc,EAAC,KAAI,GAAG,EAAC,8BAA8B,wBAAuB,GAAG;AAC7E,UAAM,EAAC,QAAQ,IAAG,IAAI;AACtB,QAAI,CAAC,QAAQ;AAGX,8BAAwB,IAAI,MAAM,CAAC,GAAG,6BAA6B,IAAI,GAAG,EAAE,IAAI,CAAC,EAAC,MAAAW,MAAI,MAAMA,KAAI,CAAC,CAAC;AAAA,IACpG;AAAA,EACF;AAAA,EAEA,gBAAgB;AAAA,IACd,MACE;AAAA,MAAE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GACA;AAAA,MAAE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GACA;AAEA,YAAM,SAAS,kBAAkB,IAAI,IAAI;AAGzC,UAAI,UAAU,SAAS,IAAI,KAAK,MAAM,GAAG;AAIvC,cAAMC,aAAY,UAAU,gBAAgB,KAAK,MAAM,GAAG,MAAM;AAChE,gCAAwB,IAAIA,YAAW,SAAS,IAAI,KAAK,MAAM,CAAC;AAChE,oBAAYA,UAAS;AACrB;AAAA,MACF;AACA,eAAS,IAAI,KAAK,QAAQ,IAAI;AAG9B,mCAA6B,IAAI,KAAK,QAAQ,CAAC,CAAC;AAChD,UAAI,KAAK,MAAM;AACb,oBAAY,8BAA8B,KAAK,MAAM,CAAC,CAAC;AAAA,MACzD;AACA,YAAM,iBAAiB,6BAA6B,IAAI,KAAK,QAAQ,KAAK,MAAM;AAChF,eAASX,KAAI,GAAGA,KAAI,eAAe,QAAQA,MAAK;AAO9C,cAAM,YAAY,eAAeA,EAAC;AAClC;AAAA;AAAA;AAAA,UAGG,WAAW,UAAU,QAAS,UAAU,WAAW,UAAU;AAAA;AAAA,UAG9D,SAAS,UAAU;AAAA,UACnB;AACA,yBAAe,OAAOA,IAAG,CAAC;AAC1B;AAAA,QACF;AAAA,MACF;AACA,mCAA6B,IAAI,KAAK,MAAM,EAAE,KAAK,EAAC,MAAM,OAAM,CAAC;AACjE,UAAI,KAAK,MAAM;AACb,qCAA6B,IAAI,KAAK,IAAI,EAAE,KAAK,EAAC,MAAM,OAAM,CAAC;AAAA,MACjE;AAQA,UAAI,KAAK,MAAM;AACb,cAAM,qBAAqB,YAAY,cAAc,KAAK,MAAM,oBAAI,IAAI,CAAC;AACzE,YAAI,2BAA2B;AAC/B,YAAI,QAAQ;AAEV,qCAA2B;AAAA,QAC7B,OAAO;AACL,qBAAW,aAAa,mBAAmB,OAAO,GAAG;AACnD,gBAAI,CAAC,UAAU,0BAA0B;AAEvC,yCAA2B;AAC3B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,qBAAa,IAAI,KAAK,IAAI,EAAE,IAAI,MAAM,EAAC,MAAM,yBAAwB,CAAC;AAAA,MACxE;AAAA,IACF;AAAA,IACA,KAAK,EAAC,KAAI,GAAG,EAAC,SAAQ,GAAG;AACvB,eAAS,OAAO,KAAK,MAAM;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,IACL,MAAM,EAAC,KAAI,GAAG,OAAO;AAEnB,YAAM,YAAY,MAAM;AACxB,UAAI,KAAK,OAAO;AACd,cAAM,eAAe,mBAAmB,MAAM,cAAc,KAAK,KAAK;AAAA,MACxE;AAAA,IACF;AAAA,IACA,KAAKD,IAAG,OAAO;AACb,YAAM,eAAe,MAAM;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,WAAW,EAAC,MAAM,QAAQ,YAAW,GAAG,OAAO;AAC7C,UAAM,EAAC,aAAa,IAAG,IAAI;AAK3B,QAAI,aAAa;AAEf,UAAI,SAAS;AACb,aAAQ,SAAS,OAAO,QAAS;AAC/B,YAAI,OAAO,SAAS,qBAAqB,OAAO,SAAS,OAAO,OAAO,WAAW,MAAM;AACtF;AAAA,QACF;AAAA,MACF;AAGA,YAAM,wBAAwB,IAAI,MAAM,MAAM;AAC9C;AAAA,IACF;AAEA,UAAM,kBAAkB,MAAM,iBAAiB,IAAI,GAAG;AAEtD,UAAM,oBAAoB,QAAQ;AAClC,UAAM,qBAAqB,oBACzB,gBAAgB,CAAC;AAAA;AAAA,MAEjB,oBAAoB,iBAAiB,MAAM,mBAAmB,IAAI;AAAA;AACpE,QAAI,cAAc;AAClB,QAAI,CAAC,mBAAmB;AAEtB,YAAM,sBAAsB,iCAAiC;AAAA,QAC3D;AAAA,QACA,OAAK,EAAE,SAAS,WAAW,CAAC,CAAC,EAAE;AAAA,MACjC,CAAC;AACD,YAAM,mBAAmB,sBACvB,mBAAmB,MAAM,aAAa,mBAAmB,IACzD,MAAM;AACR,UAAI,CAAC,cAAc,kBAAkB,MAAM,YAAY,GAAG;AACxD,sBAAcH,GAAY;AAAA,UACxB,OAAO,qBAAqB,gBAAgB;AAAA,QAC9C,CAAC;AACD,oBAAY,KAAK,CAAC,EAAE,KAAK,KAAK,kBAAkB;AAAA,MAClD;AAAA,IACF;AACA,gBAAY,cAAc,aAAa,MAAM,GAAG,EAAC,UAAU,CAAC,kBAAiB,CAAC;AAAA,EAChF;AACF;AAEA,IAA6B,mBAAmB;AAAA,EAC9C,cAAc,EAAC,MAAM,QAAQ,YAAW,GAAG,OAAO;AAChD,QAAI,KAAK,QAAQ;AACf,YAAM,uBAAuB,KAAK,IAAI,MAAM,sBAAsB,KAAK,GAAG;AAE1E;AAAA,IACF;AACA,UAAM,cAAc,MAAM,wBAAwB,IAAI,IAAI;AAC1D,UAAM,eAAe,YAAY,OAAO,YAAU,uBAAuB,QAAQ,IAAI,CAAC;AAKtF,QAAI,CAAC,aAAa,QAAQ;AAGxB,kBAAY,cAAcC,GAA0B,EAAC,QAAQ,KAAI,CAAC,GAAG,MAAM,CAAC;AAAA,IAC9E,WAAW,aAAa,SAAS,GAAG;AAElC,YAAM,QAAQD,GAAY;AAAA,QACxB,QAAQ;AAAA,QACR,MAAM,aAAa,QAAQ,EAAE,IAAI,YAAUK,GAAkB;AAAA,UAC3D,MAAM,CAACW,GAAoB,OAAO,MAAM,CAAC;AAAA,QAC3C,CAAC,CAAC;AAAA,MACJ,CAAC;AACD,kBAAY,cAAc,OAAO,MAAM,CAAC;AAAA,IAC1C,OAAO;AACL,WAAK,MAAM,aAAa,CAAC,EAAE;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,eAAe,EAAC,KAAI,GAAG,OAAO;AAE5B,SAAK,SAAS,EAAE,MAAM;AACtB,QAAI,KAAK,MAAM;AAGb,UAAI,MAAM,aAAa,IAAI,KAAK,IAAI,EAAE,IAAI,IAAI,EAAE,0BAA0B;AACxE,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,IACL,KAAK,EAAC,KAAI,GAAG,OAAO;AAUlB,YAAM,gBAAgB,KAAK,IAAI,MAAM,uBAAuB,MAAM,mBAAmB,CAAC;AACtF,eAASZ,KAAI,GAAGA,KAAI,eAAeA,MAAK;AACtC,cAAM,eAAea,GAAqB;AAC1C,aAAK,KAAK,GAAG,EAAE,EAAE,KAAK,KAAK,YAAY;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAC,KAAI,GAAG,OAAO;AACxB,QAAI,CAAC,KAAK,eAAe,KAAK,QAAQ,GAAG;AACvC;AAAA,IACF;AAMA,SAAK,MAAM,MAAM,wBAAwB,IAAI,IAAI,EAAE;AAAA,EACrD;AACF;AAMA,SAAS,oBAAoB,MAAM;AACjC,IAAS,MAAM;AAAA,IACb,IAAI,EAAC,MAAM,OAAM,GAAG;AAClB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAEA,SAAS,cAAcC,IAAGb,IAAG;AAC3B,SAAOa,GAAE,WAAWb,GAAE,UAAUa,GAAE,eAAeb,GAAE;AACrD;AAEA,SAAS,uBAAuB,SAAS,MAAM;AAG7C,MAAI,iBAAiB;AACrB,KAAG;AACD,QAAI,eAAe,SAAS,SAAS;AAEnC,aAAO;AAAA,IACT;AACA,QAAI,eAAe,SAAS,eAAe;AAEzC;AAAA,IACF;AACA,QAAI,mBAAmB,SAAS;AAE9B,aAAO;AAAA,IACT;AACA,UAAM,eAAe,QAAQ,eAAe,MAAM;AAClD,eAAW,OAAO,cAAc;AAC9B,UAAI,QAAQ,gBAAgB;AAE1B;AAAA,MACF;AACA,UAAI,QAAQ,WAAW,aAAa,KAAK,OAAO,GAAG;AACjD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAU,iBAAiB,eAAe;AAC1C,QAAM,IAAI,MAAM,iBAAiB;AACnC;AAKA,SAAS,oBAAoB,KAAK,WAAW,IAAI,KAAK;AACpD,QAAM,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AACzC,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,GAAG;AAC9C,QAAI,QAAQ,UAAU;AAEpB,YAAM,SAAS,MAAM,QAAQ,EAAE,IAAI,MAAM;AAAA,IAC3C,WAAW,SAAS,OAAO,UAAU,UAAU;AAC7C,YAAM,GAAG,IAAI,oBAAoB,OAAO,WAAW,OAAO,EAAE;AAAA,IAC9D,OAAO;AACL,UAAI,QAAQ,UAAU,UAAU,kBAAkB;AAEhD,kBAAU,IAAI,OAAO,UAAU,IAAI,GAAG,KAAK,GAAG;AAAA,MAChD;AACA,YAAM,GAAG,IAAI;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAK;AAC5B,QAAM,OAAOc,GAAiB,GAAG;AAIjC,OAAK,cAAc;AACnB,SAAO;AACT;AAEA,SAAS,cAAc,MAAM,UAAU;AACrC,QAAM,UAAU,CAAC;AACjB,SAAQ,OAAO,KAAK,QAAS;AAC3B,QAAI,CAAC,YAAY,SAAS,IAAI,GAAG;AAC/B,cAAQ,KAAK,IAAI;AAAA,IACnB;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,uBAAuB,MAAM,KAAK;AACzC,MAAI,IAAI,IAAI,IAAI,GAAG;AACjB,WAAO,IAAI,IAAI,IAAI;AAAA,EACrB;AAEA,QAAM,SAAS,IAAI,IAAI,IAAI,IAAI,KAAK,QAAQ,2CAA2C,GAAG,CAAC;AAC3F,MAAI,IAAI,MAAM,MAAM;AACpB,SAAO;AACT;AAEA,SAAS,iCAAiC,WAAW;AACnD,QAAM,YAAY,CAAC,UAAU,YAAY;AACzC,QAAM,gBAAgB,EAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,EAAC;AAC9C,YAAU,QAAQ,CAAC,EAAC,MAAK,MAAM;AAC7B,cAAU,QAAQ,UAAQ;AACxB,UAAI,MAAM,SAAS,IAAI,GAAG;AAExB,eAAO,cAAc,QAAQ,IAAI;AACjC,sBAAc,OAAO,IAAI,IAAI;AAAA,MAC/B;AACA,UAAI,MAAM,UAAU,IAAI,GAAG;AACzB,sBAAc,QAAQ,IAAI,IAAI;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,CAAC,OAAO,KAAK,cAAc,MAAM,EAAE,QAAQ;AAC7C,WAAO,cAAc;AAAA,EACvB;AACA,MAAI,CAAC,OAAO,KAAK,cAAc,OAAO,EAAE,QAAQ;AAC9C,WAAO,cAAc;AAAA,EACvB;AACA,MAAI,cAAc,UAAU,cAAc,SAAS;AACjD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,qBAAqB,EAAC,QAAQ,WAAU,GAAG;AAClD,QAAM,OAAO,CAAC;AACd,MAAI,UAAU,YAAY;AACxB,SAAK,SAAS,CAAC;AACf,eAAW,KAAK,OAAO,SAAS;AAChC,mBAAe,KAAK,OAAO,aAAa;AAAA,EAC1C;AACA,MAAI,CAAC,UAAU,CAAC,YAAY;AAC1B,SAAK,UAAU,CAAC;AAChB,KAAC,WAAW,KAAK,QAAQ,SAAS;AAClC,KAAC,eAAe,KAAK,QAAQ,aAAa;AAAA,EAC5C;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,MAAM;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAGA,QAAM,EAAC,KAAI,IAAI;AACf,SAAO,MAAM,QAAQ,IAAI,IAAI,OAAQ,OAAO,CAAC,IAAI,IAAI;AACvD;AAEA,SAAS,YAAY,KAAK;AACxB,QAAM,kBAAkB,IAAI,KAAK,QAC/B,GAAG,SAAS,kBACZ,kBAAkB,IAAI,EAAC,QAAQ,MAAK,CAAC,KACrC,CAAC,mBAAmB,EAAE,CACvB;AACD,MAAI,CAAC,iBAAiB;AACpB,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,SAAS,gBAAgB;AAC3C,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,SAAS,uBAAuB;AAClD,WAAO,gBAAgB,KAAK,CAAC,EAAE,KAAK,CAAC;AAAA,EACvC;AACA,MAAI,gBAAgB,SAAS,oBAAoB,gBAAgB,SAAS,SAAS;AACjF,UAAM,iBAAiB,CAAC;AAExB,eAAW,OAAO,gBAAgB,MAAM;AACtC,YAAM,WAAW,YAAY,IAAI,IAAI;AACrC,UAAI,CAAC,UAAU;AAEb,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,QAAQ,IACpB,eAAe,KAAK,GAAG,QAAQ,IAC/B,eAAe,KAAK,QAAQ;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,aAAa,MAAM,YAAY;AACtC,QAAM,OAAO,QAAQ,IAAI,KAAK,CAAC;AAC/B,aAAW,OAAO,MAAM;AACtB,QAAI,QAAQ,cAAc,aAAa,KAAK,UAAU,GAAG;AACvD,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAMA,SAAS,mBAAmB,EAAC,KAAI,GAAG;AAClC,SACE,SAAS,eACT,SAAS,eACT,SAAS;AAEb;AAMA,SAAS,sBAAsB,MAAM;AACnC,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO,MAAM,SAAS,KAAK,IAAI,KAC7B,KAAK,SAAS,gBACd,KAAK,OACL,MAAM,SAAS,KAAK,KAAK,IAAI;AAEjC;AAEA,SAAS,kBAAkB,MAAM,SAAS;AACxC,QAAM,OAAO;AAAA,IACX,QAAQ;AAAA,IACR,GAAG;AAAA,EACL;AACA,SACE,KAAK,SAAS,0BACb,KAAK,WAAW,QAAQ,KAAK,WAAW,KAAK,WAC9C,KAAK,KAAK,WAAW,KACrBP,GAAa,KAAK,KAAK,CAAC,GAAG;AAAA,IACzB,MAAM;AAAA,IACN,MAAM;AAAA,EACR,CAAC;AAEL;AAGA,SAAS,mBAAmB,MAAM;AAGhC,SAAO,wCAAwC,KAAK,IAAI;AAC1D;AAGA,SAAS,cAAc,SAAS,SAAS;AACvC,QAAM,MAAMQ,GAAM,SAAS;AAAA,IACzB,GAAG;AAAA;AAAA;AAAA,IAGH,oBAAoB;AAAA,EACtB,CAAC;AACD,QAAM,OAAO,IAAI;AACjB,MAAI,KAAK,SAAS,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,GAAG;AAC9C,WAAOpB,GAAY,EAAC,MAAM,KAAI,CAAC;AAAA,EACjC;AACA,SAAO,KAAK,CAAC,EAAE,KAAK,CAAC;AACvB;AAEA,SAAS,UAAU,MAAM,QAAQ;AAC/B,OAAK,SAAS;AACd,SAAO;AACT;AAEA,SAAS,UAAU,MAAM,QAAQ;AAC/B,OAAK,SAAS;AACd,SAAO;AACT;AAEA,SAAS,cAAc,MAAM,QAAQ;AACnC,sBAAoB,IAAI;AACxB,OAAK,SAAS;AACd,SAAO;AACT;;;ACh8BA,SAAS,SAAS,KAAK,SAAS;AAC9B,QAAM,OAAO,WAAW,OAAO;AAC/B,QAAM,kBAAkB,YAAY,KAAK,QAAQ,QAAQ;AACzD,QAAM,kBAAkB,YAAY,KAAK,QAAQ,QAAQ;AACzD,QAAM,iBAAiB,KAAK,MAAM;AAClC,MAAI,CAAC,OAAO,UAAU,cAAc,KAAK,iBAAiB,KAAK,iBAAiB,IAAI;AAClF,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACpD;AAOA,MAAI,yBAAyB;AAC7B,MAAI,uBAAuB;AAC3B,MAAI,CAAC,iBAAiB;AACpB,UAAM,SAAS,CAAC,IAAI,MAAM,UAAU;AACpC,MAAS,KAAK,qBAAqB;AAAA,MACjC,gBAAgB,MAAM,OAAO,GAAG,EAAE;AAAA,MAClC,UAAU;AAAC,eAAO,IAAI;AAAA,MAAC;AAAA,MACvB,SAAS,OAAO;AAAC,eAAO,KAAK,KAAK;AAAA,MAAC;AAAA,MACnC,kBAAkB;AAChB,YAAI,OAAO,GAAG,EAAE,GAAG;AACjB,mCAAyB;AAAA,QAC3B,OAAO;AACL,iCAAuB;AAAA,QACzB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,qBAAqB;AAAA,IACzB,QAAQ,IAAI,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,IAKlB,YAAY,CAAC,GAAG,IAAI,MAAM,cAAc,2BAA2B,CAAC;AAAA,EACtE;AACA,MAAwB,WAAW;AACnC,QAAM,QAAQ;AAAA,IACZ,UAAU,KAAK;AAAA,IACf;AAAA,IACA,YAAY,oBAAI,IAAI;AAAA,IACpB,cAAc;AAAA,MACZ,QAAQ,IAAI,MAAM;AAAA,MAClB,YAAY,IAAI,MAAM;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA,WAAW,IAAI;AAAA,IACf;AAAA,IACA,sBAAsB,CAAC,EAAE,CAAC,mBAAmB,0BAA0B;AAAA,IACvE,aAAa;AAAA,IACb,UAAU;AAAA,IACV,SAAS,KAAK;AAAA,EAChB;AACA,WAAS,IAAwB,MAAM;AACrC,UAAM,WAAW;AACjB,eAAW;AACX,UAAM,KAAK,eAAe,UAAU,KAAK,IAAI,GAAG,yBAAyB,KAAK,IAAI,GAAG;AACrF,WAAO,GAAG,MAAM,OAAO,GAAG;AAAA,EAC5B;AAEA,QAAM,SAAS;AAAA,IACb,SAAS,IAAI,KAAK,IAAI,GAAG,EAAE,KAAK,GAAG;AAAA;AAAA,IAEnC,OAAO,IAAI,IAAI,KAAK;AAAA,IACpB,SAAS,EAAC,GAAG,IAAI,QAAO;AAAA,EAC1B;AACA,MAAI,CAAC,iBAAiB;AAEpB,WAAO,OAAO,QAAQ,MAAM;AAC5B,WAAO,QAAQ,QAAQ,IAAI;AAC3B,WAAO,QAAQ,oBAAoB;AAAA,EACrC;AACA,SAAO,oBAAoB,oBAAI,IAAI;AACnC,SAAO,kBAAkB,CAAC;AAC1B,QAAM,WAAW,QAAQ,CAAC,OAAO,QAAQ;AACvC,QAAI,MAAM,QAAQ;AAChB,aAAO,gBAAgB,KAAK,GAAG;AAAA,IACjC;AACA,QAAI,MAAM,YAAY;AACpB,kBAAY,OAAO,mBAAmB,MAAM,YAAY,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,IACtE;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,IAA6B,sBAAsB;AAAA,EACjD,KAAK;AAAA,IACH,MAAM,EAAC,KAAI,GAAG,OAAO;AACnB,UAAI,WAAW,IAAI,GAAG;AACpB,cAAM,cAAc,MAAM,eAAe;AACzC,cAAM;AAAA,UACJ,KAAK,QACH,mBAAmB,EAAC,YAAY,YAAW,GAAG,KAAK,KAAK,EAAE,aAC1D;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,IACA,KAAK,EAAC,KAAI,GAAG,OAAO;AAClB,UAAI,WAAW,IAAI,GAAG;AACpB,cAAM,QAAQ;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAcqB,IAAG,OAAO;AAItB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,UAAU,EAAC,KAAI,GAAG,OAAO;AACvB,QAAI,YAAY,GAAG,KAAK,KAAK,CAAC,GAAG;AAC/B,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,oBAAoB,EAAC,MAAM,KAAI,GAAG,OAAO;AACvC,SAAK;AACL,QAAI,8BAA8B,MAAM,EAAC,WAAW,KAAI,CAAC,EAAE,QAAQ;AACjE,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,aAAa,EAAC,KAAI,GAAG,OAAO;AAC1B,QACE,KAAK,SAAS,cACd,kCAAkC,IAAI,KAAK,KAAK,GAChD;AACA,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AACF;AAGA,IAAM,YAAY;AAAA;AAAA;AAAA;AAAA,EAIhB,YAAY,EAAC,KAAI,GAAGA,IAAG,KAAK;AAC1B,WAAO,KAAK,IAAI,GAAG,EAAE,KAAK,EAAE;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,EAAC,MAAM,OAAM,GAAG;AAGxB,QAAI,SAAS,cAAc;AACzB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,gBAAgB;AAC3B,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,iBAAiB;AAC5B,aAAO,SAAS,QAAQ;AAAA,IAC1B;AAGA,UAAM,IAAI,MAAM,8BAA8B,IAAI,GAAG;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,EAAC,IAAG,GAAG,OAAO;AAC1B,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AACA,QACE,CAAC,MAAM,eACP,MAAM,aAAa,YACnB,MAAM,aAAa,cACnB,CAAC,MAAM,WAAW,IAAI,GAAG,EAAE,YAC3B;AACA,YAAM,IAAI,MAAM,uGAAuG;AAAA,IACzH;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM,OAAO,KAAK;AAC/B,UAAM,EAAC,MAAM,MAAM,OAAM,IAAI;AAC7B,UAAM,OAAO,EAAC,YAAY,MAAM,aAAa,WAAU;AAEvD,UAAM,SAAS,MAAM,UAAU,IAAI,IAAI;AACvC,QAAI,QAAQ;AAEV,WAAK,SAAS;AAId,UAAI,SAAS,OAAO,QAAQ;AAC1B,aAAK,aAAa,OAAO;AAAA,MAC3B;AAAA,IACF;AACA,UAAM,WAAW,IAAI,QAAQ,IAAI;AACjC,WAAO,IAAI,OAAO,KAAK,IAAI,MAAM,EAAE,GAAG,KAAK,IAAI,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,EAAC,MAAK,GAAG,OAAO;AACxB,UAAM,OAAO,GAAG,KAAK;AACrB,UAAM,UAAU,cAAc,OAAO;AAAA,MACnC,UAAU,MAAM,SAAS,SAAS;AAAA,MAClC,aAAa,MAAM;AAAA,MACnB,UAAU,MAAM;AAAA,IAClB,CAAC;AACD,QAAI,YAAY,MAAM;AACpB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,wBAAwB,MAAM,aAAa,cAAc,YAAY,IAAI,GAAG;AACpF,YAAM,QAAQ,wBAAwB,IAAI;AAC1C,aAAO,MAAM,cACX,MAAM,KAAK,EAAE,IACZ,MAAM,SAAS,IAAI,IAAI,MAAM,KAAK,EAAE,CAAC,MAAM,MAAM,CAAC;AAAA,IACvD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM,OAAO,KAAK;AAC/B,UAAM,EAAC,MAAM,QAAQ,OAAM,IAAI;AAC/B,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,SAAS,kBAAkB,CAAC,MAAM,UAAU;AAC9C,YAAM,IAAI,MAAM,sDAAsD;AAAA,IACxE;AAGA,QAAI,SAAS,gCAAgC,MAAM,YAAY,KAAK,KAAK,eAAe,GAAG;AACzF,aAAO,CAACC,GAAgB,EAAE,GAAG,GAAG,KAAK,OAAO,SAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAAA,IAC3E;AACA,UAAM,WAAW,MAAM,IAAI,SAAS,MAAM,EAAE,GAC1C,KAAK,IAAI,GAAG,EAAE,KAAK,SAAS,iBAAiB,OAAO,EAAE,CACxD;AACA,QAAI,CAAC,MAAM,aAAa;AAMtB;AAAA;AAAA,SAEG,CAAC,MAAM,YAAY,SAAS,kCAC7B,CAAC;AAAA,QACD;AACA,cAAM,sBAAsB,KAAK;AAAA,UAC/B,SAAO,IAAI,SAAS,oBAAoB,IAAI,SAAS,WAAW,IAAI;AAAA,QACtE;AACA,YAAI,oBAAoB,QAAQ;AAC9B,gBAAM,QAAQC,GAAY;AAC1B,gBAAM,gBAAgB,MAAM,KAAK,CAAC;AAClC,gBAAM,SAAS;AACf,wBAAc,SAAS;AACvB,iBAAO,KAAK,OAAO,SAAO,CAAC,oBAAoB,SAAS,GAAG,CAAC;AAC5D,eAAK,OAAO;AACZ,cAAI,KAAK,QAAQ;AACf,iBAAK,SAAS;AACd,0BAAc,KAAK,KAAK,IAAI;AAAA,UAC9B,OAAO;AAIL,kBAAM,KAAK,IAAI;AAAA,UACjB;AACA,8BAAoB,QAAQ,QAAM;AAChC,kBAAM,SAASC,GAAkB,EAAC,MAAM,CAAC,EAAE,EAAC,CAAC;AAC7C,eAAG,SAAS;AACZ,mBAAO,SAAS;AAChB,kBAAM,KAAK,KAAK,MAAM;AAAA,UACxB,CAAC;AACD,iBAAO,IAAI,KAAK;AAAA,QAClB;AAAA,MACF;AAEA,YAAM,cAAc;AACpB,YAAM,SAAS,SAAS;AACxB,YAAM,cAAc;AACpB,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,KAAK,CAAC;AACtB;AAAA;AAAA,MAEE,SAAS,WACT,CAAC,UACD;AAAA,QAGK,CAAC,MAAM,YAAY,CAAC,MAAM,YAC3B,OAAO,SAAS,WAChB,EAAE,SAAS,gCAAgC,MAAM,aAEjD,CAAC,MAAM,WACP,OAAO,SAAS;AAAA,MAEhB,KAAK,WAAW,KAChB,QAAQ,SAAS;AAAA,MAGrB;AAEA,aAAO,KAAK,IAAI,GAAG,EAAE,KAAK,EAAE;AAAA,IAC9B;AACA,QAAI,CAAC,MAAM,YAAY,OAAO,SAAS,kBAAkB;AACvD,YAAM,IAAI,MAAM,0DAA0D;AAAA,IAC5E;AACA,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,MAAM,OAAO;AAC/B,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,UAAU;AAAA,MACd,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU,MAAM;AAAA,IAClB;AACA,UAAM,SAAS,cAAc,KAAK,OAAO;AACzC,UAAM,SAAS,cAAc,KAAK,OAAO;AACzC,UAAM,aAAa,oBAAI,IAAI;AAC3B,QAAI,MAAM,wBAAwB,MAAM,aAAa,YAAY;AAE/D,YAAM,oBAAoB,8BAA8B,IAAI;AAC5D,YAAM,SAAS,4BAA4B,iBAAiB;AAC5D,aAAO,QAAQ,WAAS;AACtB,mBAAW;AAAA,UACT,MAAM,QAAQ,KAAK,IACjB,GAAG,cAAc,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,cAAc,MAAM,CAAC,GAAG,OAAO,CAAC,KACvE,cAAc,OAAO,OAAO;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,CAAC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,EAAC,MAAM,QAAQ,OAAO,IAAG,GAAG,OAAO;AAC9C,QAAI,SAAS,OAAO;AAClB,aAAO,MAAM,aAAa,SACtB,MAAM,mBAAmB,UAAU,MAAM,cAAe,MAAM;AAAA;AAAA,QAEhE;AAAA;AAAA,IACJ;AACA,QAAI,SAAS,SAAS;AACpB,aAAO,SAAS,QAAQ;AAAA,IAC1B;AACA,QAAI,SAAS,YAAY;AACvB,UACE,MAAM,wBACN,MAAM,aAAa,cACnB,kCAAkC,IAAI,KAAK,GAC3C;AAKA,cAAM,IAAI,MAAM,qBAAqB,KAAK,iEAAiE;AAAA,MAC7G;AACA,aAAO,GAAG,SAAS,QAAQ,KAAK,IAAI,MAAM,GAAG,GAAG,MAAM,EAAE,GAAG,KAAK;AAAA,IAClE;AACA,QAAI,SAAS,QAAQ;AACnB,aAAO,SAAS,QAAQ;AAAA,IAC1B;AAGA,UAAM,IAAI,MAAM,kCAAkC,IAAI,GAAG;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAM,OAAO;AACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAMG,MAAM,mBAAmB,aAAa,MAAM,OAC5C,KAAK,SAAS,MAAM,OACpB,KAAK,SAAS,MAAM;AAAA;AAAA,EAIzB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,EAAC,QAAAC,SAAQ,MAAM,OAAO,OAAM,GAAG,OAAO,KAAK;AAC/C,UAAM,eAAe,MAAM;AAC3B,QAAI,OAAO;AACT,YAAM,eAAe,mBAAmB,cAAc,KAAK;AAAA,IAC7D;AACA,UAAM,WAAW,KAAK,IAAI,GAAG,EAAE,KAAK,GAAG;AACvC,UAAM,SACJ,CAAC,MAAM,WACP,KAAK,WAAW;AAAA,IAChB,OAAO,SAAS,gBAChB,CAACA,YACA,CAAC,MAAM,eAAe,CAAC,SACrB,WAAW,KAAK,eAAeA,SAAQ,OAAO,MAAM,WAAW,CAAC,GAAG,QAAQ;AAChF,UAAM,eAAe;AACrB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,EAAC,MAAM,MAAM,OAAM,GAAGJ,IAAG,KAAK;AAChD,UAAM,SAAS,GAAG,SAAS,cAAc,KAAK,GAAG,GAAG,SAAS,MAAM,GAAG;AACtE,WAAO,KAAK,MAAM,GAAG,KAAK,IAAI,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAMA,IAAG,KAAK;AACvB,WAAO,IAAI,KAAK,IAAI,IAAI,iBAAiB,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,EAAC,aAAa,IAAG,GAAG,OAAO;AACpC,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC1E;AACA,UAAM,QAAQ,MAAM;AAEpB,WAAO,QAAQ,IAAI,OAAO,KAAK,MAAM,OAAO,GAAG,MAAM,KAAK;AAAA,EAC5D;AACF;AAMA,IAAM,kBAAkB,oBAAI,IAAI;AAAA,EAC9B;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AACpE,CAAC;AAED,IAAM,uBAAuB,oBAAI,IAAI;AAAA,EACnC;AAAA,EAAK;AAAA,EAAM;AAAA,EAAK;AAAA;AAAA;AAAA,EAGhB;AACF,CAAC;AAED,IAAM,4BAA4B,oBAAI,IAAI;AAAA,EACxC;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA;AAAA,EAEnD;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AACvF,CAAC;AAED,IAAM,oBAAoB,oBAAI,IAAI;AAAA,EAChC,CAAE,GAAG,KAAK;AAAA;AAAA,EACV,CAAC,IAAI,KAAK;AAAA;AAAA,EACV,CAAC,IAAI,KAAK;AAAA;AAAA,EACV,CAAC,IAAI,KAAK;AAAA;AAAA,EACV,CAAC,IAAI,KAAK;AAAA;AAAA,EACV,CAAC,MAAQ,SAAS;AAAA;AAAA,EAClB,CAAC,MAAQ,SAAS;AAAA;AAAA,EAClB,CAAC,OAAQ,SAAS;AAAA;AACpB,CAAC;AAED,IAAM,UAAU;AAChB,SAAS,YAAY,MAAM;AACzB,SAAO,QAAQ,KAAK,IAAI;AAC1B;AAMA,SAAS,8BAA8B,MAAM,SAAS;AACpD,QAAM,YAAY,CAAC,CAAC,SAAS;AAC7B,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,QAAQ,CAAC;AAIf,MAAK,MAAM,OAAO,QAAQ,SAAU,OAAO,WAAc,QAAQ,SAAW,OAAO,QAAU;AAC3F,WAAO;AAAA,EACT;AACA,WAASK,KAAI,KAAKA,MAAK,KAAKA,MAAK;AAC/B,UAAM,OAAO,GAAGA,EAAC;AACjB,QAAI,CAAC,YAAY,IAAI,GAAG;AACtB;AAAA,IACF;AACA,UAAM,oBAAoB,wBAAwB,IAAI,EAAE,OAAO,gBAAc;AAC3E,YAAM,MAAM,WAAW,YAAY,CAAC;AACpC,aAAO,MAAM,OAAO,MAAM;AAAA,IAC5B,CAAC;AACD,QAAI,kBAAkB,QAAQ;AAC5B,YAAM,KAAK,GAAG,iBAAiB;AAC/B,UAAI,WAAW;AACb;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,cAAc,WAAW,EAAC,UAAU,aAAa,SAAQ,GAAG;AACnE,MAAI,kBAAkB,IAAI,SAAS,GAAG;AACpC,WAAO,kBAAkB,IAAI,SAAS;AAAA,EACxC;AACA;AAAA;AAAA,IAEE,YAAY,MAAO,YAAY,OAAO,YAAY;AAAA,IAElD,YAAY;AAAA,IAEX,YAAY,gBAAgB,SAAS;AAAA,IACtC;AAGA,WAAO,YAAY,MACjB,OAAO,UAAU,SAAS,EAAE,EAAE,YAAY,CAAC,MAC3C,MAAM,UAAU,SAAS,EAAE,EAAE,YAAY,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,EAC/D;AACA,QAAM,cAAc,cACjB,WAAW,4BAA4B,uBACxC;AACF,QAAM,OAAO,GAAG,SAAS;AACzB,UAAQ,YAAY,IAAI,IAAI,IAAI,OAAO,MAAM;AAC/C;AAEA,SAAS,4BAA4B,OAAO;AAC1C,QAAM,aAAa,MAAM,IAAI,UAAQ,KAAK,YAAY,CAAC,CAAC,EAAE,KAAK,CAACC,IAAGH,OAAMG,KAAIH,EAAC;AAC9E,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AACZ,WAASE,KAAI,GAAGA,KAAI,WAAW,QAAQA,MAAK;AAC1C,QAAI,WAAWA,KAAI,CAAC,MAAM,WAAWA,EAAC,IAAI,GAAG;AAC3C,gBAAU,WAAWA,EAAC;AAAA,IACxB,WAAW,UAAU,MAAM;AACzB,aAAO,KAAK,WAAWA,EAAC,CAAC;AAAA,IAC3B,OAAO;AACL,aAAO,KAAK,CAAC,OAAO,WAAWA,EAAC,CAAC,CAAC;AAClC,cAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,eAAeD,SAAQ,UAAU,aAAa;AACrD,MAAIA,SAAQ;AACV,WAAO;AAAA,EACT;AACA,MAAI,OAAO;AACX,MAAI,YAAY,aAAa;AAC3B,UAAM,EAAC,QAAQ,QAAO,IAAI;AAC1B,YACG,QAAQ,aAAa,MAAM,OAC3B,QAAQ,SAAS,MAAM,OACvB,UAAU,MAAM,OAChB,SAAS,aAAa,MAAM,OAC5B,SAAS,SAAS,MAAM;AAAA,EAC7B;AACA,SAAO,GAAG,IAAI;AAChB;AAMA,SAAS,iBAAiB,EAAC,MAAM,KAAK,IAAG,GAAG;AAC1C,MAAI;AACJ,MAAI,CAAC,OAAO,QAAQ,GAAG;AACrB,WAAO;AAAA,EACT,WAAW,CAAC,OAAO,QAAQ,UAAU;AACnC,WAAO;AAAA,EACT,WAAW,QAAQ,KAAK,QAAQ,UAAU;AACxC,WAAO;AAAA,EACT,WAAW,QAAQ,KAAK;AACtB,WAAO,IAAI,GAAG;AAAA,EAChB,OAAO;AACL,WAAO,IAAI,GAAG,IAAI,QAAQ,WAAW,KAAK,GAAG;AAAA,EAC/C;AACA,SAAO,OAAO;AAAA,IACZ,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,YAAY;AAAA,EACd,EAAE,IAAI;AACR;AAMA,SAAS,WAAW,EAAC,KAAI,GAAG;AAC1B,SAAO,SAAS,oBACd,SAAS,WACT,SAAS;AACb;AAEA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,QAAQ,MAAM,QAAQ;AAC/B;AAMA,SAAS,gBAAgB,EAAC,MAAM,MAAK,GAAG;AACtC,SAAO,SAAS,eAAe,UAAU;AAC3C;;;AC5nBA,IAAM,iBAAN,MAAM,wBAAuB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlC,cAAc,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAKtB,YAAY;AAAA;AAAA;AAAA;AAAA,EAKZ;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA,EAKX,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,aAAa,CAAC;AAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,YAAY,SAAS,OAAO,SAAS;AACnC,UAAM,cAAc,CAAC,CAAC,SAAS;AAC/B,QAAI,mBAAmB,QAAQ;AAG7B,UAAI,SAAS;AACX,cAAM,IAAI,MAAM,8CAA8C;AAAA,MAChE;AACA,YAAMG,MAAK;AACX,YAAMA,KAAI,KAAK;AACf,WAAK,WAAWA,IAAG;AACnB,UAAIA,eAAc,iBAAgB;AAChC,aAAK,cAAcA,IAAG;AACtB,aAAK,WAAWA,IAAG;AACnB,aAAK,YAAYA,IAAG;AACpB,aAAK,aAAaA,IAAG;AAAA,MACvB;AAAA,IACF,OAAO;AACL,YAAM,OAAO;AAAA,QACX,gBAAgB,CAAC;AAAA,QACjB,UAAU;AAAA,QACV,WAAW,CAAC;AAAA,QACZ,GAAG;AAAA,MACL;AACA,YAAM,cAAc,KAAK,SAAS,KAAK;AACvC,WAAK,WAAW;AAChB,WAAK,cAAc,iBAAiB,KAAK,gBAAgB,KAAK,SAAS;AACvE,WAAK,YAAY,KAAK;AAEtB,WAAK,aAAa,WAAW,CAAC;AAAA,IAChC;AACA,QAAI,CAAC,aAAa;AAChB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,KAAK;AAER,QAAI,CAAC,KAAK,WAAW;AACnB,YAAM,EAAC,aAAa,GAAG,KAAI,IAAI,KAAK;AACpC,WAAK,YAAY,IAAI,gBAAe,KAAK,UAAU,KAAK,OAAO,IAAI;AAAA,IACrE;AAEA,UAAM,eAAe,KAAK,UAAU,KAAK;AACzC,UAAM,MAAM,KAAK;AAEjB,QAAI,KAAK,cAAc,iBAAiB,gBAAgB,KAAK;AAE3D,WAAK,YAAY;AAOjB,YAAM,QAAQ,KAAK,UAAU,IAAI,MAAM,GAAG,CAAC;AAC3C,UAAI,OAAO;AACT,oCAA4B,OAAO,KAAK,KAAK,KAAK,UAAU;AAC5D,aAAK,aAAa;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,UAAU,GAAG;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,KAAK;AAEb,SAAK,UAAU,YAAY,KAAK;AAChC,UAAM,QAAQ,MAAM,KAAK,KAAK,KAAK,WAAW,GAAG;AACjD,SAAK,YAAY,KAAK,UAAU;AAEhC,QAAI,CAAC,SAAS,CAAC,KAAK,YAAY,MAAM;AACpC,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,CAAC,GAAG,KAAK;AAE3B,UAAM,SAAS;AACf,QAAI;AACJ,QAAI,KAAK,YAAY;AACnB,oBAAc,CAAC,GAAG,MAAM,OAAO;AAC/B,YAAM,QAAQ,SAAS;AAAA,IACzB;AACA,UAAM,aAAa,CAAC,CAAC;AACrB,aAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,YAAM,EAAC,QAAQ,WAAU,IAAI,KAAK,YAAY,IAAIA,EAAC,KAAK,CAAC;AACzD,UAAI,QAAQ;AACV,mBAAW,KAAK,IAAI;AAAA,MACtB,OAAO;AACL,mBAAW,KAAK,MAAM,MAAM;AAC5B,cAAM,KAAK,UAAUA,EAAC,CAAC;AACvB,YAAI,KAAK,YAAY;AACnB,gBAAM,QAAQ,KAAK,YAAYA,EAAC,CAAC;AAAA,QACnC;AAAA,MACF;AAGA,UAAI,cAAc,UAAUA,EAAC,MAAM,QAAW;AAC5C,cAAM,KAAK,WAAW,UAAU;AAChC,YAAI,CAAC,IAAI;AACP,gBAAM,IAAI,MAAM,gCAAgC,EAAE,GAAG;AAAA,QACvD;AACA,cAAM,EAAE,IAAI,UAAUA,EAAC;AACvB,YAAI,KAAK,YAAY;AACnB,gBAAM,QAAQ,EAAE,IAAI,YAAYA,EAAC;AAAA,QACnC;AACA,YAAI,MAAM,QAAQ;AAChB,cAAI,CAAC,KAAK,UAAU;AAElB,iBAAK,WAAW,cAAc,KAAK,MAAM;AAAA,UAC3C;AACA,gBAAM,OAAO,KAAK,SAAS,IAAI,UAAU;AACzC,cAAI,MAAM;AACR,kBAAM,OAAO,IAAI,IAAI,UAAUA,EAAC;AAChC,gBAAI,KAAK,YAAY;AACnB,oBAAM,QAAQ,OAAO,IAAI,IAAI,YAAYA,EAAC;AAAA,YAC5C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,4BAA4B,OAAO,QAAQ,OAAO,YAAY;AACrE,QAAM,SAAS;AACf,QAAM,QAAQ;AACd,MAAI,YAAY;AACd,UAAM,UAAU,MAAM;AACtB,aAASA,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACvC,YAAM,MAAM,QAAQA,EAAC;AACrB,UAAI,KAAK;AAIP,gBAAQA,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,MAAM;AAAA,MAChD;AAAA,IACF;AACA,UAAM,eAAe,QAAQ;AAC7B,QAAI,cAAc;AAChB,aAAO,KAAK,YAAY,EAAE,QAAQ,SAAO;AACvC,cAAM,MAAM,aAAa,GAAG;AAC5B,YAAI,KAAK;AACP,uBAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,MAAM;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAYA,SAAS,iBAAiB,gBAAgB,WAAW;AACnD,QAAM,aAAa,oBAAI,IAAI;AAC3B,aAAW,OAAO,gBAAgB;AAChC,eAAW,IAAI,KAAK;AAAA,MAClB,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,aAAW,CAAC,IAAI,IAAI,KAAK,WAAW;AAClC,eAAW,OAAO,MAAM;AACtB,kBAAY,YAAY,KAAK,CAAC,CAAC,EAAE,aAAa;AAAA,IAChD;AAAA,EACF;AACA,SAAO;AACT;AAMA,SAAS,cAAc,SAAS;AAC9B,QAAMD,MAAK;AACX,QAAM,MAAM,oBAAI,IAAI;AACpB,MAAI,qBAAqB;AACzB,MAAI,cAAc;AAClB,MAAI;AACJ,SAAQ,QAAQA,IAAG,KAAK,OAAO,GAAI;AACjC,UAAM,EAAC,GAAGE,IAAG,QAAQ,EAAC,SAAS,KAAI,EAAC,IAAI;AAIxC,QAAIA,OAAM,KAAK;AACb;AAAA,IACF,WAAW,CAAC,oBAAoB;AAC9B,UAAI,SAAS;AACX;AACA,YAAI,MAAM;AACR,cAAI,IAAI,aAAa,IAAI;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,WAAWA,OAAM,KAAK;AACpB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AC1RA,IAAM,oBAAoB,OAAO;AAOjC,SAAS,mBAAmB,KAAK,WAAW;AAC1C,WAASC,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACnC,QAAI,IAAIA,EAAC,KAAK,WAAW;AACvB,UAAIA,EAAC;AAAA,IACP;AAAA,EACF;AACF;AASA,SAAS,UAAU,KAAK,KAAK,UAAU,UAAU;AAC/C,SAAO,IAAI,MAAM,GAAG,GAAG,IAAI,WAAW,IAAI,MAAM,MAAM,SAAS,MAAM;AACvE;;;ACzBO,IAAM,UAAU,OAAO,OAAO;AAAA,EACnC,SAAS;AAAA,EACT,YAAY;AACd,CAAC;AAyBM,SAAS,iBAAiB,YAAY,QAAQ,aAAa,SAAS;AACzE,QAAMC,MAAK,IAAI,OAAO,OAAO,MAAM,MAAM,yBAAyB,KAAK;AACvE,QAAM,UAAU,CAAC,KAAK;AACtB,MAAI,qBAAqB;AACzB,MAAI,SAAS;AACb,aAAW,SAAS,WAAW,SAASA,GAAE,GAAG;AAC3C,UAAM,EAAC,GAAGC,IAAG,QAAQ,EAAC,MAAK,EAAC,IAAI;AAChC,QAAI,CAAC,UAAU,CAAC,WAAY,YAAY,QAAQ,YAAa,CAAC,qBAAqB;AACjF,UAAI,uBAAuB,UAAU;AACnC,kBAAU,YAAY,OAAO;AAAA,UAC3B,SAAS,qBAAqB,QAAQ,aAAa,QAAQ;AAAA,UAC3D,SAAS,QAAQ,QAAQ,SAAS,CAAC;AAAA,QACrC,CAAC;AAAA,MACH,OAAO;AACL,kBAAU;AAAA,MACZ;AACA;AAAA,IACF;AACA,QAAIA,GAAE,CAAC,MAAM,KAAK;AAChB;AACA,cAAQ,KAAKA,GAAE,CAAC,MAAM,GAAG;AAAA,IAC3B,WAAWA,OAAM,OAAO,oBAAoB;AAC1C;AACA,cAAQ,IAAI;AAAA,IACd;AACA,cAAUA;AAAA,EACZ;AACA,SAAO;AACT;AAeO,SAAS,iBAAiB,YAAY,QAAQ,UAAU,SAAS;AAEtE,mBAAiB,YAAY,QAAQ,UAAU,OAAO;AACxD;AAcO,SAAS,cAAc,YAAY,QAAQ,MAAM,GAAG,SAAS;AAElE,MAAI,CAAE,IAAI,OAAO,QAAQ,IAAI,EAAE,KAAK,UAAU,GAAI;AAChD,WAAO;AAAA,EACT;AACA,QAAMD,MAAK,IAAI,OAAO,GAAG,MAAM,qBAAqB,KAAK;AACzD,EAAAA,IAAG,YAAY;AACf,MAAI,qBAAqB;AACzB,MAAI;AACJ,SAAO,QAAQA,IAAG,KAAK,UAAU,GAAG;AAClC,UAAM,EAAC,GAAGC,IAAG,QAAQ,EAAC,MAAK,EAAC,IAAI;AAChC,QAAI,CAAC,UAAU,CAAC,WAAY,YAAY,QAAQ,YAAa,CAAC,qBAAqB;AACjF,aAAO;AAAA,IACT;AACA,QAAIA,OAAM,KAAK;AACb;AAAA,IACF,WAAWA,OAAM,OAAO,oBAAoB;AAC1C;AAAA,IACF;AAEA,QAAID,IAAG,aAAa,MAAM,OAAO;AAC/B,MAAAA,IAAG;AAAA,IACL;AAAA,EACF;AACA,SAAO;AACT;AAYO,SAAS,aAAa,YAAY,QAAQ,SAAS;AAExD,SAAO,CAAC,CAAC,cAAc,YAAY,QAAQ,GAAG,OAAO;AACvD;AAaO,SAAS,iBAAiB,YAAY,kBAAkB;AAC7D,QAAME,SAAQ;AACd,EAAAA,OAAM,YAAY;AAClB,MAAI,iBAAiB,WAAW;AAChC,MAAI,qBAAqB;AAEzB,MAAI,gBAAgB;AACpB,MAAI;AACJ,SAAO,QAAQA,OAAM,KAAK,UAAU,GAAG;AACrC,UAAM,CAACD,EAAC,IAAI;AACZ,QAAIA,OAAM,KAAK;AACb;AAAA,IACF,WAAW,CAAC,oBAAoB;AAC9B,UAAIA,OAAM,KAAK;AACb;AAAA,MACF,WAAWA,OAAM,KAAK;AACpB;AACA,YAAI,CAAC,eAAe;AAClB,2BAAiB,MAAM;AACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAWA,OAAM,KAAK;AACpB;AAAA,IACF;AAAA,EACF;AACA,SAAO,WAAW,MAAM,kBAAkB,cAAc;AAC1D;;;ACtKA,IAAM,oBAAoB,IAAI,OAAO,OAAO,2BAA2B,iBAAiB,8CAA8C,KAAK;AAQ3I,SAAS,OAAO,YAAY,MAAM;AAChC,QAAM,iBAAiB,MAAM,kBAAkB,CAAC;AAEhD,MAAI,mBAAmB,MAAM,oBAAoB,oBAAI,IAAI;AACzD,MAAI,CAAC,QAAQ,KAAK,UAAU,GAAG;AAC7B,WAAO;AAAA,MACL,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UAAU;AAChB,QAAM,kBAAkB;AACxB,QAAM,gBAAgB,CAAC,CAAC;AACxB,QAAM,sBAAsB,CAAC;AAC7B,MAAI,sBAAsB;AAC1B,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI;AACJ,KAAG;AACD,qBAAiB;AACjB,QAAI,qBAAqB;AACzB,QAAI,oBAAoB;AACxB,QAAI,OAAO;AACX,QAAI;AACJ,sBAAkB,YAAY,OAAO,MAAM,KAAK,IAAI,IAAI,QAAQ,gBAAgB;AAChF,WAAO,QAAQ,kBAAkB,KAAK,UAAU,GAAG;AACjD,YAAM,EAAC,GAAGE,IAAG,OAAO,QAAQ,EAAC,gBAAgB,kBAAiB,EAAC,IAAI;AACnE,UAAIA,OAAM,KAAK;AACb;AAAA,MACF,WAAW,CAAC,oBAAoB;AAE9B,YAAIA,OAAM,WAAW,CAAC,MAAM;AAC1B,kBAAQ;AACR,iBAAO;AAAA,QACT,WAAW,QAAQ,mBAAmB;AACpC;AAAA,QACF,WAAW,gBAAgB;AACzB,cAAI,MAAM;AACR;AAAA,UACF,OAAO;AACL;AACA,0BAAc,KAAK,sBAAsB,MAAM;AAAA,UACjD;AAAA,QACF,WAAWA,OAAM,OAAO,MAAM;AAC5B,cAAI,CAAC,mBAAmB;AACtB;AACA,kBAAM,kBAAkB,sBAAsB;AAK9C,yBAAa,GAAG,WAAW,MAAM,GAAG,KAAK,CAAC,GAAG,eAAe,GACxD,WAAW,MAAM,QAAQ,QAAQ,QAAQ,KAAK,CAChD,QAAQ,eAAe,KAAK,WAAW,MAAM,QAAQ,CAAC,CAAC;AACzD,6BAAiB;AACjB,gCAAoB,KAAK,eAAe;AACxC,+BAAmB,gBAAgB,eAAe;AAClD,gBAAI,iBAAiB,MAAM;AACzB,oBAAM,sBAAsB,oBAAI,IAAI;AACpC,+BAAiB,QAAQ,CAAC,MAAM,OAAO;AACrC,oCAAoB;AAAA,kBAClB,MAAM,kBAAkB,KAAK,IAAI;AAAA,kBACjC,KAAK,IAAI,CAAAC,OAAKA,MAAK,kBAAkBA,KAAI,IAAIA,EAAC;AAAA,gBAChD;AAAA,cACF,CAAC;AACD,iCAAmB;AAAA,YACrB;AACA;AAAA,UACF;AACA;AAAA,QACF;AAAA,MAEF,WAAWD,OAAM,KAAK;AACpB;AAAA,MACF;AAAA,IACF;AAAA,EAGF,SAAS;AAET,iBAAe,KAAK,GAAG,mBAAmB;AAG1C,eAAa;AAAA,IACX;AAAA,IACA,OAAO;AAAA,IACP,CAAC,EAAC,GAAGA,IAAG,QAAQ,EAAC,YAAY,kBAAiB,EAAC,MAAM;AACnD,UAAI,YAAY;AACd,cAAM,OAAO,CAAC;AACd,YAAI,OAAO,cAAc,SAAS,GAAG;AACnC,gBAAM,IAAI,MAAM,YAAYA,EAAC,mCAAmC;AAAA,QAClE;AACA,eAAO,KAAK,cAAc,IAAI,CAAC;AAAA,MACjC;AACA,aAAO,KAAK,iBAAiB;AAAA,IAC/B;AAAA,IACA,QAAQ;AAAA,EACV;AAEA,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,iBAAiB,OAAO;AAE9B,IAAM,wBAAwB,IAAI,OAAO,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAanC,cAAc;AAAA;AAAA,EAEzB,QAAQ,QAAQ,EAAE,GAAG,KAAK;AAU5B,SAAS,WAAW,YAAY;AAC9B,MAAI,CAAE,IAAI,OAAO,GAAG,cAAc,KAAK,EAAE,KAAK,UAAU,GAAI;AAC1D,WAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,EACF;AAEA,QAAM,mBAAmB,CAAC;AAC1B,MAAI,iBAAiB;AACrB,MAAI,qBAAqB;AACzB,MAAI,YAAY;AAChB,MAAI,qBAAqB;AACzB,MAAI;AACJ,wBAAsB,YAAY;AAClC,SAAO,QAAQ,sBAAsB,KAAK,UAAU,GAAG;AACrD,UAAM,EAAC,GAAGA,IAAG,OAAO,QAAQ,EAAC,OAAO,MAAM,SAAQ,EAAC,IAAI;AACvD,QAAIA,OAAM,KAAK;AACb,UAAI,CAAC,oBAAoB;AACvB,6BAAqB;AAAA,MACvB;AACA;AAAA,IACF,WAAWA,OAAM,KAAK;AACpB,UAAI,oBAAoB;AACtB;AAAA,MAEF,OAAO;AACL,6BAAqB;AAAA,MACvB;AAAA,IACF,WAAW,CAAC,oBAAoB;AAE9B,UAAI,SAAS,OAAO,aAAa,CAAC,UAAU,WAAW,GAAG,GAAG;AAE3D,YAAI,UAAU;AACZ,gBAAM,IAAI,MAAM,uBAAuBA,EAAC,GAAG;AAAA,QAC7C;AACA,YAAI,aAAa;AAGjB,YAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,uBAAa,UAAU,YAAY,QAAQ,MAAM,QAAQ,MAAM,EAAE;AAAA,QACnE,OAAO;AACL,cAAI,cAAc,OAAO,cAAc,KAAK;AAC1C,kBAAM,YAAY,cAAc,MAAM,iBAAiB;AAIvD,gBAAI,cAAc,MAAM;AACtB,oBAAM,IAAI,MAAM,sBAAsB,SAAS,GAAG;AAAA,YACpD;AACA,yBAAa,GAAG,WAAW,MAAM,GAAG,SAAS,CAAC,MAAM,WAAW,MAAM,WAAW,KAAK,CAAC,GAAG,KAAK,IAAI,WAAW,MAAM,QAAQA,GAAE,MAAM,CAAC;AAAA,UACtI,OAAO;AACL,yBAAa,GAAG,WAAW,MAAM,GAAG,QAAQ,UAAU,MAAM,CAAC,MAAM,SAAS,GAAG,KAAK,IAAI,WAAW,MAAM,QAAQA,GAAE,MAAM,CAAC;AAAA,UAC5H;AACA,wBAAc;AAAA,QAChB;AACA,8BAAsB,aAAa;AAAA,MACrC,WAAWA,GAAE,CAAC,MAAM,KAAK;AACvB,yBAAiB,KAAK,KAAK;AAAA,MAC7B,WAAWA,OAAM,KAAK;AACpB,yBAAiB,iBAAiB,SAAS,iBAAiB,IAAI,IAAI;AAAA,MACtE;AAAA,IAEF;AACA,gBAAYA;AAAA,EACd;AAEA,SAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;;;ACrNA,IAAME,KAAI,OAAO;AACjB,IAAM,UAAUA;AAChB,IAAM,iBAAiBA,+BAA8B,OAAO;AAC5D,IAAM,oBAAoBA;AAC1B,IAAM,eAAeA,KAAI,iBAAiB;AAC1C,IAAM,QAAQ,IAAI,OAAOA,KAAI,iBAAiB,IAAI,cAAc,cAAc,KAAK;AACnF,IAAM,0BAA0B;AAgBhC,SAAS,UAAU,SAAS,MAAM;AAChC,QAAM,EAAC,gBAAgB,KAAI,IAAI;AAAA,IAC7B,gBAAgB,CAAC;AAAA,IACjB,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AAEA,MAAI,mBAAmB,MAAM,oBAAoB,oBAAI,IAAI;AAGzD,MAAI,CAAE,IAAI,OAAO,gBAAgB,IAAI,EAAE,KAAK,OAAO,GAAI;AACrD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS,YAAY,aAAa,SAASA,oBAAmB,QAAQ,OAAO,GAAG;AAClF,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC/D;AAEA,QAAM,sBAAsB,CAAC;AAC7B,QAAM,qBAAqB,aAAa,SAASA,aAAY,QAAQ,OAAO;AAC5E,QAAM,wBAAwB,oBAAI,IAAI;AACtC,QAAM,aAAa,CAAC;AACpB,MAAI,cAAc;AAClB,MAAI,qBAAqB;AACzB,MAAI,oBAAoB;AACxB,MAAI;AACJ,QAAM,YAAY;AAClB,SAAQ,QAAQ,MAAM,KAAK,OAAO,GAAI;AACpC,UAAM,EAAC,GAAGC,IAAG,QAAQ,EAAC,aAAa,QAAQ,aAAa,QAAO,EAAC,IAAI;AACpE,QAAIA,OAAM,KAAK;AACb;AAAA,IACF,WAAW,CAAC,oBAAoB;AAG9B,UAAI,QAAQ;AACV,0BAAkB,MAAM;AACxB,YAAI,aAAa;AACf,gBAAM,IAAI,MAAM,uBAAuB;AAAA,QACzC;AACA,YAAI,oBAAoB;AAStB,gBAAM,IAAI;AAAA;AAAA;AAAA,YAGR,GAAG,SAAS,aAAa,aAAa,mBAAmB;AAAA,UAC3D;AAAA,QACF;AACA,cAAM,OAAO,QAAQ,MAAM,GAAG,MAAM,KAAK;AACzC,cAAM,QAAQ,QAAQ,MAAM,MAAM,SAAS;AAC3C,YAAI,aAAa,OAAO,gBAAgB,QAAQ,OAAO,GAAG;AACxD,gBAAM,IAAI,MAAM,uBAAuB;AAAA,QACzC;AACA,cAAM,OAAO,CAAC,SAAS;AACvB,kBAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,2BAAmB;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA,oBAAoB;AAAA,UACpB;AAAA,UACA;AAAA,QACF;AAEA;AAAA,MAEF,WAAW,aAAa;AACtB,0BAAkB,OAAO;AACzB,YAAI,sBAAsB;AAC1B,mBAAW,KAAK,YAAY;AAC1B,cAAI,EAAE,SAAS,eAAe,EAAE,QAAQ,CAAC,aAAa;AACpD,kCAAsB;AACtB,gBAAI,EAAE,mBAAmB;AACvB,oBAAM,IAAI,MAAM,uBAAuB;AAAA,YACzC;AACA;AAAA,UACF;AAAA,QACF;AACA,YAAI,CAAC,qBAAqB;AACxB,gBAAM,IAAI,MAAMD,+DACd,SAAS,aAAa,cAAcA,QAAO,WAAW,MAAM,OAAO,GACrE,GAAG;AAAA,QACL;AACA,cAAM,WAAW,sBAAsB,IAAI,WAAW;AACtD,cAAM,gBAAgB,iBAAiB,SAAS,QAAQ;AACxD,YACE,sBACA,aAAa,eAAeA,KAAI,iBAAiB,aAAa,QAAQ,OAAO,GAC7E;AACA,gBAAM,IAAI;AAAA;AAAA;AAAA,YAGR,GAAG,SAAS,aAAa,aAAa,mBAAmB;AAAA,UAC3D;AAAA,QACF;AACA,cAAM,oBAAoB,QAAQ,MAAM,UAAU,MAAM,KAAK;AAC7D,cAAM,qBAAqB,cAAc,MAAM,kBAAkB,SAASC,GAAE,MAAM;AAClF,cAAM,qCAAqC,oBAAoB;AAC/D,cAAM,OAAO,CAAC,UAAU;AACxB,cAAM,YAAY;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,2BAAmB;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA,oBAAoB,SAAS;AAAA,UAC7B;AAAA,UACA;AAAA,QACF;AACA,cAAM,MAAM,QAAQ,MAAM,GAAG,QAAQ;AACrC,cAAM,OAAO,QAAQ,MAAM,WAAW,cAAc,MAAM;AAE1D,kBAAU,GAAG,GAAG,GAAG,SAAS,GAAG,IAAI;AAEnC,cAAM,aAAa,UAAU,SAASA,GAAE,SAAS,kBAAkB,SAAS,mBAAmB;AAC/F,mBAAW,QAAQ,OAAK,EAAE,oBAAoB,IAAI;AAClD,sBAAc;AAAA,MAChB,WAAW,aAAa;AACtB;AACA,8BAAsB,IAAI,OAAO,iBAAiB,GAAG,MAAM,SAAS;AACpE,8BAAsB,IAAI,aAAa,MAAM,SAAS;AACtD,mBAAW,KAAK;AAAA,UACd,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC;AAAA,MACH,WAAWA,GAAE,CAAC,MAAM,KAAK;AACvB,cAAM,mBAAmBA,OAAM;AAC/B,YAAI,kBAAkB;AACpB;AACA,gCAAsB,IAAI,OAAO,iBAAiB,GAAG,MAAM,SAAS;AAAA,QACtE;AACA,mBAAW,KAAK,mBAAmB,EAAC,KAAK,kBAAiB,IAAI,CAAC,CAAC;AAAA,MAClE,WAAWA,OAAM,KAAK;AACpB,mBAAW,IAAI;AAAA,MACjB;AAAA,IAEF,WAAWA,OAAM,KAAK;AACpB;AAAA,IACF;AAAA,EACF;AAEA,iBAAe,KAAK,GAAG,mBAAmB;AAE1C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKA,SAAS,kBAAkB,KAAK;AAC9B,QAAM,SAAS,qDAAqD,GAAG;AACvE,MAAI,CAAC,aAAa,KAAK,GAAG,GAAG;AAC3B,UAAM,IAAI,MAAM,MAAM;AAAA,EACxB;AACA,QAAM,CAAC;AACP,MAAI,MAAM,KAAK,MAAM,KAAK;AACxB,UAAM,IAAI,MAAM,MAAM;AAAA,EACxB;AACF;AAYA,SAAS,cACP,MACA,OACA,MACA,cACA,gBACA,qBACA,mBACA;AACA,QAAM,kBAAkB,oBAAI,IAAI;AAEhC,MAAI,cAAc;AAChB,qBAAiB,OAAO,OAAO,mBAAmB,CAAC,EAAC,QAAQ,EAAC,YAAW,EAAC,MAAM;AAC7E,sBAAgB,IAAI,WAAW;AAAA,IACjC,GAAG,QAAQ,OAAO;AAAA,EACpB;AACA,QAAM,OAAO;AAAA,IACX;AAAA,IACA,eAAe,kBAAkB;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAIA,SAAO,GAAG,IAAI,GACZ,gBAAgB,MAAM,IAAI,IAAI,WAAW,GAAG,IAAI,CAClD,OACE,gBAAgB,GAAG,KAAK,KAAK,YAAY,GAAG,IAAI,CAClD,GAAG,KAAK;AACV;AAYA,SAAS,gBACP,SACA,WACA,MACA,iBACA,gBACA,qBACA,mBACA;AACA,QAAM,WAAW;AACjB,QAAM,cAAc,CAAAC,OAAK,cAAc,YAAaA,KAAI,WAAa,OAAOA,KAAI,WAAW;AAC3F,MAAI,SAAS;AACb,WAASA,KAAI,GAAGA,KAAI,MAAMA,MAAK;AAC7B,UAAM,WAAW,YAAYA,EAAC;AAC9B,cAAU;AAAA,MACR;AAAA,MACAF,KAAI,YAAY;AAAA,MAChB,CAAC,EAAC,GAAGC,IAAG,QAAQ,EAAC,aAAa,SAAS,QAAO,EAAC,MAAM;AACnD,YAAI,WAAW,mBAAmB,CAAC,gBAAgB,IAAI,OAAO,GAAG;AAE/D,iBAAOA;AAAA,QACT;AACA,cAAM,SAAS,KAAK,QAAQ;AAC5B,YAAI,WAAW,aAAa;AAC1B,gBAAM,kBAAkB,oBAAoB,oBAAoB,SAAS;AACzE,8BAAoB,KAAK,eAAe;AACxC,UAAAE,oBAAmB,gBAAgB,eAAe;AAClD,iBAAO,UAAUF,KAAI,MAAM,WAAW,GAAG,MAAM;AAAA,QACjD;AACA,eAAOD,QAAO,OAAO,GAAG,MAAM;AAAA,MAChC;AAAA,MACA,QAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAASG,oBAAmB,KAAK,WAAW;AAC1C,WAASD,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACnC,QAAI,IAAIA,EAAC,KAAK,WAAW;AACvB,UAAIA,EAAC;AAAA,IACP;AAAA,EACF;AACF;AAWA,SAAS,oBAAoB,kBAAkB,MAAM,MAAM,6BAA6B,oCAAoC,mBAAmB;AAC7I,MAAI,iBAAiB,QAAQ,6BAA6B;AACxD,QAAI,oBAAoB;AACxB,qBAAiB,MAAM,cAAc,MAAM,qBAAqB,QAAQ,OAAO;AAE/E,UAAM,2BAA2B,oBAAoB,oBAAoB;AACzE,UAAM,sBAAsB,oBAAI,IAAI;AACpC,qBAAiB,QAAQ,CAAC,MAAM,OAAO;AACrC,YAAM,sBAAsB,8BAA+B,oBAAoB,QAAS;AACxF,YAAM,yBAAyB,oBAAoB;AACnD,YAAM,QAAQ,KAAM,2BAA2B,oBAAqB,KAAK,8BAA8B;AACvG,YAAM,UAAU,CAAC;AACjB,iBAAWE,MAAK,MAAM;AAEpB,YAAIA,MAAK,0BAA0B;AACjC,kBAAQ,KAAKA,EAAC;AAAA,QAEhB,WAAWA,KAAK,2BAA2B,oBAAoB,oBAAqB;AAClF,kBAAQ,KAAKA,KAAI,2BAA2B;AAAA,QAE9C,WAAWA,MAAM,2BAA2B,mBAAoB;AAC9D,mBAASF,KAAI,GAAGA,MAAK,MAAMA,MAAK;AAC9B,oBAAQ,KAAKE,KAAK,oBAAoBF,EAAE;AAAA,UAC1C;AAAA,QAEF,OAAO;AACL,mBAASA,KAAI,GAAGA,MAAK,MAAMA,MAAK;AAC9B,oBAAQ,KAAKE,KAAI,yBAA0B,qBAAqBF,EAAE;AAAA,UACpE;AAAA,QACF;AAAA,MACF;AACA,0BAAoB,IAAI,OAAO,OAAO;AAAA,IACxC,CAAC;AACD,WAAO;AAAA,EACT;AACA,SAAO;AACT;;;AfrTA,SAAS,SAAS,SAAS,SAAS;AAClC,QAAMG,KAAI,gBAAgB,SAAS,OAAO;AAC1C,MAAIA,GAAE,SAAS;AACb,WAAO,IAAI,eAAeA,GAAE,SAASA,GAAE,OAAOA,GAAE,OAAO;AAAA,EACzD;AACA,SAAO,IAAI,OAAOA,GAAE,SAASA,GAAE,KAAK;AACtC;AAYA,SAAS,gBAAgB,SAAS,SAAS;AACzC,QAAM,OAAO,WAAW,OAAO;AAC/B,QAAM,eAAeC,GAAM,SAAS;AAAA,IAClC,OAAO,KAAK;AAAA,IACZ,+BAA+B;AAAA,IAC/B,OAAO;AAAA,MACL,cAAc,KAAK,MAAM;AAAA,MACzB,YAAY,KAAK,MAAM;AAAA,IACzB;AAAA,IACA,uBAAuB,KAAK,MAAM;AAAA,IAClC,oBAAoB;AAAA,EACtB,CAAC;AACD,QAAM,eAAe,UAAU,cAAc;AAAA,IAC3C,UAAU,KAAK;AAAA,IACf,qBAAqB,KAAK,MAAM;AAAA,IAChC,eAAe,KAAK;AAAA,IACpB,kBAAkB,KAAK;AAAA,EACzB,CAAC;AACD,QAAM,YAAY,SAAS,cAAc,IAAI;AAC7C,QAAM,kBAAkB,UAAU,UAAU,SAAS;AAAA,IACnD,kBAAkB,UAAU;AAAA,IAC5B,gBAAgB,UAAU;AAAA,IAC1B,MAAM;AAAA,EACR,CAAC;AACD,QAAM,mBAAmB,WAAW,gBAAgB,OAAO;AAC3D,QAAM,eAAe,OAAO,iBAAiB,SAAS;AAAA,IACpD,kBAAkB,gBAAgB;AAAA,IAClC,gBAAgB,gBAAgB;AAAA,EAClC,CAAC;AACD,QAAM,UAAU;AAAA,IACd,SAAS,aAAa;AAAA,IACtB,OAAO,GAAG,KAAK,aAAa,MAAM,EAAE,GAAG,KAAK,SAAS,MAAM,EAAE,GAAG,UAAU,KAAK,GAAG,UAAU,QAAQ,QAAQ,IAAI,MAAM,GAAG;AAAA,EAC3H;AACA,MAAI,KAAK,eAAe;AACtB,QAAI,KAAK,sBAAsB,UAAU;AACvC,YAAM,IAAI,MAAM,oCAAoC;AAAA,IACtD;AAAA,EACF,OAAO;AAEL,UAAM,iBAAiB,aAAa,eAAe,KAAK,CAACC,IAAGC,OAAMD,KAAIC,EAAC;AAEvE,UAAM,YAAY,MAAM,KAAK,aAAa,gBAAgB;AAC1D,UAAM,WAAW,aAAa;AAC9B,UAAM,cAAc,QAAQ,QAAQ,UAAU,KAAK;AACnD,QAAI,eAAe,UAAU,UAAU,UAAU,YAAY,aAAa;AACxE,cAAQ,UAAU;AAAA,QAChB,GAAI,eAAe,UAAU,EAAC,eAAc;AAAA,QAC5C,GAAI,UAAU,UAAU,EAAC,UAAS;AAAA,QAClC,GAAI,YAAY,EAAC,SAAQ;AAAA,QACzB,GAAI,eAAe,EAAC,YAAW;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;", "names": ["cpOf", "char", "getOrInsert", "map", "key", "defaultValue", "PosixClassNames", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "msg", "charClassOpenPattern", "r", "sharedEscapesPattern", "quantifierRe", "tokenRe", "charClassTokenRe", "tokenize", "pattern", "options", "opts", "flagProperties", "getFlagProperties", "xStack", "context", "isXOn", "tokens", "match", "result", "getTokenWithDetails", "potentialUnnamedCaptureTokens", "numNamedAndOptInUnnamedCaptures", "t", "i", "numCaptures", "splitEscapedNumberToken", "m", "lastIndex", "m0", "m1", "getAllTokensForCharClass", "createAssertionToken", "createSubroutineToken", "createBackreferenceToken", "createDirectiveToken", "createCharacterSetToken", "tokenizeSharedEscape", "tokenizeNamedCallout", "tokenizeFlagModifier", "createGroupOpenToken", "createGroupCloseToken", "end", "re", "kind", "createAlternatorToken", "splitQuantifierMatch", "createCharacterToken", "cpOf", "opener", "createCharacterClassOpenToken", "numCharClassesOpen", "createCharacterClassCloseToken", "tokenizeAnyTokenWithinCharClass", "raw", "posix", "PosixClassNames", "createCharacterClassHyphenToken", "createCharacterClassIntersectorToken", "inCharClass", "char1", "tokenizeControlCharacter", "tokenizeS<PERSON>thand", "tokenizeUnicodeProperty", "bytes", "hex", "decoded", "encoder", "char", "byte", "getValidatedHexCharCode", "EscapeCharCodes", "createEscapedNumberToken", "value", "negate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createNamedCalloutToken", "tag", "args", "createQuantifierToken", "min", "max", "CalloutNames", "on", "off", "enabledFlags", "getFlagGroupSwitches", "disabledFlags", "flagChanges", "callout", "name", "<PERSON>rg<PERSON><PERSON><PERSON><PERSON>", "arg", "arg0", "arg1", "arg2", "tokenizeQuantifier", "minStr", "maxStr", "limit", "lower", "p", "neg", "flags", "obj", "token", "matches", "str", "withG", "parts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "props", "kid", "key", "isQuantifiable", "node", "quantifiableTypes", "parse", "pattern", "options", "opts", "tokenized", "tokenize", "walk", "parent", "state", "token", "context", "createAlternative", "parseAssertion", "parseBackreference", "createCharacter", "parseCharacterClassHyphen", "parseCharacterClassOpen", "parseCharacterSet", "createDirective", "parseGroupOpen", "createNamedCallout", "parseQuantifier", "parseSubroutine", "ast", "createRegex", "createFlags", "top", "node", "capturingGroups", "hasNumberedRef", "namedGroupsByName", "subroutines", "ref", "r", "kind", "createAssertion", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raw", "hasKWrapper", "fromNum", "num", "isRelative", "numCapturesToLeft", "orphan", "createBackreference", "numberedRef", "_", "tokens", "prevSiblingNode", "nextToken", "nextNode", "createCharacterClassRange", "cpOf", "negate", "firstClassToken", "intersections", "createCharacterClass", "throwIfUnclosedCharacterClass", "cc", "value", "normalizeUnknownPropertyNames", "skipPropertyNameValidation", "unicodePropertyMap", "normalized", "slug", "PosixClassNames", "createUnicodeProperty", "createPosixClass", "createCharacterSet", "skipLookbehindValidation", "createByGroupKind", "isThisAbsenceFunction", "isThisLookbehind", "isLookbehind", "isThisNegLookbehind", "getOrInsert", "throwIfUnclosedGroup", "alt", "child", "msg", "isLookahead", "min", "max", "quantifiedNode", "isQuantifiable", "createQuantifier", "createSubroutine", "createAbsenceFunction", "getBodyForAlternativeContainer", "getBodyForElementContainer", "createCapturingGroup", "number", "isValidGroupName", "charCode", "hex", "flags", "createGroup", "atomic", "createLookaroundAssertion", "tag", "args", "name", "body", "normalizeUnicodePropertyName", "m", "w", "range", "i", "traverse", "root", "visitor", "state", "traverseArray", "array", "parent", "i", "keyShift", "traverseNode", "node", "key", "container", "skipTraversingKidsOfPath", "path", "arrayContainer", "numericKey", "shifted", "newNode", "options", "traverseNew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newNodes", "keyShiftInLoop", "type", "anyTypeVisitor", "thisTypeVisitor", "enterAllFn", "enterThisFn", "value", "A", "K", "Q", "_", "i", "b", "F", "m", "B", "C", "E", "w", "o", "f", "node", "recursion", "k", "P", "a", "O", "J", "_", "m", "A", "b", "atomic", "i", "a", "re", "i", "m", "i", "re", "m", "token", "m", "f", "r", "m", "i", "incrementIfAtLeast", "f", "d", "J", "a", "b"]}