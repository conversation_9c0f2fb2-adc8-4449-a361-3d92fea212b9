module.exports = {

"[turbopack-node]/transforms/webpack-loaders.ts [webpack_loaders] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/[root-of-the-server]__b64ae596._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/webpack-loaders.ts [webpack_loaders] (ecmascript)");
    });
});
}),

};