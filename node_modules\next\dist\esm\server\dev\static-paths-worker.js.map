{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../config-shared'\n\nimport '../require-hook'\nimport '../node-environment'\n\nimport { reduceAppConfig } from '../../build/utils'\nimport { collectSegments } from '../../build/segment-config/app/app-segments'\nimport type { StaticPathsResult } from '../../build/static-paths/types'\nimport { loadComponents } from '../load-components'\nimport { setHttpClientAndAgentOptions } from '../setup-http-agent-env'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport { isAppPageRouteModule } from '../route-modules/checks'\nimport {\n  checkIsRoutePPREnabled,\n  type ExperimentalPPRConfig,\n} from '../lib/experimental/ppr'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { collectRootParamKeys } from '../../build/segment-config/app/collect-root-param-keys'\nimport { buildAppStaticPaths } from '../../build/static-paths/app'\nimport { buildPagesStaticPaths } from '../../build/static-paths/pages'\nimport { createIncrementalCache } from '../../export/helpers/create-incremental-cache'\n\ntype RuntimeConfig = {\n  pprConfig: ExperimentalPPRConfig | undefined\n  configFileName: string\n  publicRuntimeConfig: { [key: string]: any }\n  serverRuntimeConfig: { [key: string]: any }\n  cacheComponents: boolean\n}\n\n// we call getStaticPaths in a separate process to ensure\n// side-effects aren't relied on in dev that will break\n// during a production build\nexport async function loadStaticPaths({\n  dir,\n  distDir,\n  pathname,\n  config,\n  httpAgentOptions,\n  locales,\n  defaultLocale,\n  isAppPath,\n  page,\n  isrFlushToDisk,\n  fetchCacheKeyPrefix,\n  maxMemoryCacheSize,\n  requestHeaders,\n  cacheHandler,\n  cacheHandlers,\n  cacheLifeProfiles,\n  nextConfigOutput,\n  buildId,\n  authInterrupts,\n  sriEnabled,\n}: {\n  dir: string\n  distDir: string\n  pathname: string\n  config: RuntimeConfig\n  httpAgentOptions: NextConfigComplete['httpAgentOptions']\n  locales?: readonly string[]\n  defaultLocale?: string\n  isAppPath: boolean\n  page: string\n  isrFlushToDisk?: boolean\n  fetchCacheKeyPrefix?: string\n  maxMemoryCacheSize?: number\n  requestHeaders: IncrementalCache['requestHeaders']\n  cacheHandler?: string\n  cacheHandlers?: NextConfigComplete['experimental']['cacheHandlers']\n  cacheLifeProfiles?: {\n    [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n  }\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  buildId: string\n  authInterrupts: boolean\n  sriEnabled: boolean\n}): Promise<StaticPathsResult> {\n  // this needs to be initialized before loadComponents otherwise\n  // \"use cache\" could be missing it's cache handlers\n  await createIncrementalCache({\n    dir,\n    distDir,\n    cacheHandler,\n    cacheHandlers,\n    requestHeaders,\n    fetchCacheKeyPrefix,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  // update work memory runtime-config\n  ;(\n    require('../../shared/lib/runtime-config.external') as typeof import('../../shared/lib/runtime-config.external')\n  ).setConfig(config)\n  setHttpClientAndAgentOptions({\n    httpAgentOptions,\n  })\n\n  const components = await loadComponents({\n    distDir,\n    // In `pages/`, the page is the same as the pathname.\n    page: page || pathname,\n    isAppPath,\n    isDev: true,\n    sriEnabled,\n  })\n\n  if (isAppPath) {\n    const segments = await collectSegments(components)\n\n    const isRoutePPREnabled =\n      isAppPageRouteModule(components.routeModule) &&\n      checkIsRoutePPREnabled(config.pprConfig, reduceAppConfig(segments))\n\n    const rootParamKeys = collectRootParamKeys(components)\n\n    return buildAppStaticPaths({\n      dir,\n      page: pathname,\n      cacheComponents: config.cacheComponents,\n      segments,\n      distDir,\n      requestHeaders,\n      cacheHandler,\n      cacheLifeProfiles,\n      isrFlushToDisk,\n      fetchCacheKeyPrefix,\n      maxMemoryCacheSize,\n      ComponentMod: components.ComponentMod,\n      nextConfigOutput,\n      isRoutePPREnabled,\n      buildId,\n      authInterrupts,\n      rootParamKeys,\n    })\n  } else if (!components.getStaticPaths) {\n    // We shouldn't get to this point since the worker should only be called for\n    // SSG pages with getStaticPaths.\n    throw new InvariantError(\n      `Failed to load page with getStaticPaths for ${pathname}`\n    )\n  }\n\n  return buildPagesStaticPaths({\n    page: pathname,\n    getStaticPaths: components.getStaticPaths,\n    configFileName: config.configFileName,\n    locales,\n    defaultLocale,\n  })\n}\n"], "names": ["reduceAppConfig", "collectSegments", "loadComponents", "setHttpClientAndAgentOptions", "isAppPageRouteModule", "checkIsRoutePPREnabled", "InvariantError", "collectRootParamKeys", "buildAppStaticPaths", "buildPagesStaticPaths", "createIncrementalCache", "loadStaticPaths", "dir", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheLifeProfiles", "nextConfigOutput", "buildId", "authInterrupts", "sriEnabled", "flushToDisk", "cacheMaxMemorySize", "require", "setConfig", "components", "isDev", "segments", "isRoutePPREnabled", "routeModule", "pprConfig", "rootParamKeys", "cacheComponents", "ComponentMod", "getStaticPaths", "configFileName"], "mappings": "AAEA,OAAO,kBAAiB;AACxB,OAAO,sBAAqB;AAE5B,SAASA,eAAe,QAAQ,oBAAmB;AACnD,SAASC,eAAe,QAAQ,8CAA6C;AAE7E,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,4BAA4B,QAAQ,0BAAyB;AAEtE,SAASC,oBAAoB,QAAQ,0BAAyB;AAC9D,SACEC,sBAAsB,QAEjB,0BAAyB;AAChC,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,oBAAoB,QAAQ,yDAAwD;AAC7F,SAASC,mBAAmB,QAAQ,+BAA8B;AAClE,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,SAASC,sBAAsB,QAAQ,gDAA+C;AAUtF,yDAAyD;AACzD,uDAAuD;AACvD,4BAA4B;AAC5B,OAAO,eAAeC,gBAAgB,EACpCC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,aAAa,EACbC,iBAAiB,EACjBC,gBAAgB,EAChBC,OAAO,EACPC,cAAc,EACdC,UAAU,EAwBX;IACC,+DAA+D;IAC/D,mDAAmD;IACnD,MAAMrB,uBAAuB;QAC3BE;QACAC;QACAY;QACAC;QACAF;QACAF;QACAU,aAAaX;QACbY,oBAAoBV;IACtB;IAIEW,QAAQ,4CACRC,SAAS,CAACpB;IACZZ,6BAA6B;QAC3Ba;IACF;IAEA,MAAMoB,aAAa,MAAMlC,eAAe;QACtCW;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;QACAkB,OAAO;QACPN;IACF;IAEA,IAAIZ,WAAW;QACb,MAAMmB,WAAW,MAAMrC,gBAAgBmC;QAEvC,MAAMG,oBACJnC,qBAAqBgC,WAAWI,WAAW,KAC3CnC,uBAAuBU,OAAO0B,SAAS,EAAEzC,gBAAgBsC;QAE3D,MAAMI,gBAAgBnC,qBAAqB6B;QAE3C,OAAO5B,oBAAoB;YACzBI;YACAQ,MAAMN;YACN6B,iBAAiB5B,OAAO4B,eAAe;YACvCL;YACAzB;YACAW;YACAC;YACAE;YACAN;YACAC;YACAC;YACAqB,cAAcR,WAAWQ,YAAY;YACrChB;YACAW;YACAV;YACAC;YACAY;QACF;IACF,OAAO,IAAI,CAACN,WAAWS,cAAc,EAAE;QACrC,4EAA4E;QAC5E,iCAAiC;QACjC,MAAM,qBAEL,CAFK,IAAIvC,eACR,CAAC,4CAA4C,EAAEQ,UAAU,GADrD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOL,sBAAsB;QAC3BW,MAAMN;QACN+B,gBAAgBT,WAAWS,cAAc;QACzCC,gBAAgB/B,OAAO+B,cAAc;QACrC7B;QACAC;IACF;AACF", "ignoreList": [0]}