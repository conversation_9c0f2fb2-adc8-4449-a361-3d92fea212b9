{"version": 3, "sources": ["../../src/generator/generate.ts"], "sourcesContent": ["import type {GroupNode, Node, OnigurumaAst, ParentNode, QuantifierNode, RegexNode} from '../parser/parse.js';\nimport type {FlagProperties} from '../tokenizer/tokenize.js';\nimport {r, throwIfNullish} from '../utils.js';\n\ntype OnigurumaRegex = {\n  pattern: string;\n  flags: string;\n};\n\n/**\nGenerates an Oniguruma `pattern` and `flags` from an `OnigurumaAst`.\n*/\nfunction generate(ast: OnigurumaAst): OnigurumaRegex {\n  const parentStack: Array<ParentNode> = [];\n  let lastNode: Node = ast;\n  const state: State = {\n    inCharClass: false,\n    lastNode,\n    parent: ast,\n  };\n  const gen: Gen = node => {\n    state.lastNode = lastNode;\n    lastNode = node; // For the next iteration\n    if (getFirstChild(state.lastNode) === node) {\n      state.parent = state.lastNode as ParentNode;\n      parentStack.push(state.parent);\n    }\n    const fn = throwIfNullish(generator[node.type], `Unexpected node type \"${node.type}\"`);\n    // @ts-expect-error\n    const result = fn(node, state, gen);\n    if (getLastChild(state.parent) === node) {\n      parentStack.pop();\n      state.parent = parentStack.at(-1) ?? ast;\n    };\n    return result;\n  };\n  return {\n    pattern: ast.body.map(gen).join('|'),\n    // Could reset `lastNode` at this point via `lastNode = ast`, but it isn't needed by flags\n    flags: gen(ast.flags),\n  };\n}\n\ntype State = {\n  inCharClass: boolean;\n  lastNode: Node;\n  parent: ParentNode;\n};\n\ntype NonRootNode = Exclude<Node, RegexNode>;\n\ntype Gen = (node: NonRootNode) => string;\n\ntype Generator = {\n  [N in NonRootNode as N['type']]:\n  (node: N, state: State, gen: Gen) => string\n};\n\nconst generator: Generator = {\n  AbsenceFunction({body, kind}, _, gen) {\n    if (kind !== 'repeater') {\n      throw new Error(`Unexpected absence function kind \"${kind}\"`);\n    }\n    return `(?~${body.map(gen).join('|')})`;\n  },\n\n  Alternative({body}, _, gen) {\n    return body.map(gen).join('');\n  },\n\n  Assertion({kind, negate}) {\n    if (kind === 'text_segment_boundary') {\n      return negate ? r`\\Y` : r`\\y`;\n    }\n    if (kind === 'word_boundary') {\n      return negate ? r`\\B` : r`\\b`;\n    }\n    return throwIfNullish({\n      line_end: '$',\n      line_start: '^',\n      search_start: r`\\G`,\n      string_end: r`\\z`,\n      string_end_newline: r`\\Z`,\n      string_start: r`\\A`,\n    }[kind], `Unexpected assertion kind \"${kind}\"`);\n  },\n\n  Backreference({ref}) {\n    if (typeof ref === 'number') {\n      // TODO: Won't be safe to indiscriminately unenclose when forward backrefs are supported\n      return '\\\\' + ref;\n    }\n    // Onig doesn't allow chars `>` or `'` in backref names, so this is safe\n    return `\\\\k<${ref}>`;\n  },\n\n  CapturingGroup({body, name}, _, gen) {\n    const enclosedName = name ? `?${name.includes('>') ? `'${name}'` : `<${name}>`}` : '';\n    return `(${enclosedName}${body.map(gen).join('|')})`;\n  },\n\n  Character(node, {inCharClass, lastNode, parent}) {\n    const {value} = node;\n    if (CharCodeEscapeMap.has(value)) {\n      return CharCodeEscapeMap.get(value)!;\n    }\n    const escDigit = lastNode.type === 'Backreference';\n    if (\n      // Control chars, etc.; condition modeled on the Chrome developer console's display for strings\n      value < 32 || (value > 126 && value < 160) ||\n      // Unicode planes 4-16; unassigned, special purpose, and private use area\n      value > 0x3FFFF ||\n      // Avoid corrupting a preceding backref by immediately following it with a literal digit\n      (escDigit && isDigitCharCode(value))\n    ) {\n      // Onig treats unenclosed `\\x80` to `\\xFF` as an encoded byte (a fragment of a code unit), so\n      // we can't use them to represent a character. Also, don't convert value `0` to `\\0` since\n      // that's corruptible by following literal digits\n      return value > 0x7F ?\n        `\\\\x{${value.toString(16).toUpperCase()}}` :\n        `\\\\x${value.toString(16).toUpperCase().padStart(2, '0')}`;\n    }\n    const char = String.fromCodePoint(value);\n    let escape = false;\n    if (inCharClass) {\n      const isDirectClassKid = parent.type === 'CharacterClass';\n      const isFirst = isDirectClassKid && parent.body[0] === node;\n      const isLast = isDirectClassKid && parent.body.at(-1) === node;\n      // Avoid escaping in some optional special cases when escaping isn't needed due to position\n      if (char === '^') {\n        escape = isFirst && !parent.negate;\n      } else if (char === ']') {\n        escape = !isFirst;\n      } else if (char === '-') {\n        // Could also avoid escaping if it's immediately after a range or nested class, but don't\n        // make that the default rendering\n        escape = !isFirst && !isLast;\n      } else {\n        escape = CharClassEscapeChars.has(char);\n      }\n    } else {\n      escape = BaseEscapeChars.has(char);\n    }\n    return `${escape ? '\\\\' : ''}${char}`;\n  },\n\n  CharacterClass({body, kind, negate}, state, gen) {\n    function genClass() {\n      if (\n        state.parent.type === 'CharacterClass' &&\n        state.parent.kind === 'intersection' &&\n        kind === 'union' &&\n        !body.length\n      ) {\n        // Prevent empty intersection like `[&&]` from becoming the invalid `[[]&&[]]`\n        return '';\n      }\n      return `[${negate ? '^' : ''}${\n        body.map(gen).join(kind === 'intersection' ? '&&' : '')\n      }]`;\n    }\n    if (!state.inCharClass) {\n      // For the outermost char class, set state\n      state.inCharClass = true;\n      const result = genClass();\n      state.inCharClass = false;\n      return result;\n    }\n    return genClass();\n  },\n\n  CharacterClassRange({min, max}, _, gen) {\n    return `${gen(min)}-${gen(max)}`;\n  },\n\n  CharacterSet({kind, negate, value}, {inCharClass}) {\n    switch (kind) {\n      case 'any':\n        return r`\\O`;\n      case 'digit':\n        return negate ? r`\\D` : r`\\d`;\n      case 'dot':\n        return '.';\n      case 'hex':\n        return negate ? r`\\H` : r`\\h`;\n      case 'newline':\n        return negate ? r`\\N` : r`\\R`;\n      case 'posix':\n        return inCharClass ?\n          `[:${negate ? '^' : ''}${value}:]` :\n          `${negate ? r`\\P` : r`\\p`}{${value}}`;\n      case 'property':\n        return `${negate ? r`\\P` : r`\\p`}{${value}}`;\n      case 'space':\n        return negate ? r`\\S` : r`\\s`;\n      case 'text_segment':\n        return r`\\X`;\n      case 'word':\n        return negate ? r`\\W` : r`\\w`;\n      default:\n        throw new Error(`Unexpected character set kind \"${kind}\"`);\n    }\n  },\n\n  Directive({kind, flags}) {\n    if (kind === 'flags') {\n      const {enable = {}, disable = {}} = flags;\n      const enableStr = getFlagsStr(enable);\n      const disableStr = getFlagsStr(disable);\n      return (enableStr || disableStr) ? `(?${enableStr}${disableStr ? `-${disableStr}` : ''})` : '';\n    }\n    if (kind === 'keep') {\n      return r`\\K`;\n    }\n    throw new Error(`Unexpected directive kind \"${kind}\"`);\n  },\n\n  Flags(node) {\n    return getFlagsStr(node);\n  },\n\n  Group({atomic, body, flags}, _, gen) {\n    const contents = body.map(gen).join('|');\n    return `(?${getGroupPrefix(atomic, flags)}${contents})`;\n  },\n\n  LookaroundAssertion({body, kind, negate}, _, gen) {\n    const prefix = `${kind === 'lookahead' ? '' : '<'}${negate ? '!' : '='}`;\n    return `(?${prefix}${body.map(gen).join('|')})`;\n  },\n\n  NamedCallout({kind, tag, arguments: args}) {\n    if (kind === 'custom') {\n      // TODO: If supporting custom callout names in the future (with an added `name` property for\n      // `NamedCalloutNode`), will need to use `name` instead of `kind` if `kind` is `'custom'`\n      throw new Error(`Unexpected named callout kind \"${kind}\"`);\n    }\n    return `(*${kind.toUpperCase()}${tag ? `[${tag}]` : ''}${args ? `{${args.join(',')}}` : ''})`;\n  },\n\n  Quantifier(node, {parent}, gen) {\n    // Rendering Onig quantifiers is wildly, unnecessarily complex compared to other regex flavors\n    // because of the combination of a few features unique to Onig:\n    // - You can create quantifier chains (i.e., quantify a quantifier).\n    // - An implicit zero min is allowed for interval quantifiers (ex: `{,2}`).\n    // - Interval quantifiers can't use `+` to make them possessive (it creates a quantifier\n    //   chain), even though quantifiers `?` `*` `+` can.\n    // - A reversed range in a quantifier makes it possessive (ex: `{2,1}`).\n    //   - `{,n}` is always greedy with an implicit zero min, and can't represent a possesive range\n    //     from n to infinity.\n    const {body, kind, max, min} = node;\n    // These errors shouldn't happen unless the AST is modified in an invalid way after parsing\n    if (min === Infinity) {\n      throw new Error(`Invalid quantifier: infinite min`);\n    }\n    if (min > max) {\n      throw new Error(`Invalid quantifier: min \"${min}\" > max \"${max}\"`);\n    }\n    const kidIsGreedyQuantifier = (\n      body.type === 'Quantifier' &&\n      body.kind === 'greedy'\n    );\n    const parentIsPossessivePlus = (\n      parent.type === 'Quantifier' &&\n      parent.kind === 'possessive' &&\n      parent.min === 1 &&\n      parent.max === Infinity\n    );\n    // Can't render as a symbol, because the following (parent) quantifier, which is `++`, would\n    // then alter this node's meaning to make it possessive, and the parent quantifier can't change\n    // to avoid this because there's no interval representation possible for `++`. There's also no\n    // other way to render `*+`, but a following `*` wouldn't alter the meaning of this node. `?+`\n    // is also safe since it can use the alternative `{1,0}` representation (which is possessive)\n    const forcedInterval = kind === 'greedy' && parentIsPossessivePlus;\n    let base;\n    if (isSymbolQuantifierCandidate(node) && !forcedInterval) {\n      if (\n        !min && max === 1 &&\n        // Can't chain a base of `?` to any greedy quantifier since that would make it lazy\n        !kidIsGreedyQuantifier\n      ) {\n        base = '?';\n      } else if (!min && max === Infinity) {\n        base = '*';\n      } else if (\n        min === 1 && max === Infinity &&\n        ( // Can't chain a base of `+` to greedy `?`/`*`/`+` since that would make them possessive\n          !(kidIsGreedyQuantifier && isSymbolQuantifierCandidate(body)) ||\n          // ...but, we're forced to use `+` (and change the kid's rendering) if this is possessive\n          // `++` since you can't use a possessive reversed range with `Infinity`\n          kind === 'possessive'\n        )\n      ) {\n        base = '+';\n      }\n    }\n    const isIntervalQuantifier = !base;\n    if (isIntervalQuantifier) {\n      if (kind === 'possessive') {\n        if (min === max) {\n          // Can't add a `+` suffix to a fixed `{n}` interval quantifier\n          throw new Error(`Invalid possessive quantifier: min and max are equal \"${min}\"`);\n        }\n        if (max === Infinity) {\n          // Onig reversed ranges are possessive but `{,n}` is the same as greedy `{0,n}`, so\n          // there's no way to represent this without adding additional nodes that aren't in the\n          // AST. The exceptions are when `min` is 0 or 1 (`?+`, `*+`, `++`), but we've already\n          // ruled out rendering as a symbol at this point\n          throw new Error(`Invalid possessive quantifier: min \"${min}\" with infinite max\"`);\n        }\n        // Reversed range\n        base = `{${max},${min}}`;\n      } else if (min === max) {\n        base = `{${min}}`;\n      } else {\n        base = `{${min},${max === Infinity ? '' : max}}`;\n      }\n    }\n    const suffix = {\n      greedy: '',\n      lazy: '?',\n      // Interval quantifiers are marked possessive by reversing their min/max; a `+` suffix would\n      // create a quantifier chain\n      possessive: isIntervalQuantifier ? '' : '+',\n    }[kind];\n    return `${gen(body)}${base}${suffix}`;\n  },\n\n  Subroutine({ref}) {\n    if (typeof ref === 'string' && ref.includes('>')) {\n      return r`\\g'${ref}'`;\n    }\n    return r`\\g<${ref}>`;\n  },\n};\n\n// ---------------\n// --- Helpers ---\n// ---------------\n\nconst BaseEscapeChars = new Set([\n  '$', '(', ')', '*', '+', '.', '?', '[', '\\\\', '^', '{', '|',\n]);\n\nconst CharClassEscapeChars = new Set([\n  '&', '-', '[', '\\\\', ']', '^',\n]);\n\nconst CharCodeEscapeMap = new Map([\n  [ 7, r`\\a`], // bell\n  [ 9, r`\\t`], // horizontal tab\n  [10, r`\\n`], // line feed\n  [11, r`\\v`], // vertical tab\n  [12, r`\\f`], // form feed\n  [13, r`\\r`], // carriage return\n  [27, r`\\e`], // escape\n  [0x2028, r`\\u2028`], // line separator\n  [0x2029, r`\\u2029`], // paragraph separator\n  [0xFEFF, r`\\uFEFF`], // ZWNBSP/BOM\n]);\n\nfunction getFirstChild(node: Node) {\n  if ('body' in node) {\n    return Array.isArray(node.body) ? (node.body[0] ?? null) : node.body;\n  }\n  // Check for `type` to determine if it's a child node; quantifiers have a numeric `min`\n  if ('min' in node && node.min.type) {\n    return node.min;\n  }\n  return null;\n}\n\nfunction getLastChild(node: Node) {\n  if ('body' in node) {\n    return Array.isArray(node.body) ? (node.body.at(-1) ?? null) : node.body;\n  }\n  // Check for `type` to determine if it's a child node; quantifiers have a numeric `max`\n  if ('max' in node && node.max.type) {\n    return node.max;\n  }\n  return null;\n}\n\nfunction getFlagsStr({\n  ignoreCase,\n  dotAll,\n  extended,\n  digitIsAscii,\n  posixIsAscii,\n  spaceIsAscii,\n  wordIsAscii,\n  textSegmentMode,\n}: Partial<FlagProperties>): string {\n  return `${\n    ignoreCase ? 'i' : ''\n  }${\n    dotAll ? 'm' : ''\n  }${\n    extended ? 'x' : ''\n  }${\n    digitIsAscii ? 'D' : ''\n  }${\n    posixIsAscii ? 'P' : ''\n  }${\n    spaceIsAscii ? 'S' : ''\n  }${\n    wordIsAscii ? 'W' : ''\n  }${\n    textSegmentMode ? throwIfNullish({\n      grapheme: 'y{g}',\n      word: 'y{w}',\n    }[textSegmentMode], `Unexpected text segment mode \"${textSegmentMode}\"`) : ''\n  }`;\n}\n\nfunction getGroupPrefix(atomic: GroupNode['atomic'], flags?: GroupNode['flags']): string {\n  if (atomic) {\n    return '>';\n  }\n  let mods = '';\n  if (flags) {\n    const {enable = {}, disable = {}} = flags;\n    const enableStr = getFlagsStr(enable);\n    const disableStr = getFlagsStr(disable);\n    mods = `${enableStr}${disableStr ? `-${disableStr}` : ''}`;\n  }\n  return `${mods}:`;\n}\n\nfunction isDigitCharCode(value: number): boolean {\n  return value > 47 && value < 58;\n}\n\nfunction isSymbolQuantifierCandidate({min, max}: QuantifierNode): boolean {\n  return (\n    (!min && max === 1) || // `?`\n    (!min && max === Infinity) || // `*`\n    (min === 1 && max === Infinity) // `+`\n  );\n}\n\nexport {\n  type OnigurumaRegex,\n  generate,\n};\n"], "mappings": "aAEA,OAAQ,KAAAA,EAAG,kBAAAC,MAAqB,cAUhC,SAASC,EAASC,EAAmC,CACnD,MAAMC,EAAiC,CAAC,EACxC,IAAIC,EAAiBF,EACrB,MAAMG,EAAe,CACnB,YAAa,GACb,SAAAD,EACA,OAAQF,CACV,EACMI,EAAWC,GAAQ,CACvBF,EAAM,SAAWD,EACjBA,EAAWG,EACPC,EAAcH,EAAM,QAAQ,IAAME,IACpCF,EAAM,OAASA,EAAM,SACrBF,EAAY,KAAKE,EAAM,MAAM,GAI/B,MAAMI,EAFKT,EAAeU,EAAUH,EAAK,IAAI,EAAG,yBAAyBA,EAAK,IAAI,GAAG,EAEnEA,EAAMF,EAAOC,CAAG,EAClC,OAAIK,EAAaN,EAAM,MAAM,IAAME,IACjCJ,EAAY,IAAI,EAChBE,EAAM,OAASF,EAAY,GAAG,EAAE,GAAKD,GAEhCO,CACT,EACA,MAAO,CACL,QAASP,EAAI,KAAK,IAAII,CAAG,EAAE,KAAK,GAAG,EAEnC,MAAOA,EAAIJ,EAAI,KAAK,CACtB,CACF,CAiBA,MAAMQ,EAAuB,CAC3B,gBAAgB,CAAC,KAAAE,EAAM,KAAAC,CAAI,EAAGC,EAAGR,EAAK,CACpC,GAAIO,IAAS,WACX,MAAM,IAAI,MAAM,qCAAqCA,CAAI,GAAG,EAE9D,MAAO,MAAMD,EAAK,IAAIN,CAAG,EAAE,KAAK,GAAG,CAAC,GACtC,EAEA,YAAY,CAAC,KAAAM,CAAI,EAAGE,EAAGR,EAAK,CAC1B,OAAOM,EAAK,IAAIN,CAAG,EAAE,KAAK,EAAE,CAC9B,EAEA,UAAU,CAAC,KAAAO,EAAM,OAAAE,CAAM,EAAG,CACxB,OAAIF,IAAS,wBACJE,EAAShB,MAAQA,MAEtBc,IAAS,gBACJE,EAAShB,MAAQA,MAEnBC,EAAe,CACpB,SAAU,IACV,WAAY,IACZ,aAAcD,MACd,WAAYA,MACZ,mBAAoBA,MACpB,aAAcA,KAChB,EAAEc,CAAI,EAAG,8BAA8BA,CAAI,GAAG,CAChD,EAEA,cAAc,CAAC,IAAAG,CAAG,EAAG,CACnB,OAAI,OAAOA,GAAQ,SAEV,KAAOA,EAGT,OAAOA,CAAG,GACnB,EAEA,eAAe,CAAC,KAAAJ,EAAM,KAAAK,CAAI,EAAGH,EAAGR,EAAK,CAEnC,MAAO,IADcW,EAAO,IAAIA,EAAK,SAAS,GAAG,EAAI,IAAIA,CAAI,IAAM,IAAIA,CAAI,GAAG,GAAK,EAC5D,GAAGL,EAAK,IAAIN,CAAG,EAAE,KAAK,GAAG,CAAC,GACnD,EAEA,UAAUC,EAAM,CAAC,YAAAW,EAAa,SAAAd,EAAU,OAAAe,CAAM,EAAG,CAC/C,KAAM,CAAC,MAAAC,CAAK,EAAIb,EAChB,GAAIc,EAAkB,IAAID,CAAK,EAC7B,OAAOC,EAAkB,IAAID,CAAK,EAEpC,MAAME,EAAWlB,EAAS,OAAS,gBACnC,GAEEgB,EAAQ,IAAOA,EAAQ,KAAOA,EAAQ,KAEtCA,EAAQ,QAEPE,GAAYC,EAAgBH,CAAK,EAKlC,OAAOA,EAAQ,IACb,OAAOA,EAAM,SAAS,EAAE,EAAE,YAAY,CAAC,IACvC,MAAMA,EAAM,SAAS,EAAE,EAAE,YAAY,EAAE,SAAS,EAAG,GAAG,CAAC,GAE3D,MAAMI,EAAO,OAAO,cAAcJ,CAAK,EACvC,IAAIK,EAAS,GACb,GAAIP,EAAa,CACf,MAAMQ,EAAmBP,EAAO,OAAS,iBACnCQ,EAAUD,GAAoBP,EAAO,KAAK,CAAC,IAAMZ,EACjDqB,EAASF,GAAoBP,EAAO,KAAK,GAAG,EAAE,IAAMZ,EAEtDiB,IAAS,IACXC,EAASE,GAAW,CAACR,EAAO,OACnBK,IAAS,IAClBC,EAAS,CAACE,EACDH,IAAS,IAGlBC,EAAS,CAACE,GAAW,CAACC,EAEtBH,EAASI,EAAqB,IAAIL,CAAI,CAE1C,MACEC,EAASK,EAAgB,IAAIN,CAAI,EAEnC,MAAO,GAAGC,EAAS,KAAO,EAAE,GAAGD,CAAI,EACrC,EAEA,eAAe,CAAC,KAAAZ,EAAM,KAAAC,EAAM,OAAAE,CAAM,EAAGV,EAAOC,EAAK,CAC/C,SAASyB,GAAW,CAClB,OACE1B,EAAM,OAAO,OAAS,kBACtBA,EAAM,OAAO,OAAS,gBACtBQ,IAAS,SACT,CAACD,EAAK,OAGC,GAEF,IAAIG,EAAS,IAAM,EAAE,GAC1BH,EAAK,IAAIN,CAAG,EAAE,KAAKO,IAAS,eAAiB,KAAO,EAAE,CACxD,GACF,CACA,GAAI,CAACR,EAAM,YAAa,CAEtBA,EAAM,YAAc,GACpB,MAAMI,EAASsB,EAAS,EACxB,OAAA1B,EAAM,YAAc,GACbI,CACT,CACA,OAAOsB,EAAS,CAClB,EAEA,oBAAoB,CAAC,IAAAC,EAAK,IAAAC,CAAG,EAAGnB,EAAGR,EAAK,CACtC,MAAO,GAAGA,EAAI0B,CAAG,CAAC,IAAI1B,EAAI2B,CAAG,CAAC,EAChC,EAEA,aAAa,CAAC,KAAApB,EAAM,OAAAE,EAAQ,MAAAK,CAAK,EAAG,CAAC,YAAAF,CAAW,EAAG,CACjD,OAAQL,EAAM,CACZ,IAAK,MACH,OAAOd,MACT,IAAK,QACH,OAAOgB,EAAShB,MAAQA,MAC1B,IAAK,MACH,MAAO,IACT,IAAK,MACH,OAAOgB,EAAShB,MAAQA,MAC1B,IAAK,UACH,OAAOgB,EAAShB,MAAQA,MAC1B,IAAK,QACH,OAAOmB,EACL,KAAKH,EAAS,IAAM,EAAE,GAAGK,CAAK,KAC9B,GAAGL,EAAShB,MAAQA,KAAK,IAAIqB,CAAK,IACtC,IAAK,WACH,MAAO,GAAGL,EAAShB,MAAQA,KAAK,IAAIqB,CAAK,IAC3C,IAAK,QACH,OAAOL,EAAShB,MAAQA,MAC1B,IAAK,eACH,OAAOA,MACT,IAAK,OACH,OAAOgB,EAAShB,MAAQA,MAC1B,QACE,MAAM,IAAI,MAAM,kCAAkCc,CAAI,GAAG,CAC7D,CACF,EAEA,UAAU,CAAC,KAAAA,EAAM,MAAAqB,CAAK,EAAG,CACvB,GAAIrB,IAAS,QAAS,CACpB,KAAM,CAAC,OAAAsB,EAAS,CAAC,EAAG,QAAAC,EAAU,CAAC,CAAC,EAAIF,EAC9BG,EAAYC,EAAYH,CAAM,EAC9BI,EAAaD,EAAYF,CAAO,EACtC,OAAQC,GAAaE,EAAc,KAAKF,CAAS,GAAGE,EAAa,IAAIA,CAAU,GAAK,EAAE,IAAM,EAC9F,CACA,GAAI1B,IAAS,OACX,OAAOd,MAET,MAAM,IAAI,MAAM,8BAA8Bc,CAAI,GAAG,CACvD,EAEA,MAAMN,EAAM,CACV,OAAO+B,EAAY/B,CAAI,CACzB,EAEA,MAAM,CAAC,OAAAiC,EAAQ,KAAA5B,EAAM,MAAAsB,CAAK,EAAGpB,EAAGR,EAAK,CACnC,MAAMmC,EAAW7B,EAAK,IAAIN,CAAG,EAAE,KAAK,GAAG,EACvC,MAAO,KAAKoC,EAAeF,EAAQN,CAAK,CAAC,GAAGO,CAAQ,GACtD,EAEA,oBAAoB,CAAC,KAAA7B,EAAM,KAAAC,EAAM,OAAAE,CAAM,EAAGD,EAAGR,EAAK,CAEhD,MAAO,KADQ,GAAGO,IAAS,YAAc,GAAK,GAAG,GAAGE,EAAS,IAAM,GAAG,EACpD,GAAGH,EAAK,IAAIN,CAAG,EAAE,KAAK,GAAG,CAAC,GAC9C,EAEA,aAAa,CAAC,KAAAO,EAAM,IAAA8B,EAAK,UAAWC,CAAI,EAAG,CACzC,GAAI/B,IAAS,SAGX,MAAM,IAAI,MAAM,kCAAkCA,CAAI,GAAG,EAE3D,MAAO,KAAKA,EAAK,YAAY,CAAC,GAAG8B,EAAM,IAAIA,CAAG,IAAM,EAAE,GAAGC,EAAO,IAAIA,EAAK,KAAK,GAAG,CAAC,IAAM,EAAE,GAC5F,EAEA,WAAWrC,EAAM,CAAC,OAAAY,CAAM,EAAGb,EAAK,CAU9B,KAAM,CAAC,KAAAM,EAAM,KAAAC,EAAM,IAAAoB,EAAK,IAAAD,CAAG,EAAIzB,EAE/B,GAAIyB,IAAQ,IACV,MAAM,IAAI,MAAM,kCAAkC,EAEpD,GAAIA,EAAMC,EACR,MAAM,IAAI,MAAM,4BAA4BD,CAAG,YAAYC,CAAG,GAAG,EAEnE,MAAMY,EACJjC,EAAK,OAAS,cACdA,EAAK,OAAS,SAEVkC,EACJ3B,EAAO,OAAS,cAChBA,EAAO,OAAS,cAChBA,EAAO,MAAQ,GACfA,EAAO,MAAQ,IAOX4B,EAAiBlC,IAAS,UAAYiC,EAC5C,IAAIE,EACAC,EAA4B1C,CAAI,GAAK,CAACwC,IAEtC,CAACf,GAAOC,IAAQ,GAEhB,CAACY,EAEDG,EAAO,IACE,CAAChB,GAAOC,IAAQ,IACzBe,EAAO,IAEPhB,IAAQ,GAAKC,IAAQ,MAEnB,EAAEY,GAAyBI,EAA4BrC,CAAI,IAG3DC,IAAS,gBAGXmC,EAAO,MAGX,MAAME,EAAuB,CAACF,EAC9B,GAAIE,EACF,GAAIrC,IAAS,aAAc,CACzB,GAAImB,IAAQC,EAEV,MAAM,IAAI,MAAM,yDAAyDD,CAAG,GAAG,EAEjF,GAAIC,IAAQ,IAKV,MAAM,IAAI,MAAM,uCAAuCD,CAAG,sBAAsB,EAGlFgB,EAAO,IAAIf,CAAG,IAAID,CAAG,GACvB,MAAWA,IAAQC,EACjBe,EAAO,IAAIhB,CAAG,IAEdgB,EAAO,IAAIhB,CAAG,IAAIC,IAAQ,IAAW,GAAKA,CAAG,IAGjD,MAAMkB,EAAS,CACb,OAAQ,GACR,KAAM,IAGN,WAAYD,EAAuB,GAAK,GAC1C,EAAErC,CAAI,EACN,MAAO,GAAGP,EAAIM,CAAI,CAAC,GAAGoC,CAAI,GAAGG,CAAM,EACrC,EAEA,WAAW,CAAC,IAAAnC,CAAG,EAAG,CAChB,OAAI,OAAOA,GAAQ,UAAYA,EAAI,SAAS,GAAG,EACtCjB,OAAOiB,CAAG,IAEZjB,OAAOiB,CAAG,GACnB,CACF,EAMMc,EAAkB,IAAI,IAAI,CAC9B,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,IAAK,IAAK,GAC1D,CAAC,EAEKD,EAAuB,IAAI,IAAI,CACnC,IAAK,IAAK,IAAK,KAAM,IAAK,GAC5B,CAAC,EAEKR,EAAoB,IAAI,IAAI,CAChC,CAAE,EAAGtB,KAAK,EACV,CAAE,EAAGA,KAAK,EACV,CAAC,GAAIA,KAAK,EACV,CAAC,GAAIA,KAAK,EACV,CAAC,GAAIA,KAAK,EACV,CAAC,GAAIA,KAAK,EACV,CAAC,GAAIA,KAAK,EACV,CAAC,KAAQA,SAAS,EAClB,CAAC,KAAQA,SAAS,EAClB,CAAC,MAAQA,SAAS,CACpB,CAAC,EAED,SAASS,EAAcD,EAAY,CACjC,MAAI,SAAUA,EACL,MAAM,QAAQA,EAAK,IAAI,EAAKA,EAAK,KAAK,CAAC,GAAK,KAAQA,EAAK,KAG9D,QAASA,GAAQA,EAAK,IAAI,KACrBA,EAAK,IAEP,IACT,CAEA,SAASI,EAAaJ,EAAY,CAChC,MAAI,SAAUA,EACL,MAAM,QAAQA,EAAK,IAAI,EAAKA,EAAK,KAAK,GAAG,EAAE,GAAK,KAAQA,EAAK,KAGlE,QAASA,GAAQA,EAAK,IAAI,KACrBA,EAAK,IAEP,IACT,CAEA,SAAS+B,EAAY,CACnB,WAAAc,EACA,OAAAC,EACA,SAAAC,EACA,aAAAC,EACA,aAAAC,EACA,aAAAC,EACA,YAAAC,EACA,gBAAAC,CACF,EAAoC,CAClC,MAAO,GACLP,EAAa,IAAM,EACrB,GACEC,EAAS,IAAM,EACjB,GACEC,EAAW,IAAM,EACnB,GACEC,EAAe,IAAM,EACvB,GACEC,EAAe,IAAM,EACvB,GACEC,EAAe,IAAM,EACvB,GACEC,EAAc,IAAM,EACtB,GACEC,EAAkB3D,EAAe,CAC/B,SAAU,OACV,KAAM,MACR,EAAE2D,CAAe,EAAG,iCAAiCA,CAAe,GAAG,EAAI,EAC7E,EACF,CAEA,SAASjB,EAAeF,EAA6BN,EAAoC,CACvF,GAAIM,EACF,MAAO,IAET,IAAIoB,EAAO,GACX,GAAI1B,EAAO,CACT,KAAM,CAAC,OAAAC,EAAS,CAAC,EAAG,QAAAC,EAAU,CAAC,CAAC,EAAIF,EAC9BG,EAAYC,EAAYH,CAAM,EAC9BI,EAAaD,EAAYF,CAAO,EACtCwB,EAAO,GAAGvB,CAAS,GAAGE,EAAa,IAAIA,CAAU,GAAK,EAAE,EAC1D,CACA,MAAO,GAAGqB,CAAI,GAChB,CAEA,SAASrC,EAAgBH,EAAwB,CAC/C,OAAOA,EAAQ,IAAMA,EAAQ,EAC/B,CAEA,SAAS6B,EAA4B,CAAC,IAAAjB,EAAK,IAAAC,CAAG,EAA4B,CACxE,MACG,CAACD,GAAOC,IAAQ,GAChB,CAACD,GAAOC,IAAQ,KAChBD,IAAQ,GAAKC,IAAQ,GAE1B,CAEA,OAEEhC,KAAA", "names": ["r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generate", "ast", "parentStack", "lastNode", "state", "gen", "node", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "generator", "getLastChild", "body", "kind", "_", "negate", "ref", "name", "inCharClass", "parent", "value", "CharCodeEscapeMap", "escDigit", "isDigitCharCode", "char", "escape", "isDirectClassKid", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "CharClassEscapeChars", "BaseEscapeChars", "genClass", "min", "max", "flags", "enable", "disable", "enableStr", "getFlagsStr", "disableStr", "atomic", "contents", "getGroupPrefix", "tag", "args", "kidIsGreedyQuantifier", "parentIsPossessivePlus", "forcedInterval", "base", "isSymbolQuantifierCandidate", "isIntervalQuantifier", "suffix", "ignoreCase", "dotAll", "extended", "digitIsAscii", "posixIsAscii", "spaceIsAscii", "wordIsAscii", "textSegmentMode", "mods"]}