{"version": 3, "sources": ["../../src/server/base-server.ts"], "sourcesContent": ["import type { __ApiPreviewProps } from './api-utils'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { Params } from './request/params'\nimport type { NextConfig, NextConfigComplete } from './config-shared'\nimport type {\n  NextParsedUrlQuery,\n  NextUrlWithParsedQuery,\n  RequestMeta,\n} from './request-meta'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RenderOptsPartial as PagesRenderOptsPartial } from './render'\nimport type {\n  RenderOptsPartial as AppRenderOptsPartial,\n  ServerOnInstrumentationRequestError,\n} from './app-render/types'\nimport type {\n  ServerComponentsHmrCache,\n  ResponseCacheBase,\n} from './response-cache'\nimport type { UrlWithParsedQuery } from 'url'\nimport {\n  NormalizeError,\n  DecodeError,\n  normalizeRepeatedSlashes,\n  MissingStaticPage,\n} from '../shared/lib/utils'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport type {\n  ManifestRewriteRoute,\n  ManifestRoute,\n  PrerenderManifest,\n} from '../build'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type {\n  Server as HTTPServer,\n  IncomingMessage,\n  ServerResponse as HTTPServerResponse,\n} from 'http'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport type { TLSSocket } from 'tls'\nimport type { PathnameNormalizer } from './normalizers/request/pathname-normalizer'\nimport type { InstrumentationModule } from './instrumentation/types'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { formatHostname } from './lib/format-hostname'\nimport {\n  APP_PATHS_MANIFEST,\n  NEXT_BUILTIN_DOCUMENT,\n  PAGES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport { execOnce } from '../shared/lib/utils'\nimport { isBlockedPage } from './utils'\nimport { getBotType, isBot } from '../shared/lib/router/utils/is-bot'\nimport RenderResult from './render-result'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport * as Log from '../build/output/log'\nimport { getPreviouslyRevalidatedTags, getServerUtils } from './server-utils'\nimport isError, { getProperError } from '../lib/is-error'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  removeRequestMeta,\n  setRequestMeta,\n} from './request-meta'\nimport { removePathPrefix } from '../shared/lib/router/utils/remove-path-prefix'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { getHostname } from '../shared/lib/get-hostname'\nimport { parseUrl as parseUrlUtil } from '../shared/lib/router/utils/parse-url'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport {\n  RSC_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_URL,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n} from '../client/components/app-router-headers'\nimport type {\n  MatchOptions,\n  RouteMatcherManager,\n} from './route-matcher-managers/route-matcher-manager'\nimport { LocaleRouteNormalizer } from './normalizers/locale-route-normalizer'\nimport { DefaultRouteMatcherManager } from './route-matcher-managers/default-route-matcher-manager'\nimport { AppPageRouteMatcherProvider } from './route-matcher-providers/app-page-route-matcher-provider'\nimport { AppRouteRouteMatcherProvider } from './route-matcher-providers/app-route-route-matcher-provider'\nimport { PagesAPIRouteMatcherProvider } from './route-matcher-providers/pages-api-route-matcher-provider'\nimport { PagesRouteMatcherProvider } from './route-matcher-providers/pages-route-matcher-provider'\nimport { ServerManifestLoader } from './route-matcher-providers/helpers/manifest-loaders/server-manifest-loader'\nimport {\n  getTracer,\n  isBubbledError,\n  SpanKind,\n  SpanStatusCode,\n} from './lib/trace/tracer'\nimport { BaseServerSpan } from './lib/trace/constants'\nimport { I18NProvider } from './lib/i18n-provider'\nimport { sendResponse } from './send-response'\nimport { normalizeNextQueryParam } from './web/utils'\nimport {\n  HTML_CONTENT_TYPE_HEADER,\n  JSON_CONTENT_TYPE_HEADER,\n  MATCHED_PATH_HEADER,\n  NEXT_RESUME_HEADER,\n} from '../lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { matchNextDataPathname } from './lib/match-next-data-pathname'\nimport getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\nimport { RSCPathnameNormalizer } from './normalizers/request/rsc'\nimport { stripFlightHeaders } from './app-render/strip-flight-headers'\nimport {\n  isAppPageRouteModule,\n  isAppRouteRouteModule,\n} from './route-modules/checks'\nimport { PrefetchRSCPathnameNormalizer } from './normalizers/request/prefetch-rsc'\nimport { NextDataPathnameNormalizer } from './normalizers/request/next-data'\nimport { getIsPossibleServerAction } from './lib/server-action-request-meta'\nimport { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes'\nimport { toRoute } from './lib/to-route'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isNodeNextRequest, isNodeNextResponse } from './base-http/helpers'\nimport { patchSetHeaderWithCookieSupport } from './lib/patch-set-header'\nimport { checkIsAppPPREnabled } from './lib/experimental/ppr'\nimport {\n  getBuiltinRequestContext,\n  type WaitUntil,\n} from './after/builtin-request-context'\nimport { NextRequestHint } from './web/adapter'\nimport type { RouteModule } from './route-modules/route-module'\nimport { type FallbackMode, parseFallbackField } from '../lib/fallback'\nimport { SegmentPrefixRSCPathnameNormalizer } from './normalizers/request/segment-prefix-rsc'\nimport { shouldServeStreamingMetadata } from './lib/streaming-metadata'\nimport { decodeQueryPathParameter } from './lib/decode-query-path-parameter'\nimport { NoFallbackError } from '../shared/lib/no-fallback-error.external'\nimport { getCacheHandlers } from './use-cache/handlers'\nimport { fixMojibake } from './lib/fix-mojibake'\nimport { computeCacheBustingSearchParam } from '../shared/lib/router/utils/cache-busting-search-param'\nimport { setCacheBustingSearchParamWithHash } from '../client/components/router-reducer/set-cache-busting-search-param'\nimport type { CacheControl } from './lib/cache-control'\nimport type { PrerenderedRoute } from '../build/static-paths/types'\n\nexport type FindComponentsResult = {\n  components: LoadComponentsReturnType\n  query: NextParsedUrlQuery\n}\n\nexport interface MiddlewareRoutingItem {\n  page: string\n  match: MiddlewareRouteMatch\n  matchers?: MiddlewareMatcher[]\n}\n\nexport type RouteHandler<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = (\n  req: ServerRequest,\n  res: ServerResponse,\n  parsedUrl: NextUrlWithParsedQuery\n) => PromiseLike<boolean> | boolean\n\n/**\n * The normalized route manifest is the same as the route manifest, but with\n * the rewrites normalized to the object shape that the router expects.\n */\nexport type NormalizedRouteManifest = {\n  readonly dynamicRoutes: ReadonlyArray<ManifestRoute>\n  readonly rewrites: {\n    readonly beforeFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly afterFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly fallback: ReadonlyArray<ManifestRewriteRoute>\n  }\n}\n\nexport interface Options {\n  /**\n   * Object containing the configuration next.config.js\n   */\n  conf: NextConfig\n  /**\n   * Set to false when the server was created by Next.js\n   */\n  customServer?: boolean\n  /**\n   * Tells if Next.js is running in dev mode\n   */\n  dev?: boolean\n  /**\n   * Enables the experimental testing mode.\n   */\n  experimentalTestProxy?: boolean\n\n  /**\n   * Whether or not the dev server is running in experimental HTTPS mode\n   */\n  experimentalHttpsServer?: boolean\n  /**\n   * Where the Next project is located\n   */\n  dir?: string\n  /**\n   * Tells if Next.js is at the platform-level\n   */\n  minimalMode?: boolean\n  /**\n   * Hide error messages containing server information\n   */\n  quiet?: boolean\n  /**\n   * The hostname the server is running behind\n   */\n  hostname?: string\n  /**\n   * The port the server is running behind\n   */\n  port?: number\n  /**\n   * The HTTP Server that Next.js is running behind\n   */\n  httpServer?: HTTPServer\n}\n\nexport type RenderOpts = PagesRenderOptsPartial & AppRenderOptsPartial\n\nexport type LoadedRenderOpts = RenderOpts &\n  LoadComponentsReturnType &\n  RequestLifecycleOpts\n\nexport type RequestLifecycleOpts = {\n  waitUntil: ((promise: Promise<any>) => void) | undefined\n  onClose: (callback: () => void) => void\n  onAfterTaskError: ((error: unknown) => void) | undefined\n}\n\ntype BaseRenderOpts = RenderOpts & {\n  poweredByHeader: boolean\n  generateEtags: boolean\n  previewProps: __ApiPreviewProps\n}\n\n/**\n * The public interface for rendering with the server programmatically. This\n * would typically only allow the base request or response to extend it, but\n * because this can be programmatically accessed, we assume that it could also\n * be the base Node.js request and response types.\n */\nexport interface BaseRequestHandler<\n  ServerRequest extends BaseNextRequest | IncomingMessage = BaseNextRequest,\n  ServerResponse extends\n    | BaseNextResponse\n    | HTTPServerResponse = BaseNextResponse,\n> {\n  (\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery | undefined\n  ): Promise<void> | void\n}\n\nexport type RequestContext<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = {\n  req: ServerRequest\n  res: ServerResponse\n  pathname: string\n  query: NextParsedUrlQuery\n  renderOpts: RenderOpts\n}\n\n// Internal wrapper around build errors at development\n// time, to prevent us from propagating or logging them\nexport class WrappedBuildError extends Error {\n  innerError: Error\n\n  constructor(innerError: Error) {\n    super()\n    this.innerError = innerError\n  }\n}\n\ntype ResponsePayload = {\n  body: RenderResult\n  cacheControl?: CacheControl\n}\n\nexport type NextEnabledDirectories = {\n  readonly pages: boolean\n  readonly app: boolean\n}\n\nexport default abstract class Server<\n  ServerOptions extends Options = Options,\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> {\n  public readonly hostname?: string\n  public readonly fetchHostname?: string\n  public readonly port?: number\n  protected readonly dir: string\n  protected readonly quiet: boolean\n  protected readonly nextConfig: NextConfigComplete\n  protected readonly distDir: string\n  protected readonly publicDir: string\n  protected readonly hasStaticDir: boolean\n  protected readonly pagesManifest?: PagesManifest\n  protected readonly appPathsManifest?: PagesManifest\n  protected readonly buildId: string\n  protected readonly minimalMode: boolean\n  protected readonly renderOpts: BaseRenderOpts\n  protected readonly serverOptions: Readonly<ServerOptions>\n  protected readonly appPathRoutes?: Record<string, string[]>\n  protected readonly clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  protected interceptionRoutePatterns: RegExp[]\n  protected nextFontManifest?: DeepReadonly<NextFontManifest>\n  protected instrumentation: InstrumentationModule | undefined\n  private readonly responseCache: ResponseCacheBase\n\n  protected abstract getPublicDir(): string\n  protected abstract getHasStaticDir(): boolean\n  protected abstract getPagesManifest(): PagesManifest | undefined\n  protected abstract getAppPathsManifest(): PagesManifest | undefined\n  protected abstract getBuildId(): string\n  protected abstract getinterceptionRoutePatterns(): RegExp[]\n\n  protected readonly enabledDirectories: NextEnabledDirectories\n  protected abstract getEnabledDirectories(dev: boolean): NextEnabledDirectories\n\n  protected readonly experimentalTestProxy?: boolean\n\n  protected abstract findPageComponents(params: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure?: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null>\n  protected abstract getPrerenderManifest(): DeepReadonly<PrerenderManifest>\n  protected abstract getNextFontManifest():\n    | DeepReadonly<NextFontManifest>\n    | undefined\n  protected abstract attachRequestMeta(\n    req: ServerRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ): void\n  protected abstract hasPage(pathname: string): Promise<boolean>\n\n  protected abstract sendRenderResult(\n    req: ServerRequest,\n    res: ServerResponse,\n    options: {\n      result: RenderResult\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void>\n\n  protected abstract runApi(\n    req: ServerRequest,\n    res: ServerResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean>\n\n  protected abstract renderHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult>\n\n  protected abstract getIncrementalCache(options: {\n    requestHeaders: Record<string, undefined | string | string[]>\n  }): Promise<import('./lib/incremental-cache').IncrementalCache>\n\n  protected abstract getResponseCache(options: {\n    dev: boolean\n  }): ResponseCacheBase\n\n  protected getServerComponentsHmrCache():\n    | ServerComponentsHmrCache\n    | undefined {\n    return this.nextConfig.experimental.serverComponentsHmrCache\n      ? (globalThis as any).__serverComponentsHmrCache\n      : undefined\n  }\n\n  protected abstract loadEnvConfig(params: {\n    dev: boolean\n    forceReload?: boolean\n  }): void\n\n  // TODO-APP: (wyattjoh): Make protected again. Used for turbopack in route-resolver.ts right now.\n  public readonly matchers: RouteMatcherManager\n  protected readonly i18nProvider?: I18NProvider\n  protected readonly localeNormalizer?: LocaleRouteNormalizer\n\n  protected readonly normalizers: {\n    readonly rsc: RSCPathnameNormalizer | undefined\n    readonly prefetchRSC: PrefetchRSCPathnameNormalizer | undefined\n    readonly segmentPrefetchRSC: SegmentPrefixRSCPathnameNormalizer | undefined\n    readonly data: NextDataPathnameNormalizer | undefined\n  }\n\n  private readonly isAppPPREnabled: boolean\n  private readonly isAppSegmentPrefetchEnabled: boolean\n\n  /**\n   * This is used to persist cache scopes across\n   * prefetch -> full route requests for cache components\n   * it's only fully used in dev\n   */\n\n  public constructor(options: ServerOptions) {\n    const {\n      dir = '.',\n      quiet = false,\n      conf,\n      dev = false,\n      minimalMode = false,\n      hostname,\n      port,\n      experimentalTestProxy,\n    } = options\n\n    this.experimentalTestProxy = experimentalTestProxy\n    this.serverOptions = options\n\n    this.dir = (require('path') as typeof import('path')).resolve(dir)\n\n    this.quiet = quiet\n    this.loadEnvConfig({ dev })\n\n    // TODO: should conf be normalized to prevent missing\n    // values from causing issues as this can be user provided\n    this.nextConfig = conf as NextConfigComplete\n    this.hostname = hostname\n    if (this.hostname) {\n      // we format the hostname so that it can be fetched\n      this.fetchHostname = formatHostname(this.hostname)\n    }\n    this.port = port\n    this.distDir = (require('path') as typeof import('path')).join(\n      this.dir,\n      this.nextConfig.distDir\n    )\n    this.publicDir = this.getPublicDir()\n    this.hasStaticDir = !minimalMode && this.getHasStaticDir()\n\n    this.i18nProvider = this.nextConfig.i18n?.locales\n      ? new I18NProvider(this.nextConfig.i18n)\n      : undefined\n\n    // Configure the locale normalizer, it's used for routes inside `pages/`.\n    this.localeNormalizer = this.i18nProvider\n      ? new LocaleRouteNormalizer(this.i18nProvider)\n      : undefined\n\n    // Only serverRuntimeConfig needs the default\n    // publicRuntimeConfig gets it's default in client/index.js\n    const {\n      serverRuntimeConfig = {},\n      publicRuntimeConfig,\n      assetPrefix,\n      generateEtags,\n    } = this.nextConfig\n\n    this.buildId = this.getBuildId()\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] =\n      minimalMode || !!process.env.NEXT_PRIVATE_MINIMAL_MODE\n\n    this.enabledDirectories = this.getEnabledDirectories(dev)\n\n    this.isAppPPREnabled =\n      this.enabledDirectories.app &&\n      checkIsAppPPREnabled(this.nextConfig.experimental.ppr)\n\n    this.isAppSegmentPrefetchEnabled =\n      this.enabledDirectories.app &&\n      this.nextConfig.experimental.clientSegmentCache === true\n\n    this.normalizers = {\n      // We should normalize the pathname from the RSC prefix only in minimal\n      // mode as otherwise that route is not exposed external to the server as\n      // we instead only rely on the headers.\n      rsc:\n        this.enabledDirectories.app && this.minimalMode\n          ? new RSCPathnameNormalizer()\n          : undefined,\n      prefetchRSC:\n        this.isAppPPREnabled && this.minimalMode\n          ? new PrefetchRSCPathnameNormalizer()\n          : undefined,\n      segmentPrefetchRSC:\n        this.isAppSegmentPrefetchEnabled && this.minimalMode\n          ? new SegmentPrefixRSCPathnameNormalizer()\n          : undefined,\n      data: this.enabledDirectories.pages\n        ? new NextDataPathnameNormalizer(this.buildId)\n        : undefined,\n    }\n\n    this.nextFontManifest = this.getNextFontManifest()\n    process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n\n    this.renderOpts = {\n      dir: this.dir,\n      supportsDynamicResponse: true,\n      trailingSlash: this.nextConfig.trailingSlash,\n      deploymentId: this.nextConfig.deploymentId,\n      poweredByHeader: this.nextConfig.poweredByHeader,\n      canonicalBase: this.nextConfig.amp.canonicalBase || '',\n      generateEtags,\n      previewProps: this.getPrerenderManifest().preview,\n      ampOptimizerConfig: this.nextConfig.experimental.amp?.optimizer,\n      basePath: this.nextConfig.basePath,\n      images: this.nextConfig.images,\n      optimizeCss: this.nextConfig.experimental.optimizeCss,\n      nextConfigOutput: this.nextConfig.output,\n      nextScriptWorkers: this.nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading:\n        this.nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: this.nextConfig.i18n?.domains,\n      distDir: this.distDir,\n      serverComponents: this.enabledDirectories.app,\n      cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n      enableTainting: this.nextConfig.experimental.taint,\n      crossOrigin: this.nextConfig.crossOrigin\n        ? this.nextConfig.crossOrigin\n        : undefined,\n      largePageDataBytes: this.nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(publicRuntimeConfig).length > 0\n          ? publicRuntimeConfig\n          : undefined,\n\n      isExperimentalCompile: this.nextConfig.experimental.isExperimentalCompile,\n      // `htmlLimitedBots` is passed to server as serialized config in string format\n      htmlLimitedBots: this.nextConfig.htmlLimitedBots,\n      experimental: {\n        expireTime: this.nextConfig.expireTime,\n        staleTimes: this.nextConfig.experimental.staleTimes,\n        clientTraceMetadata: this.nextConfig.experimental.clientTraceMetadata,\n        cacheComponents: this.nextConfig.experimental.cacheComponents ?? false,\n        clientSegmentCache:\n          this.nextConfig.experimental.clientSegmentCache === 'client-only'\n            ? 'client-only'\n            : Boolean(this.nextConfig.experimental.clientSegmentCache),\n        clientParamParsing:\n          this.nextConfig.experimental.clientParamParsing ?? false,\n        dynamicOnHover: this.nextConfig.experimental.dynamicOnHover ?? false,\n        inlineCss: this.nextConfig.experimental.inlineCss ?? false,\n        authInterrupts: !!this.nextConfig.experimental.authInterrupts,\n      },\n      onInstrumentationRequestError:\n        this.instrumentationOnRequestError.bind(this),\n      reactMaxHeadersLength: this.nextConfig.reactMaxHeadersLength,\n      devtoolSegmentExplorer:\n        this.nextConfig.experimental.devtoolSegmentExplorer,\n    }\n\n    // Initialize next/config with the environment configuration\n    setConfig({\n      serverRuntimeConfig,\n      publicRuntimeConfig,\n    })\n\n    this.pagesManifest = this.getPagesManifest()\n    this.appPathsManifest = this.getAppPathsManifest()\n    this.appPathRoutes = this.getAppPathRoutes()\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // Configure the routes.\n    this.matchers = this.getRouteMatchers()\n\n    // Start route compilation. We don't wait for the routes to finish loading\n    // because we use the `waitTillReady` promise below in `handleRequest` to\n    // wait. Also we can't `await` in the constructor.\n    void this.matchers.reload()\n\n    this.setAssetPrefix(assetPrefix)\n    this.responseCache = this.getResponseCache({ dev })\n  }\n\n  private handleRSCRequest: RouteHandler<ServerRequest, ServerResponse> = (\n    req,\n    _res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname) return false\n\n    if (this.normalizers.segmentPrefetchRSC?.match(parsedUrl.pathname)) {\n      const result = this.normalizers.segmentPrefetchRSC.extract(\n        parsedUrl.pathname\n      )\n      if (!result) return false\n\n      const { originalPathname, segmentPath } = result\n      parsedUrl.pathname = originalPathname\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n      req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] = segmentPath\n\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n      addRequestMeta(req, 'segmentPrefetchRSCRequest', segmentPath)\n    } else if (this.normalizers.prefetchRSC?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.prefetchRSC.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n    } else if (this.normalizers.rsc?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.rsc.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a RSC request.\n      req.headers[RSC_HEADER] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n    } else if (req.headers['x-now-route-matches']) {\n      // If we didn't match, return with the flight headers stripped. If in\n      // minimal mode we didn't match based on the path, this can't be a RSC\n      // request. This is because Vercel only sends this header during\n      // revalidation requests and we want the cache to instead depend on the\n      // request path for flight information.\n      stripFlightHeaders(req.headers)\n\n      return false\n    } else if (req.headers[RSC_HEADER] === '1') {\n      addRequestMeta(req, 'isRSCRequest', true)\n\n      if (req.headers[NEXT_ROUTER_PREFETCH_HEADER] === '1') {\n        addRequestMeta(req, 'isPrefetchRSCRequest', true)\n\n        const segmentPrefetchRSCRequest =\n          req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]\n        if (typeof segmentPrefetchRSCRequest === 'string') {\n          addRequestMeta(\n            req,\n            'segmentPrefetchRSCRequest',\n            segmentPrefetchRSCRequest\n          )\n        }\n      }\n    } else {\n      // Otherwise just return without doing anything.\n      return false\n    }\n\n    if (req.url) {\n      const parsed = parseUrl(req.url)\n      parsed.pathname = parsedUrl.pathname\n      req.url = formatUrl(parsed)\n    }\n\n    return false\n  }\n\n  private handleNextDataRequest: RouteHandler<ServerRequest, ServerResponse> =\n    async (req, res, parsedUrl) => {\n      const middleware = await this.getMiddleware()\n      const params = matchNextDataPathname(parsedUrl.pathname)\n\n      // ignore for non-next data URLs\n      if (!params || !params.path) {\n        return false\n      }\n\n      if (params.path[0] !== this.buildId) {\n        // Ignore if its a middleware request when we aren't on edge.\n        if (getRequestMeta(req, 'middlewareInvoke')) {\n          return false\n        }\n\n        // Make sure to 404 if the buildId isn't correct\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // remove buildId from URL\n      params.path.shift()\n\n      const lastParam = params.path[params.path.length - 1]\n\n      // show 404 if it doesn't end with .json\n      if (typeof lastParam !== 'string' || !lastParam.endsWith('.json')) {\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // re-create page's pathname\n      let pathname = `/${params.path.join('/')}`\n      pathname = getRouteFromAssetPath(pathname, '.json')\n\n      // ensure trailing slash is normalized per config\n      if (middleware) {\n        if (this.nextConfig.trailingSlash && !pathname.endsWith('/')) {\n          pathname += '/'\n        }\n        if (\n          !this.nextConfig.trailingSlash &&\n          pathname.length > 1 &&\n          pathname.endsWith('/')\n        ) {\n          pathname = pathname.substring(0, pathname.length - 1)\n        }\n      }\n\n      if (this.i18nProvider) {\n        // Remove the port from the hostname if present.\n        const hostname = req?.headers.host?.split(':', 1)[0].toLowerCase()\n\n        const domainLocale = this.i18nProvider.detectDomainLocale(hostname)\n        const defaultLocale =\n          domainLocale?.defaultLocale ?? this.i18nProvider.config.defaultLocale\n\n        const localePathResult = this.i18nProvider.analyze(pathname)\n\n        // If the locale is detected from the path, we need to remove it\n        // from the pathname.\n        if (localePathResult.detectedLocale) {\n          pathname = localePathResult.pathname\n        }\n\n        // Update the query with the detected locale and default locale.\n        addRequestMeta(req, 'locale', localePathResult.detectedLocale)\n        addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n        // If the locale is not detected from the path, we need to mark that\n        // it was not inferred from default.\n        if (!localePathResult.detectedLocale) {\n          removeRequestMeta(req, 'localeInferredFromDefault')\n        }\n\n        // If no locale was detected and we don't have middleware, we need\n        // to render a 404 page.\n        if (!localePathResult.detectedLocale && !middleware) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n      }\n\n      parsedUrl.pathname = pathname\n      addRequestMeta(req, 'isNextDataReq', true)\n\n      return false\n    }\n\n  protected handleNextImageRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallRenderRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallMiddlewareRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    // Create a new manifest loader that get's the manifests from the server.\n    const manifestLoader = new ServerManifestLoader((name) => {\n      switch (name) {\n        case PAGES_MANIFEST:\n          return this.getPagesManifest() ?? null\n        case APP_PATHS_MANIFEST:\n          return this.getAppPathsManifest() ?? null\n        default:\n          return null\n      }\n    })\n\n    // Configure the matchers and handlers.\n    const matchers: RouteMatcherManager = new DefaultRouteMatcherManager()\n\n    // Match pages under `pages/`.\n    matchers.push(\n      new PagesRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // Match api routes under `pages/api/`.\n    matchers.push(\n      new PagesAPIRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // If the app directory is enabled, then add the app matchers and handlers.\n    if (this.enabledDirectories.app) {\n      // Match app pages under `app/`.\n      matchers.push(\n        new AppPageRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n      matchers.push(\n        new AppRouteRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    const [err, req, ctx] = args\n\n    if (this.instrumentation) {\n      try {\n        await this.instrumentation.onRequestError?.(\n          err,\n          {\n            path: req.url || '',\n            method: req.method || 'GET',\n            // Normalize middleware headers and other server request headers\n            headers:\n              req instanceof NextRequestHint\n                ? Object.fromEntries(req.headers.entries())\n                : req.headers,\n          },\n          ctx\n        )\n      } catch (handlerErr) {\n        // Log the soft error and continue, since errors can thrown from react stream handler\n        console.error('Error in instrumentation.onRequestError:', handlerErr)\n      }\n    }\n  }\n\n  public logError(err: Error): void {\n    if (this.quiet) return\n    Log.error(err)\n  }\n\n  public async handleRequest(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    await this.prepare()\n    const method = req.method.toUpperCase()\n\n    const tracer = getTracer()\n    return tracer.withPropagatedContext(req.headers, () => {\n      return tracer.trace(\n        BaseServerSpan.handleRequest,\n        {\n          spanName: `${method} ${req.url}`,\n          kind: SpanKind.SERVER,\n          attributes: {\n            'http.method': method,\n            'http.target': req.url,\n          },\n        },\n        async (span) =>\n          this.handleRequestImpl(req, res, parsedUrl).finally(() => {\n            if (!span) return\n\n            const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n            span.setAttributes({\n              'http.status_code': res.statusCode,\n              'next.rsc': isRSCRequest,\n            })\n\n            if (res.statusCode && res.statusCode >= 500) {\n              // For 5xx status codes: SHOULD be set to 'Error' span status.\n              // x-ref: https://opentelemetry.io/docs/specs/semconv/http/http-spans/#status\n              span.setStatus({\n                code: SpanStatusCode.ERROR,\n              })\n              // For span status 'Error', SHOULD set 'error.type' attribute.\n              span.setAttribute('error.type', res.statusCode.toString())\n            }\n\n            const rootSpanAttributes = tracer.getRootSpanAttributes()\n            // We were unable to get attributes, probably OTEL is not enabled\n            if (!rootSpanAttributes) return\n\n            if (\n              rootSpanAttributes.get('next.span_type') !==\n              BaseServerSpan.handleRequest\n            ) {\n              console.warn(\n                `Unexpected root span type '${rootSpanAttributes.get(\n                  'next.span_type'\n                )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n              )\n              return\n            }\n\n            const route = rootSpanAttributes.get('next.route')\n            if (route) {\n              const name = isRSCRequest\n                ? `RSC ${method} ${route}`\n                : `${method} ${route}`\n\n              span.setAttributes({\n                'next.route': route,\n                'http.route': route,\n                'next.span_name': name,\n              })\n              span.updateName(name)\n            } else {\n              span.updateName(\n                isRSCRequest\n                  ? `RSC ${method} ${req.url}`\n                  : `${method} ${req.url}`\n              )\n            }\n          })\n      )\n    })\n  }\n\n  private async handleRequestImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    try {\n      // Wait for the matchers to be ready.\n      await this.matchers.waitTillReady()\n\n      // ensure cookies set in middleware are merged and\n      // not overridden by API routes/getServerSideProps\n      patchSetHeaderWithCookieSupport(\n        req,\n        isNodeNextResponse(res) ? res.originalResponse : res\n      )\n\n      const urlParts = (req.url || '').split('?', 1)\n      const urlNoQuery = urlParts[0]\n\n      // this normalizes repeated slashes in the path e.g. hello//world ->\n      // hello/world or backslashes to forward slashes, this does not\n      // handle trailing slash as that is handled the same as a next.config.js\n      // redirect\n      if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n        const cleanUrl = normalizeRepeatedSlashes(req.url!)\n        res.redirect(cleanUrl, 308).body(cleanUrl).send()\n        return\n      }\n\n      // Parse url if parsedUrl not provided\n      if (!parsedUrl || typeof parsedUrl !== 'object') {\n        if (!req.url) {\n          throw new Error('Invariant: url can not be undefined')\n        }\n\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (!parsedUrl.pathname) {\n        throw new Error(\"Invariant: pathname can't be empty\")\n      }\n\n      // Parse the querystring ourselves if the user doesn't handle querystring parsing\n      if (typeof parsedUrl.query === 'string') {\n        parsedUrl.query = Object.fromEntries(\n          new URLSearchParams(parsedUrl.query)\n        )\n      }\n\n      // Update the `x-forwarded-*` headers.\n      const { originalRequest = null } = isNodeNextRequest(req) ? req : {}\n      const xForwardedProto = originalRequest?.headers['x-forwarded-proto']\n      const isHttps = xForwardedProto\n        ? xForwardedProto === 'https'\n        : !!(originalRequest?.socket as TLSSocket)?.encrypted\n\n      req.headers['x-forwarded-host'] ??= req.headers['host'] ?? this.hostname\n      req.headers['x-forwarded-port'] ??= this.port\n        ? this.port.toString()\n        : isHttps\n          ? '443'\n          : '80'\n      req.headers['x-forwarded-proto'] ??= isHttps ? 'https' : 'http'\n      req.headers['x-forwarded-for'] ??= originalRequest?.socket?.remoteAddress\n\n      // This should be done before any normalization of the pathname happens as\n      // it captures the initial URL.\n      this.attachRequestMeta(req, parsedUrl)\n\n      let finished = await this.handleRSCRequest(req, res, parsedUrl)\n      if (finished) return\n\n      const domainLocale = this.i18nProvider?.detectDomainLocale(\n        getHostname(parsedUrl, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || this.nextConfig.i18n?.defaultLocale\n      addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n      const url = parseUrlUtil(req.url.replace(/^\\/+/, '/'))\n      const pathnameInfo = getNextPathnameInfo(url.pathname, {\n        nextConfig: this.nextConfig,\n        i18nProvider: this.i18nProvider,\n      })\n      url.pathname = pathnameInfo.pathname\n\n      if (pathnameInfo.basePath) {\n        req.url = removePathPrefix(req.url!, this.nextConfig.basePath)\n      }\n\n      const useMatchedPathHeader =\n        this.minimalMode && typeof req.headers[MATCHED_PATH_HEADER] === 'string'\n\n      // TODO: merge handling with invokePath\n      if (useMatchedPathHeader) {\n        try {\n          if (this.enabledDirectories.app) {\n            // ensure /index path is normalized for prerender\n            // in minimal mode\n            if (req.url.match(/^\\/index($|\\?)/)) {\n              req.url = req.url.replace(/^\\/index/, '/')\n            }\n            parsedUrl.pathname =\n              parsedUrl.pathname === '/index' ? '/' : parsedUrl.pathname\n          }\n\n          // x-matched-path is the source of truth, it tells what page\n          // should be rendered because we don't process rewrites in minimalMode\n          let { pathname: matchedPath } = new URL(\n            fixMojibake(req.headers[MATCHED_PATH_HEADER] as string),\n            'http://localhost'\n          )\n\n          let { pathname: urlPathname } = new URL(req.url, 'http://localhost')\n\n          // For ISR the URL is normalized to the prerenderPath so if\n          // it's a data request the URL path will be the data URL,\n          // basePath is already stripped by this point\n          if (this.normalizers.data?.match(urlPathname)) {\n            addRequestMeta(req, 'isNextDataReq', true)\n          }\n          // In minimal mode, if PPR is enabled, then we should check to see if\n          // the request should be a resume request.\n          else if (\n            this.isAppPPREnabled &&\n            this.minimalMode &&\n            req.headers[NEXT_RESUME_HEADER] === '1' &&\n            req.method === 'POST'\n          ) {\n            // Decode the postponed state from the request body, it will come as\n            // an array of buffers, so collect them and then concat them to form\n            // the string.\n            const body: Array<Buffer> = []\n            for await (const chunk of req.body) {\n              body.push(chunk)\n            }\n            const postponed = Buffer.concat(body).toString('utf8')\n\n            addRequestMeta(req, 'postponed', postponed)\n          }\n\n          matchedPath = this.normalize(matchedPath)\n          const normalizedUrlPath = this.stripNextDataPath(urlPathname)\n\n          matchedPath = denormalizePagePath(matchedPath)\n\n          // Perform locale detection and normalization.\n          const localeAnalysisResult = this.i18nProvider?.analyze(matchedPath, {\n            defaultLocale,\n          })\n\n          // The locale result will be defined even if the locale was not\n          // detected for the request because it will be inferred from the\n          // default locale.\n          if (localeAnalysisResult) {\n            addRequestMeta(req, 'locale', localeAnalysisResult.detectedLocale)\n\n            // If the detected locale was inferred from the default locale, we\n            // need to modify the metadata on the request to indicate that.\n            if (localeAnalysisResult.inferredFromDefault) {\n              addRequestMeta(req, 'localeInferredFromDefault', true)\n            } else {\n              removeRequestMeta(req, 'localeInferredFromDefault')\n            }\n          }\n\n          let srcPathname = matchedPath\n          let pageIsDynamic = isDynamicRoute(srcPathname)\n          let paramsResult: {\n            params: ParsedUrlQuery | false\n            hasValidParams: boolean\n          } = {\n            params: false,\n            hasValidParams: false,\n          }\n\n          if (!pageIsDynamic) {\n            const match = await this.matchers.match(srcPathname, {\n              i18n: localeAnalysisResult,\n            })\n\n            // Update the source pathname to the matched page's pathname.\n            if (match) {\n              srcPathname = match.definition.pathname\n\n              // The page is dynamic if the params are defined. We know at this\n              // stage that the matched path is not a static page if the params\n              // were parsed from the matched path header.\n              if (typeof match.params !== 'undefined') {\n                pageIsDynamic = true\n                paramsResult.params = match.params\n                paramsResult.hasValidParams = true\n              }\n            }\n          }\n\n          // The rest of this function can't handle i18n properly, so ensure we\n          // restore the pathname with the locale information stripped from it\n          // now that we're done matching if we're using i18n.\n          if (localeAnalysisResult) {\n            matchedPath = localeAnalysisResult.pathname\n          }\n\n          const utils = getServerUtils({\n            pageIsDynamic,\n            page: srcPathname,\n            i18n: this.nextConfig.i18n,\n            basePath: this.nextConfig.basePath,\n            rewrites: this.getRoutesManifest()?.rewrites || {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            caseSensitive: !!this.nextConfig.experimental.caseSensitiveRoutes,\n          })\n\n          // Ensure parsedUrl.pathname includes locale before processing\n          // rewrites or they won't match correctly.\n          if (defaultLocale && !pathnameInfo.locale) {\n            parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname}`\n          }\n\n          // Store a copy of `parsedUrl.query` before calling handleRewrites.\n          // Since `handleRewrites` might add new queries to `parsedUrl.query`.\n          const originQueryParams = { ...parsedUrl.query }\n\n          const pathnameBeforeRewrite = parsedUrl.pathname\n          const rewriteParamKeys = Object.keys(\n            utils.handleRewrites(req, parsedUrl)\n          )\n\n          // Create a copy of the query params to avoid mutating the original\n          // object. This prevents any overlapping query params that have the\n          // same normalized key from causing issues.\n          const queryParams = { ...parsedUrl.query }\n          const didRewrite = pathnameBeforeRewrite !== parsedUrl.pathname\n\n          if (didRewrite && parsedUrl.pathname) {\n            addRequestMeta(req, 'rewroteURL', parsedUrl.pathname)\n          }\n\n          const routeParamKeys = new Set<string>()\n          for (const [key, value] of Object.entries(parsedUrl.query)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            // Remove the prefixed key from the query params because we want\n            // to consume it for the dynamic route matcher.\n            delete parsedUrl.query[key]\n            routeParamKeys.add(normalizedKey)\n\n            if (typeof value === 'undefined') continue\n\n            queryParams[normalizedKey] = Array.isArray(value)\n              ? value.map((v) => decodeQueryPathParameter(v))\n              : decodeQueryPathParameter(value)\n          }\n\n          // interpolate dynamic params and normalize URL if needed\n          if (pageIsDynamic) {\n            let params: ParsedUrlQuery | false = {}\n\n            // If we don't already have valid params, try to parse them from\n            // the query params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                false\n              )\n            }\n\n            // for prerendered ISR paths we attempt parsing the route\n            // params from the URL directly as route-matches may not\n            // contain the correct values due to the filesystem path\n            // matching before the dynamic route has been matched\n            if (\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(normalizedUrlPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(normalizedUrlPath)\n\n              if (matcherParams) {\n                utils.normalizeDynamicRouteParams(matcherParams, false)\n                Object.assign(paramsResult.params, matcherParams)\n                paramsResult.hasValidParams = true\n              }\n            }\n\n            // if an action request is bypassing a prerender and we\n            // don't have the params in the URL since it was prerendered\n            // and matched during handle: 'filesystem' rather than dynamic route\n            // resolving we need to parse the params from the matched-path.\n            // Note: this is similar to above case but from match-path instead\n            // of from the request URL since a rewrite could cause that to not\n            // match the src pathname\n            if (\n              // we can have a collision with /index and a top-level /[slug]\n              matchedPath !== '/index' &&\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(matchedPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(matchedPath)\n\n              if (matcherParams) {\n                const curParamsResult = utils.normalizeDynamicRouteParams(\n                  matcherParams,\n                  false\n                )\n\n                if (curParamsResult.hasValidParams) {\n                  Object.assign(params, matcherParams)\n                  paramsResult = curParamsResult\n                }\n              }\n            }\n\n            if (paramsResult.hasValidParams) {\n              params = paramsResult.params\n            }\n\n            const routeMatchesHeader = req.headers['x-now-route-matches']\n            if (\n              typeof routeMatchesHeader === 'string' &&\n              routeMatchesHeader &&\n              isDynamicRoute(matchedPath) &&\n              !paramsResult.hasValidParams\n            ) {\n              const routeMatches =\n                utils.getParamsFromRouteMatches(routeMatchesHeader)\n\n              if (routeMatches) {\n                paramsResult = utils.normalizeDynamicRouteParams(\n                  routeMatches,\n                  true\n                )\n\n                if (paramsResult.hasValidParams) {\n                  params = paramsResult.params\n                }\n              }\n            }\n\n            // Try to parse the params from the query if we couldn't parse them\n            // from the route matches but ignore missing optional params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                true\n              )\n\n              if (paramsResult.hasValidParams) {\n                params = paramsResult.params\n              }\n            }\n\n            // If the pathname being requested is the same as the source\n            // pathname, and we don't have valid params, we want to use the\n            // default route matches.\n            if (\n              utils.defaultRouteMatches &&\n              normalizedUrlPath === srcPathname &&\n              !paramsResult.hasValidParams\n            ) {\n              params = utils.defaultRouteMatches\n\n              // If the route matches header is an empty string, we want to\n              // render a fallback shell. This is because we know this came from\n              // a prerender (it has the header) but it's values were filtered\n              // out (because the allowQuery was empty). If it was undefined\n              // then we know that the request is hitting the lambda directly.\n              if (routeMatchesHeader === '') {\n                addRequestMeta(req, 'renderFallbackShell', true)\n              }\n            }\n\n            if (params) {\n              matchedPath = utils.interpolateDynamicPath(srcPathname, params)\n              req.url = utils.interpolateDynamicPath(req.url!, params)\n\n              // If the request is for a segment prefetch, we need to update the\n              // segment prefetch request path to include the interpolated\n              // params.\n              let segmentPrefetchRSCRequest = getRequestMeta(\n                req,\n                'segmentPrefetchRSCRequest'\n              )\n              if (\n                segmentPrefetchRSCRequest &&\n                isDynamicRoute(segmentPrefetchRSCRequest, false)\n              ) {\n                segmentPrefetchRSCRequest = utils.interpolateDynamicPath(\n                  segmentPrefetchRSCRequest,\n                  params\n                )\n\n                req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] =\n                  segmentPrefetchRSCRequest\n                addRequestMeta(\n                  req,\n                  'segmentPrefetchRSCRequest',\n                  segmentPrefetchRSCRequest\n                )\n              }\n            }\n          }\n\n          if (pageIsDynamic || didRewrite) {\n            utils.normalizeCdnUrl(req, [\n              ...rewriteParamKeys,\n              ...Object.keys(utils.defaultRouteRegex?.groups || {}),\n            ])\n          }\n          // Remove the route `params` keys from `parsedUrl.query` if they are\n          // not in the original query params.\n          // If it's used in both route `params` and query `searchParams`, it should be kept.\n          for (const key of routeParamKeys) {\n            if (!(key in originQueryParams)) {\n              delete parsedUrl.query[key]\n            }\n          }\n          parsedUrl.pathname = matchedPath\n          url.pathname = parsedUrl.pathname\n          finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n          if (finished) return\n        } catch (err) {\n          if (err instanceof DecodeError || err instanceof NormalizeError) {\n            res.statusCode = 400\n            return this.renderError(null, req, res, '/_error', {})\n          }\n          throw err\n        }\n      }\n\n      addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n      if (pathnameInfo.locale) {\n        req.url = formatUrl(url)\n        addRequestMeta(req, 'didStripLocale', true)\n      }\n\n      // If we aren't in minimal mode or there is no locale in the query\n      // string, add the locale to the query string.\n      if (!this.minimalMode || !getRequestMeta(req, 'locale')) {\n        // If the locale is in the pathname, add it to the query string.\n        if (pathnameInfo.locale) {\n          addRequestMeta(req, 'locale', pathnameInfo.locale)\n        }\n        // If the default locale is available, add it to the query string and\n        // mark it as inferred rather than implicit.\n        else if (defaultLocale) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          addRequestMeta(req, 'localeInferredFromDefault', true)\n        }\n      }\n\n      // set incremental cache to request meta so it can\n      // be passed down for edge functions and the fetch disk\n      // cache can be leveraged locally\n      if (\n        !(this.serverOptions as any).webServerConfig &&\n        !getRequestMeta(req, 'incrementalCache')\n      ) {\n        const incrementalCache = await this.getIncrementalCache({\n          requestHeaders: Object.assign({}, req.headers),\n        })\n\n        incrementalCache.resetRequestCache()\n        addRequestMeta(req, 'incrementalCache', incrementalCache)\n        // This is needed for pages router to leverage unstable_cache\n        // TODO: re-work this handling to not use global and use a AsyncStore\n        ;(globalThis as any).__incrementalCache = incrementalCache\n      }\n\n      const cacheHandlers = getCacheHandlers()\n\n      if (cacheHandlers) {\n        await Promise.all(\n          [...cacheHandlers].map(async (cacheHandler) => {\n            if ('refreshTags' in cacheHandler) {\n              // Note: cacheHandler.refreshTags() is called lazily before the\n              // first cache entry is retrieved. It allows us to skip the\n              // refresh request if no caches are read at all.\n            } else {\n              const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n                req.headers,\n                this.getPrerenderManifest().preview.previewModeId\n              )\n\n              await cacheHandler.receiveExpiredTags(\n                ...previouslyRevalidatedTags\n              )\n            }\n          })\n        )\n      }\n\n      // set server components HMR cache to request meta so it can be passed\n      // down for edge functions\n      if (!getRequestMeta(req, 'serverComponentsHmrCache')) {\n        addRequestMeta(\n          req,\n          'serverComponentsHmrCache',\n          this.getServerComponentsHmrCache()\n        )\n      }\n\n      // when invokePath is specified we can short short circuit resolving\n      // we only honor this header if we are inside of a render worker to\n      // prevent external users coercing the routing path\n      const invokePath = getRequestMeta(req, 'invokePath')\n      const useInvokePath = !useMatchedPathHeader && invokePath\n\n      if (useInvokePath) {\n        const invokeStatus = getRequestMeta(req, 'invokeStatus')\n        if (invokeStatus) {\n          const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n          if (invokeQuery) {\n            Object.assign(parsedUrl.query, invokeQuery)\n          }\n\n          res.statusCode = invokeStatus\n          let err: Error | null = getRequestMeta(req, 'invokeError') || null\n\n          return this.renderError(err, req, res, '/_error', parsedUrl.query)\n        }\n\n        const parsedMatchedPath = new URL(invokePath || '/', 'http://n')\n        const invokePathnameInfo = getNextPathnameInfo(\n          parsedMatchedPath.pathname,\n          {\n            nextConfig: this.nextConfig,\n            parseData: false,\n          }\n        )\n\n        if (invokePathnameInfo.locale) {\n          addRequestMeta(req, 'locale', invokePathnameInfo.locale)\n        }\n\n        if (parsedUrl.pathname !== parsedMatchedPath.pathname) {\n          parsedUrl.pathname = parsedMatchedPath.pathname\n          addRequestMeta(req, 'rewroteURL', invokePathnameInfo.pathname)\n        }\n        const normalizeResult = normalizeLocalePath(\n          removePathPrefix(parsedUrl.pathname, this.nextConfig.basePath || ''),\n          this.nextConfig.i18n?.locales\n        )\n\n        if (normalizeResult.detectedLocale) {\n          addRequestMeta(req, 'locale', normalizeResult.detectedLocale)\n        }\n        parsedUrl.pathname = normalizeResult.pathname\n\n        for (const key of Object.keys(parsedUrl.query)) {\n          delete parsedUrl.query[key]\n        }\n        const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n        if (invokeQuery) {\n          Object.assign(parsedUrl.query, invokeQuery)\n        }\n\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        await this.handleCatchallRenderRequest(req, res, parsedUrl)\n        return\n      }\n\n      if (getRequestMeta(req, 'middlewareInvoke')) {\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        finished = await this.handleCatchallMiddlewareRequest(\n          req,\n          res,\n          parsedUrl\n        )\n        if (finished) return\n\n        const err = new Error()\n        ;(err as any).result = {\n          response: new Response(null, {\n            headers: {\n              'x-middleware-next': '1',\n            },\n          }),\n        }\n        ;(err as any).bubble = true\n        throw err\n      }\n\n      // This wasn't a request via the matched path or the invoke path, so\n      // prepare for a legacy run by removing the base path.\n\n      // ensure we strip the basePath when not using an invoke header\n      if (!useMatchedPathHeader && pathnameInfo.basePath) {\n        parsedUrl.pathname = removePathPrefix(\n          parsedUrl.pathname,\n          pathnameInfo.basePath\n        )\n      }\n\n      res.statusCode = 200\n      return await this.run(req, res, parsedUrl)\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      if (\n        (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL') ||\n        err instanceof DecodeError ||\n        err instanceof NormalizeError\n      ) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n\n      if (\n        this.minimalMode ||\n        this.renderOpts.dev ||\n        (isBubbledError(err) && err.bubble)\n      ) {\n        throw err\n      }\n      this.logError(getProperError(err))\n      res.statusCode = 500\n      res.body('Internal Server Error').send()\n    }\n  }\n\n  /**\n   * Normalizes a pathname without attaching any metadata from any matched\n   * normalizer.\n   *\n   * @param pathname the pathname to normalize\n   * @returns the normalized pathname\n   */\n  private normalize = (pathname: string) => {\n    const normalizers: Array<PathnameNormalizer> = []\n\n    if (this.normalizers.data) {\n      normalizers.push(this.normalizers.data)\n    }\n\n    // We have to put the segment prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.segmentPrefetchRSC) {\n      normalizers.push(this.normalizers.segmentPrefetchRSC)\n    }\n\n    // We have to put the prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.prefetchRSC) {\n      normalizers.push(this.normalizers.prefetchRSC)\n    }\n\n    if (this.normalizers.rsc) {\n      normalizers.push(this.normalizers.rsc)\n    }\n\n    for (const normalizer of normalizers) {\n      if (!normalizer.match(pathname)) continue\n\n      return normalizer.normalize(pathname, true)\n    }\n\n    return pathname\n  }\n\n  private normalizeAndAttachMetadata: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = async (req, res, url) => {\n    let finished = await this.handleNextImageRequest(req, res, url)\n    if (finished) return true\n\n    if (this.enabledDirectories.pages) {\n      finished = await this.handleNextDataRequest(req, res, url)\n      if (finished) return true\n    }\n\n    return false\n  }\n\n  /**\n   * @internal - this method is internal to Next.js and should not be used directly by end-users\n   */\n  public getRequestHandlerWithMetadata(\n    meta: RequestMeta\n  ): BaseRequestHandler<ServerRequest, ServerResponse> {\n    const handler = this.getRequestHandler()\n    return (req, res, parsedUrl) => {\n      setRequestMeta(req, meta)\n      return handler(req, res, parsedUrl)\n    }\n  }\n\n  public getRequestHandler(): BaseRequestHandler<\n    ServerRequest,\n    ServerResponse\n  > {\n    return this.handleRequest.bind(this)\n  }\n\n  protected abstract handleUpgrade(\n    req: ServerRequest,\n    socket: any,\n    head?: any\n  ): Promise<void>\n\n  public setAssetPrefix(prefix?: string): void {\n    this.nextConfig.assetPrefix = prefix ? prefix.replace(/\\/$/, '') : ''\n  }\n\n  protected prepared: boolean = false\n  protected preparedPromise: Promise<void> | null = null\n  /**\n   * Runs async initialization of server.\n   * It is idempotent, won't fire underlying initialization more than once.\n   */\n  public async prepare(): Promise<void> {\n    if (this.prepared) return\n\n    // Get instrumentation module\n    if (!this.instrumentation) {\n      this.instrumentation = await this.loadInstrumentationModule()\n    }\n    if (this.preparedPromise === null) {\n      this.preparedPromise = this.prepareImpl().then(() => {\n        this.prepared = true\n        this.preparedPromise = null\n      })\n    }\n    return this.preparedPromise\n  }\n  protected async prepareImpl(): Promise<void> {}\n  protected async loadInstrumentationModule(): Promise<any> {}\n\n  public async close(): Promise<void> {}\n\n  protected getAppPathRoutes(): Record<string, string[]> {\n    const appPathRoutes: Record<string, string[]> = {}\n\n    Object.keys(this.appPathsManifest || {}).forEach((entry) => {\n      const normalizedPath = normalizeAppPath(entry)\n      if (!appPathRoutes[normalizedPath]) {\n        appPathRoutes[normalizedPath] = []\n      }\n      appPathRoutes[normalizedPath].push(entry)\n    })\n    return appPathRoutes\n  }\n\n  protected async run(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.run, async () =>\n      this.runImpl(req, res, parsedUrl)\n    )\n  }\n\n  private async runImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.handleCatchallRenderRequest(req, res, parsedUrl)\n  }\n\n  private async pipe(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.pipe, async () =>\n      this.pipeImpl(fn, partialContext)\n    )\n  }\n\n  private async pipeImpl(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    const ua = partialContext.req.headers['user-agent'] || ''\n\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        // `renderOpts.botType` is accumulated in `this.renderImpl()`\n        supportsDynamicResponse: !this.renderOpts.botType,\n        serveStreamingMetadata: shouldServeStreamingMetadata(\n          ua,\n          this.nextConfig.htmlLimitedBots\n        ),\n      },\n    }\n\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return\n    }\n    const { req, res } = ctx\n    const originalStatus = res.statusCode\n    const { body } = payload\n    let { cacheControl } = payload\n    if (!res.sent) {\n      const { generateEtags, poweredByHeader, dev } = this.renderOpts\n\n      // In dev, we should not cache pages for any reason.\n      if (dev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n        cacheControl = undefined\n      }\n\n      if (cacheControl && cacheControl.expire === undefined) {\n        cacheControl.expire = this.nextConfig.expireTime\n      }\n\n      await this.sendRenderResult(req, res, {\n        result: body,\n        generateEtags,\n        poweredByHeader,\n        cacheControl,\n      })\n      res.statusCode = originalStatus\n    }\n  }\n\n  private async getStaticHTML(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<string | null> {\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: false,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return null\n    }\n    return payload.body.toUnchunkedString()\n  }\n\n  public async render(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.render, async () =>\n      this.renderImpl(req, res, pathname, query, parsedUrl, internalRender)\n    )\n  }\n\n  protected getWaitUntil(): WaitUntil | undefined {\n    const builtinRequestContext = getBuiltinRequestContext()\n    if (builtinRequestContext) {\n      // the platform provided a request context.\n      // use the `waitUntil` from there, whether actually present or not --\n      // if not present, `after` will error.\n\n      // NOTE: if we're in an edge runtime sandbox, this context will be used to forward the outer waitUntil.\n      return builtinRequestContext.waitUntil\n    }\n\n    if (this.minimalMode) {\n      // we're built for a serverless environment, and `waitUntil` is not available,\n      // but using a noop would likely lead to incorrect behavior,\n      // because we have no way of keeping the invocation alive.\n      // return nothing, and `after` will error if used.\n      //\n      // NOTE: for edge functions, `NextWebServer` always runs in minimal mode.\n      //\n      // NOTE: if we're in an edge runtime sandbox, waitUntil will be passed in using \"@next/request-context\",\n      // so we won't get here.\n      return undefined\n    }\n\n    return this.getInternalWaitUntil()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil | undefined {\n    return undefined\n  }\n\n  private async renderImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    if (!pathname.startsWith('/')) {\n      console.warn(\n        `Cannot render page with path \"${pathname}\", did you mean \"/${pathname}\"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`\n      )\n    }\n\n    if (\n      this.serverOptions.customServer &&\n      pathname === '/index' &&\n      !(await this.hasPage('/index'))\n    ) {\n      // maintain backwards compatibility for custom server\n      // (see custom-server integration tests)\n      pathname = '/'\n    }\n\n    const ua = req.headers['user-agent'] || ''\n    this.renderOpts.botType = getBotType(ua)\n\n    // we allow custom servers to call render for all URLs\n    // so check if we need to serve a static _next file or not.\n    // we don't modify the URL for _next/data request but still\n    // call render so we special case this to prevent an infinite loop\n    if (\n      !internalRender &&\n      !this.minimalMode &&\n      !getRequestMeta(req, 'isNextDataReq') &&\n      (req.url?.match(/^\\/_next\\//) ||\n        (this.hasStaticDir && req.url!.match(/^\\/static\\//)))\n    ) {\n      return this.handleRequest(req, res, parsedUrl)\n    }\n\n    if (isBlockedPage(pathname)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    return this.pipe((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async getStaticPaths({\n    pathname,\n  }: {\n    pathname: string\n    urlPathname: string\n    requestHeaders: import('./lib/incremental-cache').IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    prerenderedRoutes?: PrerenderedRoute[]\n    fallbackMode?: FallbackMode\n  }> {\n    // Read whether or not fallback should exist from the manifest.\n    const fallbackField =\n      this.getPrerenderManifest().dynamicRoutes[pathname]?.fallback\n\n    return {\n      // `staticPaths` is intentionally set to `undefined` as it should've\n      // been caught when checking disk data.\n      staticPaths: undefined,\n      fallbackMode: parseFallbackField(fallbackField),\n    }\n  }\n\n  private async renderToResponseWithComponents(\n    requestContext: RequestContext<ServerRequest, ServerResponse>,\n    findComponentsResult: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponseWithComponents,\n      async () =>\n        this.renderToResponseWithComponentsImpl(\n          requestContext,\n          findComponentsResult\n        )\n    )\n  }\n\n  protected pathCouldBeIntercepted(resolvedPathname: string): boolean {\n    return (\n      isInterceptionRouteAppPath(resolvedPathname) ||\n      this.interceptionRoutePatterns.some((regexp) => {\n        return regexp.test(resolvedPathname)\n      })\n    )\n  }\n\n  protected setVaryHeader(\n    req: ServerRequest,\n    res: ServerResponse,\n    isAppPath: boolean,\n    resolvedPathname: string\n  ): void {\n    const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    let addedNextUrlToVary = false\n\n    if (isAppPath && this.pathCouldBeIntercepted(resolvedPathname)) {\n      // Interception route responses can vary based on the `Next-URL` header.\n      // We use the Vary header to signal this behavior to the client to properly cache the response.\n      res.appendHeader('vary', `${baseVaryHeader}, ${NEXT_URL}`)\n      addedNextUrlToVary = true\n    } else if (isAppPath || isRSCRequest) {\n      // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.\n      // We also set this header for pages to avoid caching issues when navigating between pages and app.\n      res.appendHeader('vary', baseVaryHeader)\n    }\n\n    if (!addedNextUrlToVary) {\n      // Remove `Next-URL` from the request headers we determined it wasn't necessary to include in the Vary header.\n      // This is to avoid any dependency on the `Next-URL` header being present when preparing the response.\n      delete req.headers[NEXT_URL]\n    }\n  }\n\n  private async renderToResponseWithComponentsImpl(\n    {\n      req,\n      res,\n      pathname,\n      renderOpts: opts,\n    }: RequestContext<ServerRequest, ServerResponse>,\n    { components, query }: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    const isErrorPathname = pathname === '/_error'\n    const is404Page =\n      pathname === '/404' || (isErrorPathname && res.statusCode === 404)\n    const is500Page =\n      pathname === '/500' || (isErrorPathname && res.statusCode === 500)\n    const isAppPath = components.isAppPath === true\n\n    const hasServerProps = !!components.getServerSideProps\n    const isPossibleServerAction = getIsPossibleServerAction(req)\n    let isSSG = !!components.getStaticProps\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    // Not all CDNs respect the Vary header when caching. We must assume that\n    // only the URL is used to vary the responses. The Next client computes a\n    // hash of the header values and sends it as a search param. Before\n    // responding to a request, we must verify that the hash matches the\n    // expected value. Neglecting to do this properly can lead to cache\n    // poisoning attacks on certain CDNs.\n    if (\n      !this.minimalMode &&\n      this.nextConfig.experimental.validateRSCRequestHeaders &&\n      isRSCRequest\n    ) {\n      const headers = req.headers\n\n      const prefetchHeaderValue = headers[NEXT_ROUTER_PREFETCH_HEADER]\n      const routerPrefetch =\n        prefetchHeaderValue !== undefined\n          ? // We only recognize '1' and '2'. Strip all other values here.\n            prefetchHeaderValue === '1' || prefetchHeaderValue === '2'\n            ? prefetchHeaderValue\n            : undefined\n          : // For runtime prefetches, we always perform a dynamic request,\n            // so we don't expect the header to be stripped by an intermediate layer.\n            // This should only happen for static prefetches, so we only handle those here.\n            getRequestMeta(req, 'isPrefetchRSCRequest')\n            ? '1'\n            : undefined\n\n      const segmentPrefetchRSCRequest =\n        headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] ||\n        getRequestMeta(req, 'segmentPrefetchRSCRequest')\n\n      const expectedHash = computeCacheBustingSearchParam(\n        routerPrefetch,\n        segmentPrefetchRSCRequest,\n        headers[NEXT_ROUTER_STATE_TREE_HEADER],\n        headers[NEXT_URL]\n      )\n      const actualHash =\n        getRequestMeta(req, 'cacheBustingSearchParam') ??\n        new URL(req.url || '', 'http://localhost').searchParams.get(\n          NEXT_RSC_UNION_QUERY\n        )\n\n      if (expectedHash !== actualHash) {\n        // The hash sent by the client does not match the expected value.\n        // Redirect to the URL with the correct cache-busting search param.\n        // This prevents cache poisoning attacks on CDNs that don't respect Vary headers.\n        // Note: When no headers are present, expectedHash is empty string and client\n        // must send `_rsc` param, otherwise actualHash is null and hash check fails.\n        const url = new URL(req.url || '', 'http://localhost')\n        setCacheBustingSearchParamWithHash(url, expectedHash)\n        res.statusCode = 307\n        res.setHeader('location', `${url.pathname}${url.search}`)\n        res.body('').send()\n        return null\n      }\n    }\n\n    // Compute the iSSG cache key. We use the rewroteUrl since\n    // pages with fallback: false are allowed to be rewritten to\n    // and we need to look up the path by the rewritten path\n    let urlPathname = parseUrl(req.url || '').pathname || '/'\n\n    let resolvedUrlPathname = getRequestMeta(req, 'rewroteURL') || urlPathname\n\n    this.setVaryHeader(req, res, isAppPath, resolvedUrlPathname)\n\n    let staticPaths: string[] | undefined\n    let hasFallback = false\n\n    const prerenderManifest = this.getPrerenderManifest()\n\n    if (\n      hasFallback ||\n      staticPaths?.includes(resolvedUrlPathname) ||\n      // this signals revalidation in deploy environments\n      // TODO: make this more generic\n      req.headers['x-now-route-matches']\n    ) {\n      isSSG = true\n    } else if (!this.renderOpts.dev) {\n      isSSG ||= !!prerenderManifest.routes[toRoute(pathname)]\n    }\n\n    // Toggle whether or not this is a Data request\n    const isNextDataRequest =\n      !!(\n        getRequestMeta(req, 'isNextDataReq') ||\n        (req.headers['x-nextjs-data'] &&\n          (this.serverOptions as any).webServerConfig)\n      ) &&\n      (isSSG || hasServerProps)\n\n    // when we are handling a middleware prefetch and it doesn't\n    // resolve to a static data route we bail early to avoid\n    // unexpected SSR invocations\n    if (\n      !isSSG &&\n      req.headers['x-middleware-prefetch'] &&\n      !(is404Page || pathname === '/_error')\n    ) {\n      res.setHeader(MATCHED_PATH_HEADER, pathname)\n      res.setHeader('x-middleware-skip', '1')\n      res.setHeader(\n        'cache-control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n      res.body('{}').send()\n      return null\n    }\n\n    // normalize req.url for SSG paths as it is not exposed\n    // to getStaticProps and the asPath should not expose /_next/data\n    if (\n      isSSG &&\n      this.minimalMode &&\n      req.headers[MATCHED_PATH_HEADER] &&\n      req.url.startsWith('/_next/data')\n    ) {\n      req.url = this.stripNextDataPath(req.url)\n    }\n\n    const locale = getRequestMeta(req, 'locale')\n\n    if (\n      !!req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n    }\n\n    let routeModule: RouteModule | undefined\n    if (components.routeModule) {\n      routeModule = components.routeModule\n    }\n\n    /**\n     * If the route being rendered is an app page, and the ppr feature has been\n     * enabled, then the given route _could_ support PPR.\n     */\n    const couldSupportPPR: boolean =\n      this.isAppPPREnabled &&\n      typeof routeModule !== 'undefined' &&\n      isAppPageRouteModule(routeModule)\n\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n      typeof query.__nextppronly !== 'undefined' &&\n      couldSupportPPR\n\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled: boolean =\n      couldSupportPPR &&\n      ((\n        prerenderManifest.routes[pathname] ??\n        prerenderManifest.dynamicRoutes[pathname]\n      )?.renderingMode === 'PARTIALLY_STATIC' ||\n        // Ideally we'd want to check the appConfig to see if this page has PPR\n        // enabled or not, but that would require plumbing the appConfig through\n        // to the server during development. We assume that the page supports it\n        // but only during development.\n        (hasDebugStaticShellQuery &&\n          (this.renderOpts.dev === true ||\n            this.experimentalTestProxy === true)))\n\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled\n      ? getRequestMeta(req, 'postponed')\n      : undefined\n\n    // we need to ensure the status code if /404 is visited directly\n    if (is404Page && !isNextDataRequest && !isRSCRequest) {\n      res.statusCode = 404\n    }\n\n    // ensure correct status is set when visiting a status page\n    // directly e.g. /500\n    if (STATIC_STATUS_PAGES.includes(pathname)) {\n      res.statusCode = parseInt(pathname.slice(1), 10)\n    }\n\n    if (\n      // Server actions can use non-GET/HEAD methods.\n      !isPossibleServerAction &&\n      // Resume can use non-GET/HEAD methods.\n      !minimalPostponed &&\n      !is404Page &&\n      !is500Page &&\n      pathname !== '/_error' &&\n      req.method !== 'HEAD' &&\n      req.method !== 'GET' &&\n      (typeof components.Component === 'string' || isSSG)\n    ) {\n      res.statusCode = 405\n      res.setHeader('Allow', ['GET', 'HEAD'])\n      res.body('Method Not Allowed').send()\n      return null\n    }\n\n    // handle static page\n    if (typeof components.Component === 'string') {\n      return {\n        body: RenderResult.fromStatic(\n          components.Component,\n          HTML_CONTENT_TYPE_HEADER\n        ),\n      }\n    }\n\n    // Ensure that if the `amp` query parameter is falsy that we remove it from\n    // the query object. This ensures it won't be found by the `in` operator.\n    if ('amp' in query && !query.amp) delete query.amp\n\n    if (opts.supportsDynamicResponse === true) {\n      const ua = req.headers['user-agent'] || ''\n      const isBotRequest = isBot(ua)\n      const isSupportedDocument =\n        typeof components.Document?.getInitialProps !== 'function' ||\n        // The built-in `Document` component also supports dynamic HTML for concurrent mode.\n        NEXT_BUILTIN_DOCUMENT in components.Document\n\n      // Disable dynamic HTML in cases that we know it won't be generated,\n      // so that we can continue generating a cache key when possible.\n      // TODO-APP: should the first render for a dynamic app path\n      // be static so we can collect revalidate and populate the\n      // cache if there are no dynamic data requirements\n      opts.supportsDynamicResponse =\n        !isSSG && !isBotRequest && !query.amp && isSupportedDocument\n    }\n\n    // In development, we always want to generate dynamic HTML.\n    if (!isNextDataRequest && isAppPath && opts.dev) {\n      opts.supportsDynamicResponse = true\n    }\n\n    if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER]) {\n      // the url value is already correct when the matched-path header is set\n      resolvedUrlPathname = urlPathname\n    }\n\n    urlPathname = removeTrailingSlash(urlPathname)\n    resolvedUrlPathname = removeTrailingSlash(resolvedUrlPathname)\n    if (this.localeNormalizer) {\n      resolvedUrlPathname = this.localeNormalizer.normalize(resolvedUrlPathname)\n    }\n\n    // remove /_next/data prefix from urlPathname so it matches\n    // for direct page visit and /_next/data visit\n    if (isNextDataRequest) {\n      resolvedUrlPathname = this.stripNextDataPath(resolvedUrlPathname)\n      urlPathname = this.stripNextDataPath(urlPathname)\n    }\n\n    // use existing incrementalCache instance if available\n    const incrementalCache: import('./lib/incremental-cache').IncrementalCache =\n      await this.getIncrementalCache({\n        requestHeaders: Object.assign({}, req.headers),\n      })\n\n    // TODO: investigate, this is not safe across multiple concurrent requests\n    incrementalCache.resetRequestCache()\n\n    if (\n      routeModule?.isDev &&\n      isDynamicRoute(pathname) &&\n      (components.getStaticPaths || isAppPath)\n    ) {\n      const pathsResults = await this.getStaticPaths({\n        pathname,\n        urlPathname,\n        requestHeaders: req.headers,\n        page: components.page,\n        isAppPath,\n      })\n      if (isAppPath && this.nextConfig.experimental.cacheComponents) {\n        if (pathsResults.prerenderedRoutes?.length) {\n          let smallestFallbackRouteParams = null\n          for (const route of pathsResults.prerenderedRoutes) {\n            const fallbackRouteParams = route.fallbackRouteParams\n            if (!fallbackRouteParams || fallbackRouteParams.length === 0) {\n              // There are no fallback route params so we don't need to continue\n              smallestFallbackRouteParams = null\n              break\n            }\n            if (\n              smallestFallbackRouteParams === null ||\n              fallbackRouteParams.length < smallestFallbackRouteParams.length\n            ) {\n              smallestFallbackRouteParams = fallbackRouteParams\n            }\n          }\n          if (smallestFallbackRouteParams) {\n            const devValidatingFallbackParams = new Map<string, string>(\n              smallestFallbackRouteParams.map((v) => [v, ''])\n            )\n            addRequestMeta(\n              req,\n              'devValidatingFallbackParams',\n              devValidatingFallbackParams\n            )\n          }\n        }\n      }\n    }\n\n    // An OPTIONS request to a page handler is invalid.\n    if (\n      req.method === 'OPTIONS' &&\n      !is404Page &&\n      (!routeModule || !isAppRouteRouteModule(routeModule))\n    ) {\n      await sendResponse(req, res, new Response(null, { status: 400 }))\n      return null\n    }\n\n    const request = isNodeNextRequest(req) ? req.originalRequest : req\n    const response = isNodeNextResponse(res) ? res.originalResponse : res\n\n    const parsedInitUrl = parseUrl(getRequestMeta(req, 'initURL') || req.url)\n    let initPathname = parsedInitUrl.pathname || '/'\n\n    for (const normalizer of [\n      this.normalizers.segmentPrefetchRSC,\n      this.normalizers.prefetchRSC,\n      this.normalizers.rsc,\n    ]) {\n      if (normalizer?.match(initPathname)) {\n        initPathname = normalizer.normalize(initPathname)\n      }\n    }\n\n    // On minimal mode, the request url of dynamic route can be a\n    // literal dynamic route ('/[slug]') instead of actual URL, so overwriting to initPathname\n    // will transform back the resolved url to the dynamic route pathname.\n    if (!(this.minimalMode && isErrorPathname)) {\n      request.url = `${initPathname}${parsedInitUrl.search || ''}`\n    }\n\n    // propagate the request context for dev\n    setRequestMeta(request, getRequestMeta(req))\n    addRequestMeta(request, 'distDir', this.distDir)\n    addRequestMeta(request, 'query', query)\n    addRequestMeta(request, 'params', opts.params)\n    addRequestMeta(request, 'ampValidator', this.renderOpts.ampValidator)\n    addRequestMeta(request, 'minimalMode', this.minimalMode)\n\n    if (opts.err) {\n      addRequestMeta(request, 'invokeError', opts.err)\n    }\n\n    const handler: (\n      req: ServerRequest | IncomingMessage,\n      res: ServerResponse | HTTPServerResponse,\n      ctx: {\n        waitUntil: ReturnType<Server['getWaitUntil']>\n      }\n    ) => Promise<void> = components.ComponentMod.handler\n\n    const maybeDevRequest =\n      // we need to capture fetch metrics when they are set\n      // and can't wait for handler to resolve as the fetch\n      // metrics are logged on response close which happens\n      // before handler resolves\n      process.env.NODE_ENV === 'development'\n        ? new Proxy(request, {\n            get(target: any, prop) {\n              if (typeof target[prop] === 'function') {\n                return target[prop].bind(target)\n              }\n              return target[prop]\n            },\n            set(target: any, prop, value) {\n              if (prop === 'fetchMetrics') {\n                ;(req as any).fetchMetrics = value\n              }\n              target[prop] = value\n              return true\n            },\n          })\n        : request\n\n    await handler(maybeDevRequest, response, {\n      waitUntil: this.getWaitUntil(),\n    })\n\n    // response is handled fully in handler\n    return null\n  }\n\n  private stripNextDataPath(path: string, stripLocale = true) {\n    if (path.includes(this.buildId)) {\n      const splitPath = path.substring(\n        path.indexOf(this.buildId) + this.buildId.length\n      )\n\n      path = denormalizePagePath(splitPath.replace(/\\.json$/, ''))\n    }\n\n    if (this.localeNormalizer && stripLocale) {\n      return this.localeNormalizer.normalize(path)\n    }\n    return path\n  }\n\n  // map the route to the actual bundle name\n  protected getOriginalAppPaths(route: string) {\n    if (this.enabledDirectories.app) {\n      const originalAppPath = this.appPathRoutes?.[route]\n\n      if (!originalAppPath) {\n        return null\n      }\n\n      return originalAppPath\n    }\n    return null\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const { query, pathname } = ctx\n\n    const appPaths = this.getOriginalAppPaths(pathname)\n    const isAppPath = Array.isArray(appPaths)\n\n    let page = pathname\n    if (isAppPath) {\n      // the last item in the array is the root page, if there are parallel routes\n      page = appPaths[appPaths.length - 1]\n    }\n\n    const result = await this.findPageComponents({\n      locale: getRequestMeta(ctx.req, 'locale'),\n      page,\n      query,\n      params: ctx.renderOpts.params || {},\n      isAppPath,\n      sriEnabled: !!this.nextConfig.experimental.sri?.algorithm,\n      appPaths,\n      // Ensuring for loading page component routes is done via the matcher.\n      shouldEnsure: false,\n    })\n    if (result) {\n      getTracer().setRootSpanAttribute('next.route', pathname)\n      try {\n        return await this.renderToResponseWithComponents(ctx, result)\n      } catch (err) {\n        const isNoFallbackError = err instanceof NoFallbackError\n\n        if (!isNoFallbackError || (isNoFallbackError && bubbleNoFallback)) {\n          throw err\n        }\n      }\n    }\n    return false\n  }\n\n  private async renderToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponse,\n      {\n        spanName: `rendering page`,\n        attributes: {\n          'next.route': ctx.pathname,\n        },\n      },\n      async () => {\n        return this.renderToResponseImpl(ctx)\n      }\n    )\n  }\n\n  protected abstract getMiddleware(): Promise<MiddlewareRoutingItem | undefined>\n  protected abstract getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null>\n  protected abstract getRoutesManifest(): NormalizedRouteManifest | undefined\n\n  private async renderToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    const { req, res, query, pathname } = ctx\n    let page = pathname\n    const bubbleNoFallback =\n      getRequestMeta(ctx.req, 'bubbleNoFallback') ?? false\n\n    if (\n      !this.minimalMode &&\n      this.nextConfig.experimental.validateRSCRequestHeaders\n    ) {\n      addRequestMeta(\n        ctx.req,\n        'cacheBustingSearchParam',\n        query[NEXT_RSC_UNION_QUERY]\n      )\n    }\n    delete query[NEXT_RSC_UNION_QUERY]\n\n    const options: MatchOptions = {\n      i18n: this.i18nProvider?.fromRequest(req, pathname),\n    }\n\n    try {\n      for await (const match of this.matchers.matchAll(pathname, options)) {\n        // when a specific invoke-output is meant to be matched\n        // ensure a prior dynamic route/page doesn't take priority\n        const invokeOutput = getRequestMeta(ctx.req, 'invokeOutput')\n        if (\n          !this.minimalMode &&\n          typeof invokeOutput === 'string' &&\n          isDynamicRoute(invokeOutput || '') &&\n          invokeOutput !== match.definition.pathname\n        ) {\n          continue\n        }\n\n        const result = await this.renderPageComponent(\n          {\n            ...ctx,\n            pathname: match.definition.pathname,\n            renderOpts: {\n              ...ctx.renderOpts,\n              params: match.params,\n            },\n          },\n          bubbleNoFallback\n        )\n        if (result !== false) return result\n      }\n\n      // currently edge functions aren't receiving the x-matched-path\n      // header so we need to fallback to matching the current page\n      // when we weren't able to match via dynamic route to handle\n      // the rewrite case\n      // @ts-expect-error extended in child class web-server\n      if (this.serverOptions.webServerConfig) {\n        // @ts-expect-error extended in child class web-server\n        ctx.pathname = this.serverOptions.webServerConfig.page\n        const result = await this.renderPageComponent(ctx, bubbleNoFallback)\n        if (result !== false) return result\n      }\n    } catch (error) {\n      const err = getProperError(error)\n\n      if (error instanceof MissingStaticPage) {\n        console.error(\n          'Invariant: failed to load static page',\n          JSON.stringify(\n            {\n              page,\n              url: ctx.req.url,\n              matchedPath: ctx.req.headers[MATCHED_PATH_HEADER],\n              initUrl: getRequestMeta(ctx.req, 'initURL'),\n              didRewrite: !!getRequestMeta(ctx.req, 'rewroteURL'),\n              rewroteUrl: getRequestMeta(ctx.req, 'rewroteURL'),\n            },\n            null,\n            2\n          )\n        )\n        throw err\n      }\n\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        throw err\n      }\n      if (err instanceof DecodeError || err instanceof NormalizeError) {\n        res.statusCode = 400\n        return await this.renderErrorToResponse(ctx, err)\n      }\n\n      res.statusCode = 500\n\n      // if pages/500 is present we still need to trigger\n      // /_error `getInitialProps` to allow reporting error\n      if (await this.hasPage('/500')) {\n        addRequestMeta(ctx.req, 'customErrorRender', true)\n        await this.renderErrorToResponse(ctx, err)\n        removeRequestMeta(ctx.req, 'customErrorRender')\n      }\n\n      const isWrappedError = err instanceof WrappedBuildError\n\n      if (!isWrappedError) {\n        if (this.minimalMode || this.renderOpts.dev) {\n          if (isError(err)) err.page = page\n          throw err\n        }\n        this.logError(getProperError(err))\n      }\n      const response = await this.renderErrorToResponse(\n        ctx,\n        isWrappedError ? (err as WrappedBuildError).innerError : err\n      )\n      return response\n    }\n\n    const middleware = await this.getMiddleware()\n    if (\n      middleware &&\n      !!ctx.req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200 || res.statusCode === 404)\n    ) {\n      const locale = getRequestMeta(req, 'locale')\n\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n      res.statusCode = 200\n      res.setHeader('Content-Type', JSON_CONTENT_TYPE_HEADER)\n      res.body('{}')\n      res.send()\n      return null\n    }\n\n    res.statusCode = 404\n    return this.renderErrorToResponse(ctx, null)\n  }\n\n  public async renderToHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return getTracer().trace(BaseServerSpan.renderToHTML, async () => {\n      return this.renderToHTMLImpl(req, res, pathname, query)\n    })\n  }\n\n  private async renderToHTMLImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.renderError, async () => {\n      return this.renderErrorImpl(err, req, res, pathname, query, setHeaders)\n    })\n  }\n\n  private async renderErrorImpl(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    if (setHeaders) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    return this.pipe(\n      async (ctx) => {\n        const response = await this.renderErrorToResponse(ctx, err)\n        if (this.minimalMode && res.statusCode === 500) {\n          throw err\n        }\n        return response\n      },\n      { req, res, pathname, query }\n    )\n  }\n\n  private customErrorNo404Warn = execOnce(() => {\n    Log.warn(\n      `You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`\n    )\n  })\n\n  private async renderErrorToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(BaseServerSpan.renderErrorToResponse, async () => {\n      return this.renderErrorToResponseImpl(ctx, err)\n    })\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    // Short-circuit favicon.ico in development to avoid compiling 404 page when the app has no favicon.ico.\n    // Since favicon.ico is automatically requested by the browser.\n    if (this.renderOpts.dev && ctx.pathname === '/favicon.ico') {\n      return {\n        body: RenderResult.EMPTY,\n      }\n    }\n    const { res, query } = ctx\n\n    try {\n      let result: null | FindComponentsResult = null\n\n      const is404 = res.statusCode === 404\n      let using404Page = false\n\n      if (is404) {\n        if (this.enabledDirectories.app) {\n          // Use the not-found entry in app directory\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n            query,\n            params: {},\n            isAppPath: true,\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n\n        if (!result && (await this.hasPage('/404'))) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: '/404',\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 404 route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n      }\n      let statusPage = `/${res.statusCode}`\n\n      if (\n        !getRequestMeta(ctx.req, 'customErrorRender') &&\n        !result &&\n        STATIC_STATUS_PAGES.includes(statusPage)\n      ) {\n        // skip ensuring /500 in dev mode as it isn't used and the\n        // dev overlay is used instead\n        if (statusPage !== '/500' || !this.renderOpts.dev) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: statusPage,\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 500\n            // route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n        }\n      }\n\n      if (!result) {\n        result = await this.findPageComponents({\n          locale: getRequestMeta(ctx.req, 'locale'),\n          page: '/_error',\n          query,\n          params: {},\n          isAppPath: false,\n          // Ensuring can't be done here because you never \"match\" an error\n          // route.\n          shouldEnsure: true,\n          url: ctx.req.url,\n        })\n        statusPage = '/_error'\n      }\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !using404Page &&\n        (await this.hasPage('/_error')) &&\n        !(await this.hasPage('/404'))\n      ) {\n        this.customErrorNo404Warn()\n      }\n\n      if (!result) {\n        // this can occur when a project directory has been moved/deleted\n        // which is handled in the parent process in development\n        if (this.renderOpts.dev) {\n          return {\n            // wait for dev-server to restart before refreshing\n            body: RenderResult.fromStatic(\n              `\n              <pre>missing required error components, refreshing...</pre>\n              <script>\n                async function check() {\n                  const res = await fetch(location.href).catch(() => ({}))\n\n                  if (res.status === 200) {\n                    location.reload()\n                  } else {\n                    setTimeout(check, 1000)\n                  }\n                }\n                check()\n              </script>`,\n              HTML_CONTENT_TYPE_HEADER\n            ),\n          }\n        }\n\n        throw new WrappedBuildError(\n          new Error('missing required error components')\n        )\n      }\n\n      // If the page has a route module, use it for the new match. If it doesn't\n      // have a route module, remove the match.\n      if (result.components.routeModule) {\n        addRequestMeta(ctx.req, 'match', {\n          definition: result.components.routeModule.definition,\n          params: undefined,\n        })\n      } else {\n        removeRequestMeta(ctx.req, 'match')\n      }\n\n      try {\n        return await this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: statusPage,\n            renderOpts: {\n              ...ctx.renderOpts,\n              err,\n            },\n          },\n          result\n        )\n      } catch (maybeFallbackError) {\n        if (maybeFallbackError instanceof NoFallbackError) {\n          throw new Error('invariant: failed to render error page')\n        }\n        throw maybeFallbackError\n      }\n    } catch (error) {\n      const renderToHtmlError = getProperError(error)\n      const isWrappedError = renderToHtmlError instanceof WrappedBuildError\n      if (!isWrappedError) {\n        this.logError(renderToHtmlError)\n      }\n      res.statusCode = 500\n      const fallbackComponents = await this.getFallbackErrorComponents(\n        ctx.req.url\n      )\n\n      if (fallbackComponents) {\n        // There was an error, so use it's definition from the route module\n        // to add the match to the request.\n        addRequestMeta(ctx.req, 'match', {\n          definition: fallbackComponents.routeModule!.definition,\n          params: undefined,\n        })\n\n        return this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: '/_error',\n            renderOpts: {\n              ...ctx.renderOpts,\n              // We render `renderToHtmlError` here because `err` is\n              // already captured in the stacktrace.\n              err: isWrappedError\n                ? renderToHtmlError.innerError\n                : renderToHtmlError,\n            },\n          },\n          {\n            query,\n            components: fallbackComponents,\n          }\n        )\n      }\n      return {\n        body: RenderResult.fromStatic('Internal Server Error', 'text/plain'),\n      }\n    }\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderErrorToResponse(ctx, err), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async render404(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: Pick<NextUrlWithParsedQuery, 'pathname' | 'query'>,\n    setHeaders = true\n  ): Promise<void> {\n    const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(req.url!, true)\n\n    // Ensure the locales are provided on the request meta.\n    if (this.nextConfig.i18n) {\n      if (!getRequestMeta(req, 'locale')) {\n        addRequestMeta(req, 'locale', this.nextConfig.i18n.defaultLocale)\n      }\n      addRequestMeta(req, 'defaultLocale', this.nextConfig.i18n.defaultLocale)\n    }\n\n    res.statusCode = 404\n    return this.renderError(null, req, res, pathname!, query, setHeaders)\n  }\n}\n"], "names": ["NormalizeError", "DecodeError", "normalizeRepeatedSlashes", "MissingStaticPage", "format", "formatUrl", "parse", "parseUrl", "formatHostname", "APP_PATHS_MANIFEST", "NEXT_BUILTIN_DOCUMENT", "PAGES_MANIFEST", "STATIC_STATUS_PAGES", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "isDynamicRoute", "setConfig", "execOnce", "isBlockedPage", "getBotType", "isBot", "RenderResult", "removeTrailingSlash", "denormalizePagePath", "Log", "getPreviouslyRevalidatedTags", "getServerUtils", "isError", "getProperError", "addRequestMeta", "getRequestMeta", "removeRequestMeta", "setRequestMeta", "removePathPrefix", "normalizeAppPath", "getHostname", "parseUrlUtil", "getNextPathnameInfo", "RSC_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_URL", "NEXT_ROUTER_STATE_TREE_HEADER", "LocaleRouteNormalizer", "DefaultRouteMatcherManager", "AppPageRouteMatcherProvider", "AppRouteRouteMatcherProvider", "PagesAPIRouteMatcherProvider", "PagesRouteMatcherProvider", "ServerManifestLoader", "getTracer", "isBubbledError", "SpanKind", "SpanStatusCode", "BaseServerSpan", "I18NProvider", "sendResponse", "normalizeNextQueryParam", "HTML_CONTENT_TYPE_HEADER", "JSON_CONTENT_TYPE_HEADER", "MATCHED_PATH_HEADER", "NEXT_RESUME_HEADER", "normalizeLocalePath", "matchNextDataPathname", "getRouteFromAssetPath", "RSCPathnameNormalizer", "stripFlightHeaders", "isAppPageRouteModule", "isAppRouteRouteModule", "PrefetchRSCPathnameNormalizer", "NextDataPathnameNormalizer", "getIsPossibleServerAction", "isInterceptionRouteAppPath", "toRoute", "isNodeNextRequest", "isNodeNextResponse", "patchSetHeaderWithCookieSupport", "checkIsAppPPREnabled", "getBuiltinRequestContext", "NextRequestHint", "parseFallbackField", "SegmentPrefixRSCPathnameNormalizer", "shouldServeStreamingMetadata", "decodeQueryPathParameter", "NoFallbackError", "getCacheHandlers", "fixMojibake", "computeCacheBustingSearchParam", "setCacheBustingSearchParamWithHash", "WrappedBuildError", "Error", "constructor", "innerError", "Server", "getServerComponentsHmrCache", "nextConfig", "experimental", "serverComponentsHmrCache", "globalThis", "__serverComponentsHmrCache", "undefined", "options", "handleRSCRequest", "req", "_res", "parsedUrl", "pathname", "normalizers", "segmentPrefetchRSC", "match", "result", "extract", "originalPathname", "segmentPath", "headers", "prefetchRSC", "normalize", "rsc", "segmentPrefetchRSCRequest", "url", "parsed", "handleNextDataRequest", "res", "middleware", "getMiddleware", "params", "path", "buildId", "render404", "shift", "lastPara<PERSON>", "length", "endsWith", "join", "trailingSlash", "substring", "i18nProvider", "hostname", "host", "split", "toLowerCase", "domainLocale", "detectDomainLocale", "defaultLocale", "config", "localePathResult", "analyze", "detectedLocale", "handleNextImageRequest", "handleCatchallRenderRequest", "handleCatchallMiddlewareRequest", "data", "push", "normalizer", "normalizeAndAttachMetadata", "finished", "enabledDirectories", "pages", "prepared", "preparedPromise", "customErrorNo404Warn", "warn", "dir", "quiet", "conf", "dev", "minimalMode", "port", "experimentalTestProxy", "serverOptions", "require", "resolve", "loadEnvConfig", "fetchHostname", "distDir", "publicDir", "getPublicDir", "hasStaticDir", "getHasStaticDir", "i18n", "locales", "localeNormalizer", "serverRuntimeConfig", "publicRuntimeConfig", "assetPrefix", "generateEtags", "getBuildId", "minimalModeKey", "process", "env", "NEXT_PRIVATE_MINIMAL_MODE", "getEnabledDirectories", "isAppPPREnabled", "app", "ppr", "isAppSegmentPrefetchEnabled", "clientSegmentCache", "nextFontManifest", "getNextFontManifest", "NEXT_DEPLOYMENT_ID", "deploymentId", "renderOpts", "supportsDynamicResponse", "poweredByHeader", "canonicalBase", "amp", "previewProps", "getPrerenderManifest", "preview", "ampOptimizerConfig", "optimizer", "basePath", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "domains", "serverComponents", "cacheLifeProfiles", "cacheLife", "enableTainting", "taint", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "isExperimentalCompile", "htmlLimitedBots", "expireTime", "staleTimes", "clientTraceMetadata", "cacheComponents", "Boolean", "clientParamParsing", "dynamicOnHover", "inlineCss", "authInterrupts", "onInstrumentationRequestError", "instrumentationOnRequestError", "bind", "reactMaxHeadersLength", "devtoolSegmentExplorer", "pagesManifest", "getPagesManifest", "appPathsManifest", "getAppPathsManifest", "appPathRoutes", "getAppPathRoutes", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "matchers", "getRouteMatchers", "reload", "setAssetPrefix", "responseCache", "getResponseCache", "manifest<PERSON><PERSON>der", "name", "args", "err", "ctx", "instrumentation", "onRequestError", "method", "fromEntries", "entries", "handlerErr", "console", "error", "logError", "handleRequest", "prepare", "toUpperCase", "tracer", "withPropagatedContext", "trace", "spanName", "kind", "SERVER", "attributes", "span", "handleRequestImpl", "finally", "isRSCRequest", "setAttributes", "statusCode", "setStatus", "code", "ERROR", "setAttribute", "toString", "rootSpanAttributes", "getRootSpanAttributes", "get", "route", "updateName", "originalRequest", "waitTillReady", "originalResponse", "urlParts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanUrl", "redirect", "body", "send", "query", "URLSearchParams", "xForwardedProto", "isHttps", "socket", "encrypted", "remoteAddress", "attachRequestMeta", "replace", "pathnameInfo", "useMatchedPathHeader", "<PERSON><PERSON><PERSON>", "URL", "urlPathname", "chunk", "postponed", "<PERSON><PERSON><PERSON>", "concat", "normalizedUrlPath", "stripNextDataPath", "localeAnalysisResult", "inferredFromDefault", "srcPathname", "pageIsDynamic", "paramsResult", "hasValidParams", "definition", "utils", "page", "rewrites", "getRoutesManifest", "beforeFiles", "afterFiles", "fallback", "caseSensitive", "caseSensitiveRoutes", "locale", "originQueryParams", "pathnameBeforeRewrite", "rewriteParamKeys", "handleRewrites", "queryParams", "didRewrite", "routeParamKeys", "Set", "key", "value", "normalizedKey", "add", "Array", "isArray", "map", "v", "normalizeDynamicRouteParams", "matcherParams", "dynamicRouteMatcher", "assign", "curParamsResult", "routeMatchesHeader", "routeMatches", "getParamsFromRouteMatches", "defaultRouteMatches", "interpolateDynamicPath", "normalizeCdnUrl", "defaultRouteRegex", "groups", "renderError", "webServerConfig", "incrementalCache", "getIncrementalCache", "requestHeaders", "resetRequestCache", "__incrementalCache", "cacheHandlers", "Promise", "all", "cache<PERSON><PERSON><PERSON>", "previouslyRevalidatedTags", "previewModeId", "receiveExpiredTags", "invoke<PERSON><PERSON>", "useInvokePath", "invoke<PERSON>tatus", "invoke<PERSON><PERSON>y", "parsedMatchedPath", "invokePathnameInfo", "parseData", "normalizeResult", "response", "Response", "bubble", "run", "getRequestHandlerWithMetadata", "meta", "handler", "getRequestHandler", "prefix", "loadInstrumentationModule", "prepareImpl", "then", "close", "for<PERSON>ach", "entry", "normalizedPath", "runImpl", "pipe", "fn", "partialContext", "pipeImpl", "ua", "botType", "serveStreamingMetadata", "payload", "originalStatus", "cacheControl", "sent", "<PERSON><PERSON><PERSON><PERSON>", "expire", "sendRenderResult", "getStaticHTML", "toUnchunkedString", "render", "internalRender", "renderImpl", "getWaitUntil", "builtinRequestContext", "waitUntil", "getInternalWaitUntil", "startsWith", "customServer", "hasPage", "renderToResponse", "getStaticPaths", "fallback<PERSON><PERSON>", "dynamicRoutes", "staticPaths", "fallbackMode", "renderToResponseWithComponents", "requestContext", "findComponentsResult", "renderToResponseWithComponentsImpl", "pathCouldBeIntercepted", "resolvedPathname", "some", "regexp", "test", "set<PERSON>aryH<PERSON>er", "isAppPath", "baseVaryHeader", "addedNextUrlToVary", "append<PERSON><PERSON>er", "opts", "components", "prerenderManifest", "isErrorPathname", "is404Page", "is500Page", "hasServerProps", "getServerSideProps", "isPossibleServerAction", "isSSG", "getStaticProps", "validateRSCRequestHeaders", "prefetchHeaderValue", "routerPrefetch", "expectedHash", "actualHash", "searchParams", "search", "resolvedUrlPathname", "<PERSON><PERSON><PERSON><PERSON>", "includes", "routes", "isNextDataRequest", "routeModule", "couldSupportPPR", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "isRoutePPREnabled", "renderingMode", "minimalPostponed", "parseInt", "slice", "Component", "fromStatic", "isBotRequest", "isSupportedDocument", "Document", "getInitialProps", "isDev", "pathsResults", "prerenderedRoutes", "smallestFallbackRouteParams", "fallbackRouteParams", "devValidatingFallbackParams", "Map", "status", "request", "parsedInitUrl", "initPathname", "ampValidator", "ComponentMod", "maybeDevRequest", "NODE_ENV", "Proxy", "target", "prop", "set", "fetchMetrics", "stripLocale", "splitPath", "indexOf", "getOriginalAppPaths", "originalAppPath", "renderPageComponent", "bubbleNoFallback", "appPaths", "findPageComponents", "sriEnabled", "sri", "algorithm", "shouldEnsure", "setRootSpanAttribute", "isNoFallbackError", "renderToResponseImpl", "fromRequest", "matchAll", "invokeOutput", "JSON", "stringify", "initUrl", "rewroteUrl", "renderErrorToResponse", "isWrappedError", "renderToHTML", "renderToHTMLImpl", "setHeaders", "renderErrorImpl", "renderErrorToResponseImpl", "EMPTY", "is404", "using404Page", "statusPage", "maybeFallbackError", "renderToHtmlError", "fallbackComponents", "getFallbackErrorComponents", "renderErrorToHTML"], "mappings": "AAqBA,SACEA,cAAc,EACdC,WAAW,EACXC,wBAAwB,EACxBC,iBAAiB,QACZ,sBAAqB;AAqB5B,SAASC,UAAUC,SAAS,EAAEC,SAASC,QAAQ,QAAQ,MAAK;AAC5D,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SACEC,kBAAkB,EAClBC,qBAAqB,EACrBC,cAAc,EACdC,mBAAmB,EACnBC,0BAA0B,EAC1BC,gCAAgC,QAC3B,0BAAyB;AAChC,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SAASC,QAAQ,QAAQ,sBAAqB;AAC9C,SAASC,aAAa,QAAQ,UAAS;AACvC,SAASC,UAAU,EAAEC,KAAK,QAAQ,oCAAmC;AACrE,OAAOC,kBAAkB,kBAAiB;AAC1C,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,4BAA4B,EAAEC,cAAc,QAAQ,iBAAgB;AAC7E,OAAOC,WAAWC,cAAc,QAAQ,kBAAiB;AACzD,SACEC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,cAAc,QACT,iBAAgB;AACvB,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SAASC,WAAW,QAAQ,6BAA4B;AACxD,SAAS5B,YAAY6B,YAAY,QAAQ,uCAAsC;AAC/E,SAASC,mBAAmB,QAAQ,oDAAmD;AACvF,SACEC,UAAU,EACVC,oBAAoB,EACpBC,2BAA2B,EAC3BC,mCAAmC,EACnCC,QAAQ,EACRC,6BAA6B,QACxB,0CAAyC;AAKhD,SAASC,qBAAqB,QAAQ,wCAAuC;AAC7E,SAASC,0BAA0B,QAAQ,yDAAwD;AACnG,SAASC,2BAA2B,QAAQ,4DAA2D;AACvG,SAASC,4BAA4B,QAAQ,6DAA4D;AACzG,SAASC,4BAA4B,QAAQ,6DAA4D;AACzG,SAASC,yBAAyB,QAAQ,yDAAwD;AAClG,SAASC,oBAAoB,QAAQ,4EAA2E;AAChH,SACEC,SAAS,EACTC,cAAc,EACdC,QAAQ,EACRC,cAAc,QACT,qBAAoB;AAC3B,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SAASC,YAAY,QAAQ,sBAAqB;AAClD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,uBAAuB,QAAQ,cAAa;AACrD,SACEC,wBAAwB,EACxBC,wBAAwB,EACxBC,mBAAmB,EACnBC,kBAAkB,QACb,mBAAkB;AACzB,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,OAAOC,2BAA2B,uDAAsD;AACxF,SAASC,qBAAqB,QAAQ,4BAA2B;AACjE,SAASC,kBAAkB,QAAQ,oCAAmC;AACtE,SACEC,oBAAoB,EACpBC,qBAAqB,QAChB,yBAAwB;AAC/B,SAASC,6BAA6B,QAAQ,qCAAoC;AAClF,SAASC,0BAA0B,QAAQ,kCAAiC;AAC5E,SAASC,yBAAyB,QAAQ,mCAAkC;AAC5E,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,OAAO,QAAQ,iBAAgB;AAExC,SAASC,iBAAiB,EAAEC,kBAAkB,QAAQ,sBAAqB;AAC3E,SAASC,+BAA+B,QAAQ,yBAAwB;AACxE,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SACEC,wBAAwB,QAEnB,kCAAiC;AACxC,SAASC,eAAe,QAAQ,gBAAe;AAE/C,SAA4BC,kBAAkB,QAAQ,kBAAiB;AACvE,SAASC,kCAAkC,QAAQ,2CAA0C;AAC7F,SAASC,4BAA4B,QAAQ,2BAA0B;AACvE,SAASC,wBAAwB,QAAQ,oCAAmC;AAC5E,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,WAAW,QAAQ,qBAAoB;AAChD,SAASC,8BAA8B,QAAQ,wDAAuD;AACtG,SAASC,kCAAkC,QAAQ,qEAAoE;AAqIvH,sDAAsD;AACtD,uDAAuD;AACvD,OAAO,MAAMC,0BAA0BC;IAGrCC,YAAYC,UAAiB,CAAE;QAC7B,KAAK;QACL,IAAI,CAACA,UAAU,GAAGA;IACpB;AACF;AAYA,eAAe,MAAeC;IAgGlBC,8BAEI;QACZ,OAAO,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,wBAAwB,GACxD,AAACC,WAAmBC,0BAA0B,GAC9CC;IACN;IAsBA;;;;GAIC,GAED,YAAmBC,OAAsB,CAAE;YAoCrB,uBAoEE,mCAQL;aAgEXC,mBAAgE,CACtEC,KACAC,MACAC;gBAII,sCAiBO,+BAWA;YA9BX,IAAI,CAACA,UAAUC,QAAQ,EAAE,OAAO;YAEhC,KAAI,uCAAA,IAAI,CAACC,WAAW,CAACC,kBAAkB,qBAAnC,qCAAqCC,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClE,MAAMI,SAAS,IAAI,CAACH,WAAW,CAACC,kBAAkB,CAACG,OAAO,CACxDN,UAAUC,QAAQ;gBAEpB,IAAI,CAACI,QAAQ,OAAO;gBAEpB,MAAM,EAAEE,gBAAgB,EAAEC,WAAW,EAAE,GAAGH;gBAC1CL,UAAUC,QAAQ,GAAGM;gBAErB,iDAAiD;gBACjDT,IAAIW,OAAO,CAAC7E,WAAW,GAAG;gBAC1BkE,IAAIW,OAAO,CAAC3E,4BAA4B,GAAG;gBAC3CgE,IAAIW,OAAO,CAAC1E,oCAAoC,GAAGyE;gBAEnDrF,eAAe2E,KAAK,gBAAgB;gBACpC3E,eAAe2E,KAAK,wBAAwB;gBAC5C3E,eAAe2E,KAAK,6BAA6BU;YACnD,OAAO,KAAI,gCAAA,IAAI,CAACN,WAAW,CAACQ,WAAW,qBAA5B,8BAA8BN,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClED,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACQ,WAAW,CAACC,SAAS,CACzDX,UAAUC,QAAQ,EAClB;gBAGF,iDAAiD;gBACjDH,IAAIW,OAAO,CAAC7E,WAAW,GAAG;gBAC1BkE,IAAIW,OAAO,CAAC3E,4BAA4B,GAAG;gBAC3CX,eAAe2E,KAAK,gBAAgB;gBACpC3E,eAAe2E,KAAK,wBAAwB;YAC9C,OAAO,KAAI,wBAAA,IAAI,CAACI,WAAW,CAACU,GAAG,qBAApB,sBAAsBR,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC1DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACU,GAAG,CAACD,SAAS,CACjDX,UAAUC,QAAQ,EAClB;gBAGF,qCAAqC;gBACrCH,IAAIW,OAAO,CAAC7E,WAAW,GAAG;gBAC1BT,eAAe2E,KAAK,gBAAgB;YACtC,OAAO,IAAIA,IAAIW,OAAO,CAAC,sBAAsB,EAAE;gBAC7C,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,uEAAuE;gBACvE,uCAAuC;gBACvChD,mBAAmBqC,IAAIW,OAAO;gBAE9B,OAAO;YACT,OAAO,IAAIX,IAAIW,OAAO,CAAC7E,WAAW,KAAK,KAAK;gBAC1CT,eAAe2E,KAAK,gBAAgB;gBAEpC,IAAIA,IAAIW,OAAO,CAAC3E,4BAA4B,KAAK,KAAK;oBACpDX,eAAe2E,KAAK,wBAAwB;oBAE5C,MAAMe,4BACJf,IAAIW,OAAO,CAAC1E,oCAAoC;oBAClD,IAAI,OAAO8E,8BAA8B,UAAU;wBACjD1F,eACE2E,KACA,6BACAe;oBAEJ;gBACF;YACF,OAAO;gBACL,gDAAgD;gBAChD,OAAO;YACT;YAEA,IAAIf,IAAIgB,GAAG,EAAE;gBACX,MAAMC,SAASlH,SAASiG,IAAIgB,GAAG;gBAC/BC,OAAOd,QAAQ,GAAGD,UAAUC,QAAQ;gBACpCH,IAAIgB,GAAG,GAAGnH,UAAUoH;YACtB;YAEA,OAAO;QACT;aAEQC,wBACN,OAAOlB,KAAKmB,KAAKjB;YACf,MAAMkB,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,MAAMC,SAAS9D,sBAAsB0C,UAAUC,QAAQ;YAEvD,gCAAgC;YAChC,IAAI,CAACmB,UAAU,CAACA,OAAOC,IAAI,EAAE;gBAC3B,OAAO;YACT;YAEA,IAAID,OAAOC,IAAI,CAAC,EAAE,KAAK,IAAI,CAACC,OAAO,EAAE;gBACnC,6DAA6D;gBAC7D,IAAIlG,eAAe0E,KAAK,qBAAqB;oBAC3C,OAAO;gBACT;gBAEA,gDAAgD;gBAChD,MAAM,IAAI,CAACyB,SAAS,CAACzB,KAAKmB,KAAKjB;gBAC/B,OAAO;YACT;YAEA,0BAA0B;YAC1BoB,OAAOC,IAAI,CAACG,KAAK;YAEjB,MAAMC,YAAYL,OAAOC,IAAI,CAACD,OAAOC,IAAI,CAACK,MAAM,GAAG,EAAE;YAErD,wCAAwC;YACxC,IAAI,OAAOD,cAAc,YAAY,CAACA,UAAUE,QAAQ,CAAC,UAAU;gBACjE,MAAM,IAAI,CAACJ,SAAS,CAACzB,KAAKmB,KAAKjB;gBAC/B,OAAO;YACT;YAEA,4BAA4B;YAC5B,IAAIC,WAAW,CAAC,CAAC,EAAEmB,OAAOC,IAAI,CAACO,IAAI,CAAC,MAAM;YAC1C3B,WAAW1C,sBAAsB0C,UAAU;YAE3C,iDAAiD;YACjD,IAAIiB,YAAY;gBACd,IAAI,IAAI,CAAC5B,UAAU,CAACuC,aAAa,IAAI,CAAC5B,SAAS0B,QAAQ,CAAC,MAAM;oBAC5D1B,YAAY;gBACd;gBACA,IACE,CAAC,IAAI,CAACX,UAAU,CAACuC,aAAa,IAC9B5B,SAASyB,MAAM,GAAG,KAClBzB,SAAS0B,QAAQ,CAAC,MAClB;oBACA1B,WAAWA,SAAS6B,SAAS,CAAC,GAAG7B,SAASyB,MAAM,GAAG;gBACrD;YACF;YAEA,IAAI,IAAI,CAACK,YAAY,EAAE;oBAEJjC;gBADjB,gDAAgD;gBAChD,MAAMkC,WAAWlC,wBAAAA,oBAAAA,IAAKW,OAAO,CAACwB,IAAI,qBAAjBnC,kBAAmBoC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACC,WAAW;gBAEhE,MAAMC,eAAe,IAAI,CAACL,YAAY,CAACM,kBAAkB,CAACL;gBAC1D,MAAMM,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,KAAI,IAAI,CAACP,YAAY,CAACQ,MAAM,CAACD,aAAa;gBAEvE,MAAME,mBAAmB,IAAI,CAACT,YAAY,CAACU,OAAO,CAACxC;gBAEnD,gEAAgE;gBAChE,qBAAqB;gBACrB,IAAIuC,iBAAiBE,cAAc,EAAE;oBACnCzC,WAAWuC,iBAAiBvC,QAAQ;gBACtC;gBAEA,gEAAgE;gBAChE9E,eAAe2E,KAAK,UAAU0C,iBAAiBE,cAAc;gBAC7DvH,eAAe2E,KAAK,iBAAiBwC;gBAErC,oEAAoE;gBACpE,oCAAoC;gBACpC,IAAI,CAACE,iBAAiBE,cAAc,EAAE;oBACpCrH,kBAAkByE,KAAK;gBACzB;gBAEA,kEAAkE;gBAClE,wBAAwB;gBACxB,IAAI,CAAC0C,iBAAiBE,cAAc,IAAI,CAACxB,YAAY;oBACnD/F,eAAe2E,KAAK,UAAUwC;oBAC9B,MAAM,IAAI,CAACf,SAAS,CAACzB,KAAKmB,KAAKjB;oBAC/B,OAAO;gBACT;YACF;YAEAA,UAAUC,QAAQ,GAAGA;YACrB9E,eAAe2E,KAAK,iBAAiB;YAErC,OAAO;QACT;aAEQ6C,yBAGN,IAAM;aAEAC,8BAGN,IAAM;aAEAC,kCAGN,IAAM;QA4xBV;;;;;;GAMC,QACOlC,YAAY,CAACV;YACnB,MAAMC,cAAyC,EAAE;YAEjD,IAAI,IAAI,CAACA,WAAW,CAAC4C,IAAI,EAAE;gBACzB5C,YAAY6C,IAAI,CAAC,IAAI,CAAC7C,WAAW,CAAC4C,IAAI;YACxC;YAEA,2EAA2E;YAC3E,qEAAqE;YACrE,IAAI,IAAI,CAAC5C,WAAW,CAACC,kBAAkB,EAAE;gBACvCD,YAAY6C,IAAI,CAAC,IAAI,CAAC7C,WAAW,CAACC,kBAAkB;YACtD;YAEA,mEAAmE;YACnE,qEAAqE;YACrE,IAAI,IAAI,CAACD,WAAW,CAACQ,WAAW,EAAE;gBAChCR,YAAY6C,IAAI,CAAC,IAAI,CAAC7C,WAAW,CAACQ,WAAW;YAC/C;YAEA,IAAI,IAAI,CAACR,WAAW,CAACU,GAAG,EAAE;gBACxBV,YAAY6C,IAAI,CAAC,IAAI,CAAC7C,WAAW,CAACU,GAAG;YACvC;YAEA,KAAK,MAAMoC,cAAc9C,YAAa;gBACpC,IAAI,CAAC8C,WAAW5C,KAAK,CAACH,WAAW;gBAEjC,OAAO+C,WAAWrC,SAAS,CAACV,UAAU;YACxC;YAEA,OAAOA;QACT;aAEQgD,6BAGJ,OAAOnD,KAAKmB,KAAKH;YACnB,IAAIoC,WAAW,MAAM,IAAI,CAACP,sBAAsB,CAAC7C,KAAKmB,KAAKH;YAC3D,IAAIoC,UAAU,OAAO;YAErB,IAAI,IAAI,CAACC,kBAAkB,CAACC,KAAK,EAAE;gBACjCF,WAAW,MAAM,IAAI,CAAClC,qBAAqB,CAAClB,KAAKmB,KAAKH;gBACtD,IAAIoC,UAAU,OAAO;YACvB;YAEA,OAAO;QACT;aAgCUG,WAAoB;aACpBC,kBAAwC;aAohC1CC,uBAAuBhJ,SAAS;YACtCO,IAAI0I,IAAI,CACN,CAAC,iNAAiN,CAAC;QAEvN;QAnvEE,MAAM,EACJC,MAAM,GAAG,EACTC,QAAQ,KAAK,EACbC,IAAI,EACJC,MAAM,KAAK,EACXC,cAAc,KAAK,EACnB7B,QAAQ,EACR8B,IAAI,EACJC,qBAAqB,EACtB,GAAGnE;QAEJ,IAAI,CAACmE,qBAAqB,GAAGA;QAC7B,IAAI,CAACC,aAAa,GAAGpE;QAErB,IAAI,CAAC6D,GAAG,GAAG,AAACQ,QAAQ,QAAkCC,OAAO,CAACT;QAE9D,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACS,aAAa,CAAC;YAAEP;QAAI;QAEzB,qDAAqD;QACrD,0DAA0D;QAC1D,IAAI,CAACtE,UAAU,GAAGqE;QAClB,IAAI,CAAC3B,QAAQ,GAAGA;QAChB,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB,mDAAmD;YACnD,IAAI,CAACoC,aAAa,GAAGtK,eAAe,IAAI,CAACkI,QAAQ;QACnD;QACA,IAAI,CAAC8B,IAAI,GAAGA;QACZ,IAAI,CAACO,OAAO,GAAG,AAACJ,QAAQ,QAAkCrC,IAAI,CAC5D,IAAI,CAAC6B,GAAG,EACR,IAAI,CAACnE,UAAU,CAAC+E,OAAO;QAEzB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY;QAClC,IAAI,CAACC,YAAY,GAAG,CAACX,eAAe,IAAI,CAACY,eAAe;QAExD,IAAI,CAAC1C,YAAY,GAAG,EAAA,wBAAA,IAAI,CAACzC,UAAU,CAACoF,IAAI,qBAApB,sBAAsBC,OAAO,IAC7C,IAAI7H,aAAa,IAAI,CAACwC,UAAU,CAACoF,IAAI,IACrC/E;QAEJ,yEAAyE;QACzE,IAAI,CAACiF,gBAAgB,GAAG,IAAI,CAAC7C,YAAY,GACrC,IAAI7F,sBAAsB,IAAI,CAAC6F,YAAY,IAC3CpC;QAEJ,6CAA6C;QAC7C,2DAA2D;QAC3D,MAAM,EACJkF,sBAAsB,CAAC,CAAC,EACxBC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACd,GAAG,IAAI,CAAC1F,UAAU;QAEnB,IAAI,CAACgC,OAAO,GAAG,IAAI,CAAC2D,UAAU;QAC9B,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAClBrB,eAAe,CAAC,CAACsB,QAAQC,GAAG,CAACC,yBAAyB;QAExD,IAAI,CAAClC,kBAAkB,GAAG,IAAI,CAACmC,qBAAqB,CAAC1B;QAErD,IAAI,CAAC2B,eAAe,GAClB,IAAI,CAACpC,kBAAkB,CAACqC,GAAG,IAC3BpH,qBAAqB,IAAI,CAACkB,UAAU,CAACC,YAAY,CAACkG,GAAG;QAEvD,IAAI,CAACC,2BAA2B,GAC9B,IAAI,CAACvC,kBAAkB,CAACqC,GAAG,IAC3B,IAAI,CAAClG,UAAU,CAACC,YAAY,CAACoG,kBAAkB,KAAK;QAEtD,IAAI,CAACzF,WAAW,GAAG;YACjB,uEAAuE;YACvE,wEAAwE;YACxE,uCAAuC;YACvCU,KACE,IAAI,CAACuC,kBAAkB,CAACqC,GAAG,IAAI,IAAI,CAAC3B,WAAW,GAC3C,IAAIrG,0BACJmC;YACNe,aACE,IAAI,CAAC6E,eAAe,IAAI,IAAI,CAAC1B,WAAW,GACpC,IAAIjG,kCACJ+B;YACNQ,oBACE,IAAI,CAACuF,2BAA2B,IAAI,IAAI,CAAC7B,WAAW,GAChD,IAAIrF,uCACJmB;YACNmD,MAAM,IAAI,CAACK,kBAAkB,CAACC,KAAK,GAC/B,IAAIvF,2BAA2B,IAAI,CAACyD,OAAO,IAC3C3B;QACN;QAEA,IAAI,CAACiG,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChDV,QAAQC,GAAG,CAACU,kBAAkB,GAAG,IAAI,CAACxG,UAAU,CAACyG,YAAY,IAAI;QAEjE,IAAI,CAACC,UAAU,GAAG;YAChBvC,KAAK,IAAI,CAACA,GAAG;YACbwC,yBAAyB;YACzBpE,eAAe,IAAI,CAACvC,UAAU,CAACuC,aAAa;YAC5CkE,cAAc,IAAI,CAACzG,UAAU,CAACyG,YAAY;YAC1CG,iBAAiB,IAAI,CAAC5G,UAAU,CAAC4G,eAAe;YAChDC,eAAe,IAAI,CAAC7G,UAAU,CAAC8G,GAAG,CAACD,aAAa,IAAI;YACpDnB;YACAqB,cAAc,IAAI,CAACC,oBAAoB,GAAGC,OAAO;YACjDC,kBAAkB,GAAE,oCAAA,IAAI,CAAClH,UAAU,CAACC,YAAY,CAAC6G,GAAG,qBAAhC,kCAAkCK,SAAS;YAC/DC,UAAU,IAAI,CAACpH,UAAU,CAACoH,QAAQ;YAClCC,QAAQ,IAAI,CAACrH,UAAU,CAACqH,MAAM;YAC9BC,aAAa,IAAI,CAACtH,UAAU,CAACC,YAAY,CAACqH,WAAW;YACrDC,kBAAkB,IAAI,CAACvH,UAAU,CAACwH,MAAM;YACxCC,mBAAmB,IAAI,CAACzH,UAAU,CAACC,YAAY,CAACwH,iBAAiB;YACjEC,yBACE,IAAI,CAAC1H,UAAU,CAACC,YAAY,CAACyH,uBAAuB;YACtDC,aAAa,GAAE,yBAAA,IAAI,CAAC3H,UAAU,CAACoF,IAAI,qBAApB,uBAAsBwC,OAAO;YAC5C7C,SAAS,IAAI,CAACA,OAAO;YACrB8C,kBAAkB,IAAI,CAAChE,kBAAkB,CAACqC,GAAG;YAC7C4B,mBAAmB,IAAI,CAAC9H,UAAU,CAACC,YAAY,CAAC8H,SAAS;YACzDC,gBAAgB,IAAI,CAAChI,UAAU,CAACC,YAAY,CAACgI,KAAK;YAClDC,aAAa,IAAI,CAAClI,UAAU,CAACkI,WAAW,GACpC,IAAI,CAAClI,UAAU,CAACkI,WAAW,GAC3B7H;YACJ8H,oBAAoB,IAAI,CAACnI,UAAU,CAACC,YAAY,CAACkI,kBAAkB;YACnE,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAAC9C,qBAAqBpD,MAAM,GAAG,IACtCoD,sBACAnF;YAENkI,uBAAuB,IAAI,CAACvI,UAAU,CAACC,YAAY,CAACsI,qBAAqB;YACzE,8EAA8E;YAC9EC,iBAAiB,IAAI,CAACxI,UAAU,CAACwI,eAAe;YAChDvI,cAAc;gBACZwI,YAAY,IAAI,CAACzI,UAAU,CAACyI,UAAU;gBACtCC,YAAY,IAAI,CAAC1I,UAAU,CAACC,YAAY,CAACyI,UAAU;gBACnDC,qBAAqB,IAAI,CAAC3I,UAAU,CAACC,YAAY,CAAC0I,mBAAmB;gBACrEC,iBAAiB,IAAI,CAAC5I,UAAU,CAACC,YAAY,CAAC2I,eAAe,IAAI;gBACjEvC,oBACE,IAAI,CAACrG,UAAU,CAACC,YAAY,CAACoG,kBAAkB,KAAK,gBAChD,gBACAwC,QAAQ,IAAI,CAAC7I,UAAU,CAACC,YAAY,CAACoG,kBAAkB;gBAC7DyC,oBACE,IAAI,CAAC9I,UAAU,CAACC,YAAY,CAAC6I,kBAAkB,IAAI;gBACrDC,gBAAgB,IAAI,CAAC/I,UAAU,CAACC,YAAY,CAAC8I,cAAc,IAAI;gBAC/DC,WAAW,IAAI,CAAChJ,UAAU,CAACC,YAAY,CAAC+I,SAAS,IAAI;gBACrDC,gBAAgB,CAAC,CAAC,IAAI,CAACjJ,UAAU,CAACC,YAAY,CAACgJ,cAAc;YAC/D;YACAC,+BACE,IAAI,CAACC,6BAA6B,CAACC,IAAI,CAAC,IAAI;YAC9CC,uBAAuB,IAAI,CAACrJ,UAAU,CAACqJ,qBAAqB;YAC5DC,wBACE,IAAI,CAACtJ,UAAU,CAACC,YAAY,CAACqJ,sBAAsB;QACvD;QAEA,4DAA4D;QAC5DtO,UAAU;YACRuK;YACAC;QACF;QAEA,IAAI,CAAC+D,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,wBAAwB;QACxB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;QAErC,0EAA0E;QAC1E,yEAAyE;QACzE,kDAAkD;QAClD,KAAK,IAAI,CAACD,QAAQ,CAACE,MAAM;QAEzB,IAAI,CAACC,cAAc,CAACzE;QACpB,IAAI,CAAC0E,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAAE9F;QAAI;IACnD;IA+LU0F,mBAAwC;QAChD,yEAAyE;QACzE,MAAMK,iBAAiB,IAAInN,qBAAqB,CAACoN;YAC/C,OAAQA;gBACN,KAAK3P;oBACH,OAAO,IAAI,CAAC6O,gBAAgB,MAAM;gBACpC,KAAK/O;oBACH,OAAO,IAAI,CAACiP,mBAAmB,MAAM;gBACvC;oBACE,OAAO;YACX;QACF;QAEA,uCAAuC;QACvC,MAAMK,WAAgC,IAAIlN;QAE1C,8BAA8B;QAC9BkN,SAAStG,IAAI,CACX,IAAIxG,0BACF,IAAI,CAAC8H,OAAO,EACZsF,gBACA,IAAI,CAAC5H,YAAY;QAIrB,uCAAuC;QACvCsH,SAAStG,IAAI,CACX,IAAIzG,6BACF,IAAI,CAAC+H,OAAO,EACZsF,gBACA,IAAI,CAAC5H,YAAY;QAIrB,2EAA2E;QAC3E,IAAI,IAAI,CAACoB,kBAAkB,CAACqC,GAAG,EAAE;YAC/B,gCAAgC;YAChC6D,SAAStG,IAAI,CACX,IAAI3G,4BAA4B,IAAI,CAACiI,OAAO,EAAEsF;YAEhDN,SAAStG,IAAI,CACX,IAAI1G,6BAA6B,IAAI,CAACgI,OAAO,EAAEsF;QAEnD;QAEA,OAAON;IACT;IAEA,MAAgBZ,8BACd,GAAGoB,IAAqD,EACxD;QACA,MAAM,CAACC,KAAKhK,KAAKiK,IAAI,GAAGF;QAExB,IAAI,IAAI,CAACG,eAAe,EAAE;YACxB,IAAI;gBACF,OAAM,IAAI,CAACA,eAAe,CAACC,cAAc,oBAAnC,IAAI,CAACD,eAAe,CAACC,cAAc,MAAnC,IAAI,CAACD,eAAe,EACxBF,KACA;oBACEzI,MAAMvB,IAAIgB,GAAG,IAAI;oBACjBoJ,QAAQpK,IAAIoK,MAAM,IAAI;oBACtB,gEAAgE;oBAChEzJ,SACEX,eAAexB,kBACXqJ,OAAOwC,WAAW,CAACrK,IAAIW,OAAO,CAAC2J,OAAO,MACtCtK,IAAIW,OAAO;gBACnB,GACAsJ;YAEJ,EAAE,OAAOM,YAAY;gBACnB,qFAAqF;gBACrFC,QAAQC,KAAK,CAAC,4CAA4CF;YAC5D;QACF;IACF;IAEOG,SAASV,GAAU,EAAQ;QAChC,IAAI,IAAI,CAACpG,KAAK,EAAE;QAChB5I,IAAIyP,KAAK,CAACT;IACZ;IAEA,MAAaW,cACX3K,GAAkB,EAClBmB,GAAmB,EACnBjB,SAAkC,EACnB;QACf,MAAM,IAAI,CAAC0K,OAAO;QAClB,MAAMR,SAASpK,IAAIoK,MAAM,CAACS,WAAW;QAErC,MAAMC,SAASnO;QACf,OAAOmO,OAAOC,qBAAqB,CAAC/K,IAAIW,OAAO,EAAE;YAC/C,OAAOmK,OAAOE,KAAK,CACjBjO,eAAe4N,aAAa,EAC5B;gBACEM,UAAU,GAAGb,OAAO,CAAC,EAAEpK,IAAIgB,GAAG,EAAE;gBAChCkK,MAAMrO,SAASsO,MAAM;gBACrBC,YAAY;oBACV,eAAehB;oBACf,eAAepK,IAAIgB,GAAG;gBACxB;YACF,GACA,OAAOqK,OACL,IAAI,CAACC,iBAAiB,CAACtL,KAAKmB,KAAKjB,WAAWqL,OAAO,CAAC;oBAClD,IAAI,CAACF,MAAM;oBAEX,MAAMG,eAAelQ,eAAe0E,KAAK,mBAAmB;oBAC5DqL,KAAKI,aAAa,CAAC;wBACjB,oBAAoBtK,IAAIuK,UAAU;wBAClC,YAAYF;oBACd;oBAEA,IAAIrK,IAAIuK,UAAU,IAAIvK,IAAIuK,UAAU,IAAI,KAAK;wBAC3C,8DAA8D;wBAC9D,6EAA6E;wBAC7EL,KAAKM,SAAS,CAAC;4BACbC,MAAM9O,eAAe+O,KAAK;wBAC5B;wBACA,8DAA8D;wBAC9DR,KAAKS,YAAY,CAAC,cAAc3K,IAAIuK,UAAU,CAACK,QAAQ;oBACzD;oBAEA,MAAMC,qBAAqBlB,OAAOmB,qBAAqB;oBACvD,iEAAiE;oBACjE,IAAI,CAACD,oBAAoB;oBAEzB,IACEA,mBAAmBE,GAAG,CAAC,sBACvBnP,eAAe4N,aAAa,EAC5B;wBACAH,QAAQ9G,IAAI,CACV,CAAC,2BAA2B,EAAEsI,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;wBAE1E;oBACF;oBAEA,MAAMC,QAAQH,mBAAmBE,GAAG,CAAC;oBACrC,IAAIC,OAAO;wBACT,MAAMrC,OAAO0B,eACT,CAAC,IAAI,EAAEpB,OAAO,CAAC,EAAE+B,OAAO,GACxB,GAAG/B,OAAO,CAAC,EAAE+B,OAAO;wBAExBd,KAAKI,aAAa,CAAC;4BACjB,cAAcU;4BACd,cAAcA;4BACd,kBAAkBrC;wBACpB;wBACAuB,KAAKe,UAAU,CAACtC;oBAClB,OAAO;wBACLuB,KAAKe,UAAU,CACbZ,eACI,CAAC,IAAI,EAAEpB,OAAO,CAAC,EAAEpK,IAAIgB,GAAG,EAAE,GAC1B,GAAGoJ,OAAO,CAAC,EAAEpK,IAAIgB,GAAG,EAAE;oBAE9B;gBACF;QAEN;IACF;IAEA,MAAcsK,kBACZtL,GAAkB,EAClBmB,GAAmB,EACnBjB,SAAkC,EACnB;QACf,IAAI;gBAiDKmM,yBAS4BA,0BASd,oBAKY;YAvEjC,qCAAqC;YACrC,MAAM,IAAI,CAAC9C,QAAQ,CAAC+C,aAAa;YAEjC,kDAAkD;YAClD,kDAAkD;YAClDjO,gCACE2B,KACA5B,mBAAmB+C,OAAOA,IAAIoL,gBAAgB,GAAGpL;YAGnD,MAAMqL,WAAW,AAACxM,CAAAA,IAAIgB,GAAG,IAAI,EAAC,EAAGoB,KAAK,CAAC,KAAK;YAC5C,MAAMqK,aAAaD,QAAQ,CAAC,EAAE;YAE9B,oEAAoE;YACpE,+DAA+D;YAC/D,wEAAwE;YACxE,WAAW;YACX,IAAIC,8BAAAA,WAAYnM,KAAK,CAAC,cAAc;gBAClC,MAAMoM,WAAWhT,yBAAyBsG,IAAIgB,GAAG;gBACjDG,IAAIwL,QAAQ,CAACD,UAAU,KAAKE,IAAI,CAACF,UAAUG,IAAI;gBAC/C;YACF;YAEA,sCAAsC;YACtC,IAAI,CAAC3M,aAAa,OAAOA,cAAc,UAAU;gBAC/C,IAAI,CAACF,IAAIgB,GAAG,EAAE;oBACZ,MAAM,qBAAgD,CAAhD,IAAI7B,MAAM,wCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+C;gBACvD;gBAEAe,YAAYnG,SAASiG,IAAIgB,GAAG,EAAG;YACjC;YAEA,IAAI,CAACd,UAAUC,QAAQ,EAAE;gBACvB,MAAM,qBAA+C,CAA/C,IAAIhB,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEA,iFAAiF;YACjF,IAAI,OAAOe,UAAU4M,KAAK,KAAK,UAAU;gBACvC5M,UAAU4M,KAAK,GAAGjF,OAAOwC,WAAW,CAClC,IAAI0C,gBAAgB7M,UAAU4M,KAAK;YAEvC;YAEA,sCAAsC;YACtC,MAAM,EAAET,kBAAkB,IAAI,EAAE,GAAGlO,kBAAkB6B,OAAOA,MAAM,CAAC;YACnE,MAAMgN,kBAAkBX,mCAAAA,gBAAiB1L,OAAO,CAAC,oBAAoB;YACrE,MAAMsM,UAAUD,kBACZA,oBAAoB,UACpB,CAAC,EAAEX,oCAAAA,0BAAAA,gBAAiBa,MAAM,qBAAxB,AAACb,wBAAuCc,SAAS;YAEvDnN,IAAIW,OAAO,CAAC,mBAAmB,KAAKX,IAAIW,OAAO,CAAC,OAAO,IAAI,IAAI,CAACuB,QAAQ;YACxElC,IAAIW,OAAO,CAAC,mBAAmB,KAAK,IAAI,CAACqD,IAAI,GACzC,IAAI,CAACA,IAAI,CAAC+H,QAAQ,KAClBkB,UACE,QACA;YACNjN,IAAIW,OAAO,CAAC,oBAAoB,KAAKsM,UAAU,UAAU;YACzDjN,IAAIW,OAAO,CAAC,kBAAkB,KAAK0L,oCAAAA,2BAAAA,gBAAiBa,MAAM,qBAAvBb,yBAAyBe,aAAa;YAEzE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAI,CAACC,iBAAiB,CAACrN,KAAKE;YAE5B,IAAIkD,WAAW,MAAM,IAAI,CAACrD,gBAAgB,CAACC,KAAKmB,KAAKjB;YACrD,IAAIkD,UAAU;YAEd,MAAMd,gBAAe,qBAAA,IAAI,CAACL,YAAY,qBAAjB,mBAAmBM,kBAAkB,CACxD5G,YAAYuE,WAAWF,IAAIW,OAAO;YAGpC,MAAM6B,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,OAAI,wBAAA,IAAI,CAAChD,UAAU,CAACoF,IAAI,qBAApB,sBAAsBpC,aAAa;YACpEnH,eAAe2E,KAAK,iBAAiBwC;YAErC,MAAMxB,MAAMpF,aAAaoE,IAAIgB,GAAG,CAACsM,OAAO,CAAC,QAAQ;YACjD,MAAMC,eAAe1R,oBAAoBmF,IAAIb,QAAQ,EAAE;gBACrDX,YAAY,IAAI,CAACA,UAAU;gBAC3ByC,cAAc,IAAI,CAACA,YAAY;YACjC;YACAjB,IAAIb,QAAQ,GAAGoN,aAAapN,QAAQ;YAEpC,IAAIoN,aAAa3G,QAAQ,EAAE;gBACzB5G,IAAIgB,GAAG,GAAGvF,iBAAiBuE,IAAIgB,GAAG,EAAG,IAAI,CAACxB,UAAU,CAACoH,QAAQ;YAC/D;YAEA,MAAM4G,uBACJ,IAAI,CAACzJ,WAAW,IAAI,OAAO/D,IAAIW,OAAO,CAACtD,oBAAoB,KAAK;YAElE,uCAAuC;YACvC,IAAImQ,sBAAsB;gBACxB,IAAI;wBAuBE,wBA6ByB,qBA6DjB;oBAhHZ,IAAI,IAAI,CAACnK,kBAAkB,CAACqC,GAAG,EAAE;wBAC/B,iDAAiD;wBACjD,kBAAkB;wBAClB,IAAI1F,IAAIgB,GAAG,CAACV,KAAK,CAAC,mBAAmB;4BACnCN,IAAIgB,GAAG,GAAGhB,IAAIgB,GAAG,CAACsM,OAAO,CAAC,YAAY;wBACxC;wBACApN,UAAUC,QAAQ,GAChBD,UAAUC,QAAQ,KAAK,WAAW,MAAMD,UAAUC,QAAQ;oBAC9D;oBAEA,4DAA4D;oBAC5D,sEAAsE;oBACtE,IAAI,EAAEA,UAAUsN,WAAW,EAAE,GAAG,IAAIC,IAClC3O,YAAYiB,IAAIW,OAAO,CAACtD,oBAAoB,GAC5C;oBAGF,IAAI,EAAE8C,UAAUwN,WAAW,EAAE,GAAG,IAAID,IAAI1N,IAAIgB,GAAG,EAAE;oBAEjD,2DAA2D;oBAC3D,yDAAyD;oBACzD,6CAA6C;oBAC7C,KAAI,yBAAA,IAAI,CAACZ,WAAW,CAAC4C,IAAI,qBAArB,uBAAuB1C,KAAK,CAACqN,cAAc;wBAC7CtS,eAAe2E,KAAK,iBAAiB;oBACvC,OAGK,IACH,IAAI,CAACyF,eAAe,IACpB,IAAI,CAAC1B,WAAW,IAChB/D,IAAIW,OAAO,CAACrD,mBAAmB,KAAK,OACpC0C,IAAIoK,MAAM,KAAK,QACf;wBACA,oEAAoE;wBACpE,oEAAoE;wBACpE,cAAc;wBACd,MAAMwC,OAAsB,EAAE;wBAC9B,WAAW,MAAMgB,SAAS5N,IAAI4M,IAAI,CAAE;4BAClCA,KAAK3J,IAAI,CAAC2K;wBACZ;wBACA,MAAMC,YAAYC,OAAOC,MAAM,CAACnB,MAAMb,QAAQ,CAAC;wBAE/C1Q,eAAe2E,KAAK,aAAa6N;oBACnC;oBAEAJ,cAAc,IAAI,CAAC5M,SAAS,CAAC4M;oBAC7B,MAAMO,oBAAoB,IAAI,CAACC,iBAAiB,CAACN;oBAEjDF,cAAc1S,oBAAoB0S;oBAElC,8CAA8C;oBAC9C,MAAMS,wBAAuB,sBAAA,IAAI,CAACjM,YAAY,qBAAjB,oBAAmBU,OAAO,CAAC8K,aAAa;wBACnEjL;oBACF;oBAEA,+DAA+D;oBAC/D,gEAAgE;oBAChE,kBAAkB;oBAClB,IAAI0L,sBAAsB;wBACxB7S,eAAe2E,KAAK,UAAUkO,qBAAqBtL,cAAc;wBAEjE,kEAAkE;wBAClE,+DAA+D;wBAC/D,IAAIsL,qBAAqBC,mBAAmB,EAAE;4BAC5C9S,eAAe2E,KAAK,6BAA6B;wBACnD,OAAO;4BACLzE,kBAAkByE,KAAK;wBACzB;oBACF;oBAEA,IAAIoO,cAAcX;oBAClB,IAAIY,gBAAgB9T,eAAe6T;oBACnC,IAAIE,eAGA;wBACFhN,QAAQ;wBACRiN,gBAAgB;oBAClB;oBAEA,IAAI,CAACF,eAAe;wBAClB,MAAM/N,QAAQ,MAAM,IAAI,CAACiJ,QAAQ,CAACjJ,KAAK,CAAC8N,aAAa;4BACnDxJ,MAAMsJ;wBACR;wBAEA,6DAA6D;wBAC7D,IAAI5N,OAAO;4BACT8N,cAAc9N,MAAMkO,UAAU,CAACrO,QAAQ;4BAEvC,iEAAiE;4BACjE,iEAAiE;4BACjE,4CAA4C;4BAC5C,IAAI,OAAOG,MAAMgB,MAAM,KAAK,aAAa;gCACvC+M,gBAAgB;gCAChBC,aAAahN,MAAM,GAAGhB,MAAMgB,MAAM;gCAClCgN,aAAaC,cAAc,GAAG;4BAChC;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,oEAAoE;oBACpE,oDAAoD;oBACpD,IAAIL,sBAAsB;wBACxBT,cAAcS,qBAAqB/N,QAAQ;oBAC7C;oBAEA,MAAMsO,QAAQvT,eAAe;wBAC3BmT;wBACAK,MAAMN;wBACNxJ,MAAM,IAAI,CAACpF,UAAU,CAACoF,IAAI;wBAC1BgC,UAAU,IAAI,CAACpH,UAAU,CAACoH,QAAQ;wBAClC+H,UAAU,EAAA,0BAAA,IAAI,CAACC,iBAAiB,uBAAtB,wBAA0BD,QAAQ,KAAI;4BAC9CE,aAAa,EAAE;4BACfC,YAAY,EAAE;4BACdC,UAAU,EAAE;wBACd;wBACAC,eAAe,CAAC,CAAC,IAAI,CAACxP,UAAU,CAACC,YAAY,CAACwP,mBAAmB;oBACnE;oBAEA,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAIzM,iBAAiB,CAAC+K,aAAa2B,MAAM,EAAE;wBACzChP,UAAUC,QAAQ,GAAG,CAAC,CAAC,EAAEqC,gBAAgBtC,UAAUC,QAAQ,EAAE;oBAC/D;oBAEA,mEAAmE;oBACnE,qEAAqE;oBACrE,MAAMgP,oBAAoB;wBAAE,GAAGjP,UAAU4M,KAAK;oBAAC;oBAE/C,MAAMsC,wBAAwBlP,UAAUC,QAAQ;oBAChD,MAAMkP,mBAAmBxH,OAAOC,IAAI,CAClC2G,MAAMa,cAAc,CAACtP,KAAKE;oBAG5B,mEAAmE;oBACnE,mEAAmE;oBACnE,2CAA2C;oBAC3C,MAAMqP,cAAc;wBAAE,GAAGrP,UAAU4M,KAAK;oBAAC;oBACzC,MAAM0C,aAAaJ,0BAA0BlP,UAAUC,QAAQ;oBAE/D,IAAIqP,cAActP,UAAUC,QAAQ,EAAE;wBACpC9E,eAAe2E,KAAK,cAAcE,UAAUC,QAAQ;oBACtD;oBAEA,MAAMsP,iBAAiB,IAAIC;oBAC3B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAI/H,OAAOyC,OAAO,CAACpK,UAAU4M,KAAK,EAAG;wBAC1D,MAAM+C,gBAAgB3S,wBAAwByS;wBAC9C,IAAI,CAACE,eAAe;wBAEpB,gEAAgE;wBAChE,+CAA+C;wBAC/C,OAAO3P,UAAU4M,KAAK,CAAC6C,IAAI;wBAC3BF,eAAeK,GAAG,CAACD;wBAEnB,IAAI,OAAOD,UAAU,aAAa;wBAElCL,WAAW,CAACM,cAAc,GAAGE,MAAMC,OAAO,CAACJ,SACvCA,MAAMK,GAAG,CAAC,CAACC,IAAMtR,yBAAyBsR,MAC1CtR,yBAAyBgR;oBAC/B;oBAEA,yDAAyD;oBACzD,IAAIvB,eAAe;wBACjB,IAAI/M,SAAiC,CAAC;wBAEtC,gEAAgE;wBAChE,oBAAoB;wBACpB,IAAI,CAACgN,aAAaC,cAAc,EAAE;4BAChCD,eAAeG,MAAM0B,2BAA2B,CAC9CZ,aACA;wBAEJ;wBAEA,yDAAyD;wBACzD,wDAAwD;wBACxD,wDAAwD;wBACxD,qDAAqD;wBACrD,IACE,CAACjB,aAAaC,cAAc,IAC5B,CAAChU,eAAeyT,oBAChB;4BACA,IAAIoC,gBAAgB3B,MAAM4B,mBAAmB,oBAAzB5B,MAAM4B,mBAAmB,MAAzB5B,OAA4BT;4BAEhD,IAAIoC,eAAe;gCACjB3B,MAAM0B,2BAA2B,CAACC,eAAe;gCACjDvI,OAAOyI,MAAM,CAAChC,aAAahN,MAAM,EAAE8O;gCACnC9B,aAAaC,cAAc,GAAG;4BAChC;wBACF;wBAEA,uDAAuD;wBACvD,4DAA4D;wBAC5D,oEAAoE;wBACpE,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,yBAAyB;wBACzB,IACE,8DAA8D;wBAC9Dd,gBAAgB,YAChB,CAACa,aAAaC,cAAc,IAC5B,CAAChU,eAAekT,cAChB;4BACA,IAAI2C,gBAAgB3B,MAAM4B,mBAAmB,oBAAzB5B,MAAM4B,mBAAmB,MAAzB5B,OAA4BhB;4BAEhD,IAAI2C,eAAe;gCACjB,MAAMG,kBAAkB9B,MAAM0B,2BAA2B,CACvDC,eACA;gCAGF,IAAIG,gBAAgBhC,cAAc,EAAE;oCAClC1G,OAAOyI,MAAM,CAAChP,QAAQ8O;oCACtB9B,eAAeiC;gCACjB;4BACF;wBACF;wBAEA,IAAIjC,aAAaC,cAAc,EAAE;4BAC/BjN,SAASgN,aAAahN,MAAM;wBAC9B;wBAEA,MAAMkP,qBAAqBxQ,IAAIW,OAAO,CAAC,sBAAsB;wBAC7D,IACE,OAAO6P,uBAAuB,YAC9BA,sBACAjW,eAAekT,gBACf,CAACa,aAAaC,cAAc,EAC5B;4BACA,MAAMkC,eACJhC,MAAMiC,yBAAyB,CAACF;4BAElC,IAAIC,cAAc;gCAChBnC,eAAeG,MAAM0B,2BAA2B,CAC9CM,cACA;gCAGF,IAAInC,aAAaC,cAAc,EAAE;oCAC/BjN,SAASgN,aAAahN,MAAM;gCAC9B;4BACF;wBACF;wBAEA,mEAAmE;wBACnE,6DAA6D;wBAC7D,IAAI,CAACgN,aAAaC,cAAc,EAAE;4BAChCD,eAAeG,MAAM0B,2BAA2B,CAC9CZ,aACA;4BAGF,IAAIjB,aAAaC,cAAc,EAAE;gCAC/BjN,SAASgN,aAAahN,MAAM;4BAC9B;wBACF;wBAEA,4DAA4D;wBAC5D,+DAA+D;wBAC/D,yBAAyB;wBACzB,IACEmN,MAAMkC,mBAAmB,IACzB3C,sBAAsBI,eACtB,CAACE,aAAaC,cAAc,EAC5B;4BACAjN,SAASmN,MAAMkC,mBAAmB;4BAElC,6DAA6D;4BAC7D,kEAAkE;4BAClE,gEAAgE;4BAChE,8DAA8D;4BAC9D,gEAAgE;4BAChE,IAAIH,uBAAuB,IAAI;gCAC7BnV,eAAe2E,KAAK,uBAAuB;4BAC7C;wBACF;wBAEA,IAAIsB,QAAQ;4BACVmM,cAAcgB,MAAMmC,sBAAsB,CAACxC,aAAa9M;4BACxDtB,IAAIgB,GAAG,GAAGyN,MAAMmC,sBAAsB,CAAC5Q,IAAIgB,GAAG,EAAGM;4BAEjD,kEAAkE;4BAClE,4DAA4D;4BAC5D,UAAU;4BACV,IAAIP,4BAA4BzF,eAC9B0E,KACA;4BAEF,IACEe,6BACAxG,eAAewG,2BAA2B,QAC1C;gCACAA,4BAA4B0N,MAAMmC,sBAAsB,CACtD7P,2BACAO;gCAGFtB,IAAIW,OAAO,CAAC1E,oCAAoC,GAC9C8E;gCACF1F,eACE2E,KACA,6BACAe;4BAEJ;wBACF;oBACF;oBAEA,IAAIsN,iBAAiBmB,YAAY;4BAGdf;wBAFjBA,MAAMoC,eAAe,CAAC7Q,KAAK;+BACtBqP;+BACAxH,OAAOC,IAAI,CAAC2G,EAAAA,2BAAAA,MAAMqC,iBAAiB,qBAAvBrC,yBAAyBsC,MAAM,KAAI,CAAC;yBACpD;oBACH;oBACA,oEAAoE;oBACpE,oCAAoC;oBACpC,mFAAmF;oBACnF,KAAK,MAAMpB,OAAOF,eAAgB;wBAChC,IAAI,CAAEE,CAAAA,OAAOR,iBAAgB,GAAI;4BAC/B,OAAOjP,UAAU4M,KAAK,CAAC6C,IAAI;wBAC7B;oBACF;oBACAzP,UAAUC,QAAQ,GAAGsN;oBACrBzM,IAAIb,QAAQ,GAAGD,UAAUC,QAAQ;oBACjCiD,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACnD,KAAKmB,KAAKjB;oBAC3D,IAAIkD,UAAU;gBAChB,EAAE,OAAO4G,KAAK;oBACZ,IAAIA,eAAevQ,eAAeuQ,eAAexQ,gBAAgB;wBAC/D2H,IAAIuK,UAAU,GAAG;wBACjB,OAAO,IAAI,CAACsF,WAAW,CAAC,MAAMhR,KAAKmB,KAAK,WAAW,CAAC;oBACtD;oBACA,MAAM6I;gBACR;YACF;YAEA3O,eAAe2E,KAAK,kBAAkBqI,QAAQ/F;YAE9C,IAAIiL,aAAa2B,MAAM,EAAE;gBACvBlP,IAAIgB,GAAG,GAAGnH,UAAUmH;gBACpB3F,eAAe2E,KAAK,kBAAkB;YACxC;YAEA,kEAAkE;YAClE,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAAC+D,WAAW,IAAI,CAACzI,eAAe0E,KAAK,WAAW;gBACvD,gEAAgE;gBAChE,IAAIuN,aAAa2B,MAAM,EAAE;oBACvB7T,eAAe2E,KAAK,UAAUuN,aAAa2B,MAAM;gBACnD,OAGK,IAAI1M,eAAe;oBACtBnH,eAAe2E,KAAK,UAAUwC;oBAC9BnH,eAAe2E,KAAK,6BAA6B;gBACnD;YACF;YAEA,kDAAkD;YAClD,uDAAuD;YACvD,iCAAiC;YACjC,IACE,CAAC,AAAC,IAAI,CAACkE,aAAa,CAAS+M,eAAe,IAC5C,CAAC3V,eAAe0E,KAAK,qBACrB;gBACA,MAAMkR,mBAAmB,MAAM,IAAI,CAACC,mBAAmB,CAAC;oBACtDC,gBAAgBvJ,OAAOyI,MAAM,CAAC,CAAC,GAAGtQ,IAAIW,OAAO;gBAC/C;gBAEAuQ,iBAAiBG,iBAAiB;gBAClChW,eAAe2E,KAAK,oBAAoBkR;gBAGtCvR,WAAmB2R,kBAAkB,GAAGJ;YAC5C;YAEA,MAAMK,gBAAgBzS;YAEtB,IAAIyS,eAAe;gBACjB,MAAMC,QAAQC,GAAG,CACf;uBAAIF;iBAAc,CAACtB,GAAG,CAAC,OAAOyB;oBAC5B,IAAI,iBAAiBA,cAAc;oBACjC,+DAA+D;oBAC/D,2DAA2D;oBAC3D,gDAAgD;oBAClD,OAAO;wBACL,MAAMC,4BAA4B1W,6BAChC+E,IAAIW,OAAO,EACX,IAAI,CAAC6F,oBAAoB,GAAGC,OAAO,CAACmL,aAAa;wBAGnD,MAAMF,aAAaG,kBAAkB,IAChCF;oBAEP;gBACF;YAEJ;YAEA,sEAAsE;YACtE,0BAA0B;YAC1B,IAAI,CAACrW,eAAe0E,KAAK,6BAA6B;gBACpD3E,eACE2E,KACA,4BACA,IAAI,CAACT,2BAA2B;YAEpC;YAEA,oEAAoE;YACpE,mEAAmE;YACnE,mDAAmD;YACnD,MAAMuS,aAAaxW,eAAe0E,KAAK;YACvC,MAAM+R,gBAAgB,CAACvE,wBAAwBsE;YAE/C,IAAIC,eAAe;oBAkCf;gBAjCF,MAAMC,eAAe1W,eAAe0E,KAAK;gBACzC,IAAIgS,cAAc;oBAChB,MAAMC,cAAc3W,eAAe0E,KAAK;oBAExC,IAAIiS,aAAa;wBACfpK,OAAOyI,MAAM,CAACpQ,UAAU4M,KAAK,EAAEmF;oBACjC;oBAEA9Q,IAAIuK,UAAU,GAAGsG;oBACjB,IAAIhI,MAAoB1O,eAAe0E,KAAK,kBAAkB;oBAE9D,OAAO,IAAI,CAACgR,WAAW,CAAChH,KAAKhK,KAAKmB,KAAK,WAAWjB,UAAU4M,KAAK;gBACnE;gBAEA,MAAMoF,oBAAoB,IAAIxE,IAAIoE,cAAc,KAAK;gBACrD,MAAMK,qBAAqBtW,oBACzBqW,kBAAkB/R,QAAQ,EAC1B;oBACEX,YAAY,IAAI,CAACA,UAAU;oBAC3B4S,WAAW;gBACb;gBAGF,IAAID,mBAAmBjD,MAAM,EAAE;oBAC7B7T,eAAe2E,KAAK,UAAUmS,mBAAmBjD,MAAM;gBACzD;gBAEA,IAAIhP,UAAUC,QAAQ,KAAK+R,kBAAkB/R,QAAQ,EAAE;oBACrDD,UAAUC,QAAQ,GAAG+R,kBAAkB/R,QAAQ;oBAC/C9E,eAAe2E,KAAK,cAAcmS,mBAAmBhS,QAAQ;gBAC/D;gBACA,MAAMkS,kBAAkB9U,oBACtB9B,iBAAiByE,UAAUC,QAAQ,EAAE,IAAI,CAACX,UAAU,CAACoH,QAAQ,IAAI,MACjE,yBAAA,IAAI,CAACpH,UAAU,CAACoF,IAAI,qBAApB,uBAAsBC,OAAO;gBAG/B,IAAIwN,gBAAgBzP,cAAc,EAAE;oBAClCvH,eAAe2E,KAAK,UAAUqS,gBAAgBzP,cAAc;gBAC9D;gBACA1C,UAAUC,QAAQ,GAAGkS,gBAAgBlS,QAAQ;gBAE7C,KAAK,MAAMwP,OAAO9H,OAAOC,IAAI,CAAC5H,UAAU4M,KAAK,EAAG;oBAC9C,OAAO5M,UAAU4M,KAAK,CAAC6C,IAAI;gBAC7B;gBACA,MAAMsC,cAAc3W,eAAe0E,KAAK;gBAExC,IAAIiS,aAAa;oBACfpK,OAAOyI,MAAM,CAACpQ,UAAU4M,KAAK,EAAEmF;gBACjC;gBAEA7O,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACnD,KAAKmB,KAAKjB;gBAC3D,IAAIkD,UAAU;gBAEd,MAAM,IAAI,CAACN,2BAA2B,CAAC9C,KAAKmB,KAAKjB;gBACjD;YACF;YAEA,IAAI5E,eAAe0E,KAAK,qBAAqB;gBAC3CoD,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACnD,KAAKmB,KAAKjB;gBAC3D,IAAIkD,UAAU;gBAEdA,WAAW,MAAM,IAAI,CAACL,+BAA+B,CACnD/C,KACAmB,KACAjB;gBAEF,IAAIkD,UAAU;gBAEd,MAAM4G,MAAM,IAAI7K;gBACd6K,IAAYzJ,MAAM,GAAG;oBACrB+R,UAAU,IAAIC,SAAS,MAAM;wBAC3B5R,SAAS;4BACP,qBAAqB;wBACvB;oBACF;gBACF;gBACEqJ,IAAYwI,MAAM,GAAG;gBACvB,MAAMxI;YACR;YAEA,oEAAoE;YACpE,sDAAsD;YAEtD,+DAA+D;YAC/D,IAAI,CAACwD,wBAAwBD,aAAa3G,QAAQ,EAAE;gBAClD1G,UAAUC,QAAQ,GAAG1E,iBACnByE,UAAUC,QAAQ,EAClBoN,aAAa3G,QAAQ;YAEzB;YAEAzF,IAAIuK,UAAU,GAAG;YACjB,OAAO,MAAM,IAAI,CAAC+G,GAAG,CAACzS,KAAKmB,KAAKjB;QAClC,EAAE,OAAO8J,KAAU;YACjB,IAAIA,eAAenL,iBAAiB;gBAClC,MAAMmL;YACR;YAEA,IACE,AAACA,OAAO,OAAOA,QAAQ,YAAYA,IAAI4B,IAAI,KAAK,qBAChD5B,eAAevQ,eACfuQ,eAAexQ,gBACf;gBACA2H,IAAIuK,UAAU,GAAG;gBACjB,OAAO,IAAI,CAACsF,WAAW,CAAC,MAAMhR,KAAKmB,KAAK,WAAW,CAAC;YACtD;YAEA,IACE,IAAI,CAAC4C,WAAW,IAChB,IAAI,CAACmC,UAAU,CAACpC,GAAG,IAClBlH,eAAeoN,QAAQA,IAAIwI,MAAM,EAClC;gBACA,MAAMxI;YACR;YACA,IAAI,CAACU,QAAQ,CAACtP,eAAe4O;YAC7B7I,IAAIuK,UAAU,GAAG;YACjBvK,IAAIyL,IAAI,CAAC,yBAAyBC,IAAI;QACxC;IACF;IAwDA;;GAEC,GACD,AAAO6F,8BACLC,IAAiB,EACkC;QACnD,MAAMC,UAAU,IAAI,CAACC,iBAAiB;QACtC,OAAO,CAAC7S,KAAKmB,KAAKjB;YAChB1E,eAAewE,KAAK2S;YACpB,OAAOC,QAAQ5S,KAAKmB,KAAKjB;QAC3B;IACF;IAEO2S,oBAGL;QACA,OAAO,IAAI,CAAClI,aAAa,CAAC/B,IAAI,CAAC,IAAI;IACrC;IAQOc,eAAeoJ,MAAe,EAAQ;QAC3C,IAAI,CAACtT,UAAU,CAACyF,WAAW,GAAG6N,SAASA,OAAOxF,OAAO,CAAC,OAAO,MAAM;IACrE;IAIA;;;GAGC,GACD,MAAa1C,UAAyB;QACpC,IAAI,IAAI,CAACrH,QAAQ,EAAE;QAEnB,6BAA6B;QAC7B,IAAI,CAAC,IAAI,CAAC2G,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAG,MAAM,IAAI,CAAC6I,yBAAyB;QAC7D;QACA,IAAI,IAAI,CAACvP,eAAe,KAAK,MAAM;YACjC,IAAI,CAACA,eAAe,GAAG,IAAI,CAACwP,WAAW,GAAGC,IAAI,CAAC;gBAC7C,IAAI,CAAC1P,QAAQ,GAAG;gBAChB,IAAI,CAACC,eAAe,GAAG;YACzB;QACF;QACA,OAAO,IAAI,CAACA,eAAe;IAC7B;IACA,MAAgBwP,cAA6B,CAAC;IAC9C,MAAgBD,4BAA0C,CAAC;IAE3D,MAAaG,QAAuB,CAAC;IAE3B9J,mBAA6C;QACrD,MAAMD,gBAA0C,CAAC;QAEjDtB,OAAOC,IAAI,CAAC,IAAI,CAACmB,gBAAgB,IAAI,CAAC,GAAGkK,OAAO,CAAC,CAACC;YAChD,MAAMC,iBAAiB3X,iBAAiB0X;YACxC,IAAI,CAACjK,aAAa,CAACkK,eAAe,EAAE;gBAClClK,aAAa,CAACkK,eAAe,GAAG,EAAE;YACpC;YACAlK,aAAa,CAACkK,eAAe,CAACpQ,IAAI,CAACmQ;QACrC;QACA,OAAOjK;IACT;IAEA,MAAgBsJ,IACdzS,GAAkB,EAClBmB,GAAmB,EACnBjB,SAA6B,EACd;QACf,OAAOvD,YAAYqO,KAAK,CAACjO,eAAe0V,GAAG,EAAE,UAC3C,IAAI,CAACa,OAAO,CAACtT,KAAKmB,KAAKjB;IAE3B;IAEA,MAAcoT,QACZtT,GAAkB,EAClBmB,GAAmB,EACnBjB,SAA6B,EACd;QACf,MAAM,IAAI,CAAC4C,2BAA2B,CAAC9C,KAAKmB,KAAKjB;IACnD;IAEA,MAAcqT,KACZC,EAEoC,EACpCC,cAGC,EACc;QACf,OAAO9W,YAAYqO,KAAK,CAACjO,eAAewW,IAAI,EAAE,UAC5C,IAAI,CAACG,QAAQ,CAACF,IAAIC;IAEtB;IAEA,MAAcC,SACZF,EAEoC,EACpCC,cAGC,EACc;QACf,MAAME,KAAKF,eAAezT,GAAG,CAACW,OAAO,CAAC,aAAa,IAAI;QAEvD,MAAMsJ,MAAqD;YACzD,GAAGwJ,cAAc;YACjBvN,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClB,6DAA6D;gBAC7DC,yBAAyB,CAAC,IAAI,CAACD,UAAU,CAAC0N,OAAO;gBACjDC,wBAAwBlV,6BACtBgV,IACA,IAAI,CAACnU,UAAU,CAACwI,eAAe;YAEnC;QACF;QAEA,MAAM8L,UAAU,MAAMN,GAAGvJ;QACzB,IAAI6J,YAAY,MAAM;YACpB;QACF;QACA,MAAM,EAAE9T,GAAG,EAAEmB,GAAG,EAAE,GAAG8I;QACrB,MAAM8J,iBAAiB5S,IAAIuK,UAAU;QACrC,MAAM,EAAEkB,IAAI,EAAE,GAAGkH;QACjB,IAAI,EAAEE,YAAY,EAAE,GAAGF;QACvB,IAAI,CAAC3S,IAAI8S,IAAI,EAAE;YACb,MAAM,EAAE/O,aAAa,EAAEkB,eAAe,EAAEtC,GAAG,EAAE,GAAG,IAAI,CAACoC,UAAU;YAE/D,oDAAoD;YACpD,IAAIpC,KAAK;gBACP3C,IAAI+S,SAAS,CAAC,iBAAiB;gBAC/BF,eAAenU;YACjB;YAEA,IAAImU,gBAAgBA,aAAaG,MAAM,KAAKtU,WAAW;gBACrDmU,aAAaG,MAAM,GAAG,IAAI,CAAC3U,UAAU,CAACyI,UAAU;YAClD;YAEA,MAAM,IAAI,CAACmM,gBAAgB,CAACpU,KAAKmB,KAAK;gBACpCZ,QAAQqM;gBACR1H;gBACAkB;gBACA4N;YACF;YACA7S,IAAIuK,UAAU,GAAGqI;QACnB;IACF;IAEA,MAAcM,cACZb,EAEoC,EACpCC,cAGC,EACuB;QACxB,MAAMxJ,MAAqD;YACzD,GAAGwJ,cAAc;YACjBvN,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB;YAC3B;QACF;QACA,MAAM2N,UAAU,MAAMN,GAAGvJ;QACzB,IAAI6J,YAAY,MAAM;YACpB,OAAO;QACT;QACA,OAAOA,QAAQlH,IAAI,CAAC0H,iBAAiB;IACvC;IAEA,MAAaC,OACXvU,GAAkB,EAClBmB,GAAmB,EACnBhB,QAAgB,EAChB2M,QAA4B,CAAC,CAAC,EAC9B5M,SAAkC,EAClCsU,iBAAiB,KAAK,EACP;QACf,OAAO7X,YAAYqO,KAAK,CAACjO,eAAewX,MAAM,EAAE,UAC9C,IAAI,CAACE,UAAU,CAACzU,KAAKmB,KAAKhB,UAAU2M,OAAO5M,WAAWsU;IAE1D;IAEUE,eAAsC;QAC9C,MAAMC,wBAAwBpW;QAC9B,IAAIoW,uBAAuB;YACzB,2CAA2C;YAC3C,qEAAqE;YACrE,sCAAsC;YAEtC,uGAAuG;YACvG,OAAOA,sBAAsBC,SAAS;QACxC;QAEA,IAAI,IAAI,CAAC7Q,WAAW,EAAE;YACpB,8EAA8E;YAC9E,4DAA4D;YAC5D,0DAA0D;YAC1D,kDAAkD;YAClD,EAAE;YACF,yEAAyE;YACzE,EAAE;YACF,wGAAwG;YACxG,wBAAwB;YACxB,OAAOlE;QACT;QAEA,OAAO,IAAI,CAACgV,oBAAoB;IAClC;IAEUA,uBAA8C;QACtD,OAAOhV;IACT;IAEA,MAAc4U,WACZzU,GAAkB,EAClBmB,GAAmB,EACnBhB,QAAgB,EAChB2M,QAA4B,CAAC,CAAC,EAC9B5M,SAAkC,EAClCsU,iBAAiB,KAAK,EACP;YA4BZxU;QA3BH,IAAI,CAACG,SAAS2U,UAAU,CAAC,MAAM;YAC7BtK,QAAQ9G,IAAI,CACV,CAAC,8BAA8B,EAAEvD,SAAS,kBAAkB,EAAEA,SAAS,iFAAiF,CAAC;QAE7J;QAEA,IACE,IAAI,CAAC+D,aAAa,CAAC6Q,YAAY,IAC/B5U,aAAa,YACb,CAAE,MAAM,IAAI,CAAC6U,OAAO,CAAC,WACrB;YACA,qDAAqD;YACrD,wCAAwC;YACxC7U,WAAW;QACb;QAEA,MAAMwT,KAAK3T,IAAIW,OAAO,CAAC,aAAa,IAAI;QACxC,IAAI,CAACuF,UAAU,CAAC0N,OAAO,GAAGjZ,WAAWgZ;QAErC,sDAAsD;QACtD,2DAA2D;QAC3D,2DAA2D;QAC3D,kEAAkE;QAClE,IACE,CAACa,kBACD,CAAC,IAAI,CAACzQ,WAAW,IACjB,CAACzI,eAAe0E,KAAK,oBACpBA,CAAAA,EAAAA,WAAAA,IAAIgB,GAAG,qBAAPhB,SAASM,KAAK,CAAC,kBACb,IAAI,CAACoE,YAAY,IAAI1E,IAAIgB,GAAG,CAAEV,KAAK,CAAC,cAAc,GACrD;YACA,OAAO,IAAI,CAACqK,aAAa,CAAC3K,KAAKmB,KAAKjB;QACtC;QAEA,IAAIxF,cAAcyF,WAAW;YAC3B,OAAO,IAAI,CAACsB,SAAS,CAACzB,KAAKmB,KAAKjB;QAClC;QAEA,OAAO,IAAI,CAACqT,IAAI,CAAC,CAACtJ,MAAQ,IAAI,CAACgL,gBAAgB,CAAChL,MAAM;YACpDjK;YACAmB;YACAhB;YACA2M;QACF;IACF;IAEA,MAAgBoI,eAAe,EAC7B/U,QAAQ,EAOT,EAIE;YAGC;QAFF,+DAA+D;QAC/D,MAAMgV,iBACJ,oDAAA,IAAI,CAAC3O,oBAAoB,GAAG4O,aAAa,CAACjV,SAAS,qBAAnD,kDAAqD4O,QAAQ;QAE/D,OAAO;YACL,oEAAoE;YACpE,uCAAuC;YACvCsG,aAAaxV;YACbyV,cAAc7W,mBAAmB0W;QACnC;IACF;IAEA,MAAcI,+BACZC,cAA6D,EAC7DC,oBAA0C,EACT;QACjC,OAAO9Y,YAAYqO,KAAK,CACtBjO,eAAewY,8BAA8B,EAC7C,UACE,IAAI,CAACG,kCAAkC,CACrCF,gBACAC;IAGR;IAEUE,uBAAuBC,gBAAwB,EAAW;QAClE,OACE3X,2BAA2B2X,qBAC3B,IAAI,CAACvM,yBAAyB,CAACwM,IAAI,CAAC,CAACC;YACnC,OAAOA,OAAOC,IAAI,CAACH;QACrB;IAEJ;IAEUI,cACRhW,GAAkB,EAClBmB,GAAmB,EACnB8U,SAAkB,EAClBL,gBAAwB,EAClB;QACN,MAAMM,iBAAiB,GAAGpa,WAAW,EAAE,EAAEK,8BAA8B,EAAE,EAAEH,4BAA4B,EAAE,EAAEC,qCAAqC;QAChJ,MAAMuP,eAAelQ,eAAe0E,KAAK,mBAAmB;QAE5D,IAAImW,qBAAqB;QAEzB,IAAIF,aAAa,IAAI,CAACN,sBAAsB,CAACC,mBAAmB;YAC9D,wEAAwE;YACxE,+FAA+F;YAC/FzU,IAAIiV,YAAY,CAAC,QAAQ,GAAGF,eAAe,EAAE,EAAEha,UAAU;YACzDia,qBAAqB;QACvB,OAAO,IAAIF,aAAazK,cAAc;YACpC,yHAAyH;YACzH,mGAAmG;YACnGrK,IAAIiV,YAAY,CAAC,QAAQF;QAC3B;QAEA,IAAI,CAACC,oBAAoB;YACvB,8GAA8G;YAC9G,sGAAsG;YACtG,OAAOnW,IAAIW,OAAO,CAACzE,SAAS;QAC9B;IACF;IAEA,MAAcwZ,mCACZ,EACE1V,GAAG,EACHmB,GAAG,EACHhB,QAAQ,EACR+F,YAAYmQ,IAAI,EAC8B,EAChD,EAAEC,UAAU,EAAExJ,KAAK,EAAwB,EACV;YAiL7ByJ;QAhLJ,IAAIpW,aAAa9F,4BAA4B;YAC3C8F,WAAW;QACb;QACA,MAAMqW,kBAAkBrW,aAAa;QACrC,MAAMsW,YACJtW,aAAa,UAAWqW,mBAAmBrV,IAAIuK,UAAU,KAAK;QAChE,MAAMgL,YACJvW,aAAa,UAAWqW,mBAAmBrV,IAAIuK,UAAU,KAAK;QAChE,MAAMuK,YAAYK,WAAWL,SAAS,KAAK;QAE3C,MAAMU,iBAAiB,CAAC,CAACL,WAAWM,kBAAkB;QACtD,MAAMC,yBAAyB7Y,0BAA0BgC;QACzD,IAAI8W,QAAQ,CAAC,CAACR,WAAWS,cAAc;QACvC,uFAAuF;QACvF,MAAMvL,eAAelQ,eAAe0E,KAAK,mBAAmB;QAE5D,yEAAyE;QACzE,yEAAyE;QACzE,mEAAmE;QACnE,oEAAoE;QACpE,mEAAmE;QACnE,qCAAqC;QACrC,IACE,CAAC,IAAI,CAAC+D,WAAW,IACjB,IAAI,CAACvE,UAAU,CAACC,YAAY,CAACuX,yBAAyB,IACtDxL,cACA;YACA,MAAM7K,UAAUX,IAAIW,OAAO;YAE3B,MAAMsW,sBAAsBtW,OAAO,CAAC3E,4BAA4B;YAChE,MAAMkb,iBACJD,wBAAwBpX,YAEpBoX,wBAAwB,OAAOA,wBAAwB,MACrDA,sBACApX,YAEF,yEAAyE;YACzE,+EAA+E;YAC/EvE,eAAe0E,KAAK,0BAClB,MACAH;YAER,MAAMkB,4BACJJ,OAAO,CAAC1E,oCAAoC,IAC5CX,eAAe0E,KAAK;YAEtB,MAAMmX,eAAenY,+BACnBkY,gBACAnW,2BACAJ,OAAO,CAACxE,8BAA8B,EACtCwE,OAAO,CAACzE,SAAS;YAEnB,MAAMkb,aACJ9b,eAAe0E,KAAK,8BACpB,IAAI0N,IAAI1N,IAAIgB,GAAG,IAAI,IAAI,oBAAoBqW,YAAY,CAACnL,GAAG,CACzDnQ;YAGJ,IAAIob,iBAAiBC,YAAY;gBAC/B,iEAAiE;gBACjE,mEAAmE;gBACnE,iFAAiF;gBACjF,6EAA6E;gBAC7E,6EAA6E;gBAC7E,MAAMpW,MAAM,IAAI0M,IAAI1N,IAAIgB,GAAG,IAAI,IAAI;gBACnC/B,mCAAmC+B,KAAKmW;gBACxChW,IAAIuK,UAAU,GAAG;gBACjBvK,IAAI+S,SAAS,CAAC,YAAY,GAAGlT,IAAIb,QAAQ,GAAGa,IAAIsW,MAAM,EAAE;gBACxDnW,IAAIyL,IAAI,CAAC,IAAIC,IAAI;gBACjB,OAAO;YACT;QACF;QAEA,0DAA0D;QAC1D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIc,cAAc5T,SAASiG,IAAIgB,GAAG,IAAI,IAAIb,QAAQ,IAAI;QAEtD,IAAIoX,sBAAsBjc,eAAe0E,KAAK,iBAAiB2N;QAE/D,IAAI,CAACqI,aAAa,CAAChW,KAAKmB,KAAK8U,WAAWsB;QAExC,IAAIlC;QACJ,IAAImC,cAAc;QAElB,MAAMjB,oBAAoB,IAAI,CAAC/P,oBAAoB;QAEnD,IACEgR,gBACAnC,+BAAAA,YAAaoC,QAAQ,CAACF,yBACtB,mDAAmD;QACnD,+BAA+B;QAC/BvX,IAAIW,OAAO,CAAC,sBAAsB,EAClC;YACAmW,QAAQ;QACV,OAAO,IAAI,CAAC,IAAI,CAAC5Q,UAAU,CAACpC,GAAG,EAAE;YAC/BgT,UAAU,CAAC,CAACP,kBAAkBmB,MAAM,CAACxZ,QAAQiC,UAAU;QACzD;QAEA,+CAA+C;QAC/C,MAAMwX,oBACJ,CAAC,CACCrc,CAAAA,eAAe0E,KAAK,oBACnBA,IAAIW,OAAO,CAAC,gBAAgB,IAC3B,AAAC,IAAI,CAACuD,aAAa,CAAS+M,eAAe,KAE9C6F,CAAAA,SAASH,cAAa;QAEzB,4DAA4D;QAC5D,wDAAwD;QACxD,6BAA6B;QAC7B,IACE,CAACG,SACD9W,IAAIW,OAAO,CAAC,wBAAwB,IACpC,CAAE8V,CAAAA,aAAatW,aAAa,SAAQ,GACpC;YACAgB,IAAI+S,SAAS,CAAC7W,qBAAqB8C;YACnCgB,IAAI+S,SAAS,CAAC,qBAAqB;YACnC/S,IAAI+S,SAAS,CACX,iBACA;YAEF/S,IAAIyL,IAAI,CAAC,MAAMC,IAAI;YACnB,OAAO;QACT;QAEA,uDAAuD;QACvD,iEAAiE;QACjE,IACEiK,SACA,IAAI,CAAC/S,WAAW,IAChB/D,IAAIW,OAAO,CAACtD,oBAAoB,IAChC2C,IAAIgB,GAAG,CAAC8T,UAAU,CAAC,gBACnB;YACA9U,IAAIgB,GAAG,GAAG,IAAI,CAACiN,iBAAiB,CAACjO,IAAIgB,GAAG;QAC1C;QAEA,MAAMkO,SAAS5T,eAAe0E,KAAK;QAEnC,IACE,CAAC,CAACA,IAAIW,OAAO,CAAC,gBAAgB,IAC7B,CAAA,CAACQ,IAAIuK,UAAU,IAAIvK,IAAIuK,UAAU,KAAK,GAAE,GACzC;YACAvK,IAAI+S,SAAS,CACX,yBACA,GAAGhF,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK/O,UAAU;QAE9C;QAEA,IAAIyX;QACJ,IAAItB,WAAWsB,WAAW,EAAE;YAC1BA,cAActB,WAAWsB,WAAW;QACtC;QAEA;;;KAGC,GACD,MAAMC,kBACJ,IAAI,CAACpS,eAAe,IACpB,OAAOmS,gBAAgB,eACvBha,qBAAqBga;QAEvB,yEAAyE;QACzE,wCAAwC;QACxC,MAAME,2BACJzS,QAAQC,GAAG,CAACyS,0CAA0C,KAAK,OAC3D,OAAOjL,MAAMkL,aAAa,KAAK,eAC/BH;QAEF,4EAA4E;QAC5E,8CAA8C;QAC9C,MAAMI,oBACJJ,mBACC,CAAA,EACCtB,QAAAA,kBAAkBmB,MAAM,CAACvX,SAAS,IAClCoW,kBAAkBnB,aAAa,CAACjV,SAAS,qBAF1C,AACCoW,MAEC2B,aAAa,MAAK,sBACnB,uEAAuE;QACvE,wEAAwE;QACxE,wEAAwE;QACxE,+BAA+B;QAC9BJ,4BACE,CAAA,IAAI,CAAC5R,UAAU,CAACpC,GAAG,KAAK,QACvB,IAAI,CAACG,qBAAqB,KAAK,IAAG,CAAE;QAE5C,2EAA2E;QAC3E,wEAAwE;QACxE,UAAU;QACV,MAAMkU,mBAAmBF,oBACrB3c,eAAe0E,KAAK,eACpBH;QAEJ,gEAAgE;QAChE,IAAI4W,aAAa,CAACkB,qBAAqB,CAACnM,cAAc;YACpDrK,IAAIuK,UAAU,GAAG;QACnB;QAEA,2DAA2D;QAC3D,qBAAqB;QACrB,IAAItR,oBAAoBqd,QAAQ,CAACtX,WAAW;YAC1CgB,IAAIuK,UAAU,GAAG0M,SAASjY,SAASkY,KAAK,CAAC,IAAI;QAC/C;QAEA,IACE,+CAA+C;QAC/C,CAACxB,0BACD,uCAAuC;QACvC,CAACsB,oBACD,CAAC1B,aACD,CAACC,aACDvW,aAAa,aACbH,IAAIoK,MAAM,KAAK,UACfpK,IAAIoK,MAAM,KAAK,SACd,CAAA,OAAOkM,WAAWgC,SAAS,KAAK,YAAYxB,KAAI,GACjD;YACA3V,IAAIuK,UAAU,GAAG;YACjBvK,IAAI+S,SAAS,CAAC,SAAS;gBAAC;gBAAO;aAAO;YACtC/S,IAAIyL,IAAI,CAAC,sBAAsBC,IAAI;YACnC,OAAO;QACT;QAEA,qBAAqB;QACrB,IAAI,OAAOyJ,WAAWgC,SAAS,KAAK,UAAU;YAC5C,OAAO;gBACL1L,MAAM/R,aAAa0d,UAAU,CAC3BjC,WAAWgC,SAAS,EACpBnb;YAEJ;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,IAAI,SAAS2P,SAAS,CAACA,MAAMxG,GAAG,EAAE,OAAOwG,MAAMxG,GAAG;QAElD,IAAI+P,KAAKlQ,uBAAuB,KAAK,MAAM;gBAIhCmQ;YAHT,MAAM3C,KAAK3T,IAAIW,OAAO,CAAC,aAAa,IAAI;YACxC,MAAM6X,eAAe5d,MAAM+Y;YAC3B,MAAM8E,sBACJ,SAAOnC,uBAAAA,WAAWoC,QAAQ,qBAAnBpC,qBAAqBqC,eAAe,MAAK,cAChD,oFAAoF;YACpFze,yBAAyBoc,WAAWoC,QAAQ;YAE9C,oEAAoE;YACpE,gEAAgE;YAChE,2DAA2D;YAC3D,0DAA0D;YAC1D,kDAAkD;YAClDrC,KAAKlQ,uBAAuB,GAC1B,CAAC2Q,SAAS,CAAC0B,gBAAgB,CAAC1L,MAAMxG,GAAG,IAAImS;QAC7C;QAEA,2DAA2D;QAC3D,IAAI,CAACd,qBAAqB1B,aAAaI,KAAKvS,GAAG,EAAE;YAC/CuS,KAAKlQ,uBAAuB,GAAG;QACjC;QAEA,IAAI2Q,SAAS,IAAI,CAAC/S,WAAW,IAAI/D,IAAIW,OAAO,CAACtD,oBAAoB,EAAE;YACjE,uEAAuE;YACvEka,sBAAsB5J;QACxB;QAEAA,cAAc7S,oBAAoB6S;QAClC4J,sBAAsBzc,oBAAoByc;QAC1C,IAAI,IAAI,CAACzS,gBAAgB,EAAE;YACzByS,sBAAsB,IAAI,CAACzS,gBAAgB,CAACjE,SAAS,CAAC0W;QACxD;QAEA,2DAA2D;QAC3D,8CAA8C;QAC9C,IAAII,mBAAmB;YACrBJ,sBAAsB,IAAI,CAACtJ,iBAAiB,CAACsJ;YAC7C5J,cAAc,IAAI,CAACM,iBAAiB,CAACN;QACvC;QAEA,sDAAsD;QACtD,MAAMuD,mBACJ,MAAM,IAAI,CAACC,mBAAmB,CAAC;YAC7BC,gBAAgBvJ,OAAOyI,MAAM,CAAC,CAAC,GAAGtQ,IAAIW,OAAO;QAC/C;QAEF,0EAA0E;QAC1EuQ,iBAAiBG,iBAAiB;QAElC,IACEuG,CAAAA,+BAAAA,YAAagB,KAAK,KAClBre,eAAe4F,aACdmW,CAAAA,WAAWpB,cAAc,IAAIe,SAAQ,GACtC;YACA,MAAM4C,eAAe,MAAM,IAAI,CAAC3D,cAAc,CAAC;gBAC7C/U;gBACAwN;gBACAyD,gBAAgBpR,IAAIW,OAAO;gBAC3B+N,MAAM4H,WAAW5H,IAAI;gBACrBuH;YACF;YACA,IAAIA,aAAa,IAAI,CAACzW,UAAU,CAACC,YAAY,CAAC2I,eAAe,EAAE;oBACzDyQ;gBAAJ,KAAIA,kCAAAA,aAAaC,iBAAiB,qBAA9BD,gCAAgCjX,MAAM,EAAE;oBAC1C,IAAImX,8BAA8B;oBAClC,KAAK,MAAM5M,SAAS0M,aAAaC,iBAAiB,CAAE;wBAClD,MAAME,sBAAsB7M,MAAM6M,mBAAmB;wBACrD,IAAI,CAACA,uBAAuBA,oBAAoBpX,MAAM,KAAK,GAAG;4BAC5D,kEAAkE;4BAClEmX,8BAA8B;4BAC9B;wBACF;wBACA,IACEA,gCAAgC,QAChCC,oBAAoBpX,MAAM,GAAGmX,4BAA4BnX,MAAM,EAC/D;4BACAmX,8BAA8BC;wBAChC;oBACF;oBACA,IAAID,6BAA6B;wBAC/B,MAAME,8BAA8B,IAAIC,IACtCH,4BAA4B9I,GAAG,CAAC,CAACC,IAAM;gCAACA;gCAAG;6BAAG;wBAEhD7U,eACE2E,KACA,+BACAiZ;oBAEJ;gBACF;YACF;QACF;QAEA,mDAAmD;QACnD,IACEjZ,IAAIoK,MAAM,KAAK,aACf,CAACqM,aACA,CAAA,CAACmB,eAAe,CAAC/Z,sBAAsB+Z,YAAW,GACnD;YACA,MAAM3a,aAAa+C,KAAKmB,KAAK,IAAIoR,SAAS,MAAM;gBAAE4G,QAAQ;YAAI;YAC9D,OAAO;QACT;QAEA,MAAMC,UAAUjb,kBAAkB6B,OAAOA,IAAIqM,eAAe,GAAGrM;QAC/D,MAAMsS,WAAWlU,mBAAmB+C,OAAOA,IAAIoL,gBAAgB,GAAGpL;QAElE,MAAMkY,gBAAgBtf,SAASuB,eAAe0E,KAAK,cAAcA,IAAIgB,GAAG;QACxE,IAAIsY,eAAeD,cAAclZ,QAAQ,IAAI;QAE7C,KAAK,MAAM+C,cAAc;YACvB,IAAI,CAAC9C,WAAW,CAACC,kBAAkB;YACnC,IAAI,CAACD,WAAW,CAACQ,WAAW;YAC5B,IAAI,CAACR,WAAW,CAACU,GAAG;SACrB,CAAE;YACD,IAAIoC,8BAAAA,WAAY5C,KAAK,CAACgZ,eAAe;gBACnCA,eAAepW,WAAWrC,SAAS,CAACyY;YACtC;QACF;QAEA,6DAA6D;QAC7D,0FAA0F;QAC1F,sEAAsE;QACtE,IAAI,CAAE,CAAA,IAAI,CAACvV,WAAW,IAAIyS,eAAc,GAAI;YAC1C4C,QAAQpY,GAAG,GAAG,GAAGsY,eAAeD,cAAc/B,MAAM,IAAI,IAAI;QAC9D;QAEA,wCAAwC;QACxC9b,eAAe4d,SAAS9d,eAAe0E;QACvC3E,eAAe+d,SAAS,WAAW,IAAI,CAAC7U,OAAO;QAC/ClJ,eAAe+d,SAAS,SAAStM;QACjCzR,eAAe+d,SAAS,UAAU/C,KAAK/U,MAAM;QAC7CjG,eAAe+d,SAAS,gBAAgB,IAAI,CAAClT,UAAU,CAACqT,YAAY;QACpEle,eAAe+d,SAAS,eAAe,IAAI,CAACrV,WAAW;QAEvD,IAAIsS,KAAKrM,GAAG,EAAE;YACZ3O,eAAe+d,SAAS,eAAe/C,KAAKrM,GAAG;QACjD;QAEA,MAAM4I,UAMe0D,WAAWkD,YAAY,CAAC5G,OAAO;QAEpD,MAAM6G,kBACJ,qDAAqD;QACrD,qDAAqD;QACrD,qDAAqD;QACrD,0BAA0B;QAC1BpU,QAAQC,GAAG,CAACoU,QAAQ,KAAK,gBACrB,IAAIC,MAAMP,SAAS;YACjBlN,KAAI0N,MAAW,EAAEC,IAAI;gBACnB,IAAI,OAAOD,MAAM,CAACC,KAAK,KAAK,YAAY;oBACtC,OAAOD,MAAM,CAACC,KAAK,CAACjR,IAAI,CAACgR;gBAC3B;gBACA,OAAOA,MAAM,CAACC,KAAK;YACrB;YACAC,KAAIF,MAAW,EAAEC,IAAI,EAAEjK,KAAK;gBAC1B,IAAIiK,SAAS,gBAAgB;;oBACzB7Z,IAAY+Z,YAAY,GAAGnK;gBAC/B;gBACAgK,MAAM,CAACC,KAAK,GAAGjK;gBACf,OAAO;YACT;QACF,KACAwJ;QAEN,MAAMxG,QAAQ6G,iBAAiBnH,UAAU;YACvCsC,WAAW,IAAI,CAACF,YAAY;QAC9B;QAEA,uCAAuC;QACvC,OAAO;IACT;IAEQzG,kBAAkB1M,IAAY,EAAEyY,cAAc,IAAI,EAAE;QAC1D,IAAIzY,KAAKkW,QAAQ,CAAC,IAAI,CAACjW,OAAO,GAAG;YAC/B,MAAMyY,YAAY1Y,KAAKS,SAAS,CAC9BT,KAAK2Y,OAAO,CAAC,IAAI,CAAC1Y,OAAO,IAAI,IAAI,CAACA,OAAO,CAACI,MAAM;YAGlDL,OAAOxG,oBAAoBkf,UAAU3M,OAAO,CAAC,WAAW;QAC1D;QAEA,IAAI,IAAI,CAACxI,gBAAgB,IAAIkV,aAAa;YACxC,OAAO,IAAI,CAAClV,gBAAgB,CAACjE,SAAS,CAACU;QACzC;QACA,OAAOA;IACT;IAEA,0CAA0C;IAChC4Y,oBAAoBhO,KAAa,EAAE;QAC3C,IAAI,IAAI,CAAC9I,kBAAkB,CAACqC,GAAG,EAAE;gBACP;YAAxB,MAAM0U,mBAAkB,sBAAA,IAAI,CAACjR,aAAa,qBAAlB,mBAAoB,CAACgD,MAAM;YAEnD,IAAI,CAACiO,iBAAiB;gBACpB,OAAO;YACT;YAEA,OAAOA;QACT;QACA,OAAO;IACT;IAEA,MAAgBC,oBACdpQ,GAAkD,EAClDqQ,gBAAyB,EACzB;YAkBgB;QAjBhB,MAAM,EAAExN,KAAK,EAAE3M,QAAQ,EAAE,GAAG8J;QAE5B,MAAMsQ,WAAW,IAAI,CAACJ,mBAAmB,CAACha;QAC1C,MAAM8V,YAAYlG,MAAMC,OAAO,CAACuK;QAEhC,IAAI7L,OAAOvO;QACX,IAAI8V,WAAW;YACb,4EAA4E;YAC5EvH,OAAO6L,QAAQ,CAACA,SAAS3Y,MAAM,GAAG,EAAE;QACtC;QAEA,MAAMrB,SAAS,MAAM,IAAI,CAACia,kBAAkB,CAAC;YAC3CtL,QAAQ5T,eAAe2O,IAAIjK,GAAG,EAAE;YAChC0O;YACA5B;YACAxL,QAAQ2I,IAAI/D,UAAU,CAAC5E,MAAM,IAAI,CAAC;YAClC2U;YACAwE,YAAY,CAAC,GAAC,oCAAA,IAAI,CAACjb,UAAU,CAACC,YAAY,CAACib,GAAG,qBAAhC,kCAAkCC,SAAS;YACzDJ;YACA,sEAAsE;YACtEK,cAAc;QAChB;QACA,IAAIra,QAAQ;YACV5D,YAAYke,oBAAoB,CAAC,cAAc1a;YAC/C,IAAI;gBACF,OAAO,MAAM,IAAI,CAACoV,8BAA8B,CAACtL,KAAK1J;YACxD,EAAE,OAAOyJ,KAAK;gBACZ,MAAM8Q,oBAAoB9Q,eAAenL;gBAEzC,IAAI,CAACic,qBAAsBA,qBAAqBR,kBAAmB;oBACjE,MAAMtQ;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAciL,iBACZhL,GAAkD,EACjB;QACjC,OAAOtN,YAAYqO,KAAK,CACtBjO,eAAekY,gBAAgB,EAC/B;YACEhK,UAAU,CAAC,cAAc,CAAC;YAC1BG,YAAY;gBACV,cAAcnB,IAAI9J,QAAQ;YAC5B;QACF,GACA;YACE,OAAO,IAAI,CAAC4a,oBAAoB,CAAC9Q;QACnC;IAEJ;IAQA,MAAc8Q,qBACZ9Q,GAAkD,EACjB;YAmBzB;QAlBR,MAAM,EAAEjK,GAAG,EAAEmB,GAAG,EAAE2L,KAAK,EAAE3M,QAAQ,EAAE,GAAG8J;QACtC,IAAIyE,OAAOvO;QACX,MAAMma,mBACJhf,eAAe2O,IAAIjK,GAAG,EAAE,uBAAuB;QAEjD,IACE,CAAC,IAAI,CAAC+D,WAAW,IACjB,IAAI,CAACvE,UAAU,CAACC,YAAY,CAACuX,yBAAyB,EACtD;YACA3b,eACE4O,IAAIjK,GAAG,EACP,2BACA8M,KAAK,CAAC/Q,qBAAqB;QAE/B;QACA,OAAO+Q,KAAK,CAAC/Q,qBAAqB;QAElC,MAAM+D,UAAwB;YAC5B8E,IAAI,GAAE,qBAAA,IAAI,CAAC3C,YAAY,qBAAjB,mBAAmB+Y,WAAW,CAAChb,KAAKG;QAC5C;QAEA,IAAI;YACF,WAAW,MAAMG,SAAS,IAAI,CAACiJ,QAAQ,CAAC0R,QAAQ,CAAC9a,UAAUL,SAAU;gBACnE,uDAAuD;gBACvD,0DAA0D;gBAC1D,MAAMob,eAAe5f,eAAe2O,IAAIjK,GAAG,EAAE;gBAC7C,IACE,CAAC,IAAI,CAAC+D,WAAW,IACjB,OAAOmX,iBAAiB,YACxB3gB,eAAe2gB,gBAAgB,OAC/BA,iBAAiB5a,MAAMkO,UAAU,CAACrO,QAAQ,EAC1C;oBACA;gBACF;gBAEA,MAAMI,SAAS,MAAM,IAAI,CAAC8Z,mBAAmB,CAC3C;oBACE,GAAGpQ,GAAG;oBACN9J,UAAUG,MAAMkO,UAAU,CAACrO,QAAQ;oBACnC+F,YAAY;wBACV,GAAG+D,IAAI/D,UAAU;wBACjB5E,QAAQhB,MAAMgB,MAAM;oBACtB;gBACF,GACAgZ;gBAEF,IAAI/Z,WAAW,OAAO,OAAOA;YAC/B;YAEA,+DAA+D;YAC/D,6DAA6D;YAC7D,4DAA4D;YAC5D,mBAAmB;YACnB,sDAAsD;YACtD,IAAI,IAAI,CAAC2D,aAAa,CAAC+M,eAAe,EAAE;gBACtC,sDAAsD;gBACtDhH,IAAI9J,QAAQ,GAAG,IAAI,CAAC+D,aAAa,CAAC+M,eAAe,CAACvC,IAAI;gBACtD,MAAMnO,SAAS,MAAM,IAAI,CAAC8Z,mBAAmB,CAACpQ,KAAKqQ;gBACnD,IAAI/Z,WAAW,OAAO,OAAOA;YAC/B;QACF,EAAE,OAAOkK,OAAO;YACd,MAAMT,MAAM5O,eAAeqP;YAE3B,IAAIA,iBAAiB9Q,mBAAmB;gBACtC6Q,QAAQC,KAAK,CACX,yCACA0Q,KAAKC,SAAS,CACZ;oBACE1M;oBACA1N,KAAKiJ,IAAIjK,GAAG,CAACgB,GAAG;oBAChByM,aAAaxD,IAAIjK,GAAG,CAACW,OAAO,CAACtD,oBAAoB;oBACjDge,SAAS/f,eAAe2O,IAAIjK,GAAG,EAAE;oBACjCwP,YAAY,CAAC,CAAClU,eAAe2O,IAAIjK,GAAG,EAAE;oBACtCsb,YAAYhgB,eAAe2O,IAAIjK,GAAG,EAAE;gBACtC,GACA,MACA;gBAGJ,MAAMgK;YACR;YAEA,IAAIA,eAAenL,mBAAmByb,kBAAkB;gBACtD,MAAMtQ;YACR;YACA,IAAIA,eAAevQ,eAAeuQ,eAAexQ,gBAAgB;gBAC/D2H,IAAIuK,UAAU,GAAG;gBACjB,OAAO,MAAM,IAAI,CAAC6P,qBAAqB,CAACtR,KAAKD;YAC/C;YAEA7I,IAAIuK,UAAU,GAAG;YAEjB,mDAAmD;YACnD,qDAAqD;YACrD,IAAI,MAAM,IAAI,CAACsJ,OAAO,CAAC,SAAS;gBAC9B3Z,eAAe4O,IAAIjK,GAAG,EAAE,qBAAqB;gBAC7C,MAAM,IAAI,CAACub,qBAAqB,CAACtR,KAAKD;gBACtCzO,kBAAkB0O,IAAIjK,GAAG,EAAE;YAC7B;YAEA,MAAMwb,iBAAiBxR,eAAe9K;YAEtC,IAAI,CAACsc,gBAAgB;gBACnB,IAAI,IAAI,CAACzX,WAAW,IAAI,IAAI,CAACmC,UAAU,CAACpC,GAAG,EAAE;oBAC3C,IAAI3I,QAAQ6O,MAAMA,IAAI0E,IAAI,GAAGA;oBAC7B,MAAM1E;gBACR;gBACA,IAAI,CAACU,QAAQ,CAACtP,eAAe4O;YAC/B;YACA,MAAMsI,WAAW,MAAM,IAAI,CAACiJ,qBAAqB,CAC/CtR,KACAuR,iBAAiB,AAACxR,IAA0B3K,UAAU,GAAG2K;YAE3D,OAAOsI;QACT;QAEA,MAAMlR,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IACED,cACA,CAAC,CAAC6I,IAAIjK,GAAG,CAACW,OAAO,CAAC,gBAAgB,IACjC,CAAA,CAACQ,IAAIuK,UAAU,IAAIvK,IAAIuK,UAAU,KAAK,OAAOvK,IAAIuK,UAAU,KAAK,GAAE,GACnE;YACA,MAAMwD,SAAS5T,eAAe0E,KAAK;YAEnCmB,IAAI+S,SAAS,CACX,yBACA,GAAGhF,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK/O,UAAU;YAE5CgB,IAAIuK,UAAU,GAAG;YACjBvK,IAAI+S,SAAS,CAAC,gBAAgB9W;YAC9B+D,IAAIyL,IAAI,CAAC;YACTzL,IAAI0L,IAAI;YACR,OAAO;QACT;QAEA1L,IAAIuK,UAAU,GAAG;QACjB,OAAO,IAAI,CAAC6P,qBAAqB,CAACtR,KAAK;IACzC;IAEA,MAAawR,aACXzb,GAAkB,EAClBmB,GAAmB,EACnBhB,QAAgB,EAChB2M,QAAwB,CAAC,CAAC,EACF;QACxB,OAAOnQ,YAAYqO,KAAK,CAACjO,eAAe0e,YAAY,EAAE;YACpD,OAAO,IAAI,CAACC,gBAAgB,CAAC1b,KAAKmB,KAAKhB,UAAU2M;QACnD;IACF;IAEA,MAAc4O,iBACZ1b,GAAkB,EAClBmB,GAAmB,EACnBhB,QAAgB,EAChB2M,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAACuH,aAAa,CAAC,CAACpK,MAAQ,IAAI,CAACgL,gBAAgB,CAAChL,MAAM;YAC7DjK;YACAmB;YACAhB;YACA2M;QACF;IACF;IAEA,MAAakE,YACXhH,GAAiB,EACjBhK,GAAkB,EAClBmB,GAAmB,EACnBhB,QAAgB,EAChB2M,QAA4B,CAAC,CAAC,EAC9B6O,aAAa,IAAI,EACF;QACf,OAAOhf,YAAYqO,KAAK,CAACjO,eAAeiU,WAAW,EAAE;YACnD,OAAO,IAAI,CAAC4K,eAAe,CAAC5R,KAAKhK,KAAKmB,KAAKhB,UAAU2M,OAAO6O;QAC9D;IACF;IAEA,MAAcC,gBACZ5R,GAAiB,EACjBhK,GAAkB,EAClBmB,GAAmB,EACnBhB,QAAgB,EAChB2M,QAA4B,CAAC,CAAC,EAC9B6O,aAAa,IAAI,EACF;QACf,IAAIA,YAAY;YACdxa,IAAI+S,SAAS,CACX,iBACA;QAEJ;QAEA,OAAO,IAAI,CAACX,IAAI,CACd,OAAOtJ;YACL,MAAMqI,WAAW,MAAM,IAAI,CAACiJ,qBAAqB,CAACtR,KAAKD;YACvD,IAAI,IAAI,CAACjG,WAAW,IAAI5C,IAAIuK,UAAU,KAAK,KAAK;gBAC9C,MAAM1B;YACR;YACA,OAAOsI;QACT,GACA;YAAEtS;YAAKmB;YAAKhB;YAAU2M;QAAM;IAEhC;IAQA,MAAcyO,sBACZtR,GAAkD,EAClDD,GAAiB,EACgB;QACjC,OAAOrN,YAAYqO,KAAK,CAACjO,eAAewe,qBAAqB,EAAE;YAC7D,OAAO,IAAI,CAACM,yBAAyB,CAAC5R,KAAKD;QAC7C;IACF;IAEA,MAAgB6R,0BACd5R,GAAkD,EAClDD,GAAiB,EACgB;QACjC,wGAAwG;QACxG,+DAA+D;QAC/D,IAAI,IAAI,CAAC9D,UAAU,CAACpC,GAAG,IAAImG,IAAI9J,QAAQ,KAAK,gBAAgB;YAC1D,OAAO;gBACLyM,MAAM/R,aAAaihB,KAAK;YAC1B;QACF;QACA,MAAM,EAAE3a,GAAG,EAAE2L,KAAK,EAAE,GAAG7C;QAEvB,IAAI;YACF,IAAI1J,SAAsC;YAE1C,MAAMwb,QAAQ5a,IAAIuK,UAAU,KAAK;YACjC,IAAIsQ,eAAe;YAEnB,IAAID,OAAO;gBACT,IAAI,IAAI,CAAC1Y,kBAAkB,CAACqC,GAAG,EAAE;oBAC/B,2CAA2C;oBAC3CnF,SAAS,MAAM,IAAI,CAACia,kBAAkB,CAAC;wBACrCtL,QAAQ5T,eAAe2O,IAAIjK,GAAG,EAAE;wBAChC0O,MAAMpU;wBACNwS;wBACAxL,QAAQ,CAAC;wBACT2U,WAAW;wBACX2E,cAAc;wBACd5Z,KAAKiJ,IAAIjK,GAAG,CAACgB,GAAG;oBAClB;oBACAgb,eAAezb,WAAW;gBAC5B;gBAEA,IAAI,CAACA,UAAW,MAAM,IAAI,CAACyU,OAAO,CAAC,SAAU;oBAC3CzU,SAAS,MAAM,IAAI,CAACia,kBAAkB,CAAC;wBACrCtL,QAAQ5T,eAAe2O,IAAIjK,GAAG,EAAE;wBAChC0O,MAAM;wBACN5B;wBACAxL,QAAQ,CAAC;wBACT2U,WAAW;wBACX,qEAAqE;wBACrE2E,cAAc;wBACd5Z,KAAKiJ,IAAIjK,GAAG,CAACgB,GAAG;oBAClB;oBACAgb,eAAezb,WAAW;gBAC5B;YACF;YACA,IAAI0b,aAAa,CAAC,CAAC,EAAE9a,IAAIuK,UAAU,EAAE;YAErC,IACE,CAACpQ,eAAe2O,IAAIjK,GAAG,EAAE,wBACzB,CAACO,UACDnG,oBAAoBqd,QAAQ,CAACwE,aAC7B;gBACA,0DAA0D;gBAC1D,8BAA8B;gBAC9B,IAAIA,eAAe,UAAU,CAAC,IAAI,CAAC/V,UAAU,CAACpC,GAAG,EAAE;oBACjDvD,SAAS,MAAM,IAAI,CAACia,kBAAkB,CAAC;wBACrCtL,QAAQ5T,eAAe2O,IAAIjK,GAAG,EAAE;wBAChC0O,MAAMuN;wBACNnP;wBACAxL,QAAQ,CAAC;wBACT2U,WAAW;wBACX,8DAA8D;wBAC9D,SAAS;wBACT2E,cAAc;wBACd5Z,KAAKiJ,IAAIjK,GAAG,CAACgB,GAAG;oBAClB;gBACF;YACF;YAEA,IAAI,CAACT,QAAQ;gBACXA,SAAS,MAAM,IAAI,CAACia,kBAAkB,CAAC;oBACrCtL,QAAQ5T,eAAe2O,IAAIjK,GAAG,EAAE;oBAChC0O,MAAM;oBACN5B;oBACAxL,QAAQ,CAAC;oBACT2U,WAAW;oBACX,iEAAiE;oBACjE,SAAS;oBACT2E,cAAc;oBACd5Z,KAAKiJ,IAAIjK,GAAG,CAACgB,GAAG;gBAClB;gBACAib,aAAa;YACf;YAEA,IACE5W,QAAQC,GAAG,CAACoU,QAAQ,KAAK,gBACzB,CAACsC,gBACA,MAAM,IAAI,CAAChH,OAAO,CAAC,cACpB,CAAE,MAAM,IAAI,CAACA,OAAO,CAAC,SACrB;gBACA,IAAI,CAACvR,oBAAoB;YAC3B;YAEA,IAAI,CAAClD,QAAQ;gBACX,iEAAiE;gBACjE,wDAAwD;gBACxD,IAAI,IAAI,CAAC2F,UAAU,CAACpC,GAAG,EAAE;oBACvB,OAAO;wBACL,mDAAmD;wBACnD8I,MAAM/R,aAAa0d,UAAU,CAC3B,CAAC;;;;;;;;;;;;;uBAaQ,CAAC,EACVpb;oBAEJ;gBACF;gBAEA,MAAM,IAAI+B,kBACR,qBAA8C,CAA9C,IAAIC,MAAM,sCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6C;YAEjD;YAEA,0EAA0E;YAC1E,yCAAyC;YACzC,IAAIoB,OAAO+V,UAAU,CAACsB,WAAW,EAAE;gBACjCvc,eAAe4O,IAAIjK,GAAG,EAAE,SAAS;oBAC/BwO,YAAYjO,OAAO+V,UAAU,CAACsB,WAAW,CAACpJ,UAAU;oBACpDlN,QAAQzB;gBACV;YACF,OAAO;gBACLtE,kBAAkB0O,IAAIjK,GAAG,EAAE;YAC7B;YAEA,IAAI;gBACF,OAAO,MAAM,IAAI,CAACuV,8BAA8B,CAC9C;oBACE,GAAGtL,GAAG;oBACN9J,UAAU8b;oBACV/V,YAAY;wBACV,GAAG+D,IAAI/D,UAAU;wBACjB8D;oBACF;gBACF,GACAzJ;YAEJ,EAAE,OAAO2b,oBAAoB;gBAC3B,IAAIA,8BAA8Brd,iBAAiB;oBACjD,MAAM,qBAAmD,CAAnD,IAAIM,MAAM,2CAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAkD;gBAC1D;gBACA,MAAM+c;YACR;QACF,EAAE,OAAOzR,OAAO;YACd,MAAM0R,oBAAoB/gB,eAAeqP;YACzC,MAAM+Q,iBAAiBW,6BAA6Bjd;YACpD,IAAI,CAACsc,gBAAgB;gBACnB,IAAI,CAAC9Q,QAAQ,CAACyR;YAChB;YACAhb,IAAIuK,UAAU,GAAG;YACjB,MAAM0Q,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAC9DpS,IAAIjK,GAAG,CAACgB,GAAG;YAGb,IAAIob,oBAAoB;gBACtB,mEAAmE;gBACnE,mCAAmC;gBACnC/gB,eAAe4O,IAAIjK,GAAG,EAAE,SAAS;oBAC/BwO,YAAY4N,mBAAmBxE,WAAW,CAAEpJ,UAAU;oBACtDlN,QAAQzB;gBACV;gBAEA,OAAO,IAAI,CAAC0V,8BAA8B,CACxC;oBACE,GAAGtL,GAAG;oBACN9J,UAAU;oBACV+F,YAAY;wBACV,GAAG+D,IAAI/D,UAAU;wBACjB,sDAAsD;wBACtD,sCAAsC;wBACtC8D,KAAKwR,iBACDW,kBAAkB9c,UAAU,GAC5B8c;oBACN;gBACF,GACA;oBACErP;oBACAwJ,YAAY8F;gBACd;YAEJ;YACA,OAAO;gBACLxP,MAAM/R,aAAa0d,UAAU,CAAC,yBAAyB;YACzD;QACF;IACF;IAEA,MAAa+D,kBACXtS,GAAiB,EACjBhK,GAAkB,EAClBmB,GAAmB,EACnBhB,QAAgB,EAChB2M,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAACuH,aAAa,CAAC,CAACpK,MAAQ,IAAI,CAACsR,qBAAqB,CAACtR,KAAKD,MAAM;YACvEhK;YACAmB;YACAhB;YACA2M;QACF;IACF;IAEA,MAAarL,UACXzB,GAAkB,EAClBmB,GAAmB,EACnBjB,SAA8D,EAC9Dyb,aAAa,IAAI,EACF;QACf,MAAM,EAAExb,QAAQ,EAAE2M,KAAK,EAAE,GAAG5M,YAAYA,YAAYnG,SAASiG,IAAIgB,GAAG,EAAG;QAEvE,uDAAuD;QACvD,IAAI,IAAI,CAACxB,UAAU,CAACoF,IAAI,EAAE;YACxB,IAAI,CAACtJ,eAAe0E,KAAK,WAAW;gBAClC3E,eAAe2E,KAAK,UAAU,IAAI,CAACR,UAAU,CAACoF,IAAI,CAACpC,aAAa;YAClE;YACAnH,eAAe2E,KAAK,iBAAiB,IAAI,CAACR,UAAU,CAACoF,IAAI,CAACpC,aAAa;QACzE;QAEArB,IAAIuK,UAAU,GAAG;QACjB,OAAO,IAAI,CAACsF,WAAW,CAAC,MAAMhR,KAAKmB,KAAKhB,UAAW2M,OAAO6O;IAC5D;AACF", "ignoreList": [0]}